package inks.service.sa.pms;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;
import org.springframework.context.ConfigurableApplicationContext;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.FilterType;
import org.springframework.core.env.Environment;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;
import springfox.documentation.swagger2.annotations.EnableSwagger2;

import java.net.InetAddress;
import java.net.UnknownHostException;

//@EnableAsync
@SpringBootApplication(scanBasePackages = {"inks.sa.common.core","inks.common.core", "inks.service.sa.pms", "inks.service.sa.pms.utils.mqtt"})
@EnableSwagger2
@EnableAsync
@EnableScheduling
@EnableFeignClients(basePackages = {"inks.sa.common.core.feign"})
@MapperScan(basePackages = {"inks.service.sa.pms.mapper", "inks.sa.common.core.mapper"})
//@ComponentScan(
//        basePackages = "inks.sa",
//        excludeFilters = @ComponentScan.Filter(
//                type = FilterType.ASSIGNABLE_TYPE,
//                classes = inks.sa.common.core.controller.A_SaUserController.class
//        )
//)
// basePackages = "inks.sa"：指定扫描的基础包。 excludeFilters：通过 FilterType.ASSIGNABLE_TYPE 排除特定类 A_SaUserController，避免其被 Spring 扫描和注册为 Bean。
public class InksServiceSaPmsApplication {

    public static void main(String[] args) throws UnknownHostException {
        ConfigurableApplicationContext application = SpringApplication.run(InksServiceSaPmsApplication.class, args);
        Environment env = application.getEnvironment();
        String ip = InetAddress.getLocalHost().getHostAddress();
        String port = env.getProperty("server.port");
        String property = env.getProperty("server.servlet.context-path");
        String path = property == null ? "" : property;
        System.out.println(
                "\n\t" +
                        "----------------------------------------------------------\n\t" +
                        "Application Sailrui-Boot is running! Access URLs:\n\t" +
                        "Local: \t\thttp://localhost:" + port + "/swagger-ui.html\n\t" +
                        "External: \thttp://" + ip + ":" + port + "/swagger-ui.html\n\t" +
                        "External: \thttp://" + ip + ":" + port + "\n\t" +
                        "Nacos: \t\thttp://dev.inksyun.com:" + port + "/swagger-ui.html\n\t" +
                        "------------------------------------------------------------");
    }

}
