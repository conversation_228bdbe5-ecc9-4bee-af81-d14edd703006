//
// Source code recreated from a .class file by IntelliJ IDEA
// (powered by FernFlower decompiler)
//

package inks.service.sa.pms.annotation.operLog;

import com.alibaba.fastjson.JSON;
import inks.common.core.domain.LoginUser;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.ip.IpUtils;
import inks.sa.common.core.domain.SaOperlogEntity;
import inks.service.sa.pms.controller.SaOperlogController;
import inks.sa.common.core.service.SaRedisService;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterReturning;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.reflect.CodeSignature;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

@Aspect
@Component
public class OperLogAspect {
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaOperlogController operlogController;

    public OperLogAspect() {
    }

    @AfterReturning(
            pointcut = "@annotation(controllerLog)",
            returning = "jsonResult"
    )
    public void doAfterReturning(JoinPoint joinPoint, OperLog controllerLog, Object jsonResult) throws Throwable {
        SaOperlogEntity cioperlogEntity = this.setEntity(joinPoint, controllerLog);
        cioperlogEntity.setJsonresult(JSON.toJSONString(jsonResult));
        LoginUser userInfo = this.saRedisService.getLoginUser();
        cioperlogEntity.setTenantid(userInfo.getTenantid());
        cioperlogEntity.setOpername(userInfo.getRealName());
        cioperlogEntity.setStatus(0);
        this.operlogController.create(JSON.toJSONString(cioperlogEntity));
    }

    @AfterThrowing(
            value = "@annotation(controllerLog)",
            throwing = "e"
    )
    public void doAfterThrowing(JoinPoint joinPoint, OperLog controllerLog, Exception e) {
        SaOperlogEntity cioperlogEntity = this.setEntity(joinPoint, controllerLog);
        cioperlogEntity.setErrormsg(e.getMessage());
        cioperlogEntity.setStatus(1);
        LoginUser userInfo = this.saRedisService.getLoginUser();
        cioperlogEntity.setTenantid(userInfo.getTenantid());
        cioperlogEntity.setOpername(userInfo.getRealName());
        cioperlogEntity.setOperuserid(userInfo.getUserid());
        this.operlogController.create(JSON.toJSONString(cioperlogEntity));
    }

    public SaOperlogEntity setEntity(JoinPoint point, OperLog controllerLog) {
        SaOperlogEntity cioperlogEntity = new SaOperlogEntity();
        cioperlogEntity.setId(UUID.randomUUID().toString());
        cioperlogEntity.setOperurl(ServletUtils.getRequest().getRequestURI());
        String ip = IpUtils.getIpAddr(ServletUtils.getRequest());
        cioperlogEntity.setOperip(ip);
        cioperlogEntity.setOpertitle(controllerLog.title());
        cioperlogEntity.setOperatortype(controllerLog.operatorType().ordinal());
        cioperlogEntity.setBusinesstype(controllerLog.businessType().ordinal());
        Object[] paramValues = point.getArgs();
        Map<String, Object> map = new HashMap();
        String[] paramNames = ((CodeSignature) point.getSignature()).getParameterNames();

        for (int i = 0; i < paramNames.length; ++i) {
            map.put(paramNames[i], paramValues[i]);
        }

        cioperlogEntity.setOperparam(map.toString());
        cioperlogEntity.setOpertime(new Date());
        cioperlogEntity.setRequestmethod(ServletUtils.getRequest().getMethod());
        cioperlogEntity.setMethod(point.getTarget().getClass().getName() + "." + point.getSignature().getName() + "()");
        return cioperlogEntity;
    }
}
