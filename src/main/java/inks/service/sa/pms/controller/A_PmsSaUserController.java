package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.config.oss.service.OSSConfigManager;
import inks.sa.common.core.domain.pojo.LoginUserPojo;
import inks.sa.common.core.mapper.SaConfigMapper;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaUserPojo;
import inks.service.sa.pms.mapper.pmsSaUserMapper;
import inks.service.sa.pms.service.PmsSaUserService;
import inks.service.sa.pms.utils.RSA.MyRSA;
import inks.service.sa.pms.utils.RSA.RSADecryptor;
import inks.service.sa.pms.utils.SN;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Primary;
import org.springframework.web.bind.annotation.*;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 通用:用户服务(Sa_User)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-24 11:34:51
 */
@RestController
@RequestMapping("PmsSaUser")
@Api(tags = "通用:用户服务")
@Primary  // 添加此注解
public class A_PmsSaUserController {
    private final String SCANLOGIN_CODE = "scanlogin_code:";

    @Resource
    private PmsSaUserService pmsSaUserService;

    @Resource

    private pmsSaUserMapper pmsSaUserMapper;
    //@Autowired
    //@Qualifier("fileInfoMinioServiceImpl")
    //private OssService fileInfoMinioService; //minio上传
    //@Autowired
    //@Qualifier("fileInfoAliyunServiceImpl")
    //private OssService fileInfoAliYunService; //aliyun上传
    @Autowired
    private OSSConfigManager configManager;

    private String OSSTYPE;

    @PostConstruct
    public void init() {
        this.OSSTYPE = configManager.getOssType();
    }
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaConfigMapper saConfigMapper;
    @Value("${oss.bucket}")
    private String BUCKET_NAME;
    @Value("${inks.oam.api:http://oam.inksyun.com}")
    private String OAM_API;
    @Value("${inks.oam.appid:wx7850d75f765d0dce}")
    private String OAM_APPID;


    @ApiOperation(value = " 获取用户详细信息", notes = "获取用户详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaUserPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.pmsSaUserService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaUserPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_User.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.pmsSaUserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增用户", notes = "新增用户", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaUserPojo> create(@RequestBody String json) {
        try {
            SaUserPojo saUserPojo = JSONArray.parseObject(json, SaUserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            Integer isAdmin = loginUser.getIsadmin();
            // AdminMark ： 0、非管理员  1、普通管理员 2、超级管理员
            if (isAdmin == 0) {
                throw new BaseBusinessException("非管理员无权创建用户");
            }
            if (isAdmin == 1 && (saUserPojo.getAdminmark() != null && saUserPojo.getAdminmark() == 1)) {
                throw new BaseBusinessException("普通管理员无权创建管理员用户");
            }
            saUserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saUserPojo.setCreatedate(new Date());   // 创建时间
            saUserPojo.setLister(loginUser.getRealname());   // 制表
            saUserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.pmsSaUserService.insert(saUserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改用户", notes = "修改用户", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaUserPojo> update(@RequestBody String json) {
        try {
            SaUserPojo saUserPojo = JSONArray.parseObject(json, SaUserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String userid = loginUser.getUserid();
            // AdminMark ： 0、非管理员  1、普通管理员 2、超级管理员
            if (loginUser.getIsadmin() == 0 && !userid.equals(saUserPojo.getId())) {
                throw new BaseBusinessException("非管理员无权修改其他用户信息");
            }
            if (loginUser.getIsadmin() == 1 && saUserPojo.getAdminmark() != null && saUserPojo.getAdminmark() != 0) {
                throw new BaseBusinessException("普通管理员无权修改其他管理员信息");
            }
            saUserPojo.setLister(loginUser.getRealname());   // 制表
            saUserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.pmsSaUserService.update(saUserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除用户", notes = "删除用户", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaUserPojo saUserPojo = pmsSaUserService.getEntity(key);
            String userid = loginUser.getUserid();
            // AdminMark ： 0、非管理员  1、普通管理员 2、超级管理员
            if (loginUser.getIsadmin() == 0 && !userid.equals(saUserPojo.getId())) {
                throw new BaseBusinessException("非管理员无权删除其他用户信息");
            }
            if (loginUser.getIsadmin() == 1 && saUserPojo.getAdminmark() != null && saUserPojo.getAdminmark() != 0) {
                throw new BaseBusinessException("普通管理员无权删除其他管理员信息");
            }
            // 检查Userid是否被引用 (返回使用位置(表名))
            List<String> usedPlace = pmsSaUserService.checkUseridUsed(key);
            if (CollectionUtils.isNotEmpty(usedPlace)) {
                throw new BaseBusinessException("禁止删除用户,被以下表引用:" + usedPlace);
            }
            return R.ok(this.pmsSaUserService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaUserPojo saUserPojo = this.pmsSaUserService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saUserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

//    @ApiOperation(value = "头像上传", notes = "图片上传", produces = "application/json")
//    @RequestMapping(value = "/uploadPic", method = RequestMethod.POST)
//    public R<FileInfo> uploadPic(MultipartFile file, HttpServletRequest request, String ossType) {
//        try {
////            String dir = "picture/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
//            FileInfo fileInfo = null;
//
//            if (StringUtils.isBlank(ossType)) {
//                ossType = OSSTYPE;
//            }
//            if (ossType.equals("minio")) {
//                fileInfo = fileInfoMinioService.upload(file, "picture");
//            } else if (ossType.equals("aliyun")) {
//                fileInfo = fileInfoAliYunService.upload(file, "picture");
//            }
//
//            SaUserPojo saUserPojo = new SaUserPojo();
//            saUserPojo.setAvatar(fileInfo.getDirname() + "/" + fileInfo.getFilename());
//            saUserPojo.setDirname(fileInfo.getDirname());
//            saUserPojo.setFilename(fileInfo.getFilename());
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            saUserPojo.setId(loginUser.getUserid());
//            //更新用户信息(保存头像)
//            pmsSaUserService.update(saUserPojo);
//            return R.ok(fileInfo);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    //type不为空时，表示是Web登录，进行权限校验roletype和Adminmark都不为1时，说明不是无Web端登录权限
    @ApiOperation(value = "用户名密码登录", notes = "用户名密码登录", produces = "application/json")
    @RequestMapping(value = "login", method = RequestMethod.POST)
    public R<Map<String, Object>> login(@RequestBody String json, HttpServletRequest request, String type) {
        try {
            LoginUserPojo loginUserPojo = JSON.parseObject(json, LoginUserPojo.class);
            Map<String, Object> token = pmsSaUserService.loginCheck(loginUserPojo, request, type);
            return R.ok(token, "登录成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "通过openidToken登录(oam服务redis中有键值对openid_token:openid)", notes = "openidToken登录", produces = "application/json")
    @RequestMapping(value = "/loginByOpenidToken", method = RequestMethod.POST)
    public R<Map<String, Object>> loginByOpenidToken(String openidToken, HttpServletRequest request) {
        try {
            String openidFromOam = getOpenidFromOam(openidToken);
            Map<String, Object> token = pmsSaUserService.loginCheckByOpenid(openidFromOam, request);
            return R.ok(token, "登录成功");
        } catch (BaseBusinessException | IOException e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Rms登录：直接通过openid登录", notes = "openidToken登录", produces = "application/json")
    @RequestMapping(value = "/loginRmsByOpenid", method = RequestMethod.GET)
    public R<Map<String, Object>> loginRmsByOpenid(String openid, HttpServletRequest request) {
        try {
            Map<String, Object> token = pmsSaUserService.loginRmsByOpenid(openid, request);
            return R.ok(token, "登录成功");
        } catch (BaseBusinessException e) {
            return R.fail(e.getMessage());
        }
    }

    private String getOpenidFromOam(String openidToken) throws IOException {
        // 创建 OkHttpClient 对象
        OkHttpClient client = new OkHttpClient();
        // 创建请求 URL
        String url = OAM_API + "/wx/qrcode/{appid}/getOpenidByOpenidToken";
        url = url.replace("{appid}", OAM_APPID) + "?openidToken=" + openidToken;
        // 创建请求
        Request requestOam = new Request.Builder()
                .url(url)
                .get()
                .build();
        // 发起请求并处理响应
        try (Response response = client.newCall(requestOam).execute()) {
            if (!response.isSuccessful()) {
                throw new BaseBusinessException("OkHttp3发送请求失败：" + response.code());
            }
            //oam.responseBody格式--->     有openid:{"code":200,"msg":null,"data":"ozJNq6-aO6sKR4ehV3BSmqooAvFI"}
            //                            无openid:{"code":500,"msg":"redis中未找到openidToken或已过期","data":null}
            String responseBody = response.body().string(); // response.body()会消耗响应体的内容，并将其关闭,若再次尝试访问响应体时就会抛出 java.lang.IllegalStateException: closed 异常
            JSONObject jsonObject = JSON.parseObject(responseBody);
            String openid = jsonObject.getString("data");
            if (StringUtils.isBlank(openid)) {
                throw new BaseBusinessException(responseBody);
            }
            return openid;
        }
    }


    @ApiOperation(value = "获取用户信息", notes = "获取用户信息", produces = "application/json")
    @RequestMapping(value = "/getUserInfo", method = RequestMethod.GET)
    public R<SaUserPojo> getUserInfo() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaUserPojo saUserPojo = pmsSaUserService.getUserInfo(loginUser);
            return R.ok(saUserPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    @ApiOperation(value = "登录", notes = "登录", produces = "application/json")
//    @RequestMapping(value = "/wxlogin", method = RequestMethod.POST)
//    public R<Map<String, Object>> wxlogin(@RequestBody String json, HttpServletRequest request) {
//        System.out.println("-----------------------------------------------------------------------------");
//        System.out.println("我想要的值是：" + json);
//        try {
//            WechatLoginPojo wechatLoginPojo = JSON.parseObject(json, WechatLoginPojo.class);
//            Map<String, Object> map = wechatMiniappLoginService.login(wechatLoginPojo, request);
//            return R.ok(map, "登录成功");
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }


//    @ApiOperation(value = "修改密码", notes = "修改密码", produces = "application/json")
//    @RequestMapping(value = "/updatePass", method = RequestMethod.POST)
//    public R updatePass(@RequestBody String key) {
//        try {
//            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
//            SaUserloginPojo saUserloginPojo = new SaUserloginPojo();
//            saUserloginPojo.setUserid(loginUser.getUserid());
//            saUserloginPojo.setUserpassword(AESUtil.Encrypt(key));
//            saUserloginPojo.setLister(loginUser.getRealName());
//            saUserloginPojo.setListerid(loginUser.getUserid());
//            saUserloginService.insert(saUserloginPojo);
//            return R.ok();
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    @ApiOperation(value = "用户修改个人密码(先校验旧密码)", notes = "修改密码", produces = "application/json")
    @RequestMapping(value = "/updatePass", method = RequestMethod.GET)
    public R updatePass(String oldpassword, String newpassword) {
        try {
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            // 先校验旧密码
            SaUserPojo saUserPojoDB = this.pmsSaUserMapper.checkPasswordByUserid(loginUser.getUserid(), AESUtil.Encrypt(oldpassword));
            if (saUserPojoDB == null) {
                return R.fail("旧密码错误");
            }
            // 再更新新密码
            SaUserPojo saUserPojo = new SaUserPojo();
            saUserPojo.setId(loginUser.getUserid());
//            saUserPojo.setPassword(AESUtil.Encrypt(newpassword));
            // update方法里做了加密了 这里不需要再加密
            saUserPojo.setPassword(newpassword);
            saUserPojo.setLister(loginUser.getRealName());
            saUserPojo.setModifydate(new Date());
            pmsSaUserService.update(saUserPojo);
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "管理员admin账号初始化密码", notes = "", produces = "application/json")
    @RequestMapping(value = "/initPass", method = RequestMethod.GET)
    public R initPass(String password) {
        try {
            //加密密码
            String encryptPassword = AESUtil.Encrypt(password);
            pmsSaUserMapper.updateAdminPassword(encryptPassword);
            return R.ok("初始化密码成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "管理员默认重置任一用户密码123456; 若传入密码,则重置为指定密码", notes = "管理员重置任一用户密码", produces = "application/json")
    @RequestMapping(value = "/resetPass", method = RequestMethod.GET)
    public R resetPass(String userid, String newpassword) {
        try {
            LoginUser loginUser = this.saRedisService.getLoginUser(ServletUtils.getRequest());
            // AdminMark ： 0、非管理员  1、普通管理员 2、超级管理员
            if (loginUser.getIsadmin() == 0) {
                return R.fail("非管理员无权重置密码");
            } else if (loginUser.getIsadmin() == 1) {
                int adminMark = pmsSaUserMapper.getAdminMarkByUserid(userid);
                if (adminMark == 1 || adminMark == 2) {
                    return R.fail("普通管理员无权重置管理员密码,请联系超级管理员重置");
                }
            }
            // 再更新新密码
            SaUserPojo saUserPojo = new SaUserPojo();
            saUserPojo.setId(userid);
            String password = StringUtils.isBlank(newpassword) ? "123456" : newpassword;
            saUserPojo.setPassword(password);
            saUserPojo.setLister(loginUser.getRealName());
            saUserPojo.setModifydate(new Date());
            pmsSaUserService.update(saUserPojo);
            return R.ok("重置密码成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取当前设备SN码", notes = "MAC-->MD5", produces = "application/json")
    @RequestMapping(value = "/getSN", method = RequestMethod.GET)
    public R<String> getSN() {
        return R.ok(SN.getSN());
    }

    @ApiOperation(value = "解密(直接读取Config参数中的system.registrkey,再解密后返回)", notes = "", produces = "application/json")
    @RequestMapping(value = "/decrypt", method = RequestMethod.GET)
    public R<String> decrypt() {
        try {
            // 获取加密的密文 原文为:{"code":"oms","sn":"123456","key":"xxxx-xxxx-xxxx","uc":10,"ex":1704038400}
            String systemRegistrkey = saConfigMapper.getSystemRegistrkey();
            if (StringUtils.isBlank(systemRegistrkey)) {
                return R.fail("system.registrkey为空");
            }
            // RSA私钥解密为原文
            String decrypt = RSADecryptor.decrypt(systemRegistrkey, MyRSA.PRIVATE_KEY);
            return R.ok(decrypt);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "解密+校验硬件(先解密成功返回解密文本,若未注册则返500,注册了但硬件不匹配返402)", notes = "", produces = "application/json")
    @RequestMapping(value = "/checkHard", method = RequestMethod.GET)
    public R<String> checkHard() {
        try {
            // 获取加密的密文 原文为:{"code":"oms","sn":"123456","key":"xxxx-xxxx-xxxx","uc":10,"ex":1704038400}
            String systemRegistrkey = saConfigMapper.getSystemRegistrkey();
            if (StringUtils.isBlank(systemRegistrkey)) {
                return R.fail("未注册!system.registrkey为空");
            }
            // RSA私钥解密为原文
            String decrypt = RSADecryptor.decrypt(systemRegistrkey, MyRSA.PRIVATE_KEY);
            Map<String, String> map = JSON.parseObject(decrypt, new TypeReference<Map<String, String>>() {
            });
            String sn = map.get("sn");
            if (sn.equals(SN.getSN())) {
                return R.ok(decrypt);
            } else {
                return R.fail(402, "硬件校验失败");
            }
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


//    -------------老版: 扫码登录-----------------------------------------

//    @ApiOperation(value = "(老版)获取登录二维码String", notes = "获取登录二维码String", produces = "application/json")
//    @RequestMapping(value = "/getScanLoginCode", method = RequestMethod.GET)
//    public R<Map<String, Object>> getScanLoginCode() {
//        try {
//            //生成key 即UUID
//            String uuid = UUID.randomUUID().toString();
//            //code100存入redis
//            Map<String, Object> missionMsg = new HashMap<>();
//            missionMsg.put("code", "100");
//            missionMsg.put("msg", "任务开始处理");
//            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
//            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
//            //返回前端type,date(key)
//            Map<String, Object> map = new HashMap<>();
//            map.put("type", "sapmslogin");
//            Map<String, Object> dataMap = new HashMap<>();
//            dataMap.put("key", uuid);
//            map.put("data", dataMap);
//            return R.ok(map);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
//
//    /** pms登录
//     *  key为UUID，code为前端微信的code
//     */
//    @ApiOperation(value = "(老版)PMS扫码登录", notes = "扫码登录", produces = "application/json")
//    @RequestMapping(value = "/scanLoginCode", method = RequestMethod.POST)
//    public R<String> scanLoginCode(@RequestBody String json, HttpServletRequest request) {
//        JSONObject jsonObj = JSON.parseObject(json);
//        String type = jsonObj.getString("type");
//        JSONObject dataObj = jsonObj.getJSONObject("data");
//        String key = dataObj.getString("key");
//        String api = dataObj.getString("api");
//        String code = dataObj.getString("code");
//        Map<String, Object> tokenMap = null;
//        //判断是小程序登录还是webadmin登录...
//        if ("sapmslogin".equals(type)) {
//            tokenMap = wechatMiniappLoginService.scanLogin(code, key, request);
//        } else {
//            return R.fail("登录类型错误");
//        }
//        //设置当前计算任务进度
//        Map<String, Object> missionMsg = new HashMap<>();
//        missionMsg.put("code", "200");
//        missionMsg.put("msg", "登录成功");
//        missionMsg.put("token", tokenMap);
//        this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
//        saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
//        return R.ok("扫码登录成功");
//    }
//
//    @ApiOperation(value = "(老版)获取扫码登录状态", notes = "获取扫码登录状态", produces = "application/json")
//    @RequestMapping(value = "/getScanLoginState", method = RequestMethod.GET)
//    public R<Map<String, Object>> getScanLoginState(@RequestParam String key) {
//        Map<String, Object> scanLoginState = this.saRedisService.getCacheMapValue(SCANLOGIN_CODE, key);
//        return R.ok(scanLoginState);
//    }
//
//    @ApiOperation(value = "(老版)WebAdmin扫码登录", notes = "扫码登录", produces = "application/json")
//    @RequestMapping(value = "/scanWebAdminLoginCode", method = RequestMethod.POST)
//    public R<String> scanWebAdminLoginCode(@RequestBody String json, HttpServletRequest request) {
//        JSONObject jsonObj = JSON.parseObject(json);
////        String type = jsonObj.getString("type");
//        JSONObject dataObj = jsonObj.getJSONObject("data");
//        String key = dataObj.getString("key");
//        String api = dataObj.getString("api");
//        String code = dataObj.getString("code");
//        String openid = wechatMiniappLoginService.getOpenIdByCode(code);
//        String toOmsAuthurl = api + "/auth/admin/scanLoginCode?openid=" + openid + "&key=" + key;
//        String responseBody = HttpUtil.get(toOmsAuthurl);
//        JSONObject omsRes = JSONArray.parseObject(responseBody);
//        if ("扫码登录成功".equals(omsRes.getString("data"))) {
//            return R.ok("扫码登录成功");
//        } else {
//            throw new BaseBusinessException("扫码登录失败");
//        }
//    }
//
//    @ApiOperation(value = "(老版)获取openid(通过Code)", notes = "获取openid", produces = "application/json")
//    @RequestMapping(value = "/getOpenIdByCode", method = RequestMethod.GET)
//    public R<String> getOpenIdByCode( String code) {
//        String openid = wechatMiniappLoginService.getOpenIdByCode(code);
//        return R.ok(openid);
//    }
//
//
//    /** pms登录
//     *  key为UUID，code为前端微信的code
//     */
//    @ApiOperation(value = "(老版)PMS微信扫码登录", notes = "扫码登录", produces = "application/json")
//    @RequestMapping(value = "/getWxScanLogin", method = RequestMethod.GET)
//    public R<String> getWxScanLogin(String data, HttpServletRequest request) {
//        return R.ok(data);
//    }

    // 放到一个无路由前缀的空Controller里使用！！
    @GetMapping("/{filename:.+}")
    public String getFile(@PathVariable String filename) {
        //return "5668e41a079f2990127f6be8e7dc4988";
        return "MFOct7FigLrXAJzO";
    }
}

