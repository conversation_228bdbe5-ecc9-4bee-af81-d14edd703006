package inks.service.sa.pms.controller;


import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.AjaxResult;
import inks.common.core.text.Convert;
import inks.service.sa.pms.domain.gen.GenTable;
import inks.service.sa.pms.domain.gen.GenTableColumn;
import inks.service.sa.pms.domain.gen.TableDataInfo;
import inks.service.sa.pms.service.IGenTableColumnService;
import inks.service.sa.pms.service.IGenTableService;
import io.swagger.annotations.Api;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 代码生成 操作处理
 *
 * <AUTHOR>
 */

@Api(tags = "Gen:前端代码生成器")
@RestController
@RequestMapping("/tool/gen")
public class GenController extends BaseController {
    @Autowired
    private IGenTableService genTableService;

    @Autowired
    private IGenTableColumnService genTableColumnService;

    /**
     * 查询代码生成列表
     */
    @GetMapping("/list")
    public TableDataInfo genList(GenTable genTable) {
        startPage();
        List<GenTable> list = genTableService.selectGenTableList(genTable);
        return getDataTable(list);
    }

    /**
     * 修改代码生成业务
     */
    @GetMapping(value = "/{tableId}")
    public AjaxResult getInfo(@PathVariable Long tableId) {
        GenTable table = genTableService.selectGenTableById(tableId);
        List<GenTable> tables = genTableService.selectGenTableAll();
        List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(tableId);
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("info", table);
        map.put("rows", list);
        map.put("tables", tables);
        return success(map);
    }

    /**
     * 查询数据库列表
     */
    @GetMapping("/db/list")
    public TableDataInfo dataList(GenTable genTable) {
        startPage();
        List<GenTable> list = genTableService.selectDbTableList(genTable);
        return getDataTable(list);
    }

    /**
     * 查询数据表字段列表
     */
//    @PreAuthorize("@ss.hasPermi('tool:gen:list')")
    @GetMapping(value = "/column/{tableId}")
    public TableDataInfo columnList(Long tableId) {
        TableDataInfo dataInfo = new TableDataInfo();
        List<GenTableColumn> list = genTableColumnService.selectGenTableColumnListByTableId(tableId);
        dataInfo.setRows(list);
        dataInfo.setTotal(list.size());
        return dataInfo;
    }

    /**
     * 导入表结构（保存）
     */
//    @PreAuthorize("@ss.hasPermi('tool:gen:import')")
    @PostMapping("/importTable原始")
    public AjaxResult importTableSave原始(String tables) {
        String[] tableNames = Convert.toStrArray(tables);
        // 查询表信息
        List<GenTable> tableList = genTableService.selectDbTableListByNames(tableNames);
        genTableService.importGenTable(tableList);
        return success();
    }

    /**
     * New--导入表结构（保存）
     * 前端传入 var obj = {
     * tables: this.selrows.tablename,
     * database: this.formdata.tabases,
     * moducename: this.formdata.moducename,
     * businessname: this.formdata.businessname,
     * };
     */
    @PostMapping("/importTable")
    public AjaxResult importTableSave(@RequestBody String json) {
        // 将JSON字符串转换为JSONObject
        JSONObject jsonObject = JSON.parseObject(json);
        // 获取参数的值
        String tables = jsonObject.getString("tables");
        String database = jsonObject.getString("database");
        String modulename = jsonObject.getString("modulename");
        String businessname = jsonObject.getString("businessname");
        String[] tableNames = Convert.toStrArray(tables);
        // 查询表信息
        List<GenTable> tableList = genTableService.selectDbTableListBy2Names(tableNames, database);
        // tableList每一项都加上modulename和businessname
        // 遍历表列表
        for (int i = 0; i < tableList.size(); i++) {
            GenTable genTable = tableList.get(i);
            // 设置模块名和业务名
            genTable.setModuleName(modulename);
            genTable.setBusinessName(businessname);
            // 判断当前项【不是】以"Item"结尾
            if (!genTable.getTableName().endsWith("Item")) {
                // 获取当前项
                String currentTableName = genTable.getTableName();
                // 遍历当前项后面的每一项
                for (int j = i + 1; j < tableList.size(); j++) {
                    GenTable nextGenTable = tableList.get(j);
                    // 判断是否存在以当前项加上"Item"结尾的表名
                    if (nextGenTable.getTableName().equals(currentTableName + "Item")) {
                        // 设置当前项的tableItemName属性为表名
                        genTable.setSubTableName(nextGenTable.getTableName());
                        genTable.setSubTableFkName("Pid");
                        genTable.setTplCategory("sub");
                        break;
                    }
                }
            }
        }
        genTableService.importGenTableBy2Names(tableList, database);
        return success();
    }

    /**
     * 修改保存代码生成业务
     */
    @PutMapping
    public AjaxResult editSave(@Validated @RequestBody GenTable genTable) {
        genTableService.validateEdit(genTable);
        genTableService.updateGenTable(genTable);
        return success();
    }

    /**
     * 删除代码
     */
    @DeleteMapping("/{tableIds}")
    public AjaxResult remove(@PathVariable Long[] tableIds) {
        genTableService.deleteGenTableByIds(tableIds);
        return success();
    }

    /**
     * 预览代码
     */
    @GetMapping("/preview原始/{tableId}")
    public AjaxResult preview原始(@PathVariable("tableId") Long tableId) throws IOException {
        Map<String, String> dataMap = genTableService.previewCode(tableId);
        return success(dataMap);
    }

    /**
     * 预览代码
     */
    @GetMapping("/preview/{tableId}")
    public AjaxResult preview(@PathVariable("tableId") Long tableId) throws IOException {
        Map<String, String> dataMap = genTableService.previewCodeNew(tableId);
        return success(dataMap);
    }

    /**
     * 生成代码（下载方式）
     */
    @GetMapping("/download/{tableName}")
    public void download(HttpServletResponse response, @PathVariable("tableName") String tableName) throws IOException {
        byte[] data = genTableService.downloadCode(tableName);
        genCode(response, data);
    }

    /**
     * 生成代码（自定义路径）
     */
    @GetMapping("/genCode/{tableName}")
    public AjaxResult genCode(@PathVariable("tableName") String tableName) {
        genTableService.generatorCode(tableName);
        return success();
    }

    /**
     * 同步数据库
     */
    @GetMapping("/synchDb/{tableName}")
    public AjaxResult synchDb(@PathVariable("tableName") String tableName) {
        genTableService.synchDb(tableName);
        return success();
    }

    /**
     * 批量生成代码
     */
    @GetMapping("/batchGenCode")
    public void batchGenCode(HttpServletResponse response, String tables) throws IOException {
        String[] tableNames = Convert.toStrArray(tables);
        byte[] data = genTableService.downloadCode(tableNames);
        genCode(response, data);
    }

    /**
     * 生成zip文件
     */
    private void genCode(HttpServletResponse response, byte[] data) throws IOException {
        response.reset();
        response.addHeader("Access-Control-Allow-Origin", "*");
        response.addHeader("Access-Control-Expose-Headers", "Content-Disposition");
        response.setHeader("Content-Disposition", "attachment; filename=\"ruoyi.zip\"");
        response.addHeader("Content-Length", String.valueOf(data.length));
        response.setContentType("application/octet-stream; charset=UTF-8");
        IOUtils.write(data, response.getOutputStream());
    }
}