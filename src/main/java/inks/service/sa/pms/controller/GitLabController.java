package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.*;
import inks.service.sa.pms.mapper.SaEngineerMapper;
import inks.service.sa.pms.mapper.pmsSaUserMapper;
import inks.service.sa.pms.service.SaDemandService;
import inks.service.sa.pms.service.SaProjectService;
import inks.service.sa.pms.service.SaReleaseService;
import inks.service.sa.pms.utils.PrintColor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;


@RestController
@RequestMapping("GitLab")
@Api(tags = "GitLab")
public class GitLabController {

    @Resource
    private SaDemandService saDemandService;
    @Resource
    private SaReleaseService saReleaseService;
    @Resource
    private pmsSaUserMapper pmsSaUserMapper;
    @Resource
    private SaEngineerMapper saEngineerMapper;
    @Resource
    private SaProjectService saProjectService;
    @Resource
    private SaRedisService saRedisService;

    // 获取今天的提交记录
    // userid realname 选填,是Sa_User表的id和RealName
    public static List<JSONObject> gitLabTodayEvents(String gitLabAccessToken, String gitLabUserName, String userid, String realname) {
        OkHttpClient client = new OkHttpClient();
        String gitLabUrl = "http://git.inksdev.com";
//        String accessToken = "**************************";
        //before 和 after 参数的日期应按以下格式提供：YYYY-MM-DD
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String endDay = LocalDate.now().plusDays(1).format(dateTimeFormatter);
        String startDay = LocalDate.now().minusDays(1).format(dateTimeFormatter);
        PrintColor.zi("endDay: " + endDay + " startDay: " + startDay);
        // 接口文档:https://docs.gitlab.com/ee/api/events.html#list-currently-authenticated-users-events  action可选参数: https://docs.gitlab.com/ee/user/profile/**********************.html#user-contribution-events
        // 假设今天3.16 则查询after=2023-03-15 实际上GitLab只会返回的是2023-03-16的数据 :即查今天的提交记录
        // (时区问题???GitLab采用的都是是0时区--->"created_at":"2024-03-16T06:24:17.975Z")
        String url = gitLabUrl + "/api/v4/events?username=" + gitLabUserName + "&action=pushed&after=" + startDay;
//示例登录后打开:http://git.inksdev.com/api/v4/events?username=nanno.jiang&action=pushed&after=2024-04-09
        PrintColor.red(url);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("PRIVATE-TOKEN", gitLabAccessToken)
                .build();
        try (Response response = client.newCall(request).execute()) {
            String result = response.body().string();
            List<JSONObject> events = JSON.parseArray(result, JSONObject.class);
            // 过滤出创建日期为今天的事件
            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            List<JSONObject> todayEvents = events.stream()
                    .filter(event -> {
                        String createdAt = event.getString("created_at");
                        ZonedDateTime eventDate = ZonedDateTime.parse(createdAt).withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                        return LocalDate.now().isEqual(eventDate.toLocalDate());
                    })  // 到这里是GitLab提交日志的详细信息(有需要可拿到此处信息),下面只过滤到要2个字段
                    .map(event -> {  // 只需要东八区的时间和"commit_title"
                        JSONObject newEvent = new JSONObject();
                        String createdAt = event.getString("created_at");
                        ZonedDateTime eventDate = ZonedDateTime.parse(createdAt).withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                        String commitTitle = event.getJSONObject("push_data").getString("commit_title");
                        String projectId = event.getString("project_id");

                        // 如果commit_title以...结尾(commit_title字段通常是截取的提交信息的第一行，如果这一行过长，可能会被截断并以...结尾。这是GitLab API的设计决定)
                        // 如果commit_title想手动获取完整信息，则自行加入"..."
                        // 发送新的请求获取commit的详细信息 http://git.inksdev.com/api/v4/projects/{project_id}/repository/commits/{commit_id}
                        if (commitTitle.contains("...")) {
                            String commitId = event.getJSONObject("push_data").getString("commit_to");
                            String commitUrl = gitLabUrl + "/api/v4/projects/" + projectId + "/repository/commits/" + commitId;
//示例登录后打开:http://git.inksdev.com/api/v4/projects/51/repository/commits/a812ad9e2e007fd24a339a653feedf4bb02ab937
//示例登录后打开:http://git.inksdev.com/api/v4/projects/129/repository/commits/3b85dcbff144bcac4684c45d41f6b9dae0ecabf1
                            Request commitRequest = new Request.Builder()
                                    .url(commitUrl)
                                    .addHeader("PRIVATE-TOKEN", gitLabAccessToken)
                                    .build();
                            try (Response commitResponse = client.newCall(commitRequest).execute()) {
                                String commitResult = commitResponse.body().string();
                                JSONObject commitData = JSON.parseObject(commitResult);
                                commitTitle = commitData.getString("message");
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                        newEvent.put("created_at", formatter.format(eventDate));
                        // 获取项目的名字,用于拼接 将项目的名字添加到commit_title的前面
                        String projectName = getGitLabProjectName(gitLabAccessToken, client, gitLabUrl, projectId);
                        if (isNotBlank(projectName)) {
                            commitTitle = projectName + ": " + commitTitle;
                        }
                        newEvent.put("commit_title", commitTitle);
                        if (userid != null && realname != null) {
                            newEvent.put("userid", userid);
                            newEvent.put("realname", realname);
                            newEvent.put("projectName", projectName);
                        }
                        return newEvent;
                    })
                    .collect(Collectors.toList());
            PrintColor.zi("todayEvents: " + todayEvents);
            return todayEvents;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null; //获取失败
    }

    //      示例值:   String commitTitle = "oms: [发布]pms,53，S06M02B1 子任务功能举例... ";
    public static String[] parseCommitTitle(String commitTitle) {
        // 初始化默认值
        String[] outputCommit = new String[]{
                "GitLab提交内容.split(,)[0] is null",
                "GitLab提交内容.split(,)[1] is null",
                "GitLab提交内容.split(,)[2] is null"
        };

        if (commitTitle.contains("【发布】") || commitTitle.contains("[发布]")) {
            String[] parts = commitTitle.split("【发布】|[发布]");
            if (parts.length > 1) {
                // 获取【发布】或[发布]后面的字符串
                String afterRelease = parts[1].trim();
                // 根据,或者，分隔成3部分，前两个正常分，之后的作为一个整体不分
                String[] splitParts = afterRelease.split("[,，]", 3);
                // 更新结果
                for (int i = 0; i < splitParts.length && i < 3; i++) {
                    outputCommit[i] = splitParts[i].trim();
                }
            }
        }
        // 打印前3个结果
        for (int i = 0; i < 3; i++) {
            System.out.println(outputCommit[i]);
        }
        return outputCommit;
    }

    // 发送请求获取项目的名字
    //inks-service-sa-doc 以inks-service-sa-开头的只要doc                       示例:   http://git.inksdev.com/api/v4/projects/19
    //inks-service-sale-master 以inks-service-开头并且以"-master"结尾的只要sale  示例:   http://git.inksdev.com/api/v4/projects/47
    //inks-service-oem-ym 以inks-service-开头并的只要oem-ym
    //inks-cloud-master 全称inks-cloud-master的只要cloud
    //其他情况不要项目名了
    private static String getGitLabProjectName(String accessToken, OkHttpClient client, String gitLabUrl, String projectId) {
        String projectUrl = gitLabUrl + "/api/v4/projects/" + projectId;
        Request projectRequest = new Request.Builder()
                .url(projectUrl)
                .addHeader("PRIVATE-TOKEN", accessToken)
                .build();
        String projectName = "";
        try (Response projectResponse = client.newCall(projectRequest).execute()) {
            String projectResult = projectResponse.body().string();
            JSONObject projectData = JSON.parseObject(projectResult);
            projectName = projectData.getString("name");
            // 分割项目名称并获取需要的部分
            if (projectName.startsWith("inks-service-sa-")) {
                projectName = projectName.substring("inks-service-sa-".length());
            } else if (projectName.startsWith("inks-service-") && projectName.endsWith("-master")) {
                String[] parts = projectName.split("-");
                if (parts.length > 2) {
                    projectName = parts[2];
                }
            } else if (projectName.startsWith("inks-service-")) {
                projectName = projectName.substring("inks-service-".length());
            } else if ("inks-cloud-master".equals(projectName)) {
                projectName = "cloud";
            }
//            else {
//                // 其他情况保留原样
//            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return projectName;
    }

    // 获取GitLab的 UserName:   nanno.jiang
    private static String getGitLabUsername(String accessToken) {
        OkHttpClient client = new OkHttpClient();
        String gitLabUrl = "http://git.inksdev.com"; // 替换为你的 GitLab 实例 URL
        String url = gitLabUrl + "/api/v4/user";
        Request request = new Request.Builder()
                .url(url)
                .addHeader("PRIVATE-TOKEN", accessToken)
                .build();
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                assert response.body() != null;
                String responseBody = response.body().string();
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                // String email = jsonObject.getString("email");
                return jsonObject.getString("username");
            } else {
                System.err.println("Request failed: " + response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Test
    public void git1() {
        // GitLab 实例的 URL
        String gitLabUrl = "http://git.inksdev.com";
        // 访问令牌
        String accessToken = "**************************";
        // 创建 OkHttpClient
        OkHttpClient client = new OkHttpClient();
        // 创建 Request
        Request request = new Request.Builder()
                .url(gitLabUrl + "/api/v4/projects")
                .addHeader("PRIVATE-TOKEN", accessToken)
                .build();
        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            // 打印响应的内容
            System.out.println(response.body().string());
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    @Test
    public void gitLabTodayEventsTest() throws ParseException {
        List<JSONObject> gitLabTodayEvents = gitLabTodayEvents("**************************", "nanno.jiang", null, null);
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (JSONObject event : gitLabTodayEvents) {
            String created_at = event.getString("created_at");
            String commit_title = event.getString("commit_title");
            Date date = formatter.parse(created_at);
            PrintColor.red("created_at: " + date + " commit_title: " + commit_title);
        }
    }

    @Test
    public void fastjson() {
        String jsonStr = "{\"name\":\"John\", \"age\":30, \"city\":\"New York\"}";
        Map<String, Object> map = JSON.parseObject(jsonStr, new TypeReference<Map<String, Object>>() {
        });
        Map<String, Object> map1 = JSON.parseObject(jsonStr, Map.class);
        //打印
        PrintColor.zi(map.toString());


    }

    @GetMapping("/Nanno")
    @Scheduled(cron = "0 26 17 * * ?")
    public R<String> Nanno() {
        // 先手动执行: 清除过期的Sa_Redis
        saRedisService.cleanSa_Redis();
        String lockKey = "Nanno";
        String lockString = saRedisService.getCacheObject(lockKey, String.class);
        //lock.tryLock(2, 10, TimeUnit.MINUTES)的行为是：尝试获取锁，如果锁不可用，那么等待最多2分钟，期间如果锁变为可用，那么立即获取锁并返回true；
        // 如果在2分钟内锁一直不可用，那么等待结束后返回false。如果获取成功，那么这把锁会被持有10分钟，除非在这之前已经显式地释放了锁。
        if (isBlank(lockString)) {
            PrintColor.red("未上锁:saRedisService.getCacheObject(lockKey, String.class)");
            //上锁
            saRedisService.setCacheObject(lockKey, "lock", 10, TimeUnit.MINUTES);
            try {
                return syncDemandFromGitLab("**************************", "nanno.jiang", "蒋云飞", "Nanno", 1);
            } finally {
                saRedisService.deleteObject(lockKey);
            }
        } else {
            return R.fail("其他实例正在执行任务");
        }
    }

    @GetMapping("/NannoUnFinish") //同/Nanno,只是需求单finishMark=0
    public R<String> NannoUnFinish() {
        // 先手动执行: 清除过期的Sa_Redis
        saRedisService.cleanSa_Redis();
        String lockKey = "Nanno";
        String lockString = saRedisService.getCacheObject(lockKey, String.class);
        //lock.tryLock(2, 10, TimeUnit.MINUTES)的行为是：尝试获取锁，如果锁不可用，那么等待最多2分钟，期间如果锁变为可用，那么立即获取锁并返回true；
        // 如果在2分钟内锁一直不可用，那么等待结束后返回false。如果获取成功，那么这把锁会被持有10分钟，除非在这之前已经显式地释放了锁。
        if (isBlank(lockString)) {
            PrintColor.red("未上锁:saRedisService.getCacheObject(lockKey, String.class)");
            //上锁
            saRedisService.setCacheObject(lockKey, "lock", 10, TimeUnit.MINUTES);
            try {
                return syncDemandFromGitLab("**************************", "nanno.jiang", "蒋云飞", "Nanno", 0);
            } finally {
                saRedisService.deleteObject(lockKey);
            }
        } else {
            return R.fail("其他实例正在执行任务");
        }
    }

    @ApiOperation(value = "GitLab同步需求 GitLab提交标题包含^不生成需求,包含·或`累加工时1h,包含--记录到周会WeekMark,自动计算开始结束时间", notes = "", produces = "application/json")
    @GetMapping("/syncDemandFromGitLabByLogin")
    public R<String> syncDemandFromGitLabByLogin() {
        LoginUser loginUser = saRedisService.getLoginUser();
        SaUserPojo userPojo = pmsSaUserMapper.getEntity(loginUser.getUserid());
        String gitlabusername = userPojo.getGitlabusername();
        String projectName = gitlabusername;
        switch (projectName) {
            case "nanno.jiang":
                projectName = "Nanno";
                break;
            case "edan.hu":
                projectName = "Edan";
                break;
            case "denglujun":
                projectName = "Deng";
                break;
            default:
                break;
        }
        return syncDemandFromGitLab(userPojo.getGitlabtoken(), gitlabusername, userPojo.getRealname(), projectName, 1);
    }

    @ApiOperation(value = "GitLab同步需求 GitLab提交标题包含^不生成需求,包含·或`累加工时1h,包含--记录到周会WeekMark,自动计算开始结束时间 finishMark=1置状态为已完成", notes = "", produces = "application/json")
    @GetMapping("/syncDemandFromGitLab")
    public R<String> syncDemandFromGitLab(String gitLabToken, String gitLabUsername, String realName, String projectName,
                                          @RequestParam(defaultValue = "0") Integer finishMark) {
        try {
            // GitLab今天提交的事件
            List<JSONObject> gitLabEvents = gitLabTodayEvents(gitLabToken, gitLabUsername, null, null);
            // gitLabEvents判空
            if (CollectionUtils.isEmpty(gitLabEvents)) {
                return R.fail("今天没有提交记录");
            }
            SaUserPojo saUserPojo = pmsSaUserMapper.getEntityByRealName(realName);
            SaProjectPojo saProjectPojo = saProjectService.getBillEntityByProjectName(projectName);
            if (saProjectPojo == null) {
                return R.fail("没有找到项目:" + projectName);
            }
            // 找到需求状态id 开始OR已完成
            String statusId = "";
            List<SaProjectstatusPojo> status = saProjectPojo.getStatus();
            if (Objects.equals(finishMark, 1)) {
                statusId = status.stream()
                        .filter(s -> "已完成".equals(s.getStatustype()) && s.getFinishmark() == 1)
                        .map(s -> String.valueOf(s.getId()))
                        .findFirst()
                        .orElse(null);
            } else {
                statusId = status.stream()
                        .filter(s -> "开始".equals(s.getStatustype()))
                        .map(s -> String.valueOf(s.getId()))
                        .findFirst()
                        .orElse(null);
            }

            // loginUser业务层需要userid和realname
            LoginUser loginUser = new LoginUser();
            loginUser.setUserid(saUserPojo.getId());
            loginUser.setRealname(saUserPojo.getRealname());
            // 创建需求、已存在需求、禁止需求的记录
            StringBuilder titleCreate = new StringBuilder();
            StringBuilder titleExist = new StringBuilder();
            StringBuilder titleBan = new StringBuilder();
            int createCount = 0;
            int existCount = 0;
            int banCount = 0;
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            //執行者
            SaEngineerPojo engineerDB = saEngineerMapper.getEntityByUserId(saUserPojo.getId());
            // 多个事件创建多个需求
            for (JSONObject gitLabEvent : gitLabEvents) {
                SaDemandPojo newDemand = new SaDemandPojo();
                // GitLab提交事件的标题、提交时间
                String commitTitle = gitLabEvent.getString("commit_title");
                String createdAt = gitLabEvent.getString("created_at");
                Date gitDate = formatter.parse(createdAt);
                // 以下标题禁止生成: 1.包含^或…则不生成  2.系统提示不生成:Merge remote-tracking branch 'origin/master'、Initial commit
                if (commitTitle.contains("^") || commitTitle.contains("…") || commitTitle.contains("Merge remote-tracking branch") || commitTitle.contains("Initial commit")) {
                    banCount++;
                    titleBan.append(banCount).append(commitTitle).append("\n");
                    continue;
                }
                // 包含--记录到周会WeekMark
                if (commitTitle.contains("--")) {
                    newDemand.setWeekmark(1);
                }

                PrintColor.red("commitTitle------------------>" + commitTitle);
                // 项目状态中的第一个状态为开始状态
                newDemand.setStatus(statusId);
                //检查标题是否已经存在,存在则跳过 (只检查当天的需求)
                if (saDemandService.checkTitleExist(commitTitle)) {
                    existCount++;
                    titleExist.append(existCount).append(commitTitle).append("\n");
                    continue;
                }
                newDemand.setBilltitle(commitTitle);
                // 正常需求创建需要字段:
                newDemand.setProjectid(saProjectPojo.getId());
                newDemand.setBilltype("需求");
                newDemand.setItemcode(saProjectPojo.getProjcode());
                newDemand.setItemname(saProjectPojo.getProjname());
                //執行者
//                String engineerId = saEngineerMapper.getIdByUserId(saUserPojo.getId());
                newDemand.setAppointeeid(engineerDB.getId());
                newDemand.setAppointee(engineerDB.getEngineername());
                //计算工时:标题中的符号 ·或`(键盘ESC下的按键)的个数,出现一次累加1小时,没有出现默认0.5小时
                double workTime;
                long count = commitTitle.chars().filter(c -> c == '·' || c == '`').count();
                workTime = count == 0 ? 0.5 : count;
                newDemand.setWorktime(workTime);
                // git提交时间是需求完成(截止)时间
                newDemand.setDeaddate(gitDate);
                // 需求开始时间=gitDate减去工时
                newDemand.setStartdate(Date.from(gitDate.toInstant().minusSeconds((long) (workTime * 60 * 60))));

                //生成单据编码
                String refno = DateUtils.parseDateToStr("yyyyMMdd", new Date()) + String.format("%04d", new Random().nextInt(9999));
                newDemand.setRefno(refno);
                newDemand.setCreateby(saUserPojo.getRealname());
                newDemand.setCreatebyid(saUserPojo.getId());
                newDemand.setRemark("GitLab提交");
                // 20240619 避免自动生成的展现在pms软件的需求列表中(pms软件只展示FinshMark=0)
                newDemand.setFinishmark(finishMark);
                this.saDemandService.insert(newDemand, loginUser);
                createCount++;
                titleCreate.append(createCount).append(commitTitle).append("\n");
            }
            titleCreate = new StringBuilder("本次創建了" + createCount + "個需求:\n" + titleCreate);
            if (titleExist.length() > 0) {
                titleExist = new StringBuilder("以下" + existCount + "个标题已生成需求:\n" + titleExist);
            }
            if (titleBan.length() > 0) {
                titleBan = new StringBuilder("以下" + banCount + "个标题禁止生成需求:\n" + titleBan);
            }

            String result = titleCreate + "                                                                              \n"
                    + titleExist + "                                                                              \n"
                    + titleBan + "                                                                              \n"
                    + "GitLab API:\n" + gitLabEvents;
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "GitLab同步[版本发布]", notes = "GitLab提交标题示例:[发布]pms,53,S06M02B1,子任务功能,举例... ", produces = "application/json")
    @GetMapping("/syncReleaseFromGitLab")
    @Scheduled(cron = "0 32 17 * * ?")
    public R<String> syncReleaseFromGitLab() {
        try {
            // GitLab今天提交的事件:所有用户的提交记录
            List<SaUserPojo> allUsersDB = pmsSaUserMapper.getAllList();
            // 使用Stream API过滤和收集gitLabToken不为空的用户
            List<SaUserPojo> gitLabUsers = allUsersDB.stream()
                    .filter(user -> StringUtils.isNotBlank(user.getGitlabtoken()))
                    .collect(Collectors.toList());
            List<JSONObject> gitLabEventsTodayAllUsers = new ArrayList<>();
            for (SaUserPojo user : gitLabUsers) {
                List<JSONObject> gitLabEvents = gitLabTodayEvents(user.getGitlabtoken(), user.getGitlabusername(), user.getId(), user.getRealname());
                if (gitLabEvents != null) {
                    gitLabEventsTodayAllUsers.addAll(gitLabEvents);
                }
            }
            // gitLabEvents判空
            if (CollectionUtils.isEmpty(gitLabEventsTodayAllUsers)) {
                return R.fail("今天没有提交记录");
            }

            // 创建版本发布单、已存在发布单
            StringBuilder titleCreate = new StringBuilder();
            StringBuilder titleExist = new StringBuilder();
            int createCount = 0;
            int existCount = 0;
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

            // 多个事件创建多个 版本发布单
            // 举例gitLabEvent :
            // [{"commit_title":"oms: [发布]pms,53,S06M02B1 子任务功能,举例... ","created_at":"2024-06-05 09:41:21","userid":"1314","realname":"蒋劲夫"}]
            for (JSONObject gitLabEvent : gitLabEventsTodayAllUsers) {
                SaReleasePojo newRelease = new SaReleasePojo();
                // GitLab提交事件的标题、提交时间、提交人userid、realname
                String commitTitle = gitLabEvent.getString("commit_title");
                // 如果commitTitle不包含[发布]或【发布】则跳至下次循环
//                if (!commitTitle.contains("[发布]") && !commitTitle.contains("【发布】")) {
//                    continue;
//                }
                String createdAt = gitLabEvent.getString("created_at");
                String userid = gitLabEvent.getString("userid");
                String realname = gitLabEvent.getString("realname");
                String projectNameGitLab = gitLabEvent.getString("projectName");// GitLab上的实际项目名
                // 日期格式化 createdAt是字符串2024-06-05 10:10:11 转为Date类型
                Date gitDate = formatter.parse(createdAt);


                PrintColor.red("commitTitle------------------>" + commitTitle);

                // 通过remark检查版本发布是否已经存在,存在则跳过
                String remark = "GitLab版本发布:" + commitTitle;
                if (saReleaseService.checkExist(remark)) {
                    existCount++;
                    titleExist.append(existCount).append(commitTitle).append("\n");
                    continue;
                }
                // 正常需求创建需要字段:ReleaseName、ProjectName、ReleaseDate、Version、Description、Remark、CreateByid、CreateBy
                // 将commitTitle解析为3部分 gitCommit[0]、gitCommit[1]、gitCommit[2]
                // 分别对应                (发布名称)pms  (版本)53      (描述)S06M02B1 子任务功能,举例...
                String[] gitCommit = parseCommitTitle(commitTitle);
                newRelease.setReleasename(gitCommit[0]);
                newRelease.setProjectname(projectNameGitLab);
                newRelease.setReleasedate(gitDate);
                newRelease.setVersion(gitCommit[1]);
                newRelease.setDescription(gitCommit[2]);
                newRelease.setRemark(remark);
                newRelease.setCreateby(realname);
                newRelease.setCreatebyid(userid);
                this.saReleaseService.insert(newRelease);
                createCount++;
                titleCreate.append(createCount).append(commitTitle).append("\n");
            }
            titleCreate = new StringBuilder("本次創建了" + createCount + "個版本发布:\n" + titleCreate);
            if (titleExist.length() > 0) {
                titleExist = new StringBuilder("以下" + existCount + "个标题已生成版本发布:\n" + titleExist);
            }


            String result = titleCreate + "   \n"
                    + titleExist + "       \n"
                    + "GitLab API:\n" + gitLabEventsTodayAllUsers;
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
