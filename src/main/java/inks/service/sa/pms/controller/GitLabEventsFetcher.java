package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import inks.service.sa.pms.utils.PrintColor;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.junit.jupiter.api.Test;

import java.io.IOException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

public class GitLabEventsFetcher {

    public static void main(String[] args) {
        String accessToken = "**************************";
        String username = getGitLabUsername(accessToken);
        System.out.println("GitLab Username: " + username);
    }

    private static String getGitLabUsername(String accessToken) {
        OkHttpClient client = new OkHttpClient();
        String gitLabUrl = "http://git.inksdev.com"; // 替换为你的 GitLab 实例 URL
        String url = gitLabUrl + "/api/v4/user";

        Request request = new Request.Builder()
                .url(url)
                .addHeader("PRIVATE-TOKEN", accessToken)
                .build();

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                assert response.body() != null;
                String responseBody = response.body().string();
                JSONObject jsonObject = JSONObject.parseObject(responseBody);
                // String email = jsonObject.getString("email");
                return jsonObject.getString("username");
            } else {
                System.err.println("Request failed: " + response.code());
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return null;
    }

    @Test
    public void gitLabTodayEventsWithUserInfoTest() throws ParseException {
//        List<JSONObject> gitLabTodayEvents = gitLabTodayEventsWithUserInfo("**************************");
        List<JSONObject> gitLabTodayEvents = GitLabController.gitLabTodayEvents("**************************", "jiang.nanno", "1314", "蒋劲夫");
        SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        for (JSONObject event : gitLabTodayEvents) {
            String created_at = event.getString("created_at");
            String commit_title = event.getString("commit_title");
            Date date = formatter.parse(created_at);
            PrintColor.red("created_at: " + date + " commit_title: " + commit_title);
        }
    }

    private List<JSONObject> gitLabTodayEventsWithUserInfo(String accessToken) {
        OkHttpClient client = new OkHttpClient();
        String gitLabUrl = "http://git.inksdev.com";
        DateTimeFormatter dateTimeFormatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        String endDay = LocalDate.now().plusDays(1).format(dateTimeFormatter);
        String startDay = LocalDate.now().minusDays(1).format(dateTimeFormatter);
        PrintColor.zi("endDay: " + endDay + " startDay: " + startDay);

        String url = gitLabUrl + "/api/v4/events?action=pushed&after=" + startDay;
        PrintColor.red(url);
        Request request = new Request.Builder()
                .url(url)
                .addHeader("PRIVATE-TOKEN", accessToken)
                .build();
        try (Response response = client.newCall(request).execute()) {
            String result = response.body().string();
            List<JSONObject> events = JSON.parseArray(result, JSONObject.class);

            DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
            List<JSONObject> todayEvents = events.stream()
                    .filter(event -> {
                        String createdAt = event.getString("created_at");
                        ZonedDateTime eventDate = ZonedDateTime.parse(createdAt).withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                        return LocalDate.now().isEqual(eventDate.toLocalDate());
                    })
                    .map(event -> {
                        JSONObject newEvent = new JSONObject();
                        String createdAt = event.getString("created_at");
                        ZonedDateTime eventDate = ZonedDateTime.parse(createdAt).withZoneSameInstant(ZoneId.of("Asia/Shanghai"));
                        String commitTitle = event.getJSONObject("push_data").getString("commit_title");
                        String projectId = event.getString("project_id");

                        if (commitTitle.endsWith("...")) {
                            String commitId = event.getJSONObject("push_data").getString("commit_to");
                            String commitUrl = gitLabUrl + "/api/v4/projects/" + projectId + "/repository/commits/" + commitId;
                            Request commitRequest = new Request.Builder()
                                    .url(commitUrl)
                                    .addHeader("PRIVATE-TOKEN", accessToken)
                                    .build();
                            try (Response commitResponse = client.newCall(commitRequest).execute()) {
                                String commitResult = commitResponse.body().string();
                                JSONObject commitData = JSON.parseObject(commitResult);
                                commitTitle = commitData.getString("message");
                            } catch (IOException e) {
                                e.printStackTrace();
                            }
                        }
                        newEvent.put("created_at", formatter.format(eventDate));
                        String projectName = getGitLabProjectName(accessToken, client, gitLabUrl, projectId);
                        if (isNotBlank(projectName)) {
                            commitTitle = projectName + ": " + commitTitle;
                        }
                        newEvent.put("commit_title", commitTitle);
                        return newEvent;
                    })
                    .collect(Collectors.toList());
            PrintColor.zi("todayEvents: " + todayEvents);
            return todayEvents;
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private String getGitLabProjectName(String accessToken, OkHttpClient client, String gitLabUrl, String projectId) {
        String projectUrl = gitLabUrl + "/api/v4/projects/" + projectId;
        Request request = new Request.Builder()
                .url(projectUrl)
                .addHeader("PRIVATE-TOKEN", accessToken)
                .build();
        try (Response response = client.newCall(request).execute()) {
            String result = response.body().string();
            JSONObject projectData = JSON.parseObject(result);
            return projectData.getString("name");
        } catch (IOException e) {
            e.printStackTrace();
        }
        return null;
    }

    private boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }
}