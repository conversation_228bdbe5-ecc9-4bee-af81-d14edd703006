package inks.service.sa.pms.controller;

import inks.service.sa.pms.service.SaMqttlogService;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * MqUtils - MQTT工具类
 * 
 * 这是一个单例工具类，用于在全局范围内提供对SaMqttlogService的访问。
 * 采用Spring管理的单例模式实现，确保线程安全和资源的有效管理。
 * 
 * 设计说明：
 * 1. 单例模式：通过静态变量和私有构造函数确保全局唯一实例
 * 2. Spring集成：使用@Component注解将类交给Spring容器管理
 * 3. 延迟初始化：实例在Spring容器启动时才完成初始化
 * 4. 线程安全：由Spring容器保证实例的线程安全性
 * 
 * 使用示例：
 * SaMqttlogService mqttLogService = MqUtils.getInstance().getMqttLogService();
 * 
 * <AUTHOR>
 * @version 1.0
 * @since [起始版本]
 */
@Component
public class MqUtils {

    /**
     * 静态实例，用于存储当前类的唯一实例
     * volatile关键字确保多线程环境下的可见性
     */
    private static volatile MqUtils instance;

    /**
     * MQTT日志服务实例
     * 使用@Resource注解进行依赖注入
     */
    @Resource
    private SaMqttlogService saMqttlogService;

    /**
     * 私有构造函数
     * 防止外部直接创建实例，确保单例模式
     */
    private MqUtils() {
    }

    /**
     * 获取MqUtils的单例实例
     * 
     * @return MqUtils实例
     * @throws IllegalStateException 如果实例未初始化
     */
    public static MqUtils getInstance() {
        if (instance == null) {
            throw new IllegalStateException("MqUtils instance not initialized yet");
        }
        return instance;
    }

    /**
     * 初始化方法
     * 在Spring容器启动时自动执行
     * 使用@PostConstruct注解确保在依赖注入完成后执行
     */
    @PostConstruct
    public void init() {
        MqUtils.instance = this;
    }

    /**
     * 获取MQTT日志服务实例
     * 
     * @return SaMqttlogService实例
     * @throws IllegalStateException 如果实例未初始化
     */
    public SaMqttlogService getMqttLogService() {
        return MqUtils.getInstance().saMqttlogService;
    }
}
