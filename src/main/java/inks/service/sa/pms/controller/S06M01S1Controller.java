package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.domain.vo.SaEngineerPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaProjectPojo;
import inks.service.sa.pms.service.SaProjectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 工程项目(Sa_Project)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-22 13:57:54
 */
@RestController
@RequestMapping("S06M01S1")
@Api(tags = "S06M01S1:工程项目")
public class S06M01S1Controller extends SaProjectController {
    @Resource
    private SaProjectService saProjectService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<PageInfo<SaProjectPojo>> getBillList(@RequestBody String json, boolean own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            String qpfilter = "";
            // 获得用户数据
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
//             将 loginUser 强制转换为 MyLoginUser 类型
//            MyLoginUser myLoginUser = (MyLoginUser) loginUser;
            // 现在，您可以访问工程师信息
            SaEngineerPojo engineer = myLoginUser.getEngineer();
            boolean isadmin = true;//是adminmark=2或工程师管理者
            // own=true时,查询工程项目，需要根据当前登录的用户判断是否显示 (admin和工程师管理者全显示，其他工程师只显示他参与的项目)
            if (own && myLoginUser.getIsadmin() != 2 && (engineer == null || !"管理者".equals(engineer.getEngineertype()))) {
                qpfilter += " and Sa_Project.id in(select distinct Pid from Sa_ProjectItem where Engineerid = '" + engineer.getId() + "')";
                isadmin = false;
            }
            // projectFilter是用来查询需求任务的(参考D06M02B1/getPageList接口)，根据当前登录用户判断是否返回 (admin+创建者+管理者+执行者，判断当前用户是否为其中一个)
            // 目的是查询项目下自己待处理任务数-自己已逾期任务数(见业务层)
            String projectFilter = null;
            if (own && myLoginUser.getIsadmin() != 2 && (engineer == null || !"管理者".equals(engineer.getEngineertype()))) {
                projectFilter = " and (Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "' or Sa_Demand.Appointeeid = '" + engineer.getId() + "')";
            }

            // 根据管理的项目排序表排序:先按星标(StarMark)排序，再按最后查询时间(LastAccessTime)排序
            // 加在xml层了:"Sa_ProjectSort.StarMark desc,Sa_ProjectSort.LastAccessTime desc"
            // 并且Sa_ProjectSort的工程师id是当前登录人工程师id
            qpfilter += " and Sa_ProjectSort.Engineerid = '" + engineer.getId() + "'";

            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saProjectService.getBillList(queryParam, projectFilter, isadmin, engineer.getId()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
