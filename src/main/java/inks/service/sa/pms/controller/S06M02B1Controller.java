package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.DateRange;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.domain.vo.SaEngineerPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDemandPojo;
import inks.service.sa.pms.domain.pojo.SaUserPojo;
import inks.service.sa.pms.mapper.SaDemandMapper;
import inks.service.sa.pms.mapper.SaEngineerMapper;
import inks.service.sa.pms.mapper.SaProjectsortMapper;
import inks.service.sa.pms.service.PmsSaUserService;
import inks.service.sa.pms.service.SaDemandService;
import inks.service.sa.pms.utils.email.EmailSendUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.time.*;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 产品需求(Sa_Demand)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-24 16:43:02
 */
@RestController
@RequestMapping("S06M02B1")
@Api(tags = "S06M02B1:产品需求")
@Validated
public class S06M02B1Controller extends SaDemandController {
    @Resource
    private SaDemandService saDemandService;
    @Resource
    private SaDemandMapper saDemandMapper;
    @Resource
    private SaEngineerMapper saEngineerMapper;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private PmsSaUserService pmsSaUserService;
    @Resource
    private SaProjectsortMapper saProjectsortMapper;
    @Resource
    private EmailSendUtils emailSendUtils;

    //获取传入json的场景中的某个目标字段(targetField)的值
    public static String getSceneDataField(String jsonScene, String targetField) {
        JSONArray jsonArray = JSON.parseArray(jsonScene);
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject jsonObject = jsonArray.getJSONObject(i);
            if (targetField.equals(jsonObject.getString("field"))) {
                return jsonObject.getString("value");
            }
        }
        return null;  // 如果未找到匹配的字段，则返回 null 或其他适当的默认值
    }

    public static Object getFieldValue(Object object, String fieldName) {
        try {
            Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            return field.get(object);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            e.printStackTrace();
            return null;
        }
    }

    //    @ApiOperation(value = "获取需求任务日历(不传时间范围默认本周一到本周日)", notes = "", produces = "application/json")
//    @RequestMapping(value = "/getDemandCalendar", method = RequestMethod.POST)
//    public R<List<Map<String, Object>>> getDemandCalendar(@RequestBody String json,String statustype,String appointeengineerid,String createengineerid) {
//        try {
//            // 获得用户数据
//            MyLoginUser myLoginUser = (MyLoginUser)saRedisService.getLoginUser(ServletUtils.getRequest());
//            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            Date startDate = null; // 时间范围
//            Date endDate = null;
//            // 未传时间范围,则默认构建[本周一到本周日]的时间范围
//            if (queryParam.getDateRange() == null) {
//                // 获取当前日期
//                Calendar calendar = Calendar.getInstance();
//                // 设置日期为本周的第一天（星期一）
//                calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
//                startDate = calendar.getTime();
//                // 增加6天，以获取本周的最后一天（星期日）
//                calendar.add(Calendar.DATE, 6);
//                endDate = calendar.getTime();
//                // 设置SQL的时间过滤信息
//                queryParam.setDateRange(new DateRange("Sa_Demand.StartDate", startDate, endDate));
//            } else {
//                startDate = queryParam.getDateRange().getStartDate();
//                endDate = queryParam.getDateRange().getEndDate();
//            }
//
//            //构建传入的日期范围内的数据，如传入 "StartDate": "2023-03-01 00:00:00","EndDate": "2023-03-31 23:59:59"
//            //则构建一个从2023-03-01到2023-03-31的数据，31行HrPwrecgroupPojo空对象，Billdate从3.1到3.31
//            Calendar calendar = Calendar.getInstance();
//            calendar.setTime(startDate);
//            // 使用 SimpleDateFormat 格式化日期，以便进行日期比较
//            SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd");
//            // 构建传入的日期范围内的数据list7Day 存储日期和需求任务对象的List
//            List<Map<String, Object>> list7Day = new ArrayList<>();
//            while (!calendar.getTime().after(endDate)) {
//                HashMap<String, Object> map = new HashMap<>();
//                map.put("date", dateFormat.format(calendar.getTime()));//每天日期
//                map.put("demands", null);// 需求任务对象的List
//                list7Day.add(map);
//                calendar.add(Calendar.DATE, 1);
//            }
//
//            //查询DB中的需求任务数据(有了时间范围)
//            if (StringUtils.isBlank(queryParam.getOrderBy())) {
//                queryParam.setOrderBy("Sa_Demand.StartDate");//默认按需求的开始时间排序
//            }
//            String qpfilter = "";
//            qpfilter = getDemandFilter(statustype, appointeengineerid, createengineerid, qpfilter);
//            qpfilter += queryParam.getAllFilter();
//            queryParam.setFilterstr(qpfilter);
//            List<SaDemandPojo> todoPageList = this.saDemandMapper.getTodoPageList(queryParam);
//
//            // 遍历list7Day,将DB中每天的需求任务赋值给list7Day的demands
//            for (SaDemandPojo saDemandPojo : todoPageList) {
//                for (Map<String, Object> map : list7Day) {
//                    String list7DayDate = (String) map.get("date");
//                    String startDateDB = dateFormat.format(saDemandPojo.getStartdate());
//                    // 如果DB日期和空Map的日期匹配，则将 SaDemandPojo 添加到 空Map的"demands" 列表
//                    if (list7DayDate.equals(startDateDB)) {
//                        List<SaDemandPojo> demandsList = (List<SaDemandPojo>) map.get("demands");
//                        if (demandsList == null) {
//                            demandsList = new ArrayList<>();
//                            map.put("demands", demandsList);
//                        }
//                        demandsList.add(saDemandPojo);
//                    }
//                }
//            }
//            return R.ok(list7Day);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
    private static void MonToSunDay(QueryParam queryParam) {
        Date endDate;
        Date startDate;
        if (queryParam.getDateRange() == null) {
            // 获取当前日期
            Calendar calendar = Calendar.getInstance();
            // 设置日期为本周的第一天（星期一 00:00:00）
            calendar.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY);
            calendar.set(Calendar.HOUR_OF_DAY, 0);
            calendar.set(Calendar.MINUTE, 0);
            calendar.set(Calendar.SECOND, 0);
            startDate = calendar.getTime();
            // 增加6天，以获取本周的最后一天（星期日 23:59:59）
            calendar.add(Calendar.DATE, 6);
            calendar.set(Calendar.HOUR_OF_DAY, 23);
            calendar.set(Calendar.MINUTE, 59);
            calendar.set(Calendar.SECOND, 59);
            endDate = calendar.getTime();
            // 设置SQL的时间过滤信息
            queryParam.setDateRange(new DateRange("Sa_Demand.StartDate", startDate, endDate));
        } else {
            startDate = queryParam.getDateRange().getStartDate();
            endDate = queryParam.getDateRange().getEndDate();
        }
    }

    /**
     * @return R<PageInfo < SaDemandPojo>>
     * @Description
     * <AUTHOR>
     * @param[1] json
     * @param[2] own
     * @param[3] todo
     * @param[4] finishlimit=20表示取完结状态的需求前20条(不传默认20条)  UNION ALL: WHERE Sa_Demand.Status IN (SELECT id FROM Sa_ProjectStatus WHERE StatusType = '已完成' AND FinishMark = 1) limit 20
     * @time 2023/10/26 14:16
     */

    @ApiOperation(value = "需求任务:按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<PageInfo<SaDemandPojo>> getPageList(@RequestBody String json, boolean own, Integer finishlimit) {
        try {
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 将 loginUser 强制转换为 MyLoginUser 类型
//            MyLoginUser myLoginUser = (MyLoginUser) loginUser;
            // 现在，您可以访问工程师信息
            inks.sa.common.core.domain.vo.SaEngineerPojo engineer = myLoginUser.getEngineer();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            }
            // 拿到前端传入的json中的场景数据里的projectid
            String projectid = getSceneDataField(queryParam.getScenedata(), "projectid");
            // 如果有projectid，就根据projectid和工程师id更新项目排序表的最后点击时间
            if (isNotBlank(projectid)) {
                saProjectsortMapper.updateLastAccessTime(new Date(), projectid, engineer.getId());
            }
            String qpfilter = "";
            // own=true时,查询需求任务，根据当前登录用户判断是否返回 (admin+创建者+管理者+执行者，判断当前用户是否为其中一个)
//            and ((Sa_Demand.CreateByid = '12138' or Sa_Demand.Appointeeid = '12138') and Sa_Demand.Parentid is null or Sa_Demand.Parentid = '')
            if (own && myLoginUser.getIsadmin() != 2 && (engineer == null || !"管理者".equals(engineer.getEngineertype()))) { //执行者条件不查询子任务
//                qpfilter += " and ((Sa_Demand.CreateByid = '" + loginUser.getUserid() + "' or Sa_Demand.Appointeeid = '" + engineer.getId() + "') and Sa_Demand.Parentid = '')";
                qpfilter += " and (Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "' or Sa_Demand.Appointeeid = '" + engineer.getId() + "')";
            }
//            // 4、查询任务列表时，执行者条件不查询子任务
//            qpfilter += " and Sa_Demand.Parentid is null or Sa_Demand.Parentid = ''";

            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageListUNIONALL(queryParam, finishlimit == null ? 20 : finishlimit));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<PageInfo < SaDemandPojo>>
     * @Description
     * <AUTHOR>
     * @param[1] json
     * @param[2] statustype  状态过滤: 即Sa_ProjectStatus表中的StatusType字段(开始、进行中、已完成),FinishMark字段(0未结转,1已结转)
     * @param[3] appointeengineerid  执行者工程师id过滤 格式是'aa','bb'
     * @param[4] createengineerid  创建者userid过滤 格式是'aa','bb'
     * @param[5] engineerids  engineerids是多个工程师id,格式是'aa','bb'(需要转换为userids,去查询创建人是userids或执行人是engineerids的任务)
     * @time 2023/10/26 15:07
     */
    @ApiOperation(value = "代办任务/getTodoPageList", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getTodoPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<PageInfo<SaDemandPojo>> getTodoPageList(@RequestBody String json, String statustype, String appointeengineerid, String createengineerid, String engineerids) {
        try {
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 将 loginUser 强制转换为 MyLoginUser 类型
//            MyLoginUser myLoginUser = (MyLoginUser) loginUser;
            // 现在，您可以访问工程师信息
            SaEngineerPojo engineer = myLoginUser.getEngineer();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Demand.StatusModifyDate");
            }
            String qpfilter = "";
            qpfilter = getDemandFilter(statustype, appointeengineerid, createengineerid, engineerids, qpfilter);

            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getTodoPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return String
     * @Description
     * <AUTHOR>
     * @param[1] statustype
     * @param[2] appointeengineerid Sa_Demand.Appointeeid 是执行者工程师id;格式是'aa','bb'
     * @param[3] createuserid       Sa_Demand.CreateByid 是创建者userid;格式是'aa','bb'
     * @param[4] engineerids        engineerids是多个工程师id,格式是'aa','bb'(需要转换为userids,去查询创建人是userids或者执行人是engineerids的任务)
     * @param[5] qpfilter
     * @time 2023/10/27 8:48
     */
    private String getDemandFilter(String statustype, String appointeengineerid, String createengineerid, String engineerids, String qpfilter) {
        // 1.statustype状态过滤: 即Sa_ProjectStatus表中的StatusType字段(开始、进行中、已完成),FinishMark字段(0未结转,1已结转)
        // 若未传入statustype,则默认查询所有状态的任务
        if ("开始".equals(statustype)) {
            qpfilter += " and Sa_Demand.Status in (select id from Sa_ProjectStatus where StatusType = '开始')";
        } else if ("进行中".equals(statustype)) {
            qpfilter += " and Sa_Demand.Status in (select id from Sa_ProjectStatus where StatusType = '进行中')";
        } else if ("已完成".equals(statustype)) {
            qpfilter += " and Sa_Demand.Status in (select id from Sa_ProjectStatus where StatusType = '已完成')";
        } else if ("已完成未结转".equals(statustype)) {
            qpfilter += " and Sa_Demand.Status in (select id from Sa_ProjectStatus where StatusType = '已完成' and FinishMark = 0)";
        } else if ("已完成已结转".equals(statustype)) {
            qpfilter += " and Sa_Demand.Status in (select id from Sa_ProjectStatus where StatusType = '已完成' and FinishMark = 1)";
        }
        //2.执行者工程师id过滤
        if (isNotBlank(appointeengineerid)) {
            qpfilter += " and Sa_Demand.Appointeeid in (" + appointeengineerid + ")";
        }
        //3.创建者工程师id过滤
        if (isNotBlank(createengineerid)) {
            List<String> useridList = saEngineerMapper.getUseridList(engineerids);
            // 使用 Java 8 Stream 将列表元素拼接成逗号分隔的字符串，并加上单引号
            String userIds = useridList.stream()
                    .map(s -> "'" + s + "'")
                    .collect(Collectors.joining(","));
            qpfilter += " and Sa_Demand.CreateByid in (" + userIds + ")";
        }
        //4执行者工程师ids或创建者工程师ids过滤
        if (isNotBlank(engineerids)) {
            List<String> useridList = saEngineerMapper.getUseridList(engineerids);
            // 使用 Java 8 Stream 将列表元素拼接成逗号分隔的字符串，并加上单引号
            String userIds = useridList.stream()
                    .map(s -> "'" + s + "'")
                    .collect(Collectors.joining(","));
            qpfilter += " and (Sa_Demand.CreateByid in (" + userIds + ") or Sa_Demand.Appointeeid in (" + engineerids + "))";
        }
        return qpfilter;
    }

    @ApiOperation(value = "获取需求任务日历(不传时间范围默认本周一到本周日),engineerids:查询多个工程师的任务,格式: 'aa','bb'", notes = "", produces = "application/json")
    @RequestMapping(value = "/getDemandCalendar", method = RequestMethod.POST)
    public R<List<Map<String, Object>>> getDemandCalendar(@RequestBody String json, String statustype, String appointeengineerid, String createengineerid, String engineerids) {
        try {
            // 获得用户数据
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            Date startDate = null; // 时间范围
            Date endDate = null;
            // 未传时间范围,则默认构建[本周一到本周日]的时间范围
            MonToSunDay(queryParam);
            //查询DB中的需求任务数据(有了时间范围)
            if (StringUtils.isBlank(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Demand.StartDate");//默认按需求的开始时间排序
            }
            String qpfilter = "";
            qpfilter = getDemandFilter(statustype, appointeengineerid, createengineerid, engineerids, qpfilter);
            // StartDate和DeadDate，只查这俩同时有值的记录 且 StartDate DeadDate必须在同一天
            qpfilter += " and Sa_Demand.StartDate is not null and Sa_Demand.DeadDate is not null and Date(Sa_Demand.StartDate) = Date(Sa_Demand.DeadDate)";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            List<Map<String, Object>> calendarPageList = this.saDemandMapper.getCalendarPageList(queryParam);
            return R.ok(calendarPageList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getGroupPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<PageInfo<SaDemandPojo>> getGroupPageList(@RequestBody String json) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            String qpfilter = "";
//            SaUserPojo saUserPojo = saUserService.getEntity(loginUser.getUserid());
            // Adminmark或Roletype只要有一个为1，就能看所有需求单
            // 都为0，就只能看自己(或自己绑定的客户ids的)需求单
//            if (saUserPojo.getAdminmark() == 0 && saUserPojo.getRoletype() == 0) {
            qpfilter += " and (Sa_Demand.CreateByid = '" + loginUser.getUserid() + "'";
            if (isNotBlank(loginUser.getGroupids())) {
                qpfilter += " or Sa_Demand.Groupid in (" + loginUser.getGroupids() + "))";
            } else {
                qpfilter += ")";
            }
//            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageListUNIONALL(queryParam, null));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<PageInfo<SaDemandPojo>> getOnlinePageList(@RequestBody String json) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            String qpfilter = " and Sa_Demand.DisannulMark = 0 and Sa_Demand.FinishMark = 0";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageListUNIONALL(queryParam, null));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineGroupPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<PageInfo<SaDemandPojo>> getOnlineGroupPageList(@RequestBody String json) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            String qpfilter = " and Sa_Demand.DisannulMark = 0 and Sa_Demand.FinishMark = 0";
//            SaUserPojo saUserPojo = saUserService.getEntity(loginUser.getUserid());
            // Adminmark或Roletype只要有一个为1，就能看所有需求单
            // 都为0，就只能看自己(或自己绑定的客户ids的)需求单
//            if (saUserPojo.getAdminmark() == 0 && saUserPojo.getRoletype() == 0) {
            qpfilter += " and (Sa_Demand.CreateByid = '" + loginUser.getUserid() + "'";
            if (isNotBlank(loginUser.getGroupids())) {
                qpfilter += " or Sa_Demand.Groupid in (" + loginUser.getGroupids() + "))";
            } else {
                qpfilter += ")";
            }
//            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageListUNIONALL(queryParam, null));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //    -----------------------发邮件--------------------------
//    minio私有图片地址(调用本服务api):https://dev.inksyun.com:30656/S06M95S1/getImage/picture/20230809/1689189082975961088.jpg
    @ApiOperation(value = "发送需求单邮件", notes = "发送需求单邮件", produces = "application/json")
    @RequestMapping(value = "/sendDemandEmail", method = RequestMethod.POST)
    public R<String> sendDemandEmail(@RequestBody String json) {
        try {
            JSONObject jsonObject = JSONArray.parseObject(json);
            //需求单id
            String key = jsonObject.getString("key");
            //收件人邮箱
            String toemail = jsonObject.getString("toemail");
            //抄送人邮箱List
            String otheremails = jsonObject.getString("otheremails");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaUserPojo saUserPojo = pmsSaUserService.getEntity(loginUser.getUserid());
            //发件人邮箱
            String fromEmail = saUserPojo.getEmail();
            //发件人邮箱授权码
            String authCode = saUserPojo.getEmailauthcode();
            //发件人姓名
            String fromUserName = loginUser.getRealname();
            // 获得需求单数据
            SaDemandPojo saDemandPojo = saDemandService.getEntity(key);
            //生成邮件标题
            String subject = saDemandPojo.getBilltitle();
            //生成邮件内容
            StringBuilder contentBuilder = new StringBuilder("<html><head><style>")
                    .append("h3 { margin-top: 20px; font-size: 18px; }")
                    .append("img { max-width: 100%; height: auto; margin-top: 10px; }")
                    .append("</style></head><body>")
                    .append("<h3>客户名称</h3>").append(saDemandPojo.getGroupname())
                    .append("<h3>项目名称</h3>").append(saDemandPojo.getItemname())
                    .append("<h3>需求描述</h3>").append(saDemandPojo.getDescription())
                    .append("<h3>完成描述</h3>").append(saDemandPojo.getFinishdes());
            // 反射拼接需求图片 pictureurl1-5
            for (int i = 1; i <= 5; i++) {
                String pictureUrl = (String) getFieldValue(saDemandPojo, "pictureurl" + i);
                if (isNotBlank(pictureUrl)) {
                    contentBuilder.append("<h3>现场照片").append(i).append("</h3><img src='https://dev.inksyun.com:30656/S06M95S1/getImage/")
                            .append(pictureUrl).append("' alt='现场照片' />");
                }
            }

            // 反射拼接完成图片 finpictureurl1-5
            for (int i = 1; i <= 5; i++) {
                String finPictureUrl = (String) getFieldValue(saDemandPojo, "finpictureurl" + i);
                if (isNotBlank(finPictureUrl)) {
                    contentBuilder.append("<h3>完成照片").append(i).append("</h3><img src='https://dev.inksyun.com:30656/S06M95S1/getImage/")
                            .append(finPictureUrl).append("' alt='完成照片' />");
                }
            }
            // 最后关闭邮件内容标签
            contentBuilder.append("</body></html>");
            String content = contentBuilder.toString();
            //发送邮件
            /**
             * fromEmail:    发件人邮箱   "<EMAIL>"
             * authCode:     发件人邮箱授权码 "pcwwnfnesudmbbfd"
             * formUserName: 发件人姓名   "nanno"
             * toEmail:      收件人邮箱   "<EMAIL>"
             * otherEmails : 抄送人邮箱List   以分号分隔
             * subject:      邮件标题     "工作日志-nanno-20230306"
             * content:      邮件内容     "邮件内容..."
             */
            emailSendUtils.sendEmail(fromEmail, authCode, fromUserName, toemail, otheremails, subject, content);
            return R.ok("发送成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "今日新增需求单列表,finish=true为今日完成需求单", notes = "今日新增需求单", produces = "application/json")
    @RequestMapping(value = "/getDemandsToday", method = RequestMethod.POST)
    public R<List<SaDemandPojo>> getDemandsToday(boolean finish) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = new QueryParam();
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            LocalDate today = LocalDate.now();
            // 获取今天的起始时间（0点）
            LocalDateTime startDateTime = today.atTime(LocalTime.MIN);
            LocalDateTime endDateTime = today.atTime(23, 59, 59);
            // 转换为 java.util.Date（如果需要）
            Date startDate = java.sql.Timestamp.valueOf(startDateTime);
            Date endDate = java.sql.Timestamp.valueOf(endDateTime);
            queryParam.setDateRange(new DateRange(null, startDate, endDate));
            if (finish) {
                queryParam.setFilterstr(" and Sa_Demand.FinishMark = 1");
            }
            return R.ok(this.saDemandMapper.getPageListUNIONALL(queryParam, null));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "今日新增需求单数量,finish=true为今日完成需求单", notes = "今日新增需求单", produces = "application/json")
    @RequestMapping(value = "/newCountToday", method = RequestMethod.GET)
    public R<Integer> newCountToday() {
        try {
            return R.ok(this.saDemandMapper.newCountToday());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "紧急需求", notes = "紧急需求", produces = "application/json")
    @RequestMapping(value = "/getDemandsUrgent", method = RequestMethod.POST)
    public R<List<SaDemandPojo>> getDemandsUrgent() {
        try {
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = new QueryParam();
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            String qpfilter = " and Sa_Demand.FinishMark = 1 AND Sa_Demand.Level = 2";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandMapper.getPageListUNIONALL(queryParam, null));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "今日需求单完成数and本周完成数", notes = "今日需求单完成数and本周完成数", produces = "application/json")
    @RequestMapping(value = "/finishCountTodayAndWeek", method = RequestMethod.POST)
    public R<HashMap<String, Object>> finishCountTodayAndWeek() {
        try {
            // 获取今天的时间范围
            ZonedDateTime startDateTime = LocalDate.now().atStartOfDay(ZoneId.systemDefault());
            ZonedDateTime endDateTime = LocalDate.now().atTime(23, 59, 59).atZone(ZoneId.systemDefault());
            // 转换为 java.util.Date（如果需要）
            Date startDate = Date.from(startDateTime.toInstant());
            Date endDate = Date.from(endDateTime.toInstant());
            int todayFinishCount = saDemandMapper.finishCountInTime(startDate, endDate);
            // 获取本周的时间范围
            ZonedDateTime startWeekDateTime = LocalDate.now().with(DayOfWeek.MONDAY).atStartOfDay(ZoneId.systemDefault());
            ZonedDateTime endWeekDateTime = LocalDate.now().with(DayOfWeek.SUNDAY).atTime(23, 59, 59).atZone(ZoneId.systemDefault());
            // 转换为 java.util.Date（如果需要）
            Date startWeekDate = Date.from(startWeekDateTime.toInstant());
            Date endWeekDate = Date.from(endWeekDateTime.toInstant());
            int weekFinishCount = saDemandMapper.finishCountInTime(startWeekDate, endWeekDate);
            HashMap<String, Object> result = new HashMap<>();
            result.put("today", todayFinishCount);
            result.put("week", weekFinishCount);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "今日、本周、本月的需求单总数、完成数、未完成数、耗费工时 (SQL时间过滤:StartDate 是否完成:FinishMark)", notes = "今日、本周、本月的需求单总数、完成数", produces = "application/json")
    @RequestMapping(value = "/finishCountTodayAndWeekAndMonth", method = RequestMethod.POST)
    public R<HashMap<String, Object>> finishCountTodayAndWeekAndMonth() {
        try {
            // 获取今天的时间范围
            ZonedDateTime startDateTimeToday = LocalDate.now().atStartOfDay(ZoneId.systemDefault());
            ZonedDateTime endDateTimeToday = LocalDate.now().atTime(23, 59, 59).atZone(ZoneId.systemDefault());
            // 转换为 java.util.Date（如果需要）
            Date startDateToday = Date.from(startDateTimeToday.toInstant());
            Date endDateToday = Date.from(endDateTimeToday.toInstant());
            int todayAllCount = saDemandMapper.allCountInTime(startDateToday, endDateToday);
            int todayFinishCount = saDemandMapper.finishCountInTime(startDateToday, endDateToday);
            int todayUnFinishCount = saDemandMapper.unFinishCountInTime(startDateToday, endDateToday);
            double todayWorkedTime = saDemandMapper.workTimeInTime(startDateToday, endDateToday);

            // 获取本周的时间范围
            ZonedDateTime startWeekDateTime = LocalDate.now().with(DayOfWeek.MONDAY).atStartOfDay(ZoneId.systemDefault());
            ZonedDateTime endWeekDateTime = LocalDate.now().with(DayOfWeek.SUNDAY).atTime(23, 59, 59).atZone(ZoneId.systemDefault());
            // 转换为 java.util.Date（如果需要）
            Date startWeekDate = Date.from(startWeekDateTime.toInstant());
            Date endWeekDate = Date.from(endWeekDateTime.toInstant());
            int weekAllCount = saDemandMapper.allCountInTime(startWeekDate, endWeekDate);
            int weekFinishCount = saDemandMapper.finishCountInTime(startWeekDate, endWeekDate);
            int weekUnFinishCount = saDemandMapper.unFinishCountInTime(startWeekDate, endWeekDate);
            double weekWorkedTime = saDemandMapper.workTimeInTime(startWeekDate, endWeekDate);

            // 获取本月的时间范围
            ZonedDateTime startMonthDateTime = LocalDate.now().withDayOfMonth(1).atStartOfDay(ZoneId.systemDefault());
            ZonedDateTime endMonthDateTime = LocalDate.now().withDayOfMonth(LocalDate.now().lengthOfMonth()).atTime(23, 59, 59).atZone(ZoneId.systemDefault());
            // 转换为 java.util.Date（如果需要）
            Date startMonthDate = Date.from(startMonthDateTime.toInstant());
            Date endMonthDate = Date.from(endMonthDateTime.toInstant());
            int monthAllCount = saDemandMapper.allCountInTime(startMonthDate, endMonthDate);
            int monthFinishCount = saDemandMapper.finishCountInTime(startMonthDate, endMonthDate);
            int monthUnFinishCount = saDemandMapper.unFinishCountInTime(startMonthDate, endMonthDate);
            double monthWorkedTime = saDemandMapper.workTimeInTime(startMonthDate, endMonthDate);

            HashMap<String, Object> result = new LinkedHashMap<>();
            // 今日
            result.put("AllCountToday", todayAllCount);
            result.put("FinishCountToday", todayFinishCount);
            result.put("UnFinishCountToday", todayUnFinishCount);
            result.put("WorkedTimeToday", todayWorkedTime);
            // 本周
            result.put("AllCountWeek", weekAllCount);
            result.put("FinishCountWeek", weekFinishCount);
            result.put("UnFinishCountWeek", weekUnFinishCount);
            result.put("WorkedTimeWeek", weekWorkedTime);
            // 本月
            result.put("AllCountMonth", monthAllCount);
            result.put("FinishCountMonth", monthFinishCount);
            result.put("UnFinishCountMonth", monthUnFinishCount);
            result.put("WorkedTimeMonth", monthWorkedTime);
            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "在线的总需求数 (Sa_ProjectStatus.StatusType有3种状态:开始/进行中/已完成; 此处统计!='已完成' 不用FinishMark统计有脏数据)", notes = "", produces = "application/json")
    @RequestMapping(value = "/onlineCount", method = RequestMethod.POST)
    public R<Integer> onlineCount() {
        try {

            return R.ok(saDemandMapper.onlineCount());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "本周每日需求单总完成数 name日期 value完成数", notes = "本周每日需求单完成数", produces = "application/json")
    @RequestMapping(value = "/finishCountPerDayThisWeek", method = RequestMethod.POST)
    public R<List<Map<String, Object>>> finishCountPerDayThisWeek() {
        try {
            List<Map<String, Object>> result = new ArrayList<>();

            LocalDate today = LocalDate.now();
            LocalDate startOfWeek = today.with(DayOfWeek.MONDAY);
            LocalDate endOfWeek = today.with(DayOfWeek.SUNDAY);

            for (LocalDate date = startOfWeek; date.isBefore(endOfWeek.plusDays(1)); date = date.plusDays(1)) {
                ZonedDateTime startDateTime = date.atStartOfDay(ZoneId.systemDefault());
                ZonedDateTime endDateTime = date.atTime(23, 59, 59).atZone(ZoneId.systemDefault());
                Date startDate = Date.from(startDateTime.toInstant());
                Date endDate = Date.from(endDateTime.toInstant());
                int finishCount = saDemandMapper.finishCountInTime(startDate, endDate);

                Map<String, Object> item = new HashMap<>();
                item.put("name", date.toString());
                item.put("value", finishCount);
                result.add(item);
            }

            return R.ok(result);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "需求单:各个人员完工数量汇总(传入时间段) select count(*) as value(完成数),Sa_Engineer.EngineerName as name,Sa_Engineer.id as engineerid", notes = "", produces = "application/json")
    @RequestMapping(value = "/getFinishDemandQtyGroupByUser", method = RequestMethod.POST)
    public R<List<HashMap<String, Object>>> getFinishDemandQtyGroupByUser(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
//            LocalDate today = LocalDate.now();
//            LocalDate startOfMonth = today.withDayOfMonth(1);
//            LocalDate endOfMonth = today.withDayOfMonth(today.lengthOfMonth());
//            Date startDate = Date.from(startOfMonth.atStartOfDay(ZoneId.systemDefault()).toInstant());
//            Date endDate = Date.from(endOfMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault()).toInstant());

            List<HashMap<String, Object>> resultMaps = saDemandMapper.getFinishDemandQtyGroupByUser(queryParam);

            return R.ok(resultMaps);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "滞留排行榜", notes = "滞留排行榜", produces = "application/json")
//    @RequestMapping(value = "/strandedRankingList", method = RequestMethod.POST)
//    public R<List<SaDemandPojo>> theBudgetOfDays() {
//        try {
//            // 上个月的开始,结束时间:
//            LocalDate today = LocalDate.now();
//            LocalDate startOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
//            LocalDate endOfLastMonth = today.minusMonths(1).withDayOfMonth(today.minusMonths(1).lengthOfMonth());
//            ZonedDateTime startLastMonthDateTime = startOfLastMonth.atStartOfDay(ZoneId.systemDefault());
//            ZonedDateTime endLastMonthDateTime = endOfLastMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault());
//            Date startLastMonthDate = Date.from(startLastMonthDateTime.toInstant());
//            Date endLastMonthDate = Date.from(endLastMonthDateTime.toInstant());
//            // 上个月的完成总数:
//            int lastMonthFinishCount = saDemandMapper.finishCountInTime(startLastMonthDate, endLastMonthDate);
//            // 总结余数:
//            int onlineCount = saDemandMapper.getOnlineCount();
//
//            HashMap<String, Object> resultMap = new HashMap<>();
//            resultMap.put("lastMonthFinishCount", lastMonthFinishCount);
//            resultMap.put("onlineCount", onlineCount);
//            resultMap.put("theBudgetOfDays", onlineCount / (lastMonthFinishCount / 22.0));
//            return R.ok(resultMap);
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

    @ApiOperation(value = "结余数/(上月总数/22)=预算天数", notes = "", produces = "application/json")
    @RequestMapping(value = "/theBudgetOfDays", method = RequestMethod.POST)
    public R<HashMap<String, Object>> theBudgetOfDays() {
        try {
            // 上个月的开始,结束时间:
            LocalDate today = LocalDate.now();
            LocalDate startOfLastMonth = today.minusMonths(1).withDayOfMonth(1);
            LocalDate endOfLastMonth = today.minusMonths(1).withDayOfMonth(today.minusMonths(1).lengthOfMonth());
            ZonedDateTime startLastMonthDateTime = startOfLastMonth.atStartOfDay(ZoneId.systemDefault());
            ZonedDateTime endLastMonthDateTime = endOfLastMonth.atTime(23, 59, 59).atZone(ZoneId.systemDefault());
            Date startLastMonthDate = Date.from(startLastMonthDateTime.toInstant());
            Date endLastMonthDate = Date.from(endLastMonthDateTime.toInstant());
            // 上个月的完成总数:
            int lastMonthFinishCount = saDemandMapper.finishCountInTime(startLastMonthDate, endLastMonthDate);
            // 总结余数:
            int onlineCount = saDemandMapper.getOnlineCount();

            HashMap<String, Object> resultMap = new HashMap<>();
            resultMap.put("lastMonthFinishCount", lastMonthFinishCount);
            resultMap.put("onlineCount", onlineCount);
            resultMap.put("theBudgetOfDays", onlineCount / (lastMonthFinishCount / 22.0));
            return R.ok(resultMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
