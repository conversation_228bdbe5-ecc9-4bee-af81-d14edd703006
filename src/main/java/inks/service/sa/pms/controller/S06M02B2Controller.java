package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDemandPojo;
import inks.service.sa.pms.domain.pojo.SaEngineerPojo;
import inks.service.sa.pms.domain.vo.EmailReceiverVO;
import inks.service.sa.pms.mapper.SaEngineerMapper;
import inks.service.sa.pms.mapper.SaWorkgroupMapper;
import inks.service.sa.pms.service.SaDemandService;
import inks.service.sa.pms.utils.PrintColor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;

import static inks.service.sa.pms.utils.email.EmailReceiverUtils.receiveEmails;


/**
 * 产品需求(Sa_Demand)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-24 16:43:02
 */
@RestController
@RequestMapping("S06M02B2")
@Api(tags = "S06M02B2:产品需求")
@Validated
public class S06M02B2Controller extends SaDemandController {
    @Resource
    private SaWorkgroupMapper saWorkgroupMapper;


    @Resource
    private SaDemandService saDemandService;

    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaEngineerMapper saEngineerMapper;
    @Resource
    private S06M30B1Controller s06M30B1Controller; //用来发送Mqtt信息
    // 查询收件箱的配置:
    @Value("${spring.mail.username}")
    private String username;
    @Value("${spring.mail.password}")
    private String password;
    @Value("${spring.mail.receiver.protocol}")
    private String receiverProtocol;
    @Value("${spring.mail.receiver.port}")
    private int receiverPort;
    @Value("${spring.mail.receiver.host}")
    private String receiverHost;


    //------------------------------------需求单报表类-------------------------------------

    public static void main(String[] args) {
        // 构建消息内容
        JSONObject resultContent = new JSONObject();

        // 示例数据
        JSONObject infoObject = new JSONObject();
        infoObject.put("code", "create");
        infoObject.put("date", "2024-06-08 10:22:16");
        infoObject.put("content", "【新建】订单加入XX事务");

        JSONObject msgObject = new JSONObject();
        msgObject.put("info", infoObject);
        msgObject.put("msgtype", "info");

        resultContent.put("msg", msgObject);
        resultContent.put("modulecode", "sapms");

        // 获取创建人id 作为发送主题
        String userId = "1211212"; // 假设demandDB是已经定义的对象
        String topic = "inks/server/sapms/" + userId;

        System.out.println("消息内容: " + resultContent.toJSONString());
        System.out.println("发送主题: " + topic);
    }


    //    --------------------------------------从****************读取收件箱并同步到需求单中---------------------------------------

    // 只要除了****************之外的第一个收件人.  若只有****************,则返回空字符串
    public static String parseRecipientsFirst(String recipients) {
        if (StringUtils.isBlank(recipients)) {
            throw new IllegalArgumentException("收件人不能为空");
        }
        String[] recipientList = recipients.split(", ");
        String selectedRecipient = null;
        for (String recipient : recipientList) {
            String email = recipient.trim();
            if (email.contains("<") && email.contains(">")) {
                email = email.substring(email.indexOf("<") + 1, email.indexOf(">"));
            }
            if (!email.equals("<EMAIL>")) {//只要除了****************之外的第一个收件人
                selectedRecipient = email;
                break;
            }
        }
        return selectedRecipient == null ? "" : selectedRecipient;
    }

    /**
     * @return R<PageInfo < SaDemandPojo>>
     * @Description
     * <AUTHOR>
     * @param[1] json
     * @param[2] own 1我的 0所有 -1公海（没有Appointeeid被指派人）
     * @param[4] finishlimit=20表示取完结状态的需求前20条(不传默认20条)  UNION ALL: WHERE Sa_Demand.Status IN (SELECT id FROM Sa_ProjectStatus WHERE StatusType = '已完成' AND FinishMark = 1) limit 20
     * @time 2023/10/26 14:16
     */
    @ApiOperation(value = "(未完成)需求任务:按条件分页查询 own:1我的 0所有 -1公海（没有Appointeeid被指派人）", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<PageInfo<SaDemandPojo>> getPageList(@RequestBody String json, @RequestParam(required = false) Integer own) {
        try {
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 现在，您可以访问工程师信息
            inks.sa.common.core.domain.vo.SaEngineerPojo engineer = myLoginUser.getEngineer();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            }

            String qpfilter = "";
            if (own == null || own == 1) {
                if (engineer != null) {
                    qpfilter += " and (Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "' or Sa_Demand.Appointeeid = '" + engineer.getId() + "')";
                } else {
                    qpfilter += " and Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "'";
                }
            } else if (own == -1) {
                qpfilter += " and Sa_Demand.Appointeeid = ''";
                qpfilter += queryParam.getAllFilter();
                queryParam.setFilterstr(qpfilter);
                return R.ok(this.saDemandService.getAllPageList(queryParam));
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(已完成)需求任务:按条件分页查询 own:1我的 0所有 -1公海（没有Appointeeid被指派人）", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListFinish", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<PageInfo<SaDemandPojo>> getPageListFinish(@RequestBody String json,
                                                       @RequestParam(required = false) Integer own) {
        try {
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 现在，您可以访问工程师信息
            inks.sa.common.core.domain.vo.SaEngineerPojo engineer = myLoginUser.getEngineer();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            }

            String qpfilter = " and Sa_Demand.FinishMark = 1";
            if (own == null || own == 1) {
                if (engineer != null) {
                    qpfilter += " and (Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "' or Sa_Demand.Appointeeid = '" + engineer.getId() + "')";
                } else {
                    qpfilter += " and Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "'";
                }
            } else if (own == -1) {
                qpfilter += " and Sa_Demand.Appointeeid = ''";
                qpfilter += queryParam.getAllFilter();
                queryParam.setFilterstr(qpfilter);
                return R.ok(this.saDemandService.getAllPageList(queryParam));
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "Online需求任务:按条件分页查询 own:1我的 0所有 -1公海（没有Appointeeid被指派人）", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<PageInfo<SaDemandPojo>> getOnlinePageList(@RequestBody String json, @RequestParam(required = false) Integer own) {
        try {
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 现在，您可以访问工程师信息
            inks.sa.common.core.domain.vo.SaEngineerPojo engineer = myLoginUser.getEngineer();
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            }
            String qpfilter = " and Sa_Demand.DisannulMark = 0 and Sa_Demand.FinishMark = 0";
            if (own == null || own == 1) {
                if (engineer != null) {
                    qpfilter += " and (Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "' or Sa_Demand.Appointeeid = '" + engineer.getId() + "')";
                } else {
                    qpfilter += " and Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "'";
                }
            } else if (own == -1) {
                qpfilter += " and Sa_Demand.Appointeeid = ''";
                qpfilter += queryParam.getAllFilter();
                queryParam.setFilterstr(qpfilter);
                return R.ok(this.saDemandService.getAllPageList(queryParam));
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "无权限接口：指定客户的需求任务", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListBySerCode", method = RequestMethod.POST)
    public R<PageInfo<SaDemandPojo>> getPageListBySerCode(@RequestBody String json, @RequestParam String sercode) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            }
            String qpfilter = "";
            String groupid = saWorkgroupMapper.getIdBySerCode(sercode);
            if (StringUtils.isBlank(groupid)) {
                throw new BaseBusinessException("sercode无效客户");
            }
            qpfilter += " and Sa_Demand.Groupid =  '" + groupid + "'";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "无权限接口：指定客户的Online需求任务", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageListBySerCode", method = RequestMethod.POST)
    public R<PageInfo<SaDemandPojo>> getOnlinePageListBySerCode(@RequestBody String json, @RequestParam String sercode) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy())) {
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            }
            String qpfilter = " and Sa_Demand.DisannulMark = 0 and Sa_Demand.FinishMark = 0";
            String groupid = saWorkgroupMapper.getIdBySerCode(sercode);
            if (StringUtils.isBlank(groupid)) {
                throw new BaseBusinessException("sercode无效客户");
            }
            qpfilter += " and Sa_Demand.Groupid =  '" + groupid + "'";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "Online需求任务:按分类BillType统计数量 own:1我的 0所有(默认)", notes = "", produces = "application/json")
    @RequestMapping(value = "/getCountOnlineByBillType", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<List<Map<String, Integer>>> getCountOnlineByBillType(@RequestParam(required = false) Integer own) {
        try {
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 现在，您可以访问工程师信息
            inks.sa.common.core.domain.vo.SaEngineerPojo engineer = myLoginUser.getEngineer();
            String qpfilter = " and Sa_Demand.DisannulMark = 0 and Sa_Demand.FinishMark = 0";
            if (Objects.equals(own, 1)) {
                if (engineer != null) {
                    qpfilter += " and (Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "' or Sa_Demand.Appointeeid = '" + engineer.getId() + "')";
                } else {
                    qpfilter += " and Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "'";
                }
            }
            return R.ok(this.saDemandService.getCountByBillType(qpfilter));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "当天完成的需求任务:按分类BillType统计数量 own:1我的 0所有(默认)", notes = "", produces = "application/json")
    @RequestMapping(value = "/getCountFinishByBillType", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<List<Map<String, Integer>>> getCountFinishByBillType(@RequestParam(required = false) Integer own) {
        try {
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 现在，您可以访问工程师信息
            inks.sa.common.core.domain.vo.SaEngineerPojo engineer = myLoginUser.getEngineer();
            String qpfilter = " and Sa_Demand.DisannulMark = 0 and Sa_Demand.FinishMark = 1 ";
            qpfilter += " and DATE(Sa_Demand.FinishDate) = CURDATE()";
            if (Objects.equals(own, 1)) {
                if (engineer != null) {
                    qpfilter += " and (Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "' or Sa_Demand.Appointeeid = '" + engineer.getId() + "')";
                } else {
                    qpfilter += " and Sa_Demand.CreateByid = '" + myLoginUser.getUserid() + "'";
                }
            }
            return R.ok(this.saDemandService.getCountByBillType(qpfilter));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "(发送MQTT)完成产品需求 cmd=1完成,cmd=0撤回完成", notes = "", produces = "application/json")
    @RequestMapping(value = "/finish", method = RequestMethod.POST)
    public R<SaDemandPojo> finish(@RequestBody String json, Integer cmd) {
        try {
            if (cmd == null) cmd = 0;
            // 拿到传入的原需求单
            SaDemandPojo saDemandPojo = JSONArray.parseObject(json, SaDemandPojo.class);
            // 检验是否已完成/未完成 FinishMark=1/0 不能重复=1/0
            SaDemandPojo demandDB = saDemandService.getEntity(saDemandPojo.getId());
            Integer finishMarkDB = demandDB.getFinishmark();
            if (Objects.equals(cmd, 1)) {
                if (Objects.equals(finishMarkDB, 1)) {
                    return R.fail("该需求已完成");
                }
                saDemandPojo.setFinishmark(1);
                saDemandPojo.setFinishdate(new Date());   // 完成时间
            } else if (Objects.equals(cmd, 0)) {
                if (Objects.equals(finishMarkDB, 0)) {
                    return R.fail("该需求未完成");
                }
                saDemandPojo.setFinishmark(0);
                saDemandPojo.setFinishdate(null);   // 完成时间
            }
            // 获得用户数据
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            saDemandPojo.setLister(myLoginUser.getRealname());   // 制表
            saDemandPojo.setListerid(myLoginUser.getUserid());    // 制表id
            saDemandPojo.setModifydate(new Date());   //修改时间
            SaDemandPojo updatePojo = this.saDemandService.update(saDemandPojo, myLoginUser);

            // 发送MQTT消息 格式如下：
            //{
            //    "msg": {
            //        "msgtype": "info",
            //        "info": {
            //            "date": "2024-06-08 10:22:16",
            //            "code": "finish",
            //            "content": "【完成】订单加入XX事务"
            //        }
            //    },
            //    "modulecode": "sapms"
            //}
            // 构建最终发送消息内容和发送主题
            JSONObject resultContent = new JSONObject();
            // 获取创建人id 作为发送主题
            String resultTopic = "inks/server/sapms/" + demandDB.getCreatebyid();
            // 示例数据
            JSONObject infoObject = new JSONObject();
            infoObject.put("code", "finish");
            //完成时间: 2024-06-08 10:22:16格式
            SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
            String finishDateStr = simpleDateFormat.format(new Date());
            infoObject.put("date", finishDateStr);
            //cmd=1 【完成】 cmd=0 【撤回完成】
            String prefix = cmd == 1 ? "【完成】" : "【撤回完成】";
            String content = prefix + demandDB.getBilltitle();
            infoObject.put("content", content);

            JSONObject msgObject = new JSONObject();
            msgObject.put("info", infoObject);
            msgObject.put("msgtype", "info");

            resultContent.put("msg", msgObject);
            resultContent.put("modulecode", "sapms");


            // 构建发送的JSON
            JSONObject contentObject = new JSONObject();
            contentObject.put("content", resultContent.toJSONString());
            contentObject.put("topic", resultTopic);

            // 调用S06M30B1Controller的send方法发送MQTT消息
            s06M30B1Controller.send(contentObject.toJSONString());

            PrintColor.red("发送MQTT为:" + contentObject.toJSONString());
            return R.ok(updatePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "每个人的需求在线数、完工数、月完工数", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlineStatus", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Demand.List")
    public R<List<Map<String, Object>>> getOnlineStatus() {
        try {
            return R.ok(this.saDemandService.getOnlineStatus());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // 定时任务，每隔半小时从收件箱****************读取邮件，去重并同步到需求单中
    @Scheduled(cron = "${spring.mail.receiver.scheduled}")
    public void scheduleSyncDemandFromEmail() {
        // 默认读取半小时内收件箱
        Date currentDate = new Date();
        Date startDate = DateUtils.addMinutes(currentDate, -31);
        syncDemandFromEmail(null, null, null);
    }

//
//    public static void main(String[] args) {
//        String s = parseRecipientsFirst("1002026295 <<EMAIL>>, pms <<EMAIL>>, <EMAIL>, 1002026295 <<EMAIL>>");
//        System.out.println("s = " + s);
//    }

    /**
     * @return R<String>
     * @Description 邮件同步新建的需求单信息中:
     * Appointeeid被指派人为收件人中除了****************之外的第一个收件人关联的工程师id。
     * Appointee 指派工程师Name
     * BillTitle为邮件主题。
     * Createby、Createbyid为发件人关联的Userid/RealName。
     * Remark写死为“PMS收件箱同步”。
     * Projectid为空""。
     * <AUTHOR>
     * @param[1] iwant 要获取的邮件数量(最新的iwant封)。 如果传null，则获取全部邮件。
     * @param[2] startDate 邮件接收开始时间。 如果传null，则不限制开始时间。
     * @param[3] endDate 邮件接收结束时间。 如果传null，则不限制结束时间。
     * @time 2024/5/30 上午11:18
     */
    @GetMapping("/syncDemandFromEmail")
    @ApiOperation(value = "从****************读取收件箱并同步到需求单中", notes = "", produces = "application/json")
    public R<String> syncDemandFromEmail(@RequestParam(required = false) Integer iwant,
                                         @RequestParam(required = false) Date startDate,
                                         @RequestParam(required = false) Date endDate) {

        if (startDate == null && endDate == null) {
            // 默认读取半小时内收件箱
            Date currentDate = new Date();
            startDate = DateUtils.addMinutes(currentDate, -31);
            endDate = currentDate;
        }
        // 接收邮件
        List<EmailReceiverVO> emailReceivers = receiveEmails(receiverProtocol, receiverHost, receiverPort, username, password, iwant, startDate, endDate);
        // 检查邮件是否成功接收
        if (emailReceivers == null) {
            // 返回失败信息
            return R.fail("收件箱读取失败");
        }
        // 总邮件数
        int totalEmails = emailReceivers.size();
        // 已存在需求单数量
        int existingDemands = 0;
        // 新需求单数量
        int newDemands = 0;
        // 存储需求单主题
        StringBuilder demandSubjects = new StringBuilder();
        // 拼接一些报错提示
        StringBuilder errorMsg = new StringBuilder();

        // 遍历邮件
        for (EmailReceiverVO emailReceiver : emailReceivers) {
            String senderUserid = "";
            String senderRealName = "";
            String senderEngineerid = "";
            String recipientUserid = "";
            String recipientRealName = "";
            String recipientEngineerid = "";
            String subject = emailReceiver.getSubject();

            // 检查邮件主题是否已处理过，若已处理则跳过
            if (saDemandService.checkTitleWithRemark(subject, "PMS收件箱同步")) {
                existingDemands++;
                continue;
            }

            // 获取发件人信息  娜诺 <<EMAIL>> 若有<> 则只要<>内的部分
            String senderEmail = emailReceiver.getSender();
            if (senderEmail.contains("<") && senderEmail.contains(">")) {
                senderEmail = senderEmail.substring(senderEmail.indexOf("<") + 1, senderEmail.indexOf(">"));
            }
            SaEngineerPojo engineerSender = saEngineerMapper.getEntityByEmail(senderEmail);
            if (engineerSender != null) {
                senderUserid = engineerSender.getUserid();
                senderRealName = engineerSender.getRealname();
            } else {
                errorMsg.append("发件人邮箱: ").append(senderEmail).append(" 不存在绑定的工程师信息! ");
            }

            // 获取收件人信息
            String recipientEmail = parseRecipientsFirst(emailReceiver.getRecipients());
            SaEngineerPojo engineerRecipient = saEngineerMapper.getEntityByEmail(recipientEmail);
            if (engineerRecipient != null) {
                recipientUserid = engineerRecipient.getUserid();
                recipientRealName = engineerRecipient.getRealname();
                recipientEngineerid = engineerRecipient.getId();
            } else {
                errorMsg.append("收件人邮箱: ").append(recipientEmail).append(" 不存在绑定的工程师信息! ");
            }

            // 构建需求单对象
            SaDemandPojo newDemand = new SaDemandPojo();
            newDemand.setBilltitle(subject);
            newDemand.setRefno(DateUtils.parseDateToStr("yyyyMMdd", new Date()) + String.format("%04d", new Random().nextInt(9999)));
            newDemand.setAppointeeid(recipientEngineerid);
            newDemand.setAppointee(recipientRealName);
            newDemand.setCreateby(senderRealName);
            newDemand.setCreatebyid(senderUserid);
            newDemand.setRemark("PMS收件箱同步");
            newDemand.setProjectid("");
            LoginUser loginUser = new LoginUser();
            loginUser.setUserid(senderUserid);
            loginUser.setRealname(senderRealName);
            saDemandService.insert(newDemand, loginUser);

            // 新需求单计数，并记录主题
            newDemands++;
            demandSubjects.append(subject).append(", ");
        }

        // 返回处理结果信息
        String resultMsg = String.format(errorMsg + "本次共查询到%d个邮件，其中%d个已经生成过了需求单;本次新生成需求单%d个,主题分别是：%s",
                totalEmails, existingDemands, newDemands, demandSubjects);
        return R.ok(resultMsg);
    }

    @GetMapping("/testReceiveEmails")
    public R<String> testReceiveEmails() {
        List<EmailReceiverVO> emailReceivers = receiveEmails(receiverProtocol, receiverHost, receiverPort, username, password, 2, null, null);
        if (emailReceivers == null) {
            return R.fail("收件箱读取失败");
        }
        // 遍历并打印每封邮件
        for (EmailReceiverVO emailReceiver : emailReceivers) {
            System.out.println("================Controller层==========================");
            System.out.println(receiverProtocol + " 邮件主题: " + emailReceiver.getSubject());
            System.out.println(receiverProtocol + " 发件人: " + emailReceiver.getSender());
            System.out.println(receiverProtocol + " 接收人: " + emailReceiver.getRecipients());
            System.out.println(receiverProtocol + " 发送日期: " + emailReceiver.getSentDate());
            System.out.println(receiverProtocol + " 接收日期: " + emailReceiver.getReceivedDate());
            System.out.println(receiverProtocol + " 抄送人: " + emailReceiver.getCcRecipients());
            System.out.println(receiverProtocol + " 密送人: " + emailReceiver.getBccRecipients());
            System.out.println(receiverProtocol + " 回复给: " + emailReceiver.getReplyTo());
            System.out.println(receiverProtocol + " 邮件内容: " + emailReceiver.getContent());
            System.out.println(receiverProtocol + " 邮件大小: " + emailReceiver.getSize());
            System.out.println(receiverProtocol + " 邮件标记: " + emailReceiver.getFlags());
            System.out.println("==========================================");
        }

        return R.ok("success");
    }
}
