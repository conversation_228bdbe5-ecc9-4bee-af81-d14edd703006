package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDemandsubmitPojo;
import inks.service.sa.pms.domain.pojo.SaRmsjustauthPojo;
import inks.service.sa.pms.mapper.SaDemandsubmitMapper;
import inks.service.sa.pms.service.SaDemandsubmitService;
import inks.service.sa.pms.service.SaRmsjustauthService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.io.StringWriter;
import java.util.Date;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 需求提报(Sa_DemandSubmit)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-25 13:27:24
 */
@RestController
@RequestMapping("S06M02B3")
@Api(tags = "S06M02B3:需求提报")
public class S06M02B3Controller extends SaDemandsubmitController {
    @Resource
    private SaDemandsubmitService saDemandsubmitService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaRmsjustauthService saRmsjustauthService;
    @Resource
    private S06M36B1Controller s06M36B1Controller;
    @Resource
    private SaDemandsubmitMapper saDemandsubmitMapper;


    @ApiOperation(value = "按条件分页查询 已转需求的不要", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandSubmit.List")
    public R<PageInfo<SaDemandsubmitPojo>> getOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DemandSubmit.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Sa_DemandSubmit.DemandMark=1";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandsubmitService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // key：需求提报单id apprid:redis中存放的模板对象 apprrecPojo.getDatatemp()是模板内容
    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type, String remark) {
        try {
            if (type == null) type = "wxe";  // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
            //获取token
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //从redis中获取模板对象
            // Object obj = redisService.getCacheObject(verifyKey);
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            ApprovePojo approvePojo = new ApprovePojo();
            SaDemandsubmitPojo saDemandsubmitPojo = this.saDemandsubmitService.getEntity(key);
            approvePojo.setObject(saDemandsubmitPojo);
            // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
            if (Objects.equals(saDemandsubmitPojo.getOaflowmark(), 1)) {
                return R.fail("该单据已发起OA审批");
            }
            if ("oms".equals(type)) {
                //创建VM数据对象
                VelocityContext context = new VelocityContext();
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
//                String data = JSONObject.toJSONString(approvePojo.getObject());
//                apprrecPojo.setDatatemp(data);
            } else {
                //创建VM数据对象
                VelocityContext context = new VelocityContext();

                //获得第三方账号  默认走企业微信 type="wxe"
                SaRmsjustauthPojo rmsjustauthPojo = saRmsjustauthService.getEntityByUserid(loginUser.getUserid(), type);
                if (rmsjustauthPojo == null) {
                    throw new Exception("当前账号未关联到第三方账号 Sa_RmsJustAuth");
                }
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(rmsjustauthPojo, justauthPojo);
                approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
                approvePojo.setUserid(justauthPojo.getAuthuuid());
                approvePojo.setModelcode(apprrecPojo.getTemplateid());
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
            }


            //新建审批记录
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            apprrecPojo.setApprname("订单审批");
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key);    // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(new Date());
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(new Date());
            apprrecPojo.setTenantid(loginUser.getTenantid());
            //发起Flowable审批时加入的评论备注
            apprrecPojo.setRemark(remark == null ? "" : remark);
            //将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setCacheObject(CachKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
            if ("wxe".equals(type)) {
                R r = s06M36B1Controller.sendapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }
            }
            //else if ("ding".equals(type)) {
            //    R r = this.utilsFeignService.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
            //    if (r.getCode() != 200) {
            //        return R.fail("发起审批失败" + r.getMsg());
            //    }
            //}
            //else {//type为oms
            //    R r = this.utilsFeignService.omsapprovel(apprrecPojo.getId(), loginUser.getTenantid(), loginUser.getToken());
            //    if (r.getCode() != 200) {
            //        return R.fail("发起审批失败" + r.getMsg());
            //    }
            //}
            // 发起oms审批成功,需设置OaFlowMark=1 并更新单据
            saDemandsubmitPojo.setOaflowmark(1);
            saDemandsubmitMapper.updateOaflowmark(saDemandsubmitPojo);
            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R<SaDemandsubmitPojo> justapprovel(String key, String type, String approved) {
        try {
            System.out.println("审核通过,写入审核信息");
            //1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            //2.1 获得单据数据
            SaDemandsubmitPojo saDemandsubmitPojo = this.saDemandsubmitService.getEntity(apprrecPojo.getBillid());
            //3. 写入审核批
            //获得第三方账号
            if (type == null) type = "wxe";
            // oms审批即将完成,需设置OaFlowMark=0
            saDemandsubmitPojo.setOaflowmark(0);
            saDemandsubmitMapper.updateOaflowmark(saDemandsubmitPojo);//只更新oaflowmark
            // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
            if ("false".equals(approved)) {
                return R.ok();
            }
            if ("oms".equals(type)) {
                // 点击同意审批：审批人字段赋值, if包裹外面的approval方法会进行审核
                saDemandsubmitPojo.setAssessorid(apprrecPojo.getUserid());
                saDemandsubmitPojo.setAssessor(apprrecPojo.getRealname()); //审核员
            } else {
                SaRmsjustauthPojo rmsjustauthPojo = saRmsjustauthService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type);
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(rmsjustauthPojo, justauthPojo);

                saDemandsubmitPojo.setAssessorid(justauthPojo.getUserid());
                saDemandsubmitPojo.setAssessor(justauthPojo.getRealname()); //审核员
            }
            saDemandsubmitPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDemandsubmitService.approval(saDemandsubmitPojo));
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }
}
