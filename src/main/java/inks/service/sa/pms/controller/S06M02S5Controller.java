package inks.service.sa.pms.controller;

import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.domain.vo.SaEngineerPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.mapper.SaProjectsortMapper;
import inks.service.sa.pms.service.SaProjectsortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 个人常用项目排序(Sa_ProjectSort)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-25 13:07:31
 */
@RestController
@RequestMapping("S06M02S5")
@Api(tags = "S06M02S5:个人常用项目排序")
public class S06M02S5Controller extends SaProjectsortController {
    @Resource
    private SaProjectsortService saProjectsortService;
    @Resource
    private SaProjectsortMapper saProjectsortMapper;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "设置星标(Sa_ProjectSort表),传入projectid和starmark", notes = "", produces = "application/json")
    @RequestMapping(value = "/SetStar", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_ProjectSort.Edit")
    public R<Integer> SetStar(String projectid, int starmark) {
        try {
            // 获得用户数据
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
//            // 将 loginUser 强制转换为 MyLoginUser 类型
//            MyLoginUser myLoginUser = (MyLoginUser) loginUser;
            // 现在，您可以访问工程师信息
            SaEngineerPojo engineer = myLoginUser.getEngineer();
            if (engineer == null) {
                return R.fail("未关联工程师");
            }
            return R.ok(this.saProjectsortMapper.SetStar(projectid, starmark, engineer.getId()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
