package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.service.SaDemandtimeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.concurrent.ConcurrentHashMap;

/**
 * 需求工时记录(Sa_DemandTime)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-02 11:06:11
 */
@RestController
@RequestMapping("S06M02S7")
@Api(tags = "S06M02S7:需求工时记录")
public class S06M02S7Controller extends SaDemandtimeController {
    // 用于存储用户userid的操作时间，避免频繁点击<userid, 时间戳>
    private static final ConcurrentHashMap<String, Long> userClickTimeMap = new ConcurrentHashMap<>();
    // 限制点击间隔时间
    private static final long interval = 3 * 1000;  // 3秒
    @Resource
    private SaDemandtimeService saDemandtimeService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "开始需求工时记录 传入需求id", notes = "", produces = "application/json")
    @RequestMapping(value = "/start", method = RequestMethod.GET)
    public R<String> start(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String userid = loginUser.getUserid();

            // 检查点击间隔时间
            if (!checkClickInterval(userid)) {
                return R.fail("操作过于频繁，请" + interval / 1000 + "秒后再试。");
            }

            // 处理开始需求工时记录的逻辑
            return R.ok(this.saDemandtimeService.start(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "暂停需求工时记录 传入需求id", notes = "", produces = "application/json")
    @RequestMapping(value = "/pause", method = RequestMethod.GET)
    public R<String> pause(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String userid = loginUser.getUserid();

            // 检查点击间隔时间
            if (!checkClickInterval(userid)) {
                return R.fail("操作过于频繁，请" + interval / 1000 + "秒后再试。");
            }

            // 处理暂停需求工时记录的逻辑
            return R.ok(this.saDemandtimeService.pause(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "完成需求工时记录 传入需求id", notes = "", produces = "application/json")
    @RequestMapping(value = "/complete", method = RequestMethod.GET)
    public R<String> complete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String userid = loginUser.getUserid();

            // 检查点击间隔时间
            if (!checkClickInterval(userid)) {
                return R.fail("操作过于频繁，请" + interval / 1000 + "秒后再试。");
            }

            // 处理完成需求工时记录的逻辑
            return R.ok(this.saDemandtimeService.complete(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 检查用户点击间隔时间
     *
     * @param userid 用户ID
     * @return 是否可以通过点击间隔检查 true: 可以通过 false: 不可以通过
     */
    private boolean checkClickInterval(String userid) {
        try {
            long currentTime = System.currentTimeMillis();
            // 使用 ConcurrentHashMap 存储用户的上次点击时间
            if (userClickTimeMap.containsKey(userid)) {
                long lastClickTime = userClickTimeMap.get(userid);
                // 如果用户在限制时间内再次点击，返回 false
                if (currentTime - lastClickTime < interval) {
                    return false;
                }
            }
            // 更新用户的点击时间
            userClickTimeMap.put(userid, currentTime);
            return true;
        } catch (Exception e) {
            return false;
        }
    }
}
