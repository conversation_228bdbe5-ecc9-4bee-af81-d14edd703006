package inks.service.sa.pms.controller;


import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.config.oss.Storage;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaKnowledgePojo;
import inks.service.sa.pms.service.SaKnowledgeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.Date;

/**
 * 知识库(Sa_Knowledge)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-23 13:12:49
 */
@Api(tags = "S06M04B1:知识库")
@RestController
@RequestMapping("/S06M04B1")
public class S06M04B1Controller extends SaKnowledgeController {


    @Value("${oss.bucket}")
    private String BUCKET_NAME;
    @Resource
    @Qualifier("minioStorage")
    private Storage storage;


    @Resource
    private SaKnowledgeService saKnowledgeService;


    @Resource
    private SaRedisService saRedisService;


    //TODO 取消知识库点赞 建表用户-知识库关联表
    @ApiOperation(value = "知识库点赞/点踩", notes = "知识库点赞/点踩", produces = "application/json")
    @RequestMapping(value = "/textGoodNum", method = RequestMethod.POST)
    public R<SaKnowledgePojo> TextGoodNum(String key, Integer isGood) throws Exception {
        SaKnowledgePojo saKnowledgePojo = saKnowledgeService.getEntity(key);
        if (isGood == 1) {
            Integer textgoodnum = saKnowledgePojo.getTextgoodnum();
            saKnowledgePojo.setTextgoodnum(++textgoodnum);
        } else if (isGood == 0) {
            Integer textngnum = saKnowledgePojo.getTextngnum();
            saKnowledgePojo.setTextngnum(++textngnum);
        }
        SaKnowledgePojo updateKnowledge = saKnowledgeService.update(saKnowledgePojo);
        return R.ok(updateKnowledge);
    }


    @ApiOperation(value = "上传文本", notes = "上传文本")
//    @PostMapping(value = "saveContent")
    @RequestMapping(value = "/saveContent", method = RequestMethod.POST)//   /uploadText?title="常见问题汇总"
    public R<String> saveContent(@RequestBody String json, String billtitle, String id) {
        FileInfo fileInfofront = JSONArray.parseObject(json, FileInfo.class);
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        try {
            String dir = "text/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
            fileInfofront.setDirname(dir);
            fileInfofront.setBucketname(BUCKET_NAME);
            fileInfofront.setContenttype("text/plain");
//            FileInfo haofileInfo = this.ossService.uploadText( file, dir);
//            FileInfo fileInfo = this.ossService.putContent(fileInfofront);

            SaKnowledgePojo entity = saKnowledgeService.getEntity(id);
            SaKnowledgePojo saKnowledgePojo = new SaKnowledgePojo();

            //saKnowledgePojo.setFilename(fileInfo.getFilename());
            //saKnowledgePojo.setFilesize(fileInfo.getFilesize());
            //saKnowledgePojo.setBucketname(fileInfo.getBucketname());
            //saKnowledgePojo.setDirname(fileInfo.getDirname());
            //saKnowledgePojo.setContenttype(fileInfo.getContenttype());
            //设置标题，供知识库搜索功能
            saKnowledgePojo.setBilltitle(billtitle);
            //saKnowledgePojo.setKnowurl(fileInfo.getFileurl());
            saKnowledgePojo.setCreatebyid(loginUser.getUserid());
            saKnowledgePojo.setCreateby(loginUser.getRealName());

            //更新富文本
            if (entity != null) {
                saKnowledgePojo.setId(id);
                //更新需先删除删除桶中的富文本
                String objectName = entity.getDirname() + "/" + entity.getFilename();
                storage.removeObject(BUCKET_NAME, objectName);
                saKnowledgeService.update(saKnowledgePojo);
            } else {
                saKnowledgePojo = saKnowledgeService.insert(saKnowledgePojo);
            }
//            return R.ok(fileInfo.getFileurl());
            System.out.println(saKnowledgePojo.getId());
            return R.ok(saKnowledgePojo.getId());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "富文本读取", notes = "富文本读取", produces = "application/json")
    @RequestMapping(value = "/getText", method = RequestMethod.GET)
    ////@PreAuthorize(hasPermi = "Uts_VideoLibrary.List")                                               url即为objectName
    public R<String> getText(HttpServletResponse response, HttpServletRequest request, @RequestParam("url") String url) {
        InputStream inputstream = null;
        try {
            inputstream = storage.getObject(BUCKET_NAME, url);
            BufferedReader bdr = new BufferedReader(new InputStreamReader(inputstream));
            String line = "";
            StringBuffer stringBuffer = new StringBuffer();
            int i = 1;
            while ((line = bdr.readLine()) != null) {
                System.out.println("i----" + i + "----" + line);
                i++;
                stringBuffer.append(line);
            }
            return R.ok(stringBuffer.toString());

        } catch (IOException e) {
            e.printStackTrace();
        } catch (Exception e) {
            e.printStackTrace();
        }
        return R.fail(url);
    }


}