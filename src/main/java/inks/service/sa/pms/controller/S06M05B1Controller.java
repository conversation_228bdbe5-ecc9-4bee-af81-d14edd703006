package inks.service.sa.pms.controller;

import inks.common.core.domain.R;
import inks.common.core.utils.DateUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaVideolibraryPojo;
import inks.service.sa.pms.service.SaVideogroupService;
import inks.service.sa.pms.service.SaVideolibraryService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 视频信息表(Sa_VideoLibrary)
 *
 * <AUTHOR>
 */
@Api(tags = "S06M05B1:视频中心")
@RestController
@RequestMapping("/S06M05B1")
public class S06M05B1Controller extends SaVideolibraryController {


    @Resource
    private SaVideolibraryService saVideolibraryService;
    @Resource
    private SaVideogroupService saVideogroupService;


    @Resource
    private SaRedisService saRedisService;

    //TODO 取消点赞 建表用户-视频关联表
    @ApiOperation(value = "视频点赞/点踩", notes = "视频点赞/点踩", produces = "application/json")
    @RequestMapping(value = "/videoGoodNum", method = RequestMethod.POST)
    public R<SaVideolibraryPojo> SumVideoGoodNum(String key, Integer isGood) throws Exception {
        SaVideolibraryPojo saVideolibraryPojo = saVideolibraryService.getEntity(key);
        if (isGood == 1) {
            Integer videogoodnum = saVideolibraryPojo.getVideogoodnum();
            saVideolibraryPojo.setVideogoodnum(++videogoodnum);
        } else if (isGood == 0) {
            Integer videongnum = saVideolibraryPojo.getVideongnum();
            saVideolibraryPojo.setVideogoodnum(++videongnum);
        }
        SaVideolibraryPojo updateVideolibrary = saVideolibraryService.update(saVideolibraryPojo);
        return R.ok(updateVideolibrary);
    }


    @ApiOperation(value = "图片封面上传", notes = "图片上传", produces = "application/json")
    @RequestMapping(value = "/uploadPic", method = RequestMethod.POST)
    ////@PreAuthorize(hasPermi = "Uts_VideoLibrary.Add")
    public R<String> uploadPic(MultipartFile file) {
        try {
//            String dir = "picture/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
//            FileInfo fileInfo = this.ossService.uploadPic(file);
//            return R.ok(fileInfo.getFileurl());
            return R.ok();
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "多图片上传", notes = "", produces = "application/json")
    @RequestMapping(value = "/uploadPics", method = RequestMethod.POST)
    ////@PreAuthorize(hasPermi = "Uts_VideoLibrary.Add")
    public R<List<String>> uploadPics(MultipartFile[] files) {
        try {
            String dir = "picture/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
            //List<FileInfo> fileInfos = this.ossService.uploadPics(files, dir);
            ArrayList<String> fileUrlList = new ArrayList<>();
            //for (FileInfo fileInfo : fileInfos) {
            //    fileUrlList.add(fileInfo.getFileurl());
            //}
//            return R.ok(fileInfo.getFileurl());
            return R.ok(fileUrlList);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}
