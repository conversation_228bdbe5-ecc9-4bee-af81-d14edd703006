package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.DateUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.MyFile;
import inks.service.sa.pms.domain.pojo.SaProjprofilePojo;
import inks.service.sa.pms.service.SaProjprofileService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 产品简介 Sa_ProjProfile
 *
 * <AUTHOR>
 * @date 2023年01月05日 15:28
 */
@Api(tags = "S06M08B1:产品简介")
@RestController
@RequestMapping("/S06M08B1")
public class S06M08B1Controller extends SaProjprofileController {



    @Value("${oss.bucket}")
    private String BUCKET_NAME;

    @Resource
    private SaProjprofileService saProjprofileService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "上传产品中心文本", notes = "上传产品中心文本")
    @RequestMapping(value = "/saveProjProfile", method = RequestMethod.POST)//   /uploadText?title="常见问题汇总"
    public R<String> saveProjProfile(@RequestBody String json) {
        MyFile myFile = JSONArray.parseObject(json, MyFile.class);
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        if (loginUser == null) {
            throw new BaseBusinessException("获取用户信息失败请确保登录后进行");
        }
        try {
            SaProjprofilePojo saProjprofilePojo = new SaProjprofilePojo();
            //DirnameFilename被图片征用  此时还是picture前缀，在变成text前缀前保存
            saProjprofilePojo.setFilename(myFile.getFilename());
            saProjprofilePojo.setDirname(myFile.getDirname());
            String dir = "text/" + DateUtils.parseDateToStr("yyyyMMdd", new Date());
            myFile.setDirname(dir);
            myFile.setBucketname(BUCKET_NAME);
            myFile.setContenttype("text/plain");
            // TODO 上传
            //FileInfo fileInfo = this.ossService.putContent(myFile);

            SaProjprofilePojo entity = saProjprofileService.getEntity(myFile.getId());

            //设置标题，供知识库搜索功能
            saProjprofilePojo.setTitle(myFile.getTitle());
            //saProjprofilePojo.setContent(fileInfo.getFileurl());
            saProjprofilePojo.setPicture(myFile.getPicture());
            saProjprofilePojo.setProjectname(myFile.getProjectname());
            saProjprofilePojo.setProjectid(myFile.getProjectid());
            saProjprofilePojo.setCreatebyid(loginUser.getUserid());
            saProjprofilePojo.setCreateby(loginUser.getRealName());
            //绑定视频中心的视频
            saProjprofilePojo.setVideoname(myFile.getVideoName());
            saProjprofilePojo.setVideoid(myFile.getVideoid());
            System.out.println(saProjprofilePojo);

            //更新富文本
            if (entity != null) {
                saProjprofilePojo.setId(myFile.getId());
                //更新需先删除删除桶中的富文本
//                String objectName = entity.getDirname() + "/" + entity.getFilename();
//                storage.removeObject(BUCKET_NAME, objectName);
                saProjprofileService.update(saProjprofilePojo);
            } else {
                saProjprofilePojo = saProjprofileService.insert(saProjprofilePojo);
            }
//            return R.ok(fileInfo.getFileurl());
            System.out.println(saProjprofilePojo.getId());
            return R.ok(saProjprofilePojo.getId());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
