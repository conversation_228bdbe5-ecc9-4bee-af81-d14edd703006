package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaTodoPojo;
import inks.service.sa.pms.service.SaTodoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * To do(Sa_Todo)表控制层
 *
 * <AUTHOR>
 * @date 2023年02月18日 12:23
 */
@Api(tags = "S06M09B1:TODO")
@RestController
@RequestMapping("/S06M09B1")
public class S06M09B1Controller extends SaTodoController {


    @Resource
    private SaTodoService saTodoService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = "按条件分页查询未关闭未完成TODO own:1我的CreateByid 0所有 ", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    public R<PageInfo<SaTodoPojo>> getOnlinePageList(@RequestBody String json, @RequestParam(defaultValue = "0") Integer own) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Todo.CreateDate");
            //查询未关闭的To do事项
            String qpfilter = " and Sa_Todo.Closed=0 and Sa_Todo.FinishMark=0";
            //if (StringUtils.isNotBlank(type) && "1".equals(type)) {
            //    qpfilter += " and ( Sa_Todo.PublicMark=1";
            //    qpfilter += " or Sa_Todo.CreateByid='" + loginUser.getUserid() + "'";
            //    qpfilter += " or Sa_Todo.Accepterid='" + loginUser.getUserid() + "')";
            //}

            // 我的
            if (own == 1) {
                qpfilter += " and Sa_Todo.CreateByid='" + loginUser.getUserid() + "'";
            } else if (own == 0) {
                // 所有
                //qpfilter += " and Sa_Todo.PublicMark=1";
            }

            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            // 获得用户数据
            return R.ok(this.saTodoService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "开始Todo StartActual赋值", notes = "", produces = "application/json")
    @RequestMapping(value = "/start", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Todo.Add")
    public R<SaTodoPojo> start(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTodoService.start(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "完成Todo EndActual赋值 FinishMark=1", notes = "", produces = "application/json")
    @RequestMapping(value = "/finish", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Todo.Add")
    public R<SaTodoPojo> finish(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTodoService.finish(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "撤回完成Todo EndActual赋值null FinishMark=0", notes = "", produces = "application/json")
    @RequestMapping(value = "/recall", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Todo.Add")
    public R<SaTodoPojo> recall(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTodoService.recall(key, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
