package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaUserPojo;
import inks.service.sa.pms.domain.pojo.SaWorklogPojo;
import inks.service.sa.pms.service.PmsSaUserService;
import inks.service.sa.pms.service.SaWorklogService;
import inks.service.sa.pms.utils.email.EmailSendUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;


/**
 * 工作日志 Sa_WorkLog
 *
 * <AUTHOR>
 * @date 2023年03月06日 23:05
 */

@Api(tags = "S06M10B1:工作日志")
@RestController
@RequestMapping("/S06M10B1")
public class S06M10B1Controller extends SaWorklogController {
    @Resource
    private SaWorklogService saWorklogService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private EmailSendUtils emailSendUtils;
    //    @Resource
//    private JavaMailSenderImpl mailSender;
    @Resource
    private PmsSaUserService pmsSaUserService;


    @ApiOperation(value = "发送工作日志邮件", notes = "发送工作日志邮件", produces = "application/json")
    @RequestMapping(value = "/sendWorkEmail", method = RequestMethod.POST)
    public R<String> sendWorkEmail(@RequestBody String json) {
        try {
            JSONObject jsonObject = JSONArray.parseObject(json);
            //工作日志id
            String key = jsonObject.getString("key");
            //收件人邮箱
            String toemail = jsonObject.getString("toemail");
            //抄送人邮箱List
            String otheremails = jsonObject.getString("otheremails");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaUserPojo saUserPojo = pmsSaUserService.getEntity(loginUser.getUserid());
            //发件人邮箱
            String fromEmail = saUserPojo.getEmail();
            //发件人邮箱授权码
            String authCode = saUserPojo.getEmailauthcode();
            //发件人姓名
            String fromUserName = loginUser.getRealname();
            // 获得工作日志数据
            SaWorklogPojo saWorklogPojo = saWorklogService.getEntity(key);
            //生成邮件标题
            String subject = saWorklogPojo.getTitle();
            // 校验工作日志标题，不允许相同标题的工作日志再次发送邮件
            boolean hasTitle = saWorklogService.checkWorklogTitle(saWorklogPojo.getTitle());
            if (hasTitle) {
                return R.fail("工作日志标题重复，禁止发送邮件");
            }
            //生成邮件内容
            String content = "<h3>今日工作记录</h3>" + saWorklogPojo.getWorktoday()
                    + "<h3>明日工作计划</h3>" + saWorklogPojo.getWorktomorrow()
                    + "<h3>备注</h3>" + saWorklogPojo.getSummary();
            //发送
            /**
             * fromEmail:    发件人邮箱   "<EMAIL>"
             * authCode:     发件人邮箱授权码 "pcwwnfnesudmbbfd"
             * formUserName: 发件人姓名   "nanno"
             * toEmail:      收件人邮箱   "<EMAIL>"
             * otherEmails : 抄送人邮箱List   以分号分隔
             * subject:      邮件标题     "工作日志-nanno-20230306"
             * content:      邮件内容     "邮件内容..."
             */
            emailSendUtils.sendEmail(fromEmail, authCode, fromUserName, toemail, otheremails, subject, content);
            //发送成功后，更新邮件发送次数
            saWorklogPojo.setSendemailnum(saWorklogPojo.getSendemailnum() + 1);
            //更新工作日志的收件人和抄送人
            saWorklogPojo.setToemail(toemail);
            saWorklogPojo.setOtheremails(otheremails);
            saWorklogService.update(saWorklogPojo);
            return R.ok("发送成功");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    @ApiOperation(value = "发送工作日志邮件", notes = "发送工作日志邮件", produces = "application/json")
//    @RequestMapping(value = "/sendEmailTest", method = RequestMethod.GET)
//    public R sendEmailTest() {
//        try {
//            //创建发送邮件对象
//            MimeMessage mimeMessage = this.mailSender.createMimeMessage();
//            MimeMessageHelper helper = new MimeMessageHelper(mimeMessage, true);
//            helper.setFrom("<EMAIL>");
//            helper.setTo("<EMAIL>");
//            helper.setSubject("snbject/工作日志-nanno-20230306");
//            helper.setText("content/内容", true);
//            mailSender.send(mimeMessage);
//            return R.ok();
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }

}