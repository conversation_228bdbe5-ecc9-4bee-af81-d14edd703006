package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaLnksharePojo;
import inks.service.sa.pms.domain.pojo.SaMarkdownPojo;
import inks.service.sa.pms.service.SaLnkshareService;
import inks.service.sa.pms.service.SaMarkdownService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * MarkDown(LNK简书) Sa_Markdown
 *
 * <AUTHOR>
 * @date 2023年03月06日 23:05
 */

@Api(tags = "S06M11B1:MarkDown(LNK简书)")
@RestController
@RequestMapping("/S06M11B1")
public class S06M11B1Controller extends SaMarkdownController {
    @Resource
    private SaMarkdownService saMarkdownService;
    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaLnkshareService saLnkshareService;

    /**
     * @return R<SaMarkdownPojo>
     * @Description 通过分享id获取MarkDown
     * <AUTHOR>
     * @param[1] key 分享id:Sa_LnkShare.id
     * @param[2] password 分享密码:Sa_LnkShare.password
     * @time 2023/5/12 10:38
     */
    @ApiOperation(value = "通过分享id获取MarkDown", notes = "获取详细信息", produces = "application/json")
    @RequestMapping(value = "/getMarkdownByShareId", method = RequestMethod.GET)
    public R<SaMarkdownPojo> getMarkdownByShareId(String key, String password) {
        try {
            SaLnksharePojo saLnksharePojo = saLnkshareService.getEntity(key);
            // 若saLnksharePojo不存在或者已经过期，则返回错误信息
            if (saLnksharePojo == null || saLnksharePojo.getDeadtime().before(new Date())) {
                return R.fail("【LNK简书】分享不存在或已过期");
            }
            // 查看saLnksharePojo是否设置密码,若设置了密码,则对比密码是否正确,若不正确，则返回错误信息
            if (saLnksharePojo.getPasswordmark() == 1) {
                if (!saLnksharePojo.getPassword().equals(password)) {
                    return R.fail("【LNK简书】分享密码错误");
                }
            }
            // 通过Lnkmarkdownid返回markdown信息
            return R.ok(this.saMarkdownService.getEntity(saLnksharePojo.getLnkmarkdownid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return R<PageInfo < SaMarkdownPojo>>
     * @Description 按条件分页查询项目下所有Markdown  传入项目id，分组id可传可不传
     * 如果没有传入分组id，则查询项目下所有分组的Markdown;如果传入了分组id，则查询该分组下的Markdown
     * <AUTHOR>
     * @param[1] json
     * @param[2] projectid
     * @param[3] groupid
     * @time 2023/5/11 10:19
     */
    @ApiOperation(value = "按条件分页通过项目id或分组id查所有Markdown", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getMdByProjectIdOrGroupId", method = RequestMethod.POST)
    public R<PageInfo<SaMarkdownPojo>> getMdByProjectId(@RequestBody String json, String projectid, String groupid) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Markdown.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            //如果没有传入分组id，则查询项目下所有分组的Markdown
            if (StringUtils.isBlank(groupid)) {
                qpfilter += " and Sa_MdGroup.id in(select id from Sa_MdGroup where Sa_MdGroup.MdProjectid ='" + projectid + "')";
            } else {
                //如果传入了分组id，则查询该分组下的Markdown
                qpfilter += " and Sa_MdGroup.id  ='" + groupid + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMarkdownService.getMdByProjectId(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}