package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaMdprojectPojo;
import inks.service.sa.pms.service.SaMdprojectService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * MD文档项目(Sa_MdProject)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-08 11:23:51
 */

@Api(tags = "S06M11B2:MarkDown项目")
@RestController
@RequestMapping("/S06M11B2")
public class S06M11B2Controller extends SaMdprojectController {

    @Resource
    private SaMdprojectService saMdprojectService;
    @Resource
    private SaRedisService saRedisService;

    /**
     * @return R<PageInfo < SaMdprojectPojo>>
     * @Description 按条件分页查询当前登录用户关联可视的MD项目
     * <AUTHOR>
     * @param[1] json
     * @time 2023/5/8 13:33
     */

    @ApiOperation(value = "按条件分页查询当前登录用户关联可视的MD项目", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByUser", method = RequestMethod.POST)
    public R<PageInfo<SaMdprojectPojo>> getPageListByUser(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_MdProject.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += " and (Sa_MdProject.PublicMark=1 or " +
                    "Sa_MdProject.id in (select MdProjectid from Sa_MdProjectUser where Userid = '" + loginUser.getUserid() + "'))";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMdprojectService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}