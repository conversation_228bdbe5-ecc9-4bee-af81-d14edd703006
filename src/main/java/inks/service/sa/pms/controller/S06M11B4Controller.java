package inks.service.sa.pms.controller;

import inks.service.sa.pms.service.SaMdprojectuserService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * (用户-MD项目关联表)可视权限(Sa_MdProjectUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-08 13:07:39
 */
@Api(tags = "S06M11B4:MD项目-用户关联")
@RestController
@RequestMapping("/S06M11B4")
public class S06M11B4Controller extends SaMdprojectuserController {

    @Resource
    private SaMdprojectuserService saMdprojectuserService;


    /**
     * @return R<SaMdprojectuserPojo>
     * @Description 查询登录用户的可视权限(MD项目)
     * <AUTHOR>
     * @param[1] key
     * @time 2023/5/8 13:11
     */
//    @ApiOperation(value = "查询当前登录用户的可视权限(MD项目)", notes = "", produces = "application/json")
//    @RequestMapping(value = "/getListBySelf", method = RequestMethod.GET)
//    public R<SaMdprojectuserPojo> getListBySelf(){
//
//    }
}