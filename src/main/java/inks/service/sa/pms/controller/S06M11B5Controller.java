package inks.service.sa.pms.controller;

import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.service.SaLnkshareService;
import io.swagger.annotations.Api;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Lnk简书分享表(Sa_LnkShare)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-12 10:29:08
 */
@Api(tags = "S06M11B5:Lnk简书分享")
@RestController
@RequestMapping("/S06M11B5")
public class S06M11B5Controller extends SaLnkshareController {

    @Resource
    private SaLnkshareService saLnkshareService;


    @Resource
    private SaRedisService saRedisService;


}