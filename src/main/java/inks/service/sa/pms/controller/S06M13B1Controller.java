package inks.service.sa.pms.controller;

import inks.common.core.domain.R;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaGoodsPojo;
import inks.service.sa.pms.mapper.SaGoodsMapper;
import inks.service.sa.pms.service.SaGoodsService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 产品(Sa_PmsGoods)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-18 09:18:55
 */
@Api(tags = "S06M13B1:产品信息")
@RestController
@RequestMapping("/S06M13B1")
public class S06M13B1Controller extends SaGoodsController {
    @Resource
    private SaGoodsService saGoodsService;
    @Resource
    private SaGoodsMapper saGoodsMapper;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取产品详细信息", notes = "获取产品详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByGoodsCode", method = RequestMethod.GET)
    public R<SaGoodsPojo> getEntityByGoodsCode(String goodscode) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saGoodsMapper.getEntityByGoodsCode(goodscode));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}