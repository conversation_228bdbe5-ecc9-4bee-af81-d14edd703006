package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.service.sa.pms.domain.pojo.SaGenvelocityPojo;
import inks.service.sa.pms.mapper.SaGengroupMapper;
import inks.service.sa.pms.mapper.SaGenvelocityMapper;
import inks.service.sa.pms.service.SaGenvelocityService;
import inks.service.sa.pms.utils.PrintColor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.*;
import java.util.*;

/**
 * 代码生成器模板(Sa_GenVelocity)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-02 13:14:50
 */
@Api(tags = "S06M16B1:代码生成器模板")
@RestController
@RequestMapping("/S06M16B1")
public class S06M16B1Controller extends SaGenvelocityController {
    @Resource
    private SaGenvelocityService saGenvelocityService;

    @Resource
    private SaGenvelocityMapper saGenvelocityMapper;
    @Resource
    private SaGengroupMapper saGengroupMapper;

    @Resource
    private S06M16B2Controller s06M16B2Controller;

    /**
     * @Description 在指定路径创建文件
     * <AUTHOR>
     * @param[1] filepath 路径 D:/nanno/UseToTest/test2.txt
     * @param[2] filecontent 文件内容
     * @time 2023/8/5 15:05
     */
    @GetMapping("/createFileInPath")
    public static void createFileInPath(String filepath, String filecontent) throws IOException {
        // 使用FilenameUtils来适应不同操作系统的路径写法
        String filePath = FilenameUtils.separatorsToSystem(filepath);
        File file = new File(filePath);
        // 创建一个文件输出流
        FileOutputStream fos = new FileOutputStream(file);
        // 将字节数组写入文件
        fos.write(filecontent.getBytes());
        // 关闭文件输出流
        fos.close();
    }

    public static void main(String[] args) {
        VelocityContext context; // Velocity上下文
        String templateContent;  // 模板内容
        // 准备测试数据，包含两个字段信息列表：data1 和 data2
        String jsonData = "{\n" +
                "  \"th\": [\n" +
                "    {\n" +
                "      \"fieldName\": \"id\",\n" +
                "      \"fieldComment\": \"ID字段\",\n" +
                "      \"fieldType\": \"INT\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"fieldName\": \"name\",\n" +
                "      \"fieldComment\": \"姓名\",\n" +
                "      \"fieldType\": \"VARCHAR\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"list\": [\n" +
                "    {\n" +
                "      \"fieldName\": \"id22\",\n" +
                "      \"fieldComment\": \"ID字段22\",\n" +
                "      \"fieldType\": \"INT\"\n" +
                "    },\n" +
                "    {\n" +
                "      \"fieldName\": \"name22\",\n" +
                "      \"fieldComment\": \"姓名22\",\n" +
                "      \"fieldType\": \"VARCHAR\"\n" +
                "    }\n" +
                "  ],\n" +
                "  \"header\": [\n" +
                "    {\n" +
                "      \"rowitem\": [\n" +
                "        {\n" +
                "          \"code\": \"billtype1.1\",\n" +
                "          \"name\": \"单据类型1.1\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"billtype1.2\",\n" +
                "          \"name\": \"单据类型1.2\"\n" +
                "        }\n" +
                "      ]\n" +
                "    },\n" +
                "    {\n" +
                "      \"rowitem\": [\n" +
                "        {\n" +
                "          \"code\": \"billtype2.1\",\n" +
                "          \"name\": \"单据类型2.1\"\n" +
                "        },\n" +
                "        {\n" +
                "          \"code\": \"billtype2.2\",\n" +
                "          \"name\": \"单据类型2.2\"\n" +
                "        }\n" +
                "      ]\n" +
                "    }\n" +
                "  ]\n" +
                "}";

        context = new VelocityContext();
        JSONObject jsonObject = JSON.parseObject(jsonData);
        Set<String> keyNames = jsonObject.keySet();
        for (String keyName : keyNames) {
            List<Map<String, Object>> dataList = jsonObject.getObject(keyName, new TypeReference<List<Map<String, Object>>>() {
            });
            context.put(keyName, dataList);
        }

        templateContent = "字段信息列表1：\n" +
                "#foreach($field in $th)\n" +
                "字段名：${field.fieldName}，字段注释：${field.fieldComment}，字段类型：${field.fieldType}\n" +
                "#end\n\n" +
                "字段信息列表2：\n" +
                "#foreach($field in $list)\n" +
                "字段名：${field.fieldName}，字段注释：${field.fieldComment}，字段类型：${field.fieldType}\n" +
                "#end\n\n" +
                "Header信息：\n" +
                "#foreach($row in $header)\n" +
                "#foreach($item in $row.rowitem)\n" +
                "代码：${item.code}，名称：${item.name}\n" +
                "#end\n" +
                "#end";

        // 使用Velocity引擎渲染模板
        VelocityEngine ve = new VelocityEngine();
        ve.init();
        StringWriter sw = new StringWriter();
        ve.evaluate(context, sw, "", templateContent);
        String resultVelocity = sw.toString();
        PrintColor.zi(resultVelocity);
    }

    @ApiOperation(value = "获取当前连接下所有数据库", notes = "获取当前连接下所有数据库", produces = "application/json")
    @RequestMapping(value = "/databases", method = RequestMethod.GET)
    public R<List<String>> databases() {
        try {
            return R.ok(this.saGenvelocityService.databases());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取当前数据库下所有数据表", notes = "获取当前数据库下所有数据表", produces = "application/json")
    @RequestMapping(value = "/table", method = RequestMethod.POST)
    public R<PageInfo<Map<String, Object>>> table(@RequestBody String json, String databases) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            return R.ok(this.saGenvelocityService.table(queryParam, databases));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取一个数据表所有字段信息", notes = "传入数据库和数据表的名字，返回该表所有字段以及字段对应的注释和类型", produces = "application/json")
    @RequestMapping(value = "/tableFields", method = RequestMethod.GET)
    public R<List<Map<String, String>>> tableFields(@RequestParam("databases") String databases, @RequestParam("tableName") String tableName) {
        try {
            List<Map<String, String>> fieldsInfo = this.saGenvelocityService.getTableFields(databases, tableName);
            return R.ok(fieldsInfo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * @return R<Map < String>>
     * @Description 预览代码
     * <AUTHOR>
     * @param[1] json 填充模板的json数据
     * @param[2] velocityids 传模板id,指定模板id预览 多个id用逗号隔开
     * @param[3] groupid  传分组id,预览该分组下所有模板id
     * @time 2023/8/10 13:05
     */
    @ApiOperation(value = "(批量预览代码)数据填入Velocity模板引擎,velocityids格式为11,22", notes = "数据填入Velocity模板引擎", produces = "application/json")
    @PostMapping("/previewCode")
    public R<Map<String, String>> previewCode(@RequestBody String json,
                                              @RequestParam(required = false) String velocityids,
                                              @RequestParam(required = false) String groupid) {
        try {
            // 如果groupid不为空，则查询出所有相关的groupid,再查关联的velocityids
            if (StringUtils.isNotBlank(groupid)) {
                List<String> allRelatedIds = s06M16B2Controller.findAllRelatedIds(groupid);
                List<String> velocityidList = saGengroupMapper.getVelocityids(allRelatedIds);
                velocityids = String.join(",", velocityidList);
            }
            Map<String, String> resultMap = new LinkedHashMap<>();
            VelocityContext velocityContext = new VelocityContext();

            JSONObject jsonObject = JSON.parseObject(json);
//            Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String key = entry.getKey();
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                velocityContext.put(key, dataList);
            }
//            List<SaGenvelocityPojo> saGenvelocityPojoList = saGenvelocityMapper.getListInName("('test1','test2')");
            List<String> velocityIdsList = Arrays.asList(velocityids.split(","));
            List<SaGenvelocityPojo> saGenvelocityPojoList = saGenvelocityMapper.getListInName(velocityIdsList);
            // 使用Velocity引擎渲染模板,StringWriter缓冲区
            VelocityEngine ve = new VelocityEngine();
            StringWriter sw = new StringWriter();
            for (SaGenvelocityPojo saGenvelocityPojo : saGenvelocityPojoList) {
                // 读取模板内容
                String velocityTemplate = saGenvelocityPojo.getVelocity();
                // 每次循环都要初始化引擎渲染模板,StringWriter对象的缓冲区重置
                ve.init();
                sw.getBuffer().setLength(0);
                // Velocity引擎渲染模板
                ve.evaluate(velocityContext, sw, "", velocityTemplate);
                String resultVelocity = sw.toString(); // 渲染后的模板保存sw
                PrintColor.zi(resultVelocity);
                // 模板名和对应生成的代码 保存到resultMap中
                resultMap.put(saGenvelocityPojo.getName(), resultVelocity);
            }
            return R.ok(resultMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "(下载代码到指定路径)数据填入Velocity模板引擎,velocitynames格式为11,22", notes = "数据填入Velocity模板引擎", produces = "application/json")
    @PostMapping("/downloadCode")
    public R<Map<String, String>> downloadCode(@RequestBody String json, String velocityids, String filepath) {
        try {
            Map<String, String> resultMap = new LinkedHashMap<>();
            VelocityContext velocityContext = new VelocityContext();

            JSONObject jsonObject = JSON.parseObject(json);
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String key = entry.getKey();
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                velocityContext.put(key, dataList);
            }
            List<String> velocityIdsList = Arrays.asList(velocityids.split(","));
            List<SaGenvelocityPojo> saGenvelocityPojoList = saGenvelocityMapper.getListInName(velocityIdsList);
            // 使用Velocity引擎渲染模板,StringWriter缓冲区
            VelocityEngine ve = new VelocityEngine();
            StringWriter sw = new StringWriter();
            for (SaGenvelocityPojo saGenvelocityPojo : saGenvelocityPojoList) {
                // 读取模板内容
                String velocityTemplate = saGenvelocityPojo.getVelocity();
                // 每次循环都要初始化引擎渲染模板,StringWriter对象的缓冲区重置
                ve.init();
                sw.getBuffer().setLength(0);
                // Velocity引擎渲染模板 保存到sw
                ve.evaluate(velocityContext, sw, "", velocityTemplate);
                String resultVelocity = sw.toString(); // [最终渲染后的模板保存到resultVelocity]
                PrintColor.zi(resultVelocity);
                // 模板名和对应生成的代码 保存到resultMap中
                resultMap.put(saGenvelocityPojo.getName(), resultVelocity);
                // 在指定路径创建文件
                String resultFilepath = filepath + File.separator + saGenvelocityPojo.getName() + ".txt";
                createFileInPath(resultFilepath, resultVelocity);
            }
            return R.ok(resultMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "仅预览一个代码并上传minio,返回上传后的url(文件名为表名+模板名)", notes = "数据填入Velocity模板引擎", produces = "application/json")
    @PostMapping("/previewOneCodeMinio")
    public String previewOneCodeMinio(@RequestBody String json, String velocityid, String tablename) {
        try {
            VelocityContext velocityContext = new VelocityContext();

            JSONObject jsonObject = JSON.parseObject(json);
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String key = entry.getKey();
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                velocityContext.put(key, dataList);
            }
            SaGenvelocityPojo velocityPojo = saGenvelocityMapper.getEntity(velocityid);
            // 使用Velocity引擎渲染模板,StringWriter缓冲区
            VelocityEngine ve = new VelocityEngine();
            StringWriter sw = new StringWriter();
            // 读取模板内容
            String velocityTemplate = velocityPojo.getVelocity();
            // 每次循环都要初始化引擎渲染模板,StringWriter对象的缓冲区重置
            ve.init();
            sw.getBuffer().setLength(0);
            // Velocity引擎渲染模板 保存在sw
            ve.evaluate(velocityContext, sw, "", velocityTemplate);
            String resultVelocity = sw.toString(); // [最终渲染后的模板保存到resultVelocity]
            PrintColor.zi("开始minio上传,上传到inkspms/CodeGenerator下.字符串长度:" + resultVelocity.length() + "\n内容为:" + resultVelocity);
            InputStream inputStream = new ByteArrayInputStream(resultVelocity.getBytes());
            // 上传到minio返回的url    文件名为表名+模板名
            //String returnFileUrl = ossService.uploadInputStream(inputStream, "CodeGenerator", tablename + "_" + velocityPojo.getName());
            //return returnFileUrl;
            return null;
        } catch (Exception e) {
            return null;
        }
    }


//    -----------------------------------------------以下用于测试,待删除-----------------------------------------------

    @ApiOperation(value = "仅预览一个代码(本地),返回生成代码的内容", notes = "数据填入Velocity模板引擎", produces = "application/json")
    @PostMapping("/previewOneCodeLocal")
    public String previewOneCodeLocal(@RequestBody String json, String velocityid) {
        try {
            VelocityContext velocityContext = new VelocityContext();

            JSONObject jsonObject = JSON.parseObject(json);
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String key = entry.getKey();
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                velocityContext.put(key, dataList);
            }
            SaGenvelocityPojo velocityPojo = saGenvelocityMapper.getEntity(velocityid);
            // 使用Velocity引擎渲染模板,StringWriter缓冲区
            VelocityEngine ve = new VelocityEngine();
            StringWriter sw = new StringWriter();
            // 读取模板内容
            String velocityTemplate = velocityPojo.getVelocity();
            // 每次循环都要初始化引擎渲染模板,StringWriter对象的缓冲区重置
            ve.init();
            sw.getBuffer().setLength(0);
            // Velocity引擎渲染模板 保存在sw
            ve.evaluate(velocityContext, sw, "", velocityTemplate);
            String resultVelocity = sw.toString(); // [最终渲染后的模板保存到resultVelocity]
            PrintColor.zi("本地转化代码.字符串长度:" + resultVelocity.length() + "\n内容为:" + resultVelocity);
            return resultVelocity;
        } catch (Exception e) {
            return null;
        }
    }

    @ApiOperation(value = "数据填入Velocity模板引擎", notes = "数据填入Velocity模板引擎", produces = "application/json")
    @PostMapping("/intoVelocityTest")
    public R<String> intoVelocityTest(@RequestBody String json) {
        try {
            VelocityContext velocityContext = new VelocityContext();

            JSONObject jsonObject = JSON.parseObject(json);
//            Set<Map.Entry<String, Object>> entries = jsonObject.entrySet();
            for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
                String key = entry.getKey();
                List<Map<String, Object>> dataList = (List<Map<String, Object>>) entry.getValue();
                velocityContext.put(key, dataList);
            }

            String templateContent = "Th信息列表：\n" +
                    "#foreach($field in $th)\n" +
                    "字段名：${field.fieldName}，字段注释：${field.fieldComment}，字段类型：${field.fieldType}\n" +
                    "#end\n\n" +
                    "List信息列表：\n" +
                    "#foreach($field in $list)\n" +
                    "字段名：${field.fieldName}，字段注释：${field.fieldComment}，字段类型：${field.fieldType}\n" +
                    "#end\n\n" +
                    "Header信息：\n" +
                    "#foreach($row in $header)\n" +
                    "#foreach($item in $row.rowitem)\n" +
                    "代码：${item.code}，名称：${item.name}\n" +
                    "#end\n" +
                    "#end";

            // 使用Velocity引擎渲染模板
            VelocityEngine ve = new VelocityEngine();
            ve.init();
            StringWriter sw = new StringWriter();
            ve.evaluate(velocityContext, sw, "", templateContent);
            String resultVelocity = sw.toString();
            PrintColor.zi(resultVelocity);

            return R.ok(resultVelocity);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}