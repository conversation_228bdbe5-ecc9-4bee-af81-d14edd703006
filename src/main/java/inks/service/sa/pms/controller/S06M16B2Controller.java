package inks.service.sa.pms.controller;

import inks.service.sa.pms.domain.pojo.SaGengroupPojo;
import inks.service.sa.pms.mapper.SaGengroupMapper;
import inks.service.sa.pms.utils.PrintColor;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Random;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 代码生成器分组(Sa_GenGroup)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-05 15:46:02
 */

@Api(tags = "S06M16B2:代码生成器模板分组")
@RestController
@RequestMapping("/S06M16B2")
public class S06M16B2Controller extends SaGengroupController {

    @Resource
    private SaGengroupMapper saGengroupMapper;
    @Resource
    private S06M16B1Controller s06M16B1Controller;

    private static SaGengroupPojo findNode(String targetId, List<SaGengroupPojo> nodes) {
        for (SaGengroupPojo node : nodes) {
            if (targetId.equals(node.getId())) {
                return node;
            }
        }
        return null;
    }

    /**
     * @return SaGengroupPojo
     * @Description 构建树状结构  (注意现在只用了这几个字段(node.getId(), node.getParentid(), node.getGroupname(), node.getVelocityid(), node.getDirmark()))
     * <AUTHOR>
     * @param[1] node
     * @param[2] nodes
     * @time 2023/8/8 12:42
     */
    private static SaGengroupPojo buildTree(SaGengroupPojo node, List<SaGengroupPojo> nodes) {
//        SaGengroupPojo copyNode = node; 用全部字段
        SaGengroupPojo copyNode = new SaGengroupPojo(node.getId(), node.getParentid(), node.getGroupname(), node.getVelocityid(), node.getDirmark());
        copyNode.setChildren(new ArrayList<>());// 初始化子节点避免空指针
        for (SaGengroupPojo child : nodes) {
            if (node.getId().equals(child.getParentid())) {
                SaGengroupPojo childNode = buildTree(child, nodes);
                copyNode.getChildren().add(childNode);
            }
        }
        return copyNode;
    }

    /**
     * @Description 代码生成到指定路径: 从树状结构中结合模板生成代码,上传minio替换fileUrl,最终在指定路径创建文件夹/文件
     * <AUTHOR>
     * @param[1] json 填充进模板的json数据
     * @param[2] groupid  分组id
     * @param[3] tablename  表名
     * @param[4] filepath  生成文件的根路径
     * @time 2023/8/8 11:28
     */
    @ApiOperation(value = "代码生成到指定路径: 从树状结构中生成代码,上传minio,指定路径创建文件夹/文件", notes = "数据填入Velocity模板引擎", produces = "application/json")
    @PostMapping("/downloadCode")
    public String downloadCode(@RequestBody String json, String groupid, String tablename, String filepath) throws IOException {
        try {
            // 1.获取分组树状结(拿到顶级父节点id向下递归)
            List<SaGengroupPojo> allGroupList = saGengroupMapper.getAllList();
            String topParentId = findTopParentId(groupid, allGroupList);
            SaGengroupPojo fileTree = toFileTree(topParentId, allGroupList);
            // 2.树状结构递归,如果有模板id,则中生成代码,上传minio,最终替换fileUrl的值
            replaceFileUrl(fileTree, json, tablename);
            PrintColor.red("替换fileUrl后的树状结构为：\n" + fileTree);
            // 3.根据树状结构和本地路径生成文件/文件夹
            createFilesFromTree(fileTree, filepath);
            return "已生成代码,本地路径为：" + filepath + "\n替换fileUrl后的树状结构为：\n" + fileTree;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    @ApiOperation(value = "本地版本的downloadCode,不上传minio", notes = "数据填入Velocity模板引擎", produces = "application/json")
    @PostMapping("/downloadCodeLocal")
    public String downloadCodeLocal(@RequestBody String json, String groupid, String tablename, String filepath) throws IOException {
        try {
            // 1.获取分组树状结(拿到顶级父节点id向下递归)
            List<SaGengroupPojo> allGroupList = saGengroupMapper.getAllList();
            String topParentId = findTopParentId(groupid, allGroupList);
            SaGengroupPojo fileTree = toFileTree(topParentId, allGroupList);

            // 2.树状结构递归,如果有模板id,则中生成代码并根据树状结构和本地路径生成文件/文件夹
            createFilesFromTreeLocal(fileTree, filepath, json);
            PrintColor.red("初始树状结构为：\n" + fileTree);
            return "已生成代码,本地路径为：" + filepath;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 文件树 以传入id为根节点，构建文件树  (所以如果想从顶级父节点开始构建，需要先找到顶级父节点id再传入)
     *
     * @param targetId 目标id
     * @param nodes    节点
     * @return {@link SaGengroupPojo}
     */
    @ApiOperation(value = "以传入分组id为根节点，返回向下构建的文件树", notes = "", produces = "application/json")
    @GetMapping("/findFileTree")
    public SaGengroupPojo toFileTree(String targetId, @RequestParam(required = false) List<SaGengroupPojo> nodes) {
        if (CollectionUtils.isEmpty(nodes)) { // 如果节点列表没传入,则从数据库查找
            nodes = saGengroupMapper.getAllList();
        }
        // 找到目标节点
        SaGengroupPojo targetNode = findNode(targetId, nodes);
        if (targetNode == null) {
            return null;
        }

        // 构建树状结构
        return buildTree(targetNode, nodes);
    }

    //递归每一层(如果该层有Velocityid模板id,则结合json数据填充模板,生成代码文件,并上传minio,再填充fileUrl字段)
    public void replaceFileUrl(SaGengroupPojo node, String dataJson, String tablename) {
        // 查找每一层的velocityid是否有值，如果有则替换fileurl  DirMark=0是文件且有绑定Velocityid才会生成代码
        if (isNotBlank(node.getVelocityid()) && Objects.equals(node.getDirmark(), 0)) {
            String returnFileUrl = s06M16B1Controller.previewOneCodeMinio(dataJson, node.getVelocityid(), tablename);
            node.setFileurl(returnFileUrl);
        }
        // 递归处理子节点
        if (node.getChildren() != null) {
            for (SaGengroupPojo child : node.getChildren()) {
                replaceFileUrl(child, dataJson, tablename);
            }
        }
    }


    /**
     * 传入任一id，返回顶级父id
     * Parentid为0的就是顶级父id
     *
     * @param nodeId 目标id
     * @param nodes  文件树节点列表
     * @return 顶级父id
     */
    @ApiOperation(value = "传入一个分组id,返回[该id]的顶级id", notes = "", produces = "application/json")
    @GetMapping("/findTopParentId")
    public String findTopParentId(@RequestParam String nodeId, @RequestParam(required = false) List<SaGengroupPojo> nodes
    ) {
        if (CollectionUtils.isEmpty(nodes)) { // 如果节点列表没传入,则从数据库查找
            nodes = saGengroupMapper.getAllList();
        }
        SaGengroupPojo targetNode = findNode(nodeId, nodes);
        if (targetNode == null) {
            return null;
        }
        // 递归查找直到顶级父id "0"
        while (!"0".equals(targetNode.getParentid())) {
            targetNode = findNode(targetNode.getParentid(), nodes);
        }
        return targetNode.getId();
    }


    //    -----------------------------------------创建文件树的文件夹/文件-----------------------------------------

    /**
     * 创建文件树的文件夹/文件   (该层有fileUrl的读取minio写入文件)
     *
     * @param node     节点
     * @param basePath 基本路径
     * @throws IOException ioexception
     */
    public void createFilesFromTree(SaGengroupPojo node, String basePath) throws IOException {
        if (node.getDirmark() == null) {
            // 若dirmark属性为null，则忽略该节点，并递归处理子节点
            if (node.getChildren() != null) {
                for (SaGengroupPojo child : node.getChildren()) {
                    createFilesFromTree(child, basePath);
                }
            }
            return;
        }
        // 构建当前节点的完整路径
        String fullPath = FilenameUtils.separatorsToSystem(basePath + File.separator + node.getGroupname());

        if (node.getDirmark() == 1) {
            // 创建文件夹
            File dir = new File(fullPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
        } else if (node.getDirmark() == 0) {
            // 创建文件
            File file = new File(fullPath);
            FileOutputStream fos = new FileOutputStream(file);
            // 有fileurl则从minio下载文件
            if (isNotBlank(node.getFileurl())) {
                //InputStream inputStream = fileInfoMinioService.downloadByUrl(node.getFileurl());
                // 使用 IOUtils.copy 方法将 InputStream 内容复制到 FileOutputStream 中
                //IOUtils.copy(inputStream, fos);
                //inputStream.close(); // 关闭 InputStream
            } else {
                String filecontent = "minio无数据:S06M16B2Controller/createFilesFromTree方法" + new Random().nextInt(10000);
                fos.write(filecontent.getBytes());
            }
            fos.close();
        }

        // 递归处理子节点
        if (node.getChildren() != null) {
            for (SaGengroupPojo child : node.getChildren()) {
                // 递归调用本方法处理子节点，此处basePath更新为当前节点的完整路径
                createFilesFromTree(child, fullPath);
            }
        }
    }

    // 本地版本的createFilesFromTree()
    public void createFilesFromTreeLocal(SaGengroupPojo node, String basePath, String json) throws IOException {
        if (node.getDirmark() == null) {
            // 若dirmark属性为null，则忽略该节点，并递归处理子节点
            if (node.getChildren() != null) {
                for (SaGengroupPojo child : node.getChildren()) {
                    createFilesFromTreeLocal(child, basePath, json);
                }
            }
            return;
        }
        // 构建当前节点的完整路径
        String fullPath = FilenameUtils.separatorsToSystem(basePath + File.separator + node.getGroupname());
        String resultVelocity = "";
        if (node.getDirmark() == 1) {
            // 创建文件夹
            File dir = new File(fullPath);
            if (!dir.exists()) {
                dir.mkdirs();
            }
        } else if (node.getDirmark() == 0) {
            // 创建文件
            File file = new File(fullPath);
            FileOutputStream fos = new FileOutputStream(file);
            // 有Velocityid则生成模板
            if (isNotBlank(node.getVelocityid())) {
                resultVelocity = s06M16B1Controller.previewOneCodeLocal(json, node.getVelocityid());
            } else {
                resultVelocity = "本地无数据:S06M16B2Controller/createFilesFromTreeLocal方法" + new Random().nextInt(10000);
            }
            fos.write(resultVelocity.getBytes());
            fos.close();
        }
        // 递归处理子节点
        if (node.getChildren() != null) {
            for (SaGengroupPojo child : node.getChildren()) {
                // 递归调用本方法处理子节点，此处basePath更新为当前节点的完整路径
                createFilesFromTreeLocal(child, fullPath, json);
            }
        }
    }


    /**
     * 传入一个分组id,返回向下的所有子级id
     *
     * @param targetId 目标id
     * @return 关联父子级的所有id列表
     */
    @ApiOperation(value = "传入一个分组id,返回[该id向下的]所有子级id", notes = "", produces = "application/json")
    @GetMapping("/findAllRelatedIds")
    public List<String> findAllRelatedIds(String targetId) {
        // 获取所有分组
        List<SaGengroupPojo> nodes = saGengroupMapper.getAllList();
        List<String> relatedIds = new ArrayList<>();
        collectRelatedIds(targetId, nodes, relatedIds);
        return relatedIds;
    }

    private void collectRelatedIds(String nodeId, List<SaGengroupPojo> nodes, List<String> relatedIds) {
        SaGengroupPojo targetNode = findNode(nodeId, nodes);
        if (targetNode == null) {
            return;
        }
        relatedIds.add(targetNode.getId());
        for (SaGengroupPojo child : nodes) {
            if (targetNode.getId().equals(child.getParentid())) {
                collectRelatedIds(child.getId(), nodes, relatedIds);
            }
        }
    }

    //    // 获取传入id的顶级父id下的所有子节点树状结构
//    public static void main(String[] args) throws IOException {
//        // 假设有一组文件树节点数据如下
//        List<SaGengroupPojo> nodes = new ArrayList<>();
//        nodes.add(new SaGengroupPojo("11", "0", "双表List", null, null));
//        nodes.add(new SaGengroupPojo("12", "11", "cOmmon", null, 1));
//        nodes.add(new SaGengroupPojo("13", "12", "edit.vue", "1", 0));
//        nodes.add(new SaGengroupPojo("14", "12", "Edit2.vue", "1", 0));
//        nodes.add(new SaGengroupPojo("15", "11", "cRud.js", "2", 0));
//
//        String targetId = "15"; // 查找
//        String topParentId = findTopParentId(targetId, nodes);
//        PrintColor.zi("顶级父id为：" + topParentId);
//        SaGengroupPojo fileTree = toFileTree(topParentId, nodes);
//        //打印分组的树状结构fileTree
//        PrintColor.zi("树状结构为：" + fileTree.toString());
//
//        createFilesFromTree(fileTree, "D:/nanno/UseToTest");
//        String fileContent = "============测试文件内容=========" + new Random().nextInt(10000);
//        InputStream inputStream = new ByteArrayInputStream(fileContent.getBytes());
//
////        ossServiceImpl.uploadInputStream(inputStream,"CodeGenerator","test.txt");
//
//    }

}