package inks.service.sa.pms.controller;

import com.zaxxer.hikari.HikariDataSource;
import inks.common.core.domain.R;
import inks.service.sa.pms.domain.pojo.SaDatabasePojo;
import inks.service.sa.pms.mapper.SaDatabaseMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.jdbc.core.namedparam.MapSqlParameterSource;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.sql.DataSource;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 数据库连接池(Sa_Database)表控制层
 *
 * <AUTHOR>
 * @since 2024-04-05 15:24:15
 */

@Api(tags = "S06M16B3:数据库连接地址")
@RestController
@RequestMapping("/S06M16B3")
public class S06M16B3Controller extends SaDatabaseController {
    @Resource
    private SaDatabaseMapper saDatabaseMapper;

    //通过Sa_Database.id获取数据库连接dataSource和数据库名字
    public Map<String, Object> getDataSource(String id) {
        SaDatabasePojo databasePojo = saDatabaseMapper.getEntity(id);
        HikariDataSource dataSource = new HikariDataSource();
        String url = databasePojo.getUrl();
        dataSource.setJdbcUrl(url);
        dataSource.setUsername(databasePojo.getUsername());
        dataSource.setPassword(databasePojo.getPassword());
        dataSource.setDriverClassName("com.mysql.cj.jdbc.Driver");//默认mysql 驱动
        if (StringUtils.isNotBlank(databasePojo.getDriverclassname())) {
            dataSource.setDriverClassName(databasePojo.getDriverclassname());
        }
        // 获取数据库名字 格式为*****************************************
        int lastIndex = url.lastIndexOf("/");
        String databaseName = url.substring(lastIndex + 1);
        HashMap<String, Object> map = new HashMap<>();
        map.put("dataSource", dataSource);
        map.put("databaseName", databaseName);
        return map;
    }


    @ApiOperation(value = " 获取Url数据库下所有数据表 databaseid:数据库连接id", notes = "", produces = "application/json")
    @RequestMapping(value = "/table", method = RequestMethod.GET)
    public List<Map<String, Object>> table(String databaseid, @RequestParam(required = false) String tableName, @RequestParam(required = false) Integer searchType) {
        // 获取数据库连接和数据库名字
        Map<String, Object> dataSourceAndName = getDataSource(databaseid);
        DataSource dataSource = (DataSource) dataSourceAndName.get("dataSource");
        String databaseName = (String) dataSourceAndName.get("databaseName");
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);
        // 构建查询参数
        MapSqlParameterSource parameters = new MapSqlParameterSource();
        parameters.addValue("database", databaseName);
        if (tableName != null) {
            parameters.addValue("tablename", "%" + tableName.toLowerCase() + "%");
        }
        // 构建查询语句
        String sql = "SELECT distinct t1.table_name AS tablename, " +
                "CASE WHEN t2.table_name IS NULL THEN NULL " +
                "ELSE CONCAT(t2.table_name) END AS tableitemname, " +
                "t1.table_comment AS tablecomment, " +
                "t1.create_time AS createtime, " +
                "t1.update_time AS updatetime " +
                "FROM information_schema.TABLES t1 " +
                "LEFT JOIN information_schema.TABLES t2 ON CONCAT(t1.table_name, 'Item') = t2.table_name " +
                "WHERE t1.table_schema = :database " +
                "AND t1.table_name NOT LIKE '%Item' ";
        if (tableName != null) {
            if (searchType == null || searchType == 0) {
                sql += "AND LOWER(t1.table_name) LIKE :tablename ";
            } else if (searchType == 1) {
                sql += "AND (LOWER(t1.table_name) LIKE :tablename) ";
            }
        }
        // 执行查询并返回结果
        return jdbcTemplate.queryForList(sql, parameters);
    }

    @ApiOperation(value = "获取一个数据表所有字段信息 传入数据库连接id和数据表的名字，返回该表所有字段以及字段对应的注释和类型", notes = "", produces = "application/json")
    @RequestMapping(value = "/tableFields", method = RequestMethod.GET)
    public R<List<Map<String, Object>>> tableFields(String databaseid, String tableName) {
        // 获取数据库连接和数据库名字
        Map<String, Object> dataSourceAndName = getDataSource(databaseid);
        DataSource dataSource = (DataSource) dataSourceAndName.get("dataSource");
        String databaseName = (String) dataSourceAndName.get("databaseName");
        NamedParameterJdbcTemplate jdbcTemplate = new NamedParameterJdbcTemplate(dataSource);

        String sql = "SELECT column_name AS fieldName, column_comment AS fieldComment, column_type AS fieldType " +
                "FROM information_schema.COLUMNS WHERE table_schema = :database AND table_name = :tableName";

        MapSqlParameterSource params = new MapSqlParameterSource();
        params.addValue("database", databaseName);
        params.addValue("tableName", tableName);
        List<Map<String, Object>> result = jdbcTemplate.queryForList(sql, params);
        // 处理查询结果，将字段类型转换为中文
        return R.ok(result);
    }
}