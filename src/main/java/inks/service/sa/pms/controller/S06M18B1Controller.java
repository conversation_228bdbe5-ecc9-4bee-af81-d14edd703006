package inks.service.sa.pms.controller;


import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaIntroPojo;
import inks.service.sa.pms.service.SaIntroService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 功能简介(Sa_Intro)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-24 08:56:13
 */
@Api(tags = "S06M18B1:功能简介")
@RestController
@RequestMapping("/S06M18B1")
public class S06M18B1Controller extends SaIntroController {
    @Resource
    private SaIntroService saIntroService;

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取功能简介详细信息By功能code", notes = "获取功能简介详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByCode", method = RequestMethod.GET)
    public R<SaIntroPojo> getEntityByCode(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saIntroService.getEntityByCode(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}