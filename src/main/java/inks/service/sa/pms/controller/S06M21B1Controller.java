package inks.service.sa.pms.controller;


import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.StringUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.LoginBody;
import inks.service.sa.pms.domain.pojo.SaScmjustauthPojo;
import inks.service.sa.pms.domain.pojo.SaScmuserPojo;
import inks.service.sa.pms.service.PmsSaUserService;
import inks.service.sa.pms.service.SaScmjustauthService;
import inks.service.sa.pms.service.SaScmuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * SCM用户(Sa_ScmUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:31
 */
@Api(tags = "S06M21B1:SCM用户")
@RestController
@RequestMapping("/S06M21B1")
public class S06M21B1Controller extends SaScmuserController {
    private final String SCANLOGIN_CODE = "pmsscm_scanlogin_code:";
    @Resource
    private SaScmuserService saScmuserService;
    @Resource
    private SaScmjustauthService saScmjustauthService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private PmsSaUserService pmsSaUserService;

    @Value("${inks.justauth.api}")
    private String api;

    /**
     * 通过（手机号,邮箱）查询单条数据UserName只能是手机号或者邮箱
     *
     * @param username 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取SCM用户详细信息ByUserName,通过（手机号,邮箱）查询单条数据UserName只能是手机号或者邮箱", notes = "获取SCM用户详细信息ByUserName", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserName", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiScmUser.List")
    public R<SaScmuserPojo> getEntityByUserName(String username) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saScmuserService.getEntityByUserName(username));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询该客户下所有用户", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByCustomer", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiScmUser.List")
    public R<List<SaScmuserPojo>> getPageListByCustomer(String groupid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saScmuserService.getPageListByCustomer(groupid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 接受pms服务传来的openid,key为redis中的key
     */
    @ApiOperation(value = "绑定openid", notes = "绑定openid", produces = "application/json")
    @RequestMapping(value = "/scanWebScmBind", method = RequestMethod.GET)
    public R<String> scanWebScmBind(String openid, String key, String userid, String tenantid, HttpServletRequest request) throws ParseException {
//        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //判断当前租户下该用户openid是否已绑定
        SaScmuserPojo piscmuserPojo = saScmuserService.getEntityByOpenid(openid);
        if (piscmuserPojo != null) {
            return R.ok("用户已绑定openid,若要换绑请先解绑");
        }
        //未绑定：绑定userid和openid(插入Pi_ScmAuthJust)
        SaScmjustauthPojo saScmjustauthPojo = new SaScmjustauthPojo();
        saScmjustauthPojo.setUserid(userid);
        saScmjustauthPojo.setAuthtype("openid");
        saScmjustauthPojo.setAuthuuid(openid);
        saScmjustauthPojo.setCreatebyid(userid);  // 创建者id
        saScmjustauthPojo.setCreatedate(new Date());   // 创建时间
//        saScmjustauthPojo.setLister(loginUser.getRealname());   // 制表
        saScmjustauthPojo.setListerid(userid);    // 制表id
        saScmjustauthPojo.setModifydate(new Date());   //修改时间
//        saScmjustauthPojo.setTenantid(tenantid);   //租户id
        saScmjustauthService.insert(saScmjustauthPojo);
        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "扫码绑定openid成功");
        this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
        return R.ok("扫码绑定openid成功");
    }

    @ApiOperation(value = "解绑openid", notes = "解绑openid", produces = "application/json")
    @RequestMapping(value = "/scanWebScmUnBind", method = RequestMethod.GET)
    public R<String> scanWebScmUnBind(String openid, String key, String userid, String tenantid, HttpServletRequest request) throws ParseException {
//        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //判断当前租户下该用户openid是否已绑定
        SaScmuserPojo piscmuserPojo = saScmuserService.getEntityByOpenid(openid);
        if (piscmuserPojo == null) {
            return R.ok("用户未绑定openid,无需解绑");
        }
        //已绑定：解绑userid和openid(删除Pi_ScmAuthJust)
        saScmjustauthService.deleteByOpenid(openid);
        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "扫码解绑openid成功");
        this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
        return R.ok("扫码解绑openid成功");
    }

    /**
     * @return R<PiscmuserPojo>
     * @Description 初始化密码
     * <AUTHOR>
     * @param[1] keys是userid  初始化为123456
     * @time 2023/4/24 12:46
     */
    @ApiOperation(value = "初始化密码", notes = "初始化密码", produces = "application/json")
    @RequestMapping(value = "/initPassword", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "PiScmUser.Edit")
    public R<SaScmuserPojo> initPassword(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaScmuserPojo saScmuserPojo = new SaScmuserPojo();
            saScmuserPojo.setUserid(key);
            saScmuserPojo.setLister(loginUser.getRealname());   // 制表
            saScmuserPojo.setListerid(loginUser.getUserid());    // 制表id
//            saScmuserPojo.setTenantid(loginUser.getTenantid());   //租户id
            saScmuserPojo.setModifydate(new Date());   //修改时间
            //加密密码
            saScmuserPojo.setUserpassword(AESUtil.Encrypt("123456"));
            return R.ok(this.saScmuserService.update(saScmuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //    ---------------------------------------Scm用户登陆-------------------------------------------
    @ApiOperation(value = "scm用户登陆", notes = "用户登陆", produces = "application/json")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public R<Object> login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        try {
            LoginUser loginUser = saScmuserService.login(loginBody.getUserName(), loginBody.getPassword(), request);
//            Map<String, Object> map = saRedisService.createToken(l);
            MyLoginUser myLoginUser = new MyLoginUser();
            BeanUtils.copyBeanProp(loginUser, myLoginUser);
            Map<String, Object> map = pmsSaUserService.myCreateToken(myLoginUser, request);
            return R.ok(map.get("loginuser"));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //------------------------------------扫码登录(WX小程序)---------------------------------
//    private final String SCANLOGIN_CODE = "scanlogin_code:";//扫码登录

    @DeleteMapping("logout")
    public R logout(HttpServletRequest request) throws ParseException {
        LoginUser loginUser = saRedisService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            String username = loginUser.getUsername();
            // 删除用户缓存记录
            saRedisService.deleteObject(loginUser.getToken());
        }
        return R.ok("成功删除用户Token");
    }

    @ApiOperation(value = "获取绑定openid的二维码String", notes = "", produces = "application/json")
    @RequestMapping(value = "/getScanBindCode", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanBindCode() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成key 即UUID
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            //code100存入redis
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
            //返回前端type,date(key)
            Map<String, Object> map = new HashMap<>();
            map.put("type", "sascmbind");
            Map<String, Object> dataMap = new HashMap<>();
//            dataMap.put("key", uuid);
            dataMap.put("api", api);
            dataMap.put("userid", loginUser.getUserid());
            dataMap.put("tenantid", loginUser.getTenantid());
            map.put("data", dataMap);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取解绑openid的二维码String", notes = "", produces = "application/json")
    @RequestMapping(value = "/getScanUnBindCode", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanUnBindCode() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成key 即UUID
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            //code100存入redis
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
            //返回前端type,date(key)
            Map<String, Object> map = new HashMap<>();
            map.put("type", "sascmunbind");
            Map<String, Object> dataMap = new HashMap<>();
//            dataMap.put("key", uuid);
            dataMap.put("api", api);
            dataMap.put("userid", loginUser.getUserid());
            dataMap.put("tenantid", loginUser.getTenantid());
            map.put("data", dataMap);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取登录二维码String", notes = "获取登录二维码String", produces = "application/json")
    @RequestMapping(value = "/getScanLoginCode", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanLoginCode() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成key 即UUID
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            //code100存入redis
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
            //返回前端type,date(key)
            Map<String, Object> map = new HashMap<>();
            map.put("type", "webscmlogin");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("key", uuid);
            dataMap.put("api", api);
            map.put("data", dataMap);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 接受scm服务传来的openid,key为redis中的key
     */
    @ApiOperation(value = "扫码登录", notes = "扫码登录", produces = "application/json")
    @RequestMapping(value = "/scanLoginCode", method = RequestMethod.GET)
    public R<String> scanLoginCode(String openid, String key, HttpServletRequest request) throws ParseException {
        Map<String, Object> tokenMap = null;
        LoginUser loginUser = saScmuserService.scanLogin(openid, key, request);
//            Map<String, Object> map = saRedisService.createToken(l);
        MyLoginUser myLoginUser = new MyLoginUser();
        BeanUtils.copyBeanProp(loginUser, myLoginUser);
        Map<String, Object> map = pmsSaUserService.myCreateToken(myLoginUser, request);        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "登录成功");
        missionMsg.put("token", tokenMap);
        this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
//        saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
        return R.ok("扫码登录成功");
    }

    //    @ApiOperation(value = "scm用户登陆", notes = "用户登陆", produces = "application/json")
//    @RequestMapping(value = "/login222", method = RequestMethod.POST)
//    public R<Object> login222(@RequestBody LoginBody loginBody, HttpServletRequest request) {
//        try {
//            LoginUser l = service.login(loginBody.getUserName(), loginBody.getPassword(), request);
//            Map<String, Object> map = saRedisService.createToken(l);
//            return R.ok(map.get("loginuser"));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
    @ApiOperation(value = "获取扫码登录状态", notes = "获取扫码登录状态", produces = "application/json")
    @RequestMapping(value = "/getScanLoginState", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanLoginState(@RequestParam String key) {
        Map<String, Object> scanLoginState = this.saRedisService.getCacheMapValue(SCANLOGIN_CODE, key);
        return R.ok(scanLoginState);
    }

}