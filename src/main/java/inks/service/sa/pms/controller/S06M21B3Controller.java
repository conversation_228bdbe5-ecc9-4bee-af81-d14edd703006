package inks.service.sa.pms.controller;


import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaCustscmuserPojo;
import inks.service.sa.pms.service.SaCustscmuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 客户SCM关系表(Sa_CustScmUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:30
 */
@Api(tags = "S06M21B3:客户SCM关系表")
@RestController
@RequestMapping("/S06M21B3")
public class S06M21B3Controller extends SaCustscmuserController {
    @Resource
    private SaCustscmuserService saCustscmuserService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 通过userid获取SCM客户关系表详细信息", notes = "获取SCM客户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserid", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiTenantScmUser.List")
    public R<SaCustscmuserPojo> getEntityByUserid(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustscmuserService.getEntityByUserid(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 通过userid获取SCM客户关系表详细信息", notes = "获取SCM租户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityMapByUserid", method = RequestMethod.GET)
    public R<Map<String, Object>> getEntityMapByUserid(String key) {
        try {
            // 获得用户数据
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaCustscmuserPojo entityByUserid = this.saCustscmuserService.getEntityByUserid(key);
            Map<String, Object> stringObjectMap = BeanUtils.beanToMap(entityByUserid);
            return R.ok(stringObjectMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增SCM(租户-用户)关联
     *
     * @param userid PiScmUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增SCM(租户-用户)关联", notes = "新增SCM租户关系表", produces = "application/json")
    @RequestMapping(value = "/createByUserId", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiTenantScmUser.Add")
    public R<SaCustscmuserPojo> createByUserId(String userid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaCustscmuserPojo saCustscmuserPojo = new SaCustscmuserPojo();
            //(租户-用户)关联
            saCustscmuserPojo.setUserid(userid);
            saCustscmuserPojo.setUsername(loginUser.getUsername());
            saCustscmuserPojo.setTenantid(loginUser.getTenantid());
            saCustscmuserPojo.setRealname(loginUser.getRealname());
            saCustscmuserPojo.setIsadmin(loginUser.getIsadmin());
            if (loginUser.getTenantinfo() != null) {
                saCustscmuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                saCustscmuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                saCustscmuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                saCustscmuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            saCustscmuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saCustscmuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saCustscmuserPojo.setCreatedate(new Date());   // 创建时间
            saCustscmuserPojo.setLister(loginUser.getRealname());   // 制表
            saCustscmuserPojo.setListerid(loginUser.getUserid());    // 制表id
            saCustscmuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saCustscmuserService.insert(saCustscmuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据当前用户获得租户关系表", notes = "根据当前用户获得租户关系表", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    public R<List<SaCustscmuserPojo>> getListByUser() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustscmuserService.getListByUserid(loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增SCM(租户-用户)关联
     *
     * @param json PiScmUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增SCM(租户-用户)关联", notes = "新增SCM租户关系表", produces = "application/json")
    @RequestMapping(value = "/createScmUser", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "PiTenantScmUser.Add")
    public R<SaCustscmuserPojo> createScmUser(@RequestBody String json) {
        try {
            SaCustscmuserPojo saCustscmuserPojo = JSONArray.parseObject(json, SaCustscmuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCustscmuserPojo.setTenantid(loginUser.getTenantid());
            if (loginUser.getTenantinfo() != null) {
                saCustscmuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                saCustscmuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                saCustscmuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                saCustscmuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            saCustscmuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saCustscmuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saCustscmuserPojo.setCreatedate(new Date());   // 创建时间
            saCustscmuserPojo.setLister(loginUser.getRealname());   // 制表
            saCustscmuserPojo.setListerid(loginUser.getUserid());    // 制表id
            saCustscmuserPojo.setModifydate(new Date());   //修改时间

            //判断是否传入userid;有，修改；无，新增
            SaCustscmuserPojo saCustscmuser = saCustscmuserService.createScmUser(saCustscmuserPojo);
            return R.ok(saCustscmuser);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}