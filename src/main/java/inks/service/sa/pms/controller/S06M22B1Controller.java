package inks.service.sa.pms.controller;


import inks.common.core.domain.R;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaSolutionPojo;
import inks.service.sa.pms.service.SaSolutionService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 解决方案(Sa_Solution)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-14 16:03:36
 */
@Api(tags = "S06M22B1:解决方案")
@RestController
@RequestMapping("/S06M22B1")
public class S06M22B1Controller extends SaSolutionController {
    @Resource
    private SaSolutionService saSolutionService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 通过SolutionCode获取解决方案详细信息", notes = "获取解决方案详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityBySolutionCode", method = RequestMethod.GET)
    public R<SaSolutionPojo> getEntityBySolutionCode(String code) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSolutionService.getEntityBySolutionCode(code));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}