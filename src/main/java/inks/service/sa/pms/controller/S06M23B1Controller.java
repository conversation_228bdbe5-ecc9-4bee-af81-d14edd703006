package inks.service.sa.pms.controller;

import com.dingtalk.api.DefaultDingTalkClient;
import com.dingtalk.api.DingTalkClient;
import com.dingtalk.api.request.OapiMessageCorpconversationAsyncsendV2Request;
import com.dingtalk.api.response.OapiMessageCorpconversationAsyncsendV2Response;
import com.taobao.api.ApiException;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.utils.ding.AccessTokenUtil;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 钉钉信息
 *
 * <AUTHOR>
 * @date 2023年01月10日 22:15
 */
@RestController
@RequestMapping("S06M23B1")
@Api(tags = "S06M23B1:钉钉信息")
public class S06M23B1Controller {


    private final static Logger logger = LoggerFactory.getLogger(S06M23B1Controller.class);

    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "钉钉第三方应用发送信息测试", notes = "钉钉第三方应用发送信息测试", produces = "application/json")
    @RequestMapping(value = "/sendMsgTest", method = RequestMethod.POST)
    public R<String> sendMsgTest(String msg, String userList) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

//            String UserList = utsDingmsgPojo.getUserlist();
//            if (dingmsgPojo.getUserlist() != null && dingmsgPojo.getUserlist().length() > 0) {
//                UserList = "," + dingmsgPojo.getUserlist();
//            }
            if (StringUtils.isBlank(userList)) {
                userList = "01262948650133270328";
            }
            logger.info("开始免登 uuid(userList)：" + userList);
            logger.info("开始免登 tid：" + loginUser.getTenantid());

            Long agentId = 1494595840L;
            logger.info("钉钉免登 agentId:" + agentId);
            //   [测试APP]的AppKey和AppSecret 详见:https://open-dev.dingtalk.com/fe/app#/appMgr/inner/h5/1494595840/1
            AccessTokenUtil.AppKey = "dingrr8hb7enjeqrxvye";
            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
            AccessTokenUtil.AppSecret = "x767FiHpDPQUKOKiSv_ZqRer2nGxDLSYXBLaSP1A6FzU_c0-8WTXoRNPsoXxX0qv";
            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

            // 获取access_token，注意正式代码要有异常流处理
            String access_token = AccessTokenUtil.getToken();
            logger.info("钉钉免登 Token:" + access_token);
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(agentId);
            req.setUseridList(userList);
            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            obj1.setMsgtype("text");
            OapiMessageCorpconversationAsyncsendV2Request.Text obj2 = new OapiMessageCorpconversationAsyncsendV2Request.Text();
            // 发送内容
            obj2.setContent(msg);
            obj1.setText(obj2);
            req.setMsg(obj1);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return R.fail(e.getErrMsg());
        }
    }


    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
    @RequestMapping(value = "/sendCardMsg", method = RequestMethod.GET)
    public R<String> sendCardMsg(String msg, String userList) {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        logger.info("开始免登 uuid(userList)：" + userList);
        logger.info("开始免登 tid：" + loginUser.getTenantid());

        Long agentId = 1494595840L;
        logger.info("钉钉免登 agentId:" + agentId);
        //   [测试APP]的AppKey和AppSecret 详见:https://open-dev.dingtalk.com/fe/app#/appMgr/inner/h5/1494595840/1
        AccessTokenUtil.AppKey = "dingrr8hb7enjeqrxvye";
        logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
        AccessTokenUtil.AppSecret = "x767FiHpDPQUKOKiSv_ZqRer2nGxDLSYXBLaSP1A6FzU_c0-8WTXoRNPsoXxX0qv";
        logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);

        // 获取access_token，注意正式代码要有异常流处理
        String access_token = AccessTokenUtil.getToken();
        logger.info("钉钉免登 Token:" + access_token);

        try {
            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
            req.setAgentId(agentId);
//            req.setUseridList("manager3081");
            req.setUseridList(userList);

            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
            obj1.setMsgtype("action_card");

            OapiMessageCorpconversationAsyncsendV2Request.ActionCard obj5 = new OapiMessageCorpconversationAsyncsendV2Request.ActionCard();
            // 注意: actioncard消息必须同时设置single_url和single_title!
            obj5.setSingleUrl("https://open.dingtalk.com");
            obj5.setSingleTitle("查看详情");
            obj5.setMarkdown("支持markdown格式的正文内容");
            obj5.setTitle("是透出到会话列表和通知的文案");
            obj1.setActionCard(obj5);

            req.setMsg(obj1);
            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
            logger.info(rsp.getBody());
            return R.ok(rsp.getBody());
        } catch (ApiException e) {
            e.printStackTrace();
            return R.fail(e.getErrMsg());
        }
    }

//    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
//    @RequestMapping(value = "/sendOaMsg", method = RequestMethod.POST)
//    public R<String> sendOaMsg(@RequestBody String json) {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//
//            String tid = loginUser.getTenantid();
//            DingmsgPojo dingmsgPojo = JSONArray.parseObject(json, DingmsgPojo.class);
//            UtsDingmsgPojo utsDingmsgPojo = this.utsDingmsgService.getBillEntityByMsgCode(dingmsgPojo.getMsgcode(), loginUser.getTenantid());
//
//
//            String UserList = utsDingmsgPojo.getUserlist();
//
//            logger.info("开始免登 uuid：" + UserList);
//            logger.info("开始免登 tid：" + loginUser.getTenantid());
//            Long agentId = 0L;
//
//            R r = new R();
//            logger.info("------开始钉钉免登-------");
//            //获取企业ID
//            r = systemFeignService.getConfigValue("D96M13.appkey", tid, "");
//            if (r.getCode() == 200) {
//                AccessTokenUtil.AppKey = r.getData().toString();
//                logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
//            } else {
//                logger.info("获取应用AppKey失败" + r.toString());
//            }
//            //获取应用密钥
//            r = systemFeignService.getConfigValue("D96M13.agentsecret", tid, "");
//            if (r.getCode() == 200) {
//                AccessTokenUtil.AppSecret = r.getData().toString();
//                logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
//            } else {
//                logger.info("获取应用AppSecret失败" + r.toString());
//            }
//
//            //获取应用密钥
//            r = systemFeignService.getConfigValue("D96M13.agentid", tid, "");
//            if (r.getCode() == 200) {
//                agentId = Long.parseLong(r.getData().toString());
//                logger.info("钉钉免登 agentId:" + agentId);
//            } else {
//                logger.info("获取应用agentId失败" + r.toString());
//            }
//
//
//            // 获取access_token，注意正式代码要有异常流处理
//            String access_token = AccessTokenUtil.getToken();
//            logger.info("钉钉免登 Token:" + access_token);
//
//
//            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
//            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
//            req.setAgentId(agentId);
//            req.setUseridList(UserList);
//            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
//            obj1.setMsgtype("oa");
//            OapiMessageCorpconversationAsyncsendV2Request.Link obj2 = new OapiMessageCorpconversationAsyncsendV2Request.Link();
//            obj2.setTitle("OA订单");
//            obj1.setLink(obj2);
//            OapiMessageCorpconversationAsyncsendV2Request.OA obj3 = new OapiMessageCorpconversationAsyncsendV2Request.OA();
//            OapiMessageCorpconversationAsyncsendV2Request.Body obj4 = new OapiMessageCorpconversationAsyncsendV2Request.Body();
//            obj4.setAuthor("Eric");
//            List<OapiMessageCorpconversationAsyncsendV2Request.Form> list6 = new ArrayList<OapiMessageCorpconversationAsyncsendV2Request.Form>();
//            OapiMessageCorpconversationAsyncsendV2Request.Form obj7 = new OapiMessageCorpconversationAsyncsendV2Request.Form();
//            list6.add(obj7);
//            obj7.setValue("PO1225458");
//            obj7.setKey("订单");
//            obj4.setForm(list6);
//            obj4.setTitle("OA订单");
//            obj3.setBody(obj4);
//            OapiMessageCorpconversationAsyncsendV2Request.Head obj8 = new OapiMessageCorpconversationAsyncsendV2Request.Head();
//            obj8.setBgcolor("FFBBBBBB");
//            obj8.setText("头部");
//            obj3.setHead(obj8);
//            obj1.setOa(obj3);
//            OapiMessageCorpconversationAsyncsendV2Request.Markdown obj9 = new OapiMessageCorpconversationAsyncsendV2Request.Markdown();
//            obj9.setTitle("OA订单");
//            obj1.setMarkdown(obj9);
//            OapiMessageCorpconversationAsyncsendV2Request.ActionCard obj10 = new OapiMessageCorpconversationAsyncsendV2Request.ActionCard();
//            List<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList> list12 = new ArrayList<OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList>();
//            OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList obj13 = new OapiMessageCorpconversationAsyncsendV2Request.BtnJsonList();
//            list12.add(obj13);
//            obj13.setTitle("OA订单");
//            obj10.setBtnJsonList(list12);
//            obj10.setTitle("OA订单");
//            obj1.setActionCard(obj10);
//            req.setMsg(obj1);
//            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
//            logger.info(rsp.getBody());
//            return R.ok(rsp.getBody());
//        } catch (ApiException e) {
//            e.printStackTrace();
//            return R.fail(e.getErrMsg());
//        }
//    }
//
//    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
//    @RequestMapping(value = "/sendTextMsg", method = RequestMethod.POST)
//    public R<String> sendTextMsg(@RequestBody String json) {
//        try {
//            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            String tid = loginUser.getTenantid();
//            DingmsgPojo dingmsgPojo = JSONArray.parseObject(json, DingmsgPojo.class);
//            UtsDingmsgPojo utsDingmsgPojo = this.utsDingmsgService.getBillEntityByMsgCode(dingmsgPojo.getMsgcode(), loginUser.getTenantid());
//            if (utsDingmsgPojo == null) {
//                return R.fail(dingmsgPojo.getMsgcode() + "信息模板未找到");
//            }
//            String UserList = utsDingmsgPojo.getUserlist();
//            if (dingmsgPojo.getUserlist() != null && dingmsgPojo.getUserlist().length() > 0) {
//                UserList = "," + dingmsgPojo.getUserlist();
//            }
//            logger.info("开始免登 uuid：" + UserList);
//            logger.info("开始免登 tid：" + loginUser.getTenantid());
//            Long agentId = 0L;
//
//            logger.info("------开始钉钉发信息,获取参数-------");
//            R<Map<String, String>> rcfg = systemFeignService.getConfigTenAll(0, tid, "");
//            if (rcfg.getCode() == 200) {
//                Map<String, String> mapcfg = rcfg.getData();
//                agentId = Long.parseLong(mapcfg.get(ConfigConstant.DING_AGENTID));
//                logger.info("钉钉免登 agentId:" + agentId);
//                AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
//                logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
//                AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
//                logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
//            } else {
//                logger.info("获取Config参数失败" + rcfg.getMsg());
//                R.fail("获取Config参数失败");
//            }
//
//            // 获取access_token，注意正式代码要有异常流处理
//            String access_token = AccessTokenUtil.getToken();
//            logger.info("钉钉免登 Token:" + access_token);
//
//            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/asyncsend_v2");
//            OapiMessageCorpconversationAsyncsendV2Request req = new OapiMessageCorpconversationAsyncsendV2Request();
//            req.setAgentId(agentId);
//            req.setUseridList(UserList);
//            OapiMessageCorpconversationAsyncsendV2Request.Msg obj1 = new OapiMessageCorpconversationAsyncsendV2Request.Msg();
//            obj1.setMsgtype("text");
//            OapiMessageCorpconversationAsyncsendV2Request.Text obj2 = new OapiMessageCorpconversationAsyncsendV2Request.Text();
//            obj2.setContent(dingmsgPojo.getMsgtext());
//            obj1.setText(obj2);
//            req.setMsg(obj1);
//            OapiMessageCorpconversationAsyncsendV2Response rsp = client.execute(req, access_token);
//            logger.info(rsp.getBody());
//            return R.ok(rsp.getBody());
//        } catch (ApiException e) {
//            e.printStackTrace();
//            return R.fail(e.getErrMsg());
//        }
//    }
//
//
//    @ApiOperation(value = " 企业微信第三方应用发送信息", notes = "企业微信第三方应用发送信息", produces = "application/json")
//    @RequestMapping(value = "/chkMsgState", method = RequestMethod.GET)
//    public R<String> chkMsgState(Long taskid, String tid) {
//
//        logger.info("开始免登 taskid：" + taskid);
//        logger.info("开始免登 tid：" + tid);
//        Long agentId = 0L;
//
//        R r = new R();
//        logger.info("------开始钉钉免登-------");
//        //获取企业ID
//        r = systemFeignService.getConfigValue("D96M13.appkey", tid, "");
//        if (r.getCode() == 200) {
//            AccessTokenUtil.AppKey = r.getData().toString();
//            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
//        } else {
//            logger.info("获取应用AppKey失败" + r.toString());
//        }
//        //获取应用密钥
//        r = systemFeignService.getConfigValue("D96M13.agentsecret", tid, "");
//        if (r.getCode() == 200) {
//            AccessTokenUtil.AppSecret = r.getData().toString();
//            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
//        } else {
//            logger.info("获取应用AppSecret失败" + r.toString());
//        }
//
//        //获取应用密钥
//        r = systemFeignService.getConfigValue("D96M13.agentid", tid, "");
//        if (r.getCode() == 200) {
//            agentId = Long.parseLong(r.getData().toString());
//            logger.info("钉钉免登 agentId:" + agentId);
//        } else {
//            logger.info("获取应用agentId失败" + r.toString());
//        }
//
//
//        // 获取access_token，注意正式代码要有异常流处理
//        String access_token = AccessTokenUtil.getToken();
//        logger.info("钉钉免登 Token:" + access_token);
//
//        try {
//            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/message/corpconversation/getsendresult");
//            OapiMessageCorpconversationGetsendresultRequest req = new OapiMessageCorpconversationGetsendresultRequest();
//            req.setAgentId(agentId);
//            req.setTaskId(taskid);
//            OapiMessageCorpconversationGetsendresultResponse rsp = client.execute(req, access_token);
//            logger.info(rsp.getBody());
//            return R.ok(rsp.getBody());
//        } catch (ApiException e) {
//            e.printStackTrace();
//            return R.fail(e.getErrMsg());
//        }
//    }
//
//
//
//
//    @ApiOperation(value = "获取部门ID列表和user列表钉钉第三方应用", notes = "钉钉第三方应用获取子部门ID列表", produces = "application/json")
//    @RequestMapping(value = "/getDepartmentListId", method = RequestMethod.GET)
//    public List<Map> getDepartmentListId() throws ApiException {
//        // 获得用户数据
//        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//        String tid = loginUser.getTenantid();
//        logger.info("------开始钉钉发信息,获取参数-------");
//        R<Map<String, String>> rcfg = systemFeignService.getConfigTenAll(0, tid, "");
//        Long agentId = 0L;
//        if (rcfg.getCode() == 200) {
//            Map<String, String> mapcfg = rcfg.getData();
//            agentId = Long.parseLong(mapcfg.get(ConfigConstant.DING_AGENTID));
////            agentId = Long.parseLong("1494595840");
//            logger.info("钉钉免登 agentId:" + agentId);
//            AccessTokenUtil.AppKey = mapcfg.get(ConfigConstant.DING_APPKEY);
////            AccessTokenUtil.AppKey = "dingrr8hb7enjeqrxvye";
//            logger.info("钉钉免登 AppKey:" + AccessTokenUtil.AppKey);
//            AccessTokenUtil.AppSecret = mapcfg.get(ConfigConstant.DING_APPSECRET);
////            AccessTokenUtil.AppSecret = "x767FiHpDPQUKOKiSv_ZqRer2nGxDLSYXBLaSP1A6FzU_c0-8WTXoRNPsoXxX0qv";
//            logger.info("钉钉免登 AppSecret:" + AccessTokenUtil.AppSecret);
//        } else {
//            logger.info("获取Config参数失败" + rcfg.getMsg());
//            R.fail("获取Config参数失败");
//        }
//        String access_token = AccessTokenUtil.getToken();
//        logger.info("钉钉免登 Token:" + access_token);
//        //获取部门信息列表
//        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
//        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
//        req.setDeptId(1L);
//        OapiV2DepartmentListsubResponse rsp = client.execute(req, access_token);
//        JSONObject jsonObject = JSONArray.parseObject(rsp.getBody());
//        Object result = jsonObject.get("result");
//        List<Map> deptAndUser = JSONArray.parseArray(result.toString(), Map.class);
//        //添加根部门id=1
//        Map<String, Object> rootDept= new HashMap<>();
//        rootDept.put("dept_id", 1);
//        rootDept.put("name", "根部门");
//        deptAndUser.add(rootDept);
//        List<Map<String, Object>> mapList = new ArrayList<>();
//        for (Map<String,Object> dept : deptAndUser) {
//            //获取dept_id
//            Object deptIdString =  dept.get("dept_id");
//            Long deptId = Long.valueOf(deptIdString.toString());
//            try {
//                //获取用户详情(包括userid,name...)
//                DingTalkClient client2 = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
//                OapiV2UserListRequest req2 = new OapiV2UserListRequest();
//                req2.setDeptId(deptId);
//                req2.setCursor(0L);
//                req2.setSize(10L);
//                req2.setOrderField("modify_desc");
//                req2.setContainAccessLimit(false);
//                req2.setLanguage("zh_CN");
//                OapiV2UserListResponse rsp2 = client2.execute(req2, access_token);
//                JSONObject jsonObject2 = JSONArray.parseObject(rsp2.getBody());
//                Object result2 = jsonObject2.get("result");
//                result2 = JSONArray.parseObject(result2.toString()).get("list");
//                List<String> userIdList = JSONArray.parseArray(result2.toString(), String.class);
//                dept.put("userIdList", userIdList);
//
//            } catch (ApiException e) {
//                e.printStackTrace();
//            }
//        }
//        logger.info("部门下所有user:" + deptAndUser);
//        return deptAndUser;
//    }
//
//
//
//
//    public static void main(String[] args) throws ApiException {
//
//        List<String> departmentLis = getDepartmentListIdTest();
//        getUserListTest(departmentLis);
//
//    }
//    public static List<String> getDepartmentListIdTest() throws ApiException {
//        DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/department/listsub");
//        OapiV2DepartmentListsubRequest req = new OapiV2DepartmentListsubRequest();
//        req.setDeptId(1L);
//        OapiV2DepartmentListsubResponse rsp = client.execute(req, "8927732c95633532833b6dbaf66226b7");
//        JSONObject jsonObject = JSONArray.parseObject(rsp.getBody());
//        // rsp.getBody()的值为：{"errcode":0,"errmsg":"ok","result":{"dept_id_list":[614051279,613394515,614110180]},"request_id":"15sbc62t8bjcy"}
//        Object result = jsonObject.get("result");
//        List<String> deptIdList = JSONArray.parseArray(result.toString(), String.class);
//        deptIdList.forEach(System.out::println);
//        for (String s : deptIdList) {
//            String deptId = JSONArray.parseObject(s).getString("dept_id");
//            System.out.println("deptId = " + deptId);
//        }
//        return deptIdList;
//    }
//
//    public static List<Map<String,Object>> getUserListTest(List<String> deptIdList) throws ApiException {
//        for (String s : deptIdList) {
//            //获取dept_id
//            String deptIdString = JSONArray.parseObject(s).getString("dept_id");
//            Long deptId = Long.valueOf(deptIdString);
//            try {
//                DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
//                OapiV2UserListRequest req = new OapiV2UserListRequest();
//                req.setDeptId(deptId);
//                req.setCursor(0L);
//                req.setSize(10L);
//                req.setOrderField("modify_desc");
//                req.setContainAccessLimit(false);
//                req.setLanguage("zh_CN");
//                OapiV2UserListResponse rsp = client.execute(req, "8927732c95633532833b6dbaf66226b7");
//                System.out.println(rsp.getBody());
//            } catch (ApiException e) {
//                e.printStackTrace();
//            }
//            System.out.println("deptId = " + deptId);
//        }
//        //根部门下员工信息
//        try {
//            DingTalkClient client = new DefaultDingTalkClient("https://oapi.dingtalk.com/topapi/v2/user/list");
//            OapiV2UserListRequest req = new OapiV2UserListRequest();
//            req.setDeptId(1L);
//            req.setCursor(0L);
//            req.setSize(10L);
//            req.setOrderField("modify_desc");
//            req.setContainAccessLimit(false);
//            req.setLanguage("zh_CN");
//            OapiV2UserListResponse rsp = client.execute(req, "8927732c95633532833b6dbaf66226b7");
//            System.out.println(rsp.getBody());
//        } catch (ApiException e) {
//            e.printStackTrace();
//        }
//        return null;
//    }

}
