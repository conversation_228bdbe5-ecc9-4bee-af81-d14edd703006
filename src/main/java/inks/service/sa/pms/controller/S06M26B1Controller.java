package inks.service.sa.pms.controller;


import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.mapper.SaChatterMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * PMS客服(Sa_Chatter)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-21 13:22:05
 */
@Api(tags = "S06M26B1:PMS客服")
@RestController
@RequestMapping("/S06M26B1")
public class S06M26B1Controller extends SaChatterController {
    @Resource
    private SaChatterMapper saChatterMapper;
    @Resource
    private SaRedisService saRedisService;

    //是否是客服
    @ApiOperation(value = " 是否是PmS客服", notes = "", produces = "application/json")
    @RequestMapping(value = "/isChatter", method = RequestMethod.GET)
    public R<Boolean> isChatter() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            int chatter = this.saChatterMapper.isChatter(loginUser.getUserid());
            return R.ok(chatter > 0);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}