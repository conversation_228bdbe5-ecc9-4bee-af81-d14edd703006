package inks.service.sa.pms.controller;


import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaChatuserPojo;
import inks.service.sa.pms.mapper.SaChatuserMapper;
import inks.service.sa.pms.service.SaChatuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 对话用户(Sa_ChatUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-12-21 15:01:54
 */
@Api(tags = "S06M26B2:对话用户")
@RestController
@RequestMapping("/S06M26B2")
public class S06M26B2Controller extends SaChatuserController {
    @Resource
    private SaChatuserService saChatuserService;
    @Resource
    private SaChatuserMapper saChatuserMapper;
    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 将传入的LoginUser同步储存到PMS对话用户中Sa_ChatUser", notes = "由OMS端调用PMS接口,token没用", produces = "application/json")
    @RequestMapping(value = "/syncChatUser", method = RequestMethod.POST)
    public R<SaChatuserPojo> syncChatUser(@RequestBody String json) {
        try {
            // 注意这个loginUser是前端传进来的,不是token中的 由OMS端调用PMS接口,token没用
            LoginUser loginUser = JSONArray.parseObject(json, LoginUser.class);
            SaChatuserPojo saChatuserPojo = new SaChatuserPojo();
            BeanUtils.copyProperties(loginUser, saChatuserPojo);
            saChatuserPojo.setModifydate(new Date());
            // 检查userid是否已经存在
            String idDB = saChatuserMapper.getIdByUserId(loginUser.getUserid());
            if (StringUtils.isNotBlank(idDB)) {
                // 如果已经存在,则更新 (userid对应的信息可能会变化,比如姓名等)
                saChatuserPojo.setId(idDB);
                return R.ok(this.saChatuserService.update(saChatuserPojo));
            } else {
                // 如果不存在,则新增
                saChatuserPojo.setRemark("用户登录时由前端触发创建");
                saChatuserPojo.setCreatedate(new Date());
                return R.ok(this.saChatuserService.insert(saChatuserPojo));
            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}