package inks.service.sa.pms.controller;


import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaReportslabPojo;
import inks.service.sa.pms.service.SaReportslabService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 报表库(Sa_ReportsLab)表控制层
 *
 * <AUTHOR>
 * @since 2024-01-23 09:44:16
 */
@Api(tags = "S06M27B1:报表库")
@RestController
@RequestMapping("/S06M27B1")
public class S06M27B1Controller extends SaReportslabController {

    @Resource
    private SaReportslabService saReportslabService;


    @Resource
    private SaRedisService saRedisService;

    /**
     * 按模块编码查询报表
     *
     * @param code 主键
     * @return 查询结果
     */
    @ApiOperation(value = "按模块编码查询报表", notes = "按模块编码查询报表", produces = "application/json")
    @RequestMapping(value = "/getListByModuleCode", method = RequestMethod.GET)
    public R<List<SaReportslabPojo>> getListByModuleCode(String code) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaReportslabPojo> list = this.saReportslabService.getListByModuleCode(code);
            if (list != null) {
                for (SaReportslabPojo saReportslabPojo : list) {
                    String verifyKey = CacheConstants.REPORT_CODES_KEY + saReportslabPojo.getId();
                    ReportsPojo reportsPojo = new ReportsPojo();
                    reportsPojo.setRptdata(saReportslabPojo.getRptdata());
                    reportsPojo.setPagerow(saReportslabPojo.getPagerow());
                    reportsPojo.setTempurl(saReportslabPojo.getTempurl());
                    reportsPojo.setPrintersn(saReportslabPojo.getPrintersn());
                    reportsPojo.setPaperlength(saReportslabPojo.getPaperlength());
                    reportsPojo.setPaperwidth(saReportslabPojo.getPaperwidth());
                    reportsPojo.setGrfdata(saReportslabPojo.getGrfdata());
                    saRedisService.setCacheObject(verifyKey, reportsPojo, 60 * 12, TimeUnit.MINUTES);
                    if (StringUtils.isNotBlank(saReportslabPojo.getGrfdata())) {
                        saReportslabPojo.setGrfdata("true");
                    } else {
                        saReportslabPojo.setGrfdata(null);
                    }
                    if (StringUtils.isNotBlank(saReportslabPojo.getRptdata())) {
                        saReportslabPojo.setRptdata("true");
                    } else {
                        saReportslabPojo.setRptdata(null);
                    }
                }
            }
            return R.ok(list);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "通过分享码获取报表，用于远程导入报表", notes = "", produces = "application/json")
    @RequestMapping(value = "/getGrfDataByShareCode/{key}", method = RequestMethod.GET)
    public R<String> getGrfDataByShareCode(@PathVariable("key") String key) {
        try {
            if (StringUtils.isBlank(key)) {
                return R.fail("分享码不能为空");
            }
            return R.ok(this.saReportslabService.getGrfDataByShareCode(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取随机分享码 6位[数字+小写字母]", notes = "", produces = "application/json")
    @RequestMapping(value = "/getShareCode", method = RequestMethod.GET)
    public R<String> getShareCode() {
        try {
            return R.ok(this.saReportslabService.getShareCode());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "初始化：分享码为null的，全部刷入分享码", notes = "", produces = "application/json")
    @RequestMapping(value = "/initShareCode", method = RequestMethod.GET)
    public R<String> setShareCode() {
        try {
            return R.ok(this.saReportslabService.initShareCode());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }



    @ApiOperation(value = "拉取默认报表,重复code、name跳过", notes = "拉取默认报表 code可选", produces = "application/json")
    @RequestMapping(value = "/pullDefault", method = RequestMethod.GET)
    public R<List<SaReportslabPojo>> pullDefault(String code) {
        try {
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaReportslabPojo> fmAccountPojos = this.saReportslabService.pullDefault(code, null);
            return R.ok(fmAccountPojos);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListAll", method = RequestMethod.POST)
    public R<PageInfo<SaReportslabPojo>> getPageListAll(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Reports.ModuleCode");
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saReportslabService.getPageListAll(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}