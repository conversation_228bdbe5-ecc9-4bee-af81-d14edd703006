package inks.service.sa.pms.controller;


import inks.common.core.domain.R;
import inks.service.sa.pms.domain.pojo.SaReportsdemandPojo;
import inks.service.sa.pms.service.SaReportsdemandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 报表模版需求(Sa_ReportsDemand)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-22 15:30:01
 */
@Api(tags = "S06M27B2:报表模版需求")
@RestController
@RequestMapping("/S06M27B2")
public class S06M27B2Controller extends SaReportsdemandController {
    @Resource
    private SaReportsdemandService saReportsdemandService;

    private final static Logger logger = LoggerFactory.getLogger(SaReportsdemandController.class);


    @ApiOperation(value = " 获取报表模版需求详细信息", notes = "获取报表模版需求详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityBySerCode", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ReportsDemand.List")
    public R<SaReportsdemandPojo> getEntityBySerCode(String key) {
        //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saReportsdemandService.getEntityBySerCode(key));
    }

}