package inks.service.sa.pms.controller;


import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.domain.vo.SaEngineerPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaFeedbackPojo;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemdetailPojo;
import inks.service.sa.pms.service.SaFeedbackService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 反馈单(SaFeedback)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-17 13:04:37
 */
@Api(tags = "S06M28B1:反馈单")
@RestController
@RequestMapping("/S06M28B1")
public class S06M28B1Controller extends SaFeedbackController {

    @Resource
    private SaFeedbackService saFeedbackService;


    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    // @PreAuthoriz(hasPermi = "Sa_Feedback.List")
    public R<PageInfo<SaFeedbackitemdetailPojo>> getOnlinePageList(@RequestBody String json, @RequestParam(required = false) Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Feedback.CreateDate");
            // 获得用户数据
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 现在，您可以访问工程师信息
            SaEngineerPojo engineer = myLoginUser.getEngineer();
            if (engineer == null) {
                return R.fail("非工程师，您没有权限访问此功能");
            }
            String qpfilter = " and Sa_FeedbackItem.FinishMark=0";
            if (own != null && own == 1) {
                qpfilter += " and (Sa_Feedback.CreateByid='" + engineer.getUserid() + "' or Sa_Feedback.personnel like '%" + engineer.getId() + "%')";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFeedbackService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Feedback.List")
    public R<PageInfo<SaFeedbackPojo>> getOnlinePageTh(@RequestBody String json, @RequestParam(required = false) Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Feedback.CreateDate");
            // 获得用户数据
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            // 现在，您可以访问工程师信息
            SaEngineerPojo engineer = myLoginUser.getEngineer();
            if (engineer == null) {
                return R.fail("非工程师，您没有权限访问此功能");
            }
            String qpfilter = " and Sa_Feedback.FinishCount<Sa_Feedback.ItemCount";
            if (own != null && own == 1) {
                qpfilter += " and (Sa_Feedback.CreateByid='" + engineer.getUserid() + "' or Sa_Feedback.personnel like '%" + engineer.getId() + "%')";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFeedbackService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}