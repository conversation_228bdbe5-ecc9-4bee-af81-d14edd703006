package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.parser.Feature;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.service.SaMqttlogService;
import inks.service.sa.pms.utils.PrintColor;
import inks.service.sa.pms.utils.mqtt.MqttMsg;
import inks.service.sa.pms.utils.mqtt.MyMQTTClient;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.LinkedList;
import java.util.Queue;

/**
 * MQTT日志(Sa_MqttLog)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-26 12:57:20
 */
@RestController
@RequestMapping("S06M30B1")
@Api(tags = "S06M30B1:MQTT日志")
public class S06M30B1Controller extends SaMqttlogController {

    @Value("${mqtt.topic1}")
    String topic1;
    @Value("${mqtt.topic2}")
    String topic2;
    @Value("${mqtt.topic3}")
    String topic3;
    @Value("${mqtt.topic4}")
    String topic4;
    Queue<String> msgQueue = new LinkedList<>();
    int count = 1;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaMqttlogService utsMqttmsgService;
    @Autowired
    private MyMQTTClient myMQTTClient;


    /**
     * 格式举例:
     * {
     * "content": "{\"pagenum\":1,\"pagesize\":11}",
     * "topic": "inks/client/topic111"
     * }
     */
    @PostMapping("/send")
    @ResponseBody
    @ApiOperation(value = "自定义topic发送消息(读取json中的content和topic)", notes = "自定义topic发送消息", produces = "application/json")
    public synchronized R<String> send(@RequestBody String json) {
        //读取json中的content和topic
        JSONObject jsonObject = JSONObject.parseObject(json, Feature.OrderedField);
        String content = jsonObject.getString("content");
        String topic = jsonObject.getString("topic");

        System.out.println("队列元素数量：" + msgQueue.size());
        count++;
        //队列添加元素
        boolean flag = msgQueue.offer(content);

        if (flag) {
            //发布消息  topic2 是你要发送到那个通道里面的主题 比如我要发送到topic2主题消息
            myMQTTClient.publish(msgQueue.poll(), topic);
            PrintColor.color("时间戳" + System.currentTimeMillis());
        }
        System.out.println("队列元素数量：" + msgQueue.size());
        return R.ok("发送成功(后台共收到" + count + "条消息)" + content);
    }

    @PostMapping("/textMsgByCode")
    @ResponseBody
    @ApiOperation(value = "接收code,msg指定主题inks/oms/tid", notes = "", produces = "application/json")
    public synchronized R<String> textMsgByCode(@RequestBody String json, String tid) {
        if (StringUtils.isBlank(tid)) {
            tid = saRedisService.getLoginUser(ServletUtils.getRequest()).getTenantid();
        }
        // 从json中获取code和content
        JSONObject jsonObject = JSONObject.parseObject(json);
        String msgCode = jsonObject.getString("code");
        String content = jsonObject.getString("msg");
        PrintColor.lv("队列元素数量：" + msgQueue.size());
        count++;
        // 构建MqttTextPojo
//        UtsMqttmsgPojo utsMqttmsgPojo = utsMqttmsgService.getEntityByMsgCode(msgCode, tid);
//        // 创建 Text 对象
//        MqttTextPojo.Text text = new MqttTextPojo.Text(utsMqttmsgPojo.getMsgname(), content);
//        // 创建 Msg 对象
//        MqttTextPojo.Msg msg = new MqttTextPojo.Msg(text, utsMqttmsgPojo.getMsgtype(), utsMqttmsgPojo.getDuration(),utsMqttmsgPojo.getPosition(),utsMqttmsgPojo.getMsgicon());
//        // 创建 MqttTextPojo 对象
//        MqttTextPojo mqttTextPojo = new MqttTextPojo(msg, utsMqttmsgPojo.getToalluser() != 0, utsMqttmsgPojo.getUseridlist(), utsMqttmsgPojo.getModulecode());
        // mqttTextPojo 转 json
//        String mqttTextJson = JSON.toJSONString(mqttTextPojo);
        String mqttTextJson = JSON.toJSONString("test:构建MqttTextPojo");
        System.out.println("========转换为 JSON：" + mqttTextJson);
        //队列添加元素
        boolean flag = msgQueue.offer(mqttTextJson);
        // 构建topic inks/oms/tid
        String topic = "inks/oms/" + tid;
        //发布消息  topic2 是你要发送到那个通道里面的主题 比如我要发送到topic2主题消息
        myMQTTClient.publish(msgQueue.poll(), topic);
        PrintColor.color("【tid下ToAllUser发送】topic主题:" + topic);


        System.out.println("队列元素数量：" + msgQueue.size());
        return R.ok("发送成功(后台共收到" + count + "条消息)" + mqttTextJson);
    }


    @PostMapping("/textMsg")
    @ResponseBody
    @ApiOperation(value = "用于测试:指定主题inks/oms/tid", notes = "", produces = "application/json")
    public synchronized R<String> textMsg() throws JsonProcessingException {
        System.out.println("队列元素数量：" + msgQueue.size());
        count++;
        // JSON数据
        String sendMsgjson = "{\n" +
                "    \"msg\":{ \n" +
                "        \"text\":{\n" +
                "            \"title\":\"title\",\n" +
                "            \"content\":\"测试信息\"\n" +
                "        },\n" +
                "        \"msgtype\":\"text\",\n" +
                "        \"duration\":\"-1\"\n" +
                "    },\n" +
                "    \"to_all_user\":\"true\",\n" +
                "    \"modulecode\":\"test\"\n" +
                "}";
        System.out.println("========转换为 JSON：" + sendMsgjson);

        //队列添加元素
        boolean flag = msgQueue.offer(sendMsgjson);
        // 构建topic inks/oms/tid
        String tid = saRedisService.getLoginUser(ServletUtils.getRequest()).getTenantid();
        String topic = "inks/oms/" + tid;
        if (flag) {
            //发布消息  topic2 是你要发送到那个通道里面的主题 比如我要发送到topic2主题消息
            myMQTTClient.publish(msgQueue.poll(), topic);
            PrintColor.color("topic主题:" + topic + "时间戳:" + System.currentTimeMillis());
        }
        System.out.println("队列元素数量：" + msgQueue.size());
        return R.ok("发送成功(后台共收到" + count + "条消息)" + sendMsgjson);
    }

    //    ----------------------------------------------------以下为参考代码 待删除----------------------------------------------------
    @PostMapping("/sendMsg")
    @ResponseBody
    @ApiOperation(value = "发送消息(已指定topic)", notes = "发送消息(已指定topic)", produces = "application/json")
    public synchronized R<String> mqttMsg(MqttMsg mqttMsg) throws JsonProcessingException {
        System.out.println("队列元素数量：" + msgQueue.size());
        System.out.println("***************" + mqttMsg.getName() + ":" + mqttMsg.getContent() + "****************");
        //时间格式化
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = df.format(new Date());
        mqttMsg.setTime(time);
        mqttMsg.setContent(mqttMsg.getContent());
        count++;
        //map转json
        // 将对象转换为 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String sendMsgjson = objectMapper.writeValueAsString(mqttMsg);
        System.out.println("转换为 JSON：" + sendMsgjson);
        //队列添加元素
        boolean flag = msgQueue.offer(sendMsgjson);
        if (flag) {
            //发布消息  topic2 是你要发送到那个通道里面的主题 比如我要发送到topic2主题消息
            myMQTTClient.publish(msgQueue.poll(), topic3);
            PrintColor.color("时间戳" + System.currentTimeMillis());
        }
        System.out.println("队列元素数量：" + msgQueue.size());
        return R.ok("发送成功(后台共收到" + count + "条消息)" + sendMsgjson);
    }


    @PostMapping("/sendMqttMsg")
    @ResponseBody
    @ApiOperation(value = "自定义topic发送 格式为MqttMsg对象的消息", notes = "自定义topic发送消息", produces = "application/json")
    public synchronized R<String> sendMqttMsg(MqttMsg mqttMsg, String topic) throws JsonProcessingException {
        System.out.println("队列元素数量：" + msgQueue.size());
        System.out.println("***************" + mqttMsg.getName() + ":" + mqttMsg.getContent() + "****************");
        //时间格式化
        SimpleDateFormat df = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        String time = df.format(new Date());
        mqttMsg.setTime(time);
        mqttMsg.setContent(mqttMsg.getContent());
        count++;
        //map转json
        // 将对象转换为 JSON 字符串
        ObjectMapper objectMapper = new ObjectMapper();
        String sendMsgjson = objectMapper.writeValueAsString(mqttMsg);
        System.out.println("转换为 JSON：" + sendMsgjson);

        //队列添加元素
        boolean flag = msgQueue.offer(sendMsgjson);
        if (flag) {
            //发布消息  topic2 是你要发送到那个通道里面的主题 比如我要发送到topic2主题消息
            myMQTTClient.publish(msgQueue.poll(), topic);
            PrintColor.color("时间戳" + System.currentTimeMillis());

        }
        System.out.println("队列元素数量：" + msgQueue.size());
        return R.ok("发送成功(后台共收到" + count + "条消息)" + sendMsgjson);
    }

    @PostMapping("/testMsg")
    @ResponseBody
    @ApiOperation(value = "自定义topic发送任意消息", notes = "自定义topic发送消息", produces = "application/json")
    public synchronized R<String> testMsg(@RequestBody String json, String topic) throws JsonProcessingException {
        System.out.println("队列元素数量：" + msgQueue.size());
        count++;
        //队列添加元素
        boolean flag = msgQueue.offer(json);
        if (flag) {
            //发布消息  topic2 是你要发送到那个通道里面的主题 比如我要发送到topic2主题消息
            myMQTTClient.publish(msgQueue.poll(), topic);
            PrintColor.color("时间戳" + System.currentTimeMillis());
        }
        System.out.println("队列元素数量：" + msgQueue.size());
        return R.ok("发送成功(后台共收到" + count + "条消息)" + json);
    }


}
