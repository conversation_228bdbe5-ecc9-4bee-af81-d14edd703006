package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import inks.service.sa.pms.domain.pojo.SaGitlogPojo;
import inks.service.sa.pms.service.SaGitlogService;
import inks.service.sa.pms.utils.PrintColor;
import io.swagger.annotations.Api;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * 代码提交日志(Sa_GItLog)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-22 13:45:59
 */
@RestController
@RequestMapping("S06M32B1")
@Api(tags = "S06M32B1:代码提交日志")
public class S06M32B1Controller extends SaGitlogController {

    private static final String SECRET_TOKEN = "inks";
    // GitLab日期格式 2024-06-22T13:12:25+08:00 需转换为Date对象
    private static final SimpleDateFormat dateFormatGitLab = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssXXX");
    @Resource
    private SaGitlogService saGitlogService;

    /**
     * @return ResponseEntity<String>
     * @Description 准备85632541
     * <AUTHOR>
     * @param[1] secret_token 对应Webhook的Secret令牌 若未配置则secret_token接收为null 不做验证
     * @param[2] payload Webhook发送的JSON数据,内容包含事件名称、提交者、项目名称、项目 URL、项目描述、项目命名空间、提交信息、提交日期、提交作者、提交邮箱等信息
     * 一个收到的payload示例：
     * {"object_kind":"push","event_name":"push","before":"3c42cbe6a2c507c0d8b7de2df3617a080582e747","after":"449afb44e6291ffee8852742cc472522ef874f04","ref":"refs/heads/master","ref_protected":true,"checkout_sha":"449afb44e6291ffee8852742cc472522ef874f04","message":null,"user_id":6,"user_name":"nanno.jiang","user_username":"nanno.jiang","user_email":"","user_avatar":"http://git.inksdev.com/uploads/-/system/user/avatar/6/avatar.png","project_id":10,"project":{"id":10,"name":"inks-service-sa-pms","description":"S06 PMS项目管理后端代码","web_url":"http://git.inksdev.com/sasvc/inks-service-sa-pms","avatar_url":null,"git_ssh_url":"*******************:sasvc/inks-service-sa-pms.git","git_http_url":"http://git.inksdev.com/sasvc/inks-service-sa-pms.git","namespace":"sasvc","visibility_level":0,"path_with_namespace":"sasvc/inks-service-sa-pms","default_branch":"master","ci_config_path":null,"homepage":"http://git.inksdev.com/sasvc/inks-service-sa-pms","url":"*******************:sasvc/inks-service-sa-pms.git","ssh_url":"*******************:sasvc/inks-service-sa-pms.git","http_url":"http://git.inksdev.com/sasvc/inks-service-sa-pms.git"},"commits":[{"id":"449afb44e6291ffee8852742cc472522ef874f04","message":"test^Webhook 12.54\n","title":"test^Webhook 12.54","timestamp":"2024-06-22T12:55:37+08:00","url":"http://git.inksdev.com/sasvc/inks-service-sa-pms/-/commit/449afb44e6291ffee8852742cc472522ef874f04","author":{"name":"jyfdexh","email":"<EMAIL>"},"added":["src/main/java/inks/service/sa/pms/controller/testWebhook.txt"],"modified":[],"removed":[]}],"total_commits_count":1,"push_options":{},"repository":{"name":"inks-service-sa-pms","url":"*******************:sasvc/inks-service-sa-pms.git","description":"S06 PMS项目管理后端代码","homepage":"http://git.inksdev.com/sasvc/inks-service-sa-pms","git_http_url":"http://git.inksdev.com/sasvc/inks-service-sa-pms.git","git_ssh_url":"*******************:sasvc/inks-service-sa-pms.git","visibility_level":0}}
     * @time 2024/6/22 下午12:45
     */
    @PostMapping("/push")
    public ResponseEntity<String> handleWebhook(
            @RequestHeader(value = "X-Gitlab-Token", required = false) String secret_token,
            @RequestBody String payload) throws ParseException {
        PrintColor.red("进入Received webhook from GitLab");
        // 验证 Secret Token（如果配置了）
        if (secret_token != null && !SECRET_TOKEN.equals(secret_token)) {
            return ResponseEntity.status(HttpStatus.FORBIDDEN).body("Invalid token By PMS");
        }

        // 处理接收到的 Webhook 数据
        PrintColor.red("Received payload: " + payload);

        // 解析 payload 并提取以下信息
//        EventName 事件名 push/
//        UserName 登录名唯一标识
//        ProjectName 项目名
//        ProjectUrl 项目地址
//        ProjectDesc 项目描述
//        ProjectNameSpace 项目命名空间
//        CommitMessage 提交commit信息
//        CommitDate 提交时间 "timestamp": "2024-06-22T12:55:37+08:00"
//        CommitAuthor 提交人
//        CommitEmail 提交人邮箱
        JSONObject jsonObject = JSON.parseObject(payload);

        String eventName = jsonObject.getString("event_name");
        String userName = jsonObject.getString("user_username");
        JSONObject project = jsonObject.getJSONObject("project");
        String projectName = project.getString("name");
        String projectUrl = project.getString("web_url");
        String projectDesc = project.getString("description");
        String projectNamespace = project.getString("namespace");

        JSONObject commit = jsonObject.getJSONArray("commits").getJSONObject(0);
        String commitMessage = commit.getString("message");
        String commitDateString = commit.getString("timestamp");// 2024-06-22T13:12:25+08:00
        // 解析字符串为 Date 对象
        Date commitDate = dateFormatGitLab.parse(commitDateString);
        JSONObject author = commit.getJSONObject("author");
        String commitAuthor = author.getString("name");
        String commitEmail = author.getString("email");


        // 处理业务逻辑 创建代码提交日志
        SaGitlogPojo saGitlogPojo = new SaGitlogPojo();
        saGitlogPojo.setEventname(eventName);
        saGitlogPojo.setUsername(userName);
        saGitlogPojo.setProjectname(projectName);
        saGitlogPojo.setProjecturl(projectUrl);
        saGitlogPojo.setProjectdesc(projectDesc);
        saGitlogPojo.setProjectnamespace(projectNamespace);
        saGitlogPojo.setCommitmessage(commitMessage);
        saGitlogPojo.setCommitdate(commitDate);
        saGitlogPojo.setCommitauthor(commitAuthor);
        saGitlogPojo.setCommitemail(commitEmail);
        saGitlogPojo.setRemark("From GitLab Webhook");
        // 保存代码提交日志
        saGitlogService.insert(saGitlogPojo);
        // 打印代码提交日志信息
        PrintColor.green("GitLab Webhook创建的代码提交日志: " + saGitlogPojo);
        return ResponseEntity.ok("Webhook received and processed BY PMS");
    }
}
