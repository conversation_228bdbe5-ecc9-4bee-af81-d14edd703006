package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaNotebookPojo;
import inks.service.sa.pms.service.SaNotebookService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 笔记本(Sa_NoteBook)表控制层
 *
 * <AUTHOR>
 * @since 2024-06-22 16:39:51
 */
@RestController
@RequestMapping("S06M33B1")
@Api(tags = "S06M33B1:笔记本")
public class S06M33B1Controller extends SaNotebookController {
    @Resource
    private SaNotebookService saNotebookService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取我的笔记本详细信息 如果没有笔记本，则初始化一个", notes = "", produces = "application/json")
    @RequestMapping(value = "/getMyNotebook", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_NoteBook.List")
    public R<SaNotebookPojo> getMyNotebook() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saNotebookService.getMyNotebook(loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
