package inks.service.sa.pms.controller;

import cn.hutool.core.util.RandomUtil;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.SaFreepageEntity;
import inks.service.sa.pms.domain.pojo.SaFreepagePojo;
import inks.service.sa.pms.mapper.SaFreepageMapper;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 自由页面(Sa_FreePage)表控制层
 *
 * <AUTHOR>
 * @since 2024-07-22 10:20:59
 */
@RestController
@RequestMapping("S06M34B1")
@Api(tags = "S06M34B1:自由页面")
public class S06M34B1Controller extends SaFreepageController {

    @Resource
    private SaFreepageMapper saFreepageMapper;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = "针对一个自由页面行，生成ShareKey（分享码）", notes = "", produces = "application/json")
    @GetMapping("/share")
    //@PreAuthorize(hasPermi = "Sa_NoteBook.List")
    public R<String> share(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 8位数随机混合字符串
            String randomMixedString = RandomUtil.randomString(8);
            SaFreepageEntity saFreepageEntity = new SaFreepageEntity();
            saFreepageEntity.setId(key);
            saFreepageEntity.setSharekey(randomMixedString);
            saFreepageEntity.setLister(loginUser.getRealname());
            saFreepageEntity.setListerid(loginUser.getUserid());
            this.saFreepageMapper.update(saFreepageEntity);
            return R.ok(randomMixedString);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 根据分享码获取自由页面", notes = "", produces = "application/json")
    @GetMapping("/getEntityByKey")
    //@PreAuthorize(hasPermi = "Sa_NoteBook.List")
    public R<SaFreepagePojo> getEntityByKey(String shareKey) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFreepageMapper.getEntityByShareKey(shareKey));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


//    public static void main(String[] args) {
//        // 生成长度为10的纯数字随机码
//        String randomNumbers = RandomUtil.randomNumbers(10);
//        System.out.println("纯数字随机码: " + randomNumbers);
//
//        // 生成长度为10的纯英文随机码（大小写字母）
//        String randomLetters = RandomUtil.randomString("abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ", 10);
//        System.out.println("纯英文随机码: " + randomLetters);
//
//        // 生成长度为10的数字加英文随机码
//        String randomMixedString = RandomUtil.randomString(10);
//        System.out.println("数字加英文随机码: " + randomMixedString);
//
//        // 生成长度为10的随机码，字符范围为指定字符集合
//        String randomCustomString = RandomUtil.randomString("abcdef123456", 10);
//        System.out.println("指定字符范围[abcdef123456]的随机码: " + randomCustomString);
//
//        // 生成UUID
//        String uuid = IdUtil.randomUUID();
//        System.out.println("UUID: " + uuid);
//
//        // 生成简单的UUID，去掉了中间的横线
//        String simpleUUID = IdUtil.simpleUUID();
//        System.out.println("简单UUID: " + simpleUUID);
//
//        // 生成长度为10的纯大写英文随机码
//        String randomUpperCase = RandomUtil.randomStringUpper(10);
//        System.out.println("纯大写英文数字随机码: " + randomUpperCase);
//
//    }
}
