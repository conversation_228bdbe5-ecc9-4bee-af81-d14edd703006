package inks.service.sa.pms.controller;


import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.StringUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.LoginBody;
import inks.service.sa.pms.domain.pojo.SaRmsjustauthPojo;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import inks.service.sa.pms.service.PmsSaUserService;
import inks.service.sa.pms.service.SaRmsjustauthService;
import inks.service.sa.pms.service.SaRmsuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.text.ParseException;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * RMS用户(Sa_RmsUser)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-29 10:59:38
 */
@Api(tags = "S06M35B1:RMS用户")
@RestController
@RequestMapping("/S06M35B1")
public class S06M35B1Controller extends SaRmsuserController {
    private final String SCANLOGIN_CODE = "pmsrms_scanlogin_code:";
    @Resource
    private SaRmsuserService saRmsuserService;
    @Resource
    private SaRmsjustauthService saRmsjustauthService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private PmsSaUserService pmsSaUserService;

    @Value("${inks.justauth.api}")
    private String api;

    /**
     * 通过（手机号,邮箱）查询单条数据UserName只能是手机号或者邮箱
     *
     * @param username 主键
     * @return 单条数据
     */
    @ApiOperation(value = "获取RMS用户详细信息ByUserName,通过（手机号,邮箱）查询单条数据UserName只能是手机号或者邮箱", notes = "获取RMS用户详细信息ByUserName", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserName", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiRmsUser.List")
    public R<SaRmsuserPojo> getEntityByUserName(String username) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saRmsuserService.getEntityByUserName(username));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询该客户下所有用户", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByCustomer", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiRmsUser.List")
    public R<List<SaRmsuserPojo>> getPageListByCustomer(String groupid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saRmsuserService.getPageListByCustomer(groupid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 接受pms服务传来的openid,key为redis中的key
     */
    @ApiOperation(value = "绑定openid", notes = "绑定openid", produces = "application/json")
    @RequestMapping(value = "/scanWebRmsBind", method = RequestMethod.GET)
    public R<String> scanWebRmsBind(String openid, String key, String userid, String tenantid, HttpServletRequest request) throws ParseException {
//        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //判断当前租户下该用户openid是否已绑定
        SaRmsuserPojo pirmsuserPojo = saRmsuserService.getEntityByOpenid(openid);
        if (pirmsuserPojo != null) {
            return R.ok("用户已绑定openid,若要换绑请先解绑");
        }
        //未绑定：绑定userid和openid(插入Pi_RmsAuthJust)
        SaRmsjustauthPojo saRmsjustauthPojo = new SaRmsjustauthPojo();
        saRmsjustauthPojo.setUserid(userid);
        saRmsjustauthPojo.setAuthtype("openid");
        saRmsjustauthPojo.setAuthuuid(openid);
        saRmsjustauthPojo.setCreatebyid(userid);  // 创建者id
        saRmsjustauthPojo.setCreatedate(new Date());   // 创建时间
//        saRmsjustauthPojo.setLister(loginUser.getRealname());   // 制表
        saRmsjustauthPojo.setListerid(userid);    // 制表id
        saRmsjustauthPojo.setModifydate(new Date());   //修改时间
//        saRmsjustauthPojo.setTenantid(tenantid);   //租户id
        saRmsjustauthService.insert(saRmsjustauthPojo);
        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "扫码绑定openid成功");
        this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
        return R.ok("扫码绑定openid成功");
    }

    @ApiOperation(value = "解绑openid", notes = "解绑openid", produces = "application/json")
    @RequestMapping(value = "/scanWebRmsUnBind", method = RequestMethod.GET)
    public R<String> scanWebRmsUnBind(String openid, String key, String userid, String tenantid, HttpServletRequest request) throws ParseException {
//        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //判断当前租户下该用户openid是否已绑定
        SaRmsuserPojo pirmsuserPojo = saRmsuserService.getEntityByOpenid(openid);
        if (pirmsuserPojo == null) {
            return R.ok("用户未绑定openid,无需解绑");
        }
        //已绑定：解绑userid和openid(删除Pi_RmsAuthJust)
        saRmsjustauthService.deleteByOpenid(openid);
        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "扫码解绑openid成功");
        this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
        return R.ok("扫码解绑openid成功");
    }

    /**
     * @return R<PirmsuserPojo>
     * @Description 初始化密码
     * <AUTHOR>
     * @param[1] keys是userid  初始化为123456
     * @time 2023/4/24 12:46
     */
    @ApiOperation(value = "初始化密码", notes = "初始化密码", produces = "application/json")
    @RequestMapping(value = "/initPassword", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "PiRmsUser.Edit")
    public R<SaRmsuserPojo> initPassword(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaRmsuserPojo saRmsuserPojo = new SaRmsuserPojo();
            saRmsuserPojo.setUserid(key);
            saRmsuserPojo.setLister(loginUser.getRealname());   // 制表
            saRmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
//            saRmsuserPojo.setTenantid(loginUser.getTenantid());   //租户id
            saRmsuserPojo.setModifydate(new Date());   //修改时间
            //加密密码
            saRmsuserPojo.setUserpassword(AESUtil.Encrypt("123456"));
            return R.ok(this.saRmsuserService.update(saRmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //    ---------------------------------------Rms用户登陆-------------------------------------------
    @ApiOperation(value = "rms用户登陆", notes = "用户登陆", produces = "application/json")
    @RequestMapping(value = "/login", method = RequestMethod.POST)
    public R<Object> login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        try {
            LoginUser loginUser = saRmsuserService.login(loginBody.getUserName(), loginBody.getPassword(), request);
//            Map<String, Object> map = saRedisService.createToken(l);
            MyLoginUser myLoginUser = new MyLoginUser();
            BeanUtils.copyBeanProp(loginUser, myLoginUser);
            Map<String, Object> map = pmsSaUserService.myCreateToken(myLoginUser, request);
            return R.ok(map.get("loginuser"));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //------------------------------------扫码登录(WX小程序)---------------------------------
//    private final String SCANLOGIN_CODE = "scanlogin_code:";//扫码登录

    @DeleteMapping("logout")
    public R logout(HttpServletRequest request) throws ParseException {
        LoginUser loginUser = saRedisService.getLoginUser(request);
        if (StringUtils.isNotNull(loginUser)) {
            String username = loginUser.getUsername();
            // 删除用户缓存记录
            saRedisService.deleteObject(loginUser.getToken());
        }
        return R.ok("成功删除用户Token");
    }

    @ApiOperation(value = "获取绑定openid的二维码String", notes = "", produces = "application/json")
    @RequestMapping(value = "/getScanBindCode", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanBindCode() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成key 即UUID
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            //code100存入redis
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
            //返回前端type,date(key)
            Map<String, Object> map = new HashMap<>();
            map.put("type", "sarmsbind");
            Map<String, Object> dataMap = new HashMap<>();
//            dataMap.put("key", uuid);
            dataMap.put("api", api);
            dataMap.put("userid", loginUser.getUserid());
            dataMap.put("tenantid", loginUser.getTenantid());
            map.put("data", dataMap);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取解绑openid的二维码String", notes = "", produces = "application/json")
    @RequestMapping(value = "/getScanUnBindCode", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanUnBindCode() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成key 即UUID
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            //code100存入redis
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
            //返回前端type,date(key)
            Map<String, Object> map = new HashMap<>();
            map.put("type", "sarmsunbind");
            Map<String, Object> dataMap = new HashMap<>();
//            dataMap.put("key", uuid);
            dataMap.put("api", api);
            dataMap.put("userid", loginUser.getUserid());
            dataMap.put("tenantid", loginUser.getTenantid());
            map.put("data", dataMap);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "获取登录二维码String", notes = "获取登录二维码String", produces = "application/json")
    @RequestMapping(value = "/getScanLoginCode", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanLoginCode() {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成key 即UUID
            String uuid = inksSnowflake.getSnowflake().nextIdStr();
            //code100存入redis
            Map<String, Object> missionMsg = new HashMap<>();
            missionMsg.put("code", "100");
            missionMsg.put("msg", "任务开始处理");
            this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, uuid, missionMsg);
            //设置过期时间6分钟
//            saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
            //返回前端type,date(key)
            Map<String, Object> map = new HashMap<>();
            map.put("type", "webrmslogin");
            Map<String, Object> dataMap = new HashMap<>();
            dataMap.put("key", uuid);
            dataMap.put("api", api);
            map.put("data", dataMap);
            return R.ok(map);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 接受rms服务传来的openid,key为redis中的key
     */
    @ApiOperation(value = "扫码登录", notes = "扫码登录", produces = "application/json")
    @RequestMapping(value = "/scanLoginCode", method = RequestMethod.GET)
    public R<String> scanLoginCode(String openid, String key, HttpServletRequest request) throws ParseException {
        Map<String, Object> tokenMap = null;
        LoginUser loginUser = saRmsuserService.scanLogin(openid, key, request);
//            Map<String, Object> map = saRedisService.createToken(l);
        MyLoginUser myLoginUser = new MyLoginUser();
        BeanUtils.copyBeanProp(loginUser, myLoginUser);
        Map<String, Object> map = pmsSaUserService.myCreateToken(myLoginUser, request);        //设置当前计算任务进度
        Map<String, Object> missionMsg = new HashMap<>();
        missionMsg.put("code", "200");
        missionMsg.put("msg", "登录成功");
        missionMsg.put("token", tokenMap);
        this.saRedisService.setCacheMapValue(SCANLOGIN_CODE, key, missionMsg);
//        saRedisService.expire(SCANLOGIN_CODE, 60 * 6);
        return R.ok("扫码登录成功");
    }

    //    @ApiOperation(value = "rms用户登陆", notes = "用户登陆", produces = "application/json")
//    @RequestMapping(value = "/login222", method = RequestMethod.POST)
//    public R<Object> login222(@RequestBody LoginBody loginBody, HttpServletRequest request) {
//        try {
//            LoginUser l = service.login(loginBody.getUserName(), loginBody.getPassword(), request);
//            Map<String, Object> map = saRedisService.createToken(l);
//            return R.ok(map.get("loginuser"));
//        } catch (Exception e) {
//            return R.fail(e.getMessage());
//        }
//    }
    @ApiOperation(value = "获取扫码登录状态", notes = "获取扫码登录状态", produces = "application/json")
    @RequestMapping(value = "/getScanLoginState", method = RequestMethod.GET)
    public R<Map<String, Object>> getScanLoginState(@RequestParam String key) {
        Map<String, Object> scanLoginState = this.saRedisService.getCacheMapValue(SCANLOGIN_CODE, key);
        return R.ok(scanLoginState);
    }

}