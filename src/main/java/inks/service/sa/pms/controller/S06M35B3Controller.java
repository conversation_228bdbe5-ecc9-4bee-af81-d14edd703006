package inks.service.sa.pms.controller;


import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaCustrmsuserPojo;
import inks.service.sa.pms.service.SaCustrmsuserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 客户RMS关系表(Sa_CustRmsUser)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-29 11:04:27
 */
@Api(tags = "S06M35B3:客户RMS关系表")
@RestController
@RequestMapping("/S06M35B3")
public class S06M35B3Controller extends SaCustrmsuserController {
    @Resource
    private SaCustrmsuserService saCustrmsuserService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 通过userid获取RMS客户关系表详细信息", notes = "获取RMS客户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityByUserid", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiTenantRmsUser.List")
    public R<SaCustrmsuserPojo> getEntityByUserid(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustrmsuserService.getEntityByUserid(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 通过userid获取RMS客户关系表详细信息", notes = "获取RMS租户关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityMapByUserid", method = RequestMethod.GET)
    public R<Map<String, Object>> getEntityMapByUserid(String key) {
        try {
            // 获得用户数据
            //LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaCustrmsuserPojo entityByUserid = this.saCustrmsuserService.getEntityByUserid(key);
            Map<String, Object> stringObjectMap = BeanUtils.beanToMap(entityByUserid);
            return R.ok(stringObjectMap);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增RMS(租户-用户)关联
     *
     * @param userid PiRmsUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增RMS(租户-用户)关联", notes = "新增RMS租户关系表", produces = "application/json")
    @RequestMapping(value = "/createByUserId", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "PiTenantRmsUser.Add")
    public R<SaCustrmsuserPojo> createByUserId(String userid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaCustrmsuserPojo saCustrmsuserPojo = new SaCustrmsuserPojo();
            //(租户-用户)关联
            saCustrmsuserPojo.setUserid(userid);
            saCustrmsuserPojo.setUsername(loginUser.getUsername());
            saCustrmsuserPojo.setTenantid(loginUser.getTenantid());
            saCustrmsuserPojo.setRealname(loginUser.getRealname());
            saCustrmsuserPojo.setIsadmin(loginUser.getIsadmin());
            if (loginUser.getTenantinfo() != null) {
                saCustrmsuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                saCustrmsuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                saCustrmsuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                saCustrmsuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            saCustrmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saCustrmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saCustrmsuserPojo.setCreatedate(new Date());   // 创建时间
            saCustrmsuserPojo.setLister(loginUser.getRealname());   // 制表
            saCustrmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            saCustrmsuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saCustrmsuserService.insert(saCustrmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "根据当前用户获得租户关系表", notes = "根据当前用户获得租户关系表", produces = "application/json")
    @RequestMapping(value = "/getListByUser", method = RequestMethod.GET)
    public R<List<SaCustrmsuserPojo>> getListByUser() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustrmsuserService.getListByUserid(loginUser.getUserid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 新增RMS(租户-用户)关联
     *
     * @param json PiRmsUser.userid
     * @return 新增结果
     */
    @ApiOperation(value = " 新增RMS(租户-用户)关联", notes = "新增RMS租户关系表", produces = "application/json")
    @RequestMapping(value = "/createRmsUser", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "PiTenantRmsUser.Add")
    public R<SaCustrmsuserPojo> createRmsUser(@RequestBody String json) {
        try {
            SaCustrmsuserPojo saCustrmsuserPojo = JSONArray.parseObject(json, SaCustrmsuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCustrmsuserPojo.setTenantid(loginUser.getTenantid());
            if (loginUser.getTenantinfo() != null) {
                saCustrmsuserPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
                saCustrmsuserPojo.setDeptcode(loginUser.getTenantinfo().getDeptcode());
                saCustrmsuserPojo.setDeptid(loginUser.getTenantinfo().getDeptid());
                saCustrmsuserPojo.setDeptname(loginUser.getTenantinfo().getDeptname());
            }
            saCustrmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saCustrmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saCustrmsuserPojo.setCreatedate(new Date());   // 创建时间
            saCustrmsuserPojo.setLister(loginUser.getRealname());   // 制表
            saCustrmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            saCustrmsuserPojo.setModifydate(new Date());   //修改时间

            //判断是否传入userid;有，修改；无，新增
            SaCustrmsuserPojo saCustrmsuser = saCustrmsuserService.createRmsUser(saCustrmsuserPojo);
            return R.ok(saCustrmsuser);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


}