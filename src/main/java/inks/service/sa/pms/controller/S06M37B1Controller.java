package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaIdeapoolPojo;
import inks.service.sa.pms.service.SaIdeapoolService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 创意池(Sa_IdeaPool)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-04 15:23:23
 */
@RestController
@RequestMapping("S06M37B1")
@Api(tags = "S06M37B1:创意池")
public class S06M37B1Controller extends SaIdeapoolController {
    @Resource
    private SaIdeapoolService saIdeapoolService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取All创意池详细信息", notes = "获取创意池详细信息", produces = "application/json")
    @RequestMapping(value = "/getAllList", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_IdeaPool.List")
    public R<List<SaIdeapoolPojo>> getAllList() {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saIdeapoolService.getAllList());
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 publicmark=1 or 经办人=user or lister= user;", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListByUser", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_IdeaPool.List")
    public R<PageInfo<SaIdeapoolPojo>> getPageListByUser(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_IdeaPool.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String userid = loginUser.getUserid();
            String qpfilter = " and (Sa_IdeaPool.PublicMark=1 or Sa_IdeaPool.Listerid='" + userid + "' or Sa_IdeaPool.Operatorid='" + userid + "') ";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saIdeapoolService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
