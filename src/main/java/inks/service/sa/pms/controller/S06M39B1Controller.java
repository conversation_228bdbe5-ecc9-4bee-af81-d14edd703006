package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDbdesignPojo;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo;
import inks.service.sa.pms.service.SaDbdesignService;
import inks.service.sa.pms.utils.SqlParser;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 表格设计(Sa_DbDesign)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-15 09:54:21
 */
@RestController
@RequestMapping("S06M39B1")
@Api(tags = "S06M39B1:表格设计")
public class S06M39B1Controller extends SaDbdesignController {
    @Resource
    private SaDbdesignService saDbdesignService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value=" 建表语句转为表格设计主子表信息", notes="", produces="application/json")
    @RequestMapping(value="/saveTable",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Add")
    public R<SaDbdesignPojo> saveTable(@RequestBody String createTableSql,@RequestParam(required = false) String fnid) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaDbdesignPojo saDbdesignPojo = SqlParser.parseCreateTableSql(createTableSql, loginUser);
            if (StringUtils.isNotBlank(fnid)) {
                saDbdesignPojo.setFnid(fnid);
            }
            List<SaDbdesignitemPojo> items = SqlParser.parseFieldInfo(createTableSql, loginUser);
            saDbdesignPojo.setItem(items);
            saDbdesignPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDbdesignPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDbdesignPojo.setCreatedate(new Date());   // 创建时间
            saDbdesignPojo.setLister(loginUser.getRealname());   // 制表
            saDbdesignPojo.setListerid(loginUser.getUserid());    // 制表id
            saDbdesignPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDbdesignService.insert(saDbdesignPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value=" 勾选字段 生成插入字段语句 传入itemids []", notes="", produces="application/json")
    @RequestMapping(value="/alter",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Add")
    public R<String> alter(@RequestBody String json) {
        try {
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> itemids = JSON.parseObject(json, new TypeReference<List<String>>() {
            });
            return R.ok(this.saDbdesignService.alter(itemids));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

}
