package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.service.SaApiinfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 接口信息表(Sa_ApiInfo)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-15 17:09:56
 */
@RestController
@RequestMapping("S06M40B1")
@Api(tags = "S06M40B1:接口信息")
public class S06M40B1Controller extends SaApiinfoController {
    @Resource
    private SaApiinfoService saApiinfoService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 从swagger导入到接口信息表", notes = "", produces = "application/json")
    @RequestMapping(value = "/importSwagger", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ApiInfo.List")
    public R<String> importSwagger(String swaggerUrl, @RequestParam(required = false) String fnid, @RequestParam(required = false) String fncode) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saApiinfoService.importSwagger(swaggerUrl, fnid, fncode,loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}
