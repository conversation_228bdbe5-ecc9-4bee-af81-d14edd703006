package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import inks.common.core.constant.CacheConstants;
import inks.common.core.domain.*;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.IpUtils;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.domain.pojo.SaLogPojo;
import inks.sa.common.core.service.SaLogService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaRmsjustauthPojo;
import inks.service.sa.pms.domain.pojo.SaSqlecnPojo;
import inks.service.sa.pms.service.SaRmsjustauthService;
import inks.service.sa.pms.service.SaSqlecnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.apache.commons.lang3.StringUtils;
import org.apache.velocity.VelocityContext;
import org.apache.velocity.app.VelocityEngine;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.io.StringWriter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * SQL变更(Sa_SqlEcn)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-19 14:27:49
 */
@RestController
@RequestMapping("S06M41B1")
@Api(tags = "S06M41B1:SQL变更")
public class S06M41B1Controller extends SaSqlecnController {

    @Resource
    private SaSqlecnService saSqlecnService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaRmsjustauthService saRmsjustauthService;
    @Resource
    private S06M36B1Controller s06M36B1Controller;
    @Resource
    private SaLogService saLogService;


    @ApiOperation(value = "拉取已审核的SQL变更列表 根据code+时间戳 size可选拉取条数,不传则默认拉取10条数据 ", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/pullList", method = RequestMethod.POST)
    public R<List<SaSqlecnPojo>> pullList(@RequestBody(required = false) String json, String code, Long timestamp, @RequestParam(defaultValue = "10") int size, HttpServletRequest request) {
        try {
            if (StringUtils.isBlank(code)) {
                return R.fail("code不能为空");
            }
            // 获得用户数据
            Map<String, Object> mapFromSaDeli = JSON.parseObject(json, new TypeReference<Map<String, Object>>() {
            });
            String userid = "";
            String realname = "";
            if (mapFromSaDeli != null) {
                userid = String.valueOf(mapFromSaDeli.get("userid"));
                realname = String.valueOf(mapFromSaDeli.get("realname"));
            }
            // 记录拉取日志记录
            SaLogPojo saLogPojo = new SaLogPojo();
            saLogPojo.setLogtype("Sa_SqlEcn");
            saLogPojo.setLoglevel("INFO");
            saLogPojo.setModule(code);//所属模块
            saLogPojo.setUserid(userid == null ? "" : userid);
            saLogPojo.setRealname(realname == null ? "" : realname);
            saLogPojo.setIpaddress(IpUtils.getIpAddr(request)); // 外网IP
            saLogPojo.setHttpmethod("GET");
            //请求的URL地址
            saLogPojo.setRequesturl("/S06M41B1/pullList");
            String message = "拉取SQL变更列表：code=" + code + ",timestamp=" + timestamp + ",本次拉取" + size + "条";
            saLogPojo.setMessage(message);
            saLogService.insert(saLogPojo);
            // code转List<String>
            List<String> codeList = Arrays.asList(code.split(","));
            return R.ok(this.saSqlecnService.pullList(codeList, timestamp, size));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    // key：需求提报单id apprid:redis中存放的模板对象 apprrecPojo.getDatatemp()是模板内容
    @ApiOperation(value = "发出第三方审批", notes = "发出第三方审批wex/ding", produces = "application/json")
    @RequestMapping(value = "/sendapprovel", method = RequestMethod.GET)
    public R<ApprrecPojo> sendapprovel(String key, String apprid, String type, String remark) {
        try {
            if (type == null) type = "wxe";  // 默认走企业微信
            String verifyKey = CacheConstants.APPR_CODES_KEY + apprid;
            //获取token
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //从redis中获取模板对象
            // Object obj = redisService.getCacheObject(verifyKey);
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            ApprovePojo approvePojo = new ApprovePojo();
            SaSqlecnPojo saDemandsubmitPojo = this.saSqlecnService.getEntity(key);
            approvePojo.setObject(saDemandsubmitPojo);
            // 发起oms审批,先判断是否正在审批 (最下面发起oms审批成功后需设置OaFlowMark=1)
            if (Objects.equals(saDemandsubmitPojo.getOaflowmark(), 1)) {
                return R.fail("该单据已发起OA审批");
            }
            if ("oms".equals(type)) {
                //创建VM数据对象
                VelocityContext context = new VelocityContext();
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
//                String data = JSONObject.toJSONString(approvePojo.getObject());
//                apprrecPojo.setDatatemp(data);
            } else {
                //创建VM数据对象
                VelocityContext context = new VelocityContext();

                //获得第三方账号  默认走企业微信 type="wxe"
                SaRmsjustauthPojo rmsjustauthPojo = saRmsjustauthService.getEntityByUserid(loginUser.getUserid(), type);
                if (rmsjustauthPojo == null) {
                    throw new Exception("当前账号未关联到第三方账号 Sa_RmsJustAuth");
                }
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(rmsjustauthPojo, justauthPojo);
                approvePojo.setCreatoruserid(justauthPojo.getAuthuuid());
                approvePojo.setUserid(justauthPojo.getAuthuuid());
                approvePojo.setModelcode(apprrecPojo.getTemplateid());
                context.put("approvePojo", approvePojo);
                String str = apprrecPojo.getDatatemp();
                // 初始化并取得Velocity引擎
                VelocityEngine ve = new VelocityEngine();
                ve.init();
                // 转换输出
                StringWriter writer = new StringWriter();
                ve.evaluate(context, writer, "", str); // 关键方法
                //写回String
                str = writer.toString();
                apprrecPojo.setDatatemp(str);
            }


            //新建审批记录
            apprrecPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
            apprrecPojo.setApprname("订单审批");
            apprrecPojo.setResultcode("");
            apprrecPojo.setBillid(key);    // 单据ID
            apprrecPojo.setUserid("");
            apprrecPojo.setApprtype("");
            apprrecPojo.setCreateby(loginUser.getRealname());
            apprrecPojo.setCreatebyid(loginUser.getUserid());
            apprrecPojo.setCreatedate(new Date());
            apprrecPojo.setLister(loginUser.getRealname());
            apprrecPojo.setListerid(loginUser.getUserid());
            apprrecPojo.setModifydate(new Date());
            apprrecPojo.setTenantid(loginUser.getTenantid());
            //发起Flowable审批时加入的评论备注
            apprrecPojo.setRemark(remark == null ? "" : remark);
            //将企业微信审批信息存入redis
            String CachKey = CacheConstants.APPR_CODES_KEY + apprrecPojo.getId();
            saRedisService.setCacheObject(CachKey, apprrecPojo, 60 * 12, TimeUnit.MINUTES);
            if ("wxe".equals(type)) {
                R r = s06M36B1Controller.sendapprovel(apprrecPojo.getId(), loginUser.getTenantid());
                if (r.getCode() != 200) {
                    return R.fail("发起审批失败" + r.getMsg());
                }
            }
            //else if ("ding".equals(type)) {
            //    R r = this.utilsFeignService.dingapprovel(apprrecPojo.getId(), loginUser.getTenantid());
            //    if (r.getCode() != 200) {
            //        return R.fail("发起审批失败" + r.getMsg());
            //    }
            //}
            //else {//type为oms
            //    R r = this.utilsFeignService.omsapprovel(apprrecPojo.getId(), loginUser.getTenantid(), loginUser.getToken());
            //    if (r.getCode() != 200) {
            //        return R.fail("发起审批失败" + r.getMsg());
            //    }
            //}
            // 发起oms审批成功,需设置OaFlowMark=1 并更新单据
            saDemandsubmitPojo.setOaflowmark(1);
            saSqlecnService.updateOaflowmark(saDemandsubmitPojo);
            return R.ok(apprrecPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "审批回调修改状态", notes = "审批回调修改状态", produces = "application/json")
    @RequestMapping(value = "/justapprovel", method = RequestMethod.GET)
    public R<SaSqlecnPojo> justapprovel(String key, String type, String approved) {
        try {
            System.out.println("审核通过,写入审核信息");
            //1.读取审批记录
            String verifyKey = CacheConstants.APPR_CODES_KEY + key;
            ApprrecPojo apprrecPojo = saRedisService.getCacheObject(verifyKey, ApprrecPojo.class);
            //2.1 获得单据数据
            SaSqlecnPojo saDemandsubmitPojo = this.saSqlecnService.getEntity(apprrecPojo.getBillid());
            //3. 写入审核批
            //获得第三方账号
            if (type == null) type = "wxe";
            // oms审批即将完成,需设置OaFlowMark=0
            saDemandsubmitPojo.setOaflowmark(0);
            saSqlecnService.updateOaflowmark(saDemandsubmitPojo);//只更新oaflowmark
            // 若oms审批点击拒绝(或由发起人取消发起),则设置完OaFlowMark=0直接结束方法
            if ("false".equals(approved)) {
                return R.ok();
            }
            if ("oms".equals(type)) {
                // 点击同意审批：审批人字段赋值, if包裹外面的approval方法会进行审核
                saDemandsubmitPojo.setAssessorid(apprrecPojo.getUserid());
                saDemandsubmitPojo.setAssessor(apprrecPojo.getRealname()); //审核员
            } else {
                SaRmsjustauthPojo rmsjustauthPojo = saRmsjustauthService.getJustauthByUuid(apprrecPojo.getCallbackuuid(), type);
                JustauthPojo justauthPojo = new JustauthPojo();
                org.springframework.beans.BeanUtils.copyProperties(rmsjustauthPojo, justauthPojo);

                saDemandsubmitPojo.setAssessorid(justauthPojo.getUserid());
                saDemandsubmitPojo.setAssessor(justauthPojo.getRealname()); //审核员
            }
            saDemandsubmitPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saSqlecnService.approval(saDemandsubmitPojo));
        } catch (Exception e) {
            System.out.println("写入审核失败：" + e.getMessage());
            return R.fail(e.getMessage());
        }
    }

}
