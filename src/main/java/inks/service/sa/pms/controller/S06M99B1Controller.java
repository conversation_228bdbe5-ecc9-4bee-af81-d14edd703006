package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.utils.ServletUtils;
import inks.sa.common.core.service.SaOperlogService;
import inks.sa.common.core.service.SaRedisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 操作日志(Sa_OperLog)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-27 10:26:19
 */
@RestController
@RequestMapping("/S06M99B1")
@Api(tags = "S06M99B1:操作日志")
public class S06M99B1Controller extends SaOperlogController {
    @Resource
    private SaOperlogService saOperlogService;
    @Resource
    private SaRedisService saRedisService;

    /**
     * 根据时间段删除日志数据
     *
     * @param json 筛选条件
     * @return 删除是否成功
     */
    @ApiOperation(value = "根据时间段删除操作日志", notes = "根据时间段删除操作日志", produces = "application/json")
    @RequestMapping(value = "/deleteByTime", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "CiOperLog.Delete")
    public R<String> deleteByTime(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            int i = this.saOperlogService.deleteByTime(queryParam);
            return R.ok("成功删除" + i + "行日志数据");
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}
