package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaAcceptancePojo;
import inks.service.sa.pms.service.SaAcceptanceService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Random;

/**
 * (Sa_Acceptance)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-09 16:11:12
 */
//@RestController
//@RequestMapping("saAcceptance")
public class SaAcceptanceController {

    private final static Logger logger = LoggerFactory.getLogger(SaAcceptanceController.class);

    @Resource
    private SaAcceptanceService saAcceptanceService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取详细信息", notes = "获取详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaAcceptancePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saAcceptanceService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaAcceptancePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Acceptance.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saAcceptanceService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增", notes = "新增", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaAcceptancePojo> create(@RequestBody String json) {
        try {
            SaAcceptancePojo saAcceptancePojo = JSONArray.parseObject(json, SaAcceptancePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + String.format("%02d", new Random().nextInt(100));
            saAcceptancePojo.setRefno(refNo);
            saAcceptancePojo.setCreateby(loginUser.getRealName());   // 创建者
            saAcceptancePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saAcceptancePojo.setCreatedate(new Date());   // 创建时间
            saAcceptancePojo.setLister(loginUser.getRealname());   // 制表
            saAcceptancePojo.setListerid(loginUser.getUserid());    // 制表id  
            saAcceptancePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saAcceptanceService.insert(saAcceptancePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改", notes = "修改", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaAcceptancePojo> update(@RequestBody String json) {
        try {
            SaAcceptancePojo saAcceptancePojo = JSONArray.parseObject(json, SaAcceptancePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saAcceptancePojo.setLister(loginUser.getRealname());   // 制表
            saAcceptancePojo.setListerid(loginUser.getUserid());    // 制表id  
            saAcceptancePojo.setModifydate(new Date());   //修改时间
//            saAcceptancePojo.setAssessor(""); // 审核员
//            saAcceptancePojo.setAssessorid(""); // 审核员id
//            saAcceptancePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saAcceptanceService.update(saAcceptancePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除", notes = "删除", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saAcceptanceService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaAcceptancePojo saAcceptancePojo = this.saAcceptanceService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saAcceptancePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

