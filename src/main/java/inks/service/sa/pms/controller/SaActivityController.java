package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaActivityPojo;
import inks.service.sa.pms.domain.pojo.SaActivityitemPojo;
import inks.service.sa.pms.domain.pojo.SaActivityitemdetailPojo;
import inks.service.sa.pms.domain.pojo.SaActivityplanPojo;
import inks.service.sa.pms.service.SaActivityService;
import inks.service.sa.pms.service.SaActivityitemService;
import inks.service.sa.pms.service.SaActivityplanService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 活动主表(Sa_Activity)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:08
 */
//@RestController
//@RequestMapping("saActivity")
public class SaActivityController {
    @Resource
    private SaActivityplanService saActivityplanService;

    @Resource
    private SaActivityService saActivityService;
    @Resource
    private SaActivityitemService saActivityitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = " 获取活动主表详细信息", notes = "获取活动主表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Activity.List")
    public R<SaActivityPojo> getEntity(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivityService.getEntity(key));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.List")
    public R<PageInfo<SaActivityitemdetailPojo>> getPageList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Sa_Activity.CreateDate");
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.saActivityService.getPageList(queryParam));
    }


    @ApiOperation(value = " 获取活动主表详细信息", notes = "获取活动主表详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Activity.List")
    public R<SaActivityPojo> getBillEntity(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivityService.getBillEntity(key));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.List")
    public R<PageInfo<SaActivityPojo>> getBillList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Sa_Activity.CreateDate");
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.saActivityService.getBillList(queryParam));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.List")
    public R<PageInfo<SaActivityPojo>> getPageTh(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Sa_Activity.CreateDate");
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.saActivityService.getPageTh(queryParam));
    }


    @ApiOperation(value = " 新增活动主表", notes = "新增活动主表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.Add")
    public R<SaActivityPojo> create(@RequestBody String json) {
        SaActivityPojo saActivityPojo = JSONArray.parseObject(json, SaActivityPojo.class);
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //生成单据编码
        String refNo = saBillcodeService.getSerialNo("DxxMxxB1", loginUser.getTenantid(), "Sa_Activity");
        saActivityPojo.setRefno(refNo);
        saActivityPojo.setCreateby(loginUser.getRealName());   // 创建者
        saActivityPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        saActivityPojo.setCreatedate(new Date());   // 创建时间
        saActivityPojo.setLister(loginUser.getRealname());   // 制表
        saActivityPojo.setListerid(loginUser.getUserid());    // 制表id
        saActivityPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saActivityService.insert(saActivityPojo));
    }


    @ApiOperation(value = "修改活动主表", notes = "修改活动主表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.Edit")
    public R<SaActivityPojo> update(@RequestBody String json) {
        SaActivityPojo saActivityPojo = JSONArray.parseObject(json, SaActivityPojo.class);
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        saActivityPojo.setLister(loginUser.getRealname());   // 制表
        saActivityPojo.setListerid(loginUser.getUserid());    // 制表id
        saActivityPojo.setModifydate(new Date());   //修改时间
        saActivityPojo.setAssessor(""); //审核员
        saActivityPojo.setAssessorid(""); //审核员
        saActivityPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.saActivityService.update(saActivityPojo));
    }


    @ApiOperation(value = "删除活动主表", notes = "删除活动主表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Activity.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivityService.delete(key));
    }

    /*子表操作 */

    @ApiOperation(value = " 新增活动主表Item", notes = "新增活动主表Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.Add")
    public R<SaActivityitemPojo> createItem(@RequestBody String json) {
        SaActivityitemPojo saActivityitemPojo = JSONArray.parseObject(json, SaActivityitemPojo.class);
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivityitemService.insert(saActivityitemPojo));
    }

    @ApiOperation(value = " 修改活动主表Item", notes = "修改活动主表Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.Edit")
    public R<SaActivityitemPojo> updateItem(@RequestBody String json) {
        SaActivityitemPojo saActivityitemPojo = JSONArray.parseObject(json, SaActivityitemPojo.class);
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivityitemService.update(saActivityitemPojo));
    }

    @ApiOperation(value = "删除活动主表Item", notes = "删除活动主表Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Activity.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saActivityitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //plan子表操作
    @ApiOperation(value = " 新增活动子表Plan", notes = "", produces = "application/json")
    @RequestMapping(value = "/createPlan", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.Add")
    public R<SaActivityplanPojo> createPlan(@RequestBody String json) {
        SaActivityplanPojo saActivityplanPojo = JSONArray.parseObject(json, SaActivityplanPojo.class);
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivityplanService.insert(saActivityplanPojo));
    }

    @ApiOperation(value = " 修改活动子表Plan", notes = "", produces = "application/json")
    @RequestMapping(value = "/updatePlan", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Activity.Edit")
    public R<SaActivityplanPojo> updatePlan(@RequestBody String json) {
        SaActivityplanPojo saActivityplanPojo = JSONArray.parseObject(json, SaActivityplanPojo.class);
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivityplanService.update(saActivityplanPojo));
    }

    @ApiOperation(value = "删除活动子表Plan", notes = "", produces = "application/json")
    @RequestMapping(value = "/deletePlan", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Activity.Delete")
    public R<Integer> deletePlan(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivityplanService.delete(key));
    }


    @ApiOperation(value = "审核活动主表", notes = "审核活动主表", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Activity.Approval")
    public R<SaActivityPojo> approval(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        SaActivityPojo saActivityPojo = this.saActivityService.getEntity(key);
        if (saActivityPojo.getAssessor().isEmpty()) {
            saActivityPojo.setAssessor(loginUser.getRealname()); //审核员
            saActivityPojo.setAssessorid(loginUser.getUserid()); //审核员id
        } else {
            saActivityPojo.setAssessor(""); //审核员
            saActivityPojo.setAssessorid(""); //审核员
        }
        saActivityPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.saActivityService.approval(saActivityPojo));
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Activity.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaActivityPojo saActivityPojo = this.saActivityService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saActivityPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saActivityPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaActivityitemPojo saActivityitemPojo = new SaActivityitemPojo();
                    saActivityPojo.getItem().add(saActivityitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saActivityPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    // 测试GlobalExceptionHandler是否生效的接口
    @ApiOperation(value = "测试全局异常处理器", notes = "用于测试GlobalExceptionHandler是否能捕获异常", produces = "application/json")
    @RequestMapping(value = "/testGlobalException", method = RequestMethod.GET)
    public R<String> testGlobalException(@RequestParam(required = false) String type) {
        if ("runtime".equals(type)) {
            // 测试RuntimeException
            throw new RuntimeException("测试RuntimeException - 应该被GlobalExceptionHandler捕获");
        } else if ("nullpointer".equals(type)) {
            // 测试NullPointerException
            String str = null;
            return R.ok(str.length() + ""); // 故意触发空指针异常
        } else if ("json".equals(type)) {
            // 测试JSON解析异常
            return R.ok(JSONArray.parseObject("invalid json", SaActivityplanPojo.class).toString());
        } else {
            // 测试普通异常
            throw new IllegalArgumentException("测试IllegalArgumentException - 应该被GlobalExceptionHandler捕获");
        }
    }
}
