package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivitystagePojo;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemPojo;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemdetailPojo;
import inks.service.sa.pms.service.SaActivitystageService;
import inks.service.sa.pms.service.SaActivitystageitemService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaBillcodeService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 活动阶段表(Sa_ActivityStage)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:12
 */
//@RestController
//@RequestMapping("saActivitystage")
public class SaActivitystageController {

    @Resource
    private SaActivitystageService saActivitystageService;
    @Resource
    private SaActivitystageitemService saActivitystageitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaActivitystageController.class);
    

    @ApiOperation(value=" 获取活动阶段表详细信息", notes="获取活动阶段表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.List")
    public R<SaActivitystagePojo> getEntity(String key) {
      try {
           // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saActivitystageService.getEntity(key));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.List")
    public R<PageInfo<SaActivitystageitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_ActivityStage.CreateDate");
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saActivitystageService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value=" 获取活动阶段表详细信息", notes="获取活动阶段表详细信息", produces="application/json")
    @RequestMapping(value="/getBillEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.List")
    public R<SaActivitystagePojo> getBillEntity(String key) {
      try {
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saActivitystageService.getBillEntity(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getBillList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.List")
    public R<PageInfo<SaActivitystagePojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
           if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_ActivityStage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saActivitystageService.getBillList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageTh",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.List")
    public R<PageInfo<SaActivitystagePojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_ActivityStage.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saActivitystageService.getPageTh(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value=" 新增活动阶段表", notes="新增活动阶段表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.Add")
    public R<SaActivitystagePojo> create(@RequestBody String json) {
        try {
       SaActivitystagePojo saActivitystagePojo = JSONArray.parseObject(json,SaActivitystagePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saActivitystagePojo.setCreateby(loginUser.getRealName());   // 创建者
            saActivitystagePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saActivitystagePojo.setCreatedate(new Date());   // 创建时间
            saActivitystagePojo.setLister(loginUser.getRealname());   // 制表
            saActivitystagePojo.setListerid(loginUser.getUserid());    // 制表id            
            saActivitystagePojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saActivitystageService.insert(saActivitystagePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="修改活动阶段表", notes="修改活动阶段表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.Edit")
    public R<SaActivitystagePojo> update(@RequestBody String json) {
        try {
         SaActivitystagePojo saActivitystagePojo = JSONArray.parseObject(json,SaActivitystagePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saActivitystagePojo.setLister(loginUser.getRealname());   // 制表
            saActivitystagePojo.setListerid(loginUser.getUserid());    // 制表id   
            saActivitystagePojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saActivitystageService.update(saActivitystagePojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除活动阶段表", notes="删除活动阶段表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.Delete")
    public R<Integer> delete(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saActivitystageService.delete(key));
   }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value=" 新增活动阶段表Item", notes="新增活动阶段表Item", produces="application/json") 
    @RequestMapping(value="/createItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.Add")
    public R<SaActivitystageitemPojo> createItem(@RequestBody String json) {
       try {
     SaActivitystageitemPojo saActivitystageitemPojo = JSONArray.parseObject(json,SaActivitystageitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivitystageitemService.insert(saActivitystageitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value=" 修改活动阶段表Item", notes="修改活动阶段表Item", produces="application/json") 
    @RequestMapping(value="/updateItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.Edit")
    public R<SaActivitystageitemPojo> updateItem(@RequestBody String json) {
       try {
     SaActivitystageitemPojo saActivitystageitemPojo = JSONArray.parseObject(json,SaActivitystageitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saActivitystageitemService.update(saActivitystageitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }    

    @ApiOperation(value="删除活动阶段表Item", notes="删除活动阶段表Item", produces="application/json")   
    @RequestMapping(value="/deleteItem",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.Delete")
    public R<Integer> deleteItem(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saActivitystageitemService.delete(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ActivityStage.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaActivitystagePojo saActivitystagePojo = this.saActivitystageService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saActivitystagePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
          content = reportsPojo.getRptdata();
        } else {
          throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
       if(reportsPojo.getPagerow()>0){
       int index=0;
      // 取行余数
      index =saActivitystagePojo.getItem().size()%reportsPojo.getPagerow();
      if(index>0){
        // 补全空白行
        for(int i=0;i<reportsPojo.getPagerow()-index;i++){
            SaActivitystageitemPojo saActivitystageitemPojo = new SaActivitystageitemPojo();
            saActivitystagePojo.getItem().add(saActivitystageitemPojo);
          }
      }
     }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saActivitystagePojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

