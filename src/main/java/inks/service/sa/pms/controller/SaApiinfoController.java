package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaApiinfoPojo;
import inks.service.sa.pms.service.SaApiinfoService;
import inks.sa.common.core.service.SaRedisService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 接口信息表(Sa_ApiInfo)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-15 17:09:56
 */
//@RestController
//@RequestMapping("saApiinfo")
public class SaApiinfoController {
    @Resource
    private SaApiinfoService saApiinfoService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaApiinfoController.class);


    @ApiOperation(value=" 获取接口信息表详细信息", notes="获取接口信息表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ApiInfo.List")
    public R<SaApiinfoPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saApiinfoService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ApiInfo.List")
    public R<PageInfo<SaApiinfoPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_ApiInfo.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saApiinfoService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增接口信息表", notes="新增接口信息表", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ApiInfo.Add")
    public R<SaApiinfoPojo> create(@RequestBody String json) {
        try {
            SaApiinfoPojo saApiinfoPojo = JSONArray.parseObject(json,SaApiinfoPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saApiinfoPojo.setCreateby(loginUser.getRealName());   // 创建者
            saApiinfoPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saApiinfoPojo.setCreatedate(new Date());   // 创建时间
            saApiinfoPojo.setLister(loginUser.getRealname());   // 制表
            saApiinfoPojo.setListerid(loginUser.getUserid());    // 制表id
            saApiinfoPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saApiinfoService.insert(saApiinfoPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改接口信息表", notes="修改接口信息表", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ApiInfo.Edit")
    public R<SaApiinfoPojo> update(@RequestBody String json) {
        try {
            SaApiinfoPojo saApiinfoPojo = JSONArray.parseObject(json,SaApiinfoPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saApiinfoPojo.setLister(loginUser.getRealname());   // 制表
            saApiinfoPojo.setListerid(loginUser.getUserid());    // 制表id
            saApiinfoPojo.setModifydate(new Date());   //修改时间
    //            saApiinfoPojo.setAssessor(""); // 审核员
    //            saApiinfoPojo.setAssessorid(""); // 审核员id
    //            saApiinfoPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saApiinfoService.update(saApiinfoPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除接口信息表", notes="删除接口信息表", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ApiInfo.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saApiinfoService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ApiInfo.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaApiinfoPojo saApiinfoPojo = this.saApiinfoService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saApiinfoPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

