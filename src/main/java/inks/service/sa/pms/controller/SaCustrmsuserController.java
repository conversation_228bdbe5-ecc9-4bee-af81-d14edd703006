package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaCustrmsuserPojo;
import inks.service.sa.pms.service.SaCustrmsuserService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 客户RMS关系表(Sa_CustRmsUser)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-29 11:04:27
 */
//@RestController
//@RequestMapping("saCustrmsuser")
public class SaCustrmsuserController {
    private final static Logger logger = LoggerFactory.getLogger(SaCustrmsuserController.class);
    @Resource
    private SaCustrmsuserService saCustrmsuserService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取客户RMS关系表详细信息", notes = "获取客户RMS关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_CustRmsUser.List")
    public R<SaCustrmsuserPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustrmsuserService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_CustRmsUser.List")
    public R<PageInfo<SaCustrmsuserPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_CustRmsUser.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saCustrmsuserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增客户RMS关系表", notes = "新增客户RMS关系表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_CustRmsUser.Add")
    public R<SaCustrmsuserPojo> create(@RequestBody String json) {
        try {
            SaCustrmsuserPojo saCustrmsuserPojo = JSONArray.parseObject(json, SaCustrmsuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCustrmsuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saCustrmsuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saCustrmsuserPojo.setCreatedate(new Date());   // 创建时间
            saCustrmsuserPojo.setLister(loginUser.getRealname());   // 制表
            saCustrmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            saCustrmsuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saCustrmsuserService.insert(saCustrmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改客户RMS关系表", notes = "修改客户RMS关系表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_CustRmsUser.Edit")
    public R<SaCustrmsuserPojo> update(@RequestBody String json) {
        try {
            SaCustrmsuserPojo saCustrmsuserPojo = JSONArray.parseObject(json, SaCustrmsuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCustrmsuserPojo.setLister(loginUser.getRealname());   // 制表
            saCustrmsuserPojo.setListerid(loginUser.getUserid());    // 制表id
            saCustrmsuserPojo.setModifydate(new Date());   //修改时间
            //            saCustrmsuserPojo.setAssessor(""); // 审核员
            //            saCustrmsuserPojo.setAssessorid(""); // 审核员id
            //            saCustrmsuserPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saCustrmsuserService.update(saCustrmsuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除客户RMS关系表", notes = "删除客户RMS关系表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_CustRmsUser.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustrmsuserService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_CustRmsUser.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaCustrmsuserPojo saCustrmsuserPojo = this.saCustrmsuserService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saCustrmsuserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

