package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaCustscmuserPojo;
import inks.service.sa.pms.service.SaCustscmuserService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 客户SCM关系表(Sa_CustScmUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:30
 */
//@RestController
//@RequestMapping("saCustscmuser")
public class SaCustscmuserController {

    private final static Logger logger = LoggerFactory.getLogger(SaCustscmuserController.class);

    @Resource
    private SaCustscmuserService saCustscmuserService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取客户SCM关系表详细信息", notes = "获取客户SCM关系表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_CustScmUser.List")
    public R<SaCustscmuserPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustscmuserService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_CustScmUser.List")
    public R<PageInfo<SaCustscmuserPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_CustScmUser.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saCustscmuserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增客户SCM关系表", notes = "新增客户SCM关系表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_CustScmUser.Add")
    public R<SaCustscmuserPojo> create(@RequestBody String json) {
        try {
            SaCustscmuserPojo saCustscmuserPojo = JSONArray.parseObject(json, SaCustscmuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCustscmuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saCustscmuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saCustscmuserPojo.setCreatedate(new Date());   // 创建时间
            saCustscmuserPojo.setLister(loginUser.getRealname());   // 制表
            saCustscmuserPojo.setListerid(loginUser.getUserid());    // 制表id  
            saCustscmuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saCustscmuserService.insert(saCustscmuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改客户SCM关系表", notes = "修改客户SCM关系表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_CustScmUser.Edit")
    public R<SaCustscmuserPojo> update(@RequestBody String json) {
        try {
            SaCustscmuserPojo saCustscmuserPojo = JSONArray.parseObject(json, SaCustscmuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saCustscmuserPojo.setLister(loginUser.getRealname());   // 制表
            saCustscmuserPojo.setListerid(loginUser.getUserid());    // 制表id  
            saCustscmuserPojo.setModifydate(new Date());   //修改时间
//            saCustscmuserPojo.setAssessor(""); // 审核员
//            saCustscmuserPojo.setAssessorid(""); // 审核员id
//            saCustscmuserPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saCustscmuserService.update(saCustscmuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除客户SCM关系表", notes = "删除客户SCM关系表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_CustScmUser.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saCustscmuserService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_CustScmUser.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaCustscmuserPojo saCustscmuserPojo = this.saCustscmuserService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saCustscmuserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

