package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDbdesignPojo;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemdetailPojo;
import inks.service.sa.pms.service.SaDbdesignService;
import inks.service.sa.pms.service.SaDbdesignitemService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaBillcodeService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 表格设计(Sa_DbDesign)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-15 09:54:21
 */
//@RestController
//@RequestMapping("saDbdesign")
public class SaDbdesignController {

    @Resource
    private SaDbdesignService saDbdesignService;
    @Resource
    private SaDbdesignitemService saDbdesignitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaDbdesignController.class);
    

    @ApiOperation(value=" 获取表格设计详细信息", notes="获取表格设计详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.List")
    public R<SaDbdesignPojo> getEntity(String key) {
      try {
           // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDbdesignService.getEntity(key));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.List")
    public R<PageInfo<SaDbdesignitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_DbDesign.CreateDate");
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDbdesignService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value=" 获取表格设计详细信息", notes="获取表格设计详细信息", produces="application/json")
    @RequestMapping(value="/getBillEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.List")
    public R<SaDbdesignPojo> getBillEntity(String key) {
      try {
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDbdesignService.getBillEntity(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getBillList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.List")
    public R<PageInfo<SaDbdesignPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
           if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_DbDesign.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDbdesignService.getBillList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageTh",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.List")
    public R<PageInfo<SaDbdesignPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_DbDesign.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDbdesignService.getPageTh(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value=" 新增表格设计", notes="新增表格设计", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Add")
    public R<SaDbdesignPojo> create(@RequestBody String json) {
        try {
       SaDbdesignPojo saDbdesignPojo = JSONArray.parseObject(json,SaDbdesignPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDbdesignPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDbdesignPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDbdesignPojo.setCreatedate(new Date());   // 创建时间
            saDbdesignPojo.setLister(loginUser.getRealname());   // 制表
            saDbdesignPojo.setListerid(loginUser.getUserid());    // 制表id            
            saDbdesignPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saDbdesignService.insert(saDbdesignPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="修改表格设计", notes="修改表格设计", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Edit")
    public R<SaDbdesignPojo> update(@RequestBody String json) {
        try {
         SaDbdesignPojo saDbdesignPojo = JSONArray.parseObject(json,SaDbdesignPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDbdesignPojo.setLister(loginUser.getRealname());   // 制表
            saDbdesignPojo.setListerid(loginUser.getUserid());    // 制表id   
            saDbdesignPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saDbdesignService.update(saDbdesignPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除表格设计", notes="删除表格设计", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Delete")
    public R<Integer> delete(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDbdesignService.delete(key));
   }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value=" 新增表格设计Item", notes="新增表格设计Item", produces="application/json") 
    @RequestMapping(value="/createItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Add")
    public R<SaDbdesignitemPojo> createItem(@RequestBody String json) {
       try {
     SaDbdesignitemPojo saDbdesignitemPojo = JSONArray.parseObject(json,SaDbdesignitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saDbdesignitemService.insert(saDbdesignitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value=" 修改表格设计Item", notes="修改表格设计Item", produces="application/json") 
    @RequestMapping(value="/updateItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Edit")
    public R<SaDbdesignitemPojo> updateItem(@RequestBody String json) {
       try {
     SaDbdesignitemPojo saDbdesignitemPojo = JSONArray.parseObject(json,SaDbdesignitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saDbdesignitemService.update(saDbdesignitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }    

    @ApiOperation(value="删除表格设计Item", notes="删除表格设计Item", produces="application/json")   
    @RequestMapping(value="/deleteItem",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Delete")
    public R<Integer> deleteItem(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDbdesignitemService.delete(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DbDesign.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDbdesignPojo saDbdesignPojo = this.saDbdesignService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDbdesignPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
          content = reportsPojo.getRptdata();
        } else {
          throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
       if(reportsPojo.getPagerow()>0){
       int index=0;
      // 取行余数
      index =saDbdesignPojo.getItem().size()%reportsPojo.getPagerow();
      if(index>0){
        // 补全空白行
        for(int i=0;i<reportsPojo.getPagerow()-index;i++){
            SaDbdesignitemPojo saDbdesignitemPojo = new SaDbdesignitemPojo();
            saDbdesignPojo.getItem().add(saDbdesignitemPojo);
          }
      }
     }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saDbdesignPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

