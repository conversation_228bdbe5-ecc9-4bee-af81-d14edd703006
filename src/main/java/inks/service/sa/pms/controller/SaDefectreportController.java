package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDefectreportPojo;
import inks.service.sa.pms.service.SaDefectreportService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Random;

/**
 * 缺陷报告(Sa_DefectReport)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-26 12:52:57
 */

public class SaDefectreportController {

    private final static Logger logger = LoggerFactory.getLogger(SaDefectreportController.class);

    @Resource
    private SaDefectreportService saDefectreportService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取缺陷报告详细信息", notes = "获取缺陷报告详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DefectReport.List")
    public R<SaDefectreportPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDefectreportService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DefectReport.List")
    public R<PageInfo<SaDefectreportPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DefectReport.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDefectreportService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增缺陷报告", notes = "新增缺陷报告", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DefectReport.Add")
    public R<SaDefectreportPojo> create(@RequestBody String json) {
        try {
            SaDefectreportPojo saDefectreportPojo = JSONArray.parseObject(json, SaDefectreportPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + String.format("%02d", new Random().nextInt(100));
            saDefectreportPojo.setRefno(refNo);
            saDefectreportPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDefectreportPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDefectreportPojo.setCreatedate(new Date());   // 创建时间
            saDefectreportPojo.setLister(loginUser.getRealname());   // 制表
            saDefectreportPojo.setListerid(loginUser.getUserid());    // 制表id
            saDefectreportPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDefectreportService.insert(saDefectreportPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改缺陷报告", notes = "修改缺陷报告", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DefectReport.Edit")
    public R<SaDefectreportPojo> update(@RequestBody String json) {
        try {
            SaDefectreportPojo saDefectreportPojo = JSONArray.parseObject(json, SaDefectreportPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDefectreportPojo.setLister(loginUser.getRealname());   // 制表
            saDefectreportPojo.setListerid(loginUser.getUserid());    // 制表id
            saDefectreportPojo.setModifydate(new Date());   //修改时间
//            saDefectreportPojo.setAssessor(""); // 审核员
//            saDefectreportPojo.setAssessorid(""); // 审核员id
//            saDefectreportPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDefectreportService.update(saDefectreportPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除缺陷报告", notes = "删除缺陷报告", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DefectReport.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDefectreportService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DefectReport.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDefectreportPojo saDefectreportPojo = this.saDefectreportService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDefectreportPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

