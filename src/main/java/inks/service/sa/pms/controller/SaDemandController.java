package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.config.oss.Storage;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDemandPojo;
import inks.service.sa.pms.mapper.SaDemandMapper;
import inks.service.sa.pms.mapper.SaEngineerMapper;
import inks.service.sa.pms.service.SaDemandService;
import inks.service.sa.pms.utils.PrintColor;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

import static inks.common.core.utils.bean.BeanUtils.attrListToMaps;

/**
 * 产品需求(Sa_Demand)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-24 16:43:02
 */
//@RestController
//@RequestMapping("saDemand")
public class SaDemandController {
    @Resource
    private SaBillcodeService saBillcodeService;
    private final static Logger logger = LoggerFactory.getLogger(SaDemandController.class);
    @Resource
    private SaDemandMapper saDemandMapper;
    @Resource
    private SaEngineerMapper saEngineerMapper;
    @Resource
    private SaDemandService saDemandService;
    @Resource
    @Qualifier("minioStorage")
    private Storage storage;
    @Value("${oss.bucket}")
    private String BUCKET_NAME;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private S06M30B1Controller s06M30B1Controller;

    @ApiOperation(value = " 获取产品需求详细信息", notes = "获取产品需求详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaDemandPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "(当有被指派人时发送MQTT)新增产品需求", notes = "新增产品需求", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaDemandPojo> create(@RequestBody String json) {
        try {
            SaDemandPojo saDemandPojo = JSONArray.parseObject(json, SaDemandPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String refNo = saBillcodeService.getSerialNo("S06M02B1", loginUser.getTenantid(),"Sa_Demand");
            saDemandPojo.setRefno(refNo);
            saDemandPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDemandPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDemandPojo.setCreatedate(new Date());   // 创建时间
            saDemandPojo.setLister(loginUser.getRealname());   // 制表
            saDemandPojo.setListerid(loginUser.getUserid());    // 制表id
            saDemandPojo.setModifydate(new Date());   //修改时间
            SaDemandPojo insertPojo = this.saDemandService.insert(saDemandPojo, loginUser);

            // 当有被指派人时,才发送MQTT消息 格式如下：
            //{
            //    "msg": {
            //        "msgtype": "info",
            //        "info": {
            //            "date": "2024-06-08 10:22:16",
            //            "code": "create",
            //            "content": "【新建】订单加入XX事务"
            //        }
            //    },
            //    "modulecode": "sapms"
            //}
            String appointeeid = saDemandPojo.getAppointeeid();
            if (StringUtils.isNotBlank(appointeeid)) {
                // 获取被指派人的userid
                String userid = saEngineerMapper.getUserid(appointeeid);
                if (StringUtils.isNotBlank(userid)) {
                    // 构建最终发送消息内容和发送主题
                    JSONObject resultContent = new JSONObject();
                    // 获取创建人id 作为发送主题
                    String resultTopic = "inks/server/sapms/" + userid;
                    // 示例数据
                    JSONObject infoObject = new JSONObject();
                    infoObject.put("code", "create");
                    //完成时间: 2024-06-08 10:22:16格式
                    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                    String finishDateStr = simpleDateFormat.format(saDemandPojo.getCreatedate());
                    infoObject.put("date", finishDateStr);

                    // 标题内容
                    String content = "【新建指派】 " + saDemandPojo.getBilltitle();
                    infoObject.put("content", content);

                    JSONObject msgObject = new JSONObject();
                    msgObject.put("info", infoObject);
                    msgObject.put("msgtype", "info");

                    resultContent.put("msg", msgObject);
                    resultContent.put("modulecode", "sapms");


                    // 构建发送的JSON
                    JSONObject contentObject = new JSONObject();
                    contentObject.put("content", resultContent.toJSONString());
                    contentObject.put("topic", resultTopic);

                    //// 调用S06M30B1Controller的send方法发送MQTT消息
                    //s06M30B1Controller.send(contentObject.toJSONString());

                    PrintColor.red("【取消】发送MQTT为:" + contentObject.toJSONString());
                }
            }


            return R.ok(insertPojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "(发送MQTT【修改指派】)修改产品需求", notes = "修改产品需求", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaDemandPojo> update(@RequestBody String json) {
        try {
            SaDemandPojo saDemandPojo = JSONArray.parseObject(json, SaDemandPojo.class);
            // 先拿到原有的被指派人id
            String appointeeidOrg = saDemandMapper.getAppointeeid(saDemandPojo.getId());
            // 获得用户数据
            MyLoginUser myLoginUser = saRedisService.getMyLoginUser(ServletUtils.getRequest());
            saDemandPojo.setLister(myLoginUser.getRealname());   // 制表
            saDemandPojo.setListerid(myLoginUser.getUserid());    // 制表id
            saDemandPojo.setModifydate(new Date());   //修改时间
            SaDemandPojo updatePojo = this.saDemandService.update(saDemandPojo, myLoginUser);

            // 当有被指派人时,才发送MQTT消息 格式如下：
            //{
            //    "msg": {
            //        "msgtype": "info",
            //        "info": {
            //            "date": "2024-06-08 10:22:16",
            //            "code": "create",
            //            "content": "【新建】订单加入XX事务"
            //        }
            //    },
            //    "modulecode": "sapms"
            //}
            String appointeeid = saDemandPojo.getAppointeeid();
            if (StringUtils.isNotBlank(appointeeid)) {
                // 检查本次被指派人是否发生变化,如果发生变化,才发送MQTT消息
                if (!Objects.equals(appointeeid, appointeeidOrg)) {
                    // 获取被指派人的userid
                    String userid = saEngineerMapper.getUserid(appointeeid);
                    if (StringUtils.isNotBlank(userid)) {
                        // 构建最终发送消息内容和发送主题
                        JSONObject resultContent = new JSONObject();
                        // 获取创建人id 作为发送主题
                        String resultTopic = "inks/server/sapms/" + userid;
                        // 示例数据
                        JSONObject infoObject = new JSONObject();
                        infoObject.put("code", "update");
                        //完成时间: 2024-06-08 10:22:16格式
                        SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
                        String finishDateStr = simpleDateFormat.format(new Date());
                        infoObject.put("date", finishDateStr);

                        // 标题内容
                        String content = "【修改指派】 " + updatePojo.getBilltitle();
                        infoObject.put("content", content);

                        JSONObject msgObject = new JSONObject();
                        msgObject.put("info", infoObject);
                        msgObject.put("msgtype", "info");

                        resultContent.put("msg", msgObject);
                        resultContent.put("modulecode", "sapms");


                        // 构建发送的JSON
                        JSONObject contentObject = new JSONObject();
                        contentObject.put("content", resultContent.toJSONString());
                        contentObject.put("topic", resultTopic);

                        // 调用S06M30B1Controller的send方法发送MQTT消息
                        s06M30B1Controller.send(contentObject.toJSONString());

                        PrintColor.red("发送MQTT为:" + contentObject.toJSONString());
                    }
                }
            }


            return R.ok(updatePojo);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除产品需求", notes = "删除产品需求", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // 同步删除minio图片
            SaDemandPojo saDemandPojo = this.saDemandService.getEntity(key);
            if (saDemandPojo != null) {
                // 遍历字段名数组，如果字段对应的值不为空，则进行删除操作
                String[] fieldNames = {"pictureurl1", "pictureurl2", "pictureurl3", "pictureurl4", "pictureurl5",
                        "finpictureurl1", "finpictureurl2", "finpictureurl3", "finpictureurl4", "finpictureurl5"};
                for (String fieldName : fieldNames) {
                    Field field = saDemandPojo.getClass().getDeclaredField(fieldName);
                    field.setAccessible(true);
                    String fieldValue = (String) field.get(saDemandPojo);
                    if (StringUtils.isNotEmpty(fieldValue)) {
                        // 删除minio图片
                        storage.removeObject(BUCKET_NAME, fieldValue);
                    }
                }
                // 最后删除产品需求
                this.saDemandService.delete(key, loginUser);
            }
            return R.ok(1);
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Demand.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDemandPojo saDemandPojo = this.saDemandService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDemandPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaDemandPojo saDemandPojo = this.saDemandService.getEntity(key);
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(saDemandPojo);
            // 获取单据表头.加入公司信息
            if (loginUser.getTenantinfo() != null)
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
//            List<SaContractitemPojo> lstitem = this.saContractitemService.getList(key);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = null;

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "需求单" + saDemandPojo.getBilltitle());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限


            // 本地打印
//            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
            return R.ok(JSONObject.toJSONString(mapPrint));
//            } else {
//                // 远程SN打印
//                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
//                if (rPrint.getCode() == 200) {
//                    return R.ok();
//                } else {
//                    return R.fail(rPrint.getMsg());
//                }
//            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印SaDemand明细报表(分页PageList)", notes = "打印明细报表", produces = "application/json")
    @RequestMapping(value = "/printPageList", method = RequestMethod.POST)
    public void printPageList(@RequestBody String json, String groupid, String ptid) throws IOException, JRException {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Sa_Demand.CreateDate");
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        if (groupid != null) {
            qpfilter += " and Sa_Demand.Groupid='" + groupid + "'";
        }
        // 加入场景   Eric 20221124
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        queryParam.setTenantid(loginUser.getTenantid());  //租户id
        List<SaDemandPojo> lst = this.saDemandService.getPageListUNIONALL(queryParam, null).getList();
        //表头转MAP
        Map<String, Object> map = new HashMap<>();
        if (queryParam.getDateRange() != null) {
            map.put("startdate", queryParam.getDateRange().getStartDate());
            map.put("enddate", queryParam.getDateRange().getEndDate());
        }
        // 加入公司信息
        PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content = "";
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = lst.size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaDemandPojo pojo = new SaDemandPojo();
                    lst.add(pojo);
                }
            }
        }
        // 带属性List转为Map  EricRen 20220427
        List<Map<String, Object>> listToMaps = attrListToMaps(lst);
        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(listToMaps);
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    @ApiOperation(value = "批量云打印报表(List SaDemandPojo不带item)", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选),groupid不为null则只获取第一条客户信息", produces = "application/json")
    @RequestMapping(value = "/printWebPageList", method = RequestMethod.POST)
    public R<String> printWebPageList(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();

            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            // 报表数据
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Demand.CreateDate");
            String qpfilter = "";
            // 加入场景   Eric 20221124
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            queryParam.setTenantid(loginUser.getTenantid());  //租户id
            List<SaDemandPojo> lstTh = this.saDemandService.getPageListUNIONALL(queryParam, null).getList();
            // 获取单据表头.表头转MAP
            Map<String, Object> map = new HashMap<>();
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrListToMaps(lstTh);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaDemand批量打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill2", method = RequestMethod.GET)
    public R<String> printWebBill2(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaDemandPojo pojo = this.saDemandService.getEntity(key);

            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(pojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            // 单据Item. 带属性List转为Map  EricRen 20220427

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaDemand");
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "批量打印单据,传入ids", notes = "json={KEY,KEY},ptid打印模版", produces = "application/json")
    @RequestMapping(value = "/printBatchBill", method = RequestMethod.POST)
    public void printBatchBill(@RequestBody String json, String ptid) throws IOException, JRException {
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            String content;
            if (reportsPojo != null) {
                content = reportsPojo.getRptdata();
            } else {
                throw new BaseBusinessException("未找到报表");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());

            //数据填充
            JasperPrint printAll = new JasperPrint();
            for (int a = 0; a < lstkeys.size(); a++) {
                String key = lstkeys.get(a);
                //=========获取单据表头信息========
                SaDemandPojo pojo = this.saDemandService.getEntity(key);
                if (pojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }
                //表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(pojo);
                // 加入公司信息
                PrintUtils.addCompanyInfo(map, loginUser);

                //报表生成
                InputStream stream = new ByteArrayInputStream(content.getBytes());
                //编译报表
                JasperReport jasperReport = JasperCompileManager.compileReport(stream);

                //数据填充
                JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
                if (a == 0) {
                    printAll = print;
                } else {
                    List<JRPrintPage> pages = print.getPages();
                    for (JRPrintPage page : pages) {
                        printAll.addPage(page);
                    }
                }
            }
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(printAll, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "批量云打印SaDemand单据(ids)", notes = "json={KEY,KEY},ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printBatchWebBill", method = RequestMethod.POST)
    public R<String> printBatchWebBill(@RequestBody String json, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            List<String> lstkeys = JSONArray.parseArray(json, String.class);
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<String> lstptJson = new ArrayList<>();
            String ptRefNoMain = "";

            for (String key : lstkeys) {
                //=========获取单据表头信息========
                SaDemandPojo saDemandPojo = this.saDemandService.getEntity(key);
                if (saDemandPojo == null) {
                    throw new BaseBusinessException("无效单据,刷新后再试");
                }

                // 获取单据表头.表头转MAP
                Map<String, Object> map = BeanUtils.beanToMap(saDemandPojo);

                // 获取单据表头.加入公司信息
                if (loginUser.getTenantinfo() != null) {
                    inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
                }
                // === 整理Map.row=====
                Map<String, Object> maprow = new LinkedHashMap<>();

                // === 整理report=xml+grparam=====
                Map<String, Object> mapreport = new LinkedHashMap<>();
                mapreport.put("xml", maprow);
                mapreport.put("_grparam", map);
                // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
                Map<String, Object> mapdata = new LinkedHashMap<>();
                mapdata.put("report", mapreport);
                // ====Map转Json ==== 注 时间转String 格式；
                String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
                lstptJson.add(ptJson);
                ptRefNoMain += saDemandPojo.getRefno() + ",";

            }
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();
            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "batchpreview");
            } else {
                mapPrint.put("code", "batchprint");
            }
            String[] lstRefno = ptRefNoMain.split(",");
            mapPrint.put("msg", "SaDemand：" + lstRefno[0] + "~" + lstRefno[lstRefno.length - 1]);    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = UUID.randomUUID().toString();
                saRedisService.setCacheObject("report_data:" + rediskey, JSONArray.toJSONString(lstptJson), 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", JSONArray.toJSONString(lstptJson));   //  打印数据
            }
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());
            mapPrint.put("ptid", ptid);   // 报表id
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

