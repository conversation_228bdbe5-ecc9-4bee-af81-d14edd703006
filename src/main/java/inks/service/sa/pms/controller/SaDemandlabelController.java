package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDemandlabelPojo;
import inks.service.sa.pms.service.SaDemandlabelService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 需求标签(Sa_DemandLabel)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-26 13:02:27
 */
//@RestController
//@RequestMapping("saDemandlabel")
public class SaDemandlabelController {
    private final static Logger logger = LoggerFactory.getLogger(SaDemandlabelController.class);
    @Resource
    private SaDemandlabelService saDemandlabelService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取需求标签详细信息", notes = "获取需求标签详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandLabel.List")
    public R<SaDemandlabelPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandlabelService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandLabel.List")
    public R<PageInfo<SaDemandlabelPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DemandLabel.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandlabelService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增需求标签", notes = "新增需求标签", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandLabel.Add")
    public R<SaDemandlabelPojo> create(@RequestBody String json) {
        try {
            SaDemandlabelPojo saDemandlabelPojo = JSONArray.parseObject(json, SaDemandlabelPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandlabelPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDemandlabelPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDemandlabelPojo.setCreatedate(new Date());   // 创建时间
            saDemandlabelPojo.setLister(loginUser.getRealname());   // 制表
            saDemandlabelPojo.setListerid(loginUser.getUserid());    // 制表id
            saDemandlabelPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDemandlabelService.insert(saDemandlabelPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改需求标签", notes = "修改需求标签", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandLabel.Edit")
    public R<SaDemandlabelPojo> update(@RequestBody String json) {
        try {
            SaDemandlabelPojo saDemandlabelPojo = JSONArray.parseObject(json, SaDemandlabelPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandlabelPojo.setLister(loginUser.getRealname());   // 制表
            saDemandlabelPojo.setListerid(loginUser.getUserid());    // 制表id
            saDemandlabelPojo.setModifydate(new Date());   //修改时间
            //            saDemandlabelPojo.setAssessor(""); // 审核员
            //            saDemandlabelPojo.setAssessorid(""); // 审核员id
            //            saDemandlabelPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDemandlabelService.update(saDemandlabelPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除需求标签", notes = "删除需求标签", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandLabel.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandlabelService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandLabel.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDemandlabelPojo saDemandlabelPojo = this.saDemandlabelService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDemandlabelPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

