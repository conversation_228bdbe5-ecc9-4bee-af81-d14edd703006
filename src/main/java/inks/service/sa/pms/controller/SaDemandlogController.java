package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDemandlogPojo;
import inks.service.sa.pms.service.SaDemandlogService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 产品需求日志(Sa_DemandLog)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-26 15:00:37
 */

public class SaDemandlogController {

    private final static Logger logger = LoggerFactory.getLogger(SaDemandlogController.class);

    @Resource
    private SaDemandlogService saDemandlogService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取产品需求日志详细信息", notes = "获取产品需求日志详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DemandLog.List")
    public R<SaDemandlogPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandlogService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

//    public static void main(String[] args) {
//        //获取当前时间戳 SELECT datetime(1697611503, 'unixepoch');
//        System.out.println(new Date().getTime());
//    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DemandLog.List")
    public R<PageInfo<SaDemandlogPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DemandLog.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandlogService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增产品需求日志", notes = "新增产品需求日志", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DemandLog.Add")
    public R<SaDemandlogPojo> create(@RequestBody String json) {
        try {
            SaDemandlogPojo saDemandlogPojo = JSONArray.parseObject(json, SaDemandlogPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandlogPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDemandlogPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDemandlogPojo.setCreatedate(new Date());   // 创建时间
            saDemandlogPojo.setLister(loginUser.getRealname());   // 制表
            saDemandlogPojo.setListerid(loginUser.getUserid());    // 制表id  
            saDemandlogPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDemandlogService.insert(saDemandlogPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改产品需求日志", notes = "修改产品需求日志", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DemandLog.Edit")
    public R<SaDemandlogPojo> update(@RequestBody String json) {
        try {
            SaDemandlogPojo saDemandlogPojo = JSONArray.parseObject(json, SaDemandlogPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandlogPojo.setLister(loginUser.getRealname());   // 制表
            saDemandlogPojo.setListerid(loginUser.getUserid());    // 制表id  
            saDemandlogPojo.setModifydate(new Date());   //修改时间
//            saDemandlogPojo.setAssessor(""); // 审核员
//            saDemandlogPojo.setAssessorid(""); // 审核员id
//            saDemandlogPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDemandlogService.update(saDemandlogPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除产品需求日志", notes = "删除产品需求日志", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DemandLog.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandlogService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DemandLog.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDemandlogPojo saDemandlogPojo = this.saDemandlogService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDemandlogPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

