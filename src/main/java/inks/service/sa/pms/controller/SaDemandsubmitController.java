package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDemandsubmitPojo;
import inks.service.sa.pms.service.SaDemandsubmitService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 需求提报(Sa_DemandSubmit)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-25 13:27:24
 */
//@RestController
//@RequestMapping("saDemandsubmit")
public class SaDemandsubmitController {
    private final static Logger logger = LoggerFactory.getLogger(SaDemandsubmitController.class);
    @Resource
    private SaDemandsubmitService saDemandsubmitService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取需求提报详细信息", notes = "获取需求提报详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandSubmit.List")
    public R<SaDemandsubmitPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandsubmitService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandSubmit.List")
    public R<PageInfo<SaDemandsubmitPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DemandSubmit.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandsubmitService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增需求提报", notes = "新增需求提报", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandSubmit.Add")
    public R<SaDemandsubmitPojo> create(@RequestBody String json) {
        try {
            SaDemandsubmitPojo saDemandsubmitPojo = JSONArray.parseObject(json, SaDemandsubmitPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandsubmitPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDemandsubmitPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDemandsubmitPojo.setCreatedate(new Date());   // 创建时间
            saDemandsubmitPojo.setLister(loginUser.getRealname());   // 制表
            saDemandsubmitPojo.setListerid(loginUser.getUserid());    // 制表id
            saDemandsubmitPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDemandsubmitService.insert(saDemandsubmitPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改需求提报", notes = "修改需求提报", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandSubmit.Edit")
    public R<SaDemandsubmitPojo> update(@RequestBody String json) {
        try {
            SaDemandsubmitPojo saDemandsubmitPojo = JSONArray.parseObject(json, SaDemandsubmitPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandsubmitPojo.setLister(loginUser.getRealname());   // 制表
            saDemandsubmitPojo.setListerid(loginUser.getUserid());    // 制表id
            saDemandsubmitPojo.setModifydate(new Date());   //修改时间
            //            saDemandsubmitPojo.setAssessor(""); // 审核员
            //            saDemandsubmitPojo.setAssessorid(""); // 审核员id
            //            saDemandsubmitPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDemandsubmitService.update(saDemandsubmitPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除需求提报", notes = "删除需求提报", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandSubmit.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandsubmitService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "审核需求提报", notes = "审核需求提报", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandSubmit.Approval")
    public R<SaDemandsubmitPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaDemandsubmitPojo saDemandsubmitPojo = this.saDemandsubmitService.getEntity(key);
            if (saDemandsubmitPojo.getAssessor().isEmpty()) {
                saDemandsubmitPojo.setAssessor(loginUser.getRealname()); //审核员
                saDemandsubmitPojo.setAssessorid(loginUser.getUserid()); //审核员id
                saDemandsubmitPojo.setStatetext("");
            } else {
                saDemandsubmitPojo.setAssessor(""); //审核员
                saDemandsubmitPojo.setAssessorid(""); //审核员
            }
            saDemandsubmitPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDemandsubmitService.approval(saDemandsubmitPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandSubmit.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDemandsubmitPojo saDemandsubmitPojo = this.saDemandsubmitService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDemandsubmitPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

