package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDemandtimePojo;
import inks.service.sa.pms.service.SaDemandtimeService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 需求工时记录(Sa_DemandTime)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-02 11:09:19
 */
//@RestController
//@RequestMapping("saDemandtime")
public class SaDemandtimeController {
    private final static Logger logger = LoggerFactory.getLogger(SaDemandtimeController.class);
    @Resource
    private SaDemandtimeService saDemandtimeService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取需求工时记录详细信息", notes = "获取需求工时记录详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandTime.List")
    public R<SaDemandtimePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandtimeService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandTime.List")
    public R<PageInfo<SaDemandtimePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DemandTime.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDemandtimeService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增需求工时记录", notes = "新增需求工时记录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandTime.Add")
    public R<SaDemandtimePojo> create(@RequestBody String json) {
        try {
            SaDemandtimePojo saDemandtimePojo = JSONArray.parseObject(json, SaDemandtimePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandtimePojo.setCreateby(loginUser.getRealName());   // 创建者
            saDemandtimePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDemandtimePojo.setCreatedate(new Date());   // 创建时间
            saDemandtimePojo.setLister(loginUser.getRealname());   // 制表
            saDemandtimePojo.setListerid(loginUser.getUserid());    // 制表id
            saDemandtimePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDemandtimeService.insert(saDemandtimePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改需求工时记录", notes = "修改需求工时记录", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_DemandTime.Edit")
    public R<SaDemandtimePojo> update(@RequestBody String json) {
        try {
            SaDemandtimePojo saDemandtimePojo = JSONArray.parseObject(json, SaDemandtimePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDemandtimePojo.setLister(loginUser.getRealname());   // 制表
            saDemandtimePojo.setListerid(loginUser.getUserid());    // 制表id
            saDemandtimePojo.setModifydate(new Date());   //修改时间
            //            saDemandtimePojo.setAssessor(""); // 审核员
            //            saDemandtimePojo.setAssessorid(""); // 审核员id
            //            saDemandtimePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDemandtimeService.update(saDemandtimePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除需求工时记录", notes = "删除需求工时记录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandTime.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDemandtimeService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_DemandTime.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDemandtimePojo saDemandtimePojo = this.saDemandtimeService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDemandtimePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

