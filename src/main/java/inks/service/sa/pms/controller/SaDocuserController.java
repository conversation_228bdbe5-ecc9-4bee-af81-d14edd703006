package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaDocuserPojo;
import inks.service.sa.pms.service.SaDocuserService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 文档-用户关联(Sa_DocUser)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-24 16:15:01
 */
//@RestController
//@RequestMapping("saDocuser")
public class SaDocuserController {

    private final static Logger logger = LoggerFactory.getLogger(SaDocuserController.class);

    @Resource
    private SaDocuserService saDocuserService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取文档-详细信息", notes = "获取文档-详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DocUser.List")
    public R<SaDocuserPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDocuserService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DocUser.List")
    public R<PageInfo<SaDocuserPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_DocUser.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saDocuserService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增文档-", notes = "新增文档-", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DocUser.Add")
    public R<SaDocuserPojo> create(@RequestBody String json) {
        try {
            SaDocuserPojo saDocuserPojo = JSONArray.parseObject(json, SaDocuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDocuserPojo.setCreateby(loginUser.getRealName());   // 创建者
            saDocuserPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saDocuserPojo.setCreatedate(new Date());   // 创建时间
            saDocuserPojo.setLister(loginUser.getRealname());   // 制表
            saDocuserPojo.setListerid(loginUser.getUserid());    // 制表id  
            saDocuserPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saDocuserService.insert(saDocuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改文档-", notes = "修改文档-", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_DocUser.Edit")
    public R<SaDocuserPojo> update(@RequestBody String json) {
        try {
            SaDocuserPojo saDocuserPojo = JSONArray.parseObject(json, SaDocuserPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saDocuserPojo.setLister(loginUser.getRealname());   // 制表
            saDocuserPojo.setListerid(loginUser.getUserid());    // 制表id  
            saDocuserPojo.setModifydate(new Date());   //修改时间
//            saDocuserPojo.setAssessor(""); // 审核员
//            saDocuserPojo.setAssessorid(""); // 审核员id
//            saDocuserPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saDocuserService.update(saDocuserPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除文档-", notes = "删除文档-", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DocUser.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saDocuserService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_DocUser.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaDocuserPojo saDocuserPojo = this.saDocuserService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saDocuserPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

