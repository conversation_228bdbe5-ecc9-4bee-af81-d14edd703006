package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaEngineerPojo;
import inks.service.sa.pms.service.SaEngineerService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 处理节点(Sa_Engineer)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-29 16:04:08
 */
public class SaEngineerController {

    private final static Logger logger = LoggerFactory.getLogger(SaEngineerController.class);

    @Resource
    private SaEngineerService saEngineerService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取处理节点详细信息", notes = "获取处理节点详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaEngineerPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saEngineerService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaEngineerPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Engineer.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saEngineerService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
    public R<PageInfo<SaEngineerPojo>> getOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Engineer.EngineerCode");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = " AND Sa_Engineer.EnabledMark = 1 ";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saEngineerService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增处理节点", notes = "新增处理节点", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaEngineerPojo> create(@RequestBody String json) {
        try {
            SaEngineerPojo saEngineerPojo = JSONArray.parseObject(json, SaEngineerPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saEngineerPojo.setCreateby(loginUser.getRealName());   // 创建者
            saEngineerPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saEngineerPojo.setCreatedate(new Date());   // 创建时间
            saEngineerPojo.setLister(loginUser.getRealname());   // 制表
            saEngineerPojo.setListerid(loginUser.getUserid());    // 制表id
            saEngineerPojo.setModifydate(new Date());   //修改时间
            saEngineerPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.saEngineerService.insert(saEngineerPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改处理节点", notes = "修改处理节点", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaEngineerPojo> update(@RequestBody String json) {
        try {
            SaEngineerPojo saEngineerPojo = JSONArray.parseObject(json, SaEngineerPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saEngineerPojo.setLister(loginUser.getRealname());   // 制表
            saEngineerPojo.setListerid(loginUser.getUserid());    // 制表id
            saEngineerPojo.setTenantid(loginUser.getTenantid());   //租户id
            saEngineerPojo.setModifydate(new Date());   //修改时间
//            saEngineerPojo.setAssessor(""); // 审核员
//            saEngineerPojo.setAssessorid(""); // 审核员id
//            saEngineerPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saEngineerService.update(saEngineerPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除处理节点", notes = "删除处理节点", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saEngineerService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaEngineerPojo saEngineerPojo = this.saEngineerService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saEngineerPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

