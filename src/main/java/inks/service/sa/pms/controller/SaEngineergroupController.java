package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaEngineergroupPojo;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemPojo;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemdetailPojo;
import inks.service.sa.pms.service.SaEngineergroupService;
import inks.service.sa.pms.service.SaEngineergroupitemService;
import inks.sa.common.core.service.SaRedisService;
import inks.sa.common.core.service.SaBillcodeService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import javax.annotation.Resource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 工程组主表(Sa_EngineerGroup)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-15 12:57:58
 */
//@RestController
//@RequestMapping("saEngineergroup")
public class SaEngineergroupController {

    @Resource
    private SaEngineergroupService saEngineergroupService;
    @Resource
    private SaEngineergroupitemService saEngineergroupitemService;
    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaEngineergroupController.class);
    

    @ApiOperation(value=" 获取工程组主表详细信息", notes="获取工程组主表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.List")
    public R<SaEngineergroupPojo> getEntity(String key) {
      try {
           // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saEngineergroupService.getEntity(key));
     }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.List")
    public R<PageInfo<SaEngineergroupitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_EngineerGroup.CreateDate");
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saEngineergroupService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value=" 获取工程组主表详细信息", notes="获取工程组主表详细信息", produces="application/json")
    @RequestMapping(value="/getBillEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.List")
    public R<SaEngineergroupPojo> getBillEntity(String key) {
      try {
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saEngineergroupService.getBillEntity(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    

    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getBillList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.List")
    public R<PageInfo<SaEngineergroupPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
           if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_EngineerGroup.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saEngineergroupService.getBillList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageTh",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.List")
    public R<PageInfo<SaEngineergroupPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
             if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_EngineerGroup.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saEngineergroupService.getPageTh(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value=" 新增工程组主表", notes="新增工程组主表", produces="application/json") 
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.Add")
    public R<SaEngineergroupPojo> create(@RequestBody String json) {
        try {
       SaEngineergroupPojo saEngineergroupPojo = JSONArray.parseObject(json,SaEngineergroupPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saEngineergroupPojo.setCreateby(loginUser.getRealName());   // 创建者
            saEngineergroupPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saEngineergroupPojo.setCreatedate(new Date());   // 创建时间
            saEngineergroupPojo.setLister(loginUser.getRealname());   // 制表
            saEngineergroupPojo.setListerid(loginUser.getUserid());    // 制表id            
            saEngineergroupPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saEngineergroupService.insert(saEngineergroupPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="修改工程组主表", notes="修改工程组主表", produces="application/json")  
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.Edit")
    public R<SaEngineergroupPojo> update(@RequestBody String json) {
        try {
         SaEngineergroupPojo saEngineergroupPojo = JSONArray.parseObject(json,SaEngineergroupPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saEngineergroupPojo.setLister(loginUser.getRealname());   // 制表
            saEngineergroupPojo.setListerid(loginUser.getUserid());    // 制表id   
            saEngineergroupPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saEngineergroupService.update(saEngineergroupPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value="删除工程组主表", notes="删除工程组主表", produces="application/json")   
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.Delete")
    public R<Integer> delete(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saEngineergroupService.delete(key));
   }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value=" 新增工程组主表Item", notes="新增工程组主表Item", produces="application/json") 
    @RequestMapping(value="/createItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.Add")
    public R<SaEngineergroupitemPojo> createItem(@RequestBody String json) {
       try {
     SaEngineergroupitemPojo saEngineergroupitemPojo = JSONArray.parseObject(json,SaEngineergroupitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saEngineergroupitemService.insert(saEngineergroupitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value=" 修改工程组主表Item", notes="修改工程组主表Item", produces="application/json") 
    @RequestMapping(value="/updateItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.Edit")
    public R<SaEngineergroupitemPojo> updateItem(@RequestBody String json) {
       try {
     SaEngineergroupitemPojo saEngineergroupitemPojo = JSONArray.parseObject(json,SaEngineergroupitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saEngineergroupitemService.update(saEngineergroupitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }    

    @ApiOperation(value="删除工程组主表Item", notes="删除工程组主表Item", produces="application/json")   
    @RequestMapping(value="/deleteItem",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.Delete")
    public R<Integer> deleteItem(String key) {
      try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saEngineergroupitemService.delete(key));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_EngineerGroup.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaEngineergroupPojo saEngineergroupPojo = this.saEngineergroupService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saEngineergroupPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
          content = reportsPojo.getRptdata();
        } else {
          throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
       if(reportsPojo.getPagerow()>0){
       int index=0;
      // 取行余数
      index =saEngineergroupPojo.getItem().size()%reportsPojo.getPagerow();
      if(index>0){
        // 补全空白行
        for(int i=0;i<reportsPojo.getPagerow()-index;i++){
            SaEngineergroupitemPojo saEngineergroupitemPojo = new SaEngineergroupitemPojo();
            saEngineergroupPojo.getItem().add(saEngineergroupitemPojo);
          }
      }
     }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saEngineergroupPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

