package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.utils.inks.PrintUtils;
import inks.sa.common.core.config.InksConfigThreadLocal_Sa;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaFeedbackPojo;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemPojo;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemdetailPojo;
import inks.service.sa.pms.service.SaFeedbackService;
import inks.service.sa.pms.service.SaFeedbackitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * 反馈单(SaFeedback)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-17 13:04:37
 */
//@RestController
//@RequestMapping("saFeedback")
public class SaFeedbackController {
    private final static Logger logger = LoggerFactory.getLogger(SaFeedbackController.class);
    @Resource
    private SaBillcodeService saBillcodeService;
    @Resource
    private SaFeedbackService saFeedbackService;
    /**
     * 服务对象Item
     */
    @Resource
    private SaFeedbackitemService saFeedbackitemService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取反馈单详细信息", notes = "获取反馈单详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    // @PreAuthoriz(hasPermi = "Sa_Feedback.List")
    public R<SaFeedbackPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFeedbackService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    // @PreAuthoriz(hasPermi = "Sa_Feedback.List")
    public R<PageInfo<SaFeedbackitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Feedback.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFeedbackService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取反馈单详细信息", notes = "获取反馈单详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Feedback.List")
    public R<SaFeedbackPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFeedbackService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Feedback.List")
    public R<PageInfo<SaFeedbackPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Feedback.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFeedbackService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Feedback.List")
    public R<PageInfo<SaFeedbackPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Feedback.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFeedbackService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增反馈单", notes = "新增反馈单", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Feedback.Add")
    public R<SaFeedbackPojo> create(@RequestBody String json, HttpServletRequest request) {
        try {
            SaFeedbackPojo saFeedbackPojo = JSONArray.parseObject(json, SaFeedbackPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // sa生成单据编码 获取请求的路径,/分割,拿到"D01MXXXX"
            String[] pathParts = request.getRequestURI().split("/");
            String moduleCode = pathParts[pathParts.length - 2];
            String refNo = saBillcodeService.getSerialNo(moduleCode);
            saFeedbackPojo.setRefno(refNo);
            saFeedbackPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFeedbackPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFeedbackPojo.setCreatedate(new Date());   // 创建时间
            saFeedbackPojo.setLister(loginUser.getRealname());   // 制表
            saFeedbackPojo.setListerid(loginUser.getUserid());    // 制表id            
            saFeedbackPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFeedbackService.insert(saFeedbackPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改反馈单", notes = "修改反馈单", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Feedback.Edit")
    public R<SaFeedbackPojo> update(@RequestBody String json) {
        try {
            SaFeedbackPojo saFeedbackPojo = JSONArray.parseObject(json, SaFeedbackPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFeedbackPojo.setLister(loginUser.getRealname());   // 制表
            saFeedbackPojo.setListerid(loginUser.getUserid());    // 制表id   
            saFeedbackPojo.setModifydate(new Date());   //修改时间
            saFeedbackPojo.setAssessor(""); //审核员
            saFeedbackPojo.setAssessorid(""); //审核员
            saFeedbackPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFeedbackService.update(saFeedbackPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除反馈单", notes = "删除反馈单", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Feedback.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFeedbackService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增反馈单Item", notes = "新增反馈单Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Feedback.Add")
    public R<SaFeedbackitemPojo> createItem(@RequestBody String json) {
        try {
            SaFeedbackitemPojo saFeedbackitemPojo = JSONArray.parseObject(json, SaFeedbackitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFeedbackitemService.insert(saFeedbackitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改反馈单Item", notes = "修改反馈单Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Feedback.Edit")
    public R<SaFeedbackitemPojo> updateItem(@RequestBody String json) {
        try {
            SaFeedbackitemPojo saFeedbackitemPojo = JSONArray.parseObject(json, SaFeedbackitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFeedbackitemService.update(saFeedbackitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除反馈单Item", notes = "删除反馈单Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Feedback.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFeedbackitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核反馈单", notes = "审核反馈单", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Feedback.Approval")
    public R<SaFeedbackPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaFeedbackPojo saFeedbackPojo = this.saFeedbackService.getEntity(key);
            //关闭反馈单自审功能，自己的单据必须同事审核：
            String cfgValue = InksConfigThreadLocal_Sa.getConfig("pms.feedback.cantapprovalself");
            if ("true".equals(cfgValue)) {
                if (loginUser.getUserid().equals(saFeedbackPojo.getListerid())) {
                    throw new BaseBusinessException("禁止自审");
                }
            }

            if (saFeedbackPojo.getAssessor().isEmpty()) {
                saFeedbackPojo.setAssessor(loginUser.getRealname()); //审核员
                saFeedbackPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                saFeedbackPojo.setAssessor(""); //审核员
                saFeedbackPojo.setAssessorid(""); //审核员
            }
            saFeedbackPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFeedbackService.approval(saFeedbackPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Feedback.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaFeedbackPojo saFeedbackPojo = this.saFeedbackService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFeedbackPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saFeedbackPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaFeedbackitemPojo saFeedbackitemPojo = new SaFeedbackitemPojo();
                    saFeedbackPojo.getItem().add(saFeedbackitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saFeedbackPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }

    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Feedback.Print")
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaFeedbackPojo saFeedbackPojo = this.saFeedbackService.getEntity(key);


            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(saFeedbackPojo);
            // 获取单据表头.加入公司信息
            PrintUtils.addCompanyInfo(map, loginUser);
            //=========获取单据Item信息========
            List<SaFeedbackitemPojo> lstitem = this.saFeedbackitemService.getList(key);
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = BeanUtils.attrcostListToMaps(lstitem);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (Objects.equals(cmd, 1)) {
                mapPrint.put("code", "preview");
            } else if (Objects.equals(cmd, 2)) {
                mapPrint.put("code", "design");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "SaFeedback" + saFeedbackPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
}

