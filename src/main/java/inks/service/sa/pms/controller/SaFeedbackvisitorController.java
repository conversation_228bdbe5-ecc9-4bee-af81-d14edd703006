package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFeedbackvisitorPojo;
import inks.service.sa.pms.service.SaFeedbackvisitorService;
import inks.sa.common.core.service.SaRedisService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 游客反馈(Sa_FeedbackVisitor)表控制层
 *
 * <AUTHOR>
 * @since 2025-03-25 15:00:01
 */
//@RestController
//@RequestMapping("saFeedbackvisitor")
public class SaFeedbackvisitorController {
    @Resource
    private SaFeedbackvisitorService saFeedbackvisitorService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaFeedbackvisitorController.class);


    @ApiOperation(value=" 获取游客反馈详细信息", notes="获取游客反馈详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FeedbackVisitor.List")
    public R<SaFeedbackvisitorPojo> getEntity(String key) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            return R.ok(this.saFeedbackvisitorService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FeedbackVisitor.List")
    public R<PageInfo<SaFeedbackvisitorPojo>> getPageList(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_FeedbackVisitor.CreateDate");
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFeedbackvisitorService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增游客反馈", notes="新增游客反馈", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FeedbackVisitor.Add")
    public R<SaFeedbackvisitorPojo> create(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            SaFeedbackvisitorPojo saFeedbackvisitorPojo = JSONArray.parseObject(json,SaFeedbackvisitorPojo.class);
            saFeedbackvisitorPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFeedbackvisitorPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFeedbackvisitorPojo.setCreatedate(new Date());   // 创建时间
            saFeedbackvisitorPojo.setLister(loginUser.getRealname());   // 制表
            saFeedbackvisitorPojo.setListerid(loginUser.getUserid());    // 制表id
            saFeedbackvisitorPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFeedbackvisitorService.insert(saFeedbackvisitorPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改游客反馈", notes="修改游客反馈", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FeedbackVisitor.Edit")
    public R<SaFeedbackvisitorPojo> update(@RequestBody String json) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            SaFeedbackvisitorPojo saFeedbackvisitorPojo = JSONArray.parseObject(json,SaFeedbackvisitorPojo.class);
            saFeedbackvisitorPojo.setLister(loginUser.getRealname());   // 制表
            saFeedbackvisitorPojo.setListerid(loginUser.getUserid());    // 制表id
            saFeedbackvisitorPojo.setModifydate(new Date());   //修改时间
    //            saFeedbackvisitorPojo.setAssessor(""); // 审核员
    //            saFeedbackvisitorPojo.setAssessorid(""); // 审核员id
    //            saFeedbackvisitorPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFeedbackvisitorService.update(saFeedbackvisitorPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除游客反馈", notes="删除游客反馈", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FeedbackVisitor.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = saRedisService.getLoginUser();
        try {
            return R.ok(this.saFeedbackvisitorService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FeedbackVisitor.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser();
        //获取单据信息
        SaFeedbackvisitorPojo saFeedbackvisitorPojo = this.saFeedbackvisitorService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFeedbackvisitorPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

