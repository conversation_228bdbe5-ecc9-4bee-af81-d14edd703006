package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaFreepagePojo;
import inks.service.sa.pms.service.SaFreepageService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Random;

/**
 * 自由页面(Sa_FreePage)表控制层
 *
 * <AUTHOR>
 * @since 2024-07-22 10:20:59
 */
//@RestController
//@RequestMapping("saFreepage")
public class SaFreepageController {
    private final static Logger logger = LoggerFactory.getLogger(SaFreepageController.class);
    @Resource
    private SaFreepageService saFreepageService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取自由页面详细信息", notes = "获取自由页面详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FreePage.List")
    public R<SaFreepagePojo> getEntity(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFreepageService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FreePage.List")
    public R<PageInfo<SaFreepagePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_FreePage.CreateDate");
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFreepageService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增自由页面", notes = "新增自由页面", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FreePage.Add")
    public R<SaFreepagePojo> create(@RequestBody String json) {
        try {
            SaFreepagePojo saFreepagePojo = JSONArray.parseObject(json, SaFreepagePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + String.format("%02d", new Random().nextInt(100));
            saFreepagePojo.setRefno(refNo);
            saFreepagePojo.setCreateby(loginUser.getRealName());   // 创建者
            saFreepagePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFreepagePojo.setCreatedate(new Date());   // 创建时间
            saFreepagePojo.setLister(loginUser.getRealname());   // 制表
            saFreepagePojo.setListerid(loginUser.getUserid());    // 制表id
            saFreepagePojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFreepageService.insert(saFreepagePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改自由页面", notes = "修改自由页面", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_FreePage.Edit")
    public R<SaFreepagePojo> update(@RequestBody String json) {
        try {
            SaFreepagePojo saFreepagePojo = JSONArray.parseObject(json, SaFreepagePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFreepagePojo.setLister(loginUser.getRealname());   // 制表
            saFreepagePojo.setListerid(loginUser.getUserid());    // 制表id
            saFreepagePojo.setModifydate(new Date());   //修改时间
            //            saFreepagePojo.setAssessor(""); // 审核员
            //            saFreepagePojo.setAssessorid(""); // 审核员id
            //            saFreepagePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFreepageService.update(saFreepagePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除自由页面", notes = "删除自由页面", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FreePage.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFreepageService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_FreePage.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaFreepagePojo saFreepagePojo = this.saFreepageService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFreepagePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

