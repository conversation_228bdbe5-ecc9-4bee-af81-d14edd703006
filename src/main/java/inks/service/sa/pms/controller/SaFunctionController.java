package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFunctionPojo;
import inks.service.sa.pms.service.SaFunctionService;
import inks.sa.common.core.service.SaRedisService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 功能中心表(Sa_Function)表控制层
 *
 * <AUTHOR>
 * @since 2025-01-03 15:38:20
 */
//@RestController
//@RequestMapping("saFunction")
public class SaFunctionController {
    @Resource
    private SaFunctionService saFunctionService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaFunctionController.class);


    @ApiOperation(value=" 获取功能中心表详细信息", notes="获取功能中心表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Function.List")
    public R<SaFunctionPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFunctionService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Function.List")
    public R<PageInfo<SaFunctionPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_Function.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saFunctionService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增功能中心表", notes="新增功能中心表", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Function.Add")
    public R<SaFunctionPojo> create(@RequestBody String json) {
        try {
            SaFunctionPojo saFunctionPojo = JSONArray.parseObject(json,SaFunctionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFunctionPojo.setCreateby(loginUser.getRealName());   // 创建者
            saFunctionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saFunctionPojo.setCreatedate(new Date());   // 创建时间
            saFunctionPojo.setLister(loginUser.getRealname());   // 制表
            saFunctionPojo.setListerid(loginUser.getUserid());    // 制表id
            saFunctionPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saFunctionService.insert(saFunctionPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改功能中心表", notes="修改功能中心表", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Function.Edit")
    public R<SaFunctionPojo> update(@RequestBody String json) {
        try {
            SaFunctionPojo saFunctionPojo = JSONArray.parseObject(json,SaFunctionPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saFunctionPojo.setLister(loginUser.getRealname());   // 制表
            saFunctionPojo.setListerid(loginUser.getUserid());    // 制表id
            saFunctionPojo.setModifydate(new Date());   //修改时间
    //            saFunctionPojo.setAssessor(""); // 审核员
    //            saFunctionPojo.setAssessorid(""); // 审核员id
    //            saFunctionPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saFunctionService.update(saFunctionPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除功能中心表", notes="删除功能中心表", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Function.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saFunctionService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Function.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaFunctionPojo saFunctionPojo = this.saFunctionService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saFunctionPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

