package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.config.oss.Storage;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaGoodsPojo;
import inks.service.sa.pms.service.SaGoodsService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.util.Date;
import java.util.Map;

/**
 * 产品(Sa_PmsGoods)表控制层
 *
 * <AUTHOR>
 * @since 2023-04-18 09:18:55
 */
public class SaGoodsController {

    private final static Logger logger = LoggerFactory.getLogger(SaGoodsController.class);

    @Resource
    private SaGoodsService saGoodsService;

    @Resource
    private SaRedisService saRedisService;


    @Resource
    @Qualifier("minioStorage")
    private Storage storage;
    @Value("${oss.bucket}")
    private String BUCKET_NAME;


    @ApiOperation(value = " 获取产品详细信息", notes = "获取产品详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaGoodsPojo> getEntity(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saGoodsService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaGoodsPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_PmsGoods.CreateDate");
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saGoodsService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增产品", notes = "新增产品", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaGoodsPojo> create(@RequestBody String json) {
        try {
            SaGoodsPojo saGoodsPojo = JSONArray.parseObject(json, SaGoodsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saGoodsPojo.setCreateby(loginUser.getRealName());   // 创建者
            saGoodsPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saGoodsPojo.setCreatedate(new Date());   // 创建时间
            saGoodsPojo.setLister(loginUser.getRealname());   // 制表
            saGoodsPojo.setListerid(loginUser.getUserid());    // 制表id
            saGoodsPojo.setModifydate(new Date());   //修改时间
            //新增方法：上传markdown内容到markdown桶里 拿到url再插入saMarkdownPojo.mdurl
            if (saGoodsPojo.getContent() != null) {
                InputStream inputStream = new ByteArrayInputStream(saGoodsPojo.getContent().getBytes(StandardCharsets.UTF_8));
                //FileInfo fileInfo = ossService.uploadInputStreamMd(inputStream, "markdown");
                //saGoodsPojo.setMdurl(fileInfo.getFileurl());
            }
            return R.ok(this.saGoodsService.insert(saGoodsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改产品", notes = "修改产品", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaGoodsPojo> update(@RequestBody String json) {
        try {
            SaGoodsPojo saGoodsPojo = JSONArray.parseObject(json, SaGoodsPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saGoodsPojo.setLister(loginUser.getRealname());   // 制表
            saGoodsPojo.setListerid(loginUser.getUserid());    // 制表id  
            saGoodsPojo.setModifydate(new Date());   //修改时间
            // 修改方法：1先把之前的mdurl，从minio中删除  2再上传markdown内容到markdown桶里 拿到url再插入saMarkdownPojo.mdurl
            if (saGoodsPojo.getMdurl() != null) {
                storage.removeObject(BUCKET_NAME, saGoodsPojo.getMdurl());
            }
            if (saGoodsPojo.getContent() != null) {
                InputStream inputStream = new ByteArrayInputStream(saGoodsPojo.getContent().getBytes(StandardCharsets.UTF_8));
                //FileInfo fileInfo = ossService.uploadInputStreamMd(inputStream, "markdown");
                //saGoodsPojo.setMdurl(fileInfo.getFileurl());
            }
            return R.ok(this.saGoodsService.update(saGoodsPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除产品", notes = "删除产品", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String mdurl = this.saGoodsService.getEntity(key).getMdurl();
            // 删除方法：1先把之前的mdurl，从minio中删除
            if (StringUtils.isNotBlank(mdurl)) {
                storage.removeObject(BUCKET_NAME, mdurl);
            }
            return R.ok(this.saGoodsService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaGoodsPojo saGoodsPojo = this.saGoodsService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saGoodsPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

