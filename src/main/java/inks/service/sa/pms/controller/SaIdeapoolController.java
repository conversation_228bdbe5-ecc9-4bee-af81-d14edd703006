package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaIdeapoolPojo;
import inks.service.sa.pms.service.SaIdeapoolService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 创意池(Sa_IdeaPool)表控制层
 *
 * <AUTHOR>
 * @since 2024-12-04 15:23:23
 */
//@RestController
//@RequestMapping("saIdeapool")
public class SaIdeapoolController {
    private final static Logger logger = LoggerFactory.getLogger(SaIdeapoolController.class);
    @Resource
    private SaBillcodeService saBillcodeService;
    @Resource
    private SaIdeapoolService saIdeapoolService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取创意池详细信息", notes = "获取创意池详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_IdeaPool.List")
    public R<SaIdeapoolPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saIdeapoolService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_IdeaPool.List")
    public R<PageInfo<SaIdeapoolPojo>> getPageList(@RequestBody String json,
                                                   @RequestParam(required = false) Integer publicmark,
                                                   @RequestParam(required = false) Integer finishmark) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_IdeaPool.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saIdeapoolService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增创意池", notes = "新增创意池", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_IdeaPool.Add")
    public R<SaIdeapoolPojo> create(@RequestBody String json) {
        try {
            SaIdeapoolPojo saIdeapoolPojo = JSONArray.parseObject(json, SaIdeapoolPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = saBillcodeService.getSerialNo("S06M37B1", loginUser.getTenantid(), "Sa_IdeaPool");
            saIdeapoolPojo.setRefno(refNo);
            saIdeapoolPojo.setCreateby(loginUser.getRealName());   // 创建者
            saIdeapoolPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saIdeapoolPojo.setCreatedate(new Date());   // 创建时间
            saIdeapoolPojo.setLister(loginUser.getRealname());   // 制表
            saIdeapoolPojo.setListerid(loginUser.getUserid());    // 制表id
            saIdeapoolPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saIdeapoolService.insert(saIdeapoolPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改创意池", notes = "修改创意池", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_IdeaPool.Edit")
    public R<SaIdeapoolPojo> update(@RequestBody String json) {
        try {
            SaIdeapoolPojo saIdeapoolPojo = JSONArray.parseObject(json, SaIdeapoolPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saIdeapoolPojo.setLister(loginUser.getRealname());   // 制表
            saIdeapoolPojo.setListerid(loginUser.getUserid());    // 制表id
            saIdeapoolPojo.setModifydate(new Date());   //修改时间
            //            saIdeapoolPojo.setAssessor(""); // 审核员
            //            saIdeapoolPojo.setAssessorid(""); // 审核员id
            //            saIdeapoolPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saIdeapoolService.update(saIdeapoolPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除创意池", notes = "删除创意池", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_IdeaPool.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saIdeapoolService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_IdeaPool.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaIdeapoolPojo saIdeapoolPojo = this.saIdeapoolService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saIdeapoolPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

