package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaImplementplanPojo;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemPojo;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemdetailPojo;
import inks.service.sa.pms.service.SaImplementplanService;
import inks.service.sa.pms.service.SaImplementplanitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 实施活动计划(Sa_ImplementPlan)表控制层
 *
 * <AUTHOR>
 * @since 2024-10-06 14:40:16
 */
//@RestController
//@RequestMapping("saImplementplan")
public class SaImplementplanController {

    private final static Logger logger = LoggerFactory.getLogger(SaImplementplanController.class);
    @Resource
    private SaImplementplanService saImplementplanService;
    @Resource
    private SaImplementplanitemService saImplementplanitemService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取实施活动计划详细信息", notes = "获取实施活动计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.List")
    public R<SaImplementplanPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saImplementplanService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.List")
    public R<PageInfo<SaImplementplanitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_ImplementPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saImplementplanService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取实施活动计划详细信息", notes = "获取实施活动计划详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.List")
    public R<SaImplementplanPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saImplementplanService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.List")
    public R<PageInfo<SaImplementplanPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_ImplementPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saImplementplanService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.List")
    public R<PageInfo<SaImplementplanPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_ImplementPlan.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saImplementplanService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增实施活动计划", notes = "新增实施活动计划", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.Add")
    public R<SaImplementplanPojo> create(@RequestBody String json) {
        try {
            SaImplementplanPojo saImplementplanPojo = JSONArray.parseObject(json, SaImplementplanPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saImplementplanPojo.setCreateby(loginUser.getRealName());   // 创建者
            saImplementplanPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saImplementplanPojo.setCreatedate(new Date());   // 创建时间
            saImplementplanPojo.setLister(loginUser.getRealname());   // 制表
            saImplementplanPojo.setListerid(loginUser.getUserid());    // 制表id            
            saImplementplanPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saImplementplanService.insert(saImplementplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改实施活动计划", notes = "修改实施活动计划", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.Edit")
    public R<SaImplementplanPojo> update(@RequestBody String json) {
        try {
            SaImplementplanPojo saImplementplanPojo = JSONArray.parseObject(json, SaImplementplanPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saImplementplanPojo.setLister(loginUser.getRealname());   // 制表
            saImplementplanPojo.setListerid(loginUser.getUserid());    // 制表id   
            saImplementplanPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saImplementplanService.update(saImplementplanPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除实施活动计划", notes = "删除实施活动计划", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saImplementplanService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    @ApiOperation(value = " 新增实施活动计划Item", notes = "新增实施活动计划Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.Add")
    public R<SaImplementplanitemPojo> createItem(@RequestBody String json) {
        try {
            SaImplementplanitemPojo saImplementplanitemPojo = JSONArray.parseObject(json, SaImplementplanitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saImplementplanitemService.insert(saImplementplanitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 修改实施活动计划Item", notes = "修改实施活动计划Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.Edit")
    public R<SaImplementplanitemPojo> updateItem(@RequestBody String json) {
        try {
            SaImplementplanitemPojo saImplementplanitemPojo = JSONArray.parseObject(json, SaImplementplanitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saImplementplanitemService.update(saImplementplanitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除实施活动计划Item", notes = "删除实施活动计划Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saImplementplanitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ImplementPlan.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaImplementplanPojo saImplementplanPojo = this.saImplementplanService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saImplementplanPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saImplementplanPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaImplementplanitemPojo saImplementplanitemPojo = new SaImplementplanitemPojo();
                    saImplementplanPojo.getItem().add(saImplementplanitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saImplementplanPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

