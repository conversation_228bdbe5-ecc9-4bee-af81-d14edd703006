package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.PrintGoodsPojo;
import inks.service.sa.pms.domain.pojo.SaJoborderPojo;
import inks.service.sa.pms.service.SaJoborderService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.*;

import static inks.common.core.utils.bean.BeanUtils.attrcostListToMaps;

/**
 * 服务派工(Sa_JobOrder)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-03 14:27:16
 */
public class SaJoborderController {
    @Resource
    private SaBillcodeService saBillcodeService;

    private final static Logger logger = LoggerFactory.getLogger(SaJoborderController.class);

    @Resource
    private SaJoborderService saJoborderService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取服务派工详细信息", notes = "获取服务派工详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_JobOrder.List")
    public R<SaJoborderPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saJoborderService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_JobOrder.List")
    public R<PageInfo<SaJoborderPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_JobOrder.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saJoborderService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增服务派工", notes = "新增服务派工", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_JobOrder.Add")
    public R<SaJoborderPojo> create(@RequestBody String json) {
        try {
            SaJoborderPojo saJoborderPojo = JSONArray.parseObject(json, SaJoborderPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            // sa生成单据编码
            String refNo = saBillcodeService.getSerialNo("S06M07B1", loginUser.getTenantid(), "Sa_JobOrder");
            saJoborderPojo.setRefno(refNo);
            saJoborderPojo.setCreateby(loginUser.getRealName());   // 创建者
            saJoborderPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saJoborderPojo.setCreatedate(new Date());   // 创建时间
            saJoborderPojo.setLister(loginUser.getRealname());   // 制表
            saJoborderPojo.setListerid(loginUser.getUserid());    // 制表id
            saJoborderPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saJoborderService.insert(saJoborderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改服务派工", notes = "修改服务派工", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_JobOrder.Edit")
    public R<SaJoborderPojo> update(@RequestBody String json) {
        try {
            SaJoborderPojo saJoborderPojo = JSONArray.parseObject(json, SaJoborderPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saJoborderPojo.setLister(loginUser.getRealname());   // 制表
            saJoborderPojo.setListerid(loginUser.getUserid());    // 制表id
            saJoborderPojo.setModifydate(new Date());   //修改时间
//            saJoborderPojo.setAssessor(""); // 审核员
//            saJoborderPojo.setAssessorid(""); // 审核员id
//            saJoborderPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saJoborderService.update(saJoborderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除服务派工", notes = "删除服务派工", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_JobOrder.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saJoborderService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核服务派工", notes = "审核服务派工", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_JobOrder.Approval")
    public R<SaJoborderPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaJoborderPojo saJoborderPojo = this.saJoborderService.getEntity(key);
            if (saJoborderPojo.getAssessor().equals("")) {
                saJoborderPojo.setAssessor(loginUser.getRealname()); //审核员
                saJoborderPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                saJoborderPojo.setAssessor(""); //审核员
                saJoborderPojo.setAssessorid(""); //审核员
            }
            saJoborderPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saJoborderService.approval(saJoborderPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_JobOrder.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaJoborderPojo saJoborderPojo = this.saJoborderService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saJoborderPojo);
        // 加入公司信息
        if (!Objects.isNull(loginUser.getTenantinfo())) {
            inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        }        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "云打印报表", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/printWebBill", method = RequestMethod.GET)
    public R<String> printWebBill(String key, String ptid, String sn, Integer cmd) {
        try {
            //从redis中获取Reprot内容
            ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            if (reportsPojo == null) {
                return R.fail("未找到报表");
            }
            if (sn == null)
                sn = reportsPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //=========获取单据表头信息========
            SaJoborderPojo saJoborderPojo = this.saJoborderService.getEntity(key);
//            // 检查是否审核后方可打印单据
//            Map<String, Object> tencfg = saRedisService.getCacheObject("tenant_config:" + loginUser.getTenantid());
//            String printapproved = tencfg.get("system.bill.printapproved").toString();
//            if (printapproved != null && printapproved.equals("true") && saJoborderPojo.getAssessor().equals("")) {
//                throw new BaseBusinessException("请先审核单据");
//            }
            // 获取单据表头.表头转MAP
            Map<String, Object> map = BeanUtils.beanToMap(saJoborderPojo);
            // 获取单据表头.加入公司信息
            if (!Objects.isNull(loginUser.getTenantinfo())) {
                inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
            }
            //=========获取单据Item信息 这里直接接收的对象List的json========
            String matjson = saJoborderPojo.getMatjson();
            //如果传入matjson为空，则new一条空的List
            List<PrintGoodsPojo> printGoodsPojos = new ArrayList<PrintGoodsPojo>();
            if ("[]".equals(matjson)) {
                printGoodsPojos.add(new PrintGoodsPojo());
            } else {
                printGoodsPojos = JSONArray.parseArray(matjson, PrintGoodsPojo.class);
            }
            // 单据Item. 带属性List转为Map  EricRen 20220427
            List<Map<String, Object>> lst = attrcostListToMaps(printGoodsPojos);

            // === 整理Map.row=====
            Map<String, Object> maprow = new LinkedHashMap<>();
            maprow.put("row", lst);
            // === 整理report=xml+grparam=====
            Map<String, Object> mapreport = new LinkedHashMap<>();
            mapreport.put("xml", maprow);
            mapreport.put("_grparam", map);
            // ====生成report ==== 注间 xml必须在_grparam 前 有LinkedHashMap 销定排序
            Map<String, Object> mapdata = new LinkedHashMap<>();
            mapdata.put("report", mapreport);
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mapdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "服务派单" + saJoborderPojo.getRefno());    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            mapPrint.put("data", ptJson);   //  打印数据
            // 云打印模板，加载
            if (reportsPojo.getGrfdata() != null && !"".equals(reportsPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (reportsPojo.getTempurl() != null && !"".equals(reportsPojo.getTempurl())) {
                mapPrint.put("temp", reportsPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", reportsPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", reportsPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());

            // 刷入打印Num++
            SaJoborderPojo joborderPojo = new SaJoborderPojo();
            joborderPojo.setId(saJoborderPojo.getId());
            joborderPojo.setPrintcount(saJoborderPojo.getPrintcount() + 1);
//            joborderPojo.setTenantid(saJoborderPojo.getTenantid());
            this.saJoborderService.update(joborderPojo);

            // 本地打印
//            if (sn == null || sn.equals("local") || sn.equals("localsec") || sn.equals("localthi")) {
            return R.ok(JSONObject.toJSONString(mapPrint));
//            } else {
//                // 远程SN打印
//                R<String> rPrint = this.utilsFeignService.webPrinting(sn, JSONObject.toJSONString(mapPrint), loginUser.getToken());
//                if (rPrint.getCode() == 200) {
//                    return R.ok();
//                } else {
//                    return R.fail(rPrint.getMsg());
//                }
//            }
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

