package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaKnowledgePojo;
import inks.service.sa.pms.service.SaKnowledgeService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 知识库(Sa_Knowledge)表控制层
 *
 * <AUTHOR>
 * @since 2022-11-23 13:12:49
 */
public class SaKnowledgeController {

    private final static Logger logger = LoggerFactory.getLogger(SaKnowledgeController.class);

    @Resource
    private SaKnowledgeService saKnowledgeService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取知识库详细信息", notes = "获取知识库详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaKnowledgePojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //知识库累加浏览量
            SaKnowledgePojo saKnowledgePojo = saKnowledgeService.getEntity(key);
            Integer textlooktimes = saKnowledgePojo.getTextlooktimes();
            saKnowledgePojo.setTextlooktimes(++textlooktimes);
            saKnowledgeService.update(saKnowledgePojo);
            return R.ok(this.saKnowledgeService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaKnowledgePojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Knowledge.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saKnowledgeService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增知识库", notes = "新增知识库", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaKnowledgePojo> create(@RequestBody String json) {
        try {
            SaKnowledgePojo saKnowledgePojo = JSONArray.parseObject(json, SaKnowledgePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            saKnowledgePojo.setCreateby(loginUser.getRealName());   // 创建者
            saKnowledgePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saKnowledgePojo.setCreatedate(new Date());   // 创建时间
            saKnowledgePojo.setLister(loginUser.getRealname());   // 制表
            saKnowledgePojo.setListerid(loginUser.getUserid());    // 制表id
            saKnowledgePojo.setModifydate(new Date());   //修改时间
            saKnowledgePojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.saKnowledgeService.insert(saKnowledgePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改知识库", notes = "修改知识库", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaKnowledgePojo> update(@RequestBody String json) {
        try {
            SaKnowledgePojo saKnowledgePojo = JSONArray.parseObject(json, SaKnowledgePojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saKnowledgePojo.setLister(loginUser.getRealname());   // 制表
            saKnowledgePojo.setListerid(loginUser.getUserid());    // 制表id
            saKnowledgePojo.setTenantid(loginUser.getTenantid());   //租户id
            saKnowledgePojo.setModifydate(new Date());   //修改时间
//            saKnowledgePojo.setAssessor(""); // 审核员
//            saKnowledgePojo.setAssessorid(""); // 审核员id
//            saKnowledgePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saKnowledgeService.update(saKnowledgePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除知识库", notes = "删除知识库", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saKnowledgeService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核知识库", notes = "审核知识库", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    public R<SaKnowledgePojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaKnowledgePojo saKnowledgePojo = this.saKnowledgeService.getEntity(key);
            if (saKnowledgePojo.getAssessor().equals("")) {
                saKnowledgePojo.setAssessor(loginUser.getRealname()); //审核员
                saKnowledgePojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                saKnowledgePojo.setAssessor(""); //审核员
                saKnowledgePojo.setAssessorid(""); //审核员
            }
            saKnowledgePojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saKnowledgeService.approval(saKnowledgePojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaKnowledgePojo saKnowledgePojo = this.saKnowledgeService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saKnowledgePojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

