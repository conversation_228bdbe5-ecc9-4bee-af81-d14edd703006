package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.config.oss.Storage;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaMarkdownPojo;
import inks.service.sa.pms.service.SaMarkdownService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Random;

/**
 * MarkDown(LNK简书)(Sa_Markdown)表控制层
 *
 * <AUTHOR>
 * @since 2023-03-31 15:17:18
 */
//@RestController
//@RequestMapping("saMarkdown")
public class SaMarkdownController {

    private final static Logger logger = LoggerFactory.getLogger(SaMarkdownController.class);

    @Resource
    private SaMarkdownService saMarkdownService;

    @Resource
    private SaRedisService saRedisService;

    @Value("${oss.bucket}")
    private String BUCKET_NAME;
    @Resource
    @Qualifier("minioStorage")
    private Storage storage;


    @ApiOperation(value = " 获取详细信息", notes = "获取详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaMarkdownPojo> getEntity(String key) {
        try {
            return R.ok(this.saMarkdownService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaMarkdownPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Markdown.CreateDate");
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMarkdownService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * @return R<SaMarkdownPojo>
     * @Description 新增数据   拿到markdown的内容content上传到markdown桶里
     * <AUTHOR>
     * @param[1] json
     * @time 2023/5/12 11:07
     */
    @ApiOperation(value = " 新增 拿到markdown的内容content上传到markdown桶里", notes = "新增", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaMarkdownPojo> create(@RequestBody String json) {
        try {
            SaMarkdownPojo saMarkdownPojo = JSONArray.parseObject(json, SaMarkdownPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + String.format("%02d", new Random().nextInt(100));
            saMarkdownPojo.setRefno(refNo);
            saMarkdownPojo.setCreateby(loginUser.getRealName());   // 创建者
            saMarkdownPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saMarkdownPojo.setCreatedate(new Date());   // 创建时间
            saMarkdownPojo.setLister(loginUser.getRealname());   // 制表
            saMarkdownPojo.setListerid(loginUser.getUserid());    // 制表id  
            saMarkdownPojo.setModifydate(new Date());   //修改时间

            //TODO 新增方法：上传markdown内容到markdown桶里 拿到url再插入saMarkdownPojo.mdurl
            if (saMarkdownPojo.getContent() != null) {
                InputStream inputStream = new ByteArrayInputStream(saMarkdownPojo.getContent().getBytes(StandardCharsets.UTF_8));
                //FileInfo fileInfo = ossService.uploadInputStreamMd(inputStream, "markdown");
                //saMarkdownPojo.setMdurl(fileInfo.getFileurl());
            }
            return R.ok(this.saMarkdownService.insert(saMarkdownPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改", notes = "修改", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaMarkdownPojo> update(@RequestBody String json) {
        try {
            SaMarkdownPojo saMarkdownPojo = JSONArray.parseObject(json, SaMarkdownPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMarkdownPojo.setLister(loginUser.getRealname());   // 制表
            saMarkdownPojo.setListerid(loginUser.getUserid());    // 制表id  
            saMarkdownPojo.setModifydate(new Date());   //修改时间

            // 修改方法：1先把之前的mdurl，从minio中删除  2再上传markdown内容到markdown桶里 拿到url再插入saMarkdownPojo.mdurl
            if (saMarkdownPojo.getMdurl() != null) {
                storage.removeObject(BUCKET_NAME, saMarkdownPojo.getMdurl());
            }
            if (saMarkdownPojo.getContent() != null) {
                InputStream inputStream = new ByteArrayInputStream(saMarkdownPojo.getContent().getBytes(StandardCharsets.UTF_8));
                //TODO FileInfo fileInfo = ossService.uploadInputStreamMd(inputStream, "markdown");
                //saMarkdownPojo.setMdurl(fileInfo.getFileurl());
            }
            return R.ok(this.saMarkdownService.update(saMarkdownPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除", notes = "删除", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String mdurl = this.saMarkdownService.getEntity(key).getMdurl();
            // 删除方法：1先把之前的mdurl，从minio中删除
            storage.removeObject(BUCKET_NAME, mdurl);
            return R.ok(this.saMarkdownService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核MarkDown(LNK简书)", notes = "审核MarkDown(LNK简书)", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    public R<SaMarkdownPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaMarkdownPojo saMarkdownPojo = this.saMarkdownService.getEntity(key);
            if (saMarkdownPojo.getAssessor().equals("")) {
                saMarkdownPojo.setAssessor(loginUser.getRealname()); //审核员
                saMarkdownPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                saMarkdownPojo.setAssessor(""); //审核员
                saMarkdownPojo.setAssessorid(""); //审核员
            }
            saMarkdownPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saMarkdownService.approval(saMarkdownPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaMarkdownPojo saMarkdownPojo = this.saMarkdownService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saMarkdownPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

