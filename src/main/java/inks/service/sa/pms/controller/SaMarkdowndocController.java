package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.config.oss.Storage;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaMarkdowndocPojo;
import inks.service.sa.pms.service.SaMarkdowndocService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Date;
import java.util.Map;
import java.util.Random;

/**
 * MarkDown(Doc文档)(Sa_MarkdownDoc)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:26
 */
//@RestController
//@RequestMapping("saMarkdowndoc")
public class SaMarkdowndocController {

    private final static Logger logger = LoggerFactory.getLogger(SaMarkdowndocController.class);

    @Resource
    private SaMarkdowndocService saMarkdowndocService;

    @Resource
    private SaRedisService saRedisService;

    @Value("${oss.bucket}")
    private String BUCKET_NAME;
    @Resource
    @Qualifier("minioStorage")
    private Storage storage;


    @ApiOperation(value = " 获取MarkDown(Doc文档)详细信息", notes = "获取MarkDown(Doc文档)详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    // @PreAuthoriz(hasPermi = "Sa_MarkdownDoc.List")
    public R<SaMarkdowndocPojo> getEntity(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMarkdowndocService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_MarkdownDoc.List")
    public R<PageInfo<SaMarkdowndocPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_MarkdownDoc.CreateDate");
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMarkdowndocService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增MarkDown(Doc文档) 拿到markdown的内容content上传到markdown桶里", notes = "新增MarkDown(Doc文档)", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_MarkdownDoc.Add")
    public R<SaMarkdowndocPojo> create(@RequestBody String json) {
        try {
            SaMarkdowndocPojo saMarkdowndocPojo = JSONArray.parseObject(json, SaMarkdowndocPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
            String refNo = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss")) + String.format("%02d", new Random().nextInt(100));
            saMarkdowndocPojo.setRefno(refNo);  // 单据编码
            saMarkdowndocPojo.setCreateby(loginUser.getRealName());   // 创建者
            saMarkdowndocPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saMarkdowndocPojo.setCreatedate(new Date());   // 创建时间
            saMarkdowndocPojo.setLister(loginUser.getRealname());   // 制表
            saMarkdowndocPojo.setListerid(loginUser.getUserid());    // 制表id  
            saMarkdowndocPojo.setModifydate(new Date());   //修改时间

            //新增方法：上传markdown内容到markdown桶里 拿到url再插入saMarkdownPojo.mdurl
            if (saMarkdowndocPojo.getContent() != null) {
                InputStream inputStream = new ByteArrayInputStream(saMarkdowndocPojo.getContent().getBytes(StandardCharsets.UTF_8));
                //FileInfo fileInfo = ossService.uploadInputStreamMd(inputStream, "markdown");
                //saMarkdowndocPojo.setMdurl(fileInfo.getFileurl());
            }
            return R.ok(this.saMarkdowndocService.insert(saMarkdowndocPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改MarkDown(Doc文档) // 修改方法：1先把之前的mdurl，从minio中删除  2再上传markdown内容到markdown桶里 拿到url再插入saMarkdownPojo.mdurl", notes = "修改MarkDown(Doc文档)", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_MarkdownDoc.Edit")
    public R<SaMarkdowndocPojo> update(@RequestBody String json) {
        try {
            SaMarkdowndocPojo saMarkdowndocPojo = JSONArray.parseObject(json, SaMarkdowndocPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMarkdowndocPojo.setLister(loginUser.getRealname());   // 制表
            saMarkdowndocPojo.setListerid(loginUser.getUserid());    // 制表id  
            saMarkdowndocPojo.setModifydate(new Date());   //修改时间

            // 修改方法：1先把之前的mdurl，从minio中删除  2再上传markdown内容到markdown桶里 拿到url再插入saMarkdownPojo.mdurl
            if (saMarkdowndocPojo.getMdurl() != null) {
                storage.removeObject(BUCKET_NAME, saMarkdowndocPojo.getMdurl());
            }
            if (saMarkdowndocPojo.getContent() != null) {
                InputStream inputStream = new ByteArrayInputStream(saMarkdowndocPojo.getContent().getBytes(StandardCharsets.UTF_8));
                //FileInfo fileInfo = ossService.uploadInputStreamMd(inputStream, "markdown");
                //saMarkdowndocPojo.setMdurl(fileInfo.getFileurl());
            }
            return R.ok(this.saMarkdowndocService.update(saMarkdowndocPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除MarkDown(Doc文档)", notes = "删除MarkDown(Doc文档)", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_MarkdownDoc.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String mdurl = this.saMarkdowndocService.getEntity(key).getMdurl();
            // 删除方法：1先把之前的mdurl，从minio中删除
            storage.removeObject(BUCKET_NAME, mdurl);
            return R.ok(this.saMarkdowndocService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核MarkDown(Doc文档)", notes = "审核MarkDown(Doc文档)", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_MarkdownDoc.Approval")
    public R<SaMarkdowndocPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaMarkdowndocPojo saMarkdowndocPojo = this.saMarkdowndocService.getEntity(key);
            if (saMarkdowndocPojo.getAssessor().equals("")) {
                saMarkdowndocPojo.setAssessor(loginUser.getRealname()); //审核员
                saMarkdowndocPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                saMarkdowndocPojo.setAssessor(""); //审核员
                saMarkdowndocPojo.setAssessorid(""); //审核员
            }
            saMarkdowndocPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saMarkdowndocService.approval(saMarkdowndocPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_MarkdownDoc.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaMarkdowndocPojo saMarkdowndocPojo = this.saMarkdowndocService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saMarkdowndocPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

