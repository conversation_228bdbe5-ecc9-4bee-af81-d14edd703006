package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaMarkdownPojo;
import inks.service.sa.pms.domain.pojo.SaMdgroupPojo;
import inks.service.sa.pms.service.SaMdgroupService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * MD文档分组(Sa_MdGroup)表控制层
 *
 * <AUTHOR>
 * @since 2023-05-08 11:23:51
 */
//@RestController
//@RequestMapping("saMdgroup")
public class SaMdgroupController {

    private final static Logger logger = LoggerFactory.getLogger(SaMdgroupController.class);

    @Resource
    private SaMdgroupService saMdgroupService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取MD文档分组详细信息", notes = "获取MD文档分组详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    public R<SaMdgroupPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMdgroupService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    public R<PageInfo<SaMdgroupPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_MdGroup.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMdgroupService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增MD文档分组", notes = "新增MD文档分组", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    public R<SaMdgroupPojo> create(@RequestBody String json) {
        try {
            SaMdgroupPojo saMdgroupPojo = JSONArray.parseObject(json, SaMdgroupPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMdgroupPojo.setCreatedate(new Date());   // 创建时间
            saMdgroupPojo.setLister(loginUser.getRealname());   // 制表
            saMdgroupPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saMdgroupService.insert(saMdgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改MD文档分组", notes = "修改MD文档分组", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    public R<SaMdgroupPojo> update(@RequestBody String json) {
        try {
            SaMdgroupPojo saMdgroupPojo = JSONArray.parseObject(json, SaMdgroupPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMdgroupPojo.setLister(loginUser.getRealname());   // 制表
            saMdgroupPojo.setModifydate(new Date());   //修改时间
//            saMdgroupPojo.setAssessor(""); // 审核员
//            saMdgroupPojo.setAssessorid(""); // 审核员id
//            saMdgroupPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saMdgroupService.update(saMdgroupPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除MD文档分组", notes = "删除MD文档分组", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaMarkdownPojo> markdownsByGroupId = saMdgroupService.getMarkdownsByGroupId(key);
            if (markdownsByGroupId.size() > 0) {
                return R.fail("该分组下存在Markdown文档，禁止删除");
            }
            return R.ok(this.saMdgroupService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaMdgroupPojo saMdgroupPojo = this.saMdgroupService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saMdgroupPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

