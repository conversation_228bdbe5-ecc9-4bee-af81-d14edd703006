package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaMarkdowndocPojo;
import inks.service.sa.pms.domain.pojo.SaMdgroupdocPojo;
import inks.service.sa.pms.service.SaMdgroupdocService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * MarkDown分组(Doc文档)(Sa_MdGroupDoc)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:40
 */
//@RestController
//@RequestMapping("saMdgroupdoc")
public class SaMdgroupdocController {

    private final static Logger logger = LoggerFactory.getLogger(SaMdgroupdocController.class);

    @Resource
    private SaMdgroupdocService saMdgroupdocService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取MarkDown分组(Doc文档)详细信息", notes = "获取MarkDown分组(Doc文档)详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    // @PreAuthoriz(hasPermi = "Sa_MdGroupDoc.List")
    public R<SaMdgroupdocPojo> getEntity(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMdgroupdocService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    // @PreAuthoriz(hasPermi = "Sa_MdGroupDoc.List")
    public R<PageInfo<SaMdgroupdocPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_MdGroupDoc.CreateDate");
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMdgroupdocService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增MarkDown分组(Doc文档)", notes = "新增MarkDown分组(Doc文档)", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    // @PreAuthoriz(hasPermi = "Sa_MdGroupDoc.Add")
    public R<SaMdgroupdocPojo> create(@RequestBody String json) {
        try {
            SaMdgroupdocPojo saMdgroupdocPojo = JSONArray.parseObject(json, SaMdgroupdocPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMdgroupdocPojo.setCreatedate(new Date());   // 创建时间
            saMdgroupdocPojo.setLister(loginUser.getRealname());   // 制表
            saMdgroupdocPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saMdgroupdocService.insert(saMdgroupdocPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改MarkDown分组(Doc文档)", notes = "修改MarkDown分组(Doc文档)", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    // @PreAuthoriz(hasPermi = "Sa_MdGroupDoc.Edit")
    public R<SaMdgroupdocPojo> update(@RequestBody String json) {
        try {
            SaMdgroupdocPojo saMdgroupdocPojo = JSONArray.parseObject(json, SaMdgroupdocPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMdgroupdocPojo.setLister(loginUser.getRealname());   // 制表
            saMdgroupdocPojo.setModifydate(new Date());   //修改时间
//            saMdgroupdocPojo.setAssessor(""); // 审核员
//            saMdgroupdocPojo.setAssessorid(""); // 审核员id
//            saMdgroupdocPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saMdgroupdocService.update(saMdgroupdocPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除MarkDown分组(Doc文档)", notes = "删除MarkDown分组(Doc文档)", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_MdGroupDoc.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            List<SaMarkdowndocPojo> markdownsByGroupId = saMdgroupdocService.getMarkdownsByGroupId(key);
            if (markdownsByGroupId.size() > 0) {
                return R.fail("该分组下存在Markdown文档，禁止删除");
            }
            return R.ok(this.saMdgroupdocService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_MdGroupDoc.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaMdgroupdocPojo saMdgroupdocPojo = this.saMdgroupdocService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saMdgroupdocPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

