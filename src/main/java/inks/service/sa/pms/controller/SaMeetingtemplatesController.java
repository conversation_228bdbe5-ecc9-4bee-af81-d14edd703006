package inks.service.sa.pms.controller;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.domain.R;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetingtemplatesPojo;
import inks.service.sa.pms.service.SaMeetingtemplatesService;
import inks.sa.common.core.service.SaRedisService;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import net.sf.jasperreports.engine.*;
import org.springframework.web.bind.annotation.*;
import io.swagger.annotations.ApiOperation;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * 会议模板表(Sa_MeetingTemplates)表控制层
 *
 * <AUTHOR>
 * @since 2025-06-17 09:46:22
 */
//@RestController
//@RequestMapping("saMeetingtemplates")
public class SaMeetingtemplatesController {
    @Resource
    private SaMeetingtemplatesService saMeetingtemplatesService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaMeetingtemplatesController.class);


    @ApiOperation(value=" 获取会议模板表详细信息", notes="获取会议模板表详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_MeetingTemplates.List")
    public R<SaMeetingtemplatesPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMeetingtemplatesService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_MeetingTemplates.List")
    public R<PageInfo<SaMeetingtemplatesPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_MeetingTemplates.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMeetingtemplatesService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增会议模板表", notes="新增会议模板表", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_MeetingTemplates.Add")
    public R<SaMeetingtemplatesPojo> create(@RequestBody String json) {
        try {
            SaMeetingtemplatesPojo saMeetingtemplatesPojo = JSONArray.parseObject(json,SaMeetingtemplatesPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMeetingtemplatesPojo.setCreateby(loginUser.getRealName());   // 创建者
            saMeetingtemplatesPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saMeetingtemplatesPojo.setCreatedate(new Date());   // 创建时间
            saMeetingtemplatesPojo.setLister(loginUser.getRealname());   // 制表
            saMeetingtemplatesPojo.setListerid(loginUser.getUserid());    // 制表id
            saMeetingtemplatesPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saMeetingtemplatesService.insert(saMeetingtemplatesPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改会议模板表", notes="修改会议模板表", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_MeetingTemplates.Edit")
    public R<SaMeetingtemplatesPojo> update(@RequestBody String json) {
        try {
            SaMeetingtemplatesPojo saMeetingtemplatesPojo = JSONArray.parseObject(json,SaMeetingtemplatesPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMeetingtemplatesPojo.setLister(loginUser.getRealname());   // 制表
            saMeetingtemplatesPojo.setListerid(loginUser.getUserid());    // 制表id
            saMeetingtemplatesPojo.setModifydate(new Date());   //修改时间
    //            saMeetingtemplatesPojo.setAssessor(""); // 审核员
    //            saMeetingtemplatesPojo.setAssessorid(""); // 审核员id
    //            saMeetingtemplatesPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saMeetingtemplatesService.update(saMeetingtemplatesPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除会议模板表", notes="删除会议模板表", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_MeetingTemplates.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMeetingtemplatesService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_MeetingTemplates.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaMeetingtemplatesPojo saMeetingtemplatesPojo = this.saMeetingtemplatesService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saMeetingtemplatesPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

