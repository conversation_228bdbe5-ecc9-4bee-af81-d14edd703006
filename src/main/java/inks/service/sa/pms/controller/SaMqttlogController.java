package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaMqttlogPojo;
import inks.service.sa.pms.service.SaMqttlogService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * MQTT日志(Sa_MqttLog)表控制层
 *
 * <AUTHOR>
 * @since 2024-02-26 12:57:20
 */
//@RestController
//@RequestMapping("saMqttlog")
public class SaMqttlogController {

    private final static Logger logger = LoggerFactory.getLogger(SaMqttlogController.class);

    @Resource
    private SaMqttlogService saMqttlogService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取MQTT日志详细信息", notes = "获取MQTT日志详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_MqttLog.List")
    public R<SaMqttlogPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMqttlogService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 code:MsgContent中code对应的值  JSON_EXTRACT(MsgContent,'$.code')='login'", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_MqttLog.List")
    public R<PageInfo<SaMqttlogPojo>> getPageList(@RequestBody String json, String code) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_MqttLog.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            if (StringUtils.isNotBlank(code)) {// {"date":"2024-02-27 14:20:31","code":"login","content":"Eric,IP:*************"}
                qpfilter += " and JSON_EXTRACT(MsgContent,'$.code')='" + code + "'";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saMqttlogService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增MQTT日志", notes = "新增MQTT日志", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_MqttLog.Add")
    public R<SaMqttlogPojo> create(@RequestBody String json) {
        try {
            SaMqttlogPojo saMqttlogPojo = JSONArray.parseObject(json, SaMqttlogPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMqttlogPojo.setCreateby(loginUser.getRealName());   // 创建者
            saMqttlogPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saMqttlogPojo.setCreatedate(new Date());   // 创建时间
            saMqttlogPojo.setLister(loginUser.getRealname());   // 制表
            saMqttlogPojo.setListerid(loginUser.getUserid());    // 制表id  
            saMqttlogPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saMqttlogService.insert(saMqttlogPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改MQTT日志", notes = "修改MQTT日志", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_MqttLog.Edit")
    public R<SaMqttlogPojo> update(@RequestBody String json) {
        try {
            SaMqttlogPojo saMqttlogPojo = JSONArray.parseObject(json, SaMqttlogPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saMqttlogPojo.setLister(loginUser.getRealname());   // 制表
            saMqttlogPojo.setListerid(loginUser.getUserid());    // 制表id  
            saMqttlogPojo.setModifydate(new Date());   //修改时间
//            saMqttlogPojo.setAssessor(""); // 审核员
//            saMqttlogPojo.setAssessorid(""); // 审核员id
//            saMqttlogPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saMqttlogService.update(saMqttlogPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除MQTT日志", notes = "删除MQTT日志", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_MqttLog.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saMqttlogService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_MqttLog.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaMqttlogPojo saMqttlogPojo = this.saMqttlogService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saMqttlogPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

