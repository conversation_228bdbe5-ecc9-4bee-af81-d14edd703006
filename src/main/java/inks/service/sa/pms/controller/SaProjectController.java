package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.SecurityUtils;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.domain.vo.SaEngineerPojo;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.*;
import inks.service.sa.pms.service.SaProjectService;
import inks.service.sa.pms.service.SaProjectitemService;
import inks.service.sa.pms.service.SaProjectlabelService;
import inks.service.sa.pms.service.SaProjectstatusService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工程项目(SaProject)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-26 14:30:57
 */
//@RestController
//@RequestMapping("saProject")
public class SaProjectController {

    private final static Logger logger = LoggerFactory.getLogger(SaProjectController.class);

    @Resource
    private SaProjectService saProjectService;
    /**
     * 服务对象Item
     */
    @Resource
    private SaProjectitemService saProjectitemService;
    @Resource
    private SaProjectstatusService saProjectstatusService;
    @Resource
    private SaProjectlabelService saProjectlabelService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取工程项目详细信息", notes = "获取工程项目详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<SaProjectPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<PageInfo<SaProjectitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Project.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saProjectService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<PageInfo<SaProjectitemdetailPojo>> getOnlinePageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Project.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = " and Sa_Project.EnabledMark = 1 ";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saProjectService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取工程项目详细信息", notes = "获取工程项目详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<SaProjectPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<PageInfo<SaProjectPojo>> getPageTh(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Project.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saProjectService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增工程项目", notes = "新增工程项目", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.Add")
    public R<SaProjectPojo> create(@RequestBody String json) {
        try {
            SaProjectPojo saProjectPojo = JSONArray.parseObject(json, SaProjectPojo.class);
            // 获得用户数据
            MyLoginUser myLoginUser = getMyLoginUser(ServletUtils.getRequest());
            SaEngineerPojo engineer = myLoginUser.getEngineer();
            if (engineer == null) {
                throw new BaseBusinessException("未关联工程师");
            }
            saProjectPojo.setCreateby(myLoginUser.getRealName());   // 创建者
            saProjectPojo.setCreatebyid(myLoginUser.getUserid());  // 创建者id
            saProjectPojo.setCreatedate(new Date());   // 创建时间
            saProjectPojo.setLister(myLoginUser.getRealname());   // 制表
            saProjectPojo.setListerid(myLoginUser.getUserid());    // 制表id
            saProjectPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saProjectService.insert(saProjectPojo, engineer.getId()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    public MyLoginUser getMyLoginUser(HttpServletRequest request) {
        String token = "login_tokens:" + SecurityUtils.getToken(request);
        String redisValue = saRedisService.getValue(token);
        if (StringUtils.isBlank(redisValue)) {
            throw new BaseBusinessException("登录已过期,请重新登录");
        }
        return JSONArray.parseObject(redisValue, MyLoginUser.class);

    }


    @ApiOperation(value = "修改工程项目", notes = "修改工程项目", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.Edit")
    public R<SaProjectPojo> update(@RequestBody String json) {
        try {
            SaProjectPojo saProjectPojo = JSONArray.parseObject(json, SaProjectPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saProjectPojo.setLister(loginUser.getRealname());   // 制表
            saProjectPojo.setListerid(loginUser.getUserid());    // 制表id   
            saProjectPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saProjectService.update(saProjectPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除工程项目", notes = "删除工程项目", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //     -------------------------------------- Sa_ProjectItem(工程师信息,userid存的是工程师ID，不是用户ID~)的增删改查------------------------------------------
    @ApiOperation(value = " 获取工程项目Item详细信息", notes = "获取工程项目详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityItem", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<SaProjectitemPojo> getEntityItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectitemService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询Item", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListItem", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<PageInfo<SaProjectitemPojo>> getPageListItem(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Project.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saProjectitemService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询Pid下所有Item", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getListItem", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<List<SaProjectitemPojo>> getListItem(String Pid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectitemService.getList(Pid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增工程项目Item", notes = "新增工程项目Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.Add")
    public R<SaProjectitemPojo> createItem(@RequestBody String json) {
        try {
            SaProjectitemPojo saProjectitemPojo = JSONArray.parseObject(json, SaProjectitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectitemService.insert(saProjectitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增工程项目Item", notes = "新增工程项目Item", produces = "application/json")
    @RequestMapping(value = "/updateItem", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.Add")
    public R<SaProjectitemPojo> updateItem(@RequestBody String json) {
        try {
            SaProjectitemPojo saProjectitemPojo = JSONArray.parseObject(json, SaProjectitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectitemService.update(saProjectitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除工程项目Item", notes = "删除工程项目Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //     -------------------------------------- Sa_ProjectStatus的增删改查------------------------------------------
    @ApiOperation(value = " 获取工程项目Status详细信息", notes = "获取工程项目详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityStatus", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<SaProjectstatusPojo> getEntityStatus(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectstatusService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询Status", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListStatus", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<PageInfo<SaProjectstatusPojo>> getPageListStatus(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Project.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saProjectstatusService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询Pid下所有Status", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getListStatus", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<List<SaProjectstatusPojo>> getListStatus(String Pid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectstatusService.getList(Pid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增工程项目Status", notes = "新增工程项目Item", produces = "application/json")
    @RequestMapping(value = "/createStatus", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.Add")
    public R<SaProjectstatusPojo> createStatus(@RequestBody String json) {
        try {
            SaProjectstatusPojo saProjectstatusPojo = JSONArray.parseObject(json, SaProjectstatusPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectstatusService.insert(saProjectstatusPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增工程项目Status", notes = "新增工程项目Item", produces = "application/json")
    @RequestMapping(value = "/updateStatus", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.Add")
    public R<SaProjectstatusPojo> updateStatus(@RequestBody String json) {
        try {
            SaProjectstatusPojo saProjectstatusPojo = JSONArray.parseObject(json, SaProjectstatusPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectstatusService.update(saProjectstatusPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除工程项目Status", notes = "删除工程项目Item", produces = "application/json")
    @RequestMapping(value = "/deleteStatus", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.Delete")
    public R<Integer> deleteStatus(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectstatusService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    //     -------------------------------------- Sa_ProjectLabel的增删改查------------------------------------------
    @ApiOperation(value = " 获取工程项目Label详细信息", notes = "获取工程项目详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntityLabel", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<SaProjectlabelPojo> getEntityLabel(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectlabelService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询Label", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageListLabel", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<PageInfo<SaProjectlabelPojo>> getPageListLabel(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Project.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saProjectlabelService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "查询Pid下所有Label", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getListLabel", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.List")
    public R<List<SaProjectlabelPojo>> getListLabel(String Pid) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectlabelService.getList(Pid));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增工程项目Label", notes = "新增工程项目Item", produces = "application/json")
    @RequestMapping(value = "/createLabel", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.Add")
    public R<SaProjectlabelPojo> createLabel(@RequestBody String json) {
        try {
            SaProjectlabelPojo saProjectlabelPojo = JSONArray.parseObject(json, SaProjectlabelPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectlabelService.insert(saProjectlabelPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = " 新增工程项目Label", notes = "新增工程项目Item", produces = "application/json")
    @RequestMapping(value = "/updateLabel", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Project.Add")
    public R<SaProjectlabelPojo> updateLabel(@RequestBody String json) {
        try {
            SaProjectlabelPojo saProjectlabelPojo = JSONArray.parseObject(json, SaProjectlabelPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectlabelService.update(saProjectlabelPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "删除工程项目Label", notes = "删除工程项目Item", produces = "application/json")
    @RequestMapping(value = "/deleteLabel", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.Delete")
    public R<Integer> deleteLabel(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectlabelService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Project.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaProjectPojo saProjectPojo = this.saProjectService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saProjectPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saProjectPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaProjectitemPojo saProjectitemPojo = new SaProjectitemPojo();
                    saProjectPojo.getItem().add(saProjectitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saProjectPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

