package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaProjectsortPojo;
import inks.service.sa.pms.service.SaProjectsortService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 个人常用项目排序(Sa_ProjectSort)表控制层
 *
 * <AUTHOR>
 * @since 2023-10-25 13:07:31
 */

public class SaProjectsortController {

    private final static Logger logger = LoggerFactory.getLogger(SaProjectsortController.class);

    @Resource
    private SaProjectsortService saProjectsortService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取个人常用项目排序详细信息", notes = "获取个人常用项目排序详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_ProjectSort.List")
    public R<SaProjectsortPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectsortService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_ProjectSort.List")
    public R<PageInfo<SaProjectsortPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_ProjectSort.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saProjectsortService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增个人常用项目排序", notes = "新增个人常用项目排序", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_ProjectSort.Add")
    public R<SaProjectsortPojo> create(@RequestBody String json) {
        try {
            SaProjectsortPojo saProjectsortPojo = JSONArray.parseObject(json, SaProjectsortPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saProjectsortPojo.setCreateby(loginUser.getRealName());   // 创建者
            saProjectsortPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saProjectsortPojo.setCreatedate(new Date());   // 创建时间
            saProjectsortPojo.setLister(loginUser.getRealname());   // 制表
            saProjectsortPojo.setListerid(loginUser.getUserid());    // 制表id  
            saProjectsortPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saProjectsortService.insert(saProjectsortPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改个人常用项目排序", notes = "修改个人常用项目排序", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_ProjectSort.Edit")
    public R<SaProjectsortPojo> update(@RequestBody String json) {
        try {
            SaProjectsortPojo saProjectsortPojo = JSONArray.parseObject(json, SaProjectsortPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saProjectsortPojo.setLister(loginUser.getRealname());   // 制表
            saProjectsortPojo.setListerid(loginUser.getUserid());    // 制表id  
            saProjectsortPojo.setModifydate(new Date());   //修改时间
//            saProjectsortPojo.setAssessor(""); // 审核员
//            saProjectsortPojo.setAssessorid(""); // 审核员id
//            saProjectsortPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saProjectsortService.update(saProjectsortPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除个人常用项目排序", notes = "删除个人常用项目排序", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_ProjectSort.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saProjectsortService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_ProjectSort.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaProjectsortPojo saProjectsortPojo = this.saProjectsortService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saProjectsortPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

