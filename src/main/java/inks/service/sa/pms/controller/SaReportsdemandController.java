package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaReportsdemandPojo;
import inks.service.sa.pms.service.SaReportsdemandService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 报表模版需求(Sa_ReportsDemand)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-22 15:30:01
 */
//@RestController
//@RequestMapping("saReportsdemand")
public class SaReportsdemandController {
    @Resource
    private SaReportsdemandService saReportsdemandService;

    @Resource
    private SaRedisService saRedisService;

    private final static Logger logger = LoggerFactory.getLogger(SaReportsdemandController.class);


    @ApiOperation(value = " 获取报表模版需求详细信息", notes = "获取报表模版需求详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ReportsDemand.List")
    public R<SaReportsdemandPojo> getEntity(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saReportsdemandService.getEntity(key));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ReportsDemand.List")
    public R<PageInfo<SaReportsdemandPojo>> getPageList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Sa_ReportsDemand.CreateDate");
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.saReportsdemandService.getPageList(queryParam));
    }


    @ApiOperation(value = " 新增报表模版需求", notes = "新增报表模版需求", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ReportsDemand.Add")
    public R<SaReportsdemandPojo> create(@RequestBody String json) {
        SaReportsdemandPojo saReportsdemandPojo = JSONArray.parseObject(json, SaReportsdemandPojo.class);
        LoginUser loginUser = null;
        try {
            loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saReportsdemandPojo.setCreateby(loginUser.getRealName());   // 创建者
            saReportsdemandPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saReportsdemandPojo.setCreatedate(new Date());   // 创建时间
            saReportsdemandPojo.setLister(loginUser.getRealname());   // 制表
            saReportsdemandPojo.setListerid(loginUser.getUserid());    // 制表id
            saReportsdemandPojo.setModifydate(new Date());   //修改时间
        } catch (Exception e) {
            // 没有登录用户，来自别的系统创建的报表需求。 由发起处传入createby/company/tenantid等信息
        }
        return R.ok(this.saReportsdemandService.insert(saReportsdemandPojo));
    }


    @ApiOperation(value = "修改报表模版需求", notes = "修改报表模版需求", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_ReportsDemand.Edit")
    public R<SaReportsdemandPojo> update(@RequestBody String json) {
        SaReportsdemandPojo saReportsdemandPojo = JSONArray.parseObject(json, SaReportsdemandPojo.class);
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        saReportsdemandPojo.setLister(loginUser.getRealname());   // 制表
        saReportsdemandPojo.setListerid(loginUser.getUserid());    // 制表id
        saReportsdemandPojo.setModifydate(new Date());   //修改时间
        //            saReportsdemandPojo.setAssessor(""); // 审核员
        //            saReportsdemandPojo.setAssessorid(""); // 审核员id
        //            saReportsdemandPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.saReportsdemandService.update(saReportsdemandPojo));
    }


    @ApiOperation(value = "删除报表模版需求", notes = "删除报表模版需求", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ReportsDemand.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saReportsdemandService.delete(key));
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_ReportsDemand.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaReportsdemandPojo saReportsdemandPojo = this.saReportsdemandService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saReportsdemandPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

