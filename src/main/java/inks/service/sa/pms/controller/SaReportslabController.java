package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaReportslabPojo;
import inks.service.sa.pms.service.SaReportslabService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * 报表库(Sa_ReportsLab)表控制层
 *
 * <AUTHOR>
 * @since 2024-01-23 09:44:16
 */

public class SaReportslabController {

    private final static Logger logger = LoggerFactory.getLogger(SaReportslabController.class);

    @Resource
    private SaReportslabService saReportslabService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取报表中心详细信息", notes = "获取报表中心详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_ReportsLab.List")
    public R<SaReportslabPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saReportslabService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_ReportsLab.List")
    public R<PageInfo<SaReportslabPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_ReportsLab.CreateDate");
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saReportslabService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增报表中心", notes = "新增报表中心", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_ReportsLab.Add")
    public R<SaReportslabPojo> create(@RequestBody String json) {
        try {
            SaReportslabPojo saReportslabPojo = JSONArray.parseObject(json, SaReportslabPojo.class);
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            saReportslabPojo.setCreateby(loginUser.getRealName());   // 创建者
//            saReportslabPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saReportslabPojo.setCreatedate(new Date());   // 创建时间
//            saReportslabPojo.setLister(loginUser.getRealname());   // 制表
//            saReportslabPojo.setListerid(loginUser.getUserid());    // 制表id
            saReportslabPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saReportslabService.insert(saReportslabPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改报表中心", notes = "修改报表中心", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    // @PreAuthorize(hasPermi = "Sa_ReportsLab.Edit")
    public R<SaReportslabPojo> update(@RequestBody String json) {
        try {
            SaReportslabPojo saReportslabPojo = JSONArray.parseObject(json, SaReportslabPojo.class);
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
//            saReportslabPojo.setLister(loginUser.getRealname());   // 制表
//            saReportslabPojo.setListerid(loginUser.getUserid());    // 制表id
            saReportslabPojo.setModifydate(new Date());   //修改时间
//            saReportslabPojo.setAssessor(""); // 审核员
//            saReportslabPojo.setAssessorid(""); // 审核员id
//            saReportslabPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saReportslabService.update(saReportslabPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除报表中心", notes = "删除报表中心", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_ReportsLab.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saReportslabService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    // @PreAuthorize(hasPermi = "Sa_ReportsLab.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaReportslabPojo saReportslabPojo = this.saReportslabService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saReportslabPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }


    //{//{"msg":"送货单据BM-2024-02-937",
    // "temp":"report_codes:1759765386380742656",
    // "code":"print",
    // "data":"report_data:1765192700841201664",
    // "paperwidth":27.9,
    // "sn":"local",
    // "paperlength":21.0,
    // "token":"2d7d9ebd-8dcc-4c04-8a47-286040fea75e"}

    @ApiOperation(value = "模拟数据云打印报表 key为报表库id", notes = "打印报表 key=billid,ptid打印模版,sn远程打印SN(可选)", produces = "application/json")
    @RequestMapping(value = "/mockPrint", method = RequestMethod.GET)
    public R<String> mockPrint(String key, String ptid, String sn, Integer cmd, Integer redis) {
        try {
            ////从redis中获取Reprot内容
            //ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
            //if (reportsPojo == null) {
            //    return R.fail("未找到报表");
            //}
            // 直接从报表库获取Reprot内容
            SaReportslabPojo saReportslabPojo = this.saReportslabService.getEntity(key);
            if (sn == null)
                sn = saReportslabPojo.getPrintersn();
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String mockdata = saReportslabPojo.getMockdata();
            /*模拟数据MockData格式：
              {
                "xml": {
                  "row": [
                    {"name": "原料A", "quantity": 5.0},
                    {"name": "原料2", "quantity": 15.0}

                  ],
                  "Mat": [
                    {"matname": "原料A", "quantity": 5.0},
                    {"matname": "原料2", "quantity": 15.0}
                  ],
                  "Item": [
                    {"itemname": "焊接", "amount": 15.6}
                  ]
                },
                "_grparam": {
                  "groupname": "公司1",
                  "billdate": "2025-07-01"
                }
              }*/
            // ====Map转Json ==== 注 时间转String 格式；
            String ptJson = JSONObject.toJSONStringWithDateFormat(mockdata, "yyyy-MM-dd");
            logger.info(ptJson);
            // 全并打印JSON
            Map<String, Object> mapPrint = new HashMap<>();

            if (cmd != null && cmd == 1) {
                mapPrint.put("code", "preview");
            } else {
                mapPrint.put("code", "print");
            }
            mapPrint.put("msg", "报表库模拟数据打印");    // 打印标题
            mapPrint.put("sn", sn);   //  打印机SN
            if (redis != null && redis == 1) {
                String rediskey = inksSnowflake.getSnowflake().nextIdStr();
                saRedisService.setCacheObject("report_data:" + rediskey, ptJson, 30L, TimeUnit.SECONDS);
                mapPrint.put("data", "report_data:" + rediskey);   //  打印数据
            } else {
                mapPrint.put("data", ptJson);   //  打印数据
            }
            // 云打印模板，加载  兼容 URL模式
            if (saReportslabPojo.getGrfdata() != null && !"".equals(saReportslabPojo.getGrfdata())) {
                mapPrint.put("temp", "report_codes:" + ptid);   // Text模板
            } else if (saReportslabPojo.getTempurl() != null && !"".equals(saReportslabPojo.getTempurl())) {
                mapPrint.put("temp", saReportslabPojo.getTempurl());   // url模板
            } else {
                throw new BaseBusinessException("未找到云打印模板");
            }
            mapPrint.put("paperlength", saReportslabPojo.getPaperlength());   // 页长
            mapPrint.put("paperwidth", saReportslabPojo.getPaperwidth());   // 页宽
            mapPrint.put("token", loginUser.getToken());  // 编辑权限
            // 本地打印
            return R.ok(JSONObject.toJSONString(mapPrint));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

}

