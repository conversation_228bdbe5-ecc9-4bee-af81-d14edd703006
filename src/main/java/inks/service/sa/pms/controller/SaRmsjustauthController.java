package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaRmsjustauthPojo;
import inks.service.sa.pms.service.SaRmsjustauthService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * RMS第三方登录(Sa_RmsJustAuth)表控制层
 *
 * <AUTHOR>
 * @since 2024-09-29 10:59:38
 */
//@RestController
//@RequestMapping("saRmsjustauth")
public class SaRmsjustauthController {
    private final static Logger logger = LoggerFactory.getLogger(SaRmsjustauthController.class);
    @Resource
    private SaRmsjustauthService saRmsjustauthService;
    @Resource
    private SaRedisService saRedisService;

    @ApiOperation(value = " 获取RMS第三方登录详细信息", notes = "获取RMS第三方登录详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_RmsJustAuth.List")
    public R<SaRmsjustauthPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saRmsjustauthService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_RmsJustAuth.List")
    public R<PageInfo<SaRmsjustauthPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_RmsJustAuth.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saRmsjustauthService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增RMS第三方登录", notes = "新增RMS第三方登录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_RmsJustAuth.Add")
    public R<SaRmsjustauthPojo> create(@RequestBody String json) {
        try {
            SaRmsjustauthPojo saRmsjustauthPojo = JSONArray.parseObject(json, SaRmsjustauthPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saRmsjustauthPojo.setCreateby(loginUser.getRealName());   // 创建者
            saRmsjustauthPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saRmsjustauthPojo.setCreatedate(new Date());   // 创建时间
            saRmsjustauthPojo.setLister(loginUser.getRealname());   // 制表
            saRmsjustauthPojo.setListerid(loginUser.getUserid());    // 制表id
            saRmsjustauthPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saRmsjustauthService.insert(saRmsjustauthPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改RMS第三方登录", notes = "修改RMS第三方登录", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_RmsJustAuth.Edit")
    public R<SaRmsjustauthPojo> update(@RequestBody String json) {
        try {
            SaRmsjustauthPojo saRmsjustauthPojo = JSONArray.parseObject(json, SaRmsjustauthPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saRmsjustauthPojo.setLister(loginUser.getRealname());   // 制表
            saRmsjustauthPojo.setListerid(loginUser.getUserid());    // 制表id
            saRmsjustauthPojo.setModifydate(new Date());   //修改时间
            //            saRmsjustauthPojo.setAssessor(""); // 审核员
            //            saRmsjustauthPojo.setAssessorid(""); // 审核员id
            //            saRmsjustauthPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saRmsjustauthService.update(saRmsjustauthPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除RMS第三方登录", notes = "删除RMS第三方登录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_RmsJustAuth.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saRmsjustauthService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_RmsJustAuth.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaRmsjustauthPojo saRmsjustauthPojo = this.saRmsjustauthService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saRmsjustauthPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

