package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaScmjustauthPojo;
import inks.service.sa.pms.service.SaScmjustauthService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * SCM第三方登录(Sa_ScmJustAuth)表控制层
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:30
 */
//@RestController
//@RequestMapping("saScmjustauth")
public class SaScmjustauthController {

    private final static Logger logger = LoggerFactory.getLogger(SaScmjustauthController.class);

    @Resource
    private SaScmjustauthService saScmjustauthService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取SCM第三方登录详细信息", notes = "获取SCM第三方登录详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_ScmJustAuth.List")
    public R<SaScmjustauthPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saScmjustauthService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_ScmJustAuth.List")
    public R<PageInfo<SaScmjustauthPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_ScmJustAuth.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saScmjustauthService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增SCM第三方登录", notes = "新增SCM第三方登录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_ScmJustAuth.Add")
    public R<SaScmjustauthPojo> create(@RequestBody String json) {
        try {
            SaScmjustauthPojo saScmjustauthPojo = JSONArray.parseObject(json, SaScmjustauthPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saScmjustauthPojo.setCreateby(loginUser.getRealName());   // 创建者
            saScmjustauthPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saScmjustauthPojo.setCreatedate(new Date());   // 创建时间
            saScmjustauthPojo.setLister(loginUser.getRealname());   // 制表
            saScmjustauthPojo.setListerid(loginUser.getUserid());    // 制表id  
            saScmjustauthPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saScmjustauthService.insert(saScmjustauthPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改SCM第三方登录", notes = "修改SCM第三方登录", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_ScmJustAuth.Edit")
    public R<SaScmjustauthPojo> update(@RequestBody String json) {
        try {
            SaScmjustauthPojo saScmjustauthPojo = JSONArray.parseObject(json, SaScmjustauthPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saScmjustauthPojo.setLister(loginUser.getRealname());   // 制表
            saScmjustauthPojo.setListerid(loginUser.getUserid());    // 制表id  
            saScmjustauthPojo.setModifydate(new Date());   //修改时间
//            saScmjustauthPojo.setAssessor(""); // 审核员
//            saScmjustauthPojo.setAssessorid(""); // 审核员id
//            saScmjustauthPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saScmjustauthService.update(saScmjustauthPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除SCM第三方登录", notes = "删除SCM第三方登录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_ScmJustAuth.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saScmjustauthService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_ScmJustAuth.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaScmjustauthPojo saScmjustauthPojo = this.saScmjustauthService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saScmjustauthPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

