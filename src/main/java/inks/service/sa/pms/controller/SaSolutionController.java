package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaSolutionPojo;
import inks.service.sa.pms.service.SaSolutionService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 解决方案(Sa_Solution)表控制层
 *
 * <AUTHOR>
 * @since 2023-09-14 16:03:36
 */
//@RestController
//@RequestMapping("saSolution")
public class SaSolutionController {

    private final static Logger logger = LoggerFactory.getLogger(SaSolutionController.class);

    @Resource
    private SaSolutionService saSolutionService;

    @Resource
    private SaRedisService saRedisService;


    // 校驗json字符串是否合法
    public static boolean isValidJson(String json) {
        try {
            JSON.parseObject(json);
            return true;
        } catch (Exception e) {
            return false;
        }
    }


    @ApiOperation(value = " 获取解决方案详细信息", notes = "获取解决方案详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Solution.List")
    public R<SaSolutionPojo> getEntity(String key) {
        try {
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSolutionService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Solution.List")
    public R<PageInfo<SaSolutionPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Solution.CreateDate");
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSolutionService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增解决方案", notes = "新增解决方案", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Solution.Add")
    public R<SaSolutionPojo> create(@RequestBody SaSolutionPojo saSolutionPojo) {
        try {
//            SaSolutionPojo saSolutionPojo = JSONArray.parseObject(json, SaSolutionPojo.class);
            if (!isValidJson(saSolutionPojo.getDescjson())) {
                throw new Exception("解决方案描述JSON格式不正确");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSolutionPojo.setCreateby(loginUser.getRealName());   // 创建者
            saSolutionPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saSolutionPojo.setCreatedate(new Date());   // 创建时间
            saSolutionPojo.setLister(loginUser.getRealname());   // 制表
            saSolutionPojo.setListerid(loginUser.getUserid());    // 制表id
            saSolutionPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saSolutionService.insert(saSolutionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @return 编辑结果
     */
    @ApiOperation(value = "修改解决方案", notes = "修改解决方案", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Solution.Edit")
    public R<SaSolutionPojo> update(@RequestBody SaSolutionPojo saSolutionPojo) {
        try {
            if (!isValidJson(saSolutionPojo.getDescjson())) {
                throw new Exception("解决方案描述JSON格式不正确");
            }
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSolutionPojo.setLister(loginUser.getRealname());   // 制表
            saSolutionPojo.setListerid(loginUser.getUserid());    // 制表id
            saSolutionPojo.setModifydate(new Date());   //修改时间
//            saSolutionPojo.setAssessor(""); // 审核员
//            saSolutionPojo.setAssessorid(""); // 审核员id
//            saSolutionPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saSolutionService.update(saSolutionPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除解决方案", notes = "删除解决方案", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Solution.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSolutionService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Solution.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaSolutionPojo saSolutionPojo = this.saSolutionService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saSolutionPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

