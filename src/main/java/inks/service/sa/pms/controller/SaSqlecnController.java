package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaSqlecnPojo;
import inks.service.sa.pms.service.SaSqlecnService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;
/**
 * SQL变更(Sa_SqlEcn)表控制层
 *
 * <AUTHOR>
 * @since 2025-02-19 14:27:49
 */
//@RestController
//@RequestMapping("saSqlecn")
public class SaSqlecnController {
    @Resource
    private SaSqlecnService saSqlecnService;
    
    @Resource
    private SaRedisService saRedisService;
    
    private final static Logger logger = LoggerFactory.getLogger(SaSqlecnController.class);


    @ApiOperation(value=" 获取SQL变更详细信息", notes="获取SQL变更详细信息", produces="application/json")
    @RequestMapping(value="/getEntity",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SqlEcn.List")
    public R<SaSqlecnPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSqlecnService.getEntity(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value="按条件分页查询", notes="按条件分页查询", produces="application/json")
    @RequestMapping(value="/getPageList",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SqlEcn.List")
    public R<PageInfo<SaSqlecnPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json,QueryParam.class);
            if (queryParam.getOrderBy() ==null || "".equals(queryParam.getOrderBy())) queryParam.setOrderBy("Sa_SqlEcn.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saSqlecnService.getPageList(queryParam));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    
    @ApiOperation(value=" 新增SQL变更", notes="新增SQL变更", produces="application/json")
    @RequestMapping(value="/create",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SqlEcn.Add")
    public R<SaSqlecnPojo> create(@RequestBody String json) {
        try {
            SaSqlecnPojo saSqlecnPojo = JSONArray.parseObject(json,SaSqlecnPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSqlecnPojo.setCreateby(loginUser.getRealName());   // 创建者
            saSqlecnPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saSqlecnPojo.setCreatedate(new Date());   // 创建时间
            saSqlecnPojo.setLister(loginUser.getRealname());   // 制表
            saSqlecnPojo.setListerid(loginUser.getUserid());    // 制表id
            saSqlecnPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saSqlecnService.insert(saSqlecnPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="修改SQL变更", notes="修改SQL变更", produces="application/json")
    @RequestMapping(value="/update",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_SqlEcn.Edit")
    public R<SaSqlecnPojo> update(@RequestBody String json) {
        try {
            SaSqlecnPojo saSqlecnPojo = JSONArray.parseObject(json,SaSqlecnPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saSqlecnPojo.setLister(loginUser.getRealname());   // 制表
            saSqlecnPojo.setListerid(loginUser.getUserid());    // 制表id
            saSqlecnPojo.setModifydate(new Date());   //修改时间
    //            saSqlecnPojo.setAssessor(""); // 审核员
    //            saSqlecnPojo.setAssessorid(""); // 审核员id
    //            saSqlecnPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saSqlecnService.update(saSqlecnPojo));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    
    
    @ApiOperation(value="删除SQL变更", notes="删除SQL变更", produces="application/json")
    @RequestMapping(value="/delete",method= RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SqlEcn.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saSqlecnService.delete(key));
        }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }
    @ApiOperation(value = "审核SQL变更", notes = "审核SQL变更", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SqlEcn.Approval")
    public R<SaSqlecnPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaSqlecnPojo saSqlecnPojo = this.saSqlecnService.getEntity(key);
            if (saSqlecnPojo.getAssessor().equals(""))
            {
                saSqlecnPojo.setAssessor(loginUser.getRealname()); //审核员
                saSqlecnPojo.setAssessorid(loginUser.getUserid()); //审核员id
            }
                else
            {
                saSqlecnPojo.setAssessor(""); //审核员
                saSqlecnPojo.setAssessorid(""); //审核员
            }
            saSqlecnPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saSqlecnService.approval(saSqlecnPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_SqlEcn.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaSqlecnPojo saSqlecnPojo = this.saSqlecnService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saSqlecnPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content ;
        if (reportsPojo != null ) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response= ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
    }

