package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaTodoPojo;
import inks.service.sa.pms.service.SaTodoService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * Todo(Sa_Todo)表控制层
 *
 * <AUTHOR>
 * @since 2023-02-18 11:12:38
 */

public class SaTodoController {

    private final static Logger logger = LoggerFactory.getLogger(SaTodoController.class);

    @Resource
    private SaTodoService saTodoService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;


    @ApiOperation(value = " 获取Todo详细信息", notes = "获取Todo详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Todo.List")
    public R<SaTodoPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTodoService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//      //@PreAuthorize(hasPermi = "Sa_Todo.List")
    public R<PageInfo<SaTodoPojo>> getPageList(@RequestBody String json, @RequestParam(defaultValue = "0") Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Todo.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            // 我的
            if (own == 1) {
                qpfilter += " and Sa_Todo.CreateByid='" + loginUser.getUserid() + "'";
            } else if (own == 0) {
                // 所有
                //qpfilter += " and Sa_Todo.PublicMark=1";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saTodoService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     * 如果有Parentid，则为需求单转为To do：则更新需求单的Todoid,表示已转为To do
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增Todo", notes = "新增Todo", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Todo.Add")
    public R<SaTodoPojo> create(@RequestBody String json) {
        try {
            SaTodoPojo saTodoPojo = JSONArray.parseObject(json, SaTodoPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //生成单据编码
//            String yyyyMMdd = DateUtils.parseDateToStr("yyyyMMdd", new Date());
//            int ends = new Random().nextInt(9999);
//            String.format("%04d", ends);
//            saTodoPojo.setRefno(yyyyMMdd + ends);
            saTodoPojo.setRefno(saBillcodeService.getSerialNo("S06M09B1"));
            saTodoPojo.setCreateby(loginUser.getRealName());   // 创建者
            saTodoPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saTodoPojo.setCreatedate(new Date());   // 创建时间
            saTodoPojo.setLister(loginUser.getRealname());   // 制表
            saTodoPojo.setListerid(loginUser.getUserid());    // 制表id  
            saTodoPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saTodoService.insert(saTodoPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改Todo", notes = "修改Todo", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Todo.Edit")
    public R<SaTodoPojo> update(@RequestBody String json) {
        try {
            SaTodoPojo saTodoPojo = JSONArray.parseObject(json, SaTodoPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saTodoPojo.setLister(loginUser.getRealname());   // 制表
            saTodoPojo.setListerid(loginUser.getUserid());    // 制表id  
            saTodoPojo.setModifydate(new Date());   //修改时间
//            saTodoPojo.setAssessor(""); // 审核员
//            saTodoPojo.setAssessorid(""); // 审核员id
//            saTodoPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saTodoService.update(saTodoPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除Todo", notes = "删除Todo", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Todo.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTodoService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Todo.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaTodoPojo saTodoPojo = this.saTodoService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saTodoPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

