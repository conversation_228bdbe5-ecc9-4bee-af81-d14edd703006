package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.annotation.operLog.OperLog;
import inks.service.sa.pms.domain.pojo.SaDemandPojo;
import inks.service.sa.pms.domain.pojo.SaTrackingPojo;
import inks.service.sa.pms.domain.pojo.SaTrackingitemPojo;
import inks.service.sa.pms.domain.pojo.SaTrackingitemdetailPojo;
import inks.service.sa.pms.service.SaTrackingService;
import inks.service.sa.pms.service.SaTrackingitemService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import net.sf.jasperreports.engine.data.JRBeanCollectionDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 跟踪表主表(SaTracking)表控制层
 *
 * <AUTHOR>
 * @since 2023-06-24 09:35:51
 */
public class SaTrackingController {

    private final static Logger logger = LoggerFactory.getLogger(SaTrackingController.class);

    @Resource
    private SaTrackingService saTrackingService;
    /**
     * 服务对象Item
     */
    @Resource
    private SaTrackingitemService saTrackingitemService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取跟踪表主表详细信息", notes = "获取跟踪表主表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Tracking.List")
    public R<SaTrackingPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTrackingService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Tracking.List")
    public R<PageInfo<SaTrackingitemdetailPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Tracking.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saTrackingService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 获取跟踪表主表详细信息", notes = "获取跟踪表主表详细信息", produces = "application/json")
    @RequestMapping(value = "/getBillEntity", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Tracking.List")
    public R<SaTrackingPojo> getBillEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTrackingService.getBillEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getBillList", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Tracking.List")
    public R<PageInfo<SaTrackingPojo>> getBillList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Tracking.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saTrackingService.getBillList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询 own:1【我的】默认，条件：经办人id  or 协助人员id", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Tracking.List")
    public R<PageInfo<SaTrackingPojo>> getPageTh(@RequestBody String json, @RequestParam(required = false) Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Tracking.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "";
            //ALTER TABLE Sa_Tracking
            //    ADD COLUMN Operatorid varchar(50) NULL COMMENT '经办人ID',
            //    ADD COLUMN Assistantid varchar(50) NULL COMMENT '协助人员ID';
            if (own != null && own == 1) {
                qpfilter += " and (Sa_Tracking.Operatorid='" + loginUser.getUserid() + "' or Sa_Tracking.assistantids like '%" + loginUser.getUserid() + "%')";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saTrackingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "按条件分页查询 own:1【我的】默认，条件：经办人id  or 协助人员id or我创建的", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getOnlinePageTh", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Tracking.List")
    public R<PageInfo<SaTrackingPojo>> getOnlinePageTh(@RequestBody String json, @RequestParam(required = false) Integer own) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_Tracking.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String qpfilter = "and Sa_Tracking.BillFinishCount<Sa_Tracking.ItemCount and Sa_Tracking.closed=0";
            // 加入场景   Eric 20221124
            //ALTER TABLE Sa_Tracking
            //    ADD COLUMN Operatorid varchar(50) NULL COMMENT '经办人ID',
            //    ADD COLUMN Assistantid varchar(50) NULL COMMENT '协助人员ID';

            if (own != null && own == 1) {
                qpfilter += " and (Sa_Tracking.Operatorid='" + loginUser.getUserid() + "' " +
                        "or Sa_Tracking.assistantids like '%" + loginUser.getUserid() + "%' " +
                        "or Sa_Tracking.CreateByid='" + loginUser.getUserid() + "')";
            }
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.saTrackingService.getPageTh(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增跟踪表主表", notes = "新增跟踪表主表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Tracking.Add")
    public R<SaTrackingPojo> create(@RequestBody String json) {
        try {
            SaTrackingPojo saTrackingPojo = JSONArray.parseObject(json, SaTrackingPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saTrackingPojo.setCreateby(loginUser.getRealName());   // 创建者
            saTrackingPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saTrackingPojo.setCreatedate(new Date());   // 创建时间
            saTrackingPojo.setLister(loginUser.getRealname());   // 制表
            saTrackingPojo.setListerid(loginUser.getUserid());    // 制表id            
            saTrackingPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saTrackingService.insert(saTrackingPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改跟踪表主表", notes = "修改跟踪表主表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Tracking.Edit")
    public R<SaTrackingPojo> update(@RequestBody String json) {
        try {
            SaTrackingPojo saTrackingPojo = JSONArray.parseObject(json, SaTrackingPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saTrackingPojo.setLister(loginUser.getRealname());   // 制表
            saTrackingPojo.setListerid(loginUser.getUserid());    // 制表id   
            saTrackingPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saTrackingService.update(saTrackingPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除跟踪表主表", notes = "删除跟踪表主表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    @OperLog(title = "删除跟踪表主表")
//    @PreAuthorize(hasPermi = "Sa_Tracking.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTrackingService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /*子表操作 */

    /**
     * 新增子表
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增跟踪表主表Item", notes = "新增跟踪表主表Item", produces = "application/json")
    @RequestMapping(value = "/createItem", method = RequestMethod.POST)
//    @PreAuthorize(hasPermi = "Sa_Tracking.Add")
    public R<SaTrackingitemPojo> createItem(@RequestBody String json) {
        try {
            SaTrackingitemPojo saTrackingitemPojo = JSONArray.parseObject(json, SaTrackingitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTrackingitemService.insert(saTrackingitemPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value=" 修改跟踪表主表Item", notes="修改跟踪表主表Item", produces="application/json")
    @RequestMapping(value="/updateItem",method= RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Tracking.Edit")
    public R<SaTrackingitemPojo> updateItem(@RequestBody String json) {
       try {
     SaTrackingitemPojo saTrackingitemPojo = JSONArray.parseObject(json,SaTrackingitemPojo.class);
             // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saTrackingitemService.update(saTrackingitemPojo));
    }catch (Exception e){
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除Item数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除跟踪表主表Item", notes = "删除跟踪表主表Item", produces = "application/json")
    @RequestMapping(value = "/deleteItem", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Tracking.Delete")
    public R<Integer> deleteItem(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTrackingitemService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    @ApiOperation(value = "作废跟踪表", notes = "作废销售订单,?type=1作废，0为反作废", produces = "application/json")
    @RequestMapping(value = "/disannul", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Bus_OrderCost.Edit")
    public R<SaTrackingPojo> disannul(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<SaTrackingitemPojo> lst = JSONArray.parseArray(json, SaTrackingitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //  BusMachiningPojo busMachiningPojo = this.busMachiningService.getEntity(lst.get(0).getPid(), loginUser.getTenantid());

            return R.ok(this.saTrackingService.disannul(lst, type, loginUser));

        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 作废单据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "中止跟踪表", notes = "中止销售订单,?type=1中止，0为反作废", produces = "application/json")
    @RequestMapping(value = "/closed", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Bus_OrderCost.Edit")
    public R<SaTrackingPojo> closed(@RequestBody String json, Integer type) {
        try {
            if (type == null) type = 1;
            List<SaTrackingitemPojo> lst = JSONArray.parseArray(json, SaTrackingitemPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saTrackingService.closed(lst, type, loginUser));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }
    /**
     * 打印单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
//    @PreAuthorize(hasPermi = "Sa_Tracking.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaTrackingPojo saTrackingPojo = this.saTrackingService.getBillEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saTrackingPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        // 判定是否需要追行
        if (reportsPojo.getPagerow() > 0) {
            int index = 0;
            // 取行余数
            index = saTrackingPojo.getItem().size() % reportsPojo.getPagerow();
            if (index > 0) {
                // 补全空白行
                for (int i = 0; i < reportsPojo.getPagerow() - index; i++) {
                    SaTrackingitemPojo saTrackingitemPojo = new SaTrackingitemPojo();
                    saTrackingPojo.getItem().add(saTrackingitemPojo);
                }
            }
        }

        //item转数据源
        JRDataSource jrDataSource = new JRBeanCollectionDataSource(saTrackingPojo.getItem());
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map, jrDataSource);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

