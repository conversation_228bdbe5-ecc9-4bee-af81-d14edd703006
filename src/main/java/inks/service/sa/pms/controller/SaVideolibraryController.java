package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaVideolibraryPojo;
import inks.service.sa.pms.service.SaVideolibraryService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 视频信息表(Sa_VideoLibrary)表控制层
 *
 * <AUTHOR>
 * @since 2022-12-09 08:59:14
 */
//@RestController
//@RequestMapping("saVideolibrary")
public class SaVideolibraryController {

    private final static Logger logger = LoggerFactory.getLogger(SaVideolibraryController.class);

    @Resource
    private SaVideolibraryService saVideolibraryService;

    @Resource
    private SaRedisService saRedisService;


    @ApiOperation(value = " 获取视频信息表详细信息", notes = "获取视频信息表详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_VideoLibrary.List")
    public R<SaVideolibraryPojo> getEntity(String key) {
        try {
            // 获得用户数据
           // LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            //累加播放量
            SaVideolibraryPojo saVideolibraryPojo = saVideolibraryService.getEntity(key);
            Integer videoplaytimes = saVideolibraryPojo.getVideoplaytimes();
            //++i 先自增再赋值      不能i++
            saVideolibraryPojo.setVideoplaytimes(++videoplaytimes);
            saVideolibraryService.update(saVideolibraryPojo);
            return R.ok(this.saVideolibraryService.getEntity(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_VideoLibrary.List")
    public R<PageInfo<SaVideolibraryPojo>> getPageList(@RequestBody String json, String id) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Sa_VideoLibrary.CreateDate");
            // 获得用户数据
//            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            String filter = "";
            if (id != null) {
                filter = "and Sa_VideoLibrary.FisrtLevelGroupid = '" + id + "'";
            }
            queryParam.setFilterstr(filter);
            return R.ok(this.saVideolibraryService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = " 新增视频信息表", notes = "新增视频信息表", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_VideoLibrary.Add")
    public R<SaVideolibraryPojo> create(@RequestBody String json) {
        try {
            SaVideolibraryPojo saVideolibraryPojo = JSONArray.parseObject(json, SaVideolibraryPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saVideolibraryPojo.setCreateby(loginUser.getRealName());   // 创建者
            saVideolibraryPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            saVideolibraryPojo.setCreatedate(new Date());   // 创建时间
            saVideolibraryPojo.setLister(loginUser.getRealname());   // 制表
            saVideolibraryPojo.setListerid(loginUser.getUserid());    // 制表id
            saVideolibraryPojo.setModifydate(new Date());   //修改时间
            return R.ok(this.saVideolibraryService.insert(saVideolibraryPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "修改视频信息表", notes = "修改视频信息表", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_VideoLibrary.Edit")
    public R<SaVideolibraryPojo> update(@RequestBody String json) {
        try {
            SaVideolibraryPojo saVideolibraryPojo = JSONArray.parseObject(json, SaVideolibraryPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            saVideolibraryPojo.setLister(loginUser.getRealname());   // 制表
            saVideolibraryPojo.setListerid(loginUser.getUserid());    // 制表id
            saVideolibraryPojo.setModifydate(new Date());   //修改时间
//            saVideolibraryPojo.setAssessor(""); // 审核员
//            saVideolibraryPojo.setAssessorid(""); // 审核员id
//            saVideolibraryPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saVideolibraryService.update(saVideolibraryPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    @ApiOperation(value = "删除视频信息表", notes = "删除视频信息表", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_VideoLibrary.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.saVideolibraryService.delete(key));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 审核单据
     *
     * @param key 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "审核视频信息表", notes = "审核视频信息表", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_VideoLibrary.Approval")
    public R<SaVideolibraryPojo> approval(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            SaVideolibraryPojo saVideolibraryPojo = this.saVideolibraryService.getEntity(key);
            if (saVideolibraryPojo.getAssessor().equals("")) {
                saVideolibraryPojo.setAssessor(loginUser.getRealname()); //审核员
                saVideolibraryPojo.setAssessorid(loginUser.getUserid()); //审核员id
            } else {
                saVideolibraryPojo.setAssessor(""); //审核员
                saVideolibraryPojo.setAssessorid(""); //审核员
            }
            saVideolibraryPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.saVideolibraryService.approval(saVideolibraryPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_VideoLibrary.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaVideolibraryPojo saVideolibraryPojo = this.saVideolibraryService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saVideolibraryPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

