package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaBillcodeService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.SaWeeklyPojo;
import inks.service.sa.pms.service.SaWeeklyService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 工作周报(Sa_Weekly)表控制层
 *
 * <AUTHOR>
 * @since 2025-07-21 15:11:31
 */
//@RestController
//@RequestMapping("saWeekly")
public class SaWeeklyController {
    @Resource
    private SaWeeklyService saWeeklyService;

    @Resource
    private SaRedisService saRedisService;
    @Resource
    private SaBillcodeService saBillcodeService;

    private final static Logger logger = LoggerFactory.getLogger(SaWeeklyController.class);


    @ApiOperation(value = " 获取工作周报详细信息", notes = "获取工作周报详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Weekly.List")
    public R<SaWeeklyPojo> getEntity(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saWeeklyService.getEntity(key));
    }


    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Weekly.List")
    public R<PageInfo<SaWeeklyPojo>> getPageList(@RequestBody String json) {
        QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
        if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
            queryParam.setOrderBy("Sa_Weekly.CreateDate");
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        String qpfilter = "";
        qpfilter += queryParam.getAllFilter();
        queryParam.setFilterstr(qpfilter);
        return R.ok(this.saWeeklyService.getPageList(queryParam));
    }


    @ApiOperation(value = " 新增工作周报", notes = "新增工作周报", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Weekly.Add")
    public R<SaWeeklyPojo> create(@RequestBody String json) {
        SaWeeklyPojo saWeeklyPojo = JSONArray.parseObject(json, SaWeeklyPojo.class);
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //生成单据编码
        String refNo = saBillcodeService.getSerialNo("S06M10B2", loginUser.getTenantid());
        saWeeklyPojo.setRefno(refNo);
        saWeeklyPojo.setCreateby(loginUser.getRealName());   // 创建者
        saWeeklyPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        saWeeklyPojo.setCreatedate(new Date());   // 创建时间
        saWeeklyPojo.setLister(loginUser.getRealname());   // 制表
        saWeeklyPojo.setListerid(loginUser.getUserid());    // 制表id
        saWeeklyPojo.setModifydate(new Date());   //修改时间
        return R.ok(this.saWeeklyService.insert(saWeeklyPojo));
    }


    @ApiOperation(value = "修改工作周报", notes = "修改工作周报", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Sa_Weekly.Edit")
    public R<SaWeeklyPojo> update(@RequestBody String json) {
        SaWeeklyPojo saWeeklyPojo = JSONArray.parseObject(json, SaWeeklyPojo.class);
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        saWeeklyPojo.setLister(loginUser.getRealname());   // 制表
        saWeeklyPojo.setListerid(loginUser.getUserid());    // 制表id
        saWeeklyPojo.setModifydate(new Date());   //修改时间
        //            saWeeklyPojo.setAssessor(""); // 审核员
        //            saWeeklyPojo.setAssessorid(""); // 审核员id
        //            saWeeklyPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.saWeeklyService.update(saWeeklyPojo));
    }


    @ApiOperation(value = "删除工作周报", notes = "删除工作周报", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Weekly.Delete")
    public R<Integer> delete(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        return R.ok(this.saWeeklyService.delete(key));
    }

    @ApiOperation(value = "审核工作周报", notes = "审核工作周报", produces = "application/json")
    @RequestMapping(value = "/approval", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Weekly.Approval")
    public R<SaWeeklyPojo> approval(String key) {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        SaWeeklyPojo saWeeklyPojo = this.saWeeklyService.getEntity(key);
        if (saWeeklyPojo.getAssessor().equals("")) {
            saWeeklyPojo.setAssessor(loginUser.getRealname()); //审核员
            saWeeklyPojo.setAssessorid(loginUser.getUserid()); //审核员id
        } else {
            saWeeklyPojo.setAssessor(""); //审核员
            saWeeklyPojo.setAssessorid(""); //审核员
        }
        saWeeklyPojo.setAssessdate(new Date()); //审核时间
        return R.ok(this.saWeeklyService.approval(saWeeklyPojo));
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Sa_Weekly.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        SaWeeklyPojo saWeeklyPojo = this.saWeeklyService.getEntity(key);
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(saWeeklyPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = JSONArray.parseObject(saRedisService.getValue("report_codes:" + ptid), ReportsPojo.class);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

