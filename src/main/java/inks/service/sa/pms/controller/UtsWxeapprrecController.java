package inks.service.sa.pms.controller;

import com.alibaba.fastjson.JSONArray;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.ReportsPojo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.ServletUtils;
import inks.common.core.utils.bean.BeanUtils;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.domain.pojo.UtsWxeapprrecPojo;
import inks.service.sa.pms.service.UtsWxeapprrecService;
import io.swagger.annotations.ApiOperation;
import net.sf.jasperreports.engine.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.util.Date;
import java.util.Map;

/**
 * 微信审批记录(Uts_WxeApprRec)表控制层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:19
 */
@RestController
@RequestMapping("utsWxeapprrec")
public class UtsWxeapprrecController {
    /**
     * slf4j+logback
     */
    private final static Logger logger = LoggerFactory.getLogger(UtsWxeapprrecController.class);
    /**
     * 服务对象
     */
    @Resource
    private UtsWxeapprrecService utsWxeapprrecService;

    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过主键查询单条数据
     *
     * @param key 主键
     * @return 单条数据
     */
    @ApiOperation(value = " 获取微信审批记录详细信息", notes = "获取微信审批记录详细信息", produces = "application/json")
    @RequestMapping(value = "/getEntity", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_WxeApprRec.List")
    public R<UtsWxeapprrecPojo> getEntity(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsWxeapprrecService.getEntity(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param json 筛选条件
     * @return 查询结果
     */
    @ApiOperation(value = "按条件分页查询", notes = "按条件分页查询", produces = "application/json")
    @RequestMapping(value = "/getPageList", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_WxeApprRec.List")
    public R<PageInfo<UtsWxeapprrecPojo>> getPageList(@RequestBody String json) {
        try {
            QueryParam queryParam = JSONArray.parseObject(json, QueryParam.class);
            if (queryParam.getOrderBy() == null || "".equals(queryParam.getOrderBy()))
                queryParam.setOrderBy("Uts_WxeApprRec.CreateDate");
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            queryParam.setTenantid(loginUser.getTenantid());
            String qpfilter = "";
            qpfilter += queryParam.getAllFilter();
            queryParam.setFilterstr(qpfilter);
            return R.ok(this.utsWxeapprrecService.getPageList(queryParam));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param json 实体
     * @return 新增结果
     */
    @ApiOperation(value = " 新增微信审批记录", notes = "新增微信审批记录", produces = "application/json")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_WxeApprRec.Add")
    public R<UtsWxeapprrecPojo> create(@RequestBody String json) {
        try {
            UtsWxeapprrecPojo utsWxeapprrecPojo = JSONArray.parseObject(json, UtsWxeapprrecPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsWxeapprrecPojo.setCreateby(loginUser.getRealName());   // 创建者
            utsWxeapprrecPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
            utsWxeapprrecPojo.setCreatedate(new Date());   // 创建时间
            utsWxeapprrecPojo.setLister(loginUser.getRealname());   // 制表
            utsWxeapprrecPojo.setListerid(loginUser.getUserid());    // 制表id  
            utsWxeapprrecPojo.setModifydate(new Date());   //修改时间
            utsWxeapprrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            return R.ok(this.utsWxeapprrecService.insert(utsWxeapprrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 编辑数据
     *
     * @param json 实体
     * @return 编辑结果
     */
    @ApiOperation(value = "修改微信审批记录", notes = "修改微信审批记录", produces = "application/json")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    //@PreAuthorize(hasPermi = "Uts_WxeApprRec.Edit")
    public R<UtsWxeapprrecPojo> update(@RequestBody String json) {
        try {
            UtsWxeapprrecPojo utsWxeapprrecPojo = JSONArray.parseObject(json, UtsWxeapprrecPojo.class);
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            utsWxeapprrecPojo.setLister(loginUser.getRealname());   // 制表
            utsWxeapprrecPojo.setListerid(loginUser.getUserid());    // 制表id  
            utsWxeapprrecPojo.setTenantid(loginUser.getTenantid());   //租户id
            utsWxeapprrecPojo.setModifydate(new Date());   //修改时间
//            utsWxeapprrecPojo.setAssessor(""); // 审核员
//            utsWxeapprrecPojo.setAssessorid(""); // 审核员id
//            utsWxeapprrecPojo.setAssessdate(new Date()); //审核时间
            return R.ok(this.utsWxeapprrecService.update(utsWxeapprrecPojo));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    /**
     * 删除数据
     *
     * @param key 主键
     * @return 删除是否成功
     */
    @ApiOperation(value = "删除微信审批记录", notes = "删除微信审批记录", produces = "application/json")
    @RequestMapping(value = "/delete", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_WxeApprRec.Delete")
    public R<Integer> delete(String key) {
        try {
            // 获得用户数据
            LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
            return R.ok(this.utsWxeapprrecService.delete(key, loginUser.getTenantid()));
        } catch (Exception e) {
            return R.fail(e.getMessage());
        }
    }

    @ApiOperation(value = "打印报表", notes = "打印报表", produces = "application/json")
    @RequestMapping(value = "/printBill", method = RequestMethod.GET)
    //@PreAuthorize(hasPermi = "Uts_WxeApprRec.Print")
    public void printBill(String key, String ptid) throws IOException, JRException {
        // 获得用户数据
        LoginUser loginUser = saRedisService.getLoginUser(ServletUtils.getRequest());
        //获取单据信息
        UtsWxeapprrecPojo utsWxeapprrecPojo = this.utsWxeapprrecService.getEntity(key, loginUser.getTenantid());
        //表头转MAP
        Map<String, Object> map = BeanUtils.beanToMap(utsWxeapprrecPojo);
        // 加入公司信息
        inks.common.core.utils.inks.PrintUtils.addCompanyInfo(map, loginUser);
        //从redis中获取Reprot内容
        ReportsPojo reportsPojo = saRedisService.getCacheObject("report_codes:" + ptid);
        String content;
        if (reportsPojo != null) {
            content = reportsPojo.getRptdata();
        } else {
            throw new BaseBusinessException("未找到报表");
        }
        //报表生成
        InputStream stream = new ByteArrayInputStream(content.getBytes());
        HttpServletResponse response = ServletUtils.getResponse();
        ServletOutputStream os = response.getOutputStream();
        try {
            //编译报表
            JasperReport jasperReport = JasperCompileManager.compileReport(stream);
            //数据填充
            JasperPrint print = JasperFillManager.fillReport(jasperReport, map);
            //打印PDF数据流
            JasperExportManager.exportReportToPdfStream(print, os);
        } catch (JRException e) {
            e.printStackTrace();
        } catch (BaseBusinessException base) {
            base.getMessage();
        } finally {
            os.flush();
        }
    }
}

