EventName 事件名 push/
UserName 登录名唯一标识
ProjectName 项目名
ProjectUrl 项目地址
ProjectDesc 项目描述
ProjectNameSpace 项目命名空间
CommitMessage 提交commit信息
CommitDate 提交时间 "timestamp": "2024-06-22T12:55:37+08:00"
CommitAuthor 提交人
CommitEmail 提交人邮箱




12.54
提交commit: test^Webhook 12.54  test^Webhook 10.55
进入Received webhook from GitLab
Received payload:
接收到的Webhook发来的如下JSON数据:
{"object_kind":"push","event_name":"push","before":"3c42cbe6a2c507c0d8b7de2df3617a080582e747","after":"449afb44e6291ffee8852742cc472522ef874f04","ref":"refs/heads/master","ref_protected":true,"checkout_sha":"449afb44e6291ffee8852742cc472522ef874f04","message":null,"user_id":6,"user_name":"nanno.jiang","user_username":"nanno.jiang","user_email":"","user_avatar":"http://git.inksdev.com/uploads/-/system/user/avatar/6/avatar.png","project_id":10,"project":{"id":10,"name":"inks-service-sa-pms","description":"S06 PMS项目管理后端代码","web_url":"http://git.inksdev.com/sasvc/inks-service-sa-pms","avatar_url":null,"git_ssh_url":"*******************:sasvc/inks-service-sa-pms.git","git_http_url":"http://git.inksdev.com/sasvc/inks-service-sa-pms.git","namespace":"sasvc","visibility_level":0,"path_with_namespace":"sasvc/inks-service-sa-pms","default_branch":"master","ci_config_path":null,"homepage":"http://git.inksdev.com/sasvc/inks-service-sa-pms","url":"*******************:sasvc/inks-service-sa-pms.git","ssh_url":"*******************:sasvc/inks-service-sa-pms.git","http_url":"http://git.inksdev.com/sasvc/inks-service-sa-pms.git"},"commits":[{"id":"449afb44e6291ffee8852742cc472522ef874f04","message":"test^Webhook 12.54\n","title":"test^Webhook 12.54","timestamp":"2024-06-22T12:55:37+08:00","url":"http://git.inksdev.com/sasvc/inks-service-sa-pms/-/commit/449afb44e6291ffee8852742cc472522ef874f04","author":{"name":"jyfdexh","email":"<EMAIL>"},"added":["src/main/java/inks/service/sa/pms/controller/testWebhook.txt"],"modified":[],"removed":[]}],"total_commits_count":1,"push_options":{},"repository":{"name":"inks-service-sa-pms","url":"*******************:sasvc/inks-service-sa-pms.git","description":"S06 PMS项目管理后端代码","homepage":"http://git.inksdev.com/sasvc/inks-service-sa-pms","git_http_url":"http://git.inksdev.com/sasvc/inks-service-sa-pms.git","git_ssh_url":"*******************:sasvc/inks-service-sa-pms.git","visibility_level":0}}

13.11
提交commit: test^Webhook 13.11 长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容
进入Received webhook from GitLab
Received payload:
接收到的Webhook发来的如下JSON数据:
{
    "object_kind": "push",
    "event_name": "push",
    "before": "449afb44e6291ffee8852742cc472522ef874f04",
    "after": "408a91c7eedc3b5a83cf39873828b14a8ba21e23",
    "ref": "refs/heads/master",
    "ref_protected": true,
    "checkout_sha": "408a91c7eedc3b5a83cf39873828b14a8ba21e23",
    "message": null,
    "user_id": 6,
    "user_name": "nanno.jiang",
    "user_username": "nanno.jiang",
    "user_email": "",
    "user_avatar": "http://git.inksdev.com/uploads/-/system/user/avatar/6/avatar.png",
    "project_id": 10,
    "project": {
        "id": 10,
        "name": "inks-service-sa-pms",
        "description": "S06 PMS项目管理后端代码",
        "web_url": "http://git.inksdev.com/sasvc/inks-service-sa-pms",
        "avatar_url": null,
        "git_ssh_url": "*******************:sasvc/inks-service-sa-pms.git",
        "git_http_url": "http://git.inksdev.com/sasvc/inks-service-sa-pms.git",
        "namespace": "sasvc",
        "visibility_level": 0,
        "path_with_namespace": "sasvc/inks-service-sa-pms",
        "default_branch": "master",
        "ci_config_path": null,
        "homepage": "http://git.inksdev.com/sasvc/inks-service-sa-pms",
        "url": "*******************:sasvc/inks-service-sa-pms.git",
        "ssh_url": "*******************:sasvc/inks-service-sa-pms.git",
        "http_url": "http://git.inksdev.com/sasvc/inks-service-sa-pms.git"
    },
    "commits": [
        {
            "id": "408a91c7eedc3b5a83cf39873828b14a8ba21e23",
            "message": "test^Webhook 13.11 长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容\n",
            "title": "test^Webhook 13.11 长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容长内容",
            "timestamp": "2024-06-22T13:12:25+08:00",
            "url": "http://git.inksdev.com/sasvc/inks-service-sa-pms/-/commit/408a91c7eedc3b5a83cf39873828b14a8ba21e23",
            "author": {
                "name": "jyfdexh",
                "email": "<EMAIL>"
            },
            "added": [

            ],
            "modified": [
                "src/main/java/inks/service/sa/pms/controller/testWebhook.txt"
            ],
            "removed": [

            ]
        }
    ],
    "total_commits_count": 1,
    "push_options": {

    },
    "repository": {
        "name": "inks-service-sa-pms",
        "url": "*******************:sasvc/inks-service-sa-pms.git",
        "description": "S06 PMS项目管理后端代码",
        "homepage": "http://git.inksdev.com/sasvc/inks-service-sa-pms",
        "git_http_url": "http://git.inksdev.com/sasvc/inks-service-sa-pms.git",
        "git_ssh_url": "*******************:sasvc/inks-service-sa-pms.git",
        "visibility_level": 0
    }
}