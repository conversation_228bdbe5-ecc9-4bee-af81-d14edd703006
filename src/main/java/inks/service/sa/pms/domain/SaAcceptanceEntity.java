package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * (SaAcceptance)实体类
 *
 * <AUTHOR>
 * @since 2023-05-09 16:11:12
 */
public class SaAcceptanceEntity implements Serializable {
    private static final long serialVersionUID = -94337884759925045L;
    // 验收id
    private String id;
    // 客户id
    private String groupid;
    // 软件名称
    private String name;
    // 合同编号
    private String refno;
    // 验收时间
    private Date acceptancedate;
    // 验收地点
    private String acceptanceloc;
    // 甲方验收人员
    private String partyainsp;
    // 乙方验收人员
    private String partybinsp;
    // 验收内容
    private String content;
    // 软件安装、调试是否与合同相符
    private Integer installmatch;
    // 提供的说明书、使用手册等文档是否齐全
    private Integer documentcomplete;
    // 所有系统功能是否实现
    private Integer allfunc;
    // 乙方评价
    private String partybeval;
    // 乙方签名日期
    private Date partybevaldate;
    // 遗留的问题
    private String issues;
    // 甲方评价
    private String partyaeval;
    // 甲方签名日期
    private Date partyaevaldate;
    // 版本
    private Integer revion;
    // 行号
    private Integer rownum;
    // 创建者id
    private String createbyid;
    // 创建者
    private String createby;
    // 新建日期
    private Date createdate;
    // 制表id
    private String listerid;
    // 制表
    private String lister;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;

    // 验收id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 客户id
    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    // 软件名称
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    // 合同编号
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 验收时间
    public Date getAcceptancedate() {
        return acceptancedate;
    }

    public void setAcceptancedate(Date acceptancedate) {
        this.acceptancedate = acceptancedate;
    }

    // 验收地点
    public String getAcceptanceloc() {
        return acceptanceloc;
    }

    public void setAcceptanceloc(String acceptanceloc) {
        this.acceptanceloc = acceptanceloc;
    }

    // 甲方验收人员
    public String getPartyainsp() {
        return partyainsp;
    }

    public void setPartyainsp(String partyainsp) {
        this.partyainsp = partyainsp;
    }

    // 乙方验收人员
    public String getPartybinsp() {
        return partybinsp;
    }

    public void setPartybinsp(String partybinsp) {
        this.partybinsp = partybinsp;
    }

    // 验收内容
    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    // 软件安装、调试是否与合同相符
    public Integer getInstallmatch() {
        return installmatch;
    }

    public void setInstallmatch(Integer installmatch) {
        this.installmatch = installmatch;
    }

    // 提供的说明书、使用手册等文档是否齐全
    public Integer getDocumentcomplete() {
        return documentcomplete;
    }

    public void setDocumentcomplete(Integer documentcomplete) {
        this.documentcomplete = documentcomplete;
    }

    // 所有系统功能是否实现
    public Integer getAllfunc() {
        return allfunc;
    }

    public void setAllfunc(Integer allfunc) {
        this.allfunc = allfunc;
    }

    // 乙方评价
    public String getPartybeval() {
        return partybeval;
    }

    public void setPartybeval(String partybeval) {
        this.partybeval = partybeval;
    }

    // 乙方签名日期
    public Date getPartybevaldate() {
        return partybevaldate;
    }

    public void setPartybevaldate(Date partybevaldate) {
        this.partybevaldate = partybevaldate;
    }

    // 遗留的问题
    public String getIssues() {
        return issues;
    }

    public void setIssues(String issues) {
        this.issues = issues;
    }

    // 甲方评价
    public String getPartyaeval() {
        return partyaeval;
    }

    public void setPartyaeval(String partyaeval) {
        this.partyaeval = partyaeval;
    }

    // 甲方签名日期
    public Date getPartyaevaldate() {
        return partyaevaldate;
    }

    public void setPartyaevaldate(Date partyaevaldate) {
        this.partyaevaldate = partyaevaldate;
    }

    // 版本
    public Integer getRevion() {
        return revion;
    }

    public void setRevion(Integer revion) {
        this.revion = revion;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

