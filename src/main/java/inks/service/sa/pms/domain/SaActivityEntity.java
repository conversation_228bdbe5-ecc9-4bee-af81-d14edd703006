package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 活动主表(SaActivity)实体类
 *
 * <AUTHOR>
 * @since 2025-01-14 16:56:55
 */
@Data
public class SaActivityEntity implements Serializable {
    private static final long serialVersionUID = 548215289424279062L;
     // 主键
    private String id;
     // 单号
    private String refno;
     // 单据类型
    private String billtype;
     // 标题
    private String billtitle;
     // 日期
    private Date billdate;
     // 活动主题
    private String activitytheme;
     // 指数
    private Integer exponent;
     // 阶段id
    private String stageid;
     // 状态
    private String state;
     // 状态日期
    private Date statedate;
     // 附件s
    private String attachments;
     // 摘要
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门id
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;


}

