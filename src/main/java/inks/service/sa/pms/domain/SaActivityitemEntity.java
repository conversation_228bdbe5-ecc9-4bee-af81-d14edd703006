package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 活动子表(SaActivityitem)Entity
 *
 * <AUTHOR>
 * @since 2025-01-17 10:26:15
 */
@Data
public class SaActivityitemEntity implements Serializable {
    private static final long serialVersionUID = 537269121118582149L;
     // 主键
    private String id;
     // Pid
    private String pid;
     // 父ID
    private String parentid;
     // 阶段名称
    private String stagename;
     // 阶段子表id
    private String stageitemid;
     // 计划日期
    private Date planndate;
     // 负责人
    private String operator;
     // 负责人id
    private String operatorid;
     // 协作者们
    private String collaborators;
     // 协作者们ids
    private String collaboratorids;
     // 完成
    private Integer finishmark;
     // 完成日期
    private Date finishdate;
     // 完成描述
    private String finishdesc;
     // 必填
    private Integer mustmark;
     // 允许附件上传
    private Integer filemark;
     // 附件s
    private String attachments;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 备注
    private String remark;
     // 行号
    private Integer rownum;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

