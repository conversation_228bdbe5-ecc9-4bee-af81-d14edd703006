package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 活动计划/日志(SaActivityplan)Entity
 *
 * <AUTHOR>
 * @since 2025-07-21 17:27:54
 */
@Data
public class SaActivityplanEntity implements Serializable {
    private static final long serialVersionUID = 786527897993980554L;
     // ID
    private String id;
     // 活动主表ID
    private String pid;
     // 类型（0计划, 1日志, 2关键事项）
    private String type;
     // 关联活动子表id
    private String actiitemid;
     // 计划时间
    private Date plandate;
     // 完成时间
    private Date finishdate;
     // 完成标识（0:未完成,1:完成）
    private Integer finishmark;
     // 描述
    private String description;
     // 附件
    private String attachments;
     // 排序码
    private Integer rownum;
     // 备注
    private String remark;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

