package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 活动阶段子表(SaActivitystageitem)Entity
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:20
 */
@Data
public class SaActivitystageitemEntity implements Serializable {
    private static final long serialVersionUID = 353567200836579098L;
     // 阶段ID
    private String id;
     // 父阶段ID
    private String pid;
     // 阶段名称
    private String itemname;
     // 阶段配置（JSON格式）
    private String stagejson;
     // 排序编号
    private Integer rownum;
     // 摘要
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

