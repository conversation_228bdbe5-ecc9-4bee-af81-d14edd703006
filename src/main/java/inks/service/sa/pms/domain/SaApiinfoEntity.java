package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 接口信息表(SaApiinfo)实体类
 *
 * <AUTHOR>
 * @since 2025-01-20 16:07:07
 */
@Data
public class SaApiinfoEntity implements Serializable {
    private static final long serialVersionUID = -23622022273291022L;
     // 主键ID
    private String id;
     // 功能ID
    private String fnid;
     // 功能编码
    private String fncode;
     // 接口名称
    private String apiname;
     // 接口描述
    private String apidescription;
     // 接口路径
    private String apiurl;
     // 请求方式(GET,POST等)
    private String httpmethod;
     // 请求参数
    private String requestparams;
     // 响应参数
    private String responseparams;
     // 响应示例
    private String responseexample;
     // 响应状态码
    private String statuscode;
     // 操作ID
    private String operationid;
     // 返回格式
    private String produces;
     // 请求格式
    private String consumes;
     // 标签
    private String tags;
     // 是否已废弃
    private Integer isdeprecated;
     // Curl
    private String curl;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

