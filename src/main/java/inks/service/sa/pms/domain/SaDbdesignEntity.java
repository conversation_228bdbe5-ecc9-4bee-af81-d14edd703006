package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 表格设计(SaDbdesign)实体类
 *
 * <AUTHOR>
 * @since 2025-01-15 16:40:19
 */
@Data
public class SaDbdesignEntity implements Serializable {
    private static final long serialVersionUID = 640822587946960349L;
     // ID
    private String id;
     // 功能ID
    private String fnid;
     // 表名
    private String tablename;
     // 表注释名
    private String comment;
     // 字符集CHARACTER SET
    private String characterset;
     // 排序规则COLLATE
    private String collation;
     // 版本号
    private String versionnumber;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;


}

