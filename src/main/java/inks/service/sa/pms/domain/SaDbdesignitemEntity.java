package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;

/**
 * 表格设计字段子表(SaDbdesignitem)Entity
 *
 * <AUTHOR>
 * @since 2025-02-14 08:45:11
 */
public class SaDbdesignitemEntity implements Serializable {
    private static final long serialVersionUID = 193680879302878868L;
     // ID
    private String id;
     // Pid
    private String pid;
     // 字段名
    private String fieldname;
     // 字段类型
    private String fieldtype;
     // 字段描述
    private String comment;
     // 主键 Mark
    private Integer primarymark;
     // 非NULL Mark
    private Integer notnullmark;
     // 字符集CHARACTER SET
    private String characterset;
     // 排序规则COLLATE
    private String collation;
     // 默认值DEFAULT
    private String defvalue;
     // 计算Mark
    private String calcmark;
     // 计算描述
    private String calcdesc;
     // 计算公式
    private String calcformula;
     // 索引
    private Integer indexmark;
     // 敏感字段
    private Integer sensitivemark;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

   // ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // Pid
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 字段名
    public String getFieldname() {
        return fieldname;
    }
    
    public void setFieldname(String fieldname) {
        this.fieldname = fieldname;
    }
        
   // 字段类型
    public String getFieldtype() {
        return fieldtype;
    }
    
    public void setFieldtype(String fieldtype) {
        this.fieldtype = fieldtype;
    }
        
   // 字段描述
    public String getComment() {
        return comment;
    }
    
    public void setComment(String comment) {
        this.comment = comment;
    }
        
   // 主键 Mark
    public Integer getPrimarymark() {
        return primarymark;
    }
    
    public void setPrimarymark(Integer primarymark) {
        this.primarymark = primarymark;
    }
        
   // 非NULL Mark
    public Integer getNotnullmark() {
        return notnullmark;
    }
    
    public void setNotnullmark(Integer notnullmark) {
        this.notnullmark = notnullmark;
    }
        
   // 字符集CHARACTER SET
    public String getCharacterset() {
        return characterset;
    }
    
    public void setCharacterset(String characterset) {
        this.characterset = characterset;
    }
        
   // 排序规则COLLATE
    public String getCollation() {
        return collation;
    }
    
    public void setCollation(String collation) {
        this.collation = collation;
    }
        
   // 默认值DEFAULT
    public String getDefvalue() {
        return defvalue;
    }
    
    public void setDefvalue(String defvalue) {
        this.defvalue = defvalue;
    }
        
   // 计算Mark
    public String getCalcmark() {
        return calcmark;
    }
    
    public void setCalcmark(String calcmark) {
        this.calcmark = calcmark;
    }
        
   // 计算描述
    public String getCalcdesc() {
        return calcdesc;
    }
    
    public void setCalcdesc(String calcdesc) {
        this.calcdesc = calcdesc;
    }
        
   // 计算公式
    public String getCalcformula() {
        return calcformula;
    }
    
    public void setCalcformula(String calcformula) {
        this.calcformula = calcformula;
    }
        
   // 索引
    public Integer getIndexmark() {
        return indexmark;
    }
    
    public void setIndexmark(Integer indexmark) {
        this.indexmark = indexmark;
    }
        
   // 敏感字段
    public Integer getSensitivemark() {
        return sensitivemark;
    }
    
    public void setSensitivemark(Integer sensitivemark) {
        this.sensitivemark = sensitivemark;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

