package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 产品需求(SaDemand)实体类
 *
 * <AUTHOR>
 * @since 2025-07-15 13:22:19
 */
@Data
public class SaDemandEntity implements Serializable {
    private static final long serialVersionUID = 585653870553114174L;
     // id
    private String id;
     // 父任务id
    private String parentid;
     // 编码
    private String refno;
     // 单据日期
    private Date billdate;
     // 单据类型
    private String billtype;
     // 单据标题
    private String billtitle;
     // 客户id
    private String groupid;
     // Todoid
    private String todoid;
     // 优先级
    private Integer level;
     // 周报导入
    private Integer weekmark;
     // 关联项目
    private String projectid;
     // 项目编码
    private String itemcode;
     // 项目名称
    private String itemname;
     // 描述
    private String description;
     // 标签(json数组)
    private String labeljson;
     // 需求标签(json数组)
    private String demandlabeljson;
     // 状态id(Sa_ProjectStatus.id)
    private String status;
     // 需求状态id(Sa_DemandStatus.id)
    private String demandstatus;
     // 需求工时状态(Sa_DemandTime.Status)
    private String timestatus;
     // 开始时间
    private Date startdate;
     // 截止时间
    private Date deaddate;
     // 完成时间
    private Date finishdate;
     // 耗费工时
    private Double worktime;
     // 需求提报id
    private String demandsubmitid;
     // 被指派人id
    private String appointeeid;
     // 被指派人名字
    private String appointee;
     // 经办人
    private String operator;
     // 经办人id
    private String operatorid;
     // 任务协作者ids
    private String collaboratorids;
     // 任务协作者们
    private String collaborators;
     // 经度
    private String longitude;
     // 纬度
    private String latitude;
     // 地理位置
    private String location;
     // 需求人手机
    private String phone;
     // 关闭者
    private String closer;
     // 关闭者id
    private String closerid;
     // 是否好评
    private Integer praise;
     // 处理意见
    private String processcomment;
     // 提交需求图片
    private String pictureurl1;
    private String pictureurl2;
    private String pictureurl3;
    private String pictureurl4;
    private String pictureurl5;
     // 完成需求图片
    private String finpictureurl1;
    private String finpictureurl2;
    private String finpictureurl3;
    private String finpictureurl4;
    private String finpictureurl5;
     // 完成描述
    private String finishdes;
     // 备注
    private String remark;
     // 作废
    private Integer disannulmark;
     // 完成
    private Integer finishmark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 状态修改时间
    private Date statusmodifydate;
     // 工程组id
    private String engineergroupid;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 部门ID
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

