package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 需求提报(SaDemandsubmit)实体类
 *
 * <AUTHOR>
 * @since 2024-11-04 16:48:10
 */
@Data
public class SaDemandsubmitEntity implements Serializable {
    private static final long serialVersionUID = -60844661226113290L;
    // ID
    private String id;
    // 类型(Bug/需求/客户反馈/运维反馈)
    private String type;
    // 内容描述
    private String description;
    // 优先级
    private Integer level;
    // 进展阶段(需求提报/审批/需求处理)
    private String status;
    // 责任人
    private String assignee;
    // 执行/参与方
    private String participants;
    // 截止时间
    private Date deadlinedate;
    // 客户ID
    private String groupid;
    // 来源(PMS/RMS/客户/内部)
    private String source;
    // 状态文本
    private String statetext;
    // 状态日期
    private Date statedate;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 正在进行OA
    private Integer oaflowmark;
    // 反馈单Itemid
    private String feedbackitemid;
    // 转需求单处理
    private Integer demandmark;
    // 转测试用例
    private Integer testcasemark;
    // 转版本发布
    private Integer releasemark;
    // 审核员
    private String assessor;
    // 审核员id
    private String assessorid;
    // 审核日期
    private Date assessdate;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 类型(Bug/需求/客户反馈/运维反馈)
    public String getType() {
        return type;
    }

    public void setType(String type) {
        this.type = type;
    }

    // 内容描述
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // 优先级
    public Integer getLevel() {
        return level;
    }

    public void setLevel(Integer level) {
        this.level = level;
    }

    // 进展阶段(需求提报/审批/需求处理)
    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    // 责任人
    public String getAssignee() {
        return assignee;
    }

    public void setAssignee(String assignee) {
        this.assignee = assignee;
    }

    // 执行/参与方
    public String getParticipants() {
        return participants;
    }

    public void setParticipants(String participants) {
        this.participants = participants;
    }

    // 截止时间
    public Date getDeadlinedate() {
        return deadlinedate;
    }

    public void setDeadlinedate(Date deadlinedate) {
        this.deadlinedate = deadlinedate;
    }

    // 客户ID
    public String getGroupid() {
        return groupid;
    }

    public void setGroupid(String groupid) {
        this.groupid = groupid;
    }

    // 来源(PMS/RMS/客户/内部)
    public String getSource() {
        return source;
    }

    public void setSource(String source) {
        this.source = source;
    }

    // 状态文本
    public String getStatetext() {
        return statetext;
    }

    public void setStatetext(String statetext) {
        this.statetext = statetext;
    }

    // 状态日期
    public Date getStatedate() {
        return statedate;
    }

    public void setStatedate(Date statedate) {
        this.statedate = statedate;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 正在进行OA
    public Integer getOaflowmark() {
        return oaflowmark;
    }

    public void setOaflowmark(Integer oaflowmark) {
        this.oaflowmark = oaflowmark;
    }

    // 反馈单Itemid
    public String getFeedbackitemid() {
        return feedbackitemid;
    }

    public void setFeedbackitemid(String feedbackitemid) {
        this.feedbackitemid = feedbackitemid;
    }

    // 转需求单处理
    public Integer getDemandmark() {
        return demandmark;
    }

    public void setDemandmark(Integer demandmark) {
        this.demandmark = demandmark;
    }

    // 转测试用例
    public Integer getTestcasemark() {
        return testcasemark;
    }

    public void setTestcasemark(Integer testcasemark) {
        this.testcasemark = testcasemark;
    }

    // 转版本发布
    public Integer getReleasemark() {
        return releasemark;
    }

    public void setReleasemark(Integer releasemark) {
        this.releasemark = releasemark;
    }

    // 审核员
    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    // 审核员id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

