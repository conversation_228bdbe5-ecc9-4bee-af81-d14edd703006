package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 文档表(SaDoc)实体类
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
public class SaDocEntity implements Serializable {
    private static final long serialVersionUID = 695866927882034275L;
    // 唯一标识
    private String id;
    // 父目录ID
    private String parentid;
    // 文件名称
    private String name;
    // 类型(目录/文件)
    private Integer type;
    // 大小
    private Long size;
    // 校验和
    private String checksum;
    // 版本
    private String version;
    // 虚文件内容
    private String content;
    // 目录路径(相对于仓库根目录的路径)
    private String path;
    // 所属仓库id
    private String vid;
    // 文件节点密码
    private String pwd;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 部门id
    private String deptid;
    // 租户
    private String tenantid;
    // 乐观锁
    private Integer revision;

// 唯一标识

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 父目录ID

    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }
// 文件名称

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
// 类型(目录/文件)

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
// 大小

    public Long getSize() {
        return size;
    }

    public void setSize(Long size) {
        this.size = size;
    }
// 校验和

    public String getChecksum() {
        return checksum;
    }

    public void setChecksum(String checksum) {
        this.checksum = checksum;
    }
// 版本

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
// 虚文件内容

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
// 目录路径(相对于仓库根目录的路径)

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
// 所属仓库id

    public String getVid() {
        return vid;
    }

    public void setVid(String vid) {
        this.vid = vid;
    }
// 文件节点密码

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }
// 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 部门id

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
// 租户

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

