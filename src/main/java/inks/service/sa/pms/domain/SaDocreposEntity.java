package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 仓库表(SaDocrepos)实体类
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
public class SaDocreposEntity implements Serializable {
    private static final long serialVersionUID = -44880592905389307L;
    // 唯一标识
    private String id;
    // 名称
    private String name;
    // 类型
    private Integer type;
    // 路径
    private String path;
    // 真实文档路径
    private String realdocpath;
    // 版本控制
    private Integer verctrl;
    // 是否远程
    private Integer isremote;
    // 本地SVN路径
    private String localminiopath;
    // SVN路径
    private String miniopath;
    // SVN用户
    private String miniouser;
    // SVN密码
    private String miniopwd;
    // 版本号
    private String version;
    // 版本控制1
    private Integer verctrl1;
    // 信息
    private String info;
    // 密码
    private String pwd;
    // 所有者
    private Integer owner;
    // 状态
    private Integer state;
    // 锁定人id
    private String lockby;
    // 锁定时间
    private Date locktime;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 部门id
    private String deptid;
    // 租户
    private String tenantid;
    // 乐观锁
    private Integer revision;

// 唯一标识

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 名称

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
// 类型

    public Integer getType() {
        return type;
    }

    public void setType(Integer type) {
        this.type = type;
    }
// 路径

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
// 真实文档路径

    public String getRealdocpath() {
        return realdocpath;
    }

    public void setRealdocpath(String realdocpath) {
        this.realdocpath = realdocpath;
    }
// 版本控制

    public Integer getVerctrl() {
        return verctrl;
    }

    public void setVerctrl(Integer verctrl) {
        this.verctrl = verctrl;
    }
// 是否远程

    public Integer getIsremote() {
        return isremote;
    }

    public void setIsremote(Integer isremote) {
        this.isremote = isremote;
    }
// 本地SVN路径

    public String getLocalminiopath() {
        return localminiopath;
    }

    public void setLocalminiopath(String localminiopath) {
        this.localminiopath = localminiopath;
    }
// SVN路径

    public String getMiniopath() {
        return miniopath;
    }

    public void setMiniopath(String miniopath) {
        this.miniopath = miniopath;
    }
// SVN用户

    public String getMiniouser() {
        return miniouser;
    }

    public void setMiniouser(String miniouser) {
        this.miniouser = miniouser;
    }
// SVN密码

    public String getMiniopwd() {
        return miniopwd;
    }

    public void setMiniopwd(String miniopwd) {
        this.miniopwd = miniopwd;
    }
// 版本号

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }
// 版本控制1

    public Integer getVerctrl1() {
        return verctrl1;
    }

    public void setVerctrl1(Integer verctrl1) {
        this.verctrl1 = verctrl1;
    }
// 信息

    public String getInfo() {
        return info;
    }

    public void setInfo(String info) {
        this.info = info;
    }
// 密码

    public String getPwd() {
        return pwd;
    }

    public void setPwd(String pwd) {
        this.pwd = pwd;
    }
// 所有者

    public Integer getOwner() {
        return owner;
    }

    public void setOwner(Integer owner) {
        this.owner = owner;
    }
// 状态

    public Integer getState() {
        return state;
    }

    public void setState(Integer state) {
        this.state = state;
    }
// 锁定人id

    public String getLockby() {
        return lockby;
    }

    public void setLockby(String lockby) {
        this.lockby = lockby;
    }
// 锁定时间

    public Date getLocktime() {
        return locktime;
    }

    public void setLocktime(Date locktime) {
        this.locktime = locktime;
    }
// 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 部门id

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
// 租户

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

