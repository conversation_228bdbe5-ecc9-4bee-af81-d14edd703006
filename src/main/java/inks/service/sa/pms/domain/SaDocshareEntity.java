package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 文档分享(SaDocshare)实体类
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
public class SaDocshareEntity implements Serializable {
    private static final long serialVersionUID = -74539965289484281L;
    // 唯一标识
    private String id;
    // 分享ID
    private String shareid;
    // 名称
    private String name;
    // 路径
    private String path;
    // 文档ID
    private String docid;
    // 版本ID
    private String vid;
    // 分享权限
    private String shareauth;
    // 分享密码
    private String sharepwd;
    // 分享人ID
    private String sharedby;
    // 过期时间
    private Date expiretime;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 部门id
    private String deptid;
    // 租户
    private String tenantid;
    // 乐观锁
    private Integer revision;

// 唯一标识

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 分享ID

    public String getShareid() {
        return shareid;
    }

    public void setShareid(String shareid) {
        this.shareid = shareid;
    }
// 名称

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
// 路径

    public String getPath() {
        return path;
    }

    public void setPath(String path) {
        this.path = path;
    }
// 文档ID

    public String getDocid() {
        return docid;
    }

    public void setDocid(String docid) {
        this.docid = docid;
    }
// 版本ID

    public String getVid() {
        return vid;
    }

    public void setVid(String vid) {
        this.vid = vid;
    }
// 分享权限

    public String getShareauth() {
        return shareauth;
    }

    public void setShareauth(String shareauth) {
        this.shareauth = shareauth;
    }
// 分享密码

    public String getSharepwd() {
        return sharepwd;
    }

    public void setSharepwd(String sharepwd) {
        this.sharepwd = sharepwd;
    }
// 分享人ID

    public String getSharedby() {
        return sharedby;
    }

    public void setSharedby(String sharedby) {
        this.sharedby = sharedby;
    }
// 过期时间

    public Date getExpiretime() {
        return expiretime;
    }

    public void setExpiretime(Date expiretime) {
        this.expiretime = expiretime;
    }
// 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 部门id

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
// 租户

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

