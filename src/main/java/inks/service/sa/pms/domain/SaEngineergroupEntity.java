package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 工程组主表(SaEngineergroup)实体类
 *
 * <AUTHOR>
 * @since 2025-07-15 12:57:58
 */
@Data
public class SaEngineergroupEntity implements Serializable {
    private static final long serialVersionUID = -61924194464376181L;
     // 主键ID
    private String id;
     // 工程组编码
    private String groupcode;
     // 工程组名称
    private String groupname;
     // 工程组类型(后端/前端/运维/测试等)
    private String grouptype;
     // 工程组描述
    private String groupdesc;
     // 组长姓名
    private String groupleader;
     // 组长ID(关联Sa_Engineer.id)
    private String groupleaderid;
     // 启用标记(0:禁用,1:启用)
    private Integer enabledmark;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

