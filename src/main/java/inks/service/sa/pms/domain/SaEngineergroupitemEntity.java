package inks.service.sa.pms.domain;

import java.io.Serializable;
import lombok.Data;

/**
 * 工程组成员子表(SaEngineergroupitem)Entity
 *
 * <AUTHOR>
 * @since 2025-07-15 12:58:10
 */
@Data
public class SaEngineergroupitemEntity implements Serializable {
    private static final long serialVersionUID = -42876980362572517L;
     // 主键ID
    private String id;
     // Pid
    private String pid;
     // 工程师ID(关联Sa_Engineer.id)
    private String engineerid;
     // 工程师姓名
    private String engineername;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 自定义字段1
    private String custom1;
     // 自定义字段2
    private String custom2;
     // 自定义字段3
    private String custom3;
     // 自定义字段4
    private String custom4;
     // 自定义字段5
    private String custom5;
     // 租户ID
    private String tenantid;
     // 乐观锁版本号
    private Integer revision;


}

