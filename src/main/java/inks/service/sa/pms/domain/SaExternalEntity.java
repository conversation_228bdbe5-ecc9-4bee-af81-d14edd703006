package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 扩展应用(SaExternal)实体类
 *
 * <AUTHOR>
 * @since 2023-11-14 09:26:10
 */
public class SaExternalEntity implements Serializable {
    private static final long serialVersionUID = 513202557558085628L;
    // id
    private String id;
    // 类型
    private String exttype;
    // 编码
    private String extcode;
    // 名称
    private String extname;
    // 标题
    private String exttitle;
    // 封面图片Url
    private String frontphoto;
    // Css图标
    private String imagecss;
    // Url位置
    private String exturl;
    // ExtPd
    private String extpd;
    // AppUrl
    private String appurl;
    // AppPd
    private String apppd;
    // 排列序号
    private Integer rownum;
    // 有效标识
    private Integer enabledmark;
    // 是否公共
    private Integer ispublic;
    // 许可编码
    private String permissioncode;
    // 备注
    private String remark;
    // 负责人
    private String operator;
    // 负责人id
    private String operatorid;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

// id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 类型

    public String getExttype() {
        return exttype;
    }

    public void setExttype(String exttype) {
        this.exttype = exttype;
    }
// 编码

    public String getExtcode() {
        return extcode;
    }

    public void setExtcode(String extcode) {
        this.extcode = extcode;
    }
// 名称

    public String getExtname() {
        return extname;
    }

    public void setExtname(String extname) {
        this.extname = extname;
    }
// 标题

    public String getExttitle() {
        return exttitle;
    }

    public void setExttitle(String exttitle) {
        this.exttitle = exttitle;
    }
// 封面图片Url

    public String getFrontphoto() {
        return frontphoto;
    }

    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }
// Css图标

    public String getImagecss() {
        return imagecss;
    }

    public void setImagecss(String imagecss) {
        this.imagecss = imagecss;
    }
// Url位置

    public String getExturl() {
        return exturl;
    }

    public void setExturl(String exturl) {
        this.exturl = exturl;
    }
// ExtPd

    public String getExtpd() {
        return extpd;
    }

    public void setExtpd(String extpd) {
        this.extpd = extpd;
    }
// AppUrl

    public String getAppurl() {
        return appurl;
    }

    public void setAppurl(String appurl) {
        this.appurl = appurl;
    }
// AppPd

    public String getApppd() {
        return apppd;
    }

    public void setApppd(String apppd) {
        this.apppd = apppd;
    }
// 排列序号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
// 有效标识

    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
// 是否公共

    public Integer getIspublic() {
        return ispublic;
    }

    public void setIspublic(Integer ispublic) {
        this.ispublic = ispublic;
    }
// 许可编码

    public String getPermissioncode() {
        return permissioncode;
    }

    public void setPermissioncode(String permissioncode) {
        this.permissioncode = permissioncode;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 负责人

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
// 负责人id

    public String getOperatorid() {
        return operatorid;
    }

    public void setOperatorid(String operatorid) {
        this.operatorid = operatorid;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 租户名称

    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

