package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 游客反馈(SaFeedbackvisitor)实体类
 *
 * <AUTHOR>
 * @since 2025-05-06 16:17:52
 */
@Data
public class SaFeedbackvisitorEntity implements Serializable {
    private static final long serialVersionUID = 666108479505341473L;
     // 唯一标识
    private String id;
     // 客户名
    private String custname;
     // 客户id
    private String groupid;
     // 联系电话
    private String phone;
     // 联系邮件
    private String email;
     // 反馈标题
    private String title;
     // 反馈问题
    private String issue;
     // 附件
    private String attachment;
     // 图片url
    private String photos;
     // 是否转出
    private Integer submitmark;
     // 是否解决
    private Integer finishmark;
     // 解决描述
    private String finishdesc;
     // 工程师
    private String engineer;
     // 关闭时间
    private Date closedate;
     // 创建者id
    private String createbyid;
     // 创建者
    private String createby;
     // 新建日期
    private Date createdate;
     // 修改日期
    private Date modifydate;
     // 制表id
    private String listerid;
     // 制表
    private String lister;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 乐观锁
    private Integer revision;



}

