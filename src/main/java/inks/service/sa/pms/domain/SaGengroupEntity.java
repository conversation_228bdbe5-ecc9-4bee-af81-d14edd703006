package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 代码生成器分组(SaGengroup)实体类
 *
 * <AUTHOR>
 * @since 2023-08-07 13:39:34
 */
public class SaGengroupEntity implements Serializable {
    private static final long serialVersionUID = -41702457383453503L;
    // id
    private String id;
    // Parentid
    private String parentid;
    // 功能编码
    private String modulecode;
    // 分组编码
    private String groupcode;
    // 分组名称
    private String groupname;
    // 模板id
    private Integer velocityid;
    // 是否文件夹
    private Integer dirmark;
    // 有效性
    private Integer enabledmark;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 制表
    private String lister;
    // 新建日期
    private Date createdate;
    // 修改日期
    private Date modifydate;
    // 租户id
    private String tenantid;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // Parentid
    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    // 功能编码
    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

    // 分组编码
    public String getGroupcode() {
        return groupcode;
    }

    public void setGroupcode(String groupcode) {
        this.groupcode = groupcode;
    }

    // 分组名称
    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    // 模板id
    public Integer getVelocityid() {
        return velocityid;
    }

    public void setVelocityid(Integer velocityid) {
        this.velocityid = velocityid;
    }

    // 是否文件夹
    public Integer getDirmark() {
        return dirmark;
    }

    public void setDirmark(Integer dirmark) {
        this.dirmark = dirmark;
    }

    // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }


}

