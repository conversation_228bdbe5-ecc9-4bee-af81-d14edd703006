package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 代码生成器模板(SaGenvelocity)实体类
 *
 * <AUTHOR>
 * @since 2023-08-05 15:36:00
 */
public class SaGenvelocityEntity implements Serializable {
    private static final long serialVersionUID = 185334223614190568L;
    private String id;
    // 模板名字
    private String name;
    // 模板文件内容
    private String velocity;
    // 关联代码生成器模板分组
    private String gengroupid;
    // 版本
    private Integer revion;
    // 行号
    private Integer rownum;
    // 审核员
    private String assessor;
    // 审核员id
    private String assessorid;
    // 审核日期
    private Date assessdate;
    // 创建者id
    private String createbyid;
    // 创建者
    private String createby;
    // 新建日期
    private Date createdate;
    // 制表id
    private String listerid;
    // 制表
    private String lister;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 模板名字
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    // 模板文件内容
    public String getVelocity() {
        return velocity;
    }

    public void setVelocity(String velocity) {
        this.velocity = velocity;
    }

    // 关联代码生成器模板分组
    public String getGengroupid() {
        return gengroupid;
    }

    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }

    // 版本
    public Integer getRevion() {
        return revion;
    }

    public void setRevion(Integer revion) {
        this.revion = revion;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 审核员
    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    // 审核员id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

