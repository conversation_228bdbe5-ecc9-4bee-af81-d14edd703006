package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品(SaGoods)实体类
 *
 * <AUTHOR>
 * @since 2023-10-19 15:04:46
 */
public class SaGoodsEntity implements Serializable {
    private static final long serialVersionUID = -58862678368703467L;
    // ID
    private String id;
    // 通用分组
    private String gengroupid;
    // 货品编码
    private String goodscode;
    // 货品名称
    private String goodsname;
    // 货品规格
    private String goodsspec;
    // 货品单位
    private String goodsunit;
    // 货品售价
    private Double goodsprice;
    // 货品成本
    private Double goodscost;
    // 商品描述
    private String desciption;
    // 商品状态
    private Integer enabledmark;
    // 商品照片地址
    private String goodsphote;
    // 作者、运维人员
    private String author;
    // MD内容
    private String markdowndata;
    // minio地址
    private String mdurl;
    // 浏览次数
    private Integer mdlooktimes;
    // 小程序地址 扫码
    private String appurl;
    // 描述Json
    private String descjson;
    // 难易度
    private Integer rateval;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 1公共2内部
    private Integer publicmark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户id
    private String tenantid;
    // 租户名称
    private String tenantname;
    // 乐观锁
    private Integer revision;

// ID

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 通用分组

    public String getGengroupid() {
        return gengroupid;
    }

    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }
// 货品编码

    public String getGoodscode() {
        return goodscode;
    }

    public void setGoodscode(String goodscode) {
        this.goodscode = goodscode;
    }
// 货品名称

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }
// 货品规格

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }
// 货品单位

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }
// 货品售价

    public Double getGoodsprice() {
        return goodsprice;
    }

    public void setGoodsprice(Double goodsprice) {
        this.goodsprice = goodsprice;
    }
// 货品成本

    public Double getGoodscost() {
        return goodscost;
    }

    public void setGoodscost(Double goodscost) {
        this.goodscost = goodscost;
    }
// 商品描述

    public String getDesciption() {
        return desciption;
    }

    public void setDesciption(String desciption) {
        this.desciption = desciption;
    }
// 商品状态

    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
// 商品照片地址

    public String getGoodsphote() {
        return goodsphote;
    }

    public void setGoodsphote(String goodsphote) {
        this.goodsphote = goodsphote;
    }
// 作者、运维人员

    public String getAuthor() {
        return author;
    }

    public void setAuthor(String author) {
        this.author = author;
    }
// MD内容

    public String getMarkdowndata() {
        return markdowndata;
    }

    public void setMarkdowndata(String markdowndata) {
        this.markdowndata = markdowndata;
    }
// minio地址

    public String getMdurl() {
        return mdurl;
    }

    public void setMdurl(String mdurl) {
        this.mdurl = mdurl;
    }
// 浏览次数

    public Integer getMdlooktimes() {
        return mdlooktimes;
    }

    public void setMdlooktimes(Integer mdlooktimes) {
        this.mdlooktimes = mdlooktimes;
    }
// 小程序地址 扫码

    public String getAppurl() {
        return appurl;
    }

    public void setAppurl(String appurl) {
        this.appurl = appurl;
    }
// 描述Json

    public String getDescjson() {
        return descjson;
    }

    public void setDescjson(String descjson) {
        this.descjson = descjson;
    }
// 难易度

    public Integer getRateval() {
        return rateval;
    }

    public void setRateval(Integer rateval) {
        this.rateval = rateval;
    }
// 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
// 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
// 1公共2内部

    public Integer getPublicmark() {
        return publicmark;
    }

    public void setPublicmark(Integer publicmark) {
        this.publicmark = publicmark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 自定义6

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
// 自定义7

    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
// 自定义8

    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
// 自定义9

    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
// 自定义10

    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
// 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 租户名称

    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

