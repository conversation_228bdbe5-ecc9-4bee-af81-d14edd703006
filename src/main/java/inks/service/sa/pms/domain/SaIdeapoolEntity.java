package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 创意池(SaIdeapool)实体类
 *
 * <AUTHOR>
 * @since 2024-12-07 14:11:45
 */
@Data
public class SaIdeapoolEntity implements Serializable {
    private static final long serialVersionUID = -31044340707727746L;
     // id
    private String id;
     // 编码
    private String refno;
     // 单据日期
    private Date billdate;
     // 创意标题
    private String ideatitle;
     // 创意类型
    private String ideatype;
     // 创意内容
    private String ideajson;
     // 公共
    private Integer publicmark;
     // 完成
    private Integer finishmark;
     // 经办人
    private String operator;
     // 经办人id
    private String operatorid;
     // 数量
    private Integer itemcount;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 部门ID
    private String deptid;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

