package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 功能简介(SaIntro)实体类
 *
 * <AUTHOR>
 * @since 2023-06-24 08:56:13
 */
public class SaIntroEntity implements Serializable {
    private static final long serialVersionUID = 270440829192502743L;
    // id
    private String id;
    // 通用分组(备用)
    private String gengroupid;
    // 功能编码
    private String modulecode;
    // 简介名称
    private String introname;
    // 富文本内容
    private String introcontent;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 通用分组(备用)
    public String getGengroupid() {
        return gengroupid;
    }

    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }

    // 功能编码
    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

    // 简介名称
    public String getIntroname() {
        return introname;
    }

    public void setIntroname(String introname) {
        this.introname = introname;
    }

    // 富文本内容
    public String getIntrocontent() {
        return introcontent;
    }

    public void setIntrocontent(String introcontent) {
        this.introcontent = introcontent;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }


}

