package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 会议主表(SaMeeting)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:52:46
 */
@Data
public class SaMeetingEntity implements Serializable {
    private static final long serialVersionUID = 418804573145846768L;
     // id
    private String id;
     // 会议标题
    private String meetingtitle;
     // 会议纪要内容
    private String meetingdesc;
     // 状态(0:准备中,1:进行中,2:待审核,3:已审核,4:已驳回)
    private Integer status;
     // 会议开始时间
    private Date starttime;
     // 会议结束时间
    private Date endtime;
     // 会议地点
    private String location;
     // 参会人数
    private Integer attendeecount;
     // 实际出席人数
    private Integer actattendeecount;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 审核意见
    private String assesscomment;
     // 款数
    private Integer itemcount;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

