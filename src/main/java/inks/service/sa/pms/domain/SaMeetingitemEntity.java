package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 会议子表-议题(SaMeetingitem)Entity
 *
 * <AUTHOR>
 * @since 2025-06-17 09:50:13
 */
@Data
public class SaMeetingitemEntity implements Serializable {
    private static final long serialVersionUID = -36530341495541934L;
     // id
    private String id;
     // 会议id
    private String pid;
     // 议题标题
    private String topictitle;
     // 议题详细内容
    private String topiccontent;
     // 议题结果/结论
    private String topicresult;
     // 负责人
    private String principal;
     // 负责人id
    private String principalid;
     // 截止日期
    private Date deadlinedate;
     // 优先级(1:低,2:中,3:高)
    private Integer priority;
     // 议题状态(0:待讨论,1:讨论中,2:已完成,3:已延期)
    private Integer topicstatus;
     // 预计讨论时长(分钟)
    private Integer duration;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;


}

