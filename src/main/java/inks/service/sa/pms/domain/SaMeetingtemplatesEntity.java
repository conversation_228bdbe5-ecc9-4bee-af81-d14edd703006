package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 会议模板表(SaMeetingtemplates)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:46:22
 */
@Data
public class SaMeetingtemplatesEntity implements Serializable {
    private static final long serialVersionUID = 366246191333577174L;
     // id
    private String id;
     // 模板名称
    private String templatename;
     // 模板类型(1:周例会,2:月例会,3:项目会议,4:培训会议,5:其他)
    private Integer templatetype;
     // 模板描述
    private String description;
     // 默认会议时长(分钟)
    private Integer deftduration;
     // 默认会议地点
    private String deftlocation;
     // 议题模板(JSON格式)
    private String topictemplate;
     // 参与人员模板(JSON格式)
    private String parttemplate;
     // 是否公开(0:否,1:是)
    private Integer ispublic;
     // 使用次数
    private Integer usagecount;
     // 是否启用(0:否,1:是)
    private Integer isactive;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;



}

