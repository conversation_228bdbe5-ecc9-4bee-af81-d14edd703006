package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 产品简介(SaProjprofile)实体类
 *
 * <AUTHOR>
 * @since 2023-10-18 12:43:10
 */
public class SaProjprofileEntity implements Serializable {
    private static final long serialVersionUID = 653016025598776078L;
    private String id;
    // 产品id
    private String projectid;
    // 产品名称
    private String projectname;
    // 内容标题
    private String title;
    // 视频id
    private String videoid;
    // 视频名称
    private String videoname;
    // 内容URL
    private String content;
    // 封面URL
    private String picture;
    // 文件名
    private String filename;
    // 目录名
    private String dirname;
    // 浏览次数
    private Integer looktimes;
    // 点赞量
    private Integer goodnum;
    // 点踩量
    private Integer ngnum;
    // 1公共2内部
    private Integer publicmark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户id
    private String tenantid;
    // 乐观锁
    private Integer revision;


    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
// 产品id

    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }
// 产品名称

    public String getProjectname() {
        return projectname;
    }

    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }
// 内容标题

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
// 视频id

    public String getVideoid() {
        return videoid;
    }

    public void setVideoid(String videoid) {
        this.videoid = videoid;
    }
// 视频名称

    public String getVideoname() {
        return videoname;
    }

    public void setVideoname(String videoname) {
        this.videoname = videoname;
    }
// 内容URL

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
// 封面URL

    public String getPicture() {
        return picture;
    }

    public void setPicture(String picture) {
        this.picture = picture;
    }
// 文件名

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }
// 目录名

    public String getDirname() {
        return dirname;
    }

    public void setDirname(String dirname) {
        this.dirname = dirname;
    }
// 浏览次数

    public Integer getLooktimes() {
        return looktimes;
    }

    public void setLooktimes(Integer looktimes) {
        this.looktimes = looktimes;
    }
// 点赞量

    public Integer getGoodnum() {
        return goodnum;
    }

    public void setGoodnum(Integer goodnum) {
        this.goodnum = goodnum;
    }
// 点踩量

    public Integer getNgnum() {
        return ngnum;
    }

    public void setNgnum(Integer ngnum) {
        this.ngnum = ngnum;
    }
// 1公共2内部

    public Integer getPublicmark() {
        return publicmark;
    }

    public void setPublicmark(Integer publicmark) {
        this.publicmark = publicmark;
    }
// 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
// 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
// 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
// 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
// 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
// 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
// 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
// 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
// 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
// 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
// 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
// 自定义6

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
// 自定义7

    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
// 自定义8

    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
// 自定义9

    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
// 自定义10

    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
// 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
// 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

