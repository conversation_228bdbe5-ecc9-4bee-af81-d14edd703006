package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * (SaRelease)实体类
 *
 * <AUTHOR>
 * @since 2024-06-05 14:57:47
 */
public class SaReleaseEntity implements Serializable {
    private static final long serialVersionUID = -33787553396992778L;
     // id
    private String id;
     // 发布名称
    private String releasename;
     // GitLab的项目名称
    private String projectname;
     // 发布日期
    private Date releasedate;
     // 版本
    private String version;
     // 描述
    private String description;
     // 备注
    private String remark;
     // 是否里程碑
    private Integer milestone;
     // 完成
    private Integer finishmark;
     // 关闭
    private Integer closed;
     // 图片Url1
    private String photourl1;
     // 图片Url2
    private String photourl2;
     // 图片Url3
    private String photourl3;
     // 项目id
    private String projectid;
     // 项目编码
    private String projectcode;
     // 行号
    private Integer rownum;
     // 创建者id
    private String createbyid;
     // 创建者
    private String createby;
     // 新建日期
    private Date createdate;
     // 制表id
    private String listerid;
     // 制表
    private String lister;
     // 修改日期
    private Date modifydate;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 发布名称
    public String getReleasename() {
        return releasename;
    }
    
    public void setReleasename(String releasename) {
        this.releasename = releasename;
    }
        
   // GitLab的项目名称
    public String getProjectname() {
        return projectname;
    }
    
    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }
        
   // 发布日期
    public Date getReleasedate() {
        return releasedate;
    }
    
    public void setReleasedate(Date releasedate) {
        this.releasedate = releasedate;
    }
        
   // 版本
    public String getVersion() {
        return version;
    }
    
    public void setVersion(String version) {
        this.version = version;
    }
        
   // 描述
    public String getDescription() {
        return description;
    }
    
    public void setDescription(String description) {
        this.description = description;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 是否里程碑
    public Integer getMilestone() {
        return milestone;
    }
    
    public void setMilestone(Integer milestone) {
        this.milestone = milestone;
    }
        
   // 完成
    public Integer getFinishmark() {
        return finishmark;
    }
    
    public void setFinishmark(Integer finishmark) {
        this.finishmark = finishmark;
    }
        
   // 关闭
    public Integer getClosed() {
        return closed;
    }
    
    public void setClosed(Integer closed) {
        this.closed = closed;
    }
        
   // 图片Url1
    public String getPhotourl1() {
        return photourl1;
    }
    
    public void setPhotourl1(String photourl1) {
        this.photourl1 = photourl1;
    }
        
   // 图片Url2
    public String getPhotourl2() {
        return photourl2;
    }
    
    public void setPhotourl2(String photourl2) {
        this.photourl2 = photourl2;
    }
        
   // 图片Url3
    public String getPhotourl3() {
        return photourl3;
    }
    
    public void setPhotourl3(String photourl3) {
        this.photourl3 = photourl3;
    }
        
   // 项目id
    public String getProjectid() {
        return projectid;
    }
    
    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }
        
   // 项目编码
    public String getProjectcode() {
        return projectcode;
    }
    
    public void setProjectcode(String projectcode) {
        this.projectcode = projectcode;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 审核员
    public String getAssessor() {
        return assessor;
    }
    
    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
        
   // 审核员id
    public String getAssessorid() {
        return assessorid;
    }
    
    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
        
   // 审核日期
    public Date getAssessdate() {
        return assessdate;
    }
    
    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

