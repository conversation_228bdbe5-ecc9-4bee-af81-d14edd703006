package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 报表模版需求(SaReportsdemand)实体类
 *
 * <AUTHOR>
 * @since 2025-07-22 15:54:01
 */
@Data
public class SaReportsdemandEntity implements Serializable {
    private static final long serialVersionUID = -77743081149840115L;
     // id
    private String id;
     // 模块编码
    private String modulecode;
     // 报表类型
    private String rpttype;
     // 报表名称
    private String rptname;
     // 用户需求说明
    private String userdemand;
     // 公司
    private String company;
     // 原始的报表数据
    private String orggrfdata;
     // 修改后报表数据
    private String grfdata;
     // 服务码(随机数)
    private String sercode;
     // 经办人
    private String operator;
     // 经办人id
    private String operatorid;
     // 完成时间
    private Date finishdate;
     // 完成描述
    private String finishdesc;
     // 完成标识
    private Integer finishmark;
     // 有效标识
    private Integer enabledmark;
     // 序号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

