package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * SQL变更(SaSqlecn)实体类
 *
 * <AUTHOR>
 * @since 2025-03-17 14:32:05
 */
@Data
public class SaSqlecnEntity implements Serializable {
    private static final long serialVersionUID = -32785848527026807L;
     // id
    private String id;
     // 变更类型
    private String ecntype;
     // 变更原因
    private String ecnreason;
     // 变更描述
    private String ecndesc;
     // SQL文本
    private String sqltext;
     // 所属系统编码
    private String code;
     // 时间戳
    private Long timestamp;
     // 回滚文本【可选】
    private String rollbacktext;
     // 项目id
    private String projectid;
     // 影响范围
    private String impactscope;
     // 风险评估
    private String risk;
     // 正在进行OA
    private Integer oaflowmark;
     // 审核员
    private String assessor;
     // 审核员id
    private String assessorid;
     // 审核日期
    private Date assessdate;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 租户id
    private String tenantid;
     // 租户名称
    private String tenantname;
     // 乐观锁
    private Integer revision;



}

