package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 跟踪表主表(SaTracking)实体类
 *
 * <AUTHOR>
 * @since 2025-03-14 15:12:30
 */
@Data
public class SaTrackingEntity implements Serializable {
    private static final long serialVersionUID = 928903722770224813L;
     // 主键
    private String id;
     // 类型: 上线计划表、上线实施表、开发计划表、测试计划表等
    private String type;
     // 客户
    private String customer;
     // 工程项目
    private String project;
     // 任务总数
    private Integer itemcount;
     // 已完成任务数
    private Integer finishcount;
     // 主题，用于记录软件著作权申请
    private String subject;
     // 计划日期
    private Date billplandate;
     // item最大完工时间
    private Date billfinishdate;
     // 已排程数
    private Integer planfinishcount;
     // 已完工数
    private Integer billfinishcount;
     // 是否已关闭
    private Integer closed;
     // 公共标志
    private Integer publicmark;
     // 经办人
    private String operator;
     // 经办人ID
    private String operatorid;
     // 协助人员ID
    private String assistantids;
     // 协助人员名称
    private String assistantnames;
     // 创建者id
    private String createbyid;
     // 创建者
    private String createby;
     // 新建日期
    private Date createdate;
     // 制表id
    private String listerid;
     // 制表
    private String lister;
     // 修改日期
    private Date modifydate;
     // 作废
    private Integer disannulcount;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 乐观锁
    private Integer revision;


}

