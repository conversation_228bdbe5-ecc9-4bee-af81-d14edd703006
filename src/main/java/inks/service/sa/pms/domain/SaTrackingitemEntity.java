package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 跟踪表子表(SaTrackingitem)Entity
 *
 * <AUTHOR>
 * @since 2025-03-14 15:09:39
 */
@Data
public class SaTrackingitemEntity implements Serializable {
    private static final long serialVersionUID = -36411015432313595L;
     // 主键
    private String id;
     // 关联的跟踪表ID
    private String pid;
     // 阶段名
    private String phasename;
     // 任务名
    private String taskname;
     // 描述
    private String description;
     // 计划开始时间
    private Date planstart;
     // 计划结束时间
    private Date planend;
     // 里程碑
    private String milestone;
     // 实际开始时间
    private Date actualstart;
     // 完工时间
    private Date completiondate;
     // 产出成果
    private String outputresult;
     // 主负责人
    private String mainresponsibleperson;
     // 次负责人
    private String subresponsibleperson;
     // 关闭
    private Integer closed;
     // 作废
    private Integer disannulmark;
     // 行号
    private Integer rownum;
     // 备注
    private String remark;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 乐观锁
    private Integer revision;


}

