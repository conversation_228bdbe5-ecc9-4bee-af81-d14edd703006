package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户登录(SaUserlogin)实体类
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:30
 */
public class SaUserloginEntity implements Serializable {
    private static final long serialVersionUID = -17902407232362450L;
    // ID
    private String id;
    // 用户id
    private String userid;
    // 登录密码
    private String userpassword;
    // 制表人id
    private String listerid;
    // 制表人
    private String lister;
    // 创建时间
    private Date createdate;
    // 修改时间
    private Date modifydate;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 用户id
    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    // 登录密码
    public String getUserpassword() {
        return userpassword;
    }

    public void setUserpassword(String userpassword) {
        this.userpassword = userpassword;
    }

    // 制表人id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 制表人
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 创建时间
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改时间
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }


}

