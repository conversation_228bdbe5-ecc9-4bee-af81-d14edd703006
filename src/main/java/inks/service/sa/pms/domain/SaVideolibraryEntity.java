package inks.service.sa.pms.domain;

import java.io.Serializable;
import java.util.Date;

/**
 * 视频信息表(SaVideolibrary)实体类
 *
 * <AUTHOR>
 * @since 2023-08-22 17:05:11
 */
public class SaVideolibraryEntity implements Serializable {
    private static final long serialVersionUID = 404582983386129747L;
    // ID
    private String id;
    // 视频标题
    private String videotitle;
    // 播放地址
    private String videoplayurl;
    // 视频封面地址
    private String videocoverurl;
    // 关联产品id
    private String goodsid;
    // 目录名
    private String dirname;
    // minio文件名
    private String filename;
    // 视频时长
    private Integer videoduration;
    // 播放次数
    private Integer videoplaytimes;
    // 视频点赞量
    private Integer videogoodnum;
    // 视频点踩量
    private Integer videongnum;
    // 视频等级优先级
    private Integer videolevel;
    // 简介
    private String videodesc;
    // 文件大小
    private Long filesize;
    // 一级目录id
    private String fisrtlevelgroupid;
    // 二级目录id
    private String seclevelgroupid;
    // 密钥
    private String secretkey;
    // 上传时间
    private Date uploadtime;
    // 文字教程
    private String texttutorial;
    // 标签
    private String videotag;
    // 附加文本内容
    private String description;
    // 底色
    private String backcolorargb;
    // 底色
    private String forecolorargb;
    // 公共
    private Integer publicmark;
    // 有效
    private Integer enabledmark;
    // 排序码
    private Integer rownum;
    // 备注
    private String remark;
    // 创建者
    private String createby;
    // 创建者id
    private String createbyid;
    // 新建日期
    private Date createdate;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 审核人
    private String assessor;
    // 审核人id
    private String assessorid;
    // 审核时间
    private Date assessdate;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 自定义6
    private String custom6;
    // 自定义7
    private String custom7;
    // 自定义8
    private String custom8;
    // 自定义9
    private String custom9;
    // 自定义10
    private String custom10;
    // 租户
    private String tenantname;
    // 乐观锁
    private Integer revision;
    private String tenantid;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 视频标题
    public String getVideotitle() {
        return videotitle;
    }

    public void setVideotitle(String videotitle) {
        this.videotitle = videotitle;
    }

    // 播放地址
    public String getVideoplayurl() {
        return videoplayurl;
    }

    public void setVideoplayurl(String videoplayurl) {
        this.videoplayurl = videoplayurl;
    }

    // 视频封面地址
    public String getVideocoverurl() {
        return videocoverurl;
    }

    public void setVideocoverurl(String videocoverurl) {
        this.videocoverurl = videocoverurl;
    }

    // 关联产品id
    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    // 目录名
    public String getDirname() {
        return dirname;
    }

    public void setDirname(String dirname) {
        this.dirname = dirname;
    }

    // minio文件名
    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    // 视频时长
    public Integer getVideoduration() {
        return videoduration;
    }

    public void setVideoduration(Integer videoduration) {
        this.videoduration = videoduration;
    }

    // 播放次数
    public Integer getVideoplaytimes() {
        return videoplaytimes;
    }

    public void setVideoplaytimes(Integer videoplaytimes) {
        this.videoplaytimes = videoplaytimes;
    }

    // 视频点赞量
    public Integer getVideogoodnum() {
        return videogoodnum;
    }

    public void setVideogoodnum(Integer videogoodnum) {
        this.videogoodnum = videogoodnum;
    }

    // 视频点踩量
    public Integer getVideongnum() {
        return videongnum;
    }

    public void setVideongnum(Integer videongnum) {
        this.videongnum = videongnum;
    }

    // 视频等级优先级
    public Integer getVideolevel() {
        return videolevel;
    }

    public void setVideolevel(Integer videolevel) {
        this.videolevel = videolevel;
    }

    // 简介
    public String getVideodesc() {
        return videodesc;
    }

    public void setVideodesc(String videodesc) {
        this.videodesc = videodesc;
    }

    // 文件大小
    public Long getFilesize() {
        return filesize;
    }

    public void setFilesize(Long filesize) {
        this.filesize = filesize;
    }

    // 一级目录id
    public String getFisrtlevelgroupid() {
        return fisrtlevelgroupid;
    }

    public void setFisrtlevelgroupid(String fisrtlevelgroupid) {
        this.fisrtlevelgroupid = fisrtlevelgroupid;
    }

    // 二级目录id
    public String getSeclevelgroupid() {
        return seclevelgroupid;
    }

    public void setSeclevelgroupid(String seclevelgroupid) {
        this.seclevelgroupid = seclevelgroupid;
    }

    // 密钥
    public String getSecretkey() {
        return secretkey;
    }

    public void setSecretkey(String secretkey) {
        this.secretkey = secretkey;
    }

    // 上传时间
    public Date getUploadtime() {
        return uploadtime;
    }

    public void setUploadtime(Date uploadtime) {
        this.uploadtime = uploadtime;
    }

    // 文字教程
    public String getTexttutorial() {
        return texttutorial;
    }

    public void setTexttutorial(String texttutorial) {
        this.texttutorial = texttutorial;
    }

    // 标签
    public String getVideotag() {
        return videotag;
    }

    public void setVideotag(String videotag) {
        this.videotag = videotag;
    }

    // 附加文本内容
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // 底色
    public String getBackcolorargb() {
        return backcolorargb;
    }

    public void setBackcolorargb(String backcolorargb) {
        this.backcolorargb = backcolorargb;
    }

    // 底色
    public String getForecolorargb() {
        return forecolorargb;
    }

    public void setForecolorargb(String forecolorargb) {
        this.forecolorargb = forecolorargb;
    }

    // 公共
    public Integer getPublicmark() {
        return publicmark;
    }

    public void setPublicmark(Integer publicmark) {
        this.publicmark = publicmark;
    }

    // 有效
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 排序码
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 审核人
    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }

    // 审核人id
    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }

    // 审核时间
    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }


}

