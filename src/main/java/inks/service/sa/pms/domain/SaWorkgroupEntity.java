package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import lombok.Data;

/**
 * 往来单位(SaWorkgroup)实体类
 *
 * <AUTHOR>
 * @since 2025-07-10 16:12:02
 */
@Data
public class SaWorkgroupEntity implements Serializable {
    private static final long serialVersionUID = -85061438949706100L;
     // id
    private String id;
     // 分组id
    private String wggroupid;
     // 编码
    private String groupuid;
     // 名称
    private String groupname;
     // 缩写
    private String abbreviate;
     // 组分类
    private String groupclass;
     // 联系人
    private String linkman;
     // 联系电话
    private String telephone;
     // 传真
    private String groupfax;
     // 地址
    private String groupadd;
     // 备注
    private String remark;
     // 有效期
    private Date invaliddate;
     // 组类别
    private String grouptype;
     // 行号
    private Integer rownum;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 联系手机
    private String mobile;
     // 国家
    private String country;
     // 省份
    private String province;
     // 邮编
    private String groupzip;
     // 业务员
    private String seller;
     // 有效
    private Integer enabledmark;
     // 负责人
    private String operator;
     // 负责人id
    private String operatorid;
     // 任务协作者ids
    private String collaboratorids;
     // 任务协作者们
    private String collaborators;
     // 服务码
    private String sercode;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;



}

