package inks.service.sa.pms.domain;

import java.util.Date;
import java.io.Serializable;
import com.fasterxml.jackson.annotation.JsonProperty;
import cn.afterturn.easypoi.excel.annotation.Excel;

/**
 * 工任日志(SaWorklog)实体类
 *
 * <AUTHOR>
 * @since 2024-10-29 09:17:51
 */
public class SaWorklogEntity implements Serializable {
    private static final long serialVersionUID = 777057741176550072L;
    private String id;
     // 时间
    private Date workdate;
     // 0日志/1周报
    private Integer worktype;
     // 天气
    private String weather;
     // 今日工作事项
    private String worktoday;
     // 明日工作事项
    private String worktomorrow;
     // 邮件标题
    private String title;
     // 邮件发送次数
    private Integer sendemailnum;
     // 收件人邮箱
    private String toemail;
     // 抄送人邮箱
    private String otheremails;
     // 备注
    private String summary;
     // 创建者
    private String createby;
     // 创建者id
    private String createbyid;
     // 新建日期
    private Date createdate;
     // 制表
    private String lister;
     // 制表id
    private String listerid;
     // 修改日期
    private Date modifydate;
     // 自定义1
    private String custom1;
     // 自定义2
    private String custom2;
     // 自定义3
    private String custom3;
     // 自定义4
    private String custom4;
     // 自定义5
    private String custom5;
     // 自定义6
    private String custom6;
     // 自定义7
    private String custom7;
     // 自定义8
    private String custom8;
     // 自定义9
    private String custom9;
     // 自定义10
    private String custom10;
     // 租户id
    private String tenantid;
     // 乐观锁
    private Integer revision;

    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 时间
    public Date getWorkdate() {
        return workdate;
    }
    
    public void setWorkdate(Date workdate) {
        this.workdate = workdate;
    }
        
   // 0日志/1周报
    public Integer getWorktype() {
        return worktype;
    }
    
    public void setWorktype(Integer worktype) {
        this.worktype = worktype;
    }
        
   // 天气
    public String getWeather() {
        return weather;
    }
    
    public void setWeather(String weather) {
        this.weather = weather;
    }
        
   // 今日工作事项
    public String getWorktoday() {
        return worktoday;
    }
    
    public void setWorktoday(String worktoday) {
        this.worktoday = worktoday;
    }
        
   // 明日工作事项
    public String getWorktomorrow() {
        return worktomorrow;
    }
    
    public void setWorktomorrow(String worktomorrow) {
        this.worktomorrow = worktomorrow;
    }
        
   // 邮件标题
    public String getTitle() {
        return title;
    }
    
    public void setTitle(String title) {
        this.title = title;
    }
        
   // 邮件发送次数
    public Integer getSendemailnum() {
        return sendemailnum;
    }
    
    public void setSendemailnum(Integer sendemailnum) {
        this.sendemailnum = sendemailnum;
    }
        
   // 收件人邮箱
    public String getToemail() {
        return toemail;
    }
    
    public void setToemail(String toemail) {
        this.toemail = toemail;
    }
        
   // 抄送人邮箱
    public String getOtheremails() {
        return otheremails;
    }
    
    public void setOtheremails(String otheremails) {
        this.otheremails = otheremails;
    }
        
   // 备注
    public String getSummary() {
        return summary;
    }
    
    public void setSummary(String summary) {
        this.summary = summary;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 自定义6
    public String getCustom6() {
        return custom6;
    }
    
    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
        
   // 自定义7
    public String getCustom7() {
        return custom7;
    }
    
    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
        
   // 自定义8
    public String getCustom8() {
        return custom8;
    }
    
    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
        
   // 自定义9
    public String getCustom9() {
        return custom9;
    }
    
    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
        
   // 自定义10
    public String getCustom10() {
        return custom10;
    }
    
    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

