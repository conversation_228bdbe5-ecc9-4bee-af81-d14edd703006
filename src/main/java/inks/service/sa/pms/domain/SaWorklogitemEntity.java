package inks.service.sa.pms.domain;

import java.io.Serializable;

/**
 * 工作日志今日子表(SaWorklogitem)Entity
 *
 * <AUTHOR>
 * @since 2023-09-04 12:58:20
 */
public class SaWorklogitemEntity implements Serializable {
    private static final long serialVersionUID = 883413211179993777L;
    // id
    private String id;
    // Pid
    private String pid;
    // 类型:开发/测试/手册/维护/研发
    private String itemtype;
    // 项目id
    private String projectid;
    // 工时(单位0.5H)
    private Double worktime;
    // 描述
    private String itemdesc;
    // 功能编码
    private String modulecode;
    // 工作完成比例
    private Integer workcomprate;
    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 自定义1
    private String custom1;
    // 自定义2
    private String custom2;
    // 自定义3
    private String custom3;
    // 自定义4
    private String custom4;
    // 自定义5
    private String custom5;
    // 乐观锁
    private Integer revision;

    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // Pid

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
    // 类型:开发/测试/手册/维护/研发

    public String getItemtype() {
        return itemtype;
    }

    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }
    // 项目id

    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }
    // 工时(单位0.5H)

    public Double getWorktime() {
        return worktime;
    }

    public void setWorktime(Double worktime) {
        this.worktime = worktime;
    }
    // 描述

    public String getItemdesc() {
        return itemdesc;
    }

    public void setItemdesc(String itemdesc) {
        this.itemdesc = itemdesc;
    }
    // 功能编码

    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
    // 工作完成比例

    public Integer getWorkcomprate() {
        return workcomprate;
    }

    public void setWorkcomprate(Integer workcomprate) {
        this.workcomprate = workcomprate;
    }
    // 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

