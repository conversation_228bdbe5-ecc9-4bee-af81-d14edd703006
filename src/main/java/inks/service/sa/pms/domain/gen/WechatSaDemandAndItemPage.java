package inks.service.sa.pms.domain.gen;

import lombok.Getter;
import lombok.Setter;

import java.io.Serializable;
import java.util.Date;

@Getter
@Setter
public class WechatSaDemandAndItemPage implements Serializable {

    // 编码
    private String refno;
    // 单据日期
    private Date billdate;
    // 单据类型
    private String billtype;
    // 单据标题
    private String billtitle;

    // id
    private String id;
    // Pid
    private String pid;
    // 进程节点
    private String pointid;
    // 进程名称
    private String itemname;
    // 完工描述
    private String itemdesc;
    // 关联单号
    private String relateduid;
    // 步号
    private Integer stepnum;
    // 天数
    private Integer daynum;
    // 前置节点
    private String prepointid;
    // 监督人
    private String supervisor;
    // 监督人id
    private String supervisorid;
    // 代理人
    private String agentname;
    // 代理人id
    private String agentid;
    // 经办人
    private String operator;
    // 经办人id
    private String operatorid;

    // 接受者id
    private String accepterid;

    // 接受者
    private String accepter;

    // 接受日期
    private Date acceptdate;

    // 行号
    private Integer rownum;
    // 备注
    private String remark;
    // 制表
    private String lister;
    // 制表id
    private String listerid;
    // 修改日期
    private Date modifydate;
    // 提交人
    private String submitter;
    // 提交人id
    private String submitterid;
    // 审核日期
    private Date submitdate;
}
