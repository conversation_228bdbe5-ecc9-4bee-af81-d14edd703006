package inks.service.sa.pms.domain.pojo;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2023年02月04日 13:02
 */
public class PrintGoodsPojo implements Serializable {
    private Integer rownum;
    private String goodsid;
    private String goodspuid;
    private String goodsname;
    private String goodsunit;
    private String goodsspec;
    private Double amount;
    private String itemcode;
    private String itemname;
    private String itemspec;
    private String itemunit;
    private Double price;
    private Double quantity;
    private String remark;
    private String pid;
    private String isEdit;


    public PrintGoodsPojo() {
        this.rownum = null;
        this.goodsid = "";
        this.goodspuid = "";
        this.goodsname = "";
        this.goodsunit = "";
        this.goodsspec = "";
        this.amount = null;
        this.itemcode = "";
        this.itemname = "";
        this.itemspec = "";
        this.itemunit = "";
        this.price = null;
        this.quantity = null;
        this.remark = "";
        this.pid = "";
        this.isEdit = "";
    }

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    public String getGoodsid() {
        return goodsid;
    }

    public void setGoodsid(String goodsid) {
        this.goodsid = goodsid;
    }

    public String getGoodspuid() {
        return goodspuid;
    }

    public void setGoodspuid(String goodspuid) {
        this.goodspuid = goodspuid;
    }

    public String getGoodsname() {
        return goodsname;
    }

    public void setGoodsname(String goodsname) {
        this.goodsname = goodsname;
    }

    public String getGoodsunit() {
        return goodsunit;
    }

    public void setGoodsunit(String goodsunit) {
        this.goodsunit = goodsunit;
    }

    public String getGoodsspec() {
        return goodsspec;
    }

    public void setGoodsspec(String goodsspec) {
        this.goodsspec = goodsspec;
    }

    public Double getAmount() {
        return amount;
    }

    public void setAmount(Double amount) {
        this.amount = amount;
    }

    public String getItemcode() {
        return itemcode;
    }

    public void setItemcode(String itemcode) {
        this.itemcode = itemcode;
    }

    public String getItemname() {
        return itemname;
    }

    public void setItemname(String itemname) {
        this.itemname = itemname;
    }

    public String getItemspec() {
        return itemspec;
    }

    public void setItemspec(String itemspec) {
        this.itemspec = itemspec;
    }

    public String getItemunit() {
        return itemunit;
    }

    public void setItemunit(String itemunit) {
        this.itemunit = itemunit;
    }

    public Double getPrice() {
        return price;
    }

    public void setPrice(Double price) {
        this.price = price;
    }

    public Double getQuantity() {
        return quantity;
    }

    public void setQuantity(Double quantity) {
        this.quantity = quantity;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    public String getIsEdit() {
        return isEdit;
    }

    public void setIsEdit(String isEdit) {
        this.isEdit = isEdit;
    }


}
