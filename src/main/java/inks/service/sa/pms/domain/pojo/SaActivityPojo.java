package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 活动主表(SaActivity)实体类
 *
 * <AUTHOR>
 * @since 2025-01-14 16:56:56
 */
@Data
public class SaActivityPojo implements Serializable {
    private static final long serialVersionUID = -55350791362648557L;
     // 主键
     @Excel(name = "主键")
    private String id;
     // 单号
     @Excel(name = "单号")
    private String refno;
     // 单据类型
     @Excel(name = "单据类型")
    private String billtype;
     // 标题
     @Excel(name = "标题")
    private String billtitle;
     // 日期
     @Excel(name = "日期")
    private Date billdate;
     // 活动主题
     @Excel(name = "活动主题")
    private String activitytheme;
     // 指数
     @Excel(name = "指数")
    private Integer exponent;
     // 阶段id
     @Excel(name = "阶段id")
    private String stageid;
     // 状态
     @Excel(name = "状态")
    private String state;
     // 状态日期
     @Excel(name = "状态日期")
    private Date statedate;
     // 附件s
     @Excel(name = "附件s")
    private String attachments;
     // 摘要
     @Excel(name = "摘要")
    private String summary;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 审核员
     @Excel(name = "审核员")
    private String assessor;
     // 审核员id
     @Excel(name = "审核员id")
    private String assessorid;
     // 审核日期
     @Excel(name = "审核日期")
    private Date assessdate;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 自定义6
     @Excel(name = "自定义6")
    private String custom6;
     // 自定义7
     @Excel(name = "自定义7")
    private String custom7;
     // 自定义8
     @Excel(name = "自定义8")
    private String custom8;
     // 自定义9
     @Excel(name = "自定义9")
    private String custom9;
     // 自定义10
     @Excel(name = "自定义10")
    private String custom10;
     // 部门id
     @Excel(name = "部门id")
    private String deptid;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 租户名称
     @Excel(name = "租户名称")
    private String tenantname;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaActivityitemPojo> item;

    //StageName
    private String stagename;
    

}

