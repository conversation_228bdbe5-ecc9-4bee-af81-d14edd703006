package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * 活动子表(SaActivityitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-01-13 17:03:53
 */
@Data
public class SaActivityitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -30293592289020146L;
     // 主键
  @Excel(name = "主键")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 父ID
  @Excel(name = "父ID")    
  private String parentid;
     // 阶段名称
  @Excel(name = "阶段名称")    
  private String stagename;
     // 阶段子表id
  @Excel(name = "阶段子表id")    
  private String stageitemid;
     // 计划日期
  @Excel(name = "计划日期")    
  private Date planndate;
     // 负责人
  @Excel(name = "负责人")    
  private String operator;
     // 负责人id
  @Excel(name = "负责人id")    
  private String operatorid;
     // 协作者们
  @Excel(name = "协作者们")    
  private String collaborators;
     // 协作者们ids
  @Excel(name = "协作者们ids")    
  private String collaboratorids;
     // 完成
  @Excel(name = "完成")    
  private Integer finishmark;
     // 完成日期
  @Excel(name = "完成日期")    
  private Date finishdate;
     // 完成描述
  @Excel(name = "完成描述")    
  private String finishdesc;
     // 必填
  @Excel(name = "必填")
  private Integer mustmark;
     // 允许附件上传
  @Excel(name = "允许附件上传")
  private Integer filemark;
     // 附件s
  @Excel(name = "附件s")    
  private String attachments;
     // 制表
  @Excel(name = "制表")    
  private String lister;
     // 制表id
  @Excel(name = "制表id")    
  private String listerid;
     // 修改日期
  @Excel(name = "修改日期")    
  private Date modifydate;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 自定义6
  @Excel(name = "自定义6")    
  private String custom6;
     // 自定义7
  @Excel(name = "自定义7")    
  private String custom7;
     // 自定义8
  @Excel(name = "自定义8")    
  private String custom8;
     // 自定义9
  @Excel(name = "自定义9")    
  private String custom9;
     // 自定义10
  @Excel(name = "自定义10")    
  private String custom10;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;



}

