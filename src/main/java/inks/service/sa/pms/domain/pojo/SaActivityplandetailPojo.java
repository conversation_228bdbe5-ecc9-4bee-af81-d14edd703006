package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * 活动计划/日志(SaActivityplan)Pojo
 *
 * <AUTHOR>
 * @since 2025-07-21 17:27:54
 */
@Data
public class SaActivityplandetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 468639441266979431L;
     // ID
  @Excel(name = "ID")    
  private String id;
     // 活动主表ID
  @Excel(name = "活动主表ID")    
  private String pid;
     // 类型（0计划, 1日志, 2关键事项）
  @Excel(name = "类型（0计划, 1日志, 2关键事项）")    
  private String type;
     // 关联活动子表id
  @Excel(name = "关联活动子表id")    
  private String actiitemid;
     // 计划时间
  @Excel(name = "计划时间")    
  private Date plandate;
     // 完成时间
  @Excel(name = "完成时间")    
  private Date finishdate;
     // 完成标识（0:未完成,1:完成）
  @Excel(name = "完成标识（0:未完成,1:完成）")    
  private Integer finishmark;
     // 描述
  @Excel(name = "描述")    
  private String description;
     // 附件
  @Excel(name = "附件")    
  private String attachments;
     // 排序码
  @Excel(name = "排序码")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;



}

