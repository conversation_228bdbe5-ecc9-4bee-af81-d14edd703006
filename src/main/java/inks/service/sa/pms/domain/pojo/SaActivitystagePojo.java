package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 活动阶段表(SaActivitystage)实体类
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:13
 */
@Data
public class SaActivitystagePojo implements Serializable {
    private static final long serialVersionUID = 291165711066208635L;
     // 阶段模板主键
     @Excel(name = "阶段模板主键")
    private String id;
     // 阶段名称
     @Excel(name = "阶段名称")
    private String stagename;
     // 阶段类型（如开发、实施等）
     @Excel(name = "阶段类型（如开发、实施等）")
    private String stagetype;
     // 描述
     @Excel(name = "描述")
    private String stagedesc;
     // 排序编号
     @Excel(name = "排序编号")
    private Integer rownum;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaActivitystageitemPojo> item;
    

}

