package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 活动阶段子表(SaActivitystageitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:21
 */
@Data
public class SaActivitystageitemPojo implements Serializable {
    private static final long serialVersionUID = 945099948023106747L;
     // 阶段ID
  @Excel(name = "阶段ID")    
  private String id;
     // 父阶段ID
  @Excel(name = "父阶段ID")    
  private String pid;
     // 阶段名称
  @Excel(name = "阶段名称")    
  private String itemname;
     // 阶段配置（JSON格式）
  @Excel(name = "阶段配置（JSON格式）")    
  private String stagejson;
     // 排序编号
  @Excel(name = "排序编号")    
  private Integer rownum;
     // 摘要
  @Excel(name = "摘要")    
  private String remark;
     // 创建者
  @Excel(name = "创建者")    
  private String createby;
     // 创建者id
  @Excel(name = "创建者id")    
  private String createbyid;
     // 新建日期
  @Excel(name = "新建日期")    
  private Date createdate;
     // 制表
  @Excel(name = "制表")    
  private String lister;
     // 制表id
  @Excel(name = "制表id")    
  private String listerid;
     // 修改日期
  @Excel(name = "修改日期")    
  private Date modifydate;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;


}

