package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 接口信息表(SaApiinfo)实体类
 *
 * <AUTHOR>
 * @since 2025-01-20 16:07:08
 */
@Data
public class SaApiinfoPojo implements Serializable {
    private static final long serialVersionUID = 260226816849863899L;
     // 主键ID
    @Excel(name = "主键ID") 
    private String id;
     // 功能ID
    @Excel(name = "功能ID") 
    private String fnid;
     // 功能编码
    @Excel(name = "功能编码") 
    private String fncode;
     // 接口名称
    @Excel(name = "接口名称") 
    private String apiname;
     // 接口描述
    @Excel(name = "接口描述") 
    private String apidescription;
     // 接口路径
    @Excel(name = "接口路径") 
    private String apiurl;
     // 请求方式(GET,POST等)
    @Excel(name = "请求方式(GET,POST等)") 
    private String httpmethod;
     // 请求参数
    @Excel(name = "请求参数") 
    private String requestparams;
     // 响应参数
    @Excel(name = "响应参数") 
    private String responseparams;
     // 响应示例
    @Excel(name = "响应示例") 
    private String responseexample;
     // 响应状态码
    @Excel(name = "响应状态码") 
    private String statuscode;
     // 操作ID
    @Excel(name = "操作ID") 
    private String operationid;
     // 返回格式
    @Excel(name = "返回格式") 
    private String produces;
     // 请求格式
    @Excel(name = "请求格式") 
    private String consumes;
     // 标签
    @Excel(name = "标签") 
    private String tags;
     // 是否已废弃
    @Excel(name = "是否已废弃") 
    private Integer isdeprecated;
     // Curl
    @Excel(name = "Curl") 
    private String curl;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

