package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 数据库连接池(SaDatabase)实体类
 *
 * <AUTHOR>
 * @since 2024-04-05 15:39:29
 */
public class SaDatabasePojo implements Serializable {
    private static final long serialVersionUID = -66903764449585743L;
    // id
    @Excel(name = "id")
    private String id;
    // 标题
    @Excel(name = "标题")
    private String title;
    // 数据库url
    @Excel(name = "数据库url")
    private String url;
    // 数据库username
    @Excel(name = "数据库username")
    private String username;
    // 数据库password
    @Excel(name = "数据库password")
    private String password;
    // 数据库驱动com.mysql.cj.jdbc.Driver
    @Excel(name = "数据库驱动com.mysql.cj.jdbc.Driver")
    private String driverclassname;
    // 有效性
    @Excel(name = "有效性")
    private Integer enabledmark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;

    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // 标题

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }
    // 数据库url

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
    // 数据库username

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }
    // 数据库password

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }
    // 数据库驱动com.mysql.cj.jdbc.Driver

    public String getDriverclassname() {
        return driverclassname;
    }

    public void setDriverclassname(String driverclassname) {
        this.driverclassname = driverclassname;
    }
    // 有效性

    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
    // 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

}

