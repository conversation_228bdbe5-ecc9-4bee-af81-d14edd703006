package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 表格设计(SaDbdesign)实体类
 *
 * <AUTHOR>
 * @since 2025-01-15 16:40:20
 */
@Data
public class SaDbdesignPojo implements Serializable {
    private static final long serialVersionUID = 634486700073073288L;
     // ID
     @Excel(name = "ID")
    private String id;
     // 功能ID
     @Excel(name = "功能ID")
    private String fnid;
     // 表名
     @Excel(name = "表名")
    private String tablename;
     // 表注释名
     @Excel(name = "表注释名")
    private String comment;
     // 字符集CHARACTER SET
     @Excel(name = "字符集CHARACTER SET")
    private String characterset;
     // 排序规则COLLATE
     @Excel(name = "排序规则COLLATE")
    private String collation;
     // 版本号
     @Excel(name = "版本号")
    private String versionnumber;
     // 行号
     @Excel(name = "行号")
    private Integer rownum;
     // 备注
     @Excel(name = "备注")
    private String remark;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 租户名称
     @Excel(name = "租户名称")
    private String tenantname;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaDbdesignitemPojo> item;
    

}

