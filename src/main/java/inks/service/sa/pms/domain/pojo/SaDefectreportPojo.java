package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 缺陷报告(SaDefectreport)实体类
 *
 * <AUTHOR>
 * @since 2023-06-26 12:52:57
 */
public class SaDefectreportPojo implements Serializable {
    private static final long serialVersionUID = 343486794049042312L;
    @Excel(name = "")
    private String id;
    // 缺陷编码
    @Excel(name = "缺陷编码")
    private String refno;
    // 测试软件名称
    @Excel(name = "测试软件名称")
    private String softwarename;
    // 测试软件版本
    @Excel(name = "测试软件版本")
    private String softwareversion;
    // 缺陷发现日期
    @Excel(name = "缺陷发现日期")
    private Date discoverydate;
    // 测试人员
    @Excel(name = "测试人员")
    private String tester;
    // 缺陷描述
    @Excel(name = "缺陷描述")
    private String description;
    // 附件
    @Excel(name = "附件")
    private String attachment;
    // 缺陷类型
    @Excel(name = "缺陷类型")
    private String defecttype;
    // 缺陷严重程度
    @Excel(name = "缺陷严重程度")
    private String severity;
    // 缺陷优先级
    @Excel(name = "缺陷优先级")
    private String priority;
    // 测试环境
    @Excel(name = "测试环境")
    private String testenvironment;
    // 重现步骤
    @Excel(name = "重现步骤")
    private String reproductionsteps;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 缺陷编码
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 测试软件名称
    public String getSoftwarename() {
        return softwarename;
    }

    public void setSoftwarename(String softwarename) {
        this.softwarename = softwarename;
    }

    // 测试软件版本
    public String getSoftwareversion() {
        return softwareversion;
    }

    public void setSoftwareversion(String softwareversion) {
        this.softwareversion = softwareversion;
    }

    // 缺陷发现日期
    public Date getDiscoverydate() {
        return discoverydate;
    }

    public void setDiscoverydate(Date discoverydate) {
        this.discoverydate = discoverydate;
    }

    // 测试人员
    public String getTester() {
        return tester;
    }

    public void setTester(String tester) {
        this.tester = tester;
    }

    // 缺陷描述
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // 附件
    public String getAttachment() {
        return attachment;
    }

    public void setAttachment(String attachment) {
        this.attachment = attachment;
    }

    // 缺陷类型
    public String getDefecttype() {
        return defecttype;
    }

    public void setDefecttype(String defecttype) {
        this.defecttype = defecttype;
    }

    // 缺陷严重程度
    public String getSeverity() {
        return severity;
    }

    public void setSeverity(String severity) {
        this.severity = severity;
    }

    // 缺陷优先级
    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    // 测试环境
    public String getTestenvironment() {
        return testenvironment;
    }

    public void setTestenvironment(String testenvironment) {
        this.testenvironment = testenvironment;
    }

    // 重现步骤
    public String getReproductionsteps() {
        return reproductionsteps;
    }

    public void setReproductionsteps(String reproductionsteps) {
        this.reproductionsteps = reproductionsteps;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

