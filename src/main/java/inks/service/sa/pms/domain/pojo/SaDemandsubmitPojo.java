package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.WorkgroupPojo;
import lombok.Data;

/**
 * 需求提报(SaDemandsubmit)实体类
 *
 * <AUTHOR>
 * @since 2024-09-25 13:27:24
 */
@Data
public class SaDemandsubmitPojo extends WorkgroupPojo implements Serializable {
    private static final long serialVersionUID = 745561424583305762L;
     // ID
    @Excel(name = "ID") 
    private String id;
     // 类型(Bug/需求/客户反馈/运维反馈)
    @Excel(name = "类型(Bug/需求/客户反馈/运维反馈)") 
    private String type;
     // 内容描述
    @Excel(name = "内容描述") 
    private String description;
     // 优先级
    @Excel(name = "优先级") 
    private Integer level;
     // 进展阶段(需求提报/审批/需求处理)
    @Excel(name = "进展阶段(需求提报/审批/需求处理)") 
    private String status;
     // 责任人
    @Excel(name = "责任人") 
    private String assignee;
     // 执行/参与方
    @Excel(name = "执行/参与方") 
    private String participants;
     // 截止时间
    @Excel(name = "截止时间") 
    private Date deadlinedate;
     // 客户ID
    @Excel(name = "客户ID") 
    private String groupid;
     // 来源(PMS/RMS/客户/内部)
    @Excel(name = "来源(PMS/RMS/客户/内部)") 
    private String source;
     // 状态文本
    @Excel(name = "状态文本") 
    private String statetext;
     // 状态日期
    @Excel(name = "状态日期") 
    private Date statedate;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 正在进行OA
    @Excel(name = "正在进行OA")
    private Integer oaflowmark;
     // 反馈单Itemid
    @Excel(name = "反馈单Itemid")
    private String feedbackitemid;
     // 转需求单处理
    @Excel(name = "转需求单处理")
    private Integer demandmark;
     // 转测试用例
    @Excel(name = "转测试用例")
    private Integer testcasemark;
     // 转版本发布
    @Excel(name = "转版本发布")
    private Integer releasemark;
     // 审核员
    @Excel(name = "审核员") 
    private String assessor;
     // 审核员id
    @Excel(name = "审核员id") 
    private String assessorid;
     // 审核日期
    @Excel(name = "审核日期") 
    private Date assessdate;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

