package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 需求工时记录(SaDemandtime)实体类
 *
 * <AUTHOR>
 * @since 2025-01-02 12:54:45
 */
@Data
public class SaDemandtimePojo implements Serializable {
    private static final long serialVersionUID = 802795134156675021L;
     // id
    @Excel(name = "id") 
    private String id;
     // 需求id
    @Excel(name = "需求id") 
    private String demandid;
     // 状态：开始/暂停/完成
    @Excel(name = "状态：开始/暂停/完成") 
    private String status;
     // 开始时间
    @Excel(name = "开始时间") 
    private Date starttime;
     // 结束时间
    @Excel(name = "结束时间") 
    private Date endtime;
     // 工时（小时）
    @Excel(name = "工时（小时）") 
    private Double workhours;
     // 第几次开始
    @Excel(name = "第几次开始") 
    private Integer startcount;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 部门id
    @Excel(name = "部门id") 
    private String deptid;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

