package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 开发活动计划(SaDevelopmentplan)实体类
 *
 * <AUTHOR>
 * @since 2023-11-13 13:22:00
 */
public class SaDevelopmentplanPojo implements Serializable {
    private static final long serialVersionUID = -12236417154321098L;
    // id
    @Excel(name = "id")
    private String id;
    // 活动名称
    @Excel(name = "活动名称")
    private String name;
    // 负责人ID
    @Excel(name = "负责人ID")
    private String principalid;
    // 负责人姓名
    @Excel(name = "负责人姓名")
    private String principal;
    // 完成率，使用整数表示
    @Excel(name = "完成率，使用整数表示")
    private Integer completionrate;
    // 活动截止时间
    @Excel(name = "活动截止时间")
    private Date deaddate;
    // 是否中止
    @Excel(name = "是否中止")
    private Integer closed;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // 活动名称

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }
    // 负责人ID

    public String getPrincipalid() {
        return principalid;
    }

    public void setPrincipalid(String principalid) {
        this.principalid = principalid;
    }
    // 负责人姓名

    public String getPrincipal() {
        return principal;
    }

    public void setPrincipal(String principal) {
        this.principal = principal;
    }
    // 完成率，使用整数表示

    public Integer getCompletionrate() {
        return completionrate;
    }

    public void setCompletionrate(Integer completionrate) {
        this.completionrate = completionrate;
    }
    // 活动截止时间

    public Date getDeaddate() {
        return deaddate;
    }

    public void setDeaddate(Date deaddate) {
        this.deaddate = deaddate;
    }
    // 是否中止

    public Integer getClosed() {
        return closed;
    }

    public void setClosed(Integer closed) {
        this.closed = closed;
    }
    // 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
    // 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
    // 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
    // 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

