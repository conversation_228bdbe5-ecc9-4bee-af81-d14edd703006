package inks.service.sa.pms.domain.pojo;

import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 工程组成员子表(SaEngineergroupitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-07-15 12:58:10
 */
@Data
public class SaEngineergroupitemPojo implements Serializable {
    private static final long serialVersionUID = -40578538435033086L;
     // 主键ID
  @Excel(name = "主键ID")    
  private String id;
     // Pid
  @Excel(name = "Pid")    
  private String pid;
     // 工程师ID(关联Sa_Engineer.id)
  @Excel(name = "工程师ID(关联Sa_Engineer.id)")    
  private String engineerid;
     // 工程师姓名
  @Excel(name = "工程师姓名")    
  private String engineername;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 自定义字段1
  @Excel(name = "自定义字段1")    
  private String custom1;
     // 自定义字段2
  @Excel(name = "自定义字段2")    
  private String custom2;
     // 自定义字段3
  @Excel(name = "自定义字段3")    
  private String custom3;
     // 自定义字段4
  @Excel(name = "自定义字段4")    
  private String custom4;
     // 自定义字段5
  @Excel(name = "自定义字段5")    
  private String custom5;
     // 租户ID
  @Excel(name = "租户ID")    
  private String tenantid;
     // 乐观锁版本号
  @Excel(name = "乐观锁版本号")    
  private Integer revision;


}

