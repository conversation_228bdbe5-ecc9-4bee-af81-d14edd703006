package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 反馈单(SaFeedback)实体类
 *
 * <AUTHOR>
 * @since 2025-05-06 16:19:54
 */
@Data
public class SaFeedbackPojo implements Serializable {
    private static final long serialVersionUID = -36099000764916193L;
    // 唯一标识
    @Excel(name = "唯一标识")
    private String id;
    // 编码
    @Excel(name = "编码")
    private String refno;
    // 单据标题
    @Excel(name = "单据标题")
    private String billtitle;
    // 单据类型
    @Excel(name = "单据类型")
    private String billtype;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 项目ID
    @Excel(name = "项目ID")
    private String projectid;
    // 项目名
    @Excel(name = "项目名")
    private String projectname;
    // 客户id
    @Excel(name = "客户id")
    private String groupid;
    // 客户
    @Excel(name = "客户")
    private String customer;
    // 地点
    @Excel(name = "地点")
    private String location;
    // 人员
    @Excel(name = "人员")
    private String personnel;
    // 单据状态
    @Excel(name = "单据状态")
    private String status;
    // 状态时间
    @Excel(name = "状态时间")
    private Date statusdate;
    // 款数
    @Excel(name = "款数")
    private Integer itemcount;
    // 完成款数
    @Excel(name = "完成款数")
    private Integer finishcount;
    // 摘要
    @Excel(name = "摘要")
    private String summary;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaFeedbackitemPojo> item;


}

