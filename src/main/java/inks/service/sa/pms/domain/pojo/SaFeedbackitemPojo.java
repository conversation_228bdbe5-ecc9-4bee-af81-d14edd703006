package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 反馈单子表(SaFeedbackitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-02-17 13:59:52
 */
public class SaFeedbackitemPojo implements Serializable {
    private static final long serialVersionUID = 613527495291987178L;
    // 唯一标识
    @Excel(name = "唯一标识")
    private String id;
    // 主表关联ID
    @Excel(name = "主表关联ID")
    private String pid;
    // 类型
    @Excel(name = "类型")
    private String itemtype;
    // 故事
    @Excel(name = "故事")
    private String story;
    // 处理意见
    @Excel(name = "处理意见")
    private String opinion;
    // 指数
    @Excel(name = "指数")
    private Integer exponent;
    // 是否完成
    @Excel(name = "是否完成")
  private Integer finishmark;
     // 转需求提报
  @Excel(name = "转需求提报")
  private Integer submitmark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // 唯一标识

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // 主表关联ID

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
    // 类型

    public String getItemtype() {
        return itemtype;
    }

    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }
    // 故事

    public String getStory() {
        return story;
    }

    public void setStory(String story) {
        this.story = story;
    }
    // 处理意见

    public String getOpinion() {
        return opinion;
    }

    public void setOpinion(String opinion) {
        this.opinion = opinion;
    }
    // 指数

    public Integer getExponent() {
        return exponent;
    }

    public void setExponent(Integer exponent) {
        this.exponent = exponent;
    }
    // 是否完成

    public Integer getFinishmark() {
        return finishmark;
    }

    public void setFinishmark(Integer finishmark) {
        this.finishmark = finishmark;
    }

   // 转需求提报
    public Integer getSubmitmark() {
        return submitmark;
    }

    public void setSubmitmark(Integer submitmark) {
        this.submitmark = submitmark;
    }

    // 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

