package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 游客反馈(SaFeedbackvisitor)实体类
 *
 * <AUTHOR>
 * @since 2025-03-26 15:24:42
 */
@Data
public class SaFeedbackvisitorPojo implements Serializable {
    private static final long serialVersionUID = -36521470504839498L;
     // 唯一标识
    @Excel(name = "唯一标识") 
    private String id;
     // 客户名
    @Excel(name = "客户名") 
    private String custname;
     // 客户id
    @Excel(name = "客户id")
    private String groupid;
     // 联系电话
    @Excel(name = "联系电话") 
    private String phone;
     // 联系邮件
    @Excel(name = "联系邮件") 
    private String email;
     // 反馈标题
    @Excel(name = "反馈标题") 
    private String title;
     // 反馈问题
    @Excel(name = "反馈问题") 
    private String issue;
     // 附件
    @Excel(name = "附件") 
    private String attachment;
     // 图片url
    @Excel(name = "图片url") 
    private String photos;
     // 是否转出
    @Excel(name = "是否转出") 
    private Integer submitmark;
     // 是否解决
    @Excel(name = "是否解决") 
    private Integer finishmark;
     // 解决描述
    @Excel(name = "解决描述") 
    private String finishdesc;
     // 工程师
    @Excel(name = "工程师") 
    private String engineer;
     // 关闭时间
    @Excel(name = "关闭时间") 
    private Date closedate;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

