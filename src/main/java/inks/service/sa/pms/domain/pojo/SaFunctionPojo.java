package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 功能中心表(SaFunction)实体类
 *
 * <AUTHOR>
 * @since 2025-01-03 15:38:20
 */
@Data
public class SaFunctionPojo implements Serializable {
    private static final long serialVersionUID = -97702074381219313L;
     // ID
    @Excel(name = "ID") 
    private String id;
     // 项目id
    @Excel(name = "项目id") 
    private String projectid;
     // 项目名
    @Excel(name = "项目名")
    private String projname;
     // 功能编码
    @Excel(name = "功能编码") 
    private String functioncode;
     // 功能名称
    @Excel(name = "功能名称") 
    private String functionname;
     // 功能標題
    @Excel(name = "功能標題") 
    private String functiontitle;
     // 功能描述
    @Excel(name = "功能描述") 
    private String functiondesc;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

