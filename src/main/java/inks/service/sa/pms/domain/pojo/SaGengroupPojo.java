package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 代码生成器分组(SaGengroup)实体类
 *
 * <AUTHOR>
 * @since 2023-08-07 13:39:34
 */
public class SaGengroupPojo implements Serializable {
    private static final long serialVersionUID = 389130613858479241L;
    // 子节点
    List<SaGengroupPojo> children;
    // id
    @Excel(name = "id")
    private String id;
    // Parentid
    @Excel(name = "Parentid")
    private String parentid;
    // 分组名称
    @Excel(name = "分组名称")
    private String groupname;
    // 模板id
    @Excel(name = "模板id")
    private String velocityid;
    // 是否文件夹
    @Excel(name = "是否文件夹")
    private Integer dirmark;
    //文件上传后的url
    private String fileurl;
    // 有效性
    @Excel(name = "有效性")
    private Integer enabledmark;
    // 功能编码
    @Excel(name = "功能编码")
    private String modulecode;
    // 分组编码
    @Excel(name = "分组编码")
    private String groupcode;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    @Excel(name = "模板id的对应名称")
    private String velocityname;

    //用id,parentId,groupName,velocityId,dirMark构造方法 注意this.children = new ArrayList<>();避免空指针异常
    public SaGengroupPojo(String id, String parentId, String groupName, String velocityId, Integer dirMark) {
        this.id = id;
        this.parentid = parentId;
        this.groupname = groupName;
        this.velocityid = velocityId;
        this.dirmark = dirMark;
        this.fileurl = "";
//        this.children = new ArrayList<>();
    }


    // 无参构造方法
    public SaGengroupPojo() {
        this.children = new ArrayList<>();
    }

    // 全参构造方法
    public SaGengroupPojo(String id, String parentid, String groupname, String velocityid, Integer dirmark, String fileurl, Integer enabledmark, String modulecode, String groupcode, Integer rownum, String remark, String lister, Date createdate, Date modifydate, String tenantid) {
        this.id = id;
        this.parentid = parentid;
        this.groupname = groupname;
        this.velocityid = velocityid;
        this.dirmark = dirmark;
        this.fileurl = fileurl;
        this.enabledmark = enabledmark;
        this.modulecode = modulecode;
        this.groupcode = groupcode;
        this.rownum = rownum;
        this.remark = remark;
        this.lister = lister;
        this.createdate = createdate;
        this.modifydate = modifydate;
        this.tenantid = tenantid;
        this.children = new ArrayList<>();
    }

    // 生成指定个数的空格字符串
    private String generateIndent(int level) {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < level; i++) {
            sb.append("  "); // 两个空格为一级缩进
        }
        return sb.toString();
    }

    // toString 方法
    @Override
    public String toString() {
        return toString(0);
    }

    private String toString(int level) {
        StringBuilder sb = new StringBuilder();
        String indent = generateIndent(level);

        sb.append(indent).append("SaGengroupPojo{")
                .append("id='").append(id).append('\'')
                .append(", parentid='").append(parentid).append('\'')
                .append(", groupname='").append(groupname).append('\'')
                .append(", velocityid='").append(velocityid).append('\'')
                .append(", dirmark=").append(dirmark)
                .append(", fileurl='").append(fileurl).append('\'');

        if (children != null && !children.isEmpty()) {
            sb.append(", children=[\n");
            for (SaGengroupPojo child : children) {
                sb.append(child.toString(level + 1)).append(",\n");
            }
            sb.append(indent).append("]");
        } else {
            sb.append(", children=[]");
        }

        sb.append("}");
        return sb.toString();
    }

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getVelocityname() {
        return velocityname;
    }

    public void setVelocityname(String velocityname) {
        this.velocityname = velocityname;
    }

    // Parentid
    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    // 功能编码
    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }

    // 分组编码
    public String getGroupcode() {
        return groupcode;
    }

    public void setGroupcode(String groupcode) {
        this.groupcode = groupcode;
    }

    // 分组名称
    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }

    // 模板id
    public String getVelocityid() {
        return velocityid;
    }

    public void setVelocityid(String velocityid) {
        this.velocityid = velocityid;
    }

    // 是否文件夹
    public Integer getDirmark() {
        return dirmark;
    }

    public void setDirmark(Integer dirmark) {
        this.dirmark = dirmark;
    }

    public List<SaGengroupPojo> getChildren() {
        return children;
    }

    public void setChildren(List<SaGengroupPojo> children) {
        this.children = children;
    }

    public String getFileurl() {
        return fileurl;
    }

    public void setFileurl(String fileurl) {
        this.fileurl = fileurl;
    }

    // 有效性
    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }


}

