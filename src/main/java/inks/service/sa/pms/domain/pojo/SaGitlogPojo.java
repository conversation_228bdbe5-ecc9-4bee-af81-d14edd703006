package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.ToString;

/**
 * 代码提交日志(SaGitlog)实体类
 *
 * <AUTHOR>
 * @since 2024-06-24 10:46:40
 */
@ToString
public class SaGitlogPojo implements Serializable {
    private static final long serialVersionUID = -44887683426931786L;
     // ID
    @Excel(name = "ID") 
    private String id;
     // 事件名
    @Excel(name = "事件名") 
    private String eventname;
     // 登录名
    @Excel(name = "登录名") 
    private String username;
     // 项目名
    @Excel(name = "项目名") 
    private String projectname;
     // 项目地址
    @Excel(name = "项目地址") 
    private String projecturl;
     // 项目描述
    @Excel(name = "项目描述") 
    private String projectdesc;
     // 项目命名空间
    @Excel(name = "项目命名空间") 
    private String projectnamespace;
     // 提交commit信息
    @Excel(name = "提交commit信息") 
    private String commitmessage;
     // 提交时间
    @Excel(name = "提交时间") 
    private Date commitdate;
     // 提交人
    @Excel(name = "提交人") 
    private String commitauthor;
     // 提交人邮箱
    @Excel(name = "提交人邮箱") 
    private String commitemail;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;

   // ID
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 事件名
    public String getEventname() {
        return eventname;
    }
    
    public void setEventname(String eventname) {
        this.eventname = eventname;
    }
        
   // 登录名
    public String getUsername() {
        return username;
    }
    
    public void setUsername(String username) {
        this.username = username;
    }
        
   // 项目名
    public String getProjectname() {
        return projectname;
    }
    
    public void setProjectname(String projectname) {
        this.projectname = projectname;
    }
        
   // 项目地址
    public String getProjecturl() {
        return projecturl;
    }
    
    public void setProjecturl(String projecturl) {
        this.projecturl = projecturl;
    }
        
   // 项目描述
    public String getProjectdesc() {
        return projectdesc;
    }
    
    public void setProjectdesc(String projectdesc) {
        this.projectdesc = projectdesc;
    }
        
   // 项目命名空间
    public String getProjectnamespace() {
        return projectnamespace;
    }
    
    public void setProjectnamespace(String projectnamespace) {
        this.projectnamespace = projectnamespace;
    }
        
   // 提交commit信息
    public String getCommitmessage() {
        return commitmessage;
    }
    
    public void setCommitmessage(String commitmessage) {
        this.commitmessage = commitmessage;
    }
        
   // 提交时间
    public Date getCommitdate() {
        return commitdate;
    }
    
    public void setCommitdate(Date commitdate) {
        this.commitdate = commitdate;
    }
        
   // 提交人
    public String getCommitauthor() {
        return commitauthor;
    }
    
    public void setCommitauthor(String commitauthor) {
        this.commitauthor = commitauthor;
    }
        
   // 提交人邮箱
    public String getCommitemail() {
        return commitemail;
    }
    
    public void setCommitemail(String commitemail) {
        this.commitemail = commitemail;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 创建者
    public String getCreateby() {
        return createby;
    }
    
    public void setCreateby(String createby) {
        this.createby = createby;
    }
        
   // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }
    
    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
        
   // 新建日期
    public Date getCreatedate() {
        return createdate;
    }
    
    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
        
   // 制表
    public String getLister() {
        return lister;
    }
    
    public void setLister(String lister) {
        this.lister = lister;
    }
        
   // 制表id
    public String getListerid() {
        return listerid;
    }
    
    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
        
   // 修改日期
    public Date getModifydate() {
        return modifydate;
    }
    
    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
        
   // 租户id
    public String getTenantid() {
        return tenantid;
    }
    
    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
        
   // 租户名称
    public String getTenantname() {
        return tenantname;
    }
    
    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

