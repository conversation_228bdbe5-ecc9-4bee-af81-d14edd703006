package inks.service.sa.pms.domain.pojo;

import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

/**
 * 实施计划子表(SaImplementplanitem)Pojo
 *
 * <AUTHOR>
 * @since 2024-10-06 14:40:34
 */
public class SaImplementplanitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 420143762056481864L;
     // id
  @Excel(name = "id")    
  private String id;
     // 实施计划id
  @Excel(name = "实施计划id")    
  private String pid;
     // 类型
  @Excel(name = "类型")    
  private String itemtype;
     // 名称
  @Excel(name = "名称")    
  private String itemname;
     // 值
  @Excel(name = "值")    
  private String itemvalue;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;

   // id
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
        
   // 实施计划id
    public String getPid() {
        return pid;
    }
    
    public void setPid(String pid) {
        this.pid = pid;
    }
        
   // 类型
    public String getItemtype() {
        return itemtype;
    }
    
    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }
        
   // 名称
    public String getItemname() {
        return itemname;
    }
    
    public void setItemname(String itemname) {
        this.itemname = itemname;
    }
        
   // 值
    public String getItemvalue() {
        return itemvalue;
    }
    
    public void setItemvalue(String itemvalue) {
        this.itemvalue = itemvalue;
    }
        
   // 行号
    public Integer getRownum() {
        return rownum;
    }
    
    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
        
   // 备注
    public String getRemark() {
        return remark;
    }
    
    public void setRemark(String remark) {
        this.remark = remark;
    }
        
   // 自定义1
    public String getCustom1() {
        return custom1;
    }
    
    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
        
   // 自定义2
    public String getCustom2() {
        return custom2;
    }
    
    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
        
   // 自定义3
    public String getCustom3() {
        return custom3;
    }
    
    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
        
   // 自定义4
    public String getCustom4() {
        return custom4;
    }
    
    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
        
   // 自定义5
    public String getCustom5() {
        return custom5;
    }
    
    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
        
   // 乐观锁
    public Integer getRevision() {
        return revision;
    }
    
    public void setRevision(Integer revision) {
        this.revision = revision;
    }
        

}

