package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 服务派工(SaJoborder)实体类
 *
 * <AUTHOR>
 * @since 2025-05-06 16:18:17
 */
@Data
public class SaJoborderPojo implements Serializable {
    private static final long serialVersionUID = 983282017653558995L;
    // id
    @Excel(name = "id")
    private String id;
    // 编码
    @Excel(name = "编码")
    private String refno;
    // 单据类型
    @Excel(name = "单据类型")
    private String billtype;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 单据标题
    @Excel(name = "单据标题")
    private String billtitle;
    // 关联项目
    @Excel(name = "关联项目")
    private String projectid;
    // 产品编码
    @Excel(name = "产品编码")
    private String itemcode;
    // 产品名称
    @Excel(name = "产品名称")
    private String itemname;
    // 客户名称
    @Excel(name = "客户名称")
    private String groupname;
    // 客户id
    @Excel(name = "客户id")
    private String groupid;
    // 联系人
    @Excel(name = "联系人")
    private String linkman;
    // 联系电话
    @Excel(name = "联系电话")
    private String telephone;
    // 服务地址
    @Excel(name = "服务地址")
    private String seradd;
    // 服务日期
    @Excel(name = "服务日期")
    private Date serdate;
    // 类别
    @Excel(name = "类别")
    private String serclass;
    // 经办人
    @Excel(name = "经办人")
    private String operator;
    // 经办人id
    @Excel(name = "经办人id")
    private String operatorid;
    // 服务需求
    @Excel(name = "服务需求")
    private String sercontent;
    // 服务过程
    @Excel(name = "服务过程")
    private String serprocess;
    // 未尽事宜
    @Excel(name = "未尽事宜")
    private String serloss;
    // 机密信息
    @Excel(name = "机密信息")
    private String confidential;
    // 物料清单
    @Excel(name = "物料清单")
    private String matjson;
    // 通用分组(备用)
    @Excel(name = "通用分组(备用)")
    private String gengroupid;
    // 评价1
    @Excel(name = "评价1")
    private Integer score1;
    // 评价2
    @Excel(name = "评价2")
    private Integer score2;
    // 评价3
    @Excel(name = "评价3")
    private Integer score3;
    // 车牌号
    @Excel(name = "车牌号")
    private String licenseplate;
    // 里程数
    @Excel(name = "里程数")
    private Double mileage;
     // 附件
    @Excel(name = "附件")
    private String attachment;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
    // 分区编码
    @Excel(name = "分区编码")
    private String groupcode;
    // 作废
    @Excel(name = "作废")
    private Integer disannulmark;
    // 完成
    @Excel(name = "完成")
    private Integer finishmark;
    // item行数
    @Excel(name = "item行数")
    private Integer itemcount;
    // 打印次数
    @Excel(name = "打印次数")
    private Integer printcount;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;



}

