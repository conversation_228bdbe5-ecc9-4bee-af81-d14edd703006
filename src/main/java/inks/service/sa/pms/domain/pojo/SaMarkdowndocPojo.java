package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * MarkDown(Doc文档)(SaMarkdowndoc)实体类
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:26
 */
public class SaMarkdowndocPojo implements Serializable {
    private static final long serialVersionUID = 319233157050472768L;
    @Excel(name = "")
    private String id;
    // 关联MD分组
    @Excel(name = "关联MD分组")
    private String mdgroupid;
    // 编码
    @Excel(name = "编码")
    private String refno;
    // 类型
    @Excel(name = "类型")
    private String billtype;
    // 标题
    @Excel(name = "标题")
    private String billtitle;
    // 日期
    @Excel(name = "日期")
    private Date billdate;
    // MD内容简介
    @Excel(name = "MD内容简介")
    private String introduction;
    // minio地址
    @Excel(name = "minio地址")
    private String mdurl;
    // 点赞量
    @Excel(name = "点赞量")
    private Integer starcount;
    // 点踩量
    @Excel(name = "点踩量")
    private Integer mdngnum;
    // 浏览次数
    @Excel(name = "浏览次数")
    private Integer mdlooktimes;
    // 文本等级
    @Excel(name = "文本等级")
    private Integer mdlevel;
    // 封面图片
    @Excel(name = "封面图片")
    private String frontphoto;
    // 公共 共享
    @Excel(name = "公共 共享")
    private Integer publicmark;
    // 发布 0为草稿
    @Excel(name = "发布 0为草稿")
    private Integer releasemark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 文件大小
    @Excel(name = "文件大小")
    private Long filesize;
    // 文件格式
    @Excel(name = "文件格式")
    private String contenttype;
    // 存储方式
    @Excel(name = "存储方式")
    private String storage;
    // 关联id 单据id
    @Excel(name = "关联id 单据id")
    private String relateid;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 审核员id
    @Excel(name = "审核员id")
    private String assessorid;
    // 审核员
    @Excel(name = "审核员")
    private String assessor;
    // 审核日期
    @Excel(name = "审核日期")
    private Date assessdate;
    // 删除标识
    @Excel(name = "删除标识")
    private Integer deletemark;
    // 删除人员
    @Excel(name = "删除人员")
    private String deletelister;
    // 删除人员
    @Excel(name = "删除人员")
    private String deletelisterid;
    // 删除日期
    @Excel(name = "删除日期")
    private Date deletedate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 内容
    @Excel(name = "内容")
    private String content;

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // 关联MD分组

    public String getMdgroupid() {
        return mdgroupid;
    }

    public void setMdgroupid(String mdgroupid) {
        this.mdgroupid = mdgroupid;
    }
    // 编码

    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }
    // 类型

    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }
    // 标题

    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }
    // 日期

    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }
    // MD内容简介

    public String getIntroduction() {
        return introduction;
    }

    public void setIntroduction(String introduction) {
        this.introduction = introduction;
    }
    // minio地址

    public String getMdurl() {
        return mdurl;
    }

    public void setMdurl(String mdurl) {
        this.mdurl = mdurl;
    }
    // 点赞量

    public Integer getStarcount() {
        return starcount;
    }

    public void setStarcount(Integer starcount) {
        this.starcount = starcount;
    }
    // 点踩量

    public Integer getMdngnum() {
        return mdngnum;
    }

    public void setMdngnum(Integer mdngnum) {
        this.mdngnum = mdngnum;
    }
    // 浏览次数

    public Integer getMdlooktimes() {
        return mdlooktimes;
    }

    public void setMdlooktimes(Integer mdlooktimes) {
        this.mdlooktimes = mdlooktimes;
    }
    // 文本等级

    public Integer getMdlevel() {
        return mdlevel;
    }

    public void setMdlevel(Integer mdlevel) {
        this.mdlevel = mdlevel;
    }
    // 封面图片

    public String getFrontphoto() {
        return frontphoto;
    }

    public void setFrontphoto(String frontphoto) {
        this.frontphoto = frontphoto;
    }
    // 公共 共享

    public Integer getPublicmark() {
        return publicmark;
    }

    public void setPublicmark(Integer publicmark) {
        this.publicmark = publicmark;
    }
    // 发布 0为草稿

    public Integer getReleasemark() {
        return releasemark;
    }

    public void setReleasemark(Integer releasemark) {
        this.releasemark = releasemark;
    }
    // 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 文件大小

    public Long getFilesize() {
        return filesize;
    }

    public void setFilesize(Long filesize) {
        this.filesize = filesize;
    }
    // 文件格式

    public String getContenttype() {
        return contenttype;
    }

    public void setContenttype(String contenttype) {
        this.contenttype = contenttype;
    }
    // 存储方式

    public String getStorage() {
        return storage;
    }

    public void setStorage(String storage) {
        this.storage = storage;
    }
    // 关联id 单据id

    public String getRelateid() {
        return relateid;
    }

    public void setRelateid(String relateid) {
        this.relateid = relateid;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
    // 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
    // 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
    // 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 审核员id

    public String getAssessorid() {
        return assessorid;
    }

    public void setAssessorid(String assessorid) {
        this.assessorid = assessorid;
    }
    // 审核员

    public String getAssessor() {
        return assessor;
    }

    public void setAssessor(String assessor) {
        this.assessor = assessor;
    }
    // 审核日期

    public Date getAssessdate() {
        return assessdate;
    }

    public void setAssessdate(Date assessdate) {
        this.assessdate = assessdate;
    }
    // 删除标识

    public Integer getDeletemark() {
        return deletemark;
    }

    public void setDeletemark(Integer deletemark) {
        this.deletemark = deletemark;
    }
    // 删除人员

    public String getDeletelister() {
        return deletelister;
    }

    public void setDeletelister(String deletelister) {
        this.deletelister = deletelister;
    }
    // 删除人员

    public String getDeletelisterid() {
        return deletelisterid;
    }

    public void setDeletelisterid(String deletelisterid) {
        this.deletelisterid = deletelisterid;
    }
    // 删除日期

    public Date getDeletedate() {
        return deletedate;
    }

    public void setDeletedate(Date deletedate) {
        this.deletedate = deletedate;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 自定义6

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
    // 自定义7

    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
    // 自定义8

    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
    // 自定义9

    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
    // 自定义10

    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
    // 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

