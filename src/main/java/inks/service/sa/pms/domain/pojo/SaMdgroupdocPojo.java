package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * MarkDown分组(Doc文档)(SaMdgroupdoc)实体类
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:40
 */
public class SaMdgroupdocPojo implements Serializable {
    private static final long serialVersionUID = -14819538173549273L;
    // id
    @Excel(name = "id")
    private String id;
    // 关联Md项目
    @Excel(name = "关联Md项目")
    private String mdprojectid;
    // Parentid
    @Excel(name = "Parentid")
    private String parentid;
    // 功能编码
    @Excel(name = "功能编码")
    private String modulecode;
    // 分组编码
    @Excel(name = "分组编码")
    private String groupcode;
    // 分组名称
    @Excel(name = "分组名称")
    private String groupname;
    // 有效性
    @Excel(name = "有效性")
    private Integer enabledmark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;

    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // 关联Md项目

    public String getMdprojectid() {
        return mdprojectid;
    }

    public void setMdprojectid(String mdprojectid) {
        this.mdprojectid = mdprojectid;
    }
    // Parentid

    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }
    // 功能编码

    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
    // 分组编码

    public String getGroupcode() {
        return groupcode;
    }

    public void setGroupcode(String groupcode) {
        this.groupcode = groupcode;
    }
    // 分组名称

    public String getGroupname() {
        return groupname;
    }

    public void setGroupname(String groupname) {
        this.groupname = groupname;
    }
    // 有效性

    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
    // 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

}

