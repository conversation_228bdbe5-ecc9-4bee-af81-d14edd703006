package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 会议主表(SaMeeting)实体类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:52:46
 */
@Data
public class SaMeetingPojo implements Serializable {
    private static final long serialVersionUID = 828114369638503479L;
     // id
     @Excel(name = "id")
    private String id;
     // 会议标题
     @Excel(name = "会议标题")
    private String meetingtitle;
     // 会议纪要内容
     @Excel(name = "会议纪要内容")
    private String meetingdesc;
     // 状态(0:准备中,1:进行中,2:待审核,3:已审核,4:已驳回)
     @Excel(name = "状态(0:准备中,1:进行中,2:待审核,3:已审核,4:已驳回)")
    private Integer status;
     // 会议开始时间
     @Excel(name = "会议开始时间")
    private Date starttime;
     // 会议结束时间
     @Excel(name = "会议结束时间")
    private Date endtime;
     // 会议地点
     @Excel(name = "会议地点")
    private String location;
     // 参会人数
     @Excel(name = "参会人数")
    private Integer attendeecount;
     // 实际出席人数
     @Excel(name = "实际出席人数")
    private Integer actattendeecount;
     // 审核员
     @Excel(name = "审核员")
    private String assessor;
     // 审核员id
     @Excel(name = "审核员id")
    private String assessorid;
     // 审核日期
     @Excel(name = "审核日期")
    private Date assessdate;
     // 审核意见
     @Excel(name = "审核意见")
    private String assesscomment;
     // 款数
     @Excel(name = "款数")
    private Integer itemcount;
     // 行号
     @Excel(name = "行号")
    private Integer rownum;
     // 备注
     @Excel(name = "备注")
    private String remark;
     // 创建者
     @Excel(name = "创建者")
    private String createby;
     // 创建者id
     @Excel(name = "创建者id")
    private String createbyid;
     // 新建日期
     @Excel(name = "新建日期")
    private Date createdate;
     // 制表
     @Excel(name = "制表")
    private String lister;
     // 制表id
     @Excel(name = "制表id")
    private String listerid;
     // 修改日期
     @Excel(name = "修改日期")
    private Date modifydate;
     // 自定义1
     @Excel(name = "自定义1")
    private String custom1;
     // 自定义2
     @Excel(name = "自定义2")
    private String custom2;
     // 自定义3
     @Excel(name = "自定义3")
    private String custom3;
     // 自定义4
     @Excel(name = "自定义4")
    private String custom4;
     // 自定义5
     @Excel(name = "自定义5")
    private String custom5;
     // 租户id
     @Excel(name = "租户id")
    private String tenantid;
     // 乐观锁
     @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaMeetingitemPojo> item;
    // user子表
    private List<SaMeetinguserPojo> user;
    

}

