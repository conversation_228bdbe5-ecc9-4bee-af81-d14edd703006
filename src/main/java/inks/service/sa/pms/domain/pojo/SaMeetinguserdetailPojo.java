package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * 会议子表-人员(SaMeetinguser)Pojo
 *
 * <AUTHOR>
 * @since 2025-06-17 09:50:17
 */
@Data
public class SaMeetinguserdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -18826012577012290L;
     // id
  @Excel(name = "id")    
  private String id;
     // 会议id
  @Excel(name = "会议id")    
  private String pid;
     // 用户id
  @Excel(name = "用户id")    
  private String userid;
     // 用户姓名
  @Excel(name = "用户姓名")    
  private String username;
     // 用户部门
  @Excel(name = "用户部门")    
  private String userdept;
     // 用户职位
  @Excel(name = "用户职位")    
  private String userposition;
     // 参与类型(1:必须参加,2:可选参加,3:主持人,4:记录员)
  @Excel(name = "参与类型(1:必须参加,2:可选参加,3:主持人,4:记录员)")    
  private Integer participanttype;
     // 实际出席情况(0:缺席,1:出席,2:迟到,3:早退)
  @Excel(name = "实际出席情况(0:缺席,1:出席,2:迟到,3:早退)")    
  private Integer actualattendance;
     // 签到时间
  @Excel(name = "签到时间")    
  private Date attendancetime;
     // 离开时间
  @Excel(name = "离开时间")    
  private Date leavetime;
     // 联系电话
  @Excel(name = "联系电话")    
  private String phone;
     // 邮箱
  @Excel(name = "邮箱")    
  private String email;
     // 邀请状态(0:未发送,1:已发送,2:已确认,3:已拒绝)
  @Excel(name = "邀请状态(0:未发送,1:已发送,2:已确认,3:已拒绝)")    
  private Integer invitestatus;
     // 行号
  @Excel(name = "行号")    
  private Integer rownum;
     // 备注
  @Excel(name = "备注")    
  private String remark;
     // 自定义1
  @Excel(name = "自定义1")    
  private String custom1;
     // 自定义2
  @Excel(name = "自定义2")    
  private String custom2;
     // 自定义3
  @Excel(name = "自定义3")    
  private String custom3;
     // 自定义4
  @Excel(name = "自定义4")    
  private String custom4;
     // 自定义5
  @Excel(name = "自定义5")    
  private String custom5;
     // 租户id
  @Excel(name = "租户id")    
  private String tenantid;
     // 乐观锁
  @Excel(name = "乐观锁")    
  private Integer revision;



}

