package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;
import java.util.List;

/**
 * 工程项目(SaProject)实体类
 *
 * <AUTHOR>
 * @since 2023-09-26 14:30:59
 */
public class SaProjectPojo implements Serializable {
    private static final long serialVersionUID = 497704786718897943L;
    // id
    @Excel(name = "id")
    private String id;
    // 通用分组
    @Excel(name = "通用分组")
    private String gengroupid;
    // 项目编码
    @Excel(name = "项目编码")
    private String projcode;
    // 类型
    @Excel(name = "类型")
    private String projtype;
    // 名称
    @Excel(name = "名称")
    private String projname;
    // 规格
    @Excel(name = "规格")
    private String projspec;
    // 单位
    @Excel(name = "单位")
    private String projunit;
    // 拼音码
    @Excel(name = "拼音码")
    private String projpinyin;
    // 版本号
    @Excel(name = "版本号")
    private String versionnum;
    // 条形码
    @Excel(name = "条形码")
    private String barcode;
    // 经办人
    @Excel(name = "经办人")
    private String operator;
    // 有效标识
    @Excel(name = "有效标识")
    private Integer enabledmark;
    // 封面图
    @Excel(name = "封面图")
    private String coverimage;
    // 星标
    @Excel(name = "星标")
    private Integer starmark;
    // 行号
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 部门id
    @Excel(name = "部门id")
    private String deptid;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaProjectitemPojo> item;
    // 状态子表status
    private List<SaProjectstatusPojo> status;
    // 标签子表label
    private List<SaProjectlabelPojo> label;

    //自己待处理任务数(StatusType=开始)
    private Integer todocount;
    //自己已逾期任务数(StatusType!=已完成&&NOW() > DeadDate)
    private Integer overduecount;
    //sortstarmark (项目的starmark字段废弃了,现使用排序表的星标字段 Sa_ProjectSort.StarMark as sortstarmark)
    private Integer sortstarmark;


    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // 通用分组

    public String getGengroupid() {
        return gengroupid;
    }

    public void setGengroupid(String gengroupid) {
        this.gengroupid = gengroupid;
    }
    // 项目编码

    public Integer getSortstarmark() {
        return sortstarmark;
    }

    public void setSortstarmark(Integer sortstarmark) {
        this.sortstarmark = sortstarmark;
    }

    public String getProjcode() {
        return projcode;
    }

    public void setProjcode(String projcode) {
        this.projcode = projcode;
    }
    // 类型

    public String getProjtype() {
        return projtype;
    }

    public void setProjtype(String projtype) {
        this.projtype = projtype;
    }
    // 名称

    public String getProjname() {
        return projname;
    }

    public void setProjname(String projname) {
        this.projname = projname;
    }
    // 规格

    public String getProjspec() {
        return projspec;
    }

    public void setProjspec(String projspec) {
        this.projspec = projspec;
    }

    public Integer getOverduecount() {
        return overduecount;
    }

    public void setOverduecount(Integer overduecount) {
        this.overduecount = overduecount;
    }

    public Integer getTodocount() {
        return todocount;
    }

    public void setTodocount(Integer todocount) {
        this.todocount = todocount;
    }
    // 单位

    public String getProjunit() {
        return projunit;
    }

    public void setProjunit(String projunit) {
        this.projunit = projunit;
    }
    // 拼音码

    public String getProjpinyin() {
        return projpinyin;
    }

    public void setProjpinyin(String projpinyin) {
        this.projpinyin = projpinyin;
    }
    // 版本号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    public String getVersionnum() {
        return versionnum;
    }

    public void setVersionnum(String versionnum) {
        this.versionnum = versionnum;
    }
    // 条形码

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }
    // 经办人

    public String getOperator() {
        return operator;
    }

    public void setOperator(String operator) {
        this.operator = operator;
    }
    // 有效标识

    public Integer getEnabledmark() {
        return enabledmark;
    }

    public void setEnabledmark(Integer enabledmark) {
        this.enabledmark = enabledmark;
    }
    // 封面图

    public String getCoverimage() {
        return coverimage;
    }

    public void setCoverimage(String coverimage) {
        this.coverimage = coverimage;
    }
    // 星标

    public Integer getStarmark() {
        return starmark;
    }

    public void setStarmark(Integer starmark) {
        this.starmark = starmark;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 创建者

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }
    // 创建者id

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }
    // 新建日期

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }
    // 制表

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }
    // 制表id

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }
    // 修改日期

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 自定义6

    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }
    // 自定义7

    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }
    // 自定义8

    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }
    // 自定义9

    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }
    // 自定义10

    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }
    // 部门id

    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }
    // 租户id

    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }
    // 租户名称

    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

    public List<SaProjectitemPojo> getItem() {
        return item;
    }

    public void setItem(List<SaProjectitemPojo> item) {
        this.item = item;
    }

    public List<SaProjectstatusPojo> getStatus() {
        return status;
    }

    public void setStatus(List<SaProjectstatusPojo> status) {
        this.status = status;
    }

    public List<SaProjectlabelPojo> getLabel() {
        return label;
    }

    public void setLabel(List<SaProjectlabelPojo> label) {
        this.label = label;
    }
}

