package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * (SaReleaseitem)Pojo
 *
 * <AUTHOR>
 * @since 2023-04-14 10:05:55
 */
public class SaReleaseitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -51754791081572640L;
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 流水号
    @Excel(name = "流水号")
    private String refno;
    // 单据日期
    @Excel(name = "单据日期")
    private Date billdate;
    // 类型
    @Excel(name = "类型")
    private String billtype;
    // 标题
    @Excel(name = "标题")
    private String billtitle;
    // 关联Todoid
    @Excel(name = "关联Todoid")
    private String todoid;
    // 内容
    @Excel(name = "内容")
    private String tdcontent;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 完成
    @Excel(name = "完成")
    private Integer finishmark;
    // 关闭
    @Excel(name = "关闭")
    private Integer closed;
    // 重要的
    @Excel(name = "重要的")
    private Integer importantmark;
    // 紧急的
    @Excel(name = "紧急的")
    private Integer urgentmark;
    // 目标
    @Excel(name = "目标")
    private String targetjson;
    // 图片Url1
    @Excel(name = "图片Url1")
    private String photourl1;
    // 图片Url2
    @Excel(name = "图片Url2")
    private String photourl2;
    // 图片Url3
    @Excel(name = "图片Url3")
    private String photourl3;
    // 图片名称1(备用)
    @Excel(name = "图片名称1(备用)")
    private String photoname1;
    // 图片名称2(备用)
    @Excel(name = "图片名称2(备用)")
    private String photoname2;
    // 图片名称3(备用)
    @Excel(name = "图片名称3(备用)")
    private String photoname3;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
    // 接受人员id
    @Excel(name = "接受人员id")
    private String accepterid;
    // 接受人员
    @Excel(name = "接受人员")
    private String accepter;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 自定义6
    @Excel(name = "自定义6")
    private String custom6;
    // 自定义7
    @Excel(name = "自定义7")
    private String custom7;
    // 自定义8
    @Excel(name = "自定义8")
    private String custom8;
    // 自定义9
    @Excel(name = "自定义9")
    private String custom9;
    // 自定义10
    @Excel(name = "自定义10")
    private String custom10;
    // 租户id
    @Excel(name = "租户id")
    private String tenantid;
    // 部门id
    @Excel(name = "部门id")
    private String deptid;
    // 租户名称
    @Excel(name = "租户名称")
    private String tenantname;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 发布名称
    @Excel(name = "发布名称")
    private String releasename;
    // 发布日期
    @Excel(name = "发布日期")
    private Date releasedate;
    // 版本
    @Excel(name = "版本")
    private String version;

    // id
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getReleasename() {
        return releasename;
    }

    public void setReleasename(String releasename) {
        this.releasename = releasename;
    }

    public Date getReleasedate() {
        return releasedate;
    }

    public void setReleasedate(Date releasedate) {
        this.releasedate = releasedate;
    }

    public String getVersion() {
        return version;
    }

    public void setVersion(String version) {
        this.version = version;
    }

    // Pid
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 流水号
    public String getRefno() {
        return refno;
    }

    public void setRefno(String refno) {
        this.refno = refno;
    }

    // 单据日期
    public Date getBilldate() {
        return billdate;
    }

    public void setBilldate(Date billdate) {
        this.billdate = billdate;
    }

    // 类型
    public String getBilltype() {
        return billtype;
    }

    public void setBilltype(String billtype) {
        this.billtype = billtype;
    }

    // 标题
    public String getBilltitle() {
        return billtitle;
    }

    public void setBilltitle(String billtitle) {
        this.billtitle = billtitle;
    }

    // 关联Todoid
    public String getTodoid() {
        return todoid;
    }

    public void setTodoid(String todoid) {
        this.todoid = todoid;
    }

    // 内容
    public String getTdcontent() {
        return tdcontent;
    }

    public void setTdcontent(String tdcontent) {
        this.tdcontent = tdcontent;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 完成
    public Integer getFinishmark() {
        return finishmark;
    }

    public void setFinishmark(Integer finishmark) {
        this.finishmark = finishmark;
    }

    // 关闭
    public Integer getClosed() {
        return closed;
    }

    public void setClosed(Integer closed) {
        this.closed = closed;
    }

    // 重要的
    public Integer getImportantmark() {
        return importantmark;
    }

    public void setImportantmark(Integer importantmark) {
        this.importantmark = importantmark;
    }

    // 紧急的
    public Integer getUrgentmark() {
        return urgentmark;
    }

    public void setUrgentmark(Integer urgentmark) {
        this.urgentmark = urgentmark;
    }

    // 目标
    public String getTargetjson() {
        return targetjson;
    }

    public void setTargetjson(String targetjson) {
        this.targetjson = targetjson;
    }

    // 图片Url1
    public String getPhotourl1() {
        return photourl1;
    }

    public void setPhotourl1(String photourl1) {
        this.photourl1 = photourl1;
    }

    // 图片Url2
    public String getPhotourl2() {
        return photourl2;
    }

    public void setPhotourl2(String photourl2) {
        this.photourl2 = photourl2;
    }

    // 图片Url3
    public String getPhotourl3() {
        return photourl3;
    }

    public void setPhotourl3(String photourl3) {
        this.photourl3 = photourl3;
    }

    // 图片名称1(备用)
    public String getPhotoname1() {
        return photoname1;
    }

    public void setPhotoname1(String photoname1) {
        this.photoname1 = photoname1;
    }

    // 图片名称2(备用)
    public String getPhotoname2() {
        return photoname2;
    }

    public void setPhotoname2(String photoname2) {
        this.photoname2 = photoname2;
    }

    // 图片名称3(备用)
    public String getPhotoname3() {
        return photoname3;
    }

    public void setPhotoname3(String photoname3) {
        this.photoname3 = photoname3;
    }

    // 创建者id
    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    // 创建者
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 新建日期
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 制表
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 修改日期
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 接受人员id
    public String getAccepterid() {
        return accepterid;
    }

    public void setAccepterid(String accepterid) {
        this.accepterid = accepterid;
    }

    // 接受人员
    public String getAccepter() {
        return accepter;
    }

    public void setAccepter(String accepter) {
        this.accepter = accepter;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 自定义6
    public String getCustom6() {
        return custom6;
    }

    public void setCustom6(String custom6) {
        this.custom6 = custom6;
    }

    // 自定义7
    public String getCustom7() {
        return custom7;
    }

    public void setCustom7(String custom7) {
        this.custom7 = custom7;
    }

    // 自定义8
    public String getCustom8() {
        return custom8;
    }

    public void setCustom8(String custom8) {
        this.custom8 = custom8;
    }

    // 自定义9
    public String getCustom9() {
        return custom9;
    }

    public void setCustom9(String custom9) {
        this.custom9 = custom9;
    }

    // 自定义10
    public String getCustom10() {
        return custom10;
    }

    public void setCustom10(String custom10) {
        this.custom10 = custom10;
    }

    // 租户id
    public String getTenantid() {
        return tenantid;
    }

    public void setTenantid(String tenantid) {
        this.tenantid = tenantid;
    }

    // 部门id
    public String getDeptid() {
        return deptid;
    }

    public void setDeptid(String deptid) {
        this.deptid = deptid;
    }

    // 租户名称
    public String getTenantname() {
        return tenantname;
    }

    public void setTenantname(String tenantname) {
        this.tenantname = tenantname;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

