package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * Todo(SaTodo)实体类
 *
 * <AUTHOR>
 * @since 2025-01-02 14:45:35
 */
@Data
public class SaTodoPojo implements Serializable {
    private static final long serialVersionUID = 731222140904062447L;
     // id
    @Excel(name = "id") 
    private String id;
     // 分组编码
    @Excel(name = "分组编码") 
    private String tdgroupid;
     // 父级id(暂时作为需求单id)
    @Excel(name = "父级id(暂时作为需求单id)") 
    private String parentid;
     // 流水号
    @Excel(name = "流水号") 
    private String refno;
     // 单据日期
    @Excel(name = "单据日期") 
    private Date billdate;
     // 类型
    @Excel(name = "类型") 
    private String billtype;
     // 标题
    @Excel(name = "标题") 
    private String billtitle;
     // 内容
    @Excel(name = "内容") 
    private String tdcontent;
     // 完成
    @Excel(name = "完成") 
    private Integer finishmark;
     // 优先级
    @Excel(name = "优先级") 
    private Integer level;
     // 产品id
    @Excel(name = "产品id") 
    private String goodsid;
     // 行号
    @Excel(name = "行号") 
    private Integer rownum;
     // 计划时间
    @Excel(name = "计划时间") 
    private Date plandate;
     // 来源
    @Excel(name = "来源") 
    private String source;
     // 产品名
    @Excel(name = "产品名") 
    private String productname;
     // 客户名
    @Excel(name = "客户名") 
    private String groupname;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 关闭
    @Excel(name = "关闭") 
    private Integer closed;
     // 公共
    @Excel(name = "公共") 
    private Integer publicmark;
     // 重要的
    @Excel(name = "重要的") 
    private Integer importantmark;
     // 紧急的
    @Excel(name = "紧急的") 
    private Integer urgentmark;
     // 目标
    @Excel(name = "目标") 
    private String targetjson;
     // 单据状态
    @Excel(name = "单据状态") 
    private String statecode;
     // 状态日期
    @Excel(name = "状态日期") 
    private Date statedate;
     // 图片Url1
    @Excel(name = "图片Url1") 
    private String photourl1;
     // 图片Url2
    @Excel(name = "图片Url2") 
    private String photourl2;
     // 图片Url3
    @Excel(name = "图片Url3") 
    private String photourl3;
     // 图片名称1(备用)
    @Excel(name = "图片名称1(备用)") 
    private String photoname1;
     // 图片名称2(备用)
    @Excel(name = "图片名称2(备用)") 
    private String photoname2;
     // 图片名称3(备用)
    @Excel(name = "图片名称3(备用)") 
    private String photoname3;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 附属功能
    @Excel(name = "附属功能") 
    private String modulecode;
     // 单据编码
    @Excel(name = "单据编码") 
    private String citeuid;
     // 单据id
    @Excel(name = "单据id") 
    private Integer citeid;
     // 接受人员id
    @Excel(name = "接受人员id") 
    private String accepterid;
     // 接受人员
    @Excel(name = "接受人员") 
    private String accepter;
     // 计划开始
    @Excel(name = "计划开始") 
    private Date startplan;
     // 计划工时
    @Excel(name = "计划工时") 
    private Integer planhours;
     // 完成工时
    @Excel(name = "完成工时") 
    private Integer finishhours;
     // 计划完成
    @Excel(name = "计划完成") 
    private Date endplan;
     // 实际开始
    @Excel(name = "实际开始") 
    private Date startactual;
     // 开始备注
    @Excel(name = "开始备注") 
    private String startremark;
     // 实际完成
    @Excel(name = "实际完成") 
    private Date endactual;
     // 完工备注
    @Excel(name = "完工备注") 
    private String endremark;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 部门id
    @Excel(name = "部门id") 
    private String deptid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

