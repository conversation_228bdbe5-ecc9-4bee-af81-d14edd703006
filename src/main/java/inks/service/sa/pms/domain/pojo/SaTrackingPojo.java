package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import java.util.List;
import lombok.Data;

/**
 * 跟踪表主表(SaTracking)实体类
 *
 * <AUTHOR>
 * @since 2025-02-10 17:11:56
 */
@Data
public class SaTrackingPojo implements Serializable {
    private static final long serialVersionUID = 202103561509811648L;
    // 主键
    @Excel(name = "主键")
    private String id;
    // 类型: 上线计划表、上线实施表、开发计划表、测试计划表等
    @Excel(name = "类型: 上线计划表、上线实施表、开发计划表、测试计划表等")
    private String type;
    // 客户
    @Excel(name = "客户")
    private String customer;
    // 工程项目
    @Excel(name = "工程项目")
    private String project;
    // 任务总数
    @Excel(name = "任务总数")
    private Integer itemcount;
    // 已完成任务数
    @Excel(name = "已完成任务数")
    private Integer finishcount;
    // 主题，用于记录软件著作权申请
    @Excel(name = "主题，用于记录软件著作权申请")
    private String subject;
    // 计划日期
    @Excel(name = "计划日期")
    private Date billplandate;
    // item最大完工时间
    @Excel(name = "item最大完工时间")
    private Date billfinishdate;
    // 已排程数
    @Excel(name = "已排程数")
    private Integer planfinishcount;
    // 已完工数
    @Excel(name = "已完工数")
    private Integer billfinishcount;
    // 是否已关闭
    @Excel(name = "是否已关闭")
    private Integer closed;
    // 公共标志
    @Excel(name = "公共标志")
    private Integer publicmark;
    // 经办人
    @Excel(name = "经办人")
    private String operator;
    // 经办人ID
    @Excel(name = "经办人ID")
    private String operatorid;
    // 协助人员ID
    @Excel(name = "协助人员ID")
    private String assistantids;
    // 协助人员名称
    @Excel(name = "协助人员名称")
    private String assistantnames;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;
     // 作废
     @Excel(name = "作废")
    private Integer disannulcount;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;
    // 子表
    private List<SaTrackingitemPojo> item;


}

