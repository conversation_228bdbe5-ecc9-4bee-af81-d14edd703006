package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 跟踪表子表(SaTrackingitem)Pojo
 *
 * <AUTHOR>
 * @since 2025-03-14 15:09:42
 */
@Data
public class SaTrackingitemPojo implements Serializable {
    private static final long serialVersionUID = -48470428318549202L;
    // 主键
    @Excel(name = "主键")
    private String id;
    // 关联的跟踪表ID
    @Excel(name = "关联的跟踪表ID")
    private String pid;
    // 阶段名
    @Excel(name = "阶段名")
    private String phasename;
    // 任务名
    @Excel(name = "任务名")
    private String taskname;
    // 描述
    @Excel(name = "描述")
    private String description;
    // 计划开始时间
    @Excel(name = "计划开始时间")
    private Date planstart;
    // 计划结束时间
    @Excel(name = "计划结束时间")
    private Date planend;
    // 里程碑
    @Excel(name = "里程碑")
    private String milestone;
    // 实际开始时间
    @Excel(name = "实际开始时间")
    private Date actualstart;
    // 完工时间
    @Excel(name = "完工时间")
    private Date completiondate;
    // 产出成果
    @Excel(name = "产出成果")
    private String outputresult;
    // 主负责人
    @Excel(name = "主负责人")
    private String mainresponsibleperson;
    // 次负责人
    @Excel(name = "次负责人")
  private String subresponsibleperson;
     // 关闭
  @Excel(name = "关闭")
  private Integer closed;
     // 作废
  @Excel(name = "作废")
  private Integer disannulmark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // 主键
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 关联的跟踪表ID
    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }

    // 阶段名
    public String getPhasename() {
        return phasename;
    }

    public void setPhasename(String phasename) {
        this.phasename = phasename;
    }

    // 任务名
    public String getTaskname() {
        return taskname;
    }

    public void setTaskname(String taskname) {
        this.taskname = taskname;
    }

    // 描述
    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    // 计划开始时间
    public Date getPlanstart() {
        return planstart;
    }

    public void setPlanstart(Date planstart) {
        this.planstart = planstart;
    }

    // 计划结束时间
    public Date getPlanend() {
        return planend;
    }

    public void setPlanend(Date planend) {
        this.planend = planend;
    }

    // 里程碑
    public String getMilestone() {
        return milestone;
    }

    public void setMilestone(String milestone) {
        this.milestone = milestone;
    }

    // 实际开始时间
    public Date getActualstart() {
        return actualstart;
    }

    public void setActualstart(Date actualstart) {
        this.actualstart = actualstart;
    }

    // 完工时间
    public Date getCompletiondate() {
        return completiondate;
    }

    public void setCompletiondate(Date completiondate) {
        this.completiondate = completiondate;
    }

    // 产出成果
    public String getOutputresult() {
        return outputresult;
    }

    public void setOutputresult(String outputresult) {
        this.outputresult = outputresult;
    }

    // 主负责人
    public String getMainresponsibleperson() {
        return mainresponsibleperson;
    }

    public void setMainresponsibleperson(String mainresponsibleperson) {
        this.mainresponsibleperson = mainresponsibleperson;
    }

    // 次负责人
    public String getSubresponsibleperson() {
        return subresponsibleperson;
    }

    public void setSubresponsibleperson(String subresponsibleperson) {
        this.subresponsibleperson = subresponsibleperson;
    }

    // 行号
    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 自定义1
    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }

    // 自定义2
    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }

    // 自定义3
    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }

    // 自定义4
    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }

    // 自定义5
    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }

    // 乐观锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

