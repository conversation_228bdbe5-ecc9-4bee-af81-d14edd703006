package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;
import lombok.Data;

/**
 * 跟踪表子表(SaTrackingitem)Pojo
 *
 * <AUTHOR>
 * @since 2023-06-25 14:45:29
 */
@Data
public class SaTrackingitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = 778395519428644205L;
    // 主键
    @Excel(name = "主键")
    private String id;
    // 关联的跟踪表ID
    @Excel(name = "关联的跟踪表ID")
    private String pid;
    // 阶段名
    @Excel(name = "阶段名")
    private String phasename;
    // 任务名
    @Excel(name = "任务名")
    private String taskname;
    // 描述
    @Excel(name = "描述")
    private String description;
    // 计划开始时间
    @Excel(name = "计划开始时间")
    private Date planstart;
    // 计划结束时间
    @Excel(name = "计划结束时间")
    private Date planend;
    // 里程碑
    @Excel(name = "里程碑")
    private String milestone;
    // 实际开始时间
    @Excel(name = "实际开始时间")
    private Date actualstart;
    // 完工时间
    @Excel(name = "完工时间")
    private Date completiondate;
    // 产出成果
    @Excel(name = "产出成果")
    private String outputresult;
    // 主负责人
    @Excel(name = "主负责人")
    private String mainresponsibleperson;
    // 次负责人
    @Excel(name = "次负责人")
  private String subresponsibleperson;
     // 关闭
  @Excel(name = "关闭")
  private Integer closed;
     // 作废
  @Excel(name = "作废")
  private Integer disannulmark;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;



}

