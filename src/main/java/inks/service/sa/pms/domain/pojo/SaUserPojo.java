package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户(SaUser)实体类
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:13
 */
public class SaUserPojo implements Serializable {
    private static final long serialVersionUID = -63143474945674799L;
    // ID
    @Excel(name = "ID")
    private String id;
    //微信唯一标识
    @Excel(name = "微信唯一标识")
    private String wxopenid;
    // 用户名
    @Excel(name = "用户名")
    private String username;
    // 真实姓名
    @Excel(name = "真实姓名")
    private String realname;
    // 密码
    private String password;
    // 手机号
    @Excel(name = "手机号")
    private String phone;
    // 电子邮箱
    @Excel(name = "电子邮箱")
    private String email;
    // 阿里邮箱授权码
    private String emailauthcode;
    // 钉钉Userid
    @Excel(name = "钉钉Userid")
    private String dinguserid;
     // GitLabToken
    @Excel(name = "GitLabToken")
    private String gitlabtoken;
     // GitLabUserName
    @Excel(name = "GitLabUserName")
    private String gitlabusername;
    // 性别
    @Excel(name = "性别")
    private Integer sex;
    // 邮箱
    @Excel(name = "邮箱")
    private String avatar;
    // 用户状态
    @Excel(name = "用户状态")
    private Integer userstate;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 创建人
    @Excel(name = "创建人")
    private String createby;

    // 创建时间

    @Excel(name = "创建时间")
    private Date createdate;
    // 制表人
    @Excel(name = "制表人")
    private String lister;
    // 修改时间
    @Excel(name = "修改时间")
    private Date modifydate;
    // 锁
    @Excel(name = "锁")
    private Integer revision;
    // 目录名
    private String dirname;
    // minio文件名
    private String filename;
    // 0新注册1技术员
    private Integer roletype;
    // 是否管理员
    private Integer adminmark;
    //用户-客户关联表id
    private String usergroupid;
    // 用户-项目关联表id
    private String userprojectid;

    public Integer getRoletype() {
        return roletype;
    }

    public void setRoletype(Integer roletype) {
        this.roletype = roletype;
    }

    public String getUserprojectid() {
        return userprojectid;
    }

    public void setUserprojectid(String userprojectid) {
        this.userprojectid = userprojectid;
    }

    public Integer getAdminmark() {
        return adminmark;
    }

    public void setAdminmark(Integer adminmark) {
        this.adminmark = adminmark;
    }

    public String getUsergroupid() {
        return usergroupid;
    }

    public void setUsergroupid(String usergroupid) {
        this.usergroupid = usergroupid;
    }

    public String getDinguserid() {
        return dinguserid;
    }

    public void setDinguserid(String dinguserid) {
        this.dinguserid = dinguserid;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getDirname() {
        return dirname;
    }

    public void setDirname(String dirname) {
        this.dirname = dirname;
    }

    public String getEmailauthcode() {
        return emailauthcode;
    }

    public void setEmailauthcode(String emailauthcode) {
        this.emailauthcode = emailauthcode;
    }

    public String getFilename() {
        return filename;
    }

    public void setFilename(String filename) {
        this.filename = filename;
    }

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getWxopenid() {
        return wxopenid;
    }

    public void setWxopenid(String wxopenid) {
        this.wxopenid = wxopenid;
    }

    // 用户名
    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    // 真实姓名
    public String getRealname() {
        return realname;
    }

    public void setRealname(String realname) {
        this.realname = realname;
    }

    // 手机号
    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone;
    }

    // 电子邮箱
    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }
    // GitLabToken
    public String getGitlabtoken() {
        return gitlabtoken;
    }

    public void setGitlabtoken(String gitlabtoken) {
        this.gitlabtoken = gitlabtoken;
    }

   // GitLabUserName
    public String getGitlabusername() {
        return gitlabusername;
    }

    public void setGitlabusername(String gitlabusername) {
        this.gitlabusername = gitlabusername;
    }

    // 性别
    public Integer getSex() {
        return sex;
    }

    public void setSex(Integer sex) {
        this.sex = sex;
    }

    // 邮箱
    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    // 用户状态
    public Integer getUserstate() {
        return userstate;
    }

    public void setUserstate(Integer userstate) {
        this.userstate = userstate;
    }

    // 备注
    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    // 创建人
    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    // 创建时间
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 制表人
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 修改时间
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }

    // 锁
    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }


}

