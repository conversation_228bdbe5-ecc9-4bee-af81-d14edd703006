package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;
import java.util.Date;

/**
 * 用户登录(SaUserlogin)实体类
 *
 * <AUTHOR>
 * @since 2022-11-24 15:42:20
 */
public class SaUserloginPojo implements Serializable {
    private static final long serialVersionUID = 411568526506070155L;
    // ID
    @Excel(name = "ID")
    private String id;
    // 用户id
    @Excel(name = "用户id")
    private String userid;
    // 登录密码
    @Excel(name = "登录密码")
    private String userpassword;
    // 制表人id
    @Excel(name = "制表人id")
    private String listerid;
    // 制表人
    @Excel(name = "制表人")
    private String lister;
    // 创建时间
    @Excel(name = "创建时间")
    private Date createdate;
    // 修改时间
    @Excel(name = "修改时间")
    private Date modifydate;

    // ID
    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    // 用户id
    public String getUserid() {
        return userid;
    }

    public void setUserid(String userid) {
        this.userid = userid;
    }

    // 登录密码
    public String getUserpassword() {
        return userpassword;
    }

    public void setUserpassword(String userpassword) {
        this.userpassword = userpassword;
    }

    // 制表人id
    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    // 制表人
    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    // 创建时间
    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    // 修改时间
    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }


}

