package inks.service.sa.pms.domain.pojo;

import java.util.Date;
import java.io.Serializable;
import cn.afterturn.easypoi.excel.annotation.Excel;
import lombok.Data;

/**
 * 工作周报(SaWeekly)实体类
 *
 * <AUTHOR>
 * @since 2025-07-21 15:06:44
 */
@Data
public class SaWeeklyPojo implements Serializable {
    private static final long serialVersionUID = 512167518657438515L;
     // ID
    @Excel(name = "ID") 
    private String id;
     // 编码
    @Excel(name = "编码") 
    private String refno;
     // 单据标题
    @Excel(name = "单据标题") 
    private String billtitle;
     // 单据类型
    @Excel(name = "单据类型") 
    private String billtype;
     // 单据日期
    @Excel(name = "单据日期") 
    private Date billdate;
     // 年、第几周
    @Excel(name = "年、第几周") 
    private String billcode;
     // 部门id
    @Excel(name = "部门id") 
    private String deptid;
     // 部门
    @Excel(name = "部门") 
    private String deptname;
     // 汇报人
    @Excel(name = "汇报人") 
    private String reporter;
     // 汇报人id
    @Excel(name = "汇报人id") 
    private String reporterid;
     // 状态文本
    @Excel(name = "状态文本") 
    private String statetext;
     // 状态日期
    @Excel(name = "状态日期") 
    private Date statedate;
     // 完成工作
    @Excel(name = "完成工作") 
    private String compjson;
     // 工作记录
    @Excel(name = "工作记录") 
    private String recordjson;
     // 工作计划
    @Excel(name = "工作计划") 
    private String planjson;
     // 问题与解决
    @Excel(name = "问题与解决") 
    private String issuesjson;
     // 排序码
    @Excel(name = "排序码") 
    private Integer rownum;
     // 备注
    @Excel(name = "备注") 
    private String remark;
     // 审核员id
    @Excel(name = "审核员id") 
    private String assessorid;
     // 审核员
    @Excel(name = "审核员") 
    private String assessor;
     // 审核日期
    @Excel(name = "审核日期") 
    private Date assessdate;
     // 创建者
    @Excel(name = "创建者") 
    private String createby;
     // 创建者id
    @Excel(name = "创建者id") 
    private String createbyid;
     // 新建日期
    @Excel(name = "新建日期") 
    private Date createdate;
     // 制表
    @Excel(name = "制表") 
    private String lister;
     // 制表id
    @Excel(name = "制表id") 
    private String listerid;
     // 修改日期
    @Excel(name = "修改日期") 
    private Date modifydate;
     // 自定义1
    @Excel(name = "自定义1") 
    private String custom1;
     // 自定义2
    @Excel(name = "自定义2") 
    private String custom2;
     // 自定义3
    @Excel(name = "自定义3") 
    private String custom3;
     // 自定义4
    @Excel(name = "自定义4") 
    private String custom4;
     // 自定义5
    @Excel(name = "自定义5") 
    private String custom5;
     // 自定义6
    @Excel(name = "自定义6") 
    private String custom6;
     // 自定义7
    @Excel(name = "自定义7") 
    private String custom7;
     // 自定义8
    @Excel(name = "自定义8") 
    private String custom8;
     // 自定义9
    @Excel(name = "自定义9") 
    private String custom9;
     // 自定义10
    @Excel(name = "自定义10") 
    private String custom10;
     // 租户id
    @Excel(name = "租户id") 
    private String tenantid;
     // 租户名称
    @Excel(name = "租户名称") 
    private String tenantname;
     // 乐观锁
    @Excel(name = "乐观锁") 
    private Integer revision;



}

