package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;
import inks.common.core.domain.DatailPojo;

import java.io.Serializable;
import java.util.Date;

/**
 * 工作日志今日子表(SaWorklogitem)Pojo
 *
 * <AUTHOR>
 * @since 2023-09-04 12:58:20
 */
public class SaWorklogitemdetailPojo extends DatailPojo implements Serializable {
    private static final long serialVersionUID = -61880109342588489L;
    //------------------------------item子表 今日完成工作日志--------------------------------
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 类型:开发/测试/手册/维护/研发
    @Excel(name = "类型:开发/测试/手册/维护/研发")
    private String itemtype;
    // 项目id
    @Excel(name = "项目id")
    private String projectid;
    // 工时(单位0.5H)
    @Excel(name = "工时(单位0.5H)")
    private Double worktime;
    // 描述
    @Excel(name = "描述")
    private String itemdesc;
    // 功能编码
    @Excel(name = "功能编码")
    private String modulecode;
    // 工作完成比例
    @Excel(name = "工作完成比例")
    private Integer workcomprate;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // ---------------------------------------------------主表--------------------------
    @Excel(name = "时间")
    private Date workdate;
    // 天气
    @Excel(name = "天气")
    private String weather;
    // 今日工作事项
    @Excel(name = "今日工作事项")
    private String worktoday;
    // 明日工作事项
    @Excel(name = "明日工作事项")
    private String worktomorrow;
    // 邮件标题
    @Excel(name = "邮件标题")
    private String title;
    // 邮件发送次数
    @Excel(name = "邮件发送次数")
    private Integer sendemailnum;
    // 收件人邮箱
    @Excel(name = "收件人邮箱")
    private String toemail;
    // 抄送人邮箱
    @Excel(name = "抄送人邮箱")
    private String otheremails;
    // 创建者
    @Excel(name = "创建者")
    private String createby;
    // 创建者id
    @Excel(name = "创建者id")
    private String createbyid;
    // 新建日期
    @Excel(name = "新建日期")
    private Date createdate;
    // 制表
    @Excel(name = "制表")
    private String lister;
    // 制表id
    @Excel(name = "制表id")
    private String listerid;
    // 修改日期
    @Excel(name = "修改日期")
    private Date modifydate;

    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // Pid

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
    // 类型:开发/测试/手册/维护/研发

    public String getItemtype() {
        return itemtype;
    }

    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }
    // 项目id

    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }
    // 工时(单位0.5H)

    public Double getWorktime() {
        return worktime;
    }

    public void setWorktime(Double worktime) {
        this.worktime = worktime;
    }
    // 描述

    public String getItemdesc() {
        return itemdesc;
    }

    public void setItemdesc(String itemdesc) {
        this.itemdesc = itemdesc;
    }
    // 功能编码

    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
    // 工作完成比例

    public Integer getWorkcomprate() {
        return workcomprate;
    }

    public void setWorkcomprate(Integer workcomprate) {
        this.workcomprate = workcomprate;
    }
    // 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    public Date getWorkdate() {
        return workdate;
    }

    public void setWorkdate(Date workdate) {
        this.workdate = workdate;
    }

    public String getWeather() {
        return weather;
    }

    public void setWeather(String weather) {
        this.weather = weather;
    }

    public String getWorktoday() {
        return worktoday;
    }

    public void setWorktoday(String worktoday) {
        this.worktoday = worktoday;
    }

    public String getWorktomorrow() {
        return worktomorrow;
    }

    public void setWorktomorrow(String worktomorrow) {
        this.worktomorrow = worktomorrow;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public Integer getSendemailnum() {
        return sendemailnum;
    }

    public void setSendemailnum(Integer sendemailnum) {
        this.sendemailnum = sendemailnum;
    }

    public String getToemail() {
        return toemail;
    }

    public void setToemail(String toemail) {
        this.toemail = toemail;
    }

    public String getOtheremails() {
        return otheremails;
    }

    public void setOtheremails(String otheremails) {
        this.otheremails = otheremails;
    }

    public String getCreateby() {
        return createby;
    }

    public void setCreateby(String createby) {
        this.createby = createby;
    }

    public String getCreatebyid() {
        return createbyid;
    }

    public void setCreatebyid(String createbyid) {
        this.createbyid = createbyid;
    }

    public Date getCreatedate() {
        return createdate;
    }

    public void setCreatedate(Date createdate) {
        this.createdate = createdate;
    }

    public String getLister() {
        return lister;
    }

    public void setLister(String lister) {
        this.lister = lister;
    }

    public String getListerid() {
        return listerid;
    }

    public void setListerid(String listerid) {
        this.listerid = listerid;
    }

    public Date getModifydate() {
        return modifydate;
    }

    public void setModifydate(Date modifydate) {
        this.modifydate = modifydate;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

