package inks.service.sa.pms.domain.pojo;

import cn.afterturn.easypoi.excel.annotation.Excel;

import java.io.Serializable;

/**
 * 工作日志计划子表(SaWorklogplan)实体类
 *
 * <AUTHOR>
 * @since 2023-09-04 12:58:57
 */
public class SaWorklogplanPojo implements Serializable {
    private static final long serialVersionUID = -93022831109491189L;
    // id
    @Excel(name = "id")
    private String id;
    // Pid
    @Excel(name = "Pid")
    private String pid;
    // 类型:开发/测试/手册/维护/研发
    @Excel(name = "类型:开发/测试/手册/维护/研发")
    private String itemtype;
    // 项目id
    @Excel(name = "项目id")
    private String projectid;
    // 项目id
    @Excel(name = "项目名(项目id关联查的视图字段)")
    private String projname;
    // 预估工时(单位0.5H)
    @Excel(name = "预估工时(单位0.5H)")
    private Double worktimeexpect;
    // 描述
    @Excel(name = "描述")
    private String itemdesc;
    // 功能编码
    @Excel(name = "功能编码")
    private String modulecode;
    // 工作完成比例
    @Excel(name = "工作完成比例")
    private Integer workcomprate;
    // 行号
    @Excel(name = "行号")
    private Integer rownum;
    // 备注
    @Excel(name = "备注")
    private String remark;
    // 自定义1
    @Excel(name = "自定义1")
    private String custom1;
    // 自定义2
    @Excel(name = "自定义2")
    private String custom2;
    // 自定义3
    @Excel(name = "自定义3")
    private String custom3;
    // 自定义4
    @Excel(name = "自定义4")
    private String custom4;
    // 自定义5
    @Excel(name = "自定义5")
    private String custom5;
    // 乐观锁
    @Excel(name = "乐观锁")
    private Integer revision;

    // id

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }
    // Pid

    public String getPid() {
        return pid;
    }

    public void setPid(String pid) {
        this.pid = pid;
    }
    // 类型:开发/测试/手册/维护/研发

    public String getItemtype() {
        return itemtype;
    }

    public void setItemtype(String itemtype) {
        this.itemtype = itemtype;
    }
    // 项目id

    public String getProjectid() {
        return projectid;
    }

    public void setProjectid(String projectid) {
        this.projectid = projectid;
    }
    // 预估工时(单位0.5H)

    public Double getWorktimeexpect() {
        return worktimeexpect;
    }

    public void setWorktimeexpect(Double worktimeexpect) {
        this.worktimeexpect = worktimeexpect;
    }
    // 描述

    public String getItemdesc() {
        return itemdesc;
    }

    public void setItemdesc(String itemdesc) {
        this.itemdesc = itemdesc;
    }
    // 功能编码

    public String getModulecode() {
        return modulecode;
    }

    public void setModulecode(String modulecode) {
        this.modulecode = modulecode;
    }
    // 工作完成比例

    public Integer getWorkcomprate() {
        return workcomprate;
    }

    public void setWorkcomprate(Integer workcomprate) {
        this.workcomprate = workcomprate;
    }
    // 行号

    public Integer getRownum() {
        return rownum;
    }

    public void setRownum(Integer rownum) {
        this.rownum = rownum;
    }
    // 备注

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }
    // 自定义1

    public String getCustom1() {
        return custom1;
    }

    public void setCustom1(String custom1) {
        this.custom1 = custom1;
    }
    // 自定义2

    public String getCustom2() {
        return custom2;
    }

    public void setCustom2(String custom2) {
        this.custom2 = custom2;
    }
    // 自定义3

    public String getCustom3() {
        return custom3;
    }

    public void setCustom3(String custom3) {
        this.custom3 = custom3;
    }
    // 自定义4

    public String getCustom4() {
        return custom4;
    }

    public void setCustom4(String custom4) {
        this.custom4 = custom4;
    }
    // 自定义5

    public String getProjname() {
        return projname;
    }

    public void setProjname(String projname) {
        this.projname = projname;
    }

    public String getCustom5() {
        return custom5;
    }

    public void setCustom5(String custom5) {
        this.custom5 = custom5;
    }
    // 乐观锁

    public Integer getRevision() {
        return revision;
    }

    public void setRevision(Integer revision) {
        this.revision = revision;
    }

}

