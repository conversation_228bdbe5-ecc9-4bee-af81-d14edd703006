package inks.service.sa.pms.domain.vo;


import lombok.Builder;
import lombok.Data;

@Data
@Builder
public class EmailReceiverVO {
    private String subject; // 邮件主题
    private String sender; // 发件人
    private String recipients; // 接收人
    private String sentDate; // 发送日期
    private String receivedDate; // 接收日期
    private String ccRecipients; // 抄送人
    private String bccRecipients; // 密送人
    private String replyTo; // 回复给
    private String content; // 邮件内容
    private int size; // 邮件大小
    private String flags; // 邮件标记



}