package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaLoginlogEntity;
import inks.service.sa.pms.domain.pojo.SaLoginlogPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 登录日志(SaLoginlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-20 08:47:33
 */
@Mapper
public interface Pms_SaLoginlogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaLoginlogPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaLoginlogPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saLoginlogEntity 实例对象
     * @return 影响行数
     */
    int insert(SaLoginlogEntity saLoginlogEntity);


    /**
     * 修改数据
     *
     * @param saLoginlogEntity 实例对象
     * @return 影响行数
     */
    int update(SaLoginlogEntity saLoginlogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

