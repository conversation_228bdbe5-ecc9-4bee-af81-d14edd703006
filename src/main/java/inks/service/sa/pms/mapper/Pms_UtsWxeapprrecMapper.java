package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.UtsWxeapprrecEntity;
import inks.service.sa.pms.domain.pojo.UtsWxeapprrecPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 微信审批记录(UtsWxeapprrec)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:19
 */
@Mapper
public interface Pms_UtsWxeapprrecMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxeapprrecPojo getEntity(@Param("key") String key, @Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsWxeapprrecPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param utsWxeapprrecEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsWxeapprrecEntity utsWxeapprrecEntity);

    
    /**
     * 修改数据
     *
     * @param utsWxeapprrecEntity 实例对象
     * @return 影响行数
     */
    int update(UtsWxeapprrecEntity utsWxeapprrecEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxeapprrecPojo getEntityBySpno(@Param("key") String key, @Param("tid") String tid);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<UtsWxeapprrecPojo> getOnlineByBillid(@Param("key") String key, @Param("tid") String tid);
}

