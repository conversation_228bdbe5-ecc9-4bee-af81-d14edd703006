package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaAcceptanceEntity;
import inks.service.sa.pms.domain.pojo.SaAcceptancePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaAcceptance)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-09 16:11:12
 */
@Mapper
public interface SaAcceptanceMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaAcceptancePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaAcceptancePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saAcceptanceEntity 实例对象
     * @return 影响行数
     */
    int insert(SaAcceptanceEntity saAcceptanceEntity);


    /**
     * 修改数据
     *
     * @param saAcceptanceEntity 实例对象
     * @return 影响行数
     */
    int update(SaAcceptanceEntity saAcceptanceEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

