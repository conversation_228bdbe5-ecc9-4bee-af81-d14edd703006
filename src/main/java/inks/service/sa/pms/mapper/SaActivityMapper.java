package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivityPojo;
import inks.service.sa.pms.domain.pojo.SaActivityitemdetailPojo;
import inks.service.sa.pms.domain.SaActivityEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 活动主表(SaActivity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:09
 */
@Mapper
public interface SaActivityMapper {

    SaActivityPojo getEntity(@Param("key") String key);

    List<SaActivityitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaActivityPojo> getPageTh(QueryParam queryParam);

    int insert(SaActivityEntity saActivityEntity);

    int update(SaActivityEntity saActivityEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaActivityPojo saActivityPojo);

    int approval(SaActivityEntity saActivityEntity);
}

