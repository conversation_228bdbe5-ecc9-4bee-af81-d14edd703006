package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivityitemPojo;
import inks.service.sa.pms.domain.SaActivityitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 活动子表(SaActivityitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-13 17:03:53
 */
 @Mapper
public interface SaActivityitemMapper {

    SaActivityitemPojo getEntity(@Param("key") String key);

    List<SaActivityitemPojo> getPageList(QueryParam queryParam);

    List<SaActivityitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaActivityitemEntity saActivityitemEntity);

    int update(SaActivityitemEntity saActivityitemEntity);

    int delete(@Param("key") String key);

    void syncFinishByid(String actiitemid, Integer finishmark,Date date);
}

