package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivityplanPojo;
import inks.service.sa.pms.domain.SaActivityplanEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 活动计划/日志(SaActivityplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-21 17:27:54
 */
 @Mapper
public interface SaActivityplanMapper {

    SaActivityplanPojo getEntity(@Param("key") String key);

    List<SaActivityplanPojo> getPageList(QueryParam queryParam);

    List<SaActivityplanPojo> getList(@Param("Pid") String Pid);    

    int insert(SaActivityplanEntity saActivityplanEntity);

    int update(SaActivityplanEntity saActivityplanEntity);

    int delete(@Param("key") String key);

}

