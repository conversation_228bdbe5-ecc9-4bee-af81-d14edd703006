package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivitystagePojo;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemdetailPojo;
import inks.service.sa.pms.domain.SaActivitystageEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 活动阶段表(SaActivitystage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:13
 */
@Mapper
public interface SaActivitystageMapper {

    SaActivitystagePojo getEntity(@Param("key") String key);

    List<SaActivitystageitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaActivitystagePojo> getPageTh(QueryParam queryParam);

    int insert(SaActivitystageEntity saActivitystageEntity);

    int update(SaActivitystageEntity saActivitystageEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaActivitystagePojo saActivitystagePojo);
}

