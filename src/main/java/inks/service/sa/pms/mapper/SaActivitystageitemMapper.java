package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemPojo;
import inks.service.sa.pms.domain.SaActivitystageitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 活动阶段子表(SaActivitystageitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:21
 */
 @Mapper
public interface SaActivitystageitemMapper {

    SaActivitystageitemPojo getEntity(@Param("key") String key);

    List<SaActivitystageitemPojo> getPageList(QueryParam queryParam);

    List<SaActivitystageitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaActivitystageitemEntity saActivitystageitemEntity);

    int update(SaActivitystageitemEntity saActivitystageitemEntity);

    int delete(@Param("key") String key);

}

