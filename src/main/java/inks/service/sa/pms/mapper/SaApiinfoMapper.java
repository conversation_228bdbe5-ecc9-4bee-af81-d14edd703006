package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaApiinfoPojo;
import inks.service.sa.pms.domain.SaApiinfoEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 接口信息表(SaApiinfo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-15 17:09:56
 */
@Mapper
public interface SaApiinfoMapper {


    SaApiinfoPojo getEntity(@Param("key") String key);

    List<SaApiinfoPojo> getPageList(QueryParam queryParam);

    int insert(SaApiinfoEntity saApiinfoEntity);

    int update(SaApiinfoEntity saApiinfoEntity);

    int delete(@Param("key") String key);

    int insertBatch(List<SaApiinfoPojo> saApiinfoPojos);

}

