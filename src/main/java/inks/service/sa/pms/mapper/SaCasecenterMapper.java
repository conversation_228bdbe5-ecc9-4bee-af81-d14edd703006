package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaCasecenterEntity;
import inks.service.sa.pms.domain.pojo.SaCasecenterPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 案例中心(SaCasecenter)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-03-11 10:48:12
 */
@Mapper
public interface SaCasecenterMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaCasecenterPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaCasecenterPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saCasecenterEntity 实例对象
     * @return 影响行数
     */
    int insert(SaCasecenterEntity saCasecenterEntity);


    /**
     * 修改数据
     *
     * @param saCasecenterEntity 实例对象
     * @return 影响行数
     */
    int update(SaCasecenterEntity saCasecenterEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

