package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaChatterEntity;
import inks.service.sa.pms.domain.pojo.SaChatterPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * PMS客服(SaChatter)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-21 13:22:05
 */
@Mapper
public interface SaChatterMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaChatterPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaChatterPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saChatterEntity 实例对象
     * @return 影响行数
     */
    int insert(SaChatterEntity saChatterEntity);


    /**
     * 修改数据
     *
     * @param saChatterEntity 实例对象
     * @return 影响行数
     */
    int update(SaChatterEntity saChatterEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    int isChatter(String userid);
}

