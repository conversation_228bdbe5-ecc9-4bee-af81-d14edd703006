package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaChatuserEntity;
import inks.service.sa.pms.domain.pojo.SaChatuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 对话用户(SaChatuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-12-21 15:01:54
 */
@Mapper
public interface SaChatuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaChatuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaChatuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saChatuserEntity 实例对象
     * @return 影响行数
     */
    int insert(SaChatuserEntity saChatuserEntity);


    /**
     * 修改数据
     *
     * @param saChatuserEntity 实例对象
     * @return 影响行数
     */
    int update(SaChatuserEntity saChatuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    int checkUserId(String userid);

    SaChatuserPojo getEntityByUserId(String userid);

    String getIdByUserId(String userid);
}

