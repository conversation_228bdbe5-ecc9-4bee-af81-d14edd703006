package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaCustrmsuserPojo;
import inks.service.sa.pms.domain.SaCustrmsuserEntity;
import inks.service.sa.pms.domain.pojo.SaCustrmsuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 客户RMS关系表(SaCustrmsuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-29 11:04:27
 */
@Mapper
public interface SaCustrmsuserMapper {


    SaCustrmsuserPojo getEntity(@Param("key") String key);

    List<SaCustrmsuserPojo> getPageList(QueryParam queryParam);

    int insert(SaCustrmsuserEntity saCustrmsuserEntity);

    int update(SaCustrmsuserEntity saCustrmsuserEntity);

    int delete(@Param("key") String key);

    SaCustrmsuserPojo getEntityByUserid(@Param("key") String key);

    List<SaCustrmsuserPojo> getListByUserid(@Param("userid") String userid);
}

