package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaCustscmuserEntity;
import inks.service.sa.pms.domain.pojo.SaCustscmuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 客户SCM关系表(SaCustscmuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:30
 */
@Mapper
public interface SaCustscmuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaCustscmuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaCustscmuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saCustscmuserEntity 实例对象
     * @return 影响行数
     */
    int insert(SaCustscmuserEntity saCustscmuserEntity);


    /**
     * 修改数据
     *
     * @param saCustscmuserEntity 实例对象
     * @return 影响行数
     */
    int update(SaCustscmuserEntity saCustscmuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    SaCustscmuserPojo getEntityByUserid(@Param("key") String key);

    List<SaCustscmuserPojo> getListByUserid(@Param("userid") String userid);
}

