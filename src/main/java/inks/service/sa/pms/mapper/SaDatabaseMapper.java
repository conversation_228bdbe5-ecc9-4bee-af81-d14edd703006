package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaDatabaseEntity;
import inks.service.sa.pms.domain.pojo.SaDatabasePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * 数据库连接池(SaDatabase)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-04-05 15:24:15
 */
@Mapper
public interface SaDatabaseMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDatabasePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDatabasePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDatabaseEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDatabaseEntity saDatabaseEntity);


    /**
     * 修改数据
     *
     * @param saDatabaseEntity 实例对象
     * @return 影响行数
     */
    int update(SaDatabaseEntity saDatabaseEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    @Select("select Url from Sa_Database where id = #{databaseid}")
    String getDatabaseUrl(String databaseid);
}

