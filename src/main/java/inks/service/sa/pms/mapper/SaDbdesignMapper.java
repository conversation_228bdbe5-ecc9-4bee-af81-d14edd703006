package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDbdesignPojo;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemdetailPojo;
import inks.service.sa.pms.domain.SaDbdesignEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 表格设计(SaDbdesign)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-15 09:54:21
 */
@Mapper
public interface SaDbdesignMapper {

    SaDbdesignPojo getEntity(@Param("key") String key);

    List<SaDbdesignitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaDbdesignPojo> getPageTh(QueryParam queryParam);

    int insert(SaDbdesignEntity saDbdesignEntity);

    int update(SaDbdesignEntity saDbdesignEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaDbdesignPojo saDbdesignPojo);
}

