package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo;
import inks.service.sa.pms.domain.SaDbdesignitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 表格设计字段子表(SaDbdesignitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-15 09:54:35
 */
 @Mapper
public interface SaDbdesignitemMapper {

    SaDbdesignitemPojo getEntity(@Param("key") String key);

    List<SaDbdesignitemPojo> getPageList(QueryParam queryParam);

    List<SaDbdesignitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaDbdesignitemEntity saDbdesignitemEntity);

    int update(SaDbdesignitemEntity saDbdesignitemEntity);

    int delete(@Param("key") String key);

    String getPid(String itemid);
}

