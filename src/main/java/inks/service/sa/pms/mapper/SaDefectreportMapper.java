package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaDefectreportEntity;
import inks.service.sa.pms.domain.pojo.SaDefectreportPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 缺陷报告(SaDefectreport)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-26 12:52:57
 */
@Mapper
public interface SaDefectreportMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDefectreportPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDefectreportPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDefectreportEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDefectreportEntity saDefectreportEntity);


    /**
     * 修改数据
     *
     * @param saDefectreportEntity 实例对象
     * @return 影响行数
     */
    int update(SaDefectreportEntity saDefectreportEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

