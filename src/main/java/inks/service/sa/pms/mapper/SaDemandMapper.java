package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaDemandEntity;
import inks.service.sa.pms.domain.pojo.SaDemandPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 产品需求(SaDemand)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-24 16:43:02
 */
@Mapper
public interface SaDemandMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDemandPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDemandPojo> getPageList(@Param("queryParam")QueryParam queryParam);
    List<SaDemandPojo> getAllPageList(@Param("queryParam")QueryParam queryParam);
    List<SaDemandPojo> getPageListUNIONALL(@Param("queryParam") QueryParam queryParam, @Param("finishlimit") Integer finishlimit);


    /**
     * 新增数据
     *
     * @param saDemandEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDemandEntity saDemandEntity);


    /**
     * 修改数据
     *
     * @param saDemandEntity 实例对象
     * @return 影响行数
     */
    int update(SaDemandEntity saDemandEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);


    int allCountInTime(@Param("startdate") Date startdate, @Param("enddate") Date enddate);

    int finishCountInTime(@Param("startdate") Date startdate, @Param("enddate") Date enddate);

    int unFinishCountInTime(@Param("startdate") Date startdate, @Param("enddate") Date enddate);

    double workTimeInTime(@Param("startdate") Date startdate, @Param("enddate") Date enddate);


    int getOnlineCount();


    void updateStatusModifyDateAndFinishMark(@Param("id") String id, @Param("date") Date date, @Param("finishMark") int finishMark);

    void cleanDeadDate(String id);

    void cleanStartdate(String id);

    Map<String, BigDecimal> getCountTodoAndOverdue(@Param("id") String id, @Param("projectFilter") String projectFilter);

    List<SaDemandPojo> getListByParentid(@Param("key") String key);

    String getTitle(String id);

    String getAppointeeid(String id);

    List<SaDemandPojo> getTodoPageList(@Param("queryParam") QueryParam queryParam);

    List<Map<String, Object>> getCalendarPageList(@Param("queryParam") QueryParam queryParam);

    List<HashMap<String, Object>> getFinishDemandQtyGroupByUser(@Param("queryParam") QueryParam queryParam);

    double toBeDealtCount();

    int onlineCount();

    int checkTitleExist(@Param("commitTitle") String commitTitle);

    int checkTitleWithRemark(String commitTitle, String remark);

    List<Map<String,Object>> getOnlineStatus();

    Integer getFinishMark(String id);

    void syncSubmitDemandMark(String demandsubmitid, int demandmark);

    Integer newCountToday();

    void syncWorkTimeByDemandTime(String demandid,String timestatus);

    List<Map<String, Integer>> getCountByBillType(String qpfilter);
}

