package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandlabelPojo;
import inks.service.sa.pms.domain.SaDemandlabelEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 需求标签(SaDemandlabel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-26 13:02:27
 */
@Mapper
public interface SaDemandlabelMapper {


    SaDemandlabelPojo getEntity(@Param("key") String key);

    List<SaDemandlabelPojo> getPageList(QueryParam queryParam);

    int insert(SaDemandlabelEntity saDemandlabelEntity);

    int update(SaDemandlabelEntity saDemandlabelEntity);

    int delete(@Param("key") String key);
    
}

