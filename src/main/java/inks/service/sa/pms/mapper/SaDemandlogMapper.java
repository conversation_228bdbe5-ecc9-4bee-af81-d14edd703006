package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaDemandlogEntity;
import inks.service.sa.pms.domain.pojo.SaDemandlogPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品需求日志(SaDemandlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-26 15:00:38
 */
@Mapper
public interface SaDemandlogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDemandlogPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDemandlogPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDemandlogEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDemandlogEntity saDemandlogEntity);


    /**
     * 修改数据
     *
     * @param saDemandlogEntity 实例对象
     * @return 影响行数
     */
    int update(SaDemandlogEntity saDemandlogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

