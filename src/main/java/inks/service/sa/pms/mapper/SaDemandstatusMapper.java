package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandstatusPojo;
import inks.service.sa.pms.domain.SaDemandstatusEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 需求状态(SaDemandstatus)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-24 15:21:21
 */
@Mapper
public interface SaDemandstatusMapper {


    SaDemandstatusPojo getEntity(@Param("key") String key);

    List<SaDemandstatusPojo> getPageList(QueryParam queryParam);

    int insert(SaDemandstatusEntity saDemandstatusEntity);

    int update(SaDemandstatusEntity saDemandstatusEntity);

    int delete(@Param("key") String key);
    
}

