package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandsubmitPojo;
import inks.service.sa.pms.domain.SaDemandsubmitEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 需求提报(SaDemandsubmit)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-25 13:27:24
 */
@Mapper
public interface SaDemandsubmitMapper {


    SaDemandsubmitPojo getEntity(@Param("key") String key);

    List<SaDemandsubmitPojo> getPageList(QueryParam queryParam);

    int insert(SaDemandsubmitEntity saDemandsubmitEntity);

    int update(SaDemandsubmitEntity saDemandsubmitEntity);

    int delete(@Param("key") String key);
    

    int approval(SaDemandsubmitEntity saDemandsubmitEntity);

    void updateOaflowmark(SaDemandsubmitPojo saDemandsubmitPojo);

    void syncFeedbackItemSubmitMark(String feedbackitemid, int submitmark);

    void syncFeedbackFinishCount(String feedbackitemid);

    void syncFeedbackVisItemSubmitMark(String feedbackitemid, int submitmark);

}

