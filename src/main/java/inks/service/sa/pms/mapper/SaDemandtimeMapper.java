package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandtimePojo;
import inks.service.sa.pms.domain.SaDemandtimeEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 需求工时记录(SaDemandtime)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-02 11:09:21
 */
@Mapper
public interface SaDemandtimeMapper {


    SaDemandtimePojo getEntity(@Param("key") String key);

    List<SaDemandtimePojo> getPageList(QueryParam queryParam);

    int insert(SaDemandtimeEntity saDemandtimeEntity);

    int update(SaDemandtimeEntity saDemandtimeEntity);

    int delete(@Param("key") String key);

    int getCountByDemandid(String demandid);

    SaDemandtimePojo getLatestEntityByDemandid(String demandid);

    int getCountStatus(String demandid,String status);
}

