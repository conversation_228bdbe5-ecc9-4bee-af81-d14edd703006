package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaDevelopmentplanEntity;
import inks.service.sa.pms.domain.pojo.SaDevelopmentplanPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 开发活动计划(SaDevelopmentplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-13 13:22:01
 */
@Mapper
public interface SaDevelopmentplanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDevelopmentplanPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDevelopmentplanPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDevelopmentplanEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDevelopmentplanEntity saDevelopmentplanEntity);


    /**
     * 修改数据
     *
     * @param saDevelopmentplanEntity 实例对象
     * @return 影响行数
     */
    int update(SaDevelopmentplanEntity saDevelopmentplanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

