package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaDocEntity;
import inks.service.sa.pms.domain.pojo.SaDocPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档表(SaDoc)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
@Mapper
public interface SaDocMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDocPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDocPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDocEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDocEntity saDocEntity);


    /**
     * 修改数据
     *
     * @param saDocEntity 实例对象
     * @return 影响行数
     */
    int update(SaDocEntity saDocEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

