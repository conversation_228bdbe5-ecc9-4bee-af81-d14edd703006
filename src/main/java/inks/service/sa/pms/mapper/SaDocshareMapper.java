package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaDocshareEntity;
import inks.service.sa.pms.domain.pojo.SaDocsharePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档分享(SaDocshare)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
@Mapper
public interface SaDocshareMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDocsharePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDocsharePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDocshareEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDocshareEntity saDocshareEntity);


    /**
     * 修改数据
     *
     * @param saDocshareEntity 实例对象
     * @return 影响行数
     */
    int update(SaDocshareEntity saDocshareEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

