package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaDocuserEntity;
import inks.service.sa.pms.domain.pojo.SaDocuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 文档-(SaDocuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-24 16:15:01
 */
@Mapper
public interface SaDocuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDocuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaDocuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDocuserEntity 实例对象
     * @return 影响行数
     */
    int insert(SaDocuserEntity saDocuserEntity);


    /**
     * 修改数据
     *
     * @param saDocuserEntity 实例对象
     * @return 影响行数
     */
    int update(SaDocuserEntity saDocuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

