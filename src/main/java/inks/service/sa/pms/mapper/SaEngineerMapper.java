package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaEngineerEntity;
import inks.service.sa.pms.domain.pojo.SaEngineerPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 处理节点(SaEngineer)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-29 16:04:10
 */
@Mapper
public interface SaEngineerMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaEngineerPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaEngineerPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saEngineerEntity 实例对象
     * @return 影响行数
     */
    int insert(SaEngineerEntity saEngineerEntity);


    /**
     * 修改数据
     *
     * @param saEngineerEntity 实例对象
     * @return 影响行数
     */
    int update(SaEngineerEntity saEngineerEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    inks.sa.common.core.domain.vo.SaEngineerPojo getEngineerByUserid(String userid);

    String getUserid(String appointeeid);

    List<String> getUseridList(@Param("engineerids") String engineerids);

    String getIdByUserId(String id);

    SaEngineerPojo getEntityByEmail(String email);

    SaEngineerPojo getEntityByUserId(String userid);
}

