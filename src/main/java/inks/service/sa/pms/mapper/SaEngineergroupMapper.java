package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaEngineergroupPojo;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemdetailPojo;
import inks.service.sa.pms.domain.SaEngineergroupEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工程组主表(SaEngineergroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-15 12:57:58
 */
@Mapper
public interface SaEngineergroupMapper {

    SaEngineergroupPojo getEntity(@Param("key") String key);

    List<SaEngineergroupitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaEngineergroupPojo> getPageTh(QueryParam queryParam);

    int insert(SaEngineergroupEntity saEngineergroupEntity);

    int update(SaEngineergroupEntity saEngineergroupEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaEngineergroupPojo saEngineergroupPojo);

    String getName(String engineergroupid);
}

