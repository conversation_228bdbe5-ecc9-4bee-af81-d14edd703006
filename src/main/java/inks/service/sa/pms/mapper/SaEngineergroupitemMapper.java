package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemPojo;
import inks.service.sa.pms.domain.SaEngineergroupitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工程组成员子表(SaEngineergroupitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-15 12:58:10
 */
 @Mapper
public interface SaEngineergroupitemMapper {

    SaEngineergroupitemPojo getEntity(@Param("key") String key);

    List<SaEngineergroupitemPojo> getPageList(QueryParam queryParam);

    List<SaEngineergroupitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaEngineergroupitemEntity saEngineergroupitemEntity);

    int update(SaEngineergroupitemEntity saEngineergroupitemEntity);

    int delete(@Param("key") String key);

}

