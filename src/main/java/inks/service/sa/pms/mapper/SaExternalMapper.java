package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaExternalEntity;
import inks.service.sa.pms.domain.pojo.SaExternalPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 扩展应用(SaExternal)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-11-14 09:26:10
 */
@Mapper
public interface SaExternalMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaExternalPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaExternalPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saExternalEntity 实例对象
     * @return 影响行数
     */
    int insert(SaExternalEntity saExternalEntity);


    /**
     * 修改数据
     *
     * @param saExternalEntity 实例对象
     * @return 影响行数
     */
    int update(SaExternalEntity saExternalEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

