package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaFeedbackEntity;
import inks.service.sa.pms.domain.pojo.SaFeedbackPojo;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 反馈单(SaFeedback)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-17 13:04:37
 */
@Mapper
public interface SaFeedbackMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFeedbackPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFeedbackitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFeedbackPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFeedbackEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFeedbackEntity saFeedbackEntity);


    /**
     * 修改数据
     *
     * @param saFeedbackEntity 实例对象
     * @return 影响行数
     */
    int update(SaFeedbackEntity saFeedbackEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saFeedbackPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaFeedbackPojo saFeedbackPojo);

    /**
     * 修改数据
     *
     * @param saFeedbackEntity 实例对象
     * @return 影响行数
     */
    int approval(SaFeedbackEntity saFeedbackEntity);

    void syncFinishcount(String id);
}

