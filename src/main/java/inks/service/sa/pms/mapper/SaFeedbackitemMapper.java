package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaFeedbackitemEntity;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 反馈单子表(SaFeedbackitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-17 13:04:52
 */
@Mapper
public interface SaFeedbackitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFeedbackitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaFeedbackitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaFeedbackitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saFeedbackitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaFeedbackitemEntity saFeedbackitemEntity);


    /**
     * 修改数据
     *
     * @param saFeedbackitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaFeedbackitemEntity saFeedbackitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

