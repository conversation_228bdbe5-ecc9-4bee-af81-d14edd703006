package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFeedbackvisitorPojo;
import inks.service.sa.pms.domain.SaFeedbackvisitorEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 游客反馈(SaFeedbackvisitor)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-03-25 14:56:31
 */
@Mapper
public interface SaFeedbackvisitorMapper {


    SaFeedbackvisitorPojo getEntity(@Param("key") String key);

    List<SaFeedbackvisitorPojo> getPageList(QueryParam queryParam);

    int insert(SaFeedbackvisitorEntity saFeedbackvisitorEntity);

    int update(SaFeedbackvisitorEntity saFeedbackvisitorEntity);

    int delete(@Param("key") String key);
    
}

