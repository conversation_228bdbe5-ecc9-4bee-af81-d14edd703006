package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFreepagePojo;
import inks.service.sa.pms.domain.SaFreepageEntity;
import inks.service.sa.pms.domain.pojo.SaNotebookPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 自由页面(SaFreepage)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-07-22 10:20:59
 */
@Mapper
public interface SaFreepageMapper {


    SaFreepagePojo getEntity(@Param("key") String key);

    List<SaFreepagePojo> getPageList(QueryParam queryParam);

    int insert(SaFreepageEntity saFreepageEntity);

    int update(SaFreepageEntity saFreepageEntity);

    int delete(@Param("key") String key);

    void updateShareKey(String key, String randomMixedString);

    SaFreepagePojo getEntityByShareKey(String shareKey);
}

