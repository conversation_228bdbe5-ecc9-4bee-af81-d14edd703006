package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFunctionPojo;
import inks.service.sa.pms.domain.SaFunctionEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 功能中心表(SaFunction)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-01-03 15:38:20
 */
@Mapper
public interface SaFunctionMapper {


    SaFunctionPojo getEntity(@Param("key") String key);

    List<SaFunctionPojo> getPageList(QueryParam queryParam);

    int insert(SaFunctionEntity saFunctionEntity);

    int update(SaFunctionEntity saFunctionEntity);

    int delete(@Param("key") String key);
    
}

