package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaGengroupEntity;
import inks.service.sa.pms.domain.pojo.SaGengroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 代码生成器分组(SaGengroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-05 15:46:02
 */
@Mapper
public interface SaGengroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaGengroupPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaGengroupPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saGengroupEntity 实例对象
     * @return 影响行数
     */
    int insert(SaGengroupEntity saGengroupEntity);


    /**
     * 修改数据
     *
     * @param saGengroupEntity 实例对象
     * @return 影响行数
     */
    int update(SaGengroupEntity saGengroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaGengroupPojo> getAllList();


    List<String> getVelocityids(@Param("groupids") List<String> allRelatedIds);
}

