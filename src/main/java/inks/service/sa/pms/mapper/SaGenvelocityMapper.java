package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaGenvelocityEntity;
import inks.service.sa.pms.domain.pojo.SaGenvelocityPojo;
import org.apache.ibatis.annotations.MapKey;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * (SaGenvelocity)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-02 13:14:50
 */
@Mapper
public interface SaGenvelocityMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaGenvelocityPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaGenvelocityPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saGenvelocityEntity 实例对象
     * @return 影响行数
     */
    int insert(SaGenvelocityEntity saGenvelocityEntity);


    /**
     * 修改数据
     *
     * @param saGenvelocityEntity 实例对象
     * @return 影响行数
     */
    int update(SaGenvelocityEntity saGenvelocityEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    int approval(SaGenvelocityEntity saGenvelocityEntity);

    List<String> getAllVelocity();

    List<SaGenvelocityPojo> getAllEntity();

    List<String> databases();

    @MapKey("table_name")
    List<Map<String, Object>> table(@Param("queryParam") QueryParam queryParam, @Param("database") String database);

    @MapKey("table_name")
    List<Map<String, String>> getTableFields(@Param("database") String database, @Param("tableName") String tableName);


    List<SaGenvelocityPojo> getListInName(@Param("ids") List<String> ids);
}

