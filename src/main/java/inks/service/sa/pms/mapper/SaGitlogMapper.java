package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaGitlogPojo;
import inks.service.sa.pms.domain.SaGitlogEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 代码提交日志(SaGitlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-24 10:46:53
 */
@Mapper
public interface SaGitlogMapper {


    SaGitlogPojo getEntity(@Param("key") String key);

    List<SaGitlogPojo> getPageList(QueryParam queryParam);

    int insert(SaGitlogEntity saGitlogEntity);

    int update(SaGitlogEntity saGitlogEntity);

    int delete(@Param("key") String key);
    
}

