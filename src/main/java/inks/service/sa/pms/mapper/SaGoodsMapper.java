package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaGoodsEntity;
import inks.service.sa.pms.domain.pojo.SaGoodsPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 产品(SaGoods)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-18 09:18:55
 */
@Mapper
public interface SaGoodsMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaGoodsPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaGoodsPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saGoodsEntity 实例对象
     * @return 影响行数
     */
    int insert(SaGoodsEntity saGoodsEntity);


    /**
     * 修改数据
     *
     * @param saGoodsEntity 实例对象
     * @return 影响行数
     */
    int update(SaGoodsEntity saGoodsEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    SaGoodsPojo getEntityByGoodsCode(String goodscode);
}

