package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaIdeapoolPojo;
import inks.service.sa.pms.domain.SaIdeapoolEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 创意池(SaIdeapool)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-12-04 15:23:23
 */
@Mapper
public interface SaIdeapoolMapper {


    SaIdeapoolPojo getEntity(@Param("key") String key);

    List<SaIdeapoolPojo> getPageList(QueryParam queryParam);

    int insert(SaIdeapoolEntity saIdeapoolEntity);

    int update(SaIdeapoolEntity saIdeapoolEntity);

    int delete(@Param("key") String key);

    List<SaIdeapoolPojo> getAllList();
}

