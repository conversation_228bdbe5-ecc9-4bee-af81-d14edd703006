package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaImplementplanPojo;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemdetailPojo;
import inks.service.sa.pms.domain.SaImplementplanEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 实施活动计划(SaImplementplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-06 14:40:18
 */
@Mapper
public interface SaImplementplanMapper {

    SaImplementplanPojo getEntity(@Param("key") String key);

    List<SaImplementplanitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaImplementplanPojo> getPageTh(QueryParam queryParam);

    int insert(SaImplementplanEntity saImplementplanEntity);

    int update(SaImplementplanEntity saImplementplanEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaImplementplanPojo saImplementplanPojo);
}

