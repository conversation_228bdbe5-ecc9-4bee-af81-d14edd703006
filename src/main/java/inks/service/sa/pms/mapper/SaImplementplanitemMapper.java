package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemPojo;
import inks.service.sa.pms.domain.SaImplementplanitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 实施计划子表(SaImplementplanitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-10-06 14:40:34
 */
 @Mapper
public interface SaImplementplanitemMapper {

    SaImplementplanitemPojo getEntity(@Param("key") String key);

    List<SaImplementplanitemPojo> getPageList(QueryParam queryParam);

    List<SaImplementplanitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaImplementplanitemEntity saImplementplanitemEntity);

    int update(SaImplementplanitemEntity saImplementplanitemEntity);

    int delete(@Param("key") String key);

}

