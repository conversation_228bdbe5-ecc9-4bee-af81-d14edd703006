package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaIntroEntity;
import inks.service.sa.pms.domain.pojo.SaIntroPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 功能简介(SaIntro)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-24 08:56:13
 */
@Mapper
public interface SaIntroMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaIntroPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaIntroPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saIntroEntity 实例对象
     * @return 影响行数
     */
    int insert(SaIntroEntity saIntroEntity);


    /**
     * 修改数据
     *
     * @param saIntroEntity 实例对象
     * @return 影响行数
     */
    int update(SaIntroEntity saIntroEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    SaIntroPojo getEntityByCode(@Param("code") String code);
}

