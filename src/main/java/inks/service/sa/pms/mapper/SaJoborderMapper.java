package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaJoborderEntity;
import inks.service.sa.pms.domain.pojo.SaJoborderPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 服务派工(SaJoborder)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-03 14:27:16
 */
@Mapper
public interface SaJoborderMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaJoborderPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaJoborderPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saJoborderEntity 实例对象
     * @return 影响行数
     */
    int insert(SaJoborderEntity saJoborderEntity);


    /**
     * 修改数据
     *
     * @param saJoborderEntity 实例对象
     * @return 影响行数
     */
    int update(SaJoborderEntity saJoborderEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 修改数据
     *
     * @param saJoborderEntity 实例对象
     * @return 影响行数
     */
    int approval(SaJoborderEntity saJoborderEntity);
}

