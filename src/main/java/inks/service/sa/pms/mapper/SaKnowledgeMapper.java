package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaKnowledgeEntity;
import inks.service.sa.pms.domain.pojo.SaKnowledgePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 知识库(SaKnowledge)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-11-23 13:12:49
 */
@Mapper
public interface SaKnowledgeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaKnowledgePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaKnowledgePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saKnowledgeEntity 实例对象
     * @return 影响行数
     */
    int insert(SaKnowledgeEntity saKnowledgeEntity);


    /**
     * 修改数据
     *
     * @param saKnowledgeEntity 实例对象
     * @return 影响行数
     */
    int update(SaKnowledgeEntity saKnowledgeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    /**
     * 修改数据
     *
     * @param saKnowledgeEntity 实例对象
     * @return 影响行数
     */
    int approval(SaKnowledgeEntity saKnowledgeEntity);
}

