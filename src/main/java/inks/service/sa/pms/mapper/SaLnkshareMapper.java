package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaLnkshareEntity;
import inks.service.sa.pms.domain.pojo.SaLnksharePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Lnk简书分享表(SaLnkshare)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-12 10:29:08
 */
@Mapper
public interface SaLnkshareMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaLnksharePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaLnksharePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saLnkshareEntity 实例对象
     * @return 影响行数
     */
    int insert(SaLnkshareEntity saLnkshareEntity);


    /**
     * 修改数据
     *
     * @param saLnkshareEntity 实例对象
     * @return 影响行数
     */
    int update(SaLnkshareEntity saLnkshareEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

