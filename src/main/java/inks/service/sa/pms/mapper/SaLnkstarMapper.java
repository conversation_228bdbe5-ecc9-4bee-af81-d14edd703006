package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaLnkstarEntity;
import inks.service.sa.pms.domain.pojo.SaLnkstarPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaLnkstar)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-18 09:48:07
 */
@Mapper
public interface SaLnkstarMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaLnkstarPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaLnkstarPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saLnkstarEntity 实例对象
     * @return 影响行数
     */
    int insert(SaLnkstarEntity saLnkstarEntity);


    /**
     * 修改数据
     *
     * @param saLnkstarEntity 实例对象
     * @return 影响行数
     */
    int update(SaLnkstarEntity saLnkstarEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

