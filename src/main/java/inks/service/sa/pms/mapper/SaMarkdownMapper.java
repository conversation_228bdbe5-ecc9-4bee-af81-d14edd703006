package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaMarkdownEntity;
import inks.service.sa.pms.domain.pojo.SaMarkdownPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MarkDown(LNK简书)(SaMarkdown)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-19 09:43:25
 */
@Mapper
public interface SaMarkdownMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMarkdownPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaMarkdownPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saMarkdownEntity 实例对象
     * @return 影响行数
     */
    int insert(SaMarkdownEntity saMarkdownEntity);


    /**
     * 修改数据
     *
     * @param saMarkdownEntity 实例对象
     * @return 影响行数
     */
    int update(SaMarkdownEntity saMarkdownEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaMarkdownPojo> getMdByProjectId(QueryParam queryParam);

    /**
     * 修改数据
     *
     * @param saMarkdownEntity 实例对象
     * @return 影响行数
     */
    int approval(SaMarkdownEntity saMarkdownEntity);
}

