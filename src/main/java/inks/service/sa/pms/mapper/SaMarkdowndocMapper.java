package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaMarkdowndocEntity;
import inks.service.sa.pms.domain.pojo.SaMarkdowndocPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MarkDown(Doc文档)(SaMarkdowndoc)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:26
 */
@Mapper
public interface SaMarkdowndocMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMarkdowndocPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaMarkdowndocPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saMarkdowndocEntity 实例对象
     * @return 影响行数
     */
    int insert(SaMarkdowndocEntity saMarkdowndocEntity);


    /**
     * 修改数据
     *
     * @param saMarkdowndocEntity 实例对象
     * @return 影响行数
     */
    int update(SaMarkdowndocEntity saMarkdowndocEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 修改数据
     *
     * @param saMarkdowndocEntity 实例对象
     * @return 影响行数
     */
    int approval(SaMarkdowndocEntity saMarkdowndocEntity);

    List<SaMarkdowndocPojo> getMdByProjectId(QueryParam queryParam);

}

