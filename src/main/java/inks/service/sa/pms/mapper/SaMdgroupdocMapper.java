package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaMdgroupdocEntity;
import inks.service.sa.pms.domain.pojo.SaMarkdowndocPojo;
import inks.service.sa.pms.domain.pojo.SaMdgroupdocPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MarkDown分组(Doc文档)(SaMdgroupdoc)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:40
 */
@Mapper
public interface SaMdgroupdocMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMdgroupdocPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaMdgroupdocPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saMdgroupdocEntity 实例对象
     * @return 影响行数
     */
    int insert(SaMdgroupdocEntity saMdgroupdocEntity);


    /**
     * 修改数据
     *
     * @param saMdgroupdocEntity 实例对象
     * @return 影响行数
     */
    int update(SaMdgroupdocEntity saMdgroupdocEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaMarkdowndocPojo> getMarkdownsByGroupId(String key);
}

