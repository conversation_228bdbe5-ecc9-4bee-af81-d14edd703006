package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaMdprojectEntity;
import inks.service.sa.pms.domain.pojo.SaMdgroupPojo;
import inks.service.sa.pms.domain.pojo.SaMdprojectPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MD文档项目(SaMdproject)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-08 11:23:51
 */
@Mapper
public interface SaMdprojectMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMdprojectPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaMdprojectPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saMdprojectEntity 实例对象
     * @return 影响行数
     */
    int insert(SaMdprojectEntity saMdprojectEntity);


    /**
     * 修改数据
     *
     * @param saMdprojectEntity 实例对象
     * @return 影响行数
     */
    int update(SaMdprojectEntity saMdprojectEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaMdgroupPojo> getMdgroupsByProjectId(String key);
}

