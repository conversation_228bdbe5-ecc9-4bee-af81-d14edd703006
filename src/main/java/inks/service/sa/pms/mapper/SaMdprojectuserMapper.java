package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaMdprojectuserEntity;
import inks.service.sa.pms.domain.pojo.SaMdprojectuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (用户-MD项目)可视权限(SaMdprojectuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-05-08 13:07:40
 */
@Mapper
public interface SaMdprojectuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMdprojectuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaMdprojectuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saMdprojectuserEntity 实例对象
     * @return 影响行数
     */
    int insert(SaMdprojectuserEntity saMdprojectuserEntity);


    /**
     * 修改数据
     *
     * @param saMdprojectuserEntity 实例对象
     * @return 影响行数
     */
    int update(SaMdprojectuserEntity saMdprojectuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaMdprojectuserPojo> getListByProjectId(String projectId);
}

