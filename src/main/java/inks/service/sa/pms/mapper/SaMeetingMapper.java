package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetingPojo;
import inks.service.sa.pms.domain.pojo.SaMeetingitemdetailPojo;
import inks.service.sa.pms.domain.SaMeetingEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 会议主表(SaMeeting)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-17 09:46:04
 */
@Mapper
public interface SaMeetingMapper {

    SaMeetingPojo getEntity(@Param("key") String key);

    List<SaMeetingitemdetailPojo> getPageList(QueryParam queryParam);

    List<SaMeetingPojo> getPageTh(QueryParam queryParam);

    int insert(SaMeetingEntity saMeetingEntity);

    int update(SaMeetingEntity saMeetingEntity);

    int delete(@Param("key") String key);

     List<String> getDelItemIds(SaMeetingPojo saMeetingPojo);

    int approval(SaMeetingEntity saMeetingEntity);

    List<String> getDelUserIds(SaMeetingPojo saMeetingPojo);
}

