package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetingitemPojo;
import inks.service.sa.pms.domain.SaMeetingitemEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 会议子表-议题(SaMeetingitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-17 09:50:15
 */
 @Mapper
public interface SaMeetingitemMapper {

    SaMeetingitemPojo getEntity(@Param("key") String key);

    List<SaMeetingitemPojo> getPageList(QueryParam queryParam);

    List<SaMeetingitemPojo> getList(@Param("Pid") String Pid);    

    int insert(SaMeetingitemEntity saMeetingitemEntity);

    int update(SaMeetingitemEntity saMeetingitemEntity);

    int delete(@Param("key") String key);

}

