package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetingtemplatesPojo;
import inks.service.sa.pms.domain.SaMeetingtemplatesEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 会议模板表(SaMeetingtemplates)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-17 09:46:22
 */
@Mapper
public interface SaMeetingtemplatesMapper {


    SaMeetingtemplatesPojo getEntity(@Param("key") String key);

    List<SaMeetingtemplatesPojo> getPageList(QueryParam queryParam);

    int insert(SaMeetingtemplatesEntity saMeetingtemplatesEntity);

    int update(SaMeetingtemplatesEntity saMeetingtemplatesEntity);

    int delete(@Param("key") String key);
    
}

