package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetinguserPojo;
import inks.service.sa.pms.domain.SaMeetinguserEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 会议子表-人员(SaMeetinguser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-06-17 09:50:17
 */
 @Mapper
public interface SaMeetinguserMapper {

    SaMeetinguserPojo getEntity(@Param("key") String key);

    List<SaMeetinguserPojo> getPageList(QueryParam queryParam);

    List<SaMeetinguserPojo> getList(@Param("Pid") String Pid);    

    int insert(SaMeetinguserEntity saMeetinguserEntity);

    int update(SaMeetinguserEntity saMeetinguserEntity);

    int delete(@Param("key") String key);

}

