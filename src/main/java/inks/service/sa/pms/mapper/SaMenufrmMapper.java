package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaMenufrmEntity;
import inks.service.sa.pms.domain.pojo.SaMenufrmPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Frm导航(SaMenufrm)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-05-14 09:00:49
 */
@Mapper
public interface SaMenufrmMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMenufrmPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaMenufrmPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saMenufrmEntity 实例对象
     * @return 影响行数
     */
    int insert(SaMenufrmEntity saMenufrmEntity);


    /**
     * 修改数据
     *
     * @param saMenufrmEntity 实例对象
     * @return 影响行数
     */
    int update(SaMenufrmEntity saMenufrmEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaMenufrmPojo> getAllMenus();
}

