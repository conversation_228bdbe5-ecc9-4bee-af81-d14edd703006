package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaMqttlogEntity;
import inks.service.sa.pms.domain.pojo.SaMqttlogPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * MQTT日志(SaMqttlog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-02-26 12:57:20
 */
@Mapper
public interface SaMqttlogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMqttlogPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaMqttlogPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saMqttlogEntity 实例对象
     * @return 影响行数
     */
    int insert(SaMqttlogEntity saMqttlogEntity);


    /**
     * 修改数据
     *
     * @param saMqttlogEntity 实例对象
     * @return 影响行数
     */
    int update(SaMqttlogEntity saMqttlogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

