package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaNotebookPojo;
import inks.service.sa.pms.domain.SaNotebookEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 笔记本(SaNotebook)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-06-22 16:39:51
 */
@Mapper
public interface SaNotebookMapper {


    SaNotebookPojo getEntity(@Param("key") String key);

    List<SaNotebookPojo> getPageList(QueryParam queryParam);

    int insert(SaNotebookEntity saNotebookEntity);

    int update(SaNotebookEntity saNotebookEntity);

    int delete(@Param("key") String key);

    SaNotebookPojo getMyNotebook(String userid);
}

