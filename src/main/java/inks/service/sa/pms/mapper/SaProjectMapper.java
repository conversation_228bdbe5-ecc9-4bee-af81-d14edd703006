package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaProjectEntity;
import inks.service.sa.pms.domain.pojo.SaProjectPojo;
import inks.service.sa.pms.domain.pojo.SaProjectitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工程项目(SaProject)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-26 14:30:59
 */
@Mapper
public interface SaProjectMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaProjectitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaProjectPojo> getPageTh(@Param("queryParam") QueryParam queryParam, @Param("isadmin") boolean isadmin, @Param("engineerid") String engineerid);

    /**
     * 新增数据
     *
     * @param saProjectEntity 实例对象
     * @return 影响行数
     */
    int insert(SaProjectEntity saProjectEntity);


    /**
     * 修改数据
     *
     * @param saProjectEntity 实例对象
     * @return 影响行数
     */
    int update(SaProjectEntity saProjectEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saProjectPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaProjectPojo saProjectPojo);

    List<String> getDelStatusIds(SaProjectPojo saProjectPojo);

    List<String> getDelLabelIds(SaProjectPojo saProjectPojo);

    SaProjectPojo getEntityByProjectName(String nanno);
}

