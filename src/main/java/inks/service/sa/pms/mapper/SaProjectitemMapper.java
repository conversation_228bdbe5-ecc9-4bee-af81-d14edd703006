package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaProjectitemEntity;
import inks.service.sa.pms.domain.pojo.SaProjectitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目参与人员(SaProjectitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-26 14:28:41
 */
@Mapper
public interface SaProjectitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaProjectitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaProjectitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saProjectitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaProjectitemEntity saProjectitemEntity);


    /**
     * 修改数据
     *
     * @param saProjectitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaProjectitemEntity saProjectitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

