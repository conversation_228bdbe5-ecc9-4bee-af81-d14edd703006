package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaProjectlabelEntity;
import inks.service.sa.pms.domain.pojo.SaProjectlabelPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目标签(SaProjectlabel)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-26 14:28:41
 */
@Mapper
public interface SaProjectlabelMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectlabelPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaProjectlabelPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaProjectlabelPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saProjectlabelEntity 实例对象
     * @return 影响行数
     */
    int insert(SaProjectlabelEntity saProjectlabelEntity);


    /**
     * 修改数据
     *
     * @param saProjectlabelEntity 实例对象
     * @return 影响行数
     */
    int update(SaProjectlabelEntity saProjectlabelEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

