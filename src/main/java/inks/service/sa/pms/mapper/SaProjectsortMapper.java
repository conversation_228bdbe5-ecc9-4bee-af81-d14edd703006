package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaProjectsortEntity;
import inks.service.sa.pms.domain.pojo.SaProjectsortPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.Date;
import java.util.List;

/**
 * 个人常用项目排序(SaProjectsort)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-10-25 13:07:31
 */
@Mapper
public interface SaProjectsortMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectsortPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaProjectsortPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saProjectsortEntity 实例对象
     * @return 影响行数
     */
    int insert(SaProjectsortEntity saProjectsortEntity);


    /**
     * 修改数据
     *
     * @param saProjectsortEntity 实例对象
     * @return 影响行数
     */
    int update(SaProjectsortEntity saProjectsortEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    void deleteByProjectIdAndProjectItemId(@Param("id") String id, @Param("itemid") String itemid);

    void updateLastAccessTime(@Param("date") Date date, @Param("projectid") String projectid, @Param("engineerid") String engineerid);

    int SetStar(@Param("projectid") String projectid, @Param("starmark") int starmark, @Param("engineerid") String engineer);
}

