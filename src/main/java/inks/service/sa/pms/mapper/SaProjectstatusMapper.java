package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaProjectstatusEntity;
import inks.service.sa.pms.domain.pojo.SaProjectstatusPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 项目状态(SaProjectstatus)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-26 14:28:41
 */
@Mapper
public interface SaProjectstatusMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectstatusPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaProjectstatusPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaProjectstatusPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saProjectstatusEntity 实例对象
     * @return 影响行数
     */
    int insert(SaProjectstatusEntity saProjectstatusEntity);


    /**
     * 修改数据
     *
     * @param saProjectstatusEntity 实例对象
     * @return 影响行数
     */
    int update(SaProjectstatusEntity saProjectstatusEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

