package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaProjprofileEntity;
import inks.service.sa.pms.domain.pojo.SaProjprofilePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaProjprofile)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-05 15:44:18
 */
@Mapper
public interface SaProjprofileMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjprofilePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaProjprofilePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saProjprofileEntity 实例对象
     * @return 影响行数
     */
    int insert(SaProjprofileEntity saProjprofileEntity);


    /**
     * 修改数据
     *
     * @param saProjprofileEntity 实例对象
     * @return 影响行数
     */
    int update(SaProjprofileEntity saProjprofileEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

