package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaReleaseEntity;
import inks.service.sa.pms.domain.pojo.SaReleasePojo;
import inks.service.sa.pms.domain.pojo.SaReleaseitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaRelease)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-14 10:03:08
 */
@Mapper
public interface SaReleaseMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReleasePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaReleaseitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaReleasePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saReleaseEntity 实例对象
     * @return 影响行数
     */
    int insert(SaReleaseEntity saReleaseEntity);


    /**
     * 修改数据
     *
     * @param saReleaseEntity 实例对象
     * @return 影响行数
     */
    int update(SaReleaseEntity saReleaseEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saReleasePojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaReleasePojo saReleasePojo);

    /**
     * 修改数据
     *
     * @param saReleaseEntity 实例对象
     * @return 影响行数
     */
    int approval(SaReleaseEntity saReleaseEntity);

    int countByRemark(String remark);
}

