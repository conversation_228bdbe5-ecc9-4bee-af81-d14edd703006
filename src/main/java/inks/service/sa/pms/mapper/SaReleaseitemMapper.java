package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaReleaseitemEntity;
import inks.service.sa.pms.domain.pojo.SaReleaseitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaReleaseitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-14 10:05:55
 */
@Mapper
public interface SaReleaseitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReleaseitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaReleaseitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaReleaseitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saReleaseitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaReleaseitemEntity saReleaseitemEntity);


    /**
     * 修改数据
     *
     * @param saReleaseitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaReleaseitemEntity saReleaseitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

