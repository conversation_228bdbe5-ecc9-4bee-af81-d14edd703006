package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaReportsdemandPojo;
import inks.service.sa.pms.domain.SaReportsdemandEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 报表模版需求(SaReportsdemand)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-22 15:30:01
 */
@Mapper
public interface SaReportsdemandMapper {


    SaReportsdemandPojo getEntity(@Param("key") String key);

    List<SaReportsdemandPojo> getPageList(QueryParam queryParam);

    int insert(SaReportsdemandEntity saReportsdemandEntity);

    int update(SaReportsdemandEntity saReportsdemandEntity);

    int delete(@Param("key") String key);

    int checkSerCodeExists(String serCode);

    SaReportsdemandPojo getEntityBySerCode(String key);
}

