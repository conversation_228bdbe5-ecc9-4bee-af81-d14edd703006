package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaReportslabEntity;
import inks.service.sa.pms.domain.pojo.SaReportslabPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 报表库(SaReportslab)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-01-23 09:44:16
 */
@Mapper
public interface SaReportslabMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReportslabPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaReportslabPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saReportslabEntity 实例对象
     * @return 影响行数
     */
    int insert(SaReportslabEntity saReportslabEntity);


    /**
     * 修改数据
     *
     * @param saReportslabEntity 实例对象
     * @return 影响行数
     */
    int update(SaReportslabEntity saReportslabEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaReportslabPojo> getListByDef(@Param("moduleCode") String moduleCode);

    SaReportslabPojo getEntityByNameCode(@Param("rptname") String rptname, @Param("moduleCode") String moduleCode);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaReportslabPojo> getPageListAll(QueryParam queryParam);

    List<SaReportslabPojo> getListByModuleCode(@Param("moduleCode") String moduleCode);

    String getGrfDataByShareCode(String sharecode);

    int countShareCode(String shareCode);

    List<SaReportslabPojo> getListNoShareCode();

    int countShareCodeNoid(String shareCode, String id);
}

