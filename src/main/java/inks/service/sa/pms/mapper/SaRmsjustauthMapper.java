package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaRmsjustauthPojo;
import inks.service.sa.pms.domain.SaRmsjustauthEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * RMS第三方登录(SaRmsjustauth)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-29 10:59:38
 */
@Mapper
public interface SaRmsjustauthMapper {


    SaRmsjustauthPojo getEntity(@Param("key") String key);

    List<SaRmsjustauthPojo> getPageList(QueryParam queryParam);

    int insert(SaRmsjustauthEntity saRmsjustauthEntity);

    int update(SaRmsjustauthEntity saRmsjustauthEntity);

    int delete(@Param("key") String key);

    int deleteByOpenid(String openid);

    SaRmsjustauthPojo getEntityByAuthuuid(String authuuid);

    SaRmsjustauthPojo getEntityByUserid(String userid, String type);

    SaRmsjustauthPojo getJustauthByUuid(String callbackuuid, String type);
}

