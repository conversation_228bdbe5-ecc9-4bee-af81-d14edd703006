package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import inks.service.sa.pms.domain.SaRmsuserEntity;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * RMS用户(SaRmsuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2024-09-29 10:59:38
 */
@Mapper
public interface SaRmsuserMapper {


    SaRmsuserPojo getEntity(@Param("key") String key);

    List<SaRmsuserPojo> getPageList(QueryParam queryParam);

    int insert(SaRmsuserEntity saRmsuserEntity);

    int update(SaRmsuserEntity saRmsuserEntity);

    int delete(@Param("key") String key);

    SaRmsuserPojo getEntityByUserName(@Param("username") String username);

    List<SaRmsuserPojo> getPageListByCustomer(@Param("groupid") String groupid);

    SaRmsuserPojo getEntityByOpenid(@Param("openid") String openid);

    SaRmsuserPojo getEntityByUNameAndPass(@Param("username") String username, @Param("password") String password);

    List<SaRmsuserPojo> getListByOpenid(@Param("openid") String openid);
    
}

