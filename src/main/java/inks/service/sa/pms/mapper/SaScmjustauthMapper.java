package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaScmjustauthEntity;
import inks.service.sa.pms.domain.pojo.SaScmjustauthPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SCM第三方登录(SaScmjustauth)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:30
 */
@Mapper
public interface SaScmjustauthMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaScmjustauthPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaScmjustauthPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saScmjustauthEntity 实例对象
     * @return 影响行数
     */
    int insert(SaScmjustauthEntity saScmjustauthEntity);


    /**
     * 修改数据
     *
     * @param saScmjustauthEntity 实例对象
     * @return 影响行数
     */
    int update(SaScmjustauthEntity saScmjustauthEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    int deleteByOpenid(@Param("openid") String openid);

    SaScmjustauthPojo getEntityByAuthuuid(String authuuid);
}

