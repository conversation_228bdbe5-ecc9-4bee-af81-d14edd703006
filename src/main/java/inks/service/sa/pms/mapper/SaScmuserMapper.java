package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaScmuserEntity;
import inks.service.sa.pms.domain.pojo.SaScmuserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * SCM用户(SaScmuser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:31
 */
@Mapper
public interface SaScmuserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaScmuserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaScmuserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saScmuserEntity 实例对象
     * @return 影响行数
     */
    int insert(SaScmuserEntity saScmuserEntity);


    /**
     * 修改数据
     *
     * @param saScmuserEntity 实例对象
     * @return 影响行数
     */
    int update(SaScmuserEntity saScmuserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    SaScmuserPojo getEntityByUserName(@Param("username") String username);

    List<SaScmuserPojo> getPageListByCustomer(@Param("groupid") String groupid);

    SaScmuserPojo getEntityByOpenid(@Param("openid") String openid);

    SaScmuserPojo getEntityByUNameAndPass(@Param("username") String username, @Param("password") String password);

    List<SaScmuserPojo> getListByOpenid(@Param("openid") String openid);
}

