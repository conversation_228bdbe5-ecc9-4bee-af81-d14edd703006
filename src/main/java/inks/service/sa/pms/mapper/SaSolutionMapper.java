package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaSolutionEntity;
import inks.service.sa.pms.domain.pojo.SaSolutionPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 解决方案(SaSolution)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-14 16:03:36
 */
@Mapper
public interface SaSolutionMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaSolutionPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaSolutionPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saSolutionEntity 实例对象
     * @return 影响行数
     */
    int insert(SaSolutionEntity saSolutionEntity);


    /**
     * 修改数据
     *
     * @param saSolutionEntity 实例对象
     * @return 影响行数
     */
    int update(SaSolutionEntity saSolutionEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    int checkSolutionCodeUnique(@Param("solutioncode") String solutioncode, @Param("id") String id);

    SaSolutionPojo getEntityBySolutionCode(String code);
}

