package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaSqlecnPojo;
import inks.service.sa.pms.domain.SaSqlecnEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * SQL变更(SaSqlecn)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-02-19 14:27:49
 */
@Mapper
public interface SaSqlecnMapper {


    SaSqlecnPojo getEntity(@Param("key") String key);

    List<SaSqlecnPojo> getPageList(QueryParam queryParam);

    int insert(SaSqlecnEntity saSqlecnEntity);

    int update(SaSqlecnEntity saSqlecnEntity);

    int delete(@Param("key") String key);
    

    int approval(SaSqlecnEntity saSqlecnEntity);

    void updateOaflowmark(SaSqlecnPojo saDemandsubmitPojo);

    List<SaSqlecnPojo> pullList(List<String> codeList, Long timestamp, int size);
}

