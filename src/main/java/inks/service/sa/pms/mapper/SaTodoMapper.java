package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaTodoEntity;
import inks.service.sa.pms.domain.pojo.SaTodoPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Todo(SaTodo)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-02-18 11:12:38
 */
@Mapper
public interface SaTodoMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTodoPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTodoPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saTodoEntity 实例对象
     * @return 影响行数
     */
    int insert(SaTodoEntity saTodoEntity);


    /**
     * 修改数据
     *
     * @param saTodoEntity 实例对象
     * @return 影响行数
     */
    int update(SaTodoEntity saTodoEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    void setNullEndActual(String key);
}

