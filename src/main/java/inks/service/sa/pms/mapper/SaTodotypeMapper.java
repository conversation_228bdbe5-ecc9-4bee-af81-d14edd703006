package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaTodotypeEntity;
import inks.service.sa.pms.domain.pojo.SaTodotypePojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaTodotype)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-10 10:53:33
 */
@Mapper
public interface SaTodotypeMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTodotypePojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTodotypePojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saTodotypeEntity 实例对象
     * @return 影响行数
     */
    int insert(SaTodotypeEntity saTodotypeEntity);


    /**
     * 修改数据
     *
     * @param saTodotypeEntity 实例对象
     * @return 影响行数
     */
    int update(SaTodotypeEntity saTodotypeEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

