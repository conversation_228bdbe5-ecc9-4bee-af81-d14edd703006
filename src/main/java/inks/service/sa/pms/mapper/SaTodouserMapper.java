package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaTodouserEntity;
import inks.service.sa.pms.domain.pojo.SaTodouserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * Todo用户列表(SaTodouser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-03-03 14:07:16
 */
@Mapper
public interface SaTodouserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTodouserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTodouserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saTodouserEntity 实例对象
     * @return 影响行数
     */
    int insert(SaTodouserEntity saTodouserEntity);


    /**
     * 修改数据
     *
     * @param saTodouserEntity 实例对象
     * @return 影响行数
     */
    int update(SaTodouserEntity saTodouserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

