package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaTrackingEntity;
import inks.service.sa.pms.domain.pojo.SaTrackingPojo;
import inks.service.sa.pms.domain.pojo.SaTrackingitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 跟踪表主表(SaTracking)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-24 09:35:51
 */
@Mapper
public interface SaTrackingMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTrackingPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTrackingitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTrackingPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saTrackingEntity 实例对象
     * @return 影响行数
     */
    int insert(SaTrackingEntity saTrackingEntity);


    /**
     * 修改数据
     *
     * @param saTrackingEntity 实例对象
     * @return 影响行数
     */
    int update(SaTrackingEntity saTrackingEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saTrackingPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaTrackingPojo saTrackingPojo);

    void updateDisannulCount(String pid, String tid);

    void updateFinishCount(String pid, String tid);
}

