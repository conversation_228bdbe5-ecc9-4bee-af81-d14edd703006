package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaTrackingitemEntity;
import inks.service.sa.pms.domain.pojo.SaTrackingitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 跟踪表子表(SaTrackingitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-24 09:36:07
 */
@Mapper
public interface SaTrackingitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTrackingitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTrackingitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaTrackingitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saTrackingitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaTrackingitemEntity saTrackingitemEntity);


    /**
     * 修改数据
     *
     * @param saTrackingitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaTrackingitemEntity saTrackingitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

