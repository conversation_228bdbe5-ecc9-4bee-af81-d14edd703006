package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaTracktempEntity;
import inks.service.sa.pms.domain.pojo.SaTracktempPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 跟踪模板表(SaTracktemp)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-06-25 09:57:23
 */
@Mapper
public interface SaTracktempMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTracktempPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaTracktempPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saTracktempEntity 实例对象
     * @return 影响行数
     */
    int insert(SaTracktempEntity saTracktempEntity);


    /**
     * 修改数据
     *
     * @param saTracktempEntity 实例对象
     * @return 影响行数
     */
    int update(SaTracktempEntity saTracktempEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

