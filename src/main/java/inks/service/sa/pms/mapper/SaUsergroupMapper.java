package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaUsergroupEntity;
import inks.service.sa.pms.domain.pojo.SaUsergroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaUsergroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-24 16:01:27
 */
@Mapper
public interface SaUsergroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaUsergroupPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaUsergroupPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saUsergroupEntity 实例对象
     * @return 影响行数
     */
    int insert(SaUsergroupEntity saUsergroupEntity);


    /**
     * 修改数据
     *
     * @param saUsergroupEntity 实例对象
     * @return 影响行数
     */
    int update(SaUsergroupEntity saUsergroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaUsergroupPojo> getListByUserid(String userid);


    List<SaUsergroupPojo> getListByGroupid(String groupid);
}

