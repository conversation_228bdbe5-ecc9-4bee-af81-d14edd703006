package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaUserloginEntity;
import inks.service.sa.pms.domain.pojo.SaUserloginPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户登录(SaUserlogin)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:31
 */
@Mapper
public interface SaUserloginMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaUserloginPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaUserloginPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saUserloginEntity 实例对象
     * @return 影响行数
     */
    int insert(SaUserloginEntity saUserloginEntity);


    /**
     * 修改数据
     *
     * @param saUserloginEntity 实例对象
     * @return 影响行数
     */
    int update(SaUserloginEntity saUserloginEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

}

