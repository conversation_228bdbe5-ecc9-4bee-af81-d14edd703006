package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaVideogroupEntity;
import inks.service.sa.pms.domain.pojo.SaVideogroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频目录(SaVideogroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-09 08:58:49
 */
@Mapper
public interface SaVideogroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaVideogroupPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaVideogroupPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saVideogroupEntity 实例对象
     * @return 影响行数
     */
    int insert(SaVideogroupEntity saVideogroupEntity);


    /**
     * 修改数据
     *
     * @param saVideogroupEntity 实例对象
     * @return 影响行数
     */
    int update(SaVideogroupEntity saVideogroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    List<SaVideogroupPojo> getAllGroup();
}

