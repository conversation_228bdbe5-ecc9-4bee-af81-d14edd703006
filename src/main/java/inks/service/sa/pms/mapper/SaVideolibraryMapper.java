package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaVideolibraryEntity;
import inks.service.sa.pms.domain.pojo.SaVideolibraryPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 视频信息表(SaVideolibrary)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-12-09 08:59:14
 */
@Mapper
public interface SaVideolibraryMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaVideolibraryPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaVideolibraryPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saVideolibraryEntity 实例对象
     * @return 影响行数
     */
    int insert(SaVideolibraryEntity saVideolibraryEntity);


    /**
     * 修改数据
     *
     * @param saVideolibraryEntity 实例对象
     * @return 影响行数
     */
    int update(SaVideolibraryEntity saVideolibraryEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 修改数据
     *
     * @param saVideolibraryEntity 实例对象
     * @return 影响行数
     */
    int approval(SaVideolibraryEntity saVideolibraryEntity);
}

