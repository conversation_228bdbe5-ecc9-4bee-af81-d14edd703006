package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaWeeklyPojo;
import inks.service.sa.pms.domain.SaWeeklyEntity;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import java.util.List;

/**
 * 工作周报(SaWeekly)表数据库访问层
 *
 * <AUTHOR>
 * @since 2025-07-21 15:06:44
 */
@Mapper
public interface SaWeeklyMapper {


    SaWeeklyPojo getEntity(@Param("key") String key);

    List<SaWeeklyPojo> getPageList(QueryParam queryParam);

    int insert(SaWeeklyEntity saWeeklyEntity);

    int update(SaWeeklyEntity saWeeklyEntity);

    int delete(@Param("key") String key);
    

    int approval(SaWeeklyEntity saWeeklyEntity);
}

