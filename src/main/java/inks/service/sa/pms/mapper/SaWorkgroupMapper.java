package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaWorkgroupEntity;
import inks.service.sa.pms.domain.pojo.SaWorkgroupPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 往来单位(SaWorkgroup)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-04-24 16:01:27
 */
@Mapper
public interface SaWorkgroupMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaWorkgroupPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaWorkgroupPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saWorkgroupEntity 实例对象
     * @return 影响行数
     */
    int insert(SaWorkgroupEntity saWorkgroupEntity);


    /**
     * 修改数据
     *
     * @param saWorkgroupEntity 实例对象
     * @return 影响行数
     */
    int update(SaWorkgroupEntity saWorkgroupEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    int countBySerCode(String serCode);

    SaWorkgroupPojo findBySerCode(String sercode);

    String  getIdBySerCode(String sercode);
}

