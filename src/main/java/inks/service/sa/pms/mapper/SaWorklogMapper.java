package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaWorklogEntity;
import inks.service.sa.pms.domain.pojo.SaWorklogPojo;
import inks.service.sa.pms.domain.pojo.SaWorklogitemdetailPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * (SaWorklog)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-04 13:59:13
 */
@Mapper
public interface SaWorklogMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaWorklogPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaWorklogitemdetailPojo> getPageList(QueryParam queryParam);


    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaWorklogPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saWorklogEntity 实例对象
     * @return 影响行数
     */
    int insert(SaWorklogEntity saWorklogEntity);


    /**
     * 修改数据
     *
     * @param saWorklogEntity 实例对象
     * @return 影响行数
     */
    int update(SaWorklogEntity saWorklogEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

    /**
     * 查询 被删除的Item
     *
     * @param saWorklogPojo 筛选条件
     * @return 查询结果
     */
    List<String> getDelItemIds(SaWorklogPojo saWorklogPojo);

    List<String> getDelPlanIds(SaWorklogPojo saWorklogPojo);

    int checkWorklogTitle(String title);
}

