package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaWorklogitemEntity;
import inks.service.sa.pms.domain.pojo.SaWorklogitemPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作日志今日子表(SaWorklogitem)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-04 12:58:20
 */
@Mapper
public interface SaWorklogitemMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaWorklogitemPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaWorklogitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaWorklogitemPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saWorklogitemEntity 实例对象
     * @return 影响行数
     */
    int insert(SaWorklogitemEntity saWorklogitemEntity);


    /**
     * 修改数据
     *
     * @param saWorklogitemEntity 实例对象
     * @return 影响行数
     */
    int update(SaWorklogitemEntity saWorklogitemEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

