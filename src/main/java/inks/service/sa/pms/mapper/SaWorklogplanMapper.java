package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaWorklogplanEntity;
import inks.service.sa.pms.domain.pojo.SaWorklogplanPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 工作日志计划子表(SaWorklogplan)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-09-04 14:31:23
 */
@Mapper
public interface SaWorklogplanMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaWorklogplanPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaWorklogplanPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaWorklogplanPojo> getList(@Param("Pid") String Pid);


    /**
     * 新增数据
     *
     * @param saWorklogplanEntity 实例对象
     * @return 影响行数
     */
    int insert(SaWorklogplanEntity saWorklogplanEntity);


    /**
     * 修改数据
     *
     * @param saWorklogplanEntity 实例对象
     * @return 影响行数
     */
    int update(SaWorklogplanEntity saWorklogplanEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key);

}

