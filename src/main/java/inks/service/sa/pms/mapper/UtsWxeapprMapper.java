package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.UtsWxeapprEntity;
import inks.service.sa.pms.domain.pojo.UtsWxeapprPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 企业微审核(UtsWxeappr)表数据库访问层
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:08
 */
@Mapper
public interface UtsWxeapprMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxeapprPojo getEntity(@Param("key") String key,@Param("tid") String tid);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<UtsWxeapprPojo> getPageList(QueryParam queryParam);

   
    
    /**
     * 新增数据
     *
     * @param utsWxeapprEntity 实例对象
     * @return 影响行数
     */
    int insert(UtsWxeapprEntity utsWxeapprEntity);

    
    /**
     * 修改数据
     *
     * @param utsWxeapprEntity 实例对象
     * @return 影响行数
     */
    int update(UtsWxeapprEntity utsWxeapprEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key,@Param("tid") String tid);

    List<UtsWxeapprPojo> getListByModuleCode(@Param("moduleCode") String moduleCode, @Param("tid") String tid);
}

