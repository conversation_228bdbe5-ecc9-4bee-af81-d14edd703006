package inks.service.sa.pms.mapper;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.SaUserEntity;
import inks.service.sa.pms.domain.pojo.SaUserPojo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Qualifier;

import java.util.List;

/**
 * 用户(SaUser)表数据库访问层
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:13
 */
@Mapper
public interface pmsSaUserMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaUserPojo getEntity(@Param("key") String key);

    /**
     * 查询指定行数据
     *
     * @param queryParam 查询条件
     * @return 对象列表
     */
    List<SaUserPojo> getPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saUserEntity 实例对象
     * @return 影响行数
     */
    int insert(SaUserEntity saUserEntity);


    /**
     * 修改数据
     *
     * @param saUserEntity 实例对象
     * @return 影响行数
     */
    int update(SaUserEntity saUserEntity);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 影响行数
     */
    int delete(@Param("key") String key, @Param("tid") String tid);

    SaUserPojo getEntityByOpenid(@Param("openid") String openid);

    SaUserPojo getUserInfo(@Param("id") String userid);

    SaUserPojo getEntityByUNameAndPass(@Param("username") String username, @Param("password") String encryptPass);


    SaUserPojo checkPasswordByUserid(@Param("userid") String userid, @Param("password") String encrypt);

    int getAdminMarkByUserid(String userid);

    List<String> checkUseridUsed(@Param("userid") String userid);

    String getDingUserid(String createbyid);

    SaUserPojo getEntityByRealName(String realname);

    int updateAdminPassword(String encryptPassword);

    List<SaUserPojo> getAllList();
}

