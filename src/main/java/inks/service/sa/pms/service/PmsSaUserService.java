package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.sa.common.core.domain.pojo.LoginUserPojo;
import inks.service.sa.pms.domain.pojo.SaUserPojo;
import inks.sa.common.core.domain.vo.MyLoginUser;

import javax.servlet.http.HttpServletRequest;
import java.util.List;
import java.util.Map;

/**
 * 用户(SaUser)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:13
 */
public interface PmsSaUserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaUserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaUserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saUserPojo 实例对象
     * @return 实例对象
     */
    SaUserPojo insert(SaUserPojo saUserPojo) throws Exception;

    /**
     * 修改数据
     *
     * @param saUserpojo 实例对象
     * @return 实例对象
     */
    SaUserPojo update(SaUserPojo saUserpojo) throws Exception;

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);


    SaUserPojo getUserInfo(LoginUser loginUser);

    Map<String, Object> loginCheck(LoginUserPojo loginUserPojo, HttpServletRequest request, String type);

    Map<String, Object> loginCheckByOpenid(String openidFromOam, HttpServletRequest request);

    Map<String, Object> loginRmsByOpenid(String openid, HttpServletRequest request);
    
    Map<String, Object> myCreateToken(MyLoginUser myLoginUser, HttpServletRequest request);

    List<String> checkUseridUsed(String userid);

}
