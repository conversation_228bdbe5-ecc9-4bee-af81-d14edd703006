package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaLoginlogPojo;

/**
 * 登录日志(SaLoginlog)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-20 08:47:34
 */
public interface Pms_SaLoginlogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaLoginlogPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaLoginlogPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saLoginlogPojo 实例对象
     * @return 实例对象
     */
    SaLoginlogPojo insert(SaLoginlogPojo saLoginlogPojo);

    /**
     * 修改数据
     *
     * @param saLoginlogpojo 实例对象
     * @return 实例对象
     */
    SaLoginlogPojo update(SaLoginlogPojo saLoginlogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
