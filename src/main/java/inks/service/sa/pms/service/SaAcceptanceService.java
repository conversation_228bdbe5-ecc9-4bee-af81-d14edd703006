package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaAcceptancePojo;

/**
 * (SaAcceptance)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-09 16:11:12
 */
public interface SaAcceptanceService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaAcceptancePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaAcceptancePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saAcceptancePojo 实例对象
     * @return 实例对象
     */
    SaAcceptancePojo insert(SaAcceptancePojo saAcceptancePojo);

    /**
     * 修改数据
     *
     * @param saAcceptancepojo 实例对象
     * @return 实例对象
     */
    SaAcceptancePojo update(SaAcceptancePojo saAcceptancepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
