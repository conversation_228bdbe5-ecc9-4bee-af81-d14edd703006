package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivityPojo;
import inks.service.sa.pms.domain.pojo.SaActivityitemdetailPojo;
import inks.service.sa.pms.domain.SaActivityEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 活动主表(SaActivity)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:11
 */
public interface SaActivityService {


    SaActivityPojo getEntity(String key);

    PageInfo<SaActivityitemdetailPojo> getPageList(QueryParam queryParam);

    SaActivityPojo getBillEntity(String key);

    PageInfo<SaActivityPojo> getBillList(QueryParam queryParam);

    PageInfo<SaActivityPojo> getPageTh(QueryParam queryParam);

    SaActivityPojo insert(SaActivityPojo saActivityPojo);

    SaActivityPojo update(SaActivityPojo saActivitypojo);

    int delete(String key);


     SaActivityPojo approval(SaActivityPojo saActivityPojo);
}
