package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivityitemPojo;
import inks.service.sa.pms.domain.SaActivityitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 活动子表(SaActivityitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-13 17:03:54
 */
public interface SaActivityitemService {


    SaActivityitemPojo getEntity(String key);

    PageInfo<SaActivityitemPojo> getPageList(QueryParam queryParam);

    List<SaActivityitemPojo> getList(String Pid);  

    SaActivityitemPojo insert(SaActivityitemPojo saActivityitemPojo);

    SaActivityitemPojo update(SaActivityitemPojo saActivityitempojo);

    int delete(String key);

    SaActivityitemPojo clearNull(SaActivityitemPojo saActivityitempojo);
}
