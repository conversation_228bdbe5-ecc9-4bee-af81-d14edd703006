package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivityplanPojo;
import inks.service.sa.pms.domain.SaActivityplanEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 活动计划/日志(SaActivityplan)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-21 17:27:55
 */
public interface SaActivityplanService {


    SaActivityplanPojo getEntity(String key);

    PageInfo<SaActivityplanPojo> getPageList(QueryParam queryParam);

    List<SaActivityplanPojo> getList(String Pid);  

    SaActivityplanPojo insert(SaActivityplanPojo saActivityplanPojo);

    SaActivityplanPojo update(SaActivityplanPojo saActivityplanpojo);

    int delete(String key);

    SaActivityplanPojo clearNull(SaActivityplanPojo saActivityplanpojo);
}
