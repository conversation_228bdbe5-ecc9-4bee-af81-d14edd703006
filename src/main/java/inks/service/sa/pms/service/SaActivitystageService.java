package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivitystagePojo;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemdetailPojo;
import inks.service.sa.pms.domain.SaActivitystageEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 活动阶段表(SaActivitystage)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:14
 */
public interface SaActivitystageService {


    SaActivitystagePojo getEntity(String key);

    PageInfo<SaActivitystageitemdetailPojo> getPageList(QueryParam queryParam);

    SaActivitystagePojo getBillEntity(String key);

    PageInfo<SaActivitystagePojo> getBillList(QueryParam queryParam);

    PageInfo<SaActivitystagePojo> getPageTh(QueryParam queryParam);

    SaActivitystagePojo insert(SaActivitystagePojo saActivitystagePojo);

    SaActivitystagePojo update(SaActivitystagePojo saActivitystagepojo);

    int delete(String key);

}
