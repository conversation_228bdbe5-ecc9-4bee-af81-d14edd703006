package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemPojo;
import inks.service.sa.pms.domain.SaActivitystageitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 活动阶段子表(SaActivitystageitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:22
 */
public interface SaActivitystageitemService {


    SaActivitystageitemPojo getEntity(String key);

    PageInfo<SaActivitystageitemPojo> getPageList(QueryParam queryParam);

    List<SaActivitystageitemPojo> getList(String Pid);  

    SaActivitystageitemPojo insert(SaActivitystageitemPojo saActivitystageitemPojo);

    SaActivitystageitemPojo update(SaActivitystageitemPojo saActivitystageitempojo);

    int delete(String key);

    SaActivitystageitemPojo clearNull(SaActivitystageitemPojo saActivitystageitempojo);
}
