package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaApiinfoPojo;

/**
 * 接口信息表(SaApiinfo)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15 17:09:56
 */
public interface SaApiinfoService {


    SaApiinfoPojo getEntity(String key);

    PageInfo<SaApiinfoPojo> getPageList(QueryParam queryParam);

    SaApiinfoPojo insert(SaApiinfoPojo saApiinfoPojo);

    SaApiinfoPojo update(SaApiinfoPojo saApiinfopojo);

    int delete(String key);

    //public void importSwagger(String swaggerUrl) {
    String importSwagger(String swaggerUrl, String fnid, String fncode, LoginUser loginUser);
}
