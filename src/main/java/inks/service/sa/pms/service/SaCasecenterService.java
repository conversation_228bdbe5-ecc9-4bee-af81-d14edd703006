package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaCasecenterPojo;

/**
 * 案例中心(SaCasecenter)表服务接口
 *
 * <AUTHOR>
 * @since 2024-03-11 10:48:12
 */
public interface SaCasecenterService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaCasecenterPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaCasecenterPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saCasecenterPojo 实例对象
     * @return 实例对象
     */
    SaCasecenterPojo insert(SaCasecenterPojo saCasecenterPojo);

    /**
     * 修改数据
     *
     * @param saCasecenterpojo 实例对象
     * @return 实例对象
     */
    SaCasecenterPojo update(SaCasecenterPojo saCasecenterpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
