package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaChatterPojo;

/**
 * PMS客服(SaChatter)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-21 13:22:05
 */
public interface SaChatterService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaChatterPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaChatterPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saChatterPojo 实例对象
     * @return 实例对象
     */
    SaChatterPojo insert(SaChatterPojo saChatterPojo);

    /**
     * 修改数据
     *
     * @param saChatterpojo 实例对象
     * @return 实例对象
     */
    SaChatterPojo update(SaChatterPojo saChatterpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
