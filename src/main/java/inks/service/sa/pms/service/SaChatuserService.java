package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaChatuserPojo;

/**
 * 对话用户(SaChatuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-12-21 15:01:55
 */
public interface SaChatuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaChatuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaChatuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saChatuserPojo 实例对象
     * @return 实例对象
     */
    SaChatuserPojo insert(SaChatuserPojo saChatuserPojo);

    /**
     * 修改数据
     *
     * @param saChatuserpojo 实例对象
     * @return 实例对象
     */
    SaChatuserPojo update(SaChatuserPojo saChatuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
