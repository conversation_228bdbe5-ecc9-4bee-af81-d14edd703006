package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaCustrmsuserPojo;
import inks.service.sa.pms.domain.SaCustrmsuserEntity;

import com.github.pagehelper.PageInfo;
import inks.service.sa.pms.domain.pojo.SaCustrmsuserPojo;

import java.util.List;

/**
 * 客户RMS关系表(SaCustrmsuser)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-29 11:04:28
 */
public interface SaCustrmsuserService {


    SaCustrmsuserPojo getEntity(String key);

    PageInfo<SaCustrmsuserPojo> getPageList(QueryParam queryParam);

    SaCustrmsuserPojo insert(SaCustrmsuserPojo saCustrmsuserPojo);

    SaCustrmsuserPojo update(SaCustrmsuserPojo saCustrmsuserpojo);

    int delete(String key);
    SaCustrmsuserPojo getEntityByUserid(String key);

    List<SaCustrmsuserPojo> getListByUserid(String userid);

    SaCustrmsuserPojo createRmsUser(SaCustrmsuserPojo saCustrmsuserPojo) throws Exception;
}
