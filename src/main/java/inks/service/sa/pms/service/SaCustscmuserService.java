package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaCustscmuserPojo;

import java.util.List;

/**
 * 客户SCM关系表(SaCustscmuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:30
 */
public interface SaCustscmuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaCustscmuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaCustscmuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saCustscmuserPojo 实例对象
     * @return 实例对象
     */
    SaCustscmuserPojo insert(SaCustscmuserPojo saCustscmuserPojo);

    /**
     * 修改数据
     *
     * @param saCustscmuserpojo 实例对象
     * @return 实例对象
     */
    SaCustscmuserPojo update(SaCustscmuserPojo saCustscmuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    SaCustscmuserPojo getEntityByUserid(String key);

    List<SaCustscmuserPojo> getListByUserid(String userid);

    SaCustscmuserPojo createScmUser(SaCustscmuserPojo saCustscmuserPojo) throws Exception;
}
