package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDatabasePojo;

/**
 * 数据库连接池(SaDatabase)表服务接口
 *
 * <AUTHOR>
 * @since 2024-04-05 15:24:15
 */
public interface SaDatabaseService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDatabasePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaDatabasePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDatabasePojo 实例对象
     * @return 实例对象
     */
    SaDatabasePojo insert(SaDatabasePojo saDatabasePojo);

    /**
     * 修改数据
     *
     * @param saDatabasepojo 实例对象
     * @return 实例对象
     */
    SaDatabasePojo update(SaDatabasePojo saDatabasepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
