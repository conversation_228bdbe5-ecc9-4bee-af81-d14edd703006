package inks.service.sa.pms.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDbdesignPojo;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemdetailPojo;
import inks.service.sa.pms.domain.SaDbdesignEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 表格设计(SaDbdesign)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15 09:54:21
 */
public interface SaDbdesignService {


    SaDbdesignPojo getEntity(String key);

    PageInfo<SaDbdesignitemdetailPojo> getPageList(QueryParam queryParam);

    SaDbdesignPojo getBillEntity(String key);

    PageInfo<SaDbdesignPojo> getBillList(QueryParam queryParam);

    PageInfo<SaDbdesignPojo> getPageTh(QueryParam queryParam);

    SaDbdesignPojo insert(SaDbdesignPojo saDbdesignPojo);

    SaDbdesignPojo update(SaDbdesignPojo saDbdesignpojo);

    int delete(String key);

    String alter(List<String> itemids);
}
