package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo;
import inks.service.sa.pms.domain.SaDbdesignitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 表格设计字段子表(SaDbdesignitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-15 09:54:35
 */
public interface SaDbdesignitemService {


    SaDbdesignitemPojo getEntity(String key);

    PageInfo<SaDbdesignitemPojo> getPageList(QueryParam queryParam);

    List<SaDbdesignitemPojo> getList(String Pid);  

    SaDbdesignitemPojo insert(SaDbdesignitemPojo saDbdesignitemPojo);

    SaDbdesignitemPojo update(SaDbdesignitemPojo saDbdesignitempojo);

    int delete(String key);

    SaDbdesignitemPojo clearNull(SaDbdesignitemPojo saDbdesignitempojo);
}
