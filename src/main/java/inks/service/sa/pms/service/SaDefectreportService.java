package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDefectreportPojo;

/**
 * 缺陷报告(SaDefectreport)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-26 12:52:57
 */
public interface SaDefectreportService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDefectreportPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaDefectreportPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDefectreportPojo 实例对象
     * @return 实例对象
     */
    SaDefectreportPojo insert(SaDefectreportPojo saDefectreportPojo);

    /**
     * 修改数据
     *
     * @param saDefectreportpojo 实例对象
     * @return 实例对象
     */
    SaDefectreportPojo update(SaDefectreportPojo saDefectreportpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
