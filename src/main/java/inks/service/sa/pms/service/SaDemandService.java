package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandPojo;
import inks.sa.common.core.domain.vo.MyLoginUser;

import java.util.List;
import java.util.Map;

/**
 * 产品需求(SaDemand)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-24 16:43:02
 */
public interface SaDemandService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDemandPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaDemandPojo> getPageList(QueryParam queryParam);
    PageInfo<SaDemandPojo> getPageListUNIONALL(QueryParam queryParam, Integer finishlimit);

    PageInfo<SaDemandPojo> getTodoPageList(QueryParam queryParam);


    /**
     * 新增数据
     *
     * @param saDemandPojo 实例对象
     * @return 实例对象
     */
    SaDemandPojo insert(SaDemandPojo saDemandPojo, LoginUser loginUser);

    /**
     * 修改数据
     *
     * @param saDemandpojo 实例对象
     * @return 实例对象
     */
    SaDemandPojo update(SaDemandPojo saDemandpojo, MyLoginUser myLoginUser);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, LoginUser loginUser);

    boolean checkTitleExist(String commitTitle);

    boolean checkTitleWithRemark(String subject, String remark);

    PageInfo<SaDemandPojo> getAllPageList(QueryParam queryParam);

    List<Map<String,Object>> getOnlineStatus();

    Integer getFinishMark(String id);

    List<Map<String, Integer>> getCountByBillType(String qpfilter);
}
