package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandlabelPojo;
import inks.service.sa.pms.domain.SaDemandlabelEntity;

import com.github.pagehelper.PageInfo;

/**
 * 需求标签(SaDemandlabel)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-26 13:02:27
 */
public interface SaDemandlabelService {


    SaDemandlabelPojo getEntity(String key);

    PageInfo<SaDemandlabelPojo> getPageList(QueryParam queryParam);

    SaDemandlabelPojo insert(SaDemandlabelPojo saDemandlabelPojo);

    SaDemandlabelPojo update(SaDemandlabelPojo saDemandlabelpojo);

    int delete(String key);
}
