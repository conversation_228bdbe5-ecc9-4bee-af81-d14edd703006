package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandlogPojo;

/**
 * 产品需求日志(SaDemandlog)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-26 15:00:38
 */
public interface SaDemandlogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDemandlogPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaDemandlogPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDemandlogPojo 实例对象
     * @return 实例对象
     */
    SaDemandlogPojo insert(SaDemandlogPojo saDemandlogPojo);

    /**
     * 修改数据
     *
     * @param saDemandlogpojo 实例对象
     * @return 实例对象
     */
    SaDemandlogPojo update(SaDemandlogPojo saDemandlogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
