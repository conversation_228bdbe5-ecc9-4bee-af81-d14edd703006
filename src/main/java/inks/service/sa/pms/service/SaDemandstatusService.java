package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandstatusPojo;
import inks.service.sa.pms.domain.SaDemandstatusEntity;

import com.github.pagehelper.PageInfo;

/**
 * 需求状态(SaDemandstatus)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-24 15:21:21
 */
public interface SaDemandstatusService {


    SaDemandstatusPojo getEntity(String key);

    PageInfo<SaDemandstatusPojo> getPageList(QueryParam queryParam);

    SaDemandstatusPojo insert(SaDemandstatusPojo saDemandstatusPojo);

    SaDemandstatusPojo update(SaDemandstatusPojo saDemandstatuspojo);

    int delete(String key);
}
