package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandsubmitPojo;
import inks.service.sa.pms.domain.SaDemandsubmitEntity;

import com.github.pagehelper.PageInfo;

/**
 * 需求提报(SaDemandsubmit)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-25 13:27:24
 */
public interface SaDemandsubmitService {


    SaDemandsubmitPojo getEntity(String key);

    PageInfo<SaDemandsubmitPojo> getPageList(QueryParam queryParam);

    SaDemandsubmitPojo insert(SaDemandsubmitPojo saDemandsubmitPojo);

    SaDemandsubmitPojo update(SaDemandsubmitPojo saDemandsubmitpojo);

    int delete(String key);

     SaDemandsubmitPojo approval(SaDemandsubmitPojo saDemandsubmitPojo);
}
