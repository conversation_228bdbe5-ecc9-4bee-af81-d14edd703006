package inks.service.sa.pms.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDemandtimePojo;
import inks.service.sa.pms.domain.SaDemandtimeEntity;

import com.github.pagehelper.PageInfo;

/**
 * 需求工时记录(SaDemandtime)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-02 11:09:22
 */
public interface SaDemandtimeService {


    SaDemandtimePojo getEntity(String key);

    PageInfo<SaDemandtimePojo> getPageList(QueryParam queryParam);

    SaDemandtimePojo insert(SaDemandtimePojo saDemandtimePojo);

    SaDemandtimePojo update(SaDemandtimePojo saDemandtimepojo);

    int delete(String key);

    String start(String demandid, LoginUser loginUser);

    String pause(String demandid, LoginUser loginUser);

    String complete(String demandid, LoginUser loginUser);
}
