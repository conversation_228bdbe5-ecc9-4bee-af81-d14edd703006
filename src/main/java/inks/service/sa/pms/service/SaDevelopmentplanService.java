package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDevelopmentplanPojo;

/**
 * 开发活动计划(SaDevelopmentplan)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-13 13:22:01
 */
public interface SaDevelopmentplanService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDevelopmentplanPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaDevelopmentplanPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDevelopmentplanPojo 实例对象
     * @return 实例对象
     */
    SaDevelopmentplanPojo insert(SaDevelopmentplanPojo saDevelopmentplanPojo);

    /**
     * 修改数据
     *
     * @param saDevelopmentplanpojo 实例对象
     * @return 实例对象
     */
    SaDevelopmentplanPojo update(SaDevelopmentplanPojo saDevelopmentplanpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
