package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDocPojo;

/**
 * 文档表(SaDoc)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
public interface SaDocService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDocPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaDocPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDocPojo 实例对象
     * @return 实例对象
     */
    SaDocPojo insert(SaDocPojo saDocPojo);

    /**
     * 修改数据
     *
     * @param saDocpojo 实例对象
     * @return 实例对象
     */
    SaDocPojo update(SaDocPojo saDocpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
