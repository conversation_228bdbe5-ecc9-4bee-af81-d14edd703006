package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDocreposPojo;

/**
 * 仓库表(SaDocrepos)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
public interface SaDocreposService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDocreposPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaDocreposPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDocreposPojo 实例对象
     * @return 实例对象
     */
    SaDocreposPojo insert(SaDocreposPojo saDocreposPojo);

    /**
     * 修改数据
     *
     * @param saDocrepospojo 实例对象
     * @return 实例对象
     */
    SaDocreposPojo update(SaDocreposPojo saDocrepospojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
