package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaDocuserPojo;

/**
 * 文档-(SaDocuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-24 16:15:01
 */
public interface SaDocuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaDocuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaDocuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saDocuserPojo 实例对象
     * @return 实例对象
     */
    SaDocuserPojo insert(SaDocuserPojo saDocuserPojo);

    /**
     * 修改数据
     *
     * @param saDocuserpojo 实例对象
     * @return 实例对象
     */
    SaDocuserPojo update(SaDocuserPojo saDocuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
