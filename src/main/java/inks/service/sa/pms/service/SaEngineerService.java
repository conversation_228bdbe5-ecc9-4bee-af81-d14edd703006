package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaEngineerPojo;

/**
 * 处理节点(SaEngineer)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-29 16:04:12
 */
public interface SaEngineerService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaEngineerPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaEngineerPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saEngineerPojo 实例对象
     * @return 实例对象
     */
    SaEngineerPojo insert(SaEngineerPojo saEngineerPojo);

    /**
     * 修改数据
     *
     * @param saEngineerpojo 实例对象
     * @return 实例对象
     */
    SaEngineerPojo update(SaEngineerPojo saEngineerpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
