package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaEngineergroupPojo;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemdetailPojo;
import inks.service.sa.pms.domain.SaEngineergroupEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 工程组主表(SaEngineergroup)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-15 12:57:58
 */
public interface SaEngineergroupService {


    SaEngineergroupPojo getEntity(String key);

    PageInfo<SaEngineergroupitemdetailPojo> getPageList(QueryParam queryParam);

    SaEngineergroupPojo getBillEntity(String key);

    PageInfo<SaEngineergroupPojo> getBillList(QueryParam queryParam);

    PageInfo<SaEngineergroupPojo> getPageTh(QueryParam queryParam);

    SaEngineergroupPojo insert(SaEngineergroupPojo saEngineergroupPojo);

    SaEngineergroupPojo update(SaEngineergroupPojo saEngineergrouppojo);

    int delete(String key);

}
