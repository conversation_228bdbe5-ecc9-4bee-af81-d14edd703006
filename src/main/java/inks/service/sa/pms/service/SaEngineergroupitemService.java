package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemPojo;
import inks.service.sa.pms.domain.SaEngineergroupitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 工程组成员子表(SaEngineergroupitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-15 12:58:10
 */
public interface SaEngineergroupitemService {


    SaEngineergroupitemPojo getEntity(String key);

    PageInfo<SaEngineergroupitemPojo> getPageList(QueryParam queryParam);

    List<SaEngineergroupitemPojo> getList(String Pid);  

    SaEngineergroupitemPojo insert(SaEngineergroupitemPojo saEngineergroupitemPojo);

    SaEngineergroupitemPojo update(SaEngineergroupitemPojo saEngineergroupitempojo);

    int delete(String key);

    SaEngineergroupitemPojo clearNull(SaEngineergroupitemPojo saEngineergroupitempojo);
}
