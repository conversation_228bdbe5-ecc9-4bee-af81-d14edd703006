package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaExternalPojo;

/**
 * 扩展应用(SaExternal)表服务接口
 *
 * <AUTHOR>
 * @since 2023-11-14 09:26:10
 */
public interface SaExternalService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaExternalPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaExternalPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saExternalPojo 实例对象
     * @return 实例对象
     */
    SaExternalPojo insert(SaExternalPojo saExternalPojo);

    /**
     * 修改数据
     *
     * @param saExternalpojo 实例对象
     * @return 实例对象
     */
    SaExternalPojo update(SaExternalPojo saExternalpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
