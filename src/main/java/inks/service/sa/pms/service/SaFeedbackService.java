package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFeedbackPojo;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemdetailPojo;

/**
 * 反馈单(SaFeedback)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-17 13:04:37
 */
public interface SaFeedbackService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFeedbackPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFeedbackitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFeedbackPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFeedbackPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFeedbackPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saFeedbackPojo 实例对象
     * @return 实例对象
     */
    SaFeedbackPojo insert(SaFeedbackPojo saFeedbackPojo);

    /**
     * 修改数据
     *
     * @param saFeedbackpojo 实例对象
     * @return 实例对象
     */
    SaFeedbackPojo update(SaFeedbackPojo saFeedbackpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 审核数据
     *
     * @param saFeedbackPojo 实例对象
     * @return 实例对象
     */
    SaFeedbackPojo approval(SaFeedbackPojo saFeedbackPojo);
}
