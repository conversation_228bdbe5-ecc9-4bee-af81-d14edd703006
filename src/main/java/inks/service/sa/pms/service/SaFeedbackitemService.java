package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemPojo;

import java.util.List;

/**
 * 反馈单子表(SaFeedbackitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-17 13:04:52
 */
public interface SaFeedbackitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaFeedbackitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaFeedbackitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaFeedbackitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saFeedbackitemPojo 实例对象
     * @return 实例对象
     */
    SaFeedbackitemPojo insert(SaFeedbackitemPojo saFeedbackitemPojo);

    /**
     * 修改数据
     *
     * @param saFeedbackitempojo 实例对象
     * @return 实例对象
     */
    SaFeedbackitemPojo update(SaFeedbackitemPojo saFeedbackitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 修改数据
     *
     * @param saFeedbackitempojo 实例对象
     * @return 实例对象
     */
    SaFeedbackitemPojo clearNull(SaFeedbackitemPojo saFeedbackitempojo);
}
