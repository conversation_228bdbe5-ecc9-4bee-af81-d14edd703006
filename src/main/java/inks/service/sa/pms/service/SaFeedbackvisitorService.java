package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFeedbackvisitorPojo;
import inks.service.sa.pms.domain.SaFeedbackvisitorEntity;

import com.github.pagehelper.PageInfo;

/**
 * 游客反馈(SaFeedbackvisitor)表服务接口
 *
 * <AUTHOR>
 * @since 2025-03-25 14:56:31
 */
public interface SaFeedbackvisitorService {


    SaFeedbackvisitorPojo getEntity(String key);

    PageInfo<SaFeedbackvisitorPojo> getPageList(QueryParam queryParam);

    SaFeedbackvisitorPojo insert(SaFeedbackvisitorPojo saFeedbackvisitorPojo);

    SaFeedbackvisitorPojo update(SaFeedbackvisitorPojo saFeedbackvisitorpojo);

    int delete(String key);
}
