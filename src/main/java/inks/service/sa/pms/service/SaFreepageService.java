package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFreepagePojo;
import inks.service.sa.pms.domain.SaFreepageEntity;

import com.github.pagehelper.PageInfo;
import inks.service.sa.pms.domain.pojo.SaNotebookPojo;

/**
 * 自由页面(SaFreepage)表服务接口
 *
 * <AUTHOR>
 * @since 2024-07-22 10:20:59
 */
public interface SaFreepageService {


    SaFreepagePojo getEntity(String key);

    PageInfo<SaFreepagePojo> getPageList(QueryParam queryParam);

    SaFreepagePojo insert(SaFreepagePojo saFreepagePojo);

    SaFreepagePojo update(SaFreepagePojo saFreepagepojo);

    int delete(String key);


}
