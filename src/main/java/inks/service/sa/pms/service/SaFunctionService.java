package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaFunctionPojo;
import inks.service.sa.pms.domain.SaFunctionEntity;

import com.github.pagehelper.PageInfo;

/**
 * 功能中心表(SaFunction)表服务接口
 *
 * <AUTHOR>
 * @since 2025-01-03 15:38:20
 */
public interface SaFunctionService {


    SaFunctionPojo getEntity(String key);

    PageInfo<SaFunctionPojo> getPageList(QueryParam queryParam);

    SaFunctionPojo insert(SaFunctionPojo saFunctionPojo);

    SaFunctionPojo update(SaFunctionPojo saFunctionpojo);

    int delete(String key);
}
