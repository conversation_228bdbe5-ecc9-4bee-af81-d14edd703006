package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaGengroupPojo;

/**
 * 代码生成器分组(SaGengroup)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-05 15:46:02
 */
public interface SaGengroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaGengroupPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaGengroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saGengroupPojo 实例对象
     * @return 实例对象
     */
    SaGengroupPojo insert(SaGengroupPojo saGengroupPojo);

    /**
     * 修改数据
     *
     * @param saGengrouppojo 实例对象
     * @return 实例对象
     */
    SaGengroupPojo update(SaGengroupPojo saGengrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
