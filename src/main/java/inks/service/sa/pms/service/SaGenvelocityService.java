package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaGenvelocityPojo;

import java.util.List;
import java.util.Map;

/**
 * (SaGenvelocity)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-02 13:14:50
 */
public interface SaGenvelocityService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaGenvelocityPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaGenvelocityPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saGenvelocityPojo 实例对象
     * @return 实例对象
     */
    SaGenvelocityPojo insert(SaGenvelocityPojo saGenvelocityPojo);

    /**
     * 修改数据
     *
     * @param saGenvelocitypojo 实例对象
     * @return 实例对象
     */
    SaGenvelocityPojo update(SaGenvelocityPojo saGenvelocitypojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /* 审核数据
     *
     * @param saGenvelocityPojo 实例对象
     * @return 实例对象
     */
    SaGenvelocityPojo approval(SaGenvelocityPojo saGenvelocityPojo);

    List<String> getAllVelocity();

    List<SaGenvelocityPojo> getAllEntity();

    List<String> databases();

    PageInfo<Map<String, Object>> table(QueryParam queryParam, String database);


    List<Map<String, String>> getTableFields(String databases, String tableName);
}
