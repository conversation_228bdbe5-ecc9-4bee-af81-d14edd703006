package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaGitlogPojo;
import inks.service.sa.pms.domain.SaGitlogEntity;

import com.github.pagehelper.PageInfo;

/**
 * 代码提交日志(SaGitlog)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-24 10:47:29
 */
public interface SaGitlogService {


    SaGitlogPojo getEntity(String key);

    PageInfo<SaGitlogPojo> getPageList(QueryParam queryParam);

    SaGitlogPojo insert(SaGitlogPojo saGitlogPojo);

    SaGitlogPojo update(SaGitlogPojo saGitlogpojo);

    int delete(String key);
}
