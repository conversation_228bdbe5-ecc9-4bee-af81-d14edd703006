package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaGoodsPojo;

/**
 * 产品(SaGoods)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-18 09:18:55
 */
public interface SaGoodsService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaGoodsPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaGoodsPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saGoodsPojo 实例对象
     * @return 实例对象
     */
    SaGoodsPojo insert(SaGoodsPojo saGoodsPojo);

    /**
     * 修改数据
     *
     * @param saGoodspojo 实例对象
     * @return 实例对象
     */
    SaGoodsPojo update(SaGoodsPojo saGoodspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
