package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaIdeapoolPojo;
import inks.service.sa.pms.domain.SaIdeapoolEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * 创意池(SaIdeapool)表服务接口
 *
 * <AUTHOR>
 * @since 2024-12-04 15:23:23
 */
public interface SaIdeapoolService {


    SaIdeapoolPojo getEntity(String key);

    PageInfo<SaIdeapoolPojo> getPageList(QueryParam queryParam);

    SaIdeapoolPojo insert(SaIdeapoolPojo saIdeapoolPojo);

    SaIdeapoolPojo update(SaIdeapoolPojo saIdeapoolpojo);

    int delete(String key);

    List<SaIdeapoolPojo> getAllList();
}
