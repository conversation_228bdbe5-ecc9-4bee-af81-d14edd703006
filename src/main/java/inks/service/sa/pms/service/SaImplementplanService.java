package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaImplementplanPojo;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemdetailPojo;
import inks.service.sa.pms.domain.SaImplementplanEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 实施活动计划(SaImplementplan)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-06 14:40:18
 */
public interface SaImplementplanService {


    SaImplementplanPojo getEntity(String key);

    PageInfo<SaImplementplanitemdetailPojo> getPageList(QueryParam queryParam);

    SaImplementplanPojo getBillEntity(String key);

    PageInfo<SaImplementplanPojo> getBillList(QueryParam queryParam);

    PageInfo<SaImplementplanPojo> getPageTh(QueryParam queryParam);

    SaImplementplanPojo insert(SaImplementplanPojo saImplementplanPojo);

    SaImplementplanPojo update(SaImplementplanPojo saImplementplanpojo);

    int delete(String key);

}
