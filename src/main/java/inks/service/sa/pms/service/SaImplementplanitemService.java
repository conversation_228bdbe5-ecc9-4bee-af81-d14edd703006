package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemPojo;
import inks.service.sa.pms.domain.SaImplementplanitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 实施计划子表(SaImplementplanitem)表服务接口
 *
 * <AUTHOR>
 * @since 2024-10-06 14:40:34
 */
public interface SaImplementplanitemService {


    SaImplementplanitemPojo getEntity(String key);

    PageInfo<SaImplementplanitemPojo> getPageList(QueryParam queryParam);

    List<SaImplementplanitemPojo> getList(String Pid);  

    SaImplementplanitemPojo insert(SaImplementplanitemPojo saImplementplanitemPojo);

    SaImplementplanitemPojo update(SaImplementplanitemPojo saImplementplanitempojo);

    int delete(String key);

    SaImplementplanitemPojo clearNull(SaImplementplanitemPojo saImplementplanitempojo);
}
