package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaIntroPojo;

/**
 * 功能简介(SaIntro)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-24 08:56:14
 */
public interface SaIntroService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaIntroPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaIntroPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saIntroPojo 实例对象
     * @return 实例对象
     */
    SaIntroPojo insert(SaIntroPojo saIntroPojo);

    /**
     * 修改数据
     *
     * @param saIntropojo 实例对象
     * @return 实例对象
     */
    SaIntroPojo update(SaIntroPojo saIntropojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    SaIntroPojo getEntityByCode(String key);
}
