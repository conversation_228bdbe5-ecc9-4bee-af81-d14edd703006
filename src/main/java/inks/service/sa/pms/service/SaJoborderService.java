package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaJoborderPojo;

/**
 * 服务派工(SaJoborder)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-03 14:27:16
 */
public interface SaJoborderService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaJoborderPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaJoborderPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saJoborderPojo 实例对象
     * @return 实例对象
     */
    SaJoborderPojo insert(SaJoborderPojo saJoborderPojo);

    /**
     * 修改数据
     *
     * @param saJoborderpojo 实例对象
     * @return 实例对象
     */
    SaJoborderPojo update(SaJoborderPojo saJoborderpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 审核数据
     *
     * @param saJoborderPojo 实例对象
     * @return 实例对象
     */
    SaJoborderPojo approval(SaJoborderPojo saJoborderPojo);
}
