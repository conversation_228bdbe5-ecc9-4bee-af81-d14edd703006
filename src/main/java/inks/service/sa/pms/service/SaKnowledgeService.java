package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaKnowledgePojo;

/**
 * 知识库(SaKnowledge)表服务接口
 *
 * <AUTHOR>
 * @since 2022-11-23 13:12:49
 */
public interface SaKnowledgeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaKnowledgePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaKnowledgePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saKnowledgePojo 实例对象
     * @return 实例对象
     */
    SaKnowledgePojo insert(SaKnowledgePojo saKnowledgePojo);

    /**
     * 修改数据
     *
     * @param saKnowledgepojo 实例对象
     * @return 实例对象
     */
    SaKnowledgePojo update(SaKnowledgePojo saKnowledgepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 审核数据
     *
     * @param saKnowledgePojo 实例对象
     * @return 实例对象
     */
    SaKnowledgePojo approval(SaKnowledgePojo saKnowledgePojo);
}
