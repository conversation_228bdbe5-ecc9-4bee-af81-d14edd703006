package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaLnksharePojo;

/**
 * Lnk简书分享表(SaLnkshare)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-12 10:29:08
 */
public interface SaLnkshareService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaLnksharePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaLnksharePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saLnksharePojo 实例对象
     * @return 实例对象
     */
    SaLnksharePojo insert(SaLnksharePojo saLnksharePojo);

    /**
     * 修改数据
     *
     * @param saLnksharepojo 实例对象
     * @return 实例对象
     */
    SaLnksharePojo update(SaLnksharePojo saLnksharepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
