package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaLnkstarPojo;

/**
 * (SaLnkstar)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-18 09:48:07
 */
public interface SaLnkstarService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaLnkstarPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaLnkstarPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saLnkstarPojo 实例对象
     * @return 实例对象
     */
    SaLnkstarPojo insert(SaLnkstarPojo saLnkstarPojo);

    /**
     * 修改数据
     *
     * @param saLnkstarpojo 实例对象
     * @return 实例对象
     */
    SaLnkstarPojo update(SaLnkstarPojo saLnkstarpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
