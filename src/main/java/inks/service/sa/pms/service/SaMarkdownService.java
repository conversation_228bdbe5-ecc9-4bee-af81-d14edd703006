package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMarkdownPojo;

/**
 * MarkDown(LNK简书)(SaMarkdown)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-19 09:43:26
 */
public interface SaMarkdownService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMarkdownPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaMarkdownPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saMarkdownPojo 实例对象
     * @return 实例对象
     */
    SaMarkdownPojo insert(SaMarkdownPojo saMarkdownPojo);

    /**
     * 修改数据
     *
     * @param saMarkdownpojo 实例对象
     * @return 实例对象
     */
    SaMarkdownPojo update(SaMarkdownPojo saMarkdownpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 审核数据
     *
     * @param saMarkdownPojo 实例对象
     * @return 实例对象
     */
    SaMarkdownPojo approval(SaMarkdownPojo saMarkdownPojo);

    PageInfo<SaMarkdownPojo> getMdByProjectId(QueryParam queryParam);
}
