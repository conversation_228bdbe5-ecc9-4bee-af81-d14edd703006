package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMarkdowndocPojo;

/**
 * MarkDown(Doc文档)(SaMarkdowndoc)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:26
 */
public interface SaMarkdowndocService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMarkdowndocPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaMarkdowndocPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saMarkdowndocPojo 实例对象
     * @return 实例对象
     */
    SaMarkdowndocPojo insert(SaMarkdowndocPojo saMarkdowndocPojo);

    /**
     * 修改数据
     *
     * @param saMarkdowndocpojo 实例对象
     * @return 实例对象
     */
    SaMarkdowndocPojo update(SaMarkdowndocPojo saMarkdowndocpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 审核数据
     *
     * @param saMarkdowndocPojo 实例对象
     * @return 实例对象
     */
    SaMarkdowndocPojo approval(SaMarkdowndocPojo saMarkdowndocPojo);

    PageInfo<SaMarkdowndocPojo> getMdByProjectId(QueryParam queryParam);
}

