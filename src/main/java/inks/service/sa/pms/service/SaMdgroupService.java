package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMarkdownPojo;
import inks.service.sa.pms.domain.pojo.SaMdgroupPojo;

import java.util.List;

/**
 * MD文档分组(SaMdgroup)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-08 11:23:51
 */
public interface SaMdgroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMdgroupPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaMdgroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saMdgroupPojo 实例对象
     * @return 实例对象
     */
    SaMdgroupPojo insert(SaMdgroupPojo saMdgroupPojo);

    /**
     * 修改数据
     *
     * @param saMdgrouppojo 实例对象
     * @return 实例对象
     */
    SaMdgroupPojo update(SaMdgroupPojo saMdgrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<SaMarkdownPojo> getMarkdownsByGroupId(String key);
}
