package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMarkdowndocPojo;
import inks.service.sa.pms.domain.pojo.SaMdgroupdocPojo;

import java.util.List;

/**
 * MarkDown分组(Doc文档)(SaMdgroupdoc)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:40
 */
public interface SaMdgroupdocService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMdgroupdocPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaMdgroupdocPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saMdgroupdocPojo 实例对象
     * @return 实例对象
     */
    SaMdgroupdocPojo insert(SaMdgroupdocPojo saMdgroupdocPojo);

    /**
     * 修改数据
     *
     * @param saMdgroupdocpojo 实例对象
     * @return 实例对象
     */
    SaMdgroupdocPojo update(SaMdgroupdocPojo saMdgroupdocpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<SaMarkdowndocPojo> getMarkdownsByGroupId(String key);
}
