package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMdgroupPojo;
import inks.service.sa.pms.domain.pojo.SaMdprojectPojo;

import java.util.List;

/**
 * MD文档项目(SaMdproject)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-08 11:23:51
 */
public interface SaMdprojectService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMdprojectPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaMdprojectPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saMdprojectPojo 实例对象
     * @return 实例对象
     */
    SaMdprojectPojo insert(SaMdprojectPojo saMdprojectPojo);

    /**
     * 修改数据
     *
     * @param saMdprojectpojo 实例对象
     * @return 实例对象
     */
    SaMdprojectPojo update(SaMdprojectPojo saMdprojectpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<SaMdgroupPojo> getMdgroupsByProjectId(String key);
}
