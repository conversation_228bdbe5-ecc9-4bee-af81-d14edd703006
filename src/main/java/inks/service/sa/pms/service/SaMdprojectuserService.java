package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMdprojectuserPojo;

/**
 * (用户-MD项目)可视权限(SaMdprojectuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-05-08 13:07:40
 */
public interface SaMdprojectuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMdprojectuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaMdprojectuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saMdprojectuserPojo 实例对象
     * @return 实例对象
     */
    SaMdprojectuserPojo insert(SaMdprojectuserPojo saMdprojectuserPojo);

    /**
     * 修改数据
     *
     * @param saMdprojectuserpojo 实例对象
     * @return 实例对象
     */
    SaMdprojectuserPojo update(SaMdprojectuserPojo saMdprojectuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
