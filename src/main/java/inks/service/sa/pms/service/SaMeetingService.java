package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetingPojo;
import inks.service.sa.pms.domain.pojo.SaMeetingitemdetailPojo;
import inks.service.sa.pms.domain.SaMeetingEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;

/**
 * 会议主表(SaMeeting)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 09:46:05
 */
public interface SaMeetingService {


    SaMeetingPojo getEntity(String key);

    PageInfo<SaMeetingitemdetailPojo> getPageList(QueryParam queryParam);

    SaMeetingPojo getBillEntity(String key);

    PageInfo<SaMeetingPojo> getBillList(QueryParam queryParam);

    PageInfo<SaMeetingPojo> getPageTh(QueryParam queryParam);

    SaMeetingPojo insert(SaMeetingPojo saMeetingPojo);

    SaMeetingPojo update(SaMeetingPojo saMeetingpojo);

    int delete(String key);


     SaMeetingPojo approval(SaMeetingPojo saMeetingPojo);
}
