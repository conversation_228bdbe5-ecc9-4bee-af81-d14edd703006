package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetingitemPojo;
import inks.service.sa.pms.domain.SaMeetingitemEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 会议子表-议题(SaMeetingitem)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 09:50:15
 */
public interface SaMeetingitemService {


    SaMeetingitemPojo getEntity(String key);

    PageInfo<SaMeetingitemPojo> getPageList(QueryParam queryParam);

    List<SaMeetingitemPojo> getList(String Pid);  

    SaMeetingitemPojo insert(SaMeetingitemPojo saMeetingitemPojo);

    SaMeetingitemPojo update(SaMeetingitemPojo saMeetingitempojo);

    int delete(String key);

    SaMeetingitemPojo clearNull(SaMeetingitemPojo saMeetingitempojo);
}
