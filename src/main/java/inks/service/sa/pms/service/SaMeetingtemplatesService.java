package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetingtemplatesPojo;
import inks.service.sa.pms.domain.SaMeetingtemplatesEntity;

import com.github.pagehelper.PageInfo;

/**
 * 会议模板表(SaMeetingtemplates)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 09:46:22
 */
public interface SaMeetingtemplatesService {


    SaMeetingtemplatesPojo getEntity(String key);

    PageInfo<SaMeetingtemplatesPojo> getPageList(QueryParam queryParam);

    SaMeetingtemplatesPojo insert(SaMeetingtemplatesPojo saMeetingtemplatesPojo);

    SaMeetingtemplatesPojo update(SaMeetingtemplatesPojo saMeetingtemplatespojo);

    int delete(String key);
}
