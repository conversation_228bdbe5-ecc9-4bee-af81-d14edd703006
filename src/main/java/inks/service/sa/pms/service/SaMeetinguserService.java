package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMeetinguserPojo;
import inks.service.sa.pms.domain.SaMeetinguserEntity;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import com.github.pagehelper.PageInfo;
import java.util.List;
/**
 * 会议子表-人员(SaMeetinguser)表服务接口
 *
 * <AUTHOR>
 * @since 2025-06-17 09:50:17
 */
public interface SaMeetinguserService {


    SaMeetinguserPojo getEntity(String key);

    PageInfo<SaMeetinguserPojo> getPageList(QueryParam queryParam);

    List<SaMeetinguserPojo> getList(String Pid);  

    SaMeetinguserPojo insert(SaMeetinguserPojo saMeetinguserPojo);

    SaMeetinguserPojo update(SaMeetinguserPojo saMeetinguserpojo);

    int delete(String key);

    SaMeetinguserPojo clearNull(SaMeetinguserPojo saMeetinguserpojo);
}
