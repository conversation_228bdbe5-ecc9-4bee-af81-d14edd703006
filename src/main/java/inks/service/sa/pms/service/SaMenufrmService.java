package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMenufrmPojo;

import java.util.List;

/**
 * Frm导航(SaMenufrm)表服务接口
 *
 * <AUTHOR>
 * @since 2024-05-14 09:00:49
 */
public interface SaMenufrmService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMenufrmPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaMenufrmPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saMenufrmPojo 实例对象
     * @return 实例对象
     */
    SaMenufrmPojo insert(SaMenufrmPojo saMenufrmPojo);

    /**
     * 修改数据
     *
     * @param saMenufrmpojo 实例对象
     * @return 实例对象
     */
    SaMenufrmPojo update(SaMenufrmPojo saMenufrmpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<SaMenufrmPojo> getAllMenus();
}
