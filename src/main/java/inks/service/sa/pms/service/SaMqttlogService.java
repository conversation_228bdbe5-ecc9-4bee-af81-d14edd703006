package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaMqttlogPojo;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

/**
 * MQTT日志(SaMqttlog)表服务接口
 *
 * <AUTHOR>
 * @since 2024-02-26 12:57:20
 */
@Service
public interface SaMqttlogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaMqttlogPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaMqttlogPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saMqttlogPojo 实例对象
     * @return 实例对象
     */
    @Async
    SaMqttlogPojo insert(SaMqttlogPojo saMqttlogPojo);

    /**
     * 修改数据
     *
     * @param saMqttlogpojo 实例对象
     * @return 实例对象
     */
    SaMqttlogPojo update(SaMqttlogPojo saMqttlogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
