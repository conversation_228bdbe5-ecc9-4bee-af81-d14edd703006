package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaNotebookPojo;
import inks.service.sa.pms.domain.SaNotebookEntity;

import com.github.pagehelper.PageInfo;

/**
 * 笔记本(SaNotebook)表服务接口
 *
 * <AUTHOR>
 * @since 2024-06-22 16:39:51
 */
public interface SaNotebookService {


    SaNotebookPojo getEntity(String key);

    PageInfo<SaNotebookPojo> getPageList(QueryParam queryParam);

    SaNotebookPojo insert(SaNotebookPojo saNotebookPojo);

    SaNotebookPojo update(SaNotebookPojo saNotebookpojo);

    int delete(String key);

    SaNotebookPojo getMyNotebook(String userid);
}
