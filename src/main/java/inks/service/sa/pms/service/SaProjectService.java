package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaEngineerPojo;
import inks.service.sa.pms.domain.pojo.SaProjectPojo;
import inks.service.sa.pms.domain.pojo.SaProjectitemdetailPojo;

/**
 * 工程项目(SaProject)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-26 14:31:00
 */
public interface SaProjectService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaProjectitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaProjectPojo> getBillList(QueryParam queryParam, String projectFilter, boolean isadmin, String engineerid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaProjectPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saProjectPojo 实例对象
     * @return 实例对象
     */
    SaProjectPojo insert(SaProjectPojo saProjectPojo, String engineerid);

    /**
     * 修改数据
     *
     * @param saProjectpojo 实例对象
     * @return 实例对象
     */
    SaProjectPojo update(SaProjectPojo saProjectpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    SaProjectPojo getBillEntityByProjectName(String nanno);
}
