package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaProjectitemPojo;

import java.util.List;

/**
 * 项目参与人员(SaProjectitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-26 14:28:41
 */
public interface SaProjectitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaProjectitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaProjectitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saProjectitemPojo 实例对象
     * @return 实例对象
     */
    SaProjectitemPojo insert(SaProjectitemPojo saProjectitemPojo);

    /**
     * 修改数据
     *
     * @param saProjectitempojo 实例对象
     * @return 实例对象
     */
    SaProjectitemPojo update(SaProjectitemPojo saProjectitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 修改数据
     *
     * @param saProjectitempojo 实例对象
     * @return 实例对象
     */
    SaProjectitemPojo clearNull(SaProjectitemPojo saProjectitempojo);
}
