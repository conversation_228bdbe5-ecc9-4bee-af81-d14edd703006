package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaProjectlabelPojo;

import java.util.List;

/**
 * 项目标签(SaProjectlabel)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-26 14:28:41
 */
public interface SaProjectlabelService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectlabelPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaProjectlabelPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaProjectlabelPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saProjectlabelPojo 实例对象
     * @return 实例对象
     */
    SaProjectlabelPojo insert(SaProjectlabelPojo saProjectlabelPojo);

    /**
     * 修改数据
     *
     * @param saProjectlabelpojo 实例对象
     * @return 实例对象
     */
    SaProjectlabelPojo update(SaProjectlabelPojo saProjectlabelpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 修改数据
     *
     * @param saProjectlabelpojo 实例对象
     * @return 实例对象
     */
    SaProjectlabelPojo clearNull(SaProjectlabelPojo saProjectlabelpojo);
}
