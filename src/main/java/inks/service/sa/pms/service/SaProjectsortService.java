package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaProjectsortPojo;

/**
 * 个人常用项目排序(SaProjectsort)表服务接口
 *
 * <AUTHOR>
 * @since 2023-10-25 13:07:31
 */
public interface SaProjectsortService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectsortPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaProjectsortPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saProjectsortPojo 实例对象
     * @return 实例对象
     */
    SaProjectsortPojo insert(SaProjectsortPojo saProjectsortPojo);

    /**
     * 修改数据
     *
     * @param saProjectsortpojo 实例对象
     * @return 实例对象
     */
    SaProjectsortPojo update(SaProjectsortPojo saProjectsortpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    void deleteByProjectIdAndProjectItemId(String id, String itemid);
}
