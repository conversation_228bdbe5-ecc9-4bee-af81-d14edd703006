package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaProjectstatusPojo;

import java.util.List;

/**
 * 项目状态(SaProjectstatus)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-26 14:28:41
 */
public interface SaProjectstatusService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjectstatusPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaProjectstatusPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaProjectstatusPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saProjectstatusPojo 实例对象
     * @return 实例对象
     */
    SaProjectstatusPojo insert(SaProjectstatusPojo saProjectstatusPojo);

    /**
     * 修改数据
     *
     * @param saProjectstatuspojo 实例对象
     * @return 实例对象
     */
    SaProjectstatusPojo update(SaProjectstatusPojo saProjectstatuspojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 修改数据
     *
     * @param saProjectstatuspojo 实例对象
     * @return 实例对象
     */
    SaProjectstatusPojo clearNull(SaProjectstatusPojo saProjectstatuspojo);
}
