package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaProjprofilePojo;

/**
 * (SaProjprofile)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-05 16:25:35
 */
public interface SaProjprofileService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaProjprofilePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaProjprofilePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saProjprofilePojo 实例对象
     * @return 实例对象
     */
    SaProjprofilePojo insert(SaProjprofilePojo saProjprofilePojo);

    /**
     * 修改数据
     *
     * @param saProjprofilepojo 实例对象
     * @return 实例对象
     */
    SaProjprofilePojo update(SaProjprofilePojo saProjprofilepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
