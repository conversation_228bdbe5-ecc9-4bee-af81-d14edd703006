package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaReleasePojo;
import inks.service.sa.pms.domain.pojo.SaReleaseitemdetailPojo;

/**
 * (SaRelease)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-14 10:03:09
 */
public interface SaReleaseService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReleasePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaReleaseitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReleasePojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaReleasePojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaReleasePojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saReleasePojo 实例对象
     * @return 实例对象
     */
    SaReleasePojo insert(SaReleasePojo saReleasePojo);

    /**
     * 修改数据
     *
     * @param saReleasepojo 实例对象
     * @return 实例对象
     */
    SaReleasePojo update(SaReleasePojo saReleasepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 审核数据
     *
     * @param saReleasePojo 实例对象
     * @return 实例对象
     */
    SaReleasePojo approval(SaReleasePojo saReleasePojo);

    boolean checkExist(String remark);
}
