package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaReleaseitemPojo;

import java.util.List;

/**
 * (SaReleaseitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-14 10:05:56
 */
public interface SaReleaseitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReleaseitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaReleaseitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaReleaseitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saReleaseitemPojo 实例对象
     * @return 实例对象
     */
    SaReleaseitemPojo insert(SaReleaseitemPojo saReleaseitemPojo);

    /**
     * 修改数据
     *
     * @param saReleaseitempojo 实例对象
     * @return 实例对象
     */
    SaReleaseitemPojo update(SaReleaseitemPojo saReleaseitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 修改数据
     *
     * @param saReleaseitempojo 实例对象
     * @return 实例对象
     */
    SaReleaseitemPojo clearNull(SaReleaseitemPojo saReleaseitempojo);
}
