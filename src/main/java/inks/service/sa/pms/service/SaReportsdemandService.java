package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaReportsdemandPojo;
import inks.service.sa.pms.domain.SaReportsdemandEntity;

import com.github.pagehelper.PageInfo;

/**
 * 报表模版需求(SaReportsdemand)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-22 15:30:01
 */
public interface SaReportsdemandService {


    SaReportsdemandPojo getEntity(String key);

    PageInfo<SaReportsdemandPojo> getPageList(QueryParam queryParam);

    SaReportsdemandPojo insert(SaReportsdemandPojo saReportsdemandPojo);

    SaReportsdemandPojo update(SaReportsdemandPojo saReportsdemandpojo);

    int delete(String key);

    SaReportsdemandPojo getEntityBySerCode(String key);
}
