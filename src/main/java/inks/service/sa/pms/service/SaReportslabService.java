package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaReportslabPojo;

import java.util.List;

/**
 * 报表库(SaReportslab)表服务接口
 *
 * <AUTHOR>
 * @since 2024-01-23 09:44:16
 */
public interface SaReportslabService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaReportslabPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaReportslabPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saReportslabPojo 实例对象
     * @return 实例对象
     */
    SaReportslabPojo insert(SaReportslabPojo saReportslabPojo);

    /**
     * 修改数据
     *
     * @param saReportslabpojo 实例对象
     * @return 实例对象
     */
    SaReportslabPojo update(SaReportslabPojo saReportslabpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<SaReportslabPojo> pullDefault(String code, LoginUser loginUser);

    PageInfo<SaReportslabPojo> getPageListAll(QueryParam queryParam);

    List<SaReportslabPojo> getListByModuleCode(String moduleCode);

    String getGrfDataByShareCode(String key);

    String initShareCode();

    String getShareCode();
}
