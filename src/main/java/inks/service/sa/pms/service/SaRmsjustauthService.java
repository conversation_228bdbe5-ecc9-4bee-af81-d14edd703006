package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.service.sa.pms.domain.pojo.SaRmsjustauthPojo;
import inks.service.sa.pms.domain.SaRmsjustauthEntity;

import com.github.pagehelper.PageInfo;

/**
 * RMS第三方登录(SaRmsjustauth)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-29 10:59:38
 */
public interface SaRmsjustauthService {


    SaRmsjustauthPojo getEntity(String key);

    PageInfo<SaRmsjustauthPojo> getPageList(QueryParam queryParam);

    SaRmsjustauthPojo insert(SaRmsjustauthPojo saRmsjustauthPojo);

    SaRmsjustauthPojo update(SaRmsjustauthPojo saRmsjustauthpojo);

    int delete(String key);

    int deleteByOpenid(String openid);

    SaRmsjustauthPojo getEntityByUserid(String userid, String type);

    SaRmsjustauthPojo getJustauthByUuid(String callbackuuid, String type);
}
