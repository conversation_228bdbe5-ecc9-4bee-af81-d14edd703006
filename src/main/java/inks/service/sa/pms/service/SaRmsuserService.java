package inks.service.sa.pms.service;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import inks.service.sa.pms.domain.SaRmsuserEntity;

import com.github.pagehelper.PageInfo;
import inks.service.sa.pms.domain.pojo.SaScmuserPojo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * RMS用户(SaRmsuser)表服务接口
 *
 * <AUTHOR>
 * @since 2024-09-29 10:59:38
 */
public interface SaRmsuserService {


    SaRmsuserPojo getEntity(String key);

    PageInfo<SaRmsuserPojo> getPageList(QueryParam queryParam);

    SaRmsuserPojo insert(SaRmsuserPojo saRmsuserPojo);

    SaRmsuserPojo update(SaRmsuserPojo saRmsuserpojo);

    int delete(String key);

    SaRmsuserPojo getEntityByUserName(String username);

    List<SaRmsuserPojo> getPageListByCustomer(String groupid);

    SaRmsuserPojo getEntityByOpenid(String openid);

    LoginUser login(String userName, String password, HttpServletRequest request);

    LoginUser scanLogin(String openid, String key, HttpServletRequest request);
}
