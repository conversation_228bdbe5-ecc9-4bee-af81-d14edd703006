package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaScmjustauthPojo;

/**
 * SCM第三方登录(SaScmjustauth)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:31
 */
public interface SaScmjustauthService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaScmjustauthPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaScmjustauthPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saScmjustauthPojo 实例对象
     * @return 实例对象
     */
    SaScmjustauthPojo insert(SaScmjustauthPojo saScmjustauthPojo);

    /**
     * 修改数据
     *
     * @param saScmjustauthpojo 实例对象
     * @return 实例对象
     */
    SaScmjustauthPojo update(SaScmjustauthPojo saScmjustauthpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    int deleteByOpenid(String openid);
}
