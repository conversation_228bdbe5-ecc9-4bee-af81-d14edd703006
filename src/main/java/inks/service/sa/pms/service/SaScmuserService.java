package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaScmuserPojo;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * SCM用户(SaScmuser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:31
 */
public interface SaScmuserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaScmuserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaScmuserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saScmuserPojo 实例对象
     * @return 实例对象
     */
    SaScmuserPojo insert(SaScmuserPojo saScmuserPojo);

    /**
     * 修改数据
     *
     * @param saScmuserpojo 实例对象
     * @return 实例对象
     */
    SaScmuserPojo update(SaScmuserPojo saScmuserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    SaScmuserPojo getEntityByUserName(String username);

    List<SaScmuserPojo> getPageListByCustomer(String groupid);

    SaScmuserPojo getEntityByOpenid(String openid);

    LoginUser login(String userName, String password, HttpServletRequest request);

    LoginUser scanLogin(String openid, String key, HttpServletRequest request);
}
