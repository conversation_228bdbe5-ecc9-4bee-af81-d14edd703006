package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaSolutionPojo;

/**
 * 解决方案(SaSolution)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-14 16:03:36
 */
public interface SaSolutionService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaSolutionPojo getEntity(String key);

    SaSolutionPojo getEntityBySolutionCode(String code);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaSolutionPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saSolutionPojo 实例对象
     * @return 实例对象
     */
    SaSolutionPojo insert(SaSolutionPojo saSolutionPojo);

    /**
     * 修改数据
     *
     * @param saSolutionpojo 实例对象
     * @return 实例对象
     */
    SaSolutionPojo update(SaSolutionPojo saSolutionpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

}
