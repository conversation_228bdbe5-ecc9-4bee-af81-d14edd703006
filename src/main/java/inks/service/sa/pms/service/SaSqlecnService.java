package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaSqlecnPojo;
import inks.service.sa.pms.domain.SaSqlecnEntity;

import com.github.pagehelper.PageInfo;

import java.util.List;

/**
 * SQL变更(SaSqlecn)表服务接口
 *
 * <AUTHOR>
 * @since 2025-02-19 14:27:49
 */
public interface SaSqlecnService {


    SaSqlecnPojo getEntity(String key);

    PageInfo<SaSqlecnPojo> getPageList(QueryParam queryParam);

    SaSqlecnPojo insert(SaSqlecnPojo saSqlecnPojo);

    SaSqlecnPojo update(SaSqlecnPojo saSqlecnpojo);

    int delete(String key);

     SaSqlecnPojo approval(SaSqlecnPojo saSqlecnPojo);

    void updateOaflowmark(SaSqlecnPojo saDemandsubmitPojo);

    List<SaSqlecnPojo> pullList(List<String> codeList, Long timestamp, int size);
}
