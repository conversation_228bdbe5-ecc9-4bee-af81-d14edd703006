package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaTodoPojo;

/**
 * Todo(SaTodo)表服务接口
 *
 * <AUTHOR>
 * @since 2023-02-18 11:12:38
 */
public interface SaTodoService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTodoPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTodoPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saTodoPojo 实例对象
     * @return 实例对象
     */
    SaTodoPojo insert(SaTodoPojo saTodoPojo);

    /**
     * 修改数据
     *
     * @param saTodopojo 实例对象
     * @return 实例对象
     */
    SaTodoPojo update(SaTodoPojo saTodopojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    SaTodoPojo start(String key, LoginUser loginUser);

    SaTodoPojo finish(String key, LoginUser loginUser);

    SaTodoPojo recall(String key, LoginUser loginUser);
}
