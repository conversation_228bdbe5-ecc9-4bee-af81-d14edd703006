package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaTodotypePojo;

/**
 * (SaTodotype)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-10 10:53:33
 */
public interface SaTodotypeService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTodotypePojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTodotypePojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saTodotypePojo 实例对象
     * @return 实例对象
     */
    SaTodotypePojo insert(SaTodotypePojo saTodotypePojo);

    /**
     * 修改数据
     *
     * @param saTodotypepojo 实例对象
     * @return 实例对象
     */
    SaTodotypePojo update(SaTodotypePojo saTodotypepojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
