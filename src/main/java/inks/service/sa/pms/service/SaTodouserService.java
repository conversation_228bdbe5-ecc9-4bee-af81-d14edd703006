package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaTodouserPojo;

/**
 * Todo用户列表(SaTodouser)表服务接口
 *
 * <AUTHOR>
 * @since 2023-03-03 14:07:16
 */
public interface SaTodouserService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTodouserPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTodouserPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saTodouserPojo 实例对象
     * @return 实例对象
     */
    SaTodouserPojo insert(SaTodouserPojo saTodouserPojo);

    /**
     * 修改数据
     *
     * @param saTodouserpojo 实例对象
     * @return 实例对象
     */
    SaTodouserPojo update(SaTodouserPojo saTodouserpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
