package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaTrackingPojo;
import inks.service.sa.pms.domain.pojo.SaTrackingitemPojo;
import inks.service.sa.pms.domain.pojo.SaTrackingitemdetailPojo;

import java.util.List;

/**
 * 跟踪表主表(SaTracking)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-24 09:35:51
 */
public interface SaTrackingService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTrackingPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTrackingitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTrackingPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTrackingPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTrackingPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saTrackingPojo 实例对象
     * @return 实例对象
     */
    SaTrackingPojo insert(SaTrackingPojo saTrackingPojo);

    /**
     * 修改数据
     *
     * @param saTrackingpojo 实例对象
     * @return 实例对象
     */
    SaTrackingPojo update(SaTrackingPojo saTrackingpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    SaTrackingPojo disannul(List<SaTrackingitemPojo> lst, Integer type, LoginUser loginUser);

    SaTrackingPojo closed(List<SaTrackingitemPojo> lst, Integer type, LoginUser loginUser);
}
