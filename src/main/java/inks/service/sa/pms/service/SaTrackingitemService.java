package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaTrackingitemPojo;

import java.util.List;

/**
 * 跟踪表子表(SaTrackingitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-24 09:36:07
 */
public interface SaTrackingitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTrackingitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTrackingitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaTrackingitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saTrackingitemPojo 实例对象
     * @return 实例对象
     */
    SaTrackingitemPojo insert(SaTrackingitemPojo saTrackingitemPojo);

    /**
     * 修改数据
     *
     * @param saTrackingitempojo 实例对象
     * @return 实例对象
     */
    SaTrackingitemPojo update(SaTrackingitemPojo saTrackingitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 修改数据
     *
     * @param saTrackingitempojo 实例对象
     * @return 实例对象
     */
    SaTrackingitemPojo clearNull(SaTrackingitemPojo saTrackingitempojo);
}
