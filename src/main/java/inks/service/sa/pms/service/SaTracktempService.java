package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaTracktempPojo;

/**
 * 跟踪模板表(SaTracktemp)表服务接口
 *
 * <AUTHOR>
 * @since 2023-06-25 09:57:24
 */
public interface SaTracktempService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaTracktempPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaTracktempPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saTracktempPojo 实例对象
     * @return 实例对象
     */
    SaTracktempPojo insert(SaTracktempPojo saTracktempPojo);

    /**
     * 修改数据
     *
     * @param saTracktemppojo 实例对象
     * @return 实例对象
     */
    SaTracktempPojo update(SaTracktempPojo saTracktemppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
