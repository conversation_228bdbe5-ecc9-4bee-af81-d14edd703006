package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaUsergroupPojo;

/**
 * (SaUsergroup)表服务接口
 *
 * <AUTHOR>
 * @since 2023-04-24 16:01:27
 */
public interface SaUsergroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaUsergroupPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaUsergroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saUsergroupPojo 实例对象
     * @return 实例对象
     */
    SaUsergroupPojo insert(SaUsergroupPojo saUsergroupPojo);

    /**
     * 修改数据
     *
     * @param saUsergrouppojo 实例对象
     * @return 实例对象
     */
    SaUsergroupPojo update(SaUsergroupPojo saUsergrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);
}
