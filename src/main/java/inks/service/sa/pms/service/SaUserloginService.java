package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaUserloginPojo;

/**
 * 用户登录(SaUserlogin)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:31
 */
public interface SaUserloginService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaUserloginPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaUserloginPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saUserloginPojo 实例对象
     * @return 实例对象
     */
    SaUserloginPojo insert(SaUserloginPojo saUserloginPojo);

    /**
     * 修改数据
     *
     * @param saUserloginpojo 实例对象
     * @return 实例对象
     */
    SaUserloginPojo update(SaUserloginPojo saUserloginpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);
}
