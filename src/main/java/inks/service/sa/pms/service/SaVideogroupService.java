package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaVideogroupPojo;

import java.util.List;

/**
 * 视频目录(SaVideogroup)表服务接口
 *
 * <AUTHOR>
 * @since 2022-12-09 08:58:49
 */
public interface SaVideogroupService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaVideogroupPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaVideogroupPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saVideogroupPojo 实例对象
     * @return 实例对象
     */
    SaVideogroupPojo insert(SaVideogroupPojo saVideogroupPojo);

    /**
     * 修改数据
     *
     * @param saVideogrouppojo 实例对象
     * @return 实例对象
     */
    SaVideogroupPojo update(SaVideogroupPojo saVideogrouppojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    List<SaVideogroupPojo> getAllGroup();
}
