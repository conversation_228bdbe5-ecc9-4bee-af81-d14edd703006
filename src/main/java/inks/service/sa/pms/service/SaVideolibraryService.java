package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.FileInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaVideolibraryPojo;
import org.springframework.web.multipart.MultipartFile;

/**
 * 视频信息表(SaVideolibrary)表服务接口
 *
 * <AUTHOR>
 * @since 2022-10-20 10:25:26
 */
public interface SaVideolibraryService {


    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaVideolibraryPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaVideolibraryPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saVideolibraryPojo 实例对象
     * @return 实例对象
     */
    SaVideolibraryPojo insert(SaVideolibraryPojo saVideolibraryPojo);

    /**
     * 修改数据
     *
     * @param saVideolibrarypojo 实例对象
     * @return 实例对象
     */
    SaVideolibraryPojo update(SaVideolibraryPojo saVideolibrarypojo) throws Exception;

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key) throws Exception;

    /**
     * 审核数据
     *
     * @param saVideolibraryPojo 实例对象
     * @return 实例对象
     */
    SaVideolibraryPojo approval(SaVideolibraryPojo saVideolibraryPojo);
}
