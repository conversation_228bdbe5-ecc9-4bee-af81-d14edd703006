package inks.service.sa.pms.service;

import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaWeeklyPojo;
import inks.service.sa.pms.domain.SaWeeklyEntity;

import com.github.pagehelper.PageInfo;

/**
 * 工作周报(SaWeekly)表服务接口
 *
 * <AUTHOR>
 * @since 2025-07-21 15:06:44
 */
public interface SaWeeklyService {


    SaWeeklyPojo getEntity(String key);

    PageInfo<SaWeeklyPojo> getPageList(QueryParam queryParam);

    SaWeeklyPojo insert(SaWeeklyPojo saWeeklyPojo);

    SaWeeklyPojo update(SaWeeklyPojo saWeeklypojo);

    int delete(String key);

     SaWeeklyPojo approval(SaWeeklyPojo saWeeklyPojo);
}
