package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaWorklogPojo;
import inks.service.sa.pms.domain.pojo.SaWorklogitemdetailPojo;

/**
 * (SaWorklog)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-04 13:59:14
 */
public interface SaWorklogService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaWorklogPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaWorklogitemdetailPojo> getPageList(QueryParam queryParam);

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaWorklogPojo getBillEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaWorklogPojo> getBillList(QueryParam queryParam);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaWorklogPojo> getPageTh(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param saWorklogPojo 实例对象
     * @return 实例对象
     */
    SaWorklogPojo insert(SaWorklogPojo saWorklogPojo);

    /**
     * 修改数据
     *
     * @param saWorklogpojo 实例对象
     * @return 实例对象
     */
    SaWorklogPojo update(SaWorklogPojo saWorklogpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    boolean checkWorklogTitle(String title);
}
