package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaWorklogitemPojo;

import java.util.List;

/**
 * 工作日志今日子表(SaWorklogitem)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-04 12:58:20
 */
public interface SaWorklogitemService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaWorklogitemPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaWorklogitemPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaWorklogitemPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saWorklogitemPojo 实例对象
     * @return 实例对象
     */
    SaWorklogitemPojo insert(SaWorklogitemPojo saWorklogitemPojo);

    /**
     * 修改数据
     *
     * @param saWorklogitempojo 实例对象
     * @return 实例对象
     */
    SaWorklogitemPojo update(SaWorklogitemPojo saWorklogitempojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 修改数据
     *
     * @param saWorklogitempojo 实例对象
     * @return 实例对象
     */
    SaWorklogitemPojo clearNull(SaWorklogitemPojo saWorklogitempojo);
}
