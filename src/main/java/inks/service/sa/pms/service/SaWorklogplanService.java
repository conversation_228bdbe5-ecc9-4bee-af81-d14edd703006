package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.SaWorklogplanPojo;

import java.util.List;

/**
 * 工作日志计划子表(SaWorklogplan)表服务接口
 *
 * <AUTHOR>
 * @since 2023-09-04 14:31:24
 */
public interface SaWorklogplanService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    SaWorklogplanPojo getEntity(String key);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<SaWorklogplanPojo> getPageList(QueryParam queryParam);

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    List<SaWorklogplanPojo> getList(String Pid);

    /**
     * 新增数据
     *
     * @param saWorklogplanPojo 实例对象
     * @return 实例对象
     */
    SaWorklogplanPojo insert(SaWorklogplanPojo saWorklogplanPojo);

    /**
     * 修改数据
     *
     * @param saWorklogplanpojo 实例对象
     * @return 实例对象
     */
    SaWorklogplanPojo update(SaWorklogplanPojo saWorklogplanpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key);

    /**
     * 修改数据
     *
     * @param saWorklogplanpojo 实例对象
     * @return 实例对象
     */
    SaWorklogplanPojo clearNull(SaWorklogplanPojo saWorklogplanpojo);
}
