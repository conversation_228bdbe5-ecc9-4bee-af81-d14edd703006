package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.UtsWxeapprPojo;

import java.util.List;

/**
 * 企业微审核(UtsWxeappr)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:08
 */
public interface UtsWxeapprService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxeapprPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsWxeapprPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsWxeapprPojo 实例对象
     * @return 实例对象
     */
    UtsWxeapprPojo insert(UtsWxeapprPojo utsWxeapprPojo);

    /**
     * 修改数据
     *
     * @param utsWxeapprpojo 实例对象
     * @return 实例对象
     */
    UtsWxeapprPojo update(UtsWxeapprPojo utsWxeapprpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    List<UtsWxeapprPojo> getListByModuleCode(String moduleCode, String tid);
}
