package inks.service.sa.pms.service;

import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.service.sa.pms.domain.pojo.UtsWxeapprrecPojo;

import java.util.List;

/**
 * 微信审批记录(UtsWxeapprrec)表服务接口
 *
 * <AUTHOR>
 * @since 2023-01-10 11:12:19
 */
public interface UtsWxeapprrecService {

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxeapprrecPojo getEntity(String key, String tid);

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    PageInfo<UtsWxeapprrecPojo> getPageList(QueryParam queryParam);

    /**
     * 新增数据
     *
     * @param utsWxeapprrecPojo 实例对象
     * @return 实例对象
     */
    UtsWxeapprrecPojo insert(UtsWxeapprrecPojo utsWxeapprrecPojo);

    /**
     * 修改数据
     *
     * @param utsWxeapprrecpojo 实例对象
     * @return 实例对象
     */
    UtsWxeapprrecPojo update(UtsWxeapprrecPojo utsWxeapprrecpojo);

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    int delete(String key, String tid);

    /**
     * 通过Spno查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    UtsWxeapprrecPojo getEntityBySpno(String key, String tid);

    /**
     * 通过Billid查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    List<UtsWxeapprrecPojo> getOnlineByBillid(String key, String tid);
}
