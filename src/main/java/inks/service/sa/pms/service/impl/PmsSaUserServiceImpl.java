package inks.service.sa.pms.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import eu.bitwalker.useragentutils.UserAgent;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.domain.R;
import inks.common.core.domain.TenantInfo;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.*;
import inks.sa.common.core.domain.pojo.LoginUserPojo;
import inks.sa.common.core.domain.pojo.SaCompanyPojo;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.domain.vo.SaEngineerPojo;
import inks.sa.common.core.service.SaCompanyService;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.controller.A_PmsSaUserController;
import inks.service.sa.pms.domain.SaLoginlogEntity;
import inks.service.sa.pms.domain.SaUserEntity;
import inks.service.sa.pms.domain.pojo.SaRmsjustauthPojo;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import inks.service.sa.pms.domain.pojo.SaUserPojo;
import inks.service.sa.pms.domain.pojo.SaUsergroupPojo;
import inks.service.sa.pms.mapper.SaEngineerMapper;
import inks.service.sa.pms.mapper.Pms_SaLoginlogMapper;
import inks.service.sa.pms.mapper.pmsSaUserMapper;
import inks.service.sa.pms.mapper.SaUsergroupMapper;
import inks.service.sa.pms.service.SaRmsjustauthService;
import inks.service.sa.pms.service.SaRmsuserService;
import inks.service.sa.pms.service.PmsSaUserService;
import inks.service.sa.pms.utils.SN;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 用户(SaUser)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:13
 */
@Service("pmsSaUserService")
public class PmsSaUserServiceImpl implements PmsSaUserService {
    @Resource
    private SaRmsuserService saRmsuserService;
    @Resource
    private SaRmsjustauthService saRmsjustauthService;
    @Resource
    private pmsSaUserMapper pmsSaUserMapper;
    @Resource
    private Pms_SaLoginlogMapper pmsSaLoginLogMapper;


    @Resource
    private SaRedisService saRedisService;

    @Resource
    private SaUsergroupMapper saUsergroupMapper;
    @Resource
    private SaEngineerMapper saEngineerMapper;
    //    @Resource
//    private RedisTemplate<String, Object> redisTemplate;
    @Resource
    private A_PmsSaUserController a_Pms_saUserController;
    @Resource
    private SaCompanyService saCompanyService;
    @Value("${inks.user.wxscan-create:false}")
    private boolean wxScanCreate;

    public static void main(String[] args) {
        // 将时间戳转换为北京时间的日期时间字符串
        long timestamp = 1716467838136L;
        Instant instant = Instant.ofEpochMilli(timestamp);
        LocalDateTime dateTime = LocalDateTime.ofInstant(instant, ZoneId.of("Asia/Shanghai"));
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        String beijingTime = dateTime.format(formatter);

        System.out.println(beijingTime);
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaUserPojo getEntity(String key) {
        return this.pmsSaUserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaUserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaUserPojo> lst = pmsSaUserMapper.getPageList(queryParam);
            PageInfo<SaUserPojo> pageInfo = new PageInfo<SaUserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saUserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserPojo insert(SaUserPojo saUserPojo) throws Exception {
        //初始化NULL字段
        if (saUserPojo.getWxopenid() == null) saUserPojo.setWxopenid("");
        if (saUserPojo.getUsername() == null) saUserPojo.setUsername("");
        if (saUserPojo.getRealname() == null) saUserPojo.setRealname("");
        if (saUserPojo.getPassword() == null) saUserPojo.setPassword("");
        if (saUserPojo.getPhone() == null) saUserPojo.setPhone("");
        if (saUserPojo.getEmail() == null) saUserPojo.setEmail("");
        if (saUserPojo.getEmailauthcode() == null) saUserPojo.setEmailauthcode("");
        if (saUserPojo.getDinguserid() == null) saUserPojo.setDinguserid("");
        if (saUserPojo.getSex() == null) saUserPojo.setSex(0);
        if (saUserPojo.getAvatar() == null) saUserPojo.setAvatar("");
        if (saUserPojo.getUserstate() == null) saUserPojo.setUserstate(0);
        if (saUserPojo.getRemark() == null) saUserPojo.setRemark("");
        if (saUserPojo.getCreateby() == null) saUserPojo.setCreateby("");
        if (saUserPojo.getCreatedate() == null) saUserPojo.setCreatedate(new Date());
        if (saUserPojo.getLister() == null) saUserPojo.setLister("");
        if (saUserPojo.getModifydate() == null) saUserPojo.setModifydate(new Date());
        if (saUserPojo.getRevision() == null) saUserPojo.setRevision(0);
        if (saUserPojo.getDirname() == null) saUserPojo.setDirname("");
        if (saUserPojo.getFilename() == null) saUserPojo.setFilename("");
        if (saUserPojo.getAdminmark() == null) saUserPojo.setAdminmark(0);
        if (saUserPojo.getRoletype() == null) saUserPojo.setRoletype(0);

        // 加密密码存入数据库Sa_User
        saUserPojo.setPassword(AESUtil.Encrypt(saUserPojo.getPassword()));
        SaUserEntity saUserEntity = new SaUserEntity();
        BeanUtils.copyProperties(saUserPojo, saUserEntity);

        saUserEntity.setId(UUID.randomUUID().toString());
        saUserEntity.setRevision(1);  //乐观锁
        this.pmsSaUserMapper.insert(saUserEntity);
        return this.getEntity(saUserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saUserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserPojo update(SaUserPojo saUserPojo) throws Exception {
        String password = saUserPojo.getPassword();
        if (isNotBlank(password)) {
            saUserPojo.setPassword(AESUtil.Encrypt(password));
        }
        SaUserEntity saUserEntity = new SaUserEntity();
        BeanUtils.copyProperties(saUserPojo, saUserEntity);
        this.pmsSaUserMapper.update(saUserEntity);
        return this.getEntity(saUserEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.pmsSaUserMapper.delete(key, tid);
    }


    @Override
    public SaUserPojo getUserInfo(LoginUser loginUser) {
        return this.pmsSaUserMapper.getUserInfo(loginUser.getUserid());
    }

    @Override
    public Map<String, Object> loginCheck(LoginUserPojo loginUserPojo, HttpServletRequest request, String type) {
        try {
            //构建登陆日志对象
            SaLoginlogEntity ciLoginLogEntity = new SaLoginlogEntity();
            ciLoginLogEntity.setLogintime(BillCodeUtil.newDate());
            ciLoginLogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            //获取请求浏览器操作系统
            UserAgent userAgent = UserAgent.parseUserAgentString(request.getHeader("User-Agent"));
            String os = userAgent.getOperatingSystem().getName();
            String browser = userAgent.getBrowser().getName();
            String ip = UserAgentUtil.getIpAddr(request);
            String address = UserAgentUtil.getRealAddressByIP(ip);
            ciLoginLogEntity.setBrowsername(browser);
            ciLoginLogEntity.setHostsystem(os);
            ciLoginLogEntity.setIpaddr(ip);
            ciLoginLogEntity.setLoginlocation(address);

            MyLoginUser myLoginUser = new MyLoginUser();
            String encryptPass = AESUtil.Encrypt(loginUserPojo.getPassword());
            String username = loginUserPojo.getUsername();
            SaUserPojo saUserPojo = this.pmsSaUserMapper.getEntityByUNameAndPass(username, encryptPass);
            //如果type不为空，说明是Web端登录，进行Admin权限校验
            if (saUserPojo != null) {
                // 登录成功日志构建:
                ciLoginLogEntity.setUserid(saUserPojo.getId());
                ciLoginLogEntity.setUsername(username);
                ciLoginLogEntity.setRealname(saUserPojo.getRealname());
                ciLoginLogEntity.setDirection("登录");
                ciLoginLogEntity.setLoginstatus(0);
                //如果type不为空，说明是Web端登录，进行权限校验
                // Roletype和Adminmark只要有一个为1，说明有权限登录 ;当Adminmark和Roletype同时为0时，则检查是否关联了groupids
                if (StringUtils.isNotBlank(type)) {
                    //当Adminmark都不为1时，且Roletype说明不是无Web端登录权限 TODO Roletype=0时查询UserGroup关联表！
//                    if (saUserPojo.getRoletype() == 0 && saUserPojo.getAdminmark() == 0) {
                    List<SaUsergroupPojo> saUserGroupList = saUsergroupMapper.getListByUserid(saUserPojo.getId());
                    if (saUserGroupList == null) {
                        // 登录日志:无权限
                        ciLoginLogEntity.setLoginstatus(1);
                        ciLoginLogEntity.setLoginmsg("用户：" + username + "登录失败,登录时间："
                                + BillCodeUtil.forMatDate(ciLoginLogEntity.getLogintime()) + ",原因：没有权限登录" + ",登录系统："
                                + ciLoginLogEntity.getHostsystem() + ",操作浏览器："
                                + ciLoginLogEntity.getBrowsername() + ",登录地址："
                                + ciLoginLogEntity.getLoginlocation());
                        pmsSaLoginLogMapper.insert(ciLoginLogEntity);

                        throw new BaseBusinessException("您没有权限登录");
                    }
                    String groupids = saUserGroupList.stream()
                            .map(groupid -> "'" + groupid.getGroupid() + "'")  // 在每个groupid两端添加单引号
                            .collect(Collectors.joining(","));
                    myLoginUser.setGroupids(groupids);
//                    }
                }
                // 登录成功日志:
                ciLoginLogEntity.setLoginmsg("用户：" + username + "登录成功,登录时间："
                        + BillCodeUtil.forMatDate(ciLoginLogEntity.getLogintime()) + ",登录系统："
                        + ciLoginLogEntity.getHostsystem() + ",操作浏览器："
                        + ciLoginLogEntity.getBrowsername() + ",登录地址："
                        + ciLoginLogEntity.getLoginlocation());
                pmsSaLoginLogMapper.insert(ciLoginLogEntity);

                myLoginUser.setLogid(ciLoginLogEntity.getId());
                myLoginUser.setUserid(saUserPojo.getId());
                myLoginUser.setUsername(saUserPojo.getUsername());
                myLoginUser.setAvatar(saUserPojo.getAvatar());
                myLoginUser.setIpaddr(IpUtils.getIpAddr(request));
                myLoginUser.setRealname(saUserPojo.getRealname());
                //查DB 当前用户的AdminMark
                myLoginUser.setIsadmin(saUserPojo.getAdminmark());
                myLoginUser.setTenantid("tid-inks-pms");
                myLoginUser.setTenantinfo(new TenantInfo("tid-inks-pms", "tid-code", "应凯", "company", "companyadd", "companytel", "contactor", new Date()));
                // 查询工程师信息放入loginuser
                SaEngineerPojo engineer = this.saEngineerMapper.getEngineerByUserid(myLoginUser.getUserid());
                myLoginUser.setEngineer(engineer);
//                Map<String, Object> tokenMap = saRedisService.createToken(myLoginUser);
                Map<String, Object> tokenMap = myCreateToken(myLoginUser, request);
                LoginUser loginUser = (LoginUser) tokenMap.get("loginuser");
                loginUser.setExpiretime(loginUser.getLogintime() + 2592000000L);//30天,前端根据Expiretime判断是否需要重新登录
                // redis过期时间30天
//                redisTemplate.expire("login_tokens:" + tokenMap.get("access_token").toString(), 30, java.util.concurrent.TimeUnit.DAYS);

                return tokenMap;
            } else {
                ciLoginLogEntity.setUserid("");
                ciLoginLogEntity.setUsername(username);
                ciLoginLogEntity.setRealname("");
                ciLoginLogEntity.setDirection("登录");
                ciLoginLogEntity.setLoginstatus(1);
                ciLoginLogEntity.setLoginmsg("用户：" + username + "登录失败,登录时间："
                        + BillCodeUtil.forMatDate(ciLoginLogEntity.getLogintime()) + ",原因：用户名或密码错误" + ",登录系统："
                        + ciLoginLogEntity.getHostsystem() + ",操作浏览器："
                        + ciLoginLogEntity.getBrowsername() + ",登录地址："
                        + ciLoginLogEntity.getLoginlocation());
                pmsSaLoginLogMapper.insert(ciLoginLogEntity);
                throw new BaseBusinessException("用户名或密码错误");
            }
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    // 通过openid登录或注册
    @Override
    public Map<String, Object> loginCheckByOpenid(String openid, HttpServletRequest request) {
        try {
            // 通过openid查询用户信息
            SaUserPojo saUserPojoDB = pmsSaUserMapper.getEntityByOpenid(openid);
            // openid如果用户不存在，则报错
            if (saUserPojoDB == null) {
                throw new BaseBusinessException("微信未绑定账号");
            }
            // loginUser额外返回sn,解密后的注册信息.
            MyLoginUser myLoginUser = new MyLoginUser();
            BeanUtils.copyProperties(saUserPojoDB, myLoginUser);
            Map<String, Object> token = myCreateToken(myLoginUser, request);
            return token;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public Map<String, Object> loginRmsByOpenid(String openid, HttpServletRequest request) {
        try {
            // 通过openid查询用Rms户信息
            SaRmsuserPojo rmsUserPojoDB = saRmsuserService.getEntityByOpenid(openid);

            // 如果用户不存在并且yml未开启自动创建用户，则抛出异常
            if (rmsUserPojoDB == null) {
                if (!wxScanCreate) {
                    throw new BaseBusinessException("微信未绑定账号");
                }
                // 自动创建用户
                SaRmsuserPojo rmsUserPojo = new SaRmsuserPojo();
                // openid最后6位作为用户名
                String openidLast6 = openid.substring(openid.length() - 6);
                rmsUserPojo.setUsername("wxrms-" + openidLast6);
                rmsUserPojo.setRealname("wxrms-" + openidLast6);
                rmsUserPojo.setUserpassword("123456");
                rmsUserPojo.setIsadmin(1);
                rmsUserPojo.setRemark("微信公众号Rms用户自动注册");
                // 保存新用户
                SaRmsuserPojo insertUser = saRmsuserService.insert(rmsUserPojo);
                // 更新查询结果
                rmsUserPojoDB = rmsUserPojo;
                // 创建openid绑定关系到Sa_JustAuth表
                SaRmsjustauthPojo saJustauthPojo = new SaRmsjustauthPojo();
                saJustauthPojo.setUserid(insertUser.getUserid());
                saJustauthPojo.setUsername(rmsUserPojo.getUsername());
                saJustauthPojo.setRealname(rmsUserPojo.getRealname());
                saJustauthPojo.setAuthtype("openid");
                saJustauthPojo.setAuthuuid(openid);
                saRmsjustauthService.insert(saJustauthPojo);
            }

            //myLoginUser需要的值：userid,username,avatar,realname,isadmin,wxopenid
            MyLoginUser myLoginUser = new MyLoginUser();
            BeanUtils.copyProperties(rmsUserPojoDB, myLoginUser);
            myLoginUser.setWxopenid(openid);
            // 返回登录用户额外信息
            return myCreateToken(myLoginUser, request);
        } catch (Exception e) {
            throw new BaseBusinessException("登录检查失败: " + e.getMessage());
        }
    }

    //myLoginUser需要的值：userid,username,avatar,realname,isadmin,wxopenid
    public Map<String, Object> myCreateToken(MyLoginUser myLoginUser, HttpServletRequest request) {
        MyLoginUser myLoginUserNew = new MyLoginUser();
        inks.common.core.utils.bean.BeanUtils.copyProperties(myLoginUser, myLoginUserNew);
        myLoginUserNew.setUserid(myLoginUser.getUserid());
        myLoginUserNew.setUsername(myLoginUser.getUsername());
        myLoginUserNew.setAvatar(myLoginUser.getAvatar());
        myLoginUserNew.setIpaddr(IpUtils.getIpAddr(request));
        myLoginUserNew.setRealname(myLoginUser.getRealname());
        //查DB 当前用户的AdminMark
        myLoginUserNew.setIsadmin(myLoginUser.getIsadmin());
        myLoginUserNew.setTenantid("tid-inks-pms");
        SaCompanyPojo companyInfo = saCompanyService.getCompanyInfo();
        if (companyInfo == null) {
            throw new BaseBusinessException("公司信息不存在");
        }
        myLoginUserNew.setTenantinfo(new TenantInfo("tid-inks-cmr", "tid-code", "tid-name",
                companyInfo.getName(), companyInfo.getAddress(), companyInfo.getTel(), companyInfo.getContactperson(), new Date()));

        String token = IdUtils.fastUUID();
        myLoginUserNew.setToken(token);
        myLoginUserNew.setUsername(myLoginUserNew.getUsername());
        myLoginUserNew.setIpaddr(IpUtils.getIpAddr(ServletUtils.getRequest()));
//        this.refreshToken(loginUser);
        myLoginUserNew.setLogintime(System.currentTimeMillis());
        myLoginUserNew.setExpiretime(myLoginUserNew.getLogintime() + 2592000000L);// 30天=2592000秒 这里是毫秒
        myLoginUserNew.setIsregister(0);
        Map<String, Object> map = new HashMap();
        map.put("access_token", token);
        map.put("expires_in", 2592000L);
        map.put("sn", SN.getSN());

        // 检查SN成功才返回解密后的密文 原文为:{"code":"oms","sn":"123456","key":"xxxx-xxxx-xxxx","uc":10,"ex":1704038400},存储在Sa_Config中CfgKey=system.registrkey
        String decrypt = "";
        R<String> stringR = a_Pms_saUserController.checkHard(); //检查SN成功才返回解密后的密文
        if (stringR.getCode() == 200) {
            decrypt = stringR.getData();
            myLoginUserNew.setIsregister(1);//已注册且硬件匹配
        } else if (stringR.getCode() == 401) {
            myLoginUserNew.setIsregister(2);//已注册但硬件不匹配
        }
        map.put("registrkey", decrypt);
        map.put("loginuser", myLoginUserNew);
        map.put("openid", myLoginUser.getWxopenid());

        this.saRedisService.setKeyValue("login_tokens:" + token, JSONObject.toJSONString(myLoginUserNew), 2592000L);
        return map;
    }


    @Override
    public List<String> checkUseridUsed(String userid) {
        return this.pmsSaUserMapper.checkUseridUsed(userid);
    }
}
