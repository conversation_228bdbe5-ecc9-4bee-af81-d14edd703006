package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaLoginlogEntity;
import inks.service.sa.pms.domain.pojo.SaLoginlogPojo;
import inks.service.sa.pms.mapper.Pms_SaLoginlogMapper;
import inks.service.sa.pms.service.Pms_SaLoginlogService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 登录日志(SaLoginlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-20 08:47:34
 */
@Service("pmsSaLoginlogService")
public class Pms_SaLoginlogServiceImpl implements Pms_SaLoginlogService {
    @Resource
    private Pms_SaLoginlogMapper pmsSaLoginlogMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaLoginlogPojo getEntity(String key) {
        return this.pmsSaLoginlogMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaLoginlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaLoginlogPojo> lst = pmsSaLoginlogMapper.getPageList(queryParam);
            PageInfo<SaLoginlogPojo> pageInfo = new PageInfo<SaLoginlogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saLoginlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLoginlogPojo insert(SaLoginlogPojo saLoginlogPojo) {
        //初始化NULL字段
        if (saLoginlogPojo.getUserid() == null) saLoginlogPojo.setUserid("");
        if (saLoginlogPojo.getUsername() == null) saLoginlogPojo.setUsername("");
        if (saLoginlogPojo.getRealname() == null) saLoginlogPojo.setRealname("");
        if (saLoginlogPojo.getIpaddr() == null) saLoginlogPojo.setIpaddr("");
        if (saLoginlogPojo.getLoginlocation() == null) saLoginlogPojo.setLoginlocation("");
        if (saLoginlogPojo.getBrowsername() == null) saLoginlogPojo.setBrowsername("");
        if (saLoginlogPojo.getHostsystem() == null) saLoginlogPojo.setHostsystem("");
        if (saLoginlogPojo.getDirection() == null) saLoginlogPojo.setDirection("");
        if (saLoginlogPojo.getLoginstatus() == null) saLoginlogPojo.setLoginstatus(0);
        if (saLoginlogPojo.getLoginmsg() == null) saLoginlogPojo.setLoginmsg("");
        if (saLoginlogPojo.getLogintime() == null) saLoginlogPojo.setLogintime(new Date());
        if (saLoginlogPojo.getTenantid() == null) saLoginlogPojo.setTenantid("");
        if (saLoginlogPojo.getTenantname() == null) saLoginlogPojo.setTenantname("");
        SaLoginlogEntity saLoginlogEntity = new SaLoginlogEntity();
        BeanUtils.copyProperties(saLoginlogPojo, saLoginlogEntity);
        //生成雪花id
        saLoginlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.pmsSaLoginlogMapper.insert(saLoginlogEntity);
        return this.getEntity(saLoginlogEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saLoginlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLoginlogPojo update(SaLoginlogPojo saLoginlogPojo) {
        SaLoginlogEntity saLoginlogEntity = new SaLoginlogEntity();
        BeanUtils.copyProperties(saLoginlogPojo, saLoginlogEntity);
        this.pmsSaLoginlogMapper.update(saLoginlogEntity);
        return this.getEntity(saLoginlogEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.pmsSaLoginlogMapper.delete(key);
    }


}
