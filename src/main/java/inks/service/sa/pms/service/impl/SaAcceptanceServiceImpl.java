package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaAcceptanceEntity;
import inks.service.sa.pms.domain.pojo.SaAcceptancePojo;
import inks.service.sa.pms.mapper.SaAcceptanceMapper;
import inks.service.sa.pms.service.SaAcceptanceService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaAcceptance)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-09 16:11:12
 */
@Service("saAcceptanceService")
public class SaAcceptanceServiceImpl implements SaAcceptanceService {
    @Resource
    private SaAcceptanceMapper saAcceptanceMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaAcceptancePojo getEntity(String key) {
        return this.saAcceptanceMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaAcceptancePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaAcceptancePojo> lst = saAcceptanceMapper.getPageList(queryParam);
            PageInfo<SaAcceptancePojo> pageInfo = new PageInfo<SaAcceptancePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saAcceptancePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaAcceptancePojo insert(SaAcceptancePojo saAcceptancePojo) {
        //初始化NULL字段
        if (saAcceptancePojo.getGroupid() == null) saAcceptancePojo.setGroupid("");
        if (saAcceptancePojo.getName() == null) saAcceptancePojo.setName("");
        if (saAcceptancePojo.getRefno() == null) saAcceptancePojo.setRefno("");
        if (saAcceptancePojo.getAcceptancedate() == null) saAcceptancePojo.setAcceptancedate(new Date());
        if (saAcceptancePojo.getAcceptanceloc() == null) saAcceptancePojo.setAcceptanceloc("");
        if (saAcceptancePojo.getPartyainsp() == null) saAcceptancePojo.setPartyainsp("");
        if (saAcceptancePojo.getPartybinsp() == null) saAcceptancePojo.setPartybinsp("");
        if (saAcceptancePojo.getContent() == null) saAcceptancePojo.setContent("");
        if (saAcceptancePojo.getInstallmatch() == null) saAcceptancePojo.setInstallmatch(0);
        if (saAcceptancePojo.getDocumentcomplete() == null) saAcceptancePojo.setDocumentcomplete(0);
        if (saAcceptancePojo.getAllfunc() == null) saAcceptancePojo.setAllfunc(0);
        if (saAcceptancePojo.getPartybeval() == null) saAcceptancePojo.setPartybeval("");
        if (saAcceptancePojo.getPartybevaldate() == null) saAcceptancePojo.setPartybevaldate(new Date());
        if (saAcceptancePojo.getIssues() == null) saAcceptancePojo.setIssues("");
        if (saAcceptancePojo.getPartyaeval() == null) saAcceptancePojo.setPartyaeval("");
        if (saAcceptancePojo.getPartyaevaldate() == null) saAcceptancePojo.setPartyaevaldate(new Date());
        if (saAcceptancePojo.getRevion() == null) saAcceptancePojo.setRevion(0);
        if (saAcceptancePojo.getRownum() == null) saAcceptancePojo.setRownum(0);
        if (saAcceptancePojo.getCreatebyid() == null) saAcceptancePojo.setCreatebyid("");
        if (saAcceptancePojo.getCreateby() == null) saAcceptancePojo.setCreateby("");
        if (saAcceptancePojo.getListerid() == null) saAcceptancePojo.setListerid("");
        if (saAcceptancePojo.getLister() == null) saAcceptancePojo.setLister("");
        if (saAcceptancePojo.getModifydate() == null) saAcceptancePojo.setModifydate(new Date());
        if (saAcceptancePojo.getCustom1() == null) saAcceptancePojo.setCustom1("");
        if (saAcceptancePojo.getCustom2() == null) saAcceptancePojo.setCustom2("");
        if (saAcceptancePojo.getCustom3() == null) saAcceptancePojo.setCustom3("");
        if (saAcceptancePojo.getCustom4() == null) saAcceptancePojo.setCustom4("");
        if (saAcceptancePojo.getCustom5() == null) saAcceptancePojo.setCustom5("");
        if (saAcceptancePojo.getTenantid() == null) saAcceptancePojo.setTenantid("");
        if (saAcceptancePojo.getRevision() == null) saAcceptancePojo.setRevision(0);
        SaAcceptanceEntity saAcceptanceEntity = new SaAcceptanceEntity();
        BeanUtils.copyProperties(saAcceptancePojo, saAcceptanceEntity);
        //生成雪花id
        saAcceptanceEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saAcceptanceEntity.setRevision(1);  //乐观锁
        this.saAcceptanceMapper.insert(saAcceptanceEntity);
        return this.getEntity(saAcceptanceEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saAcceptancePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaAcceptancePojo update(SaAcceptancePojo saAcceptancePojo) {
        SaAcceptanceEntity saAcceptanceEntity = new SaAcceptanceEntity();
        BeanUtils.copyProperties(saAcceptancePojo, saAcceptanceEntity);
        this.saAcceptanceMapper.update(saAcceptanceEntity);
        return this.getEntity(saAcceptanceEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saAcceptanceMapper.delete(key);
    }


}
