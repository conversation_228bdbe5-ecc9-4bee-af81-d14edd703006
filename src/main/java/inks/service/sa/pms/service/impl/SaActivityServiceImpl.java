package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaActivityPojo;
import inks.service.sa.pms.domain.pojo.SaActivityitemPojo;
import inks.service.sa.pms.domain.pojo.SaActivityitemdetailPojo;
import inks.service.sa.pms.domain.SaActivityEntity;
import inks.service.sa.pms.domain.SaActivityitemEntity;
import inks.service.sa.pms.mapper.SaActivityMapper;
import inks.service.sa.pms.service.SaActivityService;
import inks.service.sa.pms.service.SaActivityitemService;
import inks.service.sa.pms.mapper.SaActivityitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 活动主表(SaActivity)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:12
 */
@Service("saActivityService")
public class SaActivityServiceImpl implements SaActivityService {
    @Resource
    private SaActivityMapper saActivityMapper;
    
    @Resource
    private SaActivityitemMapper saActivityitemMapper;
    

    @Resource
    private SaActivityitemService saActivityitemService;
    

    @Override
    public SaActivityPojo getEntity(String key) {
        return this.saActivityMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaActivityitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivityitemdetailPojo> lst = saActivityMapper.getPageList(queryParam);
            PageInfo<SaActivityitemdetailPojo> pageInfo = new PageInfo<SaActivityitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaActivityPojo getBillEntity(String key) {
       try {
        //读取主表
        SaActivityPojo saActivityPojo = this.saActivityMapper.getEntity(key);
        //读取子表
        saActivityPojo.setItem(saActivityitemMapper.getList(saActivityPojo.getId()));
        return saActivityPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaActivityPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivityPojo> lst = saActivityMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saActivityitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaActivityPojo> pageInfo = new PageInfo<SaActivityPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaActivityPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivityPojo> lst = saActivityMapper.getPageTh(queryParam);
            PageInfo<SaActivityPojo> pageInfo = new PageInfo<SaActivityPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaActivityPojo insert(SaActivityPojo saActivityPojo) {
        //初始化NULL字段
        cleanNull(saActivityPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaActivityEntity saActivityEntity = new SaActivityEntity(); 
        BeanUtils.copyProperties(saActivityPojo,saActivityEntity);
        //设置id和新建日期
        saActivityEntity.setId(id);
        saActivityEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saActivityMapper.insert(saActivityEntity);
        //Item子表处理
        List<SaActivityitemPojo> lst = saActivityPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaActivityitemPojo itemPojo =this.saActivityitemService.clearNull(lst.get(i));
               SaActivityitemEntity saActivityitemEntity = new SaActivityitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saActivityitemEntity);
               //设置id和Pid
               saActivityitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saActivityitemEntity.setPid(id);
               saActivityitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saActivityitemMapper.insert(saActivityitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saActivityEntity.getId());
    }


    @Override
    @Transactional
    public SaActivityPojo update(SaActivityPojo saActivityPojo) {
        //主表更改
        SaActivityEntity saActivityEntity = new SaActivityEntity(); 
        BeanUtils.copyProperties(saActivityPojo,saActivityEntity);
        this.saActivityMapper.update(saActivityEntity);
        if (saActivityPojo.getItem() != null) {
        //Item子表处理
        List<SaActivityitemPojo> lst = saActivityPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saActivityMapper.getDelItemIds(saActivityPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saActivityitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaActivityitemEntity saActivityitemEntity = new SaActivityitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaActivityitemPojo itemPojo =this.saActivityitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saActivityitemEntity);
               //设置id和Pid
               saActivityitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saActivityitemEntity.setPid(saActivityEntity.getId());  // 主表 id
               saActivityitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saActivityitemMapper.insert(saActivityitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saActivityitemEntity);             
               this.saActivityitemMapper.update(saActivityitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saActivityEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaActivityPojo saActivityPojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaActivityitemPojo> lst = saActivityPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saActivityitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.saActivityMapper.delete(key) ;
    }
    

    
    @Override
    @Transactional
    public SaActivityPojo approval(SaActivityPojo saActivityPojo) {
        //主表更改
        SaActivityEntity saActivityEntity = new SaActivityEntity();
        BeanUtils.copyProperties(saActivityPojo,saActivityEntity);
        this.saActivityMapper.approval(saActivityEntity);
        //返回Bill实例
        return this.getBillEntity(saActivityEntity.getId());
    }

    private static void cleanNull(SaActivityPojo saActivityPojo) {
        if(saActivityPojo.getRefno()==null) saActivityPojo.setRefno("");
        if(saActivityPojo.getBilltype()==null) saActivityPojo.setBilltype("");
        if(saActivityPojo.getBilltitle()==null) saActivityPojo.setBilltitle("");
        if(saActivityPojo.getBilldate()==null) saActivityPojo.setBilldate(new Date());
        if(saActivityPojo.getActivitytheme()==null) saActivityPojo.setActivitytheme("");
        if(saActivityPojo.getExponent()==null) saActivityPojo.setExponent(0);
        if(saActivityPojo.getStageid()==null) saActivityPojo.setStageid("");
        if(saActivityPojo.getState()==null) saActivityPojo.setState("");
        if(saActivityPojo.getStatedate()==null) saActivityPojo.setStatedate(new Date());
        if(saActivityPojo.getAttachments()==null) saActivityPojo.setAttachments("");
        if(saActivityPojo.getSummary()==null) saActivityPojo.setSummary("");
        if(saActivityPojo.getCreateby()==null) saActivityPojo.setCreateby("");
        if(saActivityPojo.getCreatebyid()==null) saActivityPojo.setCreatebyid("");
        if(saActivityPojo.getCreatedate()==null) saActivityPojo.setCreatedate(new Date());
        if(saActivityPojo.getLister()==null) saActivityPojo.setLister("");
        if(saActivityPojo.getListerid()==null) saActivityPojo.setListerid("");
        if(saActivityPojo.getModifydate()==null) saActivityPojo.setModifydate(new Date());
        if(saActivityPojo.getAssessor()==null) saActivityPojo.setAssessor("");
        if(saActivityPojo.getAssessorid()==null) saActivityPojo.setAssessorid("");
        if(saActivityPojo.getAssessdate()==null) saActivityPojo.setAssessdate(new Date());
        if(saActivityPojo.getCustom1()==null) saActivityPojo.setCustom1("");
        if(saActivityPojo.getCustom2()==null) saActivityPojo.setCustom2("");
        if(saActivityPojo.getCustom3()==null) saActivityPojo.setCustom3("");
        if(saActivityPojo.getCustom4()==null) saActivityPojo.setCustom4("");
        if(saActivityPojo.getCustom5()==null) saActivityPojo.setCustom5("");
        if(saActivityPojo.getCustom6()==null) saActivityPojo.setCustom6("");
        if(saActivityPojo.getCustom7()==null) saActivityPojo.setCustom7("");
        if(saActivityPojo.getCustom8()==null) saActivityPojo.setCustom8("");
        if(saActivityPojo.getCustom9()==null) saActivityPojo.setCustom9("");
        if(saActivityPojo.getCustom10()==null) saActivityPojo.setCustom10("");
        if(saActivityPojo.getDeptid()==null) saActivityPojo.setDeptid("");
        if(saActivityPojo.getTenantid()==null) saActivityPojo.setTenantid("");
        if(saActivityPojo.getTenantname()==null) saActivityPojo.setTenantname("");
        if(saActivityPojo.getRevision()==null) saActivityPojo.setRevision(0);
   }

}
