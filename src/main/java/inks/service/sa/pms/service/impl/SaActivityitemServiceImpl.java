package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaActivityitemPojo;
import inks.service.sa.pms.domain.SaActivityitemEntity;
import inks.service.sa.pms.mapper.SaActivityitemMapper;
import inks.service.sa.pms.service.SaActivityitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 活动子表(SaActivityitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-13 17:03:54
 */
@Service("saActivityitemService")
public class SaActivityitemServiceImpl implements SaActivityitemService {
    @Resource
    private SaActivityitemMapper saActivityitemMapper;

    @Override
    public SaActivityitemPojo getEntity(String key) {
        return this.saActivityitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaActivityitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivityitemPojo> lst = saActivityitemMapper.getPageList(queryParam);
            PageInfo<SaActivityitemPojo> pageInfo = new PageInfo<SaActivityitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaActivityitemPojo> getList(String Pid) { 
        try {
            List<SaActivityitemPojo> lst = saActivityitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaActivityitemPojo insert(SaActivityitemPojo saActivityitemPojo) {
        //初始化item的NULL
        SaActivityitemPojo itempojo =this.clearNull(saActivityitemPojo);
        SaActivityitemEntity saActivityitemEntity = new SaActivityitemEntity(); 
        BeanUtils.copyProperties(itempojo,saActivityitemEntity);
         //生成雪花id
          saActivityitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saActivityitemEntity.setRevision(1);  //乐观锁      
          this.saActivityitemMapper.insert(saActivityitemEntity);
        return this.getEntity(saActivityitemEntity.getId());
  
    }

    @Override
    public SaActivityitemPojo update(SaActivityitemPojo saActivityitemPojo) {
        SaActivityitemEntity saActivityitemEntity = new SaActivityitemEntity(); 
        BeanUtils.copyProperties(saActivityitemPojo,saActivityitemEntity);
        this.saActivityitemMapper.update(saActivityitemEntity);
        return this.getEntity(saActivityitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saActivityitemMapper.delete(key) ;
    }

     @Override
     public SaActivityitemPojo clearNull(SaActivityitemPojo saActivityitemPojo){
     //初始化NULL字段
     if(saActivityitemPojo.getPid()==null) saActivityitemPojo.setPid("");
     if(saActivityitemPojo.getParentid()==null) saActivityitemPojo.setParentid("");
     if(saActivityitemPojo.getStagename()==null) saActivityitemPojo.setStagename("");
     if(saActivityitemPojo.getStageitemid()==null) saActivityitemPojo.setStageitemid("");
     if(saActivityitemPojo.getPlanndate()==null) saActivityitemPojo.setPlanndate(new Date());
     if(saActivityitemPojo.getOperator()==null) saActivityitemPojo.setOperator("");
     if(saActivityitemPojo.getOperatorid()==null) saActivityitemPojo.setOperatorid("");
     if(saActivityitemPojo.getCollaborators()==null) saActivityitemPojo.setCollaborators("");
     if(saActivityitemPojo.getCollaboratorids()==null) saActivityitemPojo.setCollaboratorids("");
     if(saActivityitemPojo.getFinishmark()==null) saActivityitemPojo.setFinishmark(0);
     if(saActivityitemPojo.getFinishdate()==null) saActivityitemPojo.setFinishdate(new Date());
     if(saActivityitemPojo.getFinishdesc()==null) saActivityitemPojo.setFinishdesc("");
     if(saActivityitemPojo.getMustmark()==null) saActivityitemPojo.setMustmark(0);
     if(saActivityitemPojo.getFilemark()==null) saActivityitemPojo.setFilemark(0);
     if(saActivityitemPojo.getAttachments()==null) saActivityitemPojo.setAttachments("");
     if(saActivityitemPojo.getLister()==null) saActivityitemPojo.setLister("");
     if(saActivityitemPojo.getListerid()==null) saActivityitemPojo.setListerid("");
     if(saActivityitemPojo.getModifydate()==null) saActivityitemPojo.setModifydate(new Date());
     if(saActivityitemPojo.getRemark()==null) saActivityitemPojo.setRemark("");
     if(saActivityitemPojo.getRownum()==null) saActivityitemPojo.setRownum(0);
     if(saActivityitemPojo.getCustom1()==null) saActivityitemPojo.setCustom1("");
     if(saActivityitemPojo.getCustom2()==null) saActivityitemPojo.setCustom2("");
     if(saActivityitemPojo.getCustom3()==null) saActivityitemPojo.setCustom3("");
     if(saActivityitemPojo.getCustom4()==null) saActivityitemPojo.setCustom4("");
     if(saActivityitemPojo.getCustom5()==null) saActivityitemPojo.setCustom5("");
     if(saActivityitemPojo.getCustom6()==null) saActivityitemPojo.setCustom6("");
     if(saActivityitemPojo.getCustom7()==null) saActivityitemPojo.setCustom7("");
     if(saActivityitemPojo.getCustom8()==null) saActivityitemPojo.setCustom8("");
     if(saActivityitemPojo.getCustom9()==null) saActivityitemPojo.setCustom9("");
     if(saActivityitemPojo.getCustom10()==null) saActivityitemPojo.setCustom10("");
     if(saActivityitemPojo.getTenantid()==null) saActivityitemPojo.setTenantid("");
     if(saActivityitemPojo.getRevision()==null) saActivityitemPojo.setRevision(0);
     return saActivityitemPojo;
     }
}
