package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaActivityplanEntity;
import inks.service.sa.pms.domain.pojo.SaActivityitemPojo;
import inks.service.sa.pms.domain.pojo.SaActivityplanPojo;
import inks.service.sa.pms.mapper.SaActivityitemMapper;
import inks.service.sa.pms.mapper.SaActivityplanMapper;
import inks.service.sa.pms.service.SaActivityplanService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 活动计划/日志(SaActivityplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21 17:27:55
 */
@Service("saActivityplanService")
public class SaActivityplanServiceImpl implements SaActivityplanService {
    @Resource
    private SaActivityitemMapper saActivityitemMapper;
    @Resource
    private SaActivityplanMapper saActivityplanMapper;

    @Override
    public SaActivityplanPojo getEntity(String key) {
        return this.saActivityplanMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaActivityplanPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivityplanPojo> lst = saActivityplanMapper.getPageList(queryParam);
            PageInfo<SaActivityplanPojo> pageInfo = new PageInfo<SaActivityplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<SaActivityplanPojo> getList(String Pid) {
        try {
            List<SaActivityplanPojo> lst = saActivityplanMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaActivityplanPojo insert(SaActivityplanPojo saActivityplanPojo) {
        //初始化item的NULL
        SaActivityplanPojo itempojo = this.clearNull(saActivityplanPojo);
        SaActivityplanEntity saActivityplanEntity = new SaActivityplanEntity();
        BeanUtils.copyProperties(itempojo, saActivityplanEntity);
        //生成雪花id
        saActivityplanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saActivityplanEntity.setRevision(1);  //乐观锁
        this.saActivityplanMapper.insert(saActivityplanEntity);
        return this.getEntity(saActivityplanEntity.getId());

    }

    @Override
    @Transactional
    public SaActivityplanPojo update(SaActivityplanPojo saActivityplanPojo) {
        SaActivityplanEntity saActivityplanEntity = new SaActivityplanEntity();
        BeanUtils.copyProperties(saActivityplanPojo, saActivityplanEntity);
        this.saActivityplanMapper.update(saActivityplanEntity);
        Integer finishmark = saActivityplanPojo.getFinishmark();
        String actiitemid = saActivityplanPojo.getActiitemid();
        // 关键事项完成时同步完成关联的活动子表
        if (StringUtils.isNotBlank(actiitemid)) {
            SaActivityitemPojo activityitemDB = saActivityitemMapper.getEntity(actiitemid);
            Integer finishmarkDB = activityitemDB.getFinishmark();
            if (Objects.equals(finishmark, 1) && Objects.equals(finishmarkDB, 0)) {
                saActivityitemMapper.syncFinishByid(actiitemid, 1, new Date());
            } else if (Objects.equals(finishmark, 0) && Objects.equals(finishmarkDB, 1)) {
                saActivityitemMapper.syncFinishByid(actiitemid, 0, null);
            }
        }
        return this.getEntity(saActivityplanEntity.getId());
    }

    @Override
    public int delete(String key) {
        SaActivityplanPojo activityplanDB = saActivityplanMapper.getEntity(key);
        String actiitemid = activityplanDB.getActiitemid();
        if (StringUtils.isNotBlank(actiitemid)) {
            saActivityitemMapper.syncFinishByid(actiitemid, 0, null);
        }
        return this.saActivityplanMapper.delete(key);
    }

    @Override
    public SaActivityplanPojo clearNull(SaActivityplanPojo saActivityplanPojo) {
        //初始化NULL字段
        if (saActivityplanPojo.getPid() == null) saActivityplanPojo.setPid("");
        if (saActivityplanPojo.getType() == null) saActivityplanPojo.setType("");
        if (saActivityplanPojo.getActiitemid() == null) saActivityplanPojo.setActiitemid("");
        //if(saActivityplanPojo.getPlandate()==null) saActivityplanPojo.setPlandate(new Date());
        //if(saActivityplanPojo.getFinishdate()==null) saActivityplanPojo.setFinishdate(new Date());
        if (saActivityplanPojo.getFinishmark() == null) saActivityplanPojo.setFinishmark(0);
        if (saActivityplanPojo.getDescription() == null) saActivityplanPojo.setDescription("");
        if (saActivityplanPojo.getAttachments() == null) saActivityplanPojo.setAttachments("");
        if (saActivityplanPojo.getRownum() == null) saActivityplanPojo.setRownum(0);
        if (saActivityplanPojo.getRemark() == null) saActivityplanPojo.setRemark("");
        if (saActivityplanPojo.getCustom1() == null) saActivityplanPojo.setCustom1("");
        if (saActivityplanPojo.getCustom2() == null) saActivityplanPojo.setCustom2("");
        if (saActivityplanPojo.getCustom3() == null) saActivityplanPojo.setCustom3("");
        if (saActivityplanPojo.getCustom4() == null) saActivityplanPojo.setCustom4("");
        if (saActivityplanPojo.getCustom5() == null) saActivityplanPojo.setCustom5("");
        if (saActivityplanPojo.getTenantid() == null) saActivityplanPojo.setTenantid("");
        if (saActivityplanPojo.getRevision() == null) saActivityplanPojo.setRevision(0);
        return saActivityplanPojo;
    }
}
