package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaActivitystagePojo;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemPojo;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemdetailPojo;
import inks.service.sa.pms.domain.SaActivitystageEntity;
import inks.service.sa.pms.domain.SaActivitystageitemEntity;
import inks.service.sa.pms.mapper.SaActivitystageMapper;
import inks.service.sa.pms.service.SaActivitystageService;
import inks.service.sa.pms.service.SaActivitystageitemService;
import inks.service.sa.pms.mapper.SaActivitystageitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 活动阶段表(SaActivitystage)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:14
 */
@Service("saActivitystageService")
public class SaActivitystageServiceImpl implements SaActivitystageService {
    @Resource
    private SaActivitystageMapper saActivitystageMapper;
    
    @Resource
    private SaActivitystageitemMapper saActivitystageitemMapper;
    

    @Resource
    private SaActivitystageitemService saActivitystageitemService;
    

    @Override
    public SaActivitystagePojo getEntity(String key) {
        return this.saActivitystageMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaActivitystageitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivitystageitemdetailPojo> lst = saActivitystageMapper.getPageList(queryParam);
            PageInfo<SaActivitystageitemdetailPojo> pageInfo = new PageInfo<SaActivitystageitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaActivitystagePojo getBillEntity(String key) {
       try {
        //读取主表
        SaActivitystagePojo saActivitystagePojo = this.saActivitystageMapper.getEntity(key);
        //读取子表
        saActivitystagePojo.setItem(saActivitystageitemMapper.getList(saActivitystagePojo.getId()));
        return saActivitystagePojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaActivitystagePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivitystagePojo> lst = saActivitystageMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saActivitystageitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaActivitystagePojo> pageInfo = new PageInfo<SaActivitystagePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaActivitystagePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivitystagePojo> lst = saActivitystageMapper.getPageTh(queryParam);
            PageInfo<SaActivitystagePojo> pageInfo = new PageInfo<SaActivitystagePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaActivitystagePojo insert(SaActivitystagePojo saActivitystagePojo) {
        //初始化NULL字段
        cleanNull(saActivitystagePojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaActivitystageEntity saActivitystageEntity = new SaActivitystageEntity(); 
        BeanUtils.copyProperties(saActivitystagePojo,saActivitystageEntity);
        //设置id和新建日期
        saActivitystageEntity.setId(id);
        saActivitystageEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saActivitystageMapper.insert(saActivitystageEntity);
        //Item子表处理
        List<SaActivitystageitemPojo> lst = saActivitystagePojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaActivitystageitemPojo itemPojo =this.saActivitystageitemService.clearNull(lst.get(i));
               SaActivitystageitemEntity saActivitystageitemEntity = new SaActivitystageitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saActivitystageitemEntity);
               //设置id和Pid
               saActivitystageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saActivitystageitemEntity.setPid(id);
               saActivitystageitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saActivitystageitemMapper.insert(saActivitystageitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saActivitystageEntity.getId());
    }


    @Override
    @Transactional
    public SaActivitystagePojo update(SaActivitystagePojo saActivitystagePojo) {
        //主表更改
        SaActivitystageEntity saActivitystageEntity = new SaActivitystageEntity(); 
        BeanUtils.copyProperties(saActivitystagePojo,saActivitystageEntity);
        this.saActivitystageMapper.update(saActivitystageEntity);
        if (saActivitystagePojo.getItem() != null) {
        //Item子表处理
        List<SaActivitystageitemPojo> lst = saActivitystagePojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saActivitystageMapper.getDelItemIds(saActivitystagePojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saActivitystageitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaActivitystageitemEntity saActivitystageitemEntity = new SaActivitystageitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaActivitystageitemPojo itemPojo =this.saActivitystageitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saActivitystageitemEntity);
               //设置id和Pid
               saActivitystageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saActivitystageitemEntity.setPid(saActivitystageEntity.getId());  // 主表 id
               saActivitystageitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saActivitystageitemMapper.insert(saActivitystageitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saActivitystageitemEntity);             
               this.saActivitystageitemMapper.update(saActivitystageitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saActivitystageEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaActivitystagePojo saActivitystagePojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaActivitystageitemPojo> lst = saActivitystagePojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saActivitystageitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.saActivitystageMapper.delete(key) ;
    }
    

    

    private static void cleanNull(SaActivitystagePojo saActivitystagePojo) {
        if(saActivitystagePojo.getStagename()==null) saActivitystagePojo.setStagename("");
        if(saActivitystagePojo.getStagetype()==null) saActivitystagePojo.setStagetype("");
        if(saActivitystagePojo.getStagedesc()==null) saActivitystagePojo.setStagedesc("");
        if(saActivitystagePojo.getRownum()==null) saActivitystagePojo.setRownum(0);
        if(saActivitystagePojo.getCreateby()==null) saActivitystagePojo.setCreateby("");
        if(saActivitystagePojo.getCreatebyid()==null) saActivitystagePojo.setCreatebyid("");
        if(saActivitystagePojo.getCreatedate()==null) saActivitystagePojo.setCreatedate(new Date());
        if(saActivitystagePojo.getLister()==null) saActivitystagePojo.setLister("");
        if(saActivitystagePojo.getListerid()==null) saActivitystagePojo.setListerid("");
        if(saActivitystagePojo.getModifydate()==null) saActivitystagePojo.setModifydate(new Date());
        if(saActivitystagePojo.getCustom1()==null) saActivitystagePojo.setCustom1("");
        if(saActivitystagePojo.getCustom2()==null) saActivitystagePojo.setCustom2("");
        if(saActivitystagePojo.getCustom3()==null) saActivitystagePojo.setCustom3("");
        if(saActivitystagePojo.getCustom4()==null) saActivitystagePojo.setCustom4("");
        if(saActivitystagePojo.getCustom5()==null) saActivitystagePojo.setCustom5("");
        if(saActivitystagePojo.getTenantid()==null) saActivitystagePojo.setTenantid("");
        if(saActivitystagePojo.getRevision()==null) saActivitystagePojo.setRevision(0);
   }

}
