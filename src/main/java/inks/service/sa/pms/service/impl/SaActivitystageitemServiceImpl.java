package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaActivitystageitemPojo;
import inks.service.sa.pms.domain.SaActivitystageitemEntity;
import inks.service.sa.pms.mapper.SaActivitystageitemMapper;
import inks.service.sa.pms.service.SaActivitystageitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 活动阶段子表(SaActivitystageitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-13 17:04:22
 */
@Service("saActivitystageitemService")
public class SaActivitystageitemServiceImpl implements SaActivitystageitemService {
    @Resource
    private SaActivitystageitemMapper saActivitystageitemMapper;

    @Override
    public SaActivitystageitemPojo getEntity(String key) {
        return this.saActivitystageitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaActivitystageitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaActivitystageitemPojo> lst = saActivitystageitemMapper.getPageList(queryParam);
            PageInfo<SaActivitystageitemPojo> pageInfo = new PageInfo<SaActivitystageitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaActivitystageitemPojo> getList(String Pid) { 
        try {
            List<SaActivitystageitemPojo> lst = saActivitystageitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaActivitystageitemPojo insert(SaActivitystageitemPojo saActivitystageitemPojo) {
        //初始化item的NULL
        SaActivitystageitemPojo itempojo =this.clearNull(saActivitystageitemPojo);
        SaActivitystageitemEntity saActivitystageitemEntity = new SaActivitystageitemEntity(); 
        BeanUtils.copyProperties(itempojo,saActivitystageitemEntity);
         //生成雪花id
          saActivitystageitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saActivitystageitemEntity.setRevision(1);  //乐观锁      
          this.saActivitystageitemMapper.insert(saActivitystageitemEntity);
        return this.getEntity(saActivitystageitemEntity.getId());
  
    }

    @Override
    public SaActivitystageitemPojo update(SaActivitystageitemPojo saActivitystageitemPojo) {
        SaActivitystageitemEntity saActivitystageitemEntity = new SaActivitystageitemEntity(); 
        BeanUtils.copyProperties(saActivitystageitemPojo,saActivitystageitemEntity);
        this.saActivitystageitemMapper.update(saActivitystageitemEntity);
        return this.getEntity(saActivitystageitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saActivitystageitemMapper.delete(key) ;
    }

     @Override
     public SaActivitystageitemPojo clearNull(SaActivitystageitemPojo saActivitystageitemPojo){
     //初始化NULL字段
     if(saActivitystageitemPojo.getPid()==null) saActivitystageitemPojo.setPid("");
     if(saActivitystageitemPojo.getItemname()==null) saActivitystageitemPojo.setItemname("");
     if(saActivitystageitemPojo.getStagejson()==null) saActivitystageitemPojo.setStagejson("");
     if(saActivitystageitemPojo.getRownum()==null) saActivitystageitemPojo.setRownum(0);
     if(saActivitystageitemPojo.getRemark()==null) saActivitystageitemPojo.setRemark("");
     if(saActivitystageitemPojo.getCreateby()==null) saActivitystageitemPojo.setCreateby("");
     if(saActivitystageitemPojo.getCreatebyid()==null) saActivitystageitemPojo.setCreatebyid("");
     if(saActivitystageitemPojo.getCreatedate()==null) saActivitystageitemPojo.setCreatedate(new Date());
     if(saActivitystageitemPojo.getLister()==null) saActivitystageitemPojo.setLister("");
     if(saActivitystageitemPojo.getListerid()==null) saActivitystageitemPojo.setListerid("");
     if(saActivitystageitemPojo.getModifydate()==null) saActivitystageitemPojo.setModifydate(new Date());
     if(saActivitystageitemPojo.getCustom1()==null) saActivitystageitemPojo.setCustom1("");
     if(saActivitystageitemPojo.getCustom2()==null) saActivitystageitemPojo.setCustom2("");
     if(saActivitystageitemPojo.getCustom3()==null) saActivitystageitemPojo.setCustom3("");
     if(saActivitystageitemPojo.getCustom4()==null) saActivitystageitemPojo.setCustom4("");
     if(saActivitystageitemPojo.getCustom5()==null) saActivitystageitemPojo.setCustom5("");
     if(saActivitystageitemPojo.getTenantid()==null) saActivitystageitemPojo.setTenantid("");
     if(saActivitystageitemPojo.getRevision()==null) saActivitystageitemPojo.setRevision(0);
     return saActivitystageitemPojo;
     }
}
