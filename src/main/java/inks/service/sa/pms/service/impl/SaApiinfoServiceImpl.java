package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaApiinfoEntity;
import inks.service.sa.pms.domain.pojo.SaApiinfoPojo;
import inks.service.sa.pms.mapper.SaApiinfoMapper;
import inks.service.sa.pms.service.SaApiinfoService;
import inks.service.sa.pms.utils.PrintColor;
import inks.service.sa.pms.utils.SwaggerImportUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 接口信息表(SaApiinfo)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15 17:09:56
 */
@Service("saApiinfoService")
public class SaApiinfoServiceImpl implements SaApiinfoService {
    @Resource
    private SaApiinfoMapper saApiinfoMapper;

    @Override
    public SaApiinfoPojo getEntity(String key) {
        return this.saApiinfoMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaApiinfoPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaApiinfoPojo> lst = saApiinfoMapper.getPageList(queryParam);
            PageInfo<SaApiinfoPojo> pageInfo = new PageInfo<SaApiinfoPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaApiinfoPojo insert(SaApiinfoPojo saApiinfoPojo) {
        //初始化NULL字段
        cleanNull(saApiinfoPojo);
        SaApiinfoEntity saApiinfoEntity = new SaApiinfoEntity();
        BeanUtils.copyProperties(saApiinfoPojo, saApiinfoEntity);
        //生成雪花id
        saApiinfoEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saApiinfoEntity.setRevision(1);  //乐观锁
        this.saApiinfoMapper.insert(saApiinfoEntity);
        return this.getEntity(saApiinfoEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saApiinfoPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaApiinfoPojo update(SaApiinfoPojo saApiinfoPojo) {
        SaApiinfoEntity saApiinfoEntity = new SaApiinfoEntity();
        BeanUtils.copyProperties(saApiinfoPojo, saApiinfoEntity);
        this.saApiinfoMapper.update(saApiinfoEntity);
        return this.getEntity(saApiinfoEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saApiinfoMapper.delete(key);
    }


    private static void cleanNull(SaApiinfoPojo saApiinfoPojo) {
        if (saApiinfoPojo.getFnid() == null) saApiinfoPojo.setFnid("");
        if (saApiinfoPojo.getFncode() == null) saApiinfoPojo.setFncode("");
        if (saApiinfoPojo.getApiname() == null) saApiinfoPojo.setApiname("");
        if (saApiinfoPojo.getApidescription() == null) saApiinfoPojo.setApidescription("");
        if (saApiinfoPojo.getApiurl() == null) saApiinfoPojo.setApiurl("");
        if (saApiinfoPojo.getHttpmethod() == null) saApiinfoPojo.setHttpmethod("");
        if (saApiinfoPojo.getRequestparams() == null) saApiinfoPojo.setRequestparams("");
        if (saApiinfoPojo.getResponseparams() == null) saApiinfoPojo.setResponseparams("");
        if (saApiinfoPojo.getResponseexample() == null) saApiinfoPojo.setResponseexample("");
        if (saApiinfoPojo.getStatuscode() == null) saApiinfoPojo.setStatuscode("");
        if (saApiinfoPojo.getOperationid() == null) saApiinfoPojo.setOperationid("");
        if (saApiinfoPojo.getProduces() == null) saApiinfoPojo.setProduces("");
        if (saApiinfoPojo.getConsumes() == null) saApiinfoPojo.setConsumes("");
        if (saApiinfoPojo.getTags() == null) saApiinfoPojo.setTags("");
        if (saApiinfoPojo.getIsdeprecated() == null) saApiinfoPojo.setIsdeprecated(0);
        if (saApiinfoPojo.getCurl() == null) saApiinfoPojo.setCurl("");
        if (saApiinfoPojo.getRownum() == null) saApiinfoPojo.setRownum(0);
        if (saApiinfoPojo.getRemark() == null) saApiinfoPojo.setRemark("");
        if (saApiinfoPojo.getCreateby() == null) saApiinfoPojo.setCreateby("");
        if (saApiinfoPojo.getCreatebyid() == null) saApiinfoPojo.setCreatebyid("");
        if (saApiinfoPojo.getCreatedate() == null) saApiinfoPojo.setCreatedate(new Date());
        if (saApiinfoPojo.getLister() == null) saApiinfoPojo.setLister("");
        if (saApiinfoPojo.getListerid() == null) saApiinfoPojo.setListerid("");
        if (saApiinfoPojo.getModifydate() == null) saApiinfoPojo.setModifydate(new Date());
        if (saApiinfoPojo.getCustom1() == null) saApiinfoPojo.setCustom1("");
        if (saApiinfoPojo.getCustom2() == null) saApiinfoPojo.setCustom2("");
        if (saApiinfoPojo.getCustom3() == null) saApiinfoPojo.setCustom3("");
        if (saApiinfoPojo.getCustom4() == null) saApiinfoPojo.setCustom4("");
        if (saApiinfoPojo.getCustom5() == null) saApiinfoPojo.setCustom5("");
        if (saApiinfoPojo.getTenantid() == null) saApiinfoPojo.setTenantid("");
        if (saApiinfoPojo.getTenantname() == null) saApiinfoPojo.setTenantname("");
        if (saApiinfoPojo.getRevision() == null) saApiinfoPojo.setRevision(0);
    }

    public String importSwagger(String swaggerUrl, String fnid, String fncode, LoginUser loginUser) {
        List<SaApiinfoPojo> saApiinfoPojos = SwaggerImportUtils.importSwagger(swaggerUrl, fnid, fncode, loginUser);
        int batchSize = 50; // 每次批量插入的大小
        int total = saApiinfoPojos.size();
        int batchCount = (total + batchSize - 1) / batchSize; // 计算批次总数

        int totalInserted = 0; // 累计插入的数量

        for (int i = 0; i < batchCount; i++) {
            // 计算当前批次的起始索引和结束索引
            int start = i * batchSize;
            int end = Math.min((i + 1) * batchSize, total);
            List<SaApiinfoPojo> batchList = saApiinfoPojos.subList(start, end);

            // 批量插入
            saApiinfoMapper.insertBatch(batchList);

            // 计算当前批次插入的数量
            int batchInserted = batchList.size();
            totalInserted += batchInserted;

            // 打印插入进度，包括第几次插入
            PrintColor.yellow("第 " + (i + 1) + " 次插入，当前批次插入 " + batchInserted + " 个接口，累计插入 " + totalInserted + " 个接口");
        }

        return "成功导入 " + total + " 个接口";
    }


}
