package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaCasecenterEntity;
import inks.service.sa.pms.domain.pojo.SaCasecenterPojo;
import inks.service.sa.pms.mapper.SaCasecenterMapper;
import inks.service.sa.pms.service.SaCasecenterService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 案例中心(SaCasecenter)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-03-12 16:07:23
 */
@Service("saCasecenterService")
public class SaCasecenterServiceImpl implements SaCasecenterService {
    @Resource
    private SaCasecenterMapper saCasecenterMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaCasecenterPojo getEntity(String key) {
        return this.saCasecenterMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaCasecenterPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaCasecenterPojo> lst = saCasecenterMapper.getPageList(queryParam);
            PageInfo<SaCasecenterPojo> pageInfo = new PageInfo<SaCasecenterPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saCasecenterPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCasecenterPojo insert(SaCasecenterPojo saCasecenterPojo) {
        //初始化NULL字段
        if (saCasecenterPojo.getTitle() == null) saCasecenterPojo.setTitle("");
        if (saCasecenterPojo.getCategory() == null) saCasecenterPojo.setCategory("");
        if (saCasecenterPojo.getCoverimages() == null) saCasecenterPojo.setCoverimages("");
        if (saCasecenterPojo.getBrief() == null) saCasecenterPojo.setBrief("");
        if (saCasecenterPojo.getContent() == null) saCasecenterPojo.setContent("");
        if (saCasecenterPojo.getPublicmark() == null) saCasecenterPojo.setPublicmark(0);
        if (saCasecenterPojo.getRownum() == null) saCasecenterPojo.setRownum(0);
        if (saCasecenterPojo.getRemark() == null) saCasecenterPojo.setRemark("");
        if (saCasecenterPojo.getCreateby() == null) saCasecenterPojo.setCreateby("");
        if (saCasecenterPojo.getCreatebyid() == null) saCasecenterPojo.setCreatebyid("");
        if (saCasecenterPojo.getCreatedate() == null) saCasecenterPojo.setCreatedate(new Date());
        if (saCasecenterPojo.getLister() == null) saCasecenterPojo.setLister("");
        if (saCasecenterPojo.getListerid() == null) saCasecenterPojo.setListerid("");
        if (saCasecenterPojo.getModifydate() == null) saCasecenterPojo.setModifydate(new Date());
        if (saCasecenterPojo.getCustom1() == null) saCasecenterPojo.setCustom1("");
        if (saCasecenterPojo.getCustom2() == null) saCasecenterPojo.setCustom2("");
        if (saCasecenterPojo.getCustom3() == null) saCasecenterPojo.setCustom3("");
        if (saCasecenterPojo.getCustom4() == null) saCasecenterPojo.setCustom4("");
        if (saCasecenterPojo.getCustom5() == null) saCasecenterPojo.setCustom5("");
        if (saCasecenterPojo.getTenantid() == null) saCasecenterPojo.setTenantid("");
        if (saCasecenterPojo.getRevision() == null) saCasecenterPojo.setRevision(0);
        SaCasecenterEntity saCasecenterEntity = new SaCasecenterEntity();
        BeanUtils.copyProperties(saCasecenterPojo, saCasecenterEntity);
        //生成雪花id
        saCasecenterEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saCasecenterEntity.setRevision(1);  //乐观锁
        this.saCasecenterMapper.insert(saCasecenterEntity);
        return this.getEntity(saCasecenterEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saCasecenterPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCasecenterPojo update(SaCasecenterPojo saCasecenterPojo) {
        SaCasecenterEntity saCasecenterEntity = new SaCasecenterEntity();
        BeanUtils.copyProperties(saCasecenterPojo, saCasecenterEntity);
        this.saCasecenterMapper.update(saCasecenterEntity);
        return this.getEntity(saCasecenterEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saCasecenterMapper.delete(key);
    }


}
