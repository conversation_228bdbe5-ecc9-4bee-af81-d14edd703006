package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaChatterEntity;
import inks.service.sa.pms.domain.pojo.SaChatterPojo;
import inks.service.sa.pms.mapper.SaChatterMapper;
import inks.service.sa.pms.service.SaChatterService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * PMS客服(SaChatter)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-21 13:22:05
 */
@Service("saChatterService")
public class SaChatterServiceImpl implements SaChatterService {
    @Resource
    private SaChatterMapper saChatterMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaChatterPojo getEntity(String key) {
        return this.saChatterMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaChatterPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaChatterPojo> lst = saChatterMapper.getPageList(queryParam);
            PageInfo<SaChatterPojo> pageInfo = new PageInfo<SaChatterPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saChatterPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaChatterPojo insert(SaChatterPojo saChatterPojo) {
        //初始化NULL字段
        if (saChatterPojo.getUserid() == null) saChatterPojo.setUserid("");
        if (saChatterPojo.getNickname() == null) saChatterPojo.setNickname("");
        if (saChatterPojo.getStatemark() == null) saChatterPojo.setStatemark(0);
        if (saChatterPojo.getAvatar() == null) saChatterPojo.setAvatar("");
        if (saChatterPojo.getSex() == null) saChatterPojo.setSex(0);
        if (saChatterPojo.getMobile() == null) saChatterPojo.setMobile("");
        if (saChatterPojo.getEmail() == null) saChatterPojo.setEmail("");
        if (saChatterPojo.getLeadermark() == null) saChatterPojo.setLeadermark(0);
        if (saChatterPojo.getRownum() == null) saChatterPojo.setRownum(0);
        if (saChatterPojo.getRemark() == null) saChatterPojo.setRemark("");
        if (saChatterPojo.getCreateby() == null) saChatterPojo.setCreateby("");
        if (saChatterPojo.getCreatebyid() == null) saChatterPojo.setCreatebyid("");
        if (saChatterPojo.getCreatedate() == null) saChatterPojo.setCreatedate(new Date());
        if (saChatterPojo.getLister() == null) saChatterPojo.setLister("");
        if (saChatterPojo.getListerid() == null) saChatterPojo.setListerid("");
        if (saChatterPojo.getModifydate() == null) saChatterPojo.setModifydate(new Date());
        if (saChatterPojo.getCustom1() == null) saChatterPojo.setCustom1("");
        if (saChatterPojo.getCustom2() == null) saChatterPojo.setCustom2("");
        if (saChatterPojo.getCustom3() == null) saChatterPojo.setCustom3("");
        if (saChatterPojo.getCustom4() == null) saChatterPojo.setCustom4("");
        if (saChatterPojo.getCustom5() == null) saChatterPojo.setCustom5("");
        if (saChatterPojo.getCustom6() == null) saChatterPojo.setCustom6("");
        if (saChatterPojo.getCustom7() == null) saChatterPojo.setCustom7("");
        if (saChatterPojo.getCustom8() == null) saChatterPojo.setCustom8("");
        if (saChatterPojo.getCustom9() == null) saChatterPojo.setCustom9("");
        if (saChatterPojo.getCustom10() == null) saChatterPojo.setCustom10("");
        if (saChatterPojo.getTenantid() == null) saChatterPojo.setTenantid("");
        if (saChatterPojo.getTenantname() == null) saChatterPojo.setTenantname("");
        if (saChatterPojo.getRevision() == null) saChatterPojo.setRevision(0);
        SaChatterEntity saChatterEntity = new SaChatterEntity();
        BeanUtils.copyProperties(saChatterPojo, saChatterEntity);
        //生成雪花id
        saChatterEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saChatterEntity.setRevision(1);  //乐观锁
        this.saChatterMapper.insert(saChatterEntity);
        return this.getEntity(saChatterEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saChatterPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaChatterPojo update(SaChatterPojo saChatterPojo) {
        SaChatterEntity saChatterEntity = new SaChatterEntity();
        BeanUtils.copyProperties(saChatterPojo, saChatterEntity);
        this.saChatterMapper.update(saChatterEntity);
        return this.getEntity(saChatterEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saChatterMapper.delete(key);
    }


}
