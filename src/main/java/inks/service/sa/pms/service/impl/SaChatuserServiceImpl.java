package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaChatuserEntity;
import inks.service.sa.pms.domain.pojo.SaChatuserPojo;
import inks.service.sa.pms.mapper.SaChatuserMapper;
import inks.service.sa.pms.service.SaChatuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 对话用户(SaChatuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-12-21 15:01:55
 */
@Service("saChatuserService")
public class SaChatuserServiceImpl implements SaChatuserService {
    @Resource
    private SaChatuserMapper saChatuserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaChatuserPojo getEntity(String key) {
        return this.saChatuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaChatuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaChatuserPojo> lst = saChatuserMapper.getPageList(queryParam);
            PageInfo<SaChatuserPojo> pageInfo = new PageInfo<SaChatuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saChatuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaChatuserPojo insert(SaChatuserPojo saChatuserPojo) {
        //初始化NULL字段
        if (saChatuserPojo.getUsername() == null) saChatuserPojo.setUsername("");
        if (saChatuserPojo.getUserid() == null) saChatuserPojo.setUserid("");
        if (saChatuserPojo.getRealname() == null) saChatuserPojo.setRealname("");
        if (saChatuserPojo.getAvatar() == null) saChatuserPojo.setAvatar("");
        if (saChatuserPojo.getIsadmin() == null) saChatuserPojo.setIsadmin(0);
        if (saChatuserPojo.getIpaddr() == null) saChatuserPojo.setIpaddr("");
        if (saChatuserPojo.getGroupids() == null) saChatuserPojo.setGroupids("");
        if (saChatuserPojo.getLogid() == null) saChatuserPojo.setLogid("");
        if (saChatuserPojo.getHostsystem() == null) saChatuserPojo.setHostsystem("");
        if (saChatuserPojo.getRownum() == null) saChatuserPojo.setRownum(0);
        if (saChatuserPojo.getRemark() == null) saChatuserPojo.setRemark("");
        if (saChatuserPojo.getCreateby() == null) saChatuserPojo.setCreateby("");
        if (saChatuserPojo.getCreatebyid() == null) saChatuserPojo.setCreatebyid("");
        if (saChatuserPojo.getCreatedate() == null) saChatuserPojo.setCreatedate(new Date());
        if (saChatuserPojo.getLister() == null) saChatuserPojo.setLister("");
        if (saChatuserPojo.getListerid() == null) saChatuserPojo.setListerid("");
        if (saChatuserPojo.getModifydate() == null) saChatuserPojo.setModifydate(new Date());
        if (saChatuserPojo.getCustom1() == null) saChatuserPojo.setCustom1("");
        if (saChatuserPojo.getCustom2() == null) saChatuserPojo.setCustom2("");
        if (saChatuserPojo.getCustom3() == null) saChatuserPojo.setCustom3("");
        if (saChatuserPojo.getCustom4() == null) saChatuserPojo.setCustom4("");
        if (saChatuserPojo.getCustom5() == null) saChatuserPojo.setCustom5("");
        if (saChatuserPojo.getTenantid() == null) saChatuserPojo.setTenantid("");
        if (saChatuserPojo.getTenantname() == null) saChatuserPojo.setTenantname("");
        if (saChatuserPojo.getRevision() == null) saChatuserPojo.setRevision(0);
        SaChatuserEntity saChatuserEntity = new SaChatuserEntity();
        BeanUtils.copyProperties(saChatuserPojo, saChatuserEntity);
        //生成雪花id
        saChatuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saChatuserEntity.setRevision(1);  //乐观锁
        this.saChatuserMapper.insert(saChatuserEntity);
        return this.getEntity(saChatuserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saChatuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaChatuserPojo update(SaChatuserPojo saChatuserPojo) {
        SaChatuserEntity saChatuserEntity = new SaChatuserEntity();
        BeanUtils.copyProperties(saChatuserPojo, saChatuserEntity);
        this.saChatuserMapper.update(saChatuserEntity);
        return this.getEntity(saChatuserEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saChatuserMapper.delete(key);
    }


}
