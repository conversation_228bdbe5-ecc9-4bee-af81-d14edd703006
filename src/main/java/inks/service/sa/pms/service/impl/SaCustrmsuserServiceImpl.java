package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.service.sa.pms.domain.SaCustrmsuserEntity;
import inks.service.sa.pms.domain.pojo.SaCustrmsuserPojo;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import inks.service.sa.pms.mapper.SaCustrmsuserMapper;
import inks.service.sa.pms.service.SaCustrmsuserService;
import inks.service.sa.pms.service.SaRmsuserService;
import inks.service.sa.pms.service.SaScmuserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
/**
 * 客户RMS关系表(SaCustrmsuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-29 11:04:28
 */
@Service("saCustrmsuserService")
public class SaCustrmsuserServiceImpl implements SaCustrmsuserService {
    @Resource
    private SaCustrmsuserMapper saCustrmsuserMapper;
    @Resource
    private SaRmsuserService saRmsuserService;
    @Override
    public SaCustrmsuserPojo getEntity(String key) {
        return this.saCustrmsuserMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaCustrmsuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaCustrmsuserPojo> lst = saCustrmsuserMapper.getPageList(queryParam);
            PageInfo<SaCustrmsuserPojo> pageInfo = new PageInfo<SaCustrmsuserPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaCustrmsuserPojo insert(SaCustrmsuserPojo saCustrmsuserPojo) {
        //初始化NULL字段
        cleanNull(saCustrmsuserPojo);
        SaCustrmsuserEntity saCustrmsuserEntity = new SaCustrmsuserEntity(); 
        BeanUtils.copyProperties(saCustrmsuserPojo,saCustrmsuserEntity);
        //生成雪花id
          saCustrmsuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saCustrmsuserEntity.setRevision(1);  //乐观锁
          this.saCustrmsuserMapper.insert(saCustrmsuserEntity);
        return this.getEntity(saCustrmsuserEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saCustrmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCustrmsuserPojo update(SaCustrmsuserPojo saCustrmsuserPojo) {
        SaCustrmsuserEntity saCustrmsuserEntity = new SaCustrmsuserEntity(); 
        BeanUtils.copyProperties(saCustrmsuserPojo,saCustrmsuserEntity);
        this.saCustrmsuserMapper.update(saCustrmsuserEntity);
        return this.getEntity(saCustrmsuserEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saCustrmsuserMapper.delete(key) ;
    }
    

    private static void cleanNull(SaCustrmsuserPojo saCustrmsuserPojo) {
        if(saCustrmsuserPojo.getTenantid()==null) saCustrmsuserPojo.setTenantid("");
        if(saCustrmsuserPojo.getTenantname()==null) saCustrmsuserPojo.setTenantname("");
        if(saCustrmsuserPojo.getUserid()==null) saCustrmsuserPojo.setUserid("");
        if(saCustrmsuserPojo.getUsername()==null) saCustrmsuserPojo.setUsername("");
        if(saCustrmsuserPojo.getRealname()==null) saCustrmsuserPojo.setRealname("");
        if(saCustrmsuserPojo.getUsertype()==null) saCustrmsuserPojo.setUsertype(0);
        if(saCustrmsuserPojo.getIsadmin()==null) saCustrmsuserPojo.setIsadmin(0);
        if(saCustrmsuserPojo.getDeptid()==null) saCustrmsuserPojo.setDeptid("");
        if(saCustrmsuserPojo.getDeptcode()==null) saCustrmsuserPojo.setDeptcode("");
        if(saCustrmsuserPojo.getDeptname()==null) saCustrmsuserPojo.setDeptname("");
        if(saCustrmsuserPojo.getIsdeptadmin()==null) saCustrmsuserPojo.setIsdeptadmin(0);
        if(saCustrmsuserPojo.getDeptrownum()==null) saCustrmsuserPojo.setDeptrownum(0);
        if(saCustrmsuserPojo.getRownum()==null) saCustrmsuserPojo.setRownum(0);
        if(saCustrmsuserPojo.getUserstatus()==null) saCustrmsuserPojo.setUserstatus(0);
        if(saCustrmsuserPojo.getUsercode()==null) saCustrmsuserPojo.setUsercode("");
        if(saCustrmsuserPojo.getGroupids()==null) saCustrmsuserPojo.setGroupids("");
        if(saCustrmsuserPojo.getGroupnames()==null) saCustrmsuserPojo.setGroupnames("");
        if(saCustrmsuserPojo.getCreateby()==null) saCustrmsuserPojo.setCreateby("");
        if(saCustrmsuserPojo.getCreatebyid()==null) saCustrmsuserPojo.setCreatebyid("");
        if(saCustrmsuserPojo.getCreatedate()==null) saCustrmsuserPojo.setCreatedate(new Date());
        if(saCustrmsuserPojo.getLister()==null) saCustrmsuserPojo.setLister("");
        if(saCustrmsuserPojo.getListerid()==null) saCustrmsuserPojo.setListerid("");
        if(saCustrmsuserPojo.getModifydate()==null) saCustrmsuserPojo.setModifydate(new Date());
        if(saCustrmsuserPojo.getCustom1()==null) saCustrmsuserPojo.setCustom1("");
        if(saCustrmsuserPojo.getCustom2()==null) saCustrmsuserPojo.setCustom2("");
        if(saCustrmsuserPojo.getCustom3()==null) saCustrmsuserPojo.setCustom3("");
        if(saCustrmsuserPojo.getCustom4()==null) saCustrmsuserPojo.setCustom4("");
        if(saCustrmsuserPojo.getCustom5()==null) saCustrmsuserPojo.setCustom5("");
        if(saCustrmsuserPojo.getRevision()==null) saCustrmsuserPojo.setRevision(0);
   }

    @Override
    public SaCustrmsuserPojo getEntityByUserid(String key) {
        return this.saCustrmsuserMapper.getEntityByUserid(key);
    }

    @Override
    public List<SaCustrmsuserPojo> getListByUserid(String userid) {
        return this.saCustrmsuserMapper.getListByUserid(userid);
    }

    @Override
    public SaCustrmsuserPojo createRmsUser(SaCustrmsuserPojo saCustrmsuserPojo) throws Exception {
        //PitenantrmsuserPojo的用户信息拷贝到PirmsuserPojo
        SaRmsuserPojo saRmsuserPojo = new SaRmsuserPojo();
        BeanUtils.copyProperties(saCustrmsuserPojo, saRmsuserPojo);
        //1.判断是否有传入userid;无,新增RmsUser
        if (StringUtils.isBlank(saCustrmsuserPojo.getUserid())) {
            //新建RmsUser用户,并获取userid(给个默认密码)
            saRmsuserPojo.setUserpassword(AESUtil.Encrypt("123456")); //加密
            SaRmsuserPojo insert = saRmsuserService.insert(saRmsuserPojo);
            saCustrmsuserPojo.setUserid(insert.getUserid());
            //绑定租户,更新groupids,functids
            return this.insert(saCustrmsuserPojo);
        }
        //1.判断是否有传入userid;有,修改RmsUser
        else {
            saRmsuserService.update(saRmsuserPojo);
            //判断是否绑定当前租户
            SaCustrmsuserPojo entityByUserid = this.getEntityByUserid(saCustrmsuserPojo.getUserid());
            if (entityByUserid == null) {
                //未绑定:绑定租户,更新groupids,functids
                return this.insert(saCustrmsuserPojo);
            } else {
                //已绑定:更新groupids,functids
                return this.update(saCustrmsuserPojo);
            }
        }
    }

}
