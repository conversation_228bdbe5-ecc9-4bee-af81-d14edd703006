package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.service.sa.pms.domain.SaCustscmuserEntity;
import inks.service.sa.pms.domain.pojo.SaCustscmuserPojo;
import inks.service.sa.pms.domain.pojo.SaScmuserPojo;
import inks.service.sa.pms.mapper.SaCustscmuserMapper;
import inks.service.sa.pms.service.SaCustscmuserService;
import inks.service.sa.pms.service.SaScmuserService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 客户SCM关系表(SaCustscmuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:30
 */
@Service("saCustscmuserService")
public class SaCustscmuserServiceImpl implements SaCustscmuserService {
    @Resource
    private SaCustscmuserMapper saCustscmuserMapper;
    @Resource
    private SaScmuserService saScmuserService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaCustscmuserPojo getEntity(String key) {
        return this.saCustscmuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaCustscmuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaCustscmuserPojo> lst = saCustscmuserMapper.getPageList(queryParam);
            PageInfo<SaCustscmuserPojo> pageInfo = new PageInfo<SaCustscmuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saCustscmuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCustscmuserPojo insert(SaCustscmuserPojo saCustscmuserPojo) {
        //初始化NULL字段
        if (saCustscmuserPojo.getTenantid() == null) saCustscmuserPojo.setTenantid("");
        if (saCustscmuserPojo.getTenantname() == null) saCustscmuserPojo.setTenantname("");
        if (saCustscmuserPojo.getUserid() == null) saCustscmuserPojo.setUserid("");
        if (saCustscmuserPojo.getUsername() == null) saCustscmuserPojo.setUsername("");
        if (saCustscmuserPojo.getRealname() == null) saCustscmuserPojo.setRealname("");
        if (saCustscmuserPojo.getUsertype() == null) saCustscmuserPojo.setUsertype(0);
        if (saCustscmuserPojo.getIsadmin() == null) saCustscmuserPojo.setIsadmin(0);
        if (saCustscmuserPojo.getDeptid() == null) saCustscmuserPojo.setDeptid("");
        if (saCustscmuserPojo.getDeptcode() == null) saCustscmuserPojo.setDeptcode("");
        if (saCustscmuserPojo.getDeptname() == null) saCustscmuserPojo.setDeptname("");
        if (saCustscmuserPojo.getIsdeptadmin() == null) saCustscmuserPojo.setIsdeptadmin(0);
        if (saCustscmuserPojo.getDeptrownum() == null) saCustscmuserPojo.setDeptrownum(0);
        if (saCustscmuserPojo.getRownum() == null) saCustscmuserPojo.setRownum(0);
        if (saCustscmuserPojo.getUserstatus() == null) saCustscmuserPojo.setUserstatus(0);
        if (saCustscmuserPojo.getUsercode() == null) saCustscmuserPojo.setUsercode("");
        if (saCustscmuserPojo.getGroupids() == null) saCustscmuserPojo.setGroupids("");
        if (saCustscmuserPojo.getGroupnames() == null) saCustscmuserPojo.setGroupnames("");
        if (saCustscmuserPojo.getCreateby() == null) saCustscmuserPojo.setCreateby("");
        if (saCustscmuserPojo.getCreatebyid() == null) saCustscmuserPojo.setCreatebyid("");
        if (saCustscmuserPojo.getCreatedate() == null) saCustscmuserPojo.setCreatedate(new Date());
        if (saCustscmuserPojo.getLister() == null) saCustscmuserPojo.setLister("");
        if (saCustscmuserPojo.getListerid() == null) saCustscmuserPojo.setListerid("");
        if (saCustscmuserPojo.getModifydate() == null) saCustscmuserPojo.setModifydate(new Date());
        if (saCustscmuserPojo.getCustom1() == null) saCustscmuserPojo.setCustom1("");
        if (saCustscmuserPojo.getCustom2() == null) saCustscmuserPojo.setCustom2("");
        if (saCustscmuserPojo.getCustom3() == null) saCustscmuserPojo.setCustom3("");
        if (saCustscmuserPojo.getCustom4() == null) saCustscmuserPojo.setCustom4("");
        if (saCustscmuserPojo.getCustom5() == null) saCustscmuserPojo.setCustom5("");
        if (saCustscmuserPojo.getRevision() == null) saCustscmuserPojo.setRevision(0);
        SaCustscmuserEntity saCustscmuserEntity = new SaCustscmuserEntity();
        BeanUtils.copyProperties(saCustscmuserPojo, saCustscmuserEntity);
        //生成雪花id
        saCustscmuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saCustscmuserEntity.setRevision(1);  //乐观锁
        this.saCustscmuserMapper.insert(saCustscmuserEntity);
        return this.getEntity(saCustscmuserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saCustscmuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaCustscmuserPojo update(SaCustscmuserPojo saCustscmuserPojo) {
        SaCustscmuserEntity saCustscmuserEntity = new SaCustscmuserEntity();
        BeanUtils.copyProperties(saCustscmuserPojo, saCustscmuserEntity);
        this.saCustscmuserMapper.update(saCustscmuserEntity);
        return this.getEntity(saCustscmuserEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saCustscmuserMapper.delete(key);
    }

    @Override
    public SaCustscmuserPojo getEntityByUserid(String key) {
        return this.saCustscmuserMapper.getEntityByUserid(key);
    }

    @Override
    public List<SaCustscmuserPojo> getListByUserid(String userid) {
        return this.saCustscmuserMapper.getListByUserid(userid);
    }

    @Override
    public SaCustscmuserPojo createScmUser(SaCustscmuserPojo saCustscmuserPojo) throws Exception {
        //PitenantscmuserPojo的用户信息拷贝到PiscmuserPojo
        SaScmuserPojo saScmuserPojo = new SaScmuserPojo();
        BeanUtils.copyProperties(saCustscmuserPojo, saScmuserPojo);
        //1.判断是否有传入userid;无,新增ScmUser
        if (StringUtils.isBlank(saCustscmuserPojo.getUserid())) {
            //新建ScmUser用户,并获取userid(给个默认密码)
            saScmuserPojo.setUserpassword(AESUtil.Encrypt("123456")); //加密
            SaScmuserPojo insert = saScmuserService.insert(saScmuserPojo);
            saCustscmuserPojo.setUserid(insert.getUserid());
            //绑定租户,更新groupids,functids
            return this.insert(saCustscmuserPojo);
        }
        //1.判断是否有传入userid;有,修改ScmUser
        else {
            saScmuserService.update(saScmuserPojo);
            //判断是否绑定当前租户
            SaCustscmuserPojo entityByUserid = this.getEntityByUserid(saCustscmuserPojo.getUserid());
            if (entityByUserid == null) {
                //未绑定:绑定租户,更新groupids,functids
                return this.insert(saCustscmuserPojo);
            } else {
                //已绑定:更新groupids,functids
                return this.update(saCustscmuserPojo);
            }
        }
    }
}
