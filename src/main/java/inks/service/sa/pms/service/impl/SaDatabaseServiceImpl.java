package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDatabaseEntity;
import inks.service.sa.pms.domain.pojo.SaDatabasePojo;
import inks.service.sa.pms.mapper.SaDatabaseMapper;
import inks.service.sa.pms.service.SaDatabaseService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 数据库连接池(SaDatabase)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-04-05 15:39:30
 */
@Service("saDatabaseService")
public class SaDatabaseServiceImpl implements SaDatabaseService {
    @Resource
    private SaDatabaseMapper saDatabaseMapper;

    private static void cleanNull(SaDatabasePojo saDatabasePojo) {
        if (saDatabasePojo.getTitle() == null) saDatabasePojo.setTitle("");
        if (saDatabasePojo.getUrl() == null) saDatabasePojo.setUrl("");
        if (saDatabasePojo.getUsername() == null) saDatabasePojo.setUsername("");
        if (saDatabasePojo.getPassword() == null) saDatabasePojo.setPassword("");
        if (saDatabasePojo.getDriverclassname() == null) saDatabasePojo.setDriverclassname("");
        if (saDatabasePojo.getEnabledmark() == null) saDatabasePojo.setEnabledmark(0);
        if (saDatabasePojo.getRownum() == null) saDatabasePojo.setRownum(0);
        if (saDatabasePojo.getRemark() == null) saDatabasePojo.setRemark("");
        if (saDatabasePojo.getLister() == null) saDatabasePojo.setLister("");
        if (saDatabasePojo.getCreatedate() == null) saDatabasePojo.setCreatedate(new Date());
        if (saDatabasePojo.getModifydate() == null) saDatabasePojo.setModifydate(new Date());
    }

    @Override
    public SaDatabasePojo getEntity(String key) {
        return this.saDatabaseMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaDatabasePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDatabasePojo> lst = saDatabaseMapper.getPageList(queryParam);
            PageInfo<SaDatabasePojo> pageInfo = new PageInfo<SaDatabasePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaDatabasePojo insert(SaDatabasePojo saDatabasePojo) {
        //初始化NULL字段
        cleanNull(saDatabasePojo);
        SaDatabaseEntity saDatabaseEntity = new SaDatabaseEntity();
        BeanUtils.copyProperties(saDatabasePojo, saDatabaseEntity);
        //生成雪花id
        saDatabaseEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.saDatabaseMapper.insert(saDatabaseEntity);
        return this.getEntity(saDatabaseEntity.getId());

    }

    @Override
    public SaDatabasePojo update(SaDatabasePojo saDatabasePojo) {
        SaDatabaseEntity saDatabaseEntity = new SaDatabaseEntity();
        BeanUtils.copyProperties(saDatabasePojo, saDatabaseEntity);
        this.saDatabaseMapper.update(saDatabaseEntity);
        return this.getEntity(saDatabaseEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saDatabaseMapper.delete(key);
    }

}
