package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaDbdesignPojo;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemdetailPojo;
import inks.service.sa.pms.domain.SaDbdesignEntity;
import inks.service.sa.pms.domain.SaDbdesignitemEntity;
import inks.service.sa.pms.mapper.SaDbdesignMapper;
import inks.service.sa.pms.service.SaDbdesignService;
import inks.service.sa.pms.service.SaDbdesignitemService;
import inks.service.sa.pms.mapper.SaDbdesignitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 表格设计(SaDbdesign)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15 09:54:21
 */
@Service("saDbdesignService")
public class SaDbdesignServiceImpl implements SaDbdesignService {
    @Resource
    private SaDbdesignMapper saDbdesignMapper;
    
    @Resource
    private SaDbdesignitemMapper saDbdesignitemMapper;
    

    @Resource
    private SaDbdesignitemService saDbdesignitemService;
    

    @Override
    public SaDbdesignPojo getEntity(String key) {
        return this.saDbdesignMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDbdesignitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDbdesignitemdetailPojo> lst = saDbdesignMapper.getPageList(queryParam);
            PageInfo<SaDbdesignitemdetailPojo> pageInfo = new PageInfo<SaDbdesignitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaDbdesignPojo getBillEntity(String key) {
       try {
        //读取主表
        SaDbdesignPojo saDbdesignPojo = this.saDbdesignMapper.getEntity(key);
        //读取子表
        saDbdesignPojo.setItem(saDbdesignitemMapper.getList(saDbdesignPojo.getId()));
        return saDbdesignPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaDbdesignPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDbdesignPojo> lst = saDbdesignMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saDbdesignitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaDbdesignPojo> pageInfo = new PageInfo<SaDbdesignPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaDbdesignPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDbdesignPojo> lst = saDbdesignMapper.getPageTh(queryParam);
            PageInfo<SaDbdesignPojo> pageInfo = new PageInfo<SaDbdesignPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaDbdesignPojo insert(SaDbdesignPojo saDbdesignPojo) {
        //初始化NULL字段
        cleanNull(saDbdesignPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaDbdesignEntity saDbdesignEntity = new SaDbdesignEntity(); 
        BeanUtils.copyProperties(saDbdesignPojo,saDbdesignEntity);
        //设置id和新建日期
        saDbdesignEntity.setId(id);
        saDbdesignEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saDbdesignMapper.insert(saDbdesignEntity);
        //Item子表处理
        List<SaDbdesignitemPojo> lst = saDbdesignPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaDbdesignitemPojo itemPojo =this.saDbdesignitemService.clearNull(lst.get(i));
               SaDbdesignitemEntity saDbdesignitemEntity = new SaDbdesignitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saDbdesignitemEntity);
               //设置id和Pid
               saDbdesignitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saDbdesignitemEntity.setPid(id);
               saDbdesignitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saDbdesignitemMapper.insert(saDbdesignitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saDbdesignEntity.getId());
    }


    @Override
    @Transactional
    public SaDbdesignPojo update(SaDbdesignPojo saDbdesignPojo) {
        //主表更改
        SaDbdesignEntity saDbdesignEntity = new SaDbdesignEntity(); 
        BeanUtils.copyProperties(saDbdesignPojo,saDbdesignEntity);
        this.saDbdesignMapper.update(saDbdesignEntity);
        if (saDbdesignPojo.getItem() != null) {
        //Item子表处理
        List<SaDbdesignitemPojo> lst = saDbdesignPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saDbdesignMapper.getDelItemIds(saDbdesignPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saDbdesignitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaDbdesignitemEntity saDbdesignitemEntity = new SaDbdesignitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaDbdesignitemPojo itemPojo =this.saDbdesignitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saDbdesignitemEntity);
               //设置id和Pid
               saDbdesignitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saDbdesignitemEntity.setPid(saDbdesignEntity.getId());  // 主表 id
               saDbdesignitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saDbdesignitemMapper.insert(saDbdesignitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saDbdesignitemEntity);             
               this.saDbdesignitemMapper.update(saDbdesignitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saDbdesignEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaDbdesignPojo saDbdesignPojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaDbdesignitemPojo> lst = saDbdesignPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saDbdesignitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.saDbdesignMapper.delete(key) ;
    }
    

    

    private static void cleanNull(SaDbdesignPojo saDbdesignPojo) {
        if(saDbdesignPojo.getFnid()==null) saDbdesignPojo.setFnid("");
        if(saDbdesignPojo.getTablename()==null) saDbdesignPojo.setTablename("");
        if(saDbdesignPojo.getComment()==null) saDbdesignPojo.setComment("");
        if(saDbdesignPojo.getCharacterset()==null) saDbdesignPojo.setCharacterset("");
        if(saDbdesignPojo.getCollation()==null) saDbdesignPojo.setCollation("");
        if(saDbdesignPojo.getVersionnumber()==null) saDbdesignPojo.setVersionnumber("");
        if(saDbdesignPojo.getRownum()==null) saDbdesignPojo.setRownum(0);
        if(saDbdesignPojo.getRemark()==null) saDbdesignPojo.setRemark("");
        if(saDbdesignPojo.getCreateby()==null) saDbdesignPojo.setCreateby("");
        if(saDbdesignPojo.getCreatebyid()==null) saDbdesignPojo.setCreatebyid("");
        if(saDbdesignPojo.getCreatedate()==null) saDbdesignPojo.setCreatedate(new Date());
        if(saDbdesignPojo.getLister()==null) saDbdesignPojo.setLister("");
        if(saDbdesignPojo.getListerid()==null) saDbdesignPojo.setListerid("");
        if(saDbdesignPojo.getModifydate()==null) saDbdesignPojo.setModifydate(new Date());
        if(saDbdesignPojo.getCustom1()==null) saDbdesignPojo.setCustom1("");
        if(saDbdesignPojo.getCustom2()==null) saDbdesignPojo.setCustom2("");
        if(saDbdesignPojo.getCustom3()==null) saDbdesignPojo.setCustom3("");
        if(saDbdesignPojo.getCustom4()==null) saDbdesignPojo.setCustom4("");
        if(saDbdesignPojo.getCustom5()==null) saDbdesignPojo.setCustom5("");
        if(saDbdesignPojo.getTenantid()==null) saDbdesignPojo.setTenantid("");
        if(saDbdesignPojo.getTenantname()==null) saDbdesignPojo.setTenantname("");
        if(saDbdesignPojo.getRevision()==null) saDbdesignPojo.setRevision(0);
   }


    @Override
    public String alter(List<String> itemids) {
        // 获取表ID
        String pid = saDbdesignitemMapper.getPid(itemids.get(0));
        // 获取对应表的设计实体
        SaDbdesignPojo saDbdesignBill = getBillEntity(pid);

        // 获取表名
        String tablename = saDbdesignBill.getTablename();
        // 获取字段信息
        List<SaDbdesignitemPojo> itemList = saDbdesignBill.getItem();

        // 用来存储所有的 SQL 语句
        StringBuilder sqlBuilder = new StringBuilder();

        // 遍历字段列表，生成对应的 SQL 语句
        for (int i = 0; i < itemList.size(); i++) {
            SaDbdesignitemPojo item = itemList.get(i);

            // 只有在 itemid 存在于传入的 itemids 列表中时才生成语句
            if (itemids.contains(item.getId())) {
                if (item.getIndexmark() == 1) {
                    // 处理索引创建
                    String indexName = item.getFieldname().replace("索引", "");
                    String indexFields = item.getFieldtype();
                    sqlBuilder.append("# 索引创建\n")
                            .append("CREATE INDEX ")
                            .append(indexName)
                            .append(" ON ")
                            .append(tablename)
                            .append(" (")
                            .append(indexFields)
                            .append(");\n");
                } else {
                    // 处理字段添加
                    String fieldname = item.getFieldname();
                    String fieldtype = item.getFieldtype();
                    String comment = item.getComment();

                    // 获取前一个字段名，如果是第一个字段，则无需使用 AFTER 子句
                    String afterField = "";
                    // 向前查找最近的非索引字段
                    for (int j = i - 1; j >= 0; j--) {
                        if (itemList.get(j).getIndexmark() != 1) {
                            afterField = itemList.get(j).getFieldname();
                            break;
                        }
                    }

                    // 构建 ALTER 语句
                    sqlBuilder.append("# 字段插入\n")
                            .append("ALTER TABLE ")
                            .append(tablename)
                            .append(" ADD ")
                            .append(fieldname)
                            .append(" ")
                            .append(fieldtype);

                    // 根据 notnullmark 判断是否允许为空
                    if (item.getNotnullmark() == 1) {
                        sqlBuilder.append(" NOT NULL");
                    } else {
                        sqlBuilder.append(" NULL");
                    }

                    // 添加注释
                    if (comment != null && !comment.isEmpty()) {
                        sqlBuilder.append(" COMMENT '")
                                .append(comment)
                                .append("'");
                    }

                    // 如果存在前置字段，添加 AFTER 子句
                    if (!afterField.isEmpty()) {
                        sqlBuilder.append(" AFTER ")
                                .append(afterField);
                    }

                    sqlBuilder.append(";\n");
                }
            }
        }

        return sqlBuilder.toString();
    }


}
