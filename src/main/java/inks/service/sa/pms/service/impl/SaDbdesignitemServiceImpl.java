package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo;
import inks.service.sa.pms.domain.SaDbdesignitemEntity;
import inks.service.sa.pms.mapper.SaDbdesignitemMapper;
import inks.service.sa.pms.service.SaDbdesignitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 表格设计字段子表(SaDbdesignitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-15 12:56:15
 */
@Service("saDbdesignitemService")
public class SaDbdesignitemServiceImpl implements SaDbdesignitemService {
    @Resource
    private SaDbdesignitemMapper saDbdesignitemMapper;

    @Override
    public SaDbdesignitemPojo getEntity(String key) {
        return this.saDbdesignitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaDbdesignitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDbdesignitemPojo> lst = saDbdesignitemMapper.getPageList(queryParam);
            PageInfo<SaDbdesignitemPojo> pageInfo = new PageInfo<SaDbdesignitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaDbdesignitemPojo> getList(String Pid) { 
        try {
            List<SaDbdesignitemPojo> lst = saDbdesignitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaDbdesignitemPojo insert(SaDbdesignitemPojo saDbdesignitemPojo) {
        //初始化item的NULL
        SaDbdesignitemPojo itempojo =this.clearNull(saDbdesignitemPojo);
        SaDbdesignitemEntity saDbdesignitemEntity = new SaDbdesignitemEntity(); 
        BeanUtils.copyProperties(itempojo,saDbdesignitemEntity);
         //生成雪花id
          saDbdesignitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saDbdesignitemEntity.setRevision(1);  //乐观锁      
          this.saDbdesignitemMapper.insert(saDbdesignitemEntity);
        return this.getEntity(saDbdesignitemEntity.getId());
  
    }

    @Override
    public SaDbdesignitemPojo update(SaDbdesignitemPojo saDbdesignitemPojo) {
        SaDbdesignitemEntity saDbdesignitemEntity = new SaDbdesignitemEntity(); 
        BeanUtils.copyProperties(saDbdesignitemPojo,saDbdesignitemEntity);
        this.saDbdesignitemMapper.update(saDbdesignitemEntity);
        return this.getEntity(saDbdesignitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saDbdesignitemMapper.delete(key) ;
    }

     @Override
     public SaDbdesignitemPojo clearNull(SaDbdesignitemPojo saDbdesignitemPojo){
     //初始化NULL字段
     if(saDbdesignitemPojo.getPid()==null) saDbdesignitemPojo.setPid("");
     if(saDbdesignitemPojo.getFieldname()==null) saDbdesignitemPojo.setFieldname("");
     if(saDbdesignitemPojo.getFieldtype()==null) saDbdesignitemPojo.setFieldtype("");
     if(saDbdesignitemPojo.getComment()==null) saDbdesignitemPojo.setComment("");
     if(saDbdesignitemPojo.getPrimarymark()==null) saDbdesignitemPojo.setPrimarymark(0);
     if(saDbdesignitemPojo.getNotnullmark()==null) saDbdesignitemPojo.setNotnullmark(0);
     if(saDbdesignitemPojo.getCharacterset()==null) saDbdesignitemPojo.setCharacterset("");
     if(saDbdesignitemPojo.getCollation()==null) saDbdesignitemPojo.setCollation("");
     if(saDbdesignitemPojo.getDefvalue()==null) saDbdesignitemPojo.setDefvalue("");
     if(saDbdesignitemPojo.getCalcmark()==null) saDbdesignitemPojo.setCalcmark("");
     if(saDbdesignitemPojo.getCalcdesc()==null) saDbdesignitemPojo.setCalcdesc("");
     if(saDbdesignitemPojo.getCalcformula()==null) saDbdesignitemPojo.setCalcformula("");
     if(saDbdesignitemPojo.getIndexmark()==null) saDbdesignitemPojo.setIndexmark(0);
     if(saDbdesignitemPojo.getSensitivemark()==null) saDbdesignitemPojo.setSensitivemark(0);
     if(saDbdesignitemPojo.getRownum()==null) saDbdesignitemPojo.setRownum(0);
     if(saDbdesignitemPojo.getRemark()==null) saDbdesignitemPojo.setRemark("");
     if(saDbdesignitemPojo.getCreateby()==null) saDbdesignitemPojo.setCreateby("");
     if(saDbdesignitemPojo.getCreatebyid()==null) saDbdesignitemPojo.setCreatebyid("");
     if(saDbdesignitemPojo.getCreatedate()==null) saDbdesignitemPojo.setCreatedate(new Date());
     if(saDbdesignitemPojo.getLister()==null) saDbdesignitemPojo.setLister("");
     if(saDbdesignitemPojo.getListerid()==null) saDbdesignitemPojo.setListerid("");
     if(saDbdesignitemPojo.getModifydate()==null) saDbdesignitemPojo.setModifydate(new Date());
     if(saDbdesignitemPojo.getCustom1()==null) saDbdesignitemPojo.setCustom1("");
     if(saDbdesignitemPojo.getCustom2()==null) saDbdesignitemPojo.setCustom2("");
     if(saDbdesignitemPojo.getCustom3()==null) saDbdesignitemPojo.setCustom3("");
     if(saDbdesignitemPojo.getCustom4()==null) saDbdesignitemPojo.setCustom4("");
     if(saDbdesignitemPojo.getCustom5()==null) saDbdesignitemPojo.setCustom5("");
     if(saDbdesignitemPojo.getTenantid()==null) saDbdesignitemPojo.setTenantid("");
     if(saDbdesignitemPojo.getTenantname()==null) saDbdesignitemPojo.setTenantname("");
     if(saDbdesignitemPojo.getRevision()==null) saDbdesignitemPojo.setRevision(0);
     return saDbdesignitemPojo;
     }
}
