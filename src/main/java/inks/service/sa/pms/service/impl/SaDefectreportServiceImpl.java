package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDefectreportEntity;
import inks.service.sa.pms.domain.pojo.SaDefectreportPojo;
import inks.service.sa.pms.mapper.SaDefectreportMapper;
import inks.service.sa.pms.service.SaDefectreportService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 缺陷报告(SaDefectreport)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-26 12:52:57
 */
@Service("saDefectreportService")
public class SaDefectreportServiceImpl implements SaDefectreportService {
    @Resource
    private SaDefectreportMapper saDefectreportMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaDefectreportPojo getEntity(String key) {
        return this.saDefectreportMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaDefectreportPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDefectreportPojo> lst = saDefectreportMapper.getPageList(queryParam);
            PageInfo<SaDefectreportPojo> pageInfo = new PageInfo<SaDefectreportPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDefectreportPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDefectreportPojo insert(SaDefectreportPojo saDefectreportPojo) {
        //初始化NULL字段
        if (saDefectreportPojo.getRefno() == null) saDefectreportPojo.setRefno("");
        if (saDefectreportPojo.getSoftwarename() == null) saDefectreportPojo.setSoftwarename("");
        if (saDefectreportPojo.getSoftwareversion() == null) saDefectreportPojo.setSoftwareversion("");
        if (saDefectreportPojo.getDiscoverydate() == null) saDefectreportPojo.setDiscoverydate(new Date());
        if (saDefectreportPojo.getTester() == null) saDefectreportPojo.setTester("");
        if (saDefectreportPojo.getDescription() == null) saDefectreportPojo.setDescription("");
        if (saDefectreportPojo.getAttachment() == null) saDefectreportPojo.setAttachment("{}");
        if (saDefectreportPojo.getDefecttype() == null) saDefectreportPojo.setDefecttype("");
        if (saDefectreportPojo.getSeverity() == null) saDefectreportPojo.setSeverity("");
        if (saDefectreportPojo.getPriority() == null) saDefectreportPojo.setPriority("");
        if (saDefectreportPojo.getTestenvironment() == null) saDefectreportPojo.setTestenvironment("");
        if (saDefectreportPojo.getReproductionsteps() == null) saDefectreportPojo.setReproductionsteps("");
        if (saDefectreportPojo.getRemark() == null) saDefectreportPojo.setRemark("");
        if (saDefectreportPojo.getCreatebyid() == null) saDefectreportPojo.setCreatebyid("");
        if (saDefectreportPojo.getCreateby() == null) saDefectreportPojo.setCreateby("");
        if (saDefectreportPojo.getCreatedate() == null) saDefectreportPojo.setCreatedate(new Date());
        if (saDefectreportPojo.getListerid() == null) saDefectreportPojo.setListerid("");
        if (saDefectreportPojo.getLister() == null) saDefectreportPojo.setLister("");
        if (saDefectreportPojo.getModifydate() == null) saDefectreportPojo.setModifydate(new Date());
        if (saDefectreportPojo.getCustom1() == null) saDefectreportPojo.setCustom1("");
        if (saDefectreportPojo.getCustom2() == null) saDefectreportPojo.setCustom2("");
        if (saDefectreportPojo.getCustom3() == null) saDefectreportPojo.setCustom3("");
        if (saDefectreportPojo.getCustom4() == null) saDefectreportPojo.setCustom4("");
        if (saDefectreportPojo.getCustom5() == null) saDefectreportPojo.setCustom5("");
        if (saDefectreportPojo.getRevision() == null) saDefectreportPojo.setRevision(0);
        SaDefectreportEntity saDefectreportEntity = new SaDefectreportEntity();
        BeanUtils.copyProperties(saDefectreportPojo, saDefectreportEntity);
        //生成雪花id
        saDefectreportEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDefectreportEntity.setRevision(1);  //乐观锁
        this.saDefectreportMapper.insert(saDefectreportEntity);
        return this.getEntity(saDefectreportEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDefectreportPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDefectreportPojo update(SaDefectreportPojo saDefectreportPojo) {
        SaDefectreportEntity saDefectreportEntity = new SaDefectreportEntity();
        BeanUtils.copyProperties(saDefectreportPojo, saDefectreportEntity);
        this.saDefectreportMapper.update(saDefectreportEntity);
        return this.getEntity(saDefectreportEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saDefectreportMapper.delete(key);
    }


}
