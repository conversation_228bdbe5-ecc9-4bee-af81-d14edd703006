package inks.service.sa.pms.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.redis.service.RedisService;
import inks.sa.common.core.domain.vo.MyLoginUser;
import inks.sa.common.core.domain.vo.SaEngineerPojo;
import inks.service.sa.pms.controller.S06M23B1Controller;
import inks.service.sa.pms.domain.SaDemandEntity;
import inks.service.sa.pms.domain.pojo.SaDemandPojo;
import inks.service.sa.pms.domain.pojo.SaDemandlogPojo;
import inks.service.sa.pms.domain.pojo.SaDemandstatusPojo;
import inks.service.sa.pms.domain.pojo.SaProjectstatusPojo;
import inks.service.sa.pms.mapper.*;
import inks.service.sa.pms.service.SaDemandService;
import inks.service.sa.pms.service.SaDemandlogService;
import inks.service.sa.pms.service.SaEngineergroupService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * 产品需求(SaDemand)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-26 14:53:13
 */
@Service("saDemandService")
public class SaDemandServiceImpl implements SaDemandService {
    @Resource
    private SaDemandstatusMapper saDemandstatusMapper;
    @Resource
    private SaDemandMapper saDemandMapper;
    @Resource
    private SaDemandlogService saDemandlogService;

    @Resource
    private SaProjectstatusMapper saProjectstatusMapper;
    @Resource
    private SaProjectMapper saProjectMapper;
    @Resource
    private SaEngineerMapper saEngineerMapper;
    @Resource
    private pmsSaUserMapper pmsSaUserMapper;
    @Resource
    private S06M23B1Controller s06M23B1Controller;

    @Resource
    private SaEngineergroupMapper saEngineergroupMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaDemandPojo getEntity(String key) {
        SaDemandPojo saDemandPojo = this.saDemandMapper.getEntity(key);
        if (saDemandPojo != null) {
            // 递归查询子需求
            List<SaDemandPojo> children = fetchChildren(key);
            saDemandPojo.setChildren(children);
        }
        return saDemandPojo;
    }

    private List<SaDemandPojo> fetchChildren(String parentKey) {
        List<SaDemandPojo> children = this.saDemandMapper.getListByParentid(parentKey);
        if (CollectionUtils.isNotEmpty(children)) {
            for (SaDemandPojo child : children) {
                List<SaDemandPojo> subChildren = fetchChildren(child.getId());
                if (CollectionUtils.isNotEmpty(subChildren)) {
                    child.setChildren(subChildren);
                }
            }
        }
        return children;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaDemandPojo> getPageListUNIONALL(QueryParam queryParam, Integer finishlimit) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandPojo> lst = saDemandMapper.getPageListUNIONALL(queryParam, finishlimit);
            PageInfo<SaDemandPojo> pageInfo = new PageInfo<SaDemandPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaDemandPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandPojo> lst = saDemandMapper.getPageList(queryParam);
            PageInfo<SaDemandPojo> pageInfo = new PageInfo<SaDemandPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaDemandPojo> getAllPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandPojo> lst = saDemandMapper.getAllPageList(queryParam);
            PageInfo<SaDemandPojo> pageInfo = new PageInfo<SaDemandPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaDemandPojo> getTodoPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandPojo> lst = saDemandMapper.getTodoPageList(queryParam);
            PageInfo<SaDemandPojo> pageInfo = new PageInfo<SaDemandPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDemandPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDemandPojo insert(SaDemandPojo saDemandPojo, LoginUser loginUser) {
        //初始化NULL字段
        if (saDemandPojo.getParentid() == null) saDemandPojo.setParentid("");
        if (saDemandPojo.getRefno() == null) saDemandPojo.setRefno("");
        if (saDemandPojo.getBilldate() == null) saDemandPojo.setBilldate(new Date());
        if (saDemandPojo.getBilltype() == null) saDemandPojo.setBilltype("");
        if (saDemandPojo.getBilltitle() == null) saDemandPojo.setBilltitle("");
        if (saDemandPojo.getGroupid() == null) saDemandPojo.setGroupid("");
        if (saDemandPojo.getTodoid() == null) saDemandPojo.setTodoid("");
        if (saDemandPojo.getLevel() == null) saDemandPojo.setLevel(0);
        if (saDemandPojo.getWeekmark() == null) saDemandPojo.setWeekmark(0);
        if (saDemandPojo.getProjectid() == null) saDemandPojo.setProjectid("");
        if (saDemandPojo.getItemcode() == null) saDemandPojo.setItemcode("");
        if (saDemandPojo.getItemname() == null) saDemandPojo.setItemname("");
        if (saDemandPojo.getDescription() == null) saDemandPojo.setDescription("");
        if (saDemandPojo.getLabeljson() == null) saDemandPojo.setLabeljson("[]");
        if (saDemandPojo.getDemandlabeljson() == null) saDemandPojo.setDemandlabeljson("[]");
        if (saDemandPojo.getStatus() == null) saDemandPojo.setStatus("");
        if (saDemandPojo.getDemandstatus() == null) saDemandPojo.setDemandstatus("");
        if (saDemandPojo.getTimestatus()==null) saDemandPojo.setTimestatus("");
//     需求管理-创建任务是不需要给默认开始和结束时间
//     if(saDemandPojo.getStartdate()==null) saDemandPojo.setStartdate(new Date());
//     if(saDemandPojo.getDeaddate()==null) saDemandPojo.setDeaddate(new Date());
        if (saDemandPojo.getFinishdate() == null) saDemandPojo.setFinishdate(new Date());
        if (saDemandPojo.getWorktime() == null) saDemandPojo.setWorktime(0D);
        if (saDemandPojo.getDemandsubmitid() == null) saDemandPojo.setDemandsubmitid("");
        if (saDemandPojo.getAppointeeid() == null) saDemandPojo.setAppointeeid("");
        if (saDemandPojo.getAppointee() == null) saDemandPojo.setAppointee("");
        if (saDemandPojo.getOperator() == null) saDemandPojo.setOperator("");
        if (saDemandPojo.getOperatorid() == null) saDemandPojo.setOperatorid("");
        if (saDemandPojo.getCollaboratorids() == null) saDemandPojo.setCollaboratorids("");
        if (saDemandPojo.getCollaborators() == null) saDemandPojo.setCollaborators("");
        if (saDemandPojo.getLongitude() == null) saDemandPojo.setLongitude("");
        if (saDemandPojo.getLatitude() == null) saDemandPojo.setLatitude("");
        if (saDemandPojo.getLocation() == null) saDemandPojo.setLocation("");
        if (saDemandPojo.getPhone() == null) saDemandPojo.setPhone("");
        if (saDemandPojo.getCloser() == null) saDemandPojo.setCloser("");
        if (saDemandPojo.getCloserid() == null) saDemandPojo.setCloserid("");
        if (saDemandPojo.getPraise() == null) saDemandPojo.setPraise(0);
        if (saDemandPojo.getProcesscomment() == null) saDemandPojo.setProcesscomment("");
        if (saDemandPojo.getPictureurl1() == null) saDemandPojo.setPictureurl1("");
        if (saDemandPojo.getPictureurl2() == null) saDemandPojo.setPictureurl2("");
        if (saDemandPojo.getPictureurl3() == null) saDemandPojo.setPictureurl3("");
        if (saDemandPojo.getPictureurl4() == null) saDemandPojo.setPictureurl4("");
        if (saDemandPojo.getPictureurl5() == null) saDemandPojo.setPictureurl5("");
        if (saDemandPojo.getFinpictureurl1() == null) saDemandPojo.setFinpictureurl1("");
        if (saDemandPojo.getFinpictureurl2() == null) saDemandPojo.setFinpictureurl2("");
        if (saDemandPojo.getFinpictureurl3() == null) saDemandPojo.setFinpictureurl3("");
        if (saDemandPojo.getFinpictureurl4() == null) saDemandPojo.setFinpictureurl4("");
        if (saDemandPojo.getFinpictureurl5() == null) saDemandPojo.setFinpictureurl5("");
        if (saDemandPojo.getFinishdes() == null) saDemandPojo.setFinishdes("");
        if (saDemandPojo.getRemark() == null) saDemandPojo.setRemark("");
        if (saDemandPojo.getDisannulmark() == null) saDemandPojo.setDisannulmark(0);
        if (saDemandPojo.getFinishmark() == null) saDemandPojo.setFinishmark(0);
        if (saDemandPojo.getCreateby() == null) saDemandPojo.setCreateby("");
        if (saDemandPojo.getCreatebyid() == null) saDemandPojo.setCreatebyid("");
        if (saDemandPojo.getCreatedate() == null) saDemandPojo.setCreatedate(new Date());
        if (saDemandPojo.getLister() == null) saDemandPojo.setLister("");
        if (saDemandPojo.getListerid() == null) saDemandPojo.setListerid("");
        if (saDemandPojo.getModifydate() == null) saDemandPojo.setModifydate(new Date());
        if(saDemandPojo.getStatusmodifydate()==null) saDemandPojo.setStatusmodifydate(new Date());
        if(saDemandPojo.getEngineergroupid()==null) saDemandPojo.setEngineergroupid("");
        if (saDemandPojo.getCustom1() == null) saDemandPojo.setCustom1("");
        if (saDemandPojo.getCustom2() == null) saDemandPojo.setCustom2("");
        if (saDemandPojo.getCustom3() == null) saDemandPojo.setCustom3("");
        if (saDemandPojo.getCustom4() == null) saDemandPojo.setCustom4("");
        if (saDemandPojo.getCustom5() == null) saDemandPojo.setCustom5("");
        if (saDemandPojo.getCustom6() == null) saDemandPojo.setCustom6("");
        if (saDemandPojo.getCustom7() == null) saDemandPojo.setCustom7("");
        if (saDemandPojo.getCustom8() == null) saDemandPojo.setCustom8("");
        if (saDemandPojo.getCustom9() == null) saDemandPojo.setCustom9("");
        if (saDemandPojo.getCustom10() == null) saDemandPojo.setCustom10("");
        if (saDemandPojo.getDeptid() == null) saDemandPojo.setDeptid("");
        if (saDemandPojo.getTenantid() == null) saDemandPojo.setTenantid("");
        if (saDemandPojo.getTenantname() == null) saDemandPojo.setTenantname("");
        if (saDemandPojo.getRevision() == null) saDemandPojo.setRevision(0);
        SaDemandEntity saDemandEntity = new SaDemandEntity();
        BeanUtils.copyProperties(saDemandPojo, saDemandEntity);
        //生成雪花id
        saDemandEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDemandEntity.setRevision(1);  //乐观锁
        this.saDemandMapper.insert(saDemandEntity);
        // 插入需求日志 (nanno创建了需求"XXX")
        SaDemandlogPojo saDemandlogPojo = new SaDemandlogPojo();
        saDemandlogPojo.setType("添加");
        saDemandlogPojo.setDemandid(saDemandEntity.getId());
        saDemandlogPojo.setContent(loginUser.getRealname() + "创建了需求: \n需求标题:" + saDemandEntity.getBilltitle() + "\n需求描述: " + saDemandEntity.getRemark());
        saDemandlogPojo.setOperator(loginUser.getRealname());
        saDemandlogPojo.setOperatorid(loginUser.getUserid());
        saDemandlogPojo.setCreateby(loginUser.getRealname());   // 创建者
        saDemandlogPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        saDemandlogPojo.setCreatedate(new Date());   // 创建时间
        saDemandlogPojo.setLister(loginUser.getRealname());   // 制表
        saDemandlogPojo.setListerid(loginUser.getUserid());    // 制表id
        saDemandlogPojo.setModifydate(new Date());   //修改时间
        saDemandlogService.insert(saDemandlogPojo);

        // 同步需求提报的DemandMark（已转需求单）
        String demandsubmitid = saDemandPojo.getDemandsubmitid();
        if (isNotBlank(demandsubmitid)) {
            saDemandMapper.syncSubmitDemandMark(demandsubmitid, 1);
        }

        return this.getEntity(saDemandEntity.getId());
    }

    /**
     * 修改数据
     *
     * @param intoDemandPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaDemandPojo update(SaDemandPojo intoDemandPojo, MyLoginUser myLoginUser) {
        // 数据库里的需求任务
        SaDemandPojo saDemandPojoDB = saDemandMapper.getEntity(intoDemandPojo.getId());
        String projnameDB = saDemandPojoDB.getProjname();
        // 传入的需求任务(一般只传id加上status/remark/billtitle中的一个)
        SaDemandEntity saDemandEntity = new SaDemandEntity();
        BeanUtils.copyProperties(intoDemandPojo, saDemandEntity);
        this.saDemandMapper.update(saDemandEntity);


        // 插入需求日志 (nanno创建了需求"XXX")
        SaDemandlogPojo saDemandlogPojo = new SaDemandlogPojo();
        saDemandlogPojo.setType("修改");
        saDemandlogPojo.setDemandid(saDemandEntity.getId());

        // 构建日志内容
        StringBuilder contentBuilder = new StringBuilder();
        String realName = myLoginUser.getRealname();
        if (isNotBlank(intoDemandPojo.getProjectid())) {
            contentBuilder.append(realName).append("修改了项目所属: ").append(saProjectMapper.getEntity(intoDemandPojo.getProjectid()).getProjname());
        } else if (isNotBlank(intoDemandPojo.getStatus())) {// 如果传入了status项目状态id,则新增一条需求日志: nanno修改了需求状态为"已完成"
            SaProjectstatusPojo saProjectstatusPojo = saProjectstatusMapper.getEntity(intoDemandPojo.getStatus());
            contentBuilder.append(realName).append("修改了【项目需求状态】为: ").append(saProjectstatusPojo.getStatusname());
            // 需求完成判断: StatusType为"已完成"且FinishMark为1
            int finishMark = "已完成".equals(saProjectstatusPojo.getStatustype()) && saProjectstatusPojo.getFinishmark() == 1 ? 1 : 0;
            this.saDemandMapper.updateStatusModifyDateAndFinishMark(saDemandEntity.getId(), new Date(), finishMark);
            // 发送钉钉消息 1、需求状态改变时，提醒创建者
            String formattedDate = new SimpleDateFormat("MM-dd HH:mm").format(new Date());
            String dingMsg = "【状态改变】\n项目名:" + projnameDB + "\n需求标题: " + saDemandPojoDB.getBilltitle() + "\n" + contentBuilder + " " + formattedDate;
            s06M23B1Controller.sendMsgTest(dingMsg, pmsSaUserMapper.getDingUserid(saDemandPojoDB.getCreatebyid()));
            // 发送钉钉消息 3、需求状态改为已完成并已结转状态时，提醒执行人
            if (finishMark == 1) {
                // 拖动完成需求时必须有指派人
                if (isBlank(saDemandPojoDB.getAppointeeid()))
                    throw new BaseBusinessException("拖动完成需求时必须有指派人Appointeeid");
                String dingMsg2 = "【完成】\n项目名:" + projnameDB + "\n需求标题: " + saDemandMapper.getTitle(saDemandEntity.getId()) + "\n" + contentBuilder + " " + formattedDate;
                //拿到执行者(工程师id)关联的userid,再去获取userid关联的钉钉userid
                s06M23B1Controller.sendMsgTest(dingMsg2, pmsSaUserMapper.getDingUserid(saEngineerMapper.getUserid(saDemandPojoDB.getAppointeeid())));
            }
        } else if (isNotBlank(intoDemandPojo.getDemandstatus())) {// 如果传入了Demandstatus需求状态id,则新增一条需求日志: nanno修改了需求状态为"已完成"
            SaDemandstatusPojo saDemandstatusPojo = saDemandstatusMapper.getEntity(intoDemandPojo.getDemandstatus());
            contentBuilder.append(realName).append("修改了【需求状态】为: ").append(saDemandstatusPojo.getStatusname());
            // 需求完成判断: // 状态属性(-1已拒绝,0代办,1进行中,2已完成)  等于2就表示已完成，去同步需求单的FinishMark=1
            int finishMark = saDemandstatusPojo.getStatusattr() == 2 ? 1 : 0;
            this.saDemandMapper.updateStatusModifyDateAndFinishMark(saDemandEntity.getId(), new Date(), finishMark);
            // 发送钉钉消息 1、需求状态改变时，提醒创建者
            String formattedDate = new SimpleDateFormat("MM-dd HH:mm").format(new Date());
            String dingMsg = "【状态改变】\n项目名:" + projnameDB + "\n需求标题: " + saDemandPojoDB.getBilltitle() + "\n" + contentBuilder + " " + formattedDate;
            s06M23B1Controller.sendMsgTest(dingMsg, pmsSaUserMapper.getDingUserid(saDemandPojoDB.getCreatebyid()));
            // 发送钉钉消息 3、需求状态改为已完成并已结转状态时，提醒执行人
            if (finishMark == 1) {
                // 拖动完成需求时必须有指派人
                if (isBlank(saDemandPojoDB.getAppointeeid()))
                    throw new BaseBusinessException("拖动完成需求时必须有指派人Appointeeid");
                String dingMsg2 = "【完成】\n项目名:" + projnameDB + "\n需求标题: " + saDemandMapper.getTitle(saDemandEntity.getId()) + "\n" + contentBuilder + " " + formattedDate;
                //拿到执行者(工程师id)关联的userid,再去获取userid关联的钉钉userid
                s06M23B1Controller.sendMsgTest(dingMsg2, pmsSaUserMapper.getDingUserid(saEngineerMapper.getUserid(saDemandPojoDB.getAppointeeid())));
            }
        } else if (intoDemandPojo.getBilltype() != null) {
            contentBuilder.append(realName).append("修改了需求类型: ").append(intoDemandPojo.getBilltype());
        } else if (intoDemandPojo.getRemark() != null) {
            contentBuilder.append(realName).append("修改了任务描述: ").append(intoDemandPojo.getRemark());
        } else if (intoDemandPojo.getBilltitle() != null) {
            contentBuilder.append(realName).append("修改了任务标题: ").append(intoDemandPojo.getBilltitle());
        } else if (isNotBlank(intoDemandPojo.getBilltitle()) && isNotBlank(intoDemandPojo.getRemark())) {
            contentBuilder.append(realName).append("修改了任务标题: ").append(intoDemandPojo.getBilltitle()).append("\n任务描述: ").append(intoDemandPojo.getRemark());
        } else if (intoDemandPojo.getStartdate() != null) {
            // new Date(0)（即 Thu Jan 01 08:00:00 CST 1970） 也就是前端传入的"1970-01-01 08:00:00"
            if (intoDemandPojo.getStartdate().equals(new Date(0))) {
                // 清空开始时间
                this.saDemandMapper.cleanStartdate(saDemandEntity.getId());
                contentBuilder.append(realName).append("清空了开始时间");
            } else {
                // 使用 SimpleDateFormat 类格式化时间
                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
                String formattedDate = sdf.format(intoDemandPojo.getStartdate());
                contentBuilder.append(realName).append("修改了开始时间: ").append(formattedDate);
            }
        } else if (intoDemandPojo.getDeaddate() != null) {//(需求创建者或工程师管理者)可以任意改截止日期，其他人只能在已有截止日期时前移
            //SaEngineerPojo engineer = myLoginUser.getEngineer();
            //if (engineer == null) {
            //    throw new BaseBusinessException("未关联工程师");
            //}
            //// 清空截止时间: new Date(0)（即 Thu Jan 01 08:00:00 CST 1970） 也就是前端传入的"1970-01-01 08:00:00"
            //if (intoDemandPojo.getDeaddate().equals(new Date(0))) {
            //    // 只能创建者或工程师管理者能清空截止时间
            //    if (!saDemandPojoDB.getCreatebyid().equals(myLoginUser.getUserid()) && !"管理者".equals(engineer.getEngineertype())) {
            //        throw new BaseBusinessException("非创建者或工程师管理者禁止清空截止时间");
            //    }
            //    // 清空截止时间
            //    this.saDemandMapper.cleanDeadDate(saDemandEntity.getId());
            //    contentBuilder.append(realName).append("清空了截止时间");
            //}// 修改截止时间
            //else {
            //    if (!saDemandPojoDB.getCreatebyid().equals(myLoginUser.getUserid()) && !"管理者".equals(engineer.getEngineertype())) {
            //        //传入的截止时间在原截止时间之前
            //        if (saDemandPojoDB.getDeaddate() == null || intoDemandPojo.getDeaddate().after(saDemandPojoDB.getDeaddate())) {
            //            throw new BaseBusinessException("非创建者或工程师管理者只能在已有截止日期时前移");
            //        }
            //    }
            //    // 使用 SimpleDateFormat 类格式化时间
            //    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm");
            //    String formattedDate = sdf.format(intoDemandPojo.getDeaddate());
            //    contentBuilder.append(realName).append("修改了截止时间: ").append(formattedDate);
            //}
        } else if (intoDemandPojo.getAppointeeid() != null) {
            if ("".equals(intoDemandPojo.getAppointeeid())) {//清空执行者
                contentBuilder.append(realName).append("清空了执行者");
            } else { //有指定的执行者(工程师)时:
                contentBuilder.append(realName).append("修改了执行者: ").append(saEngineerMapper.getEntity(intoDemandPojo.getAppointeeid()).getEngineername());
                // 发送钉钉消息 2、需求修改执行人时，提醒执行人
                String formattedDate = new SimpleDateFormat("MM-dd HH:mm").format(new Date());
                String dingMsg = "【执行人】\n项目名:" + projnameDB + "\n需求标题: " + saDemandPojoDB.getBilltitle() + "\n" + contentBuilder + " " + formattedDate;
                //拿到执行者(工程师id)关联的userid,再去获取userid关联的钉钉userid
                s06M23B1Controller.sendMsgTest(dingMsg, pmsSaUserMapper.getDingUserid(saEngineerMapper.getUserid(intoDemandPojo.getAppointeeid())));
            }
        } else if (intoDemandPojo.getLevel() != null) {
            if (intoDemandPojo.getLevel() == 0) {
                contentBuilder.append(realName).append("修改了优先级: 较低");
            } else if (intoDemandPojo.getLevel() == 1) {
                contentBuilder.append(realName).append("修改了优先级: 普通");
            } else if (intoDemandPojo.getLevel() == 2) {
                contentBuilder.append(realName).append("修改了优先级: 紧急");
            } else if (intoDemandPojo.getLevel() == 3) {
                contentBuilder.append(realName).append("修改了优先级: 非常紧急");
            }
        } else if (intoDemandPojo.getLabeljson() != null) {
            // 解析Labeljson字符串为 JSON 数组
            JSONArray labelArray = JSONArray.parseArray(intoDemandPojo.getLabeljson());
            StringBuilder labelBuilder = new StringBuilder();
            // 遍历JSON数组以获取"name"属性的值
            for (int i = 0; i < labelArray.size(); i++) {
                JSONObject labelObject = labelArray.getJSONObject(i);
                String labelName = labelObject.getString("name");
                // 拼接每个标签的"name"属性值到 labelBuilder
                labelBuilder.append(labelName).append(",");
            }
            // 去掉最后一个逗号
            if (labelBuilder.length() > 0) {
                labelBuilder.deleteCharAt(labelBuilder.length() - 1);
            }
            contentBuilder.append(realName).append("修改了标签: ").append(labelBuilder);
        } else if (intoDemandPojo.getWorktime() != null) {
            contentBuilder.append(realName).append("修改了工时: ").append(intoDemandPojo.getWorktime()).append("小时");
        } else if (isNotBlank(intoDemandPojo.getEngineergroupid())) {
            String name = saEngineergroupMapper.getName(intoDemandPojo.getEngineergroupid());
            contentBuilder.append(realName).append("修改了工程组: ").append(name);
        }
        String content = contentBuilder.toString();
        // 有内容的情况下才插入需求日志
        if (isNotBlank(content)) {
            saDemandlogPojo.setContent(content);
            saDemandlogPojo.setOperator(myLoginUser.getRealname());
            saDemandlogPojo.setOperatorid(myLoginUser.getUserid());
            saDemandlogPojo.setCreateby(myLoginUser.getRealname());   // 创建者
            saDemandlogPojo.setCreatebyid(myLoginUser.getUserid());  // 创建者id
            saDemandlogPojo.setCreatedate(new Date());   // 创建时间
            saDemandlogPojo.setLister(myLoginUser.getRealname());   // 制表
            saDemandlogPojo.setListerid(myLoginUser.getUserid());    // 制表id
            saDemandlogPojo.setModifydate(new Date());   //修改时间
            saDemandlogService.insert(saDemandlogPojo);
        }
        return this.getEntity(saDemandEntity.getId());
    }


    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, LoginUser loginUser) {
        SaDemandPojo saDemandPojo = saDemandMapper.getEntity(key);
        // 插入需求日志 (nanno创建了需求"XXX")
        SaDemandlogPojo saDemandlogPojo = new SaDemandlogPojo();
        saDemandlogPojo.setType("删除");
        saDemandlogPojo.setDemandid(saDemandPojo.getId());
        saDemandlogPojo.setContent(loginUser.getRealname() + "删除了需求:" + saDemandPojo.getBilltitle());
        saDemandlogPojo.setOperator(loginUser.getRealname());
        saDemandlogPojo.setOperatorid(loginUser.getUserid());
        saDemandlogPojo.setCreateby(loginUser.getRealname());   // 创建者
        saDemandlogPojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        saDemandlogPojo.setCreatedate(new Date());   // 创建时间
        saDemandlogPojo.setLister(loginUser.getRealname());   // 制表
        saDemandlogPojo.setListerid(loginUser.getUserid());    // 制表id
        saDemandlogPojo.setModifydate(new Date());   //修改时间
        saDemandlogService.insert(saDemandlogPojo);
        return this.saDemandMapper.delete(key);
    }

    @Override
    public boolean checkTitleExist(String commitTitle) {
        return saDemandMapper.checkTitleExist(commitTitle) > 0;
    }

    @Override
    public boolean checkTitleWithRemark(String subject, String remark) {
        return saDemandMapper.checkTitleWithRemark(subject, remark) > 0;
    }

    @Override
    public List<Map<String, Object>> getOnlineStatus() {
        return saDemandMapper.getOnlineStatus();
    }

    @Override
    public Integer getFinishMark(String id) {
        return saDemandMapper.getFinishMark(id);
    }


    @Override
    public List<Map<String, Integer>> getCountByBillType(String qpfilter) {
        return saDemandMapper.getCountByBillType(qpfilter);
    }
}
