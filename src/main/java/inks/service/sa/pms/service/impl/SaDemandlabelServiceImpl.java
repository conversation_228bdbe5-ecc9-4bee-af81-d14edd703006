package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaDemandlabelPojo;
import inks.service.sa.pms.domain.SaDemandlabelEntity;
import inks.service.sa.pms.mapper.SaDemandlabelMapper;
import inks.service.sa.pms.service.SaDemandlabelService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 需求标签(SaDemandlabel)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-26 13:02:27
 */
@Service("saDemandlabelService")
public class SaDemandlabelServiceImpl implements SaDemandlabelService {
    @Resource
    private SaDemandlabelMapper saDemandlabelMapper;

    @Override
    public SaDemandlabelPojo getEntity(String key) {
        return this.saDemandlabelMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDemandlabelPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandlabelPojo> lst = saDemandlabelMapper.getPageList(queryParam);
            PageInfo<SaDemandlabelPojo> pageInfo = new PageInfo<SaDemandlabelPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaDemandlabelPojo insert(SaDemandlabelPojo saDemandlabelPojo) {
        //初始化NULL字段
        cleanNull(saDemandlabelPojo);
        SaDemandlabelEntity saDemandlabelEntity = new SaDemandlabelEntity(); 
        BeanUtils.copyProperties(saDemandlabelPojo,saDemandlabelEntity);
        //生成雪花id
          saDemandlabelEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saDemandlabelEntity.setRevision(1);  //乐观锁
          this.saDemandlabelMapper.insert(saDemandlabelEntity);
        return this.getEntity(saDemandlabelEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saDemandlabelPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDemandlabelPojo update(SaDemandlabelPojo saDemandlabelPojo) {
        SaDemandlabelEntity saDemandlabelEntity = new SaDemandlabelEntity(); 
        BeanUtils.copyProperties(saDemandlabelPojo,saDemandlabelEntity);
        this.saDemandlabelMapper.update(saDemandlabelEntity);
        return this.getEntity(saDemandlabelEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDemandlabelMapper.delete(key) ;
    }
    

    private static void cleanNull(SaDemandlabelPojo saDemandlabelPojo) {
        if(saDemandlabelPojo.getLabelname()==null) saDemandlabelPojo.setLabelname("");
        if(saDemandlabelPojo.getLabelcolor()==null) saDemandlabelPojo.setLabelcolor("");
        if(saDemandlabelPojo.getRownum()==null) saDemandlabelPojo.setRownum(0);
        if(saDemandlabelPojo.getRemark()==null) saDemandlabelPojo.setRemark("");
        if(saDemandlabelPojo.getCreateby()==null) saDemandlabelPojo.setCreateby("");
        if(saDemandlabelPojo.getCreatebyid()==null) saDemandlabelPojo.setCreatebyid("");
        if(saDemandlabelPojo.getCreatedate()==null) saDemandlabelPojo.setCreatedate(new Date());
        if(saDemandlabelPojo.getLister()==null) saDemandlabelPojo.setLister("");
        if(saDemandlabelPojo.getListerid()==null) saDemandlabelPojo.setListerid("");
        if(saDemandlabelPojo.getModifydate()==null) saDemandlabelPojo.setModifydate(new Date());
        if(saDemandlabelPojo.getCustom1()==null) saDemandlabelPojo.setCustom1("");
        if(saDemandlabelPojo.getCustom2()==null) saDemandlabelPojo.setCustom2("");
        if(saDemandlabelPojo.getCustom3()==null) saDemandlabelPojo.setCustom3("");
        if(saDemandlabelPojo.getCustom4()==null) saDemandlabelPojo.setCustom4("");
        if(saDemandlabelPojo.getCustom5()==null) saDemandlabelPojo.setCustom5("");
        if(saDemandlabelPojo.getDeptid()==null) saDemandlabelPojo.setDeptid("");
        if(saDemandlabelPojo.getTenantid()==null) saDemandlabelPojo.setTenantid("");
        if(saDemandlabelPojo.getTenantname()==null) saDemandlabelPojo.setTenantname("");
        if(saDemandlabelPojo.getRevision()==null) saDemandlabelPojo.setRevision(0);
   }

}
