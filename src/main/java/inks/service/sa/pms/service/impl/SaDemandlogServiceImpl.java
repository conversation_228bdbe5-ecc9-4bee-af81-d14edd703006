package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDemandlogEntity;
import inks.service.sa.pms.domain.pojo.SaDemandlogPojo;
import inks.service.sa.pms.mapper.SaDemandlogMapper;
import inks.service.sa.pms.service.SaDemandlogService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 产品需求日志(SaDemandlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-26 15:00:38
 */
@Service("saDemandlogService")
public class SaDemandlogServiceImpl implements SaDemandlogService {
    @Resource
    private SaDemandlogMapper saDemandlogMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaDemandlogPojo getEntity(String key) {
        return this.saDemandlogMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaDemandlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandlogPojo> lst = saDemandlogMapper.getPageList(queryParam);
            PageInfo<SaDemandlogPojo> pageInfo = new PageInfo<SaDemandlogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDemandlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDemandlogPojo insert(SaDemandlogPojo saDemandlogPojo) {
        //初始化NULL字段
        if (saDemandlogPojo.getDemandid() == null) saDemandlogPojo.setDemandid("");
        if (saDemandlogPojo.getType() == null) saDemandlogPojo.setType("");
        if (saDemandlogPojo.getContent() == null) saDemandlogPojo.setContent("");
        if (saDemandlogPojo.getAttachment() == null) saDemandlogPojo.setAttachment("{}");
        if (saDemandlogPojo.getOperator() == null) saDemandlogPojo.setOperator("");
        if (saDemandlogPojo.getOperatorid() == null) saDemandlogPojo.setOperatorid("");
        if (saDemandlogPojo.getRemark() == null) saDemandlogPojo.setRemark("");
        if (saDemandlogPojo.getCreateby() == null) saDemandlogPojo.setCreateby("");
        if (saDemandlogPojo.getCreatebyid() == null) saDemandlogPojo.setCreatebyid("");
        if (saDemandlogPojo.getCreatedate() == null) saDemandlogPojo.setCreatedate(new Date());
        if (saDemandlogPojo.getLister() == null) saDemandlogPojo.setLister("");
        if (saDemandlogPojo.getListerid() == null) saDemandlogPojo.setListerid("");
        if (saDemandlogPojo.getModifydate() == null) saDemandlogPojo.setModifydate(new Date());
        if (saDemandlogPojo.getCustom1() == null) saDemandlogPojo.setCustom1("");
        if (saDemandlogPojo.getCustom2() == null) saDemandlogPojo.setCustom2("");
        if (saDemandlogPojo.getCustom3() == null) saDemandlogPojo.setCustom3("");
        if (saDemandlogPojo.getCustom4() == null) saDemandlogPojo.setCustom4("");
        if (saDemandlogPojo.getCustom5() == null) saDemandlogPojo.setCustom5("");
        if (saDemandlogPojo.getDeptid() == null) saDemandlogPojo.setDeptid("");
        if (saDemandlogPojo.getTenantid() == null) saDemandlogPojo.setTenantid("");
        if (saDemandlogPojo.getTenantname() == null) saDemandlogPojo.setTenantname("");
        if (saDemandlogPojo.getRevision() == null) saDemandlogPojo.setRevision(0);
        SaDemandlogEntity saDemandlogEntity = new SaDemandlogEntity();
        BeanUtils.copyProperties(saDemandlogPojo, saDemandlogEntity);
        //生成雪花id
        saDemandlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDemandlogEntity.setRevision(1);  //乐观锁
        this.saDemandlogMapper.insert(saDemandlogEntity);
        return this.getEntity(saDemandlogEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDemandlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDemandlogPojo update(SaDemandlogPojo saDemandlogPojo) {
        SaDemandlogEntity saDemandlogEntity = new SaDemandlogEntity();
        BeanUtils.copyProperties(saDemandlogPojo, saDemandlogEntity);
        this.saDemandlogMapper.update(saDemandlogEntity);
        return this.getEntity(saDemandlogEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saDemandlogMapper.delete(key);
    }


}
