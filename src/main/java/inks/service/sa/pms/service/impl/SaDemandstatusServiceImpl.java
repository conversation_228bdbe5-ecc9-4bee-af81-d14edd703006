package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaDemandstatusPojo;
import inks.service.sa.pms.domain.SaDemandstatusEntity;
import inks.service.sa.pms.mapper.SaDemandstatusMapper;
import inks.service.sa.pms.service.SaDemandstatusService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 需求状态(SaDemandstatus)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-24 15:21:21
 */
@Service("saDemandstatusService")
public class SaDemandstatusServiceImpl implements SaDemandstatusService {
    @Resource
    private SaDemandstatusMapper saDemandstatusMapper;

    @Override
    public SaDemandstatusPojo getEntity(String key) {
        return this.saDemandstatusMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDemandstatusPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandstatusPojo> lst = saDemandstatusMapper.getPageList(queryParam);
            PageInfo<SaDemandstatusPojo> pageInfo = new PageInfo<SaDemandstatusPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaDemandstatusPojo insert(SaDemandstatusPojo saDemandstatusPojo) {
        //初始化NULL字段
        cleanNull(saDemandstatusPojo);
        SaDemandstatusEntity saDemandstatusEntity = new SaDemandstatusEntity(); 
        BeanUtils.copyProperties(saDemandstatusPojo,saDemandstatusEntity);
        //生成雪花id
          saDemandstatusEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saDemandstatusEntity.setRevision(1);  //乐观锁
          this.saDemandstatusMapper.insert(saDemandstatusEntity);
        return this.getEntity(saDemandstatusEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saDemandstatusPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDemandstatusPojo update(SaDemandstatusPojo saDemandstatusPojo) {
        SaDemandstatusEntity saDemandstatusEntity = new SaDemandstatusEntity(); 
        BeanUtils.copyProperties(saDemandstatusPojo,saDemandstatusEntity);
        this.saDemandstatusMapper.update(saDemandstatusEntity);
        return this.getEntity(saDemandstatusEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDemandstatusMapper.delete(key) ;
    }
    

    private static void cleanNull(SaDemandstatusPojo saDemandstatusPojo) {
        if(saDemandstatusPojo.getStatusname()==null) saDemandstatusPojo.setStatusname("");
        if(saDemandstatusPojo.getStatusattr()==null) saDemandstatusPojo.setStatusattr(0);
        if(saDemandstatusPojo.getStatuscolor()==null) saDemandstatusPojo.setStatuscolor("");
        if(saDemandstatusPojo.getRownum()==null) saDemandstatusPojo.setRownum(0);
        if(saDemandstatusPojo.getRemark()==null) saDemandstatusPojo.setRemark("");
        if(saDemandstatusPojo.getCreateby()==null) saDemandstatusPojo.setCreateby("");
        if(saDemandstatusPojo.getCreatebyid()==null) saDemandstatusPojo.setCreatebyid("");
        if(saDemandstatusPojo.getCreatedate()==null) saDemandstatusPojo.setCreatedate(new Date());
        if(saDemandstatusPojo.getLister()==null) saDemandstatusPojo.setLister("");
        if(saDemandstatusPojo.getListerid()==null) saDemandstatusPojo.setListerid("");
        if(saDemandstatusPojo.getModifydate()==null) saDemandstatusPojo.setModifydate(new Date());
        if(saDemandstatusPojo.getCustom1()==null) saDemandstatusPojo.setCustom1("");
        if(saDemandstatusPojo.getCustom2()==null) saDemandstatusPojo.setCustom2("");
        if(saDemandstatusPojo.getCustom3()==null) saDemandstatusPojo.setCustom3("");
        if(saDemandstatusPojo.getCustom4()==null) saDemandstatusPojo.setCustom4("");
        if(saDemandstatusPojo.getCustom5()==null) saDemandstatusPojo.setCustom5("");
        if(saDemandstatusPojo.getDeptid()==null) saDemandstatusPojo.setDeptid("");
        if(saDemandstatusPojo.getTenantid()==null) saDemandstatusPojo.setTenantid("");
        if(saDemandstatusPojo.getTenantname()==null) saDemandstatusPojo.setTenantname("");
        if(saDemandstatusPojo.getRevision()==null) saDemandstatusPojo.setRevision(0);
   }

}
