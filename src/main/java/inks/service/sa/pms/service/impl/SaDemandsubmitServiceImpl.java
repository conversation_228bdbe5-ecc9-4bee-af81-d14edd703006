package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDemandsubmitEntity;
import inks.service.sa.pms.domain.pojo.SaDemandsubmitPojo;
import inks.service.sa.pms.mapper.SaDemandsubmitMapper;
import inks.service.sa.pms.service.SaDemandsubmitService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 需求提报(SaDemandsubmit)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-25 13:27:24
 */
@Service("saDemandsubmitService")
public class SaDemandsubmitServiceImpl implements SaDemandsubmitService {
    @Resource
    private SaDemandsubmitMapper saDemandsubmitMapper;

    @Override
    public SaDemandsubmitPojo getEntity(String key) {
        return this.saDemandsubmitMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDemandsubmitPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandsubmitPojo> lst = saDemandsubmitMapper.getPageList(queryParam);
            PageInfo<SaDemandsubmitPojo> pageInfo = new PageInfo<SaDemandsubmitPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaDemandsubmitPojo insert(SaDemandsubmitPojo saDemandsubmitPojo) {
        //初始化NULL字段
        cleanNull(saDemandsubmitPojo);
        SaDemandsubmitEntity saDemandsubmitEntity = new SaDemandsubmitEntity();
        BeanUtils.copyProperties(saDemandsubmitPojo, saDemandsubmitEntity);
        //生成雪花id
        saDemandsubmitEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDemandsubmitEntity.setRevision(1);  //乐观锁
        this.saDemandsubmitMapper.insert(saDemandsubmitEntity);

        // 同步反馈单的SubmitMark （已转需求提报）
        String feedbackitemid = saDemandsubmitPojo.getFeedbackitemid();
        String type = saDemandsubmitPojo.getType();//反馈单、游客反馈单
        if (StringUtils.isNotBlank(feedbackitemid)) {
            switch (type) {
                case "反馈单":
                    saDemandsubmitMapper.syncFeedbackItemSubmitMark(feedbackitemid, 1);
                    saDemandsubmitMapper.syncFeedbackFinishCount(feedbackitemid);
                    break;
                case "游客反馈单":
                    saDemandsubmitMapper.syncFeedbackVisItemSubmitMark(feedbackitemid, 1);
                    break;
                default:
                    break;
            }
        }

        return this.getEntity(saDemandsubmitEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDemandsubmitPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDemandsubmitPojo update(SaDemandsubmitPojo saDemandsubmitPojo) {
        SaDemandsubmitEntity saDemandsubmitEntity = new SaDemandsubmitEntity();
        BeanUtils.copyProperties(saDemandsubmitPojo, saDemandsubmitEntity);
        this.saDemandsubmitMapper.update(saDemandsubmitEntity);
        return this.getEntity(saDemandsubmitEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDemandsubmitMapper.delete(key);
    }

    @Override
    @Transactional
    public SaDemandsubmitPojo approval(SaDemandsubmitPojo saDemandsubmitPojo) {
        //主表更改
        SaDemandsubmitEntity saDemandsubmitEntity = new SaDemandsubmitEntity();
        BeanUtils.copyProperties(saDemandsubmitPojo, saDemandsubmitEntity);
        this.saDemandsubmitMapper.approval(saDemandsubmitEntity);
        //返回Bill实例
        return this.getEntity(saDemandsubmitEntity.getId());
    }

    private static void cleanNull(SaDemandsubmitPojo saDemandsubmitPojo) {
        if (saDemandsubmitPojo.getType() == null) saDemandsubmitPojo.setType("");
        if (saDemandsubmitPojo.getDescription() == null) saDemandsubmitPojo.setDescription("");
        if (saDemandsubmitPojo.getLevel() == null) saDemandsubmitPojo.setLevel(0);
        if (saDemandsubmitPojo.getStatus() == null) saDemandsubmitPojo.setStatus("");
        if (saDemandsubmitPojo.getAssignee() == null) saDemandsubmitPojo.setAssignee("");
        if (saDemandsubmitPojo.getParticipants() == null) saDemandsubmitPojo.setParticipants("");
        if (saDemandsubmitPojo.getDeadlinedate() == null) saDemandsubmitPojo.setDeadlinedate(new Date());
        if (saDemandsubmitPojo.getGroupid() == null) saDemandsubmitPojo.setGroupid("");
        if (saDemandsubmitPojo.getSource() == null) saDemandsubmitPojo.setSource("");
        if (saDemandsubmitPojo.getStatetext() == null) saDemandsubmitPojo.setStatetext("");
        if (saDemandsubmitPojo.getStatedate() == null) saDemandsubmitPojo.setStatedate(new Date());
        if (saDemandsubmitPojo.getRownum() == null) saDemandsubmitPojo.setRownum(0);
        if (saDemandsubmitPojo.getRemark() == null) saDemandsubmitPojo.setRemark("");
        if (saDemandsubmitPojo.getOaflowmark() == null) saDemandsubmitPojo.setOaflowmark(0);
        if (saDemandsubmitPojo.getFeedbackitemid() == null) saDemandsubmitPojo.setFeedbackitemid("");
        if (saDemandsubmitPojo.getDemandmark() == null) saDemandsubmitPojo.setDemandmark(0);
        if (saDemandsubmitPojo.getTestcasemark() == null) saDemandsubmitPojo.setTestcasemark(0);
        if (saDemandsubmitPojo.getReleasemark() == null) saDemandsubmitPojo.setReleasemark(0);
        if (saDemandsubmitPojo.getAssessor() == null) saDemandsubmitPojo.setAssessor("");
        if (saDemandsubmitPojo.getAssessorid() == null) saDemandsubmitPojo.setAssessorid("");
        if (saDemandsubmitPojo.getAssessdate() == null) saDemandsubmitPojo.setAssessdate(new Date());
        if (saDemandsubmitPojo.getCreateby() == null) saDemandsubmitPojo.setCreateby("");
        if (saDemandsubmitPojo.getCreatebyid() == null) saDemandsubmitPojo.setCreatebyid("");
        if (saDemandsubmitPojo.getCreatedate() == null) saDemandsubmitPojo.setCreatedate(new Date());
        if (saDemandsubmitPojo.getLister() == null) saDemandsubmitPojo.setLister("");
        if (saDemandsubmitPojo.getListerid() == null) saDemandsubmitPojo.setListerid("");
        if (saDemandsubmitPojo.getModifydate() == null) saDemandsubmitPojo.setModifydate(new Date());
        if (saDemandsubmitPojo.getCustom1() == null) saDemandsubmitPojo.setCustom1("");
        if (saDemandsubmitPojo.getCustom2() == null) saDemandsubmitPojo.setCustom2("");
        if (saDemandsubmitPojo.getCustom3() == null) saDemandsubmitPojo.setCustom3("");
        if (saDemandsubmitPojo.getCustom4() == null) saDemandsubmitPojo.setCustom4("");
        if (saDemandsubmitPojo.getCustom5() == null) saDemandsubmitPojo.setCustom5("");
        if (saDemandsubmitPojo.getTenantid() == null) saDemandsubmitPojo.setTenantid("");
        if (saDemandsubmitPojo.getTenantname() == null) saDemandsubmitPojo.setTenantname("");
        if (saDemandsubmitPojo.getRevision() == null) saDemandsubmitPojo.setRevision(0);
    }

}
