package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDemandtimeEntity;
import inks.service.sa.pms.domain.pojo.SaDemandtimePojo;
import inks.service.sa.pms.mapper.SaDemandMapper;
import inks.service.sa.pms.mapper.SaDemandtimeMapper;
import inks.service.sa.pms.service.SaDemandtimeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Date;
import java.util.List;

/**
 * 需求工时记录(SaDemandtime)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-02 11:09:23
 */
@Service("saDemandtimeService")
public class SaDemandtimeServiceImpl implements SaDemandtimeService {
    @Resource
    private SaDemandtimeMapper saDemandtimeMapper;
    @Resource
    private SaDemandMapper saDemandMapper;

    @Override
    public SaDemandtimePojo getEntity(String key) {
        return this.saDemandtimeMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaDemandtimePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDemandtimePojo> lst = saDemandtimeMapper.getPageList(queryParam);
            PageInfo<SaDemandtimePojo> pageInfo = new PageInfo<SaDemandtimePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaDemandtimePojo insert(SaDemandtimePojo saDemandtimePojo) {
        //初始化NULL字段
        cleanNull(saDemandtimePojo);
        SaDemandtimeEntity saDemandtimeEntity = new SaDemandtimeEntity();
        BeanUtils.copyProperties(saDemandtimePojo, saDemandtimeEntity);
        //生成雪花id
        saDemandtimeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDemandtimeEntity.setRevision(1);  //乐观锁
        this.saDemandtimeMapper.insert(saDemandtimeEntity);
        return this.getEntity(saDemandtimeEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDemandtimePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDemandtimePojo update(SaDemandtimePojo saDemandtimePojo) {
        SaDemandtimeEntity saDemandtimeEntity = new SaDemandtimeEntity();
        BeanUtils.copyProperties(saDemandtimePojo, saDemandtimeEntity);
        this.saDemandtimeMapper.update(saDemandtimeEntity);
        return this.getEntity(saDemandtimeEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saDemandtimeMapper.delete(key);
    }


    private static void cleanNull(SaDemandtimePojo saDemandtimePojo) {
        if (saDemandtimePojo.getDemandid() == null) saDemandtimePojo.setDemandid("");
        if (saDemandtimePojo.getStatus() == null) saDemandtimePojo.setStatus("");
        if (saDemandtimePojo.getStarttime() == null) saDemandtimePojo.setStarttime(new Date());
        //Endtime 可以null
        //if (saDemandtimePojo.getEndtime() == null) saDemandtimePojo.setEndtime(new Date());
        if (saDemandtimePojo.getWorkhours() == null) saDemandtimePojo.setWorkhours(0D);
        if (saDemandtimePojo.getStartcount() == null) saDemandtimePojo.setStartcount(0);
        if (saDemandtimePojo.getRownum() == null) saDemandtimePojo.setRownum(0);
        if (saDemandtimePojo.getRemark() == null) saDemandtimePojo.setRemark("");
        if (saDemandtimePojo.getCreateby() == null) saDemandtimePojo.setCreateby("");
        if (saDemandtimePojo.getCreatebyid() == null) saDemandtimePojo.setCreatebyid("");
        if (saDemandtimePojo.getCreatedate() == null) saDemandtimePojo.setCreatedate(new Date());
        if (saDemandtimePojo.getLister() == null) saDemandtimePojo.setLister("");
        if (saDemandtimePojo.getListerid() == null) saDemandtimePojo.setListerid("");
        if (saDemandtimePojo.getModifydate() == null) saDemandtimePojo.setModifydate(new Date());
        if (saDemandtimePojo.getCustom1() == null) saDemandtimePojo.setCustom1("");
        if (saDemandtimePojo.getCustom2() == null) saDemandtimePojo.setCustom2("");
        if (saDemandtimePojo.getCustom3() == null) saDemandtimePojo.setCustom3("");
        if (saDemandtimePojo.getCustom4() == null) saDemandtimePojo.setCustom4("");
        if (saDemandtimePojo.getCustom5() == null) saDemandtimePojo.setCustom5("");
        if (saDemandtimePojo.getDeptid() == null) saDemandtimePojo.setDeptid("");
        if (saDemandtimePojo.getTenantid() == null) saDemandtimePojo.setTenantid("");
        if (saDemandtimePojo.getTenantname() == null) saDemandtimePojo.setTenantname("");
        if (saDemandtimePojo.getRevision() == null) saDemandtimePojo.setRevision(0);
    }

    //开始需求工时记录 传入需求id
    @Override
    @Transactional
    public String start(String demandid, LoginUser loginUser) {
        // 检查该需求是否已完成了
        int i = saDemandtimeMapper.getCountStatus(demandid, "完成");
        if (i > 0) {
            throw new BaseBusinessException("需求已完成！");
        }
        // 检查该需求是否存在开始状态的了，如果存在则不允许再次开始
        int j = saDemandtimeMapper.getCountStatus(demandid, "开始");
        if (j > 0) {
            throw new BaseBusinessException("需求已开始！");
        }
        Date now = new Date();
        SaDemandtimePojo saDemandtimePojo = new SaDemandtimePojo();
        saDemandtimePojo.setCreateby(loginUser.getRealName());   // 创建者
        saDemandtimePojo.setCreatebyid(loginUser.getUserid());  // 创建者id
        saDemandtimePojo.setCreatedate(now);   // 创建时间
        saDemandtimePojo.setLister(loginUser.getRealname());   // 制表
        saDemandtimePojo.setListerid(loginUser.getUserid());    // 制表id
        saDemandtimePojo.setModifydate(now);   //修改时间

        saDemandtimePojo.setDemandid(demandid);
        saDemandtimePojo.setStatus("开始");
        saDemandtimePojo.setStarttime(now);
        int count = saDemandtimeMapper.getCountByDemandid(demandid);
        saDemandtimePojo.setStartcount(count + 1);//第几次开始
        //插入
        this.insert(saDemandtimePojo);
        // 同步需求单的工时、状态TimeStatus
        saDemandMapper.syncWorkTimeByDemandTime(demandid, "开始");
        return "success";
    }

    @Override
    @Transactional
    public String pause(String demandid, LoginUser loginUser) {
        Date now = new Date();
        //获取最新的需求工时记录
        SaDemandtimePojo demandTimeDB = saDemandtimeMapper.getLatestEntityByDemandid(demandid);
        if (demandTimeDB == null) {
            throw new BaseBusinessException("还未点击开始！");
        }
        if (demandTimeDB.getEndtime() != null) {
            throw new BaseBusinessException("已处于" + demandTimeDB.getStatus() + "状态");
        }
        demandTimeDB.setEndtime(now);
        demandTimeDB.setStatus("暂停");
        demandTimeDB.setModifydate(now);
        // 工作小时数，保留两位小数
        demandTimeDB.setWorkhours(this.calculateWorkhours(demandTimeDB.getStarttime(), now));
        this.update(demandTimeDB);
        // 同步需求单的工时、状态TimeStatus
        saDemandMapper.syncWorkTimeByDemandTime(demandid, "暂停");
        return "success";
    }

    @Override
    @Transactional
    public String complete(String demandid, LoginUser loginUser) {
        Date now = new Date();
        //获取最新的需求工时记录
        SaDemandtimePojo demandTimeDB = saDemandtimeMapper.getLatestEntityByDemandid(demandid);
        if (demandTimeDB == null) {
            throw new BaseBusinessException("还未点击开始！");
        }
        if (demandTimeDB.getEndtime() != null) {
            throw new BaseBusinessException("已处于" + demandTimeDB.getStatus() + "状态");
        }
        demandTimeDB.setEndtime(now);
        demandTimeDB.setStatus("完成");
        demandTimeDB.setModifydate(now);
        // 工作小时数，保留两位小数
        demandTimeDB.setWorkhours(this.calculateWorkhours(demandTimeDB.getStarttime(), now));
        this.update(demandTimeDB);
        // 同步需求单的工时、状态TimeStatus
        saDemandMapper.syncWorkTimeByDemandTime(demandid, "完成");
        return "success";
    }

    /**
     * 计算两个时间点之间的工作小时数，保留两位小数
     *
     * @param startTime 开始时间
     * @param endTime   结束时间
     * @return 工作小时数，保留两位小数
     */
    private double calculateWorkhours(Date startTime, Date endTime) {
        long startTimeMillis = startTime.getTime();
        long endTimeMillis = endTime.getTime();
        double workhours = (endTimeMillis - startTimeMillis) / (1000.0 * 60 * 60); // 转换为小时

        // 保留两位小数
        BigDecimal workhoursBD = new BigDecimal(workhours).setScale(2, RoundingMode.HALF_UP);
        return workhoursBD.doubleValue();
    }
}
