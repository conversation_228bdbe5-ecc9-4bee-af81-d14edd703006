package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDevelopmentplanEntity;
import inks.service.sa.pms.domain.pojo.SaDevelopmentplanPojo;
import inks.service.sa.pms.mapper.SaDevelopmentplanMapper;
import inks.service.sa.pms.service.SaDevelopmentplanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 开发活动计划(SaDevelopmentplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-13 13:22:02
 */
@Service("saDevelopmentplanService")
public class SaDevelopmentplanServiceImpl implements SaDevelopmentplanService {
    @Resource
    private SaDevelopmentplanMapper saDevelopmentplanMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaDevelopmentplanPojo getEntity(String key) {
        return this.saDevelopmentplanMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaDevelopmentplanPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDevelopmentplanPojo> lst = saDevelopmentplanMapper.getPageList(queryParam);
            PageInfo<SaDevelopmentplanPojo> pageInfo = new PageInfo<SaDevelopmentplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDevelopmentplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDevelopmentplanPojo insert(SaDevelopmentplanPojo saDevelopmentplanPojo) {
        //初始化NULL字段
        if (saDevelopmentplanPojo.getName() == null) saDevelopmentplanPojo.setName("");
        if (saDevelopmentplanPojo.getPrincipalid() == null) saDevelopmentplanPojo.setPrincipalid("");
        if (saDevelopmentplanPojo.getPrincipal() == null) saDevelopmentplanPojo.setPrincipal("");
        if (saDevelopmentplanPojo.getCompletionrate() == null) saDevelopmentplanPojo.setCompletionrate(0);
        if (saDevelopmentplanPojo.getDeaddate() == null) saDevelopmentplanPojo.setDeaddate(new Date());
        if (saDevelopmentplanPojo.getClosed() == null) saDevelopmentplanPojo.setClosed(0);
        if (saDevelopmentplanPojo.getRownum() == null) saDevelopmentplanPojo.setRownum(0);
        if (saDevelopmentplanPojo.getCreatebyid() == null) saDevelopmentplanPojo.setCreatebyid("");
        if (saDevelopmentplanPojo.getCreateby() == null) saDevelopmentplanPojo.setCreateby("");
        if (saDevelopmentplanPojo.getCreatedate() == null) saDevelopmentplanPojo.setCreatedate(new Date());
        if (saDevelopmentplanPojo.getListerid() == null) saDevelopmentplanPojo.setListerid("");
        if (saDevelopmentplanPojo.getLister() == null) saDevelopmentplanPojo.setLister("");
        if (saDevelopmentplanPojo.getModifydate() == null) saDevelopmentplanPojo.setModifydate(new Date());
        if (saDevelopmentplanPojo.getCustom1() == null) saDevelopmentplanPojo.setCustom1("");
        if (saDevelopmentplanPojo.getCustom2() == null) saDevelopmentplanPojo.setCustom2("");
        if (saDevelopmentplanPojo.getCustom3() == null) saDevelopmentplanPojo.setCustom3("");
        if (saDevelopmentplanPojo.getCustom4() == null) saDevelopmentplanPojo.setCustom4("");
        if (saDevelopmentplanPojo.getCustom5() == null) saDevelopmentplanPojo.setCustom5("");
        if (saDevelopmentplanPojo.getTenantid() == null) saDevelopmentplanPojo.setTenantid("");
        if (saDevelopmentplanPojo.getRevision() == null) saDevelopmentplanPojo.setRevision(0);
        SaDevelopmentplanEntity saDevelopmentplanEntity = new SaDevelopmentplanEntity();
        BeanUtils.copyProperties(saDevelopmentplanPojo, saDevelopmentplanEntity);
        //生成雪花id
        saDevelopmentplanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDevelopmentplanEntity.setRevision(1);  //乐观锁
        this.saDevelopmentplanMapper.insert(saDevelopmentplanEntity);
        return this.getEntity(saDevelopmentplanEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDevelopmentplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDevelopmentplanPojo update(SaDevelopmentplanPojo saDevelopmentplanPojo) {
        SaDevelopmentplanEntity saDevelopmentplanEntity = new SaDevelopmentplanEntity();
        BeanUtils.copyProperties(saDevelopmentplanPojo, saDevelopmentplanEntity);
        this.saDevelopmentplanMapper.update(saDevelopmentplanEntity);
        return this.getEntity(saDevelopmentplanEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saDevelopmentplanMapper.delete(key);
    }


}
