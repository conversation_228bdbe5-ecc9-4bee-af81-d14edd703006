package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDocEntity;
import inks.service.sa.pms.domain.pojo.SaDocPojo;
import inks.service.sa.pms.mapper.SaDocMapper;
import inks.service.sa.pms.service.SaDocService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 文档表(SaDoc)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
@Service("saDocService")
public class SaDocServiceImpl implements SaDocService {
    @Resource
    private SaDocMapper saDocMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaDocPojo getEntity(String key) {
        return this.saDocMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaDocPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDocPojo> lst = saDocMapper.getPageList(queryParam);
            PageInfo<SaDocPojo> pageInfo = new PageInfo<SaDocPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDocPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDocPojo insert(SaDocPojo saDocPojo) {
        //初始化NULL字段
        if (saDocPojo.getParentid() == null) saDocPojo.setParentid("");
        if (saDocPojo.getName() == null) saDocPojo.setName("");
        if (saDocPojo.getType() == null) saDocPojo.setType(0);
        if (saDocPojo.getChecksum() == null) saDocPojo.setChecksum("");
        if (saDocPojo.getVersion() == null) saDocPojo.setVersion("");
        if (saDocPojo.getContent() == null) saDocPojo.setContent("");
        if (saDocPojo.getPath() == null) saDocPojo.setPath("");
        if (saDocPojo.getVid() == null) saDocPojo.setVid("");
        if (saDocPojo.getPwd() == null) saDocPojo.setPwd("");
        if (saDocPojo.getRownum() == null) saDocPojo.setRownum(0);
        if (saDocPojo.getRemark() == null) saDocPojo.setRemark("");
        if (saDocPojo.getCreateby() == null) saDocPojo.setCreateby("");
        if (saDocPojo.getCreatebyid() == null) saDocPojo.setCreatebyid("");
        if (saDocPojo.getCreatedate() == null) saDocPojo.setCreatedate(new Date());
        if (saDocPojo.getLister() == null) saDocPojo.setLister("");
        if (saDocPojo.getListerid() == null) saDocPojo.setListerid("");
        if (saDocPojo.getModifydate() == null) saDocPojo.setModifydate(new Date());
        if (saDocPojo.getCustom1() == null) saDocPojo.setCustom1("");
        if (saDocPojo.getCustom2() == null) saDocPojo.setCustom2("");
        if (saDocPojo.getCustom3() == null) saDocPojo.setCustom3("");
        if (saDocPojo.getCustom4() == null) saDocPojo.setCustom4("");
        if (saDocPojo.getCustom5() == null) saDocPojo.setCustom5("");
        if (saDocPojo.getDeptid() == null) saDocPojo.setDeptid("");
        if (saDocPojo.getTenantid() == null) saDocPojo.setTenantid("");
        if (saDocPojo.getRevision() == null) saDocPojo.setRevision(0);
        SaDocEntity saDocEntity = new SaDocEntity();
        BeanUtils.copyProperties(saDocPojo, saDocEntity);
        //生成雪花id
        saDocEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDocEntity.setRevision(1);  //乐观锁
        this.saDocMapper.insert(saDocEntity);
        return this.getEntity(saDocEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDocPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDocPojo update(SaDocPojo saDocPojo) {
        SaDocEntity saDocEntity = new SaDocEntity();
        BeanUtils.copyProperties(saDocPojo, saDocEntity);
        this.saDocMapper.update(saDocEntity);
        return this.getEntity(saDocEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saDocMapper.delete(key);
    }


}
