package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDocreposEntity;
import inks.service.sa.pms.domain.pojo.SaDocreposPojo;
import inks.service.sa.pms.mapper.SaDocreposMapper;
import inks.service.sa.pms.service.SaDocreposService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 仓库表(SaDocrepos)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
@Service("saDocreposService")
public class SaDocreposServiceImpl implements SaDocreposService {
    @Resource
    private SaDocreposMapper saDocreposMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaDocreposPojo getEntity(String key) {
        return this.saDocreposMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaDocreposPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDocreposPojo> lst = saDocreposMapper.getPageList(queryParam);
            PageInfo<SaDocreposPojo> pageInfo = new PageInfo<SaDocreposPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDocreposPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDocreposPojo insert(SaDocreposPojo saDocreposPojo) {
        //初始化NULL字段
        if (saDocreposPojo.getName() == null) saDocreposPojo.setName("");
        if (saDocreposPojo.getType() == null) saDocreposPojo.setType(0);
        if (saDocreposPojo.getPath() == null) saDocreposPojo.setPath("");
        if (saDocreposPojo.getRealdocpath() == null) saDocreposPojo.setRealdocpath("");
        if (saDocreposPojo.getVerctrl() == null) saDocreposPojo.setVerctrl(0);
        if (saDocreposPojo.getIsremote() == null) saDocreposPojo.setIsremote(0);
        if (saDocreposPojo.getLocalminiopath() == null) saDocreposPojo.setLocalminiopath("");
        if (saDocreposPojo.getMiniopath() == null) saDocreposPojo.setMiniopath("");
        if (saDocreposPojo.getMiniouser() == null) saDocreposPojo.setMiniouser("");
        if (saDocreposPojo.getMiniopwd() == null) saDocreposPojo.setMiniopwd("");
        if (saDocreposPojo.getVersion() == null) saDocreposPojo.setVersion("");
        if (saDocreposPojo.getVerctrl1() == null) saDocreposPojo.setVerctrl1(0);
        if (saDocreposPojo.getInfo() == null) saDocreposPojo.setInfo("");
        if (saDocreposPojo.getPwd() == null) saDocreposPojo.setPwd("");
        if (saDocreposPojo.getOwner() == null) saDocreposPojo.setOwner(0);
        if (saDocreposPojo.getState() == null) saDocreposPojo.setState(0);
        if (saDocreposPojo.getLockby() == null) saDocreposPojo.setLockby("");
        if (saDocreposPojo.getLocktime() == null) saDocreposPojo.setLocktime(new Date());
        if (saDocreposPojo.getRownum() == null) saDocreposPojo.setRownum(0);
        if (saDocreposPojo.getRemark() == null) saDocreposPojo.setRemark("");
        if (saDocreposPojo.getCreateby() == null) saDocreposPojo.setCreateby("");
        if (saDocreposPojo.getCreatebyid() == null) saDocreposPojo.setCreatebyid("");
        if (saDocreposPojo.getCreatedate() == null) saDocreposPojo.setCreatedate(new Date());
        if (saDocreposPojo.getLister() == null) saDocreposPojo.setLister("");
        if (saDocreposPojo.getListerid() == null) saDocreposPojo.setListerid("");
        if (saDocreposPojo.getModifydate() == null) saDocreposPojo.setModifydate(new Date());
        if (saDocreposPojo.getCustom1() == null) saDocreposPojo.setCustom1("");
        if (saDocreposPojo.getCustom2() == null) saDocreposPojo.setCustom2("");
        if (saDocreposPojo.getCustom3() == null) saDocreposPojo.setCustom3("");
        if (saDocreposPojo.getCustom4() == null) saDocreposPojo.setCustom4("");
        if (saDocreposPojo.getCustom5() == null) saDocreposPojo.setCustom5("");
        if (saDocreposPojo.getDeptid() == null) saDocreposPojo.setDeptid("");
        if (saDocreposPojo.getTenantid() == null) saDocreposPojo.setTenantid("");
        if (saDocreposPojo.getRevision() == null) saDocreposPojo.setRevision(0);
        SaDocreposEntity saDocreposEntity = new SaDocreposEntity();
        BeanUtils.copyProperties(saDocreposPojo, saDocreposEntity);
        //生成雪花id
        saDocreposEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDocreposEntity.setRevision(1);  //乐观锁
        this.saDocreposMapper.insert(saDocreposEntity);
        return this.getEntity(saDocreposEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDocreposPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDocreposPojo update(SaDocreposPojo saDocreposPojo) {
        SaDocreposEntity saDocreposEntity = new SaDocreposEntity();
        BeanUtils.copyProperties(saDocreposPojo, saDocreposEntity);
        this.saDocreposMapper.update(saDocreposEntity);
        return this.getEntity(saDocreposEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saDocreposMapper.delete(key);
    }


}
