package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDocshareEntity;
import inks.service.sa.pms.domain.pojo.SaDocsharePojo;
import inks.service.sa.pms.mapper.SaDocshareMapper;
import inks.service.sa.pms.service.SaDocshareService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 文档分享(SaDocshare)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-18 16:14:52
 */
@Service("saDocshareService")
public class SaDocshareServiceImpl implements SaDocshareService {
    @Resource
    private SaDocshareMapper saDocshareMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaDocsharePojo getEntity(String key) {
        return this.saDocshareMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaDocsharePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDocsharePojo> lst = saDocshareMapper.getPageList(queryParam);
            PageInfo<SaDocsharePojo> pageInfo = new PageInfo<SaDocsharePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDocsharePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDocsharePojo insert(SaDocsharePojo saDocsharePojo) {
        //初始化NULL字段
        if (saDocsharePojo.getShareid() == null) saDocsharePojo.setShareid("");
        if (saDocsharePojo.getName() == null) saDocsharePojo.setName("");
        if (saDocsharePojo.getPath() == null) saDocsharePojo.setPath("");
        if (saDocsharePojo.getDocid() == null) saDocsharePojo.setDocid("");
        if (saDocsharePojo.getVid() == null) saDocsharePojo.setVid("");
        if (saDocsharePojo.getShareauth() == null) saDocsharePojo.setShareauth("");
        if (saDocsharePojo.getSharepwd() == null) saDocsharePojo.setSharepwd("");
        if (saDocsharePojo.getSharedby() == null) saDocsharePojo.setSharedby("");
        if (saDocsharePojo.getExpiretime() == null) saDocsharePojo.setExpiretime(new Date());
        if (saDocsharePojo.getRownum() == null) saDocsharePojo.setRownum(0);
        if (saDocsharePojo.getRemark() == null) saDocsharePojo.setRemark("");
        if (saDocsharePojo.getCreateby() == null) saDocsharePojo.setCreateby("");
        if (saDocsharePojo.getCreatebyid() == null) saDocsharePojo.setCreatebyid("");
        if (saDocsharePojo.getCreatedate() == null) saDocsharePojo.setCreatedate(new Date());
        if (saDocsharePojo.getLister() == null) saDocsharePojo.setLister("");
        if (saDocsharePojo.getListerid() == null) saDocsharePojo.setListerid("");
        if (saDocsharePojo.getModifydate() == null) saDocsharePojo.setModifydate(new Date());
        if (saDocsharePojo.getCustom1() == null) saDocsharePojo.setCustom1("");
        if (saDocsharePojo.getCustom2() == null) saDocsharePojo.setCustom2("");
        if (saDocsharePojo.getCustom3() == null) saDocsharePojo.setCustom3("");
        if (saDocsharePojo.getCustom4() == null) saDocsharePojo.setCustom4("");
        if (saDocsharePojo.getCustom5() == null) saDocsharePojo.setCustom5("");
        if (saDocsharePojo.getDeptid() == null) saDocsharePojo.setDeptid("");
        if (saDocsharePojo.getTenantid() == null) saDocsharePojo.setTenantid("");
        if (saDocsharePojo.getRevision() == null) saDocsharePojo.setRevision(0);
        SaDocshareEntity saDocshareEntity = new SaDocshareEntity();
        BeanUtils.copyProperties(saDocsharePojo, saDocshareEntity);
        //生成雪花id
        saDocshareEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDocshareEntity.setRevision(1);  //乐观锁
        this.saDocshareMapper.insert(saDocshareEntity);
        return this.getEntity(saDocshareEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDocsharePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDocsharePojo update(SaDocsharePojo saDocsharePojo) {
        SaDocshareEntity saDocshareEntity = new SaDocshareEntity();
        BeanUtils.copyProperties(saDocsharePojo, saDocshareEntity);
        this.saDocshareMapper.update(saDocshareEntity);
        return this.getEntity(saDocshareEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saDocshareMapper.delete(key);
    }


}
