package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaDocuserEntity;
import inks.service.sa.pms.domain.pojo.SaDocuserPojo;
import inks.service.sa.pms.mapper.SaDocuserMapper;
import inks.service.sa.pms.service.SaDocuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 文档-(SaDocuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-24 16:15:01
 */
@Service("saDocuserService")
public class SaDocuserServiceImpl implements SaDocuserService {
    @Resource
    private SaDocuserMapper saDocuserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaDocuserPojo getEntity(String key) {
        return this.saDocuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaDocuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaDocuserPojo> lst = saDocuserMapper.getPageList(queryParam);
            PageInfo<SaDocuserPojo> pageInfo = new PageInfo<SaDocuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saDocuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDocuserPojo insert(SaDocuserPojo saDocuserPojo) {
        //初始化NULL字段
        if (saDocuserPojo.getUserid() == null) saDocuserPojo.setUserid("");
        if (saDocuserPojo.getGengroupid() == null) saDocuserPojo.setGengroupid("");
        if (saDocuserPojo.getDoctype() == null) saDocuserPojo.setDoctype("");
        if (saDocuserPojo.getDoccode() == null) saDocuserPojo.setDoccode("");
        if (saDocuserPojo.getDocname() == null) saDocuserPojo.setDocname("");
        if (saDocuserPojo.getEnabledmark() == null) saDocuserPojo.setEnabledmark(0);
        if (saDocuserPojo.getEmail() == null) saDocuserPojo.setEmail("");
        if (saDocuserPojo.getRownum() == null) saDocuserPojo.setRownum(0);
        if (saDocuserPojo.getRemark() == null) saDocuserPojo.setRemark("");
        if (saDocuserPojo.getCreateby() == null) saDocuserPojo.setCreateby("");
        if (saDocuserPojo.getCreatebyid() == null) saDocuserPojo.setCreatebyid("");
        if (saDocuserPojo.getCreatedate() == null) saDocuserPojo.setCreatedate(new Date());
        if (saDocuserPojo.getLister() == null) saDocuserPojo.setLister("");
        if (saDocuserPojo.getListerid() == null) saDocuserPojo.setListerid("");
        if (saDocuserPojo.getModifydate() == null) saDocuserPojo.setModifydate(new Date());
        if (saDocuserPojo.getCustom1() == null) saDocuserPojo.setCustom1("");
        if (saDocuserPojo.getCustom2() == null) saDocuserPojo.setCustom2("");
        if (saDocuserPojo.getCustom3() == null) saDocuserPojo.setCustom3("");
        if (saDocuserPojo.getCustom4() == null) saDocuserPojo.setCustom4("");
        if (saDocuserPojo.getCustom5() == null) saDocuserPojo.setCustom5("");
        if (saDocuserPojo.getCustom6() == null) saDocuserPojo.setCustom6("");
        if (saDocuserPojo.getCustom7() == null) saDocuserPojo.setCustom7("");
        if (saDocuserPojo.getCustom8() == null) saDocuserPojo.setCustom8("");
        if (saDocuserPojo.getCustom9() == null) saDocuserPojo.setCustom9("");
        if (saDocuserPojo.getCustom10() == null) saDocuserPojo.setCustom10("");
        if (saDocuserPojo.getDeptid() == null) saDocuserPojo.setDeptid("");
        if (saDocuserPojo.getTenantid() == null) saDocuserPojo.setTenantid("");
        if (saDocuserPojo.getRevision() == null) saDocuserPojo.setRevision(0);
        SaDocuserEntity saDocuserEntity = new SaDocuserEntity();
        BeanUtils.copyProperties(saDocuserPojo, saDocuserEntity);
        //生成雪花id
        saDocuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saDocuserEntity.setRevision(1);  //乐观锁
        this.saDocuserMapper.insert(saDocuserEntity);
        return this.getEntity(saDocuserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saDocuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaDocuserPojo update(SaDocuserPojo saDocuserPojo) {
        SaDocuserEntity saDocuserEntity = new SaDocuserEntity();
        BeanUtils.copyProperties(saDocuserPojo, saDocuserEntity);
        this.saDocuserMapper.update(saDocuserEntity);
        return this.getEntity(saDocuserEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saDocuserMapper.delete(key);
    }


}
