package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.SaEngineerEntity;
import inks.service.sa.pms.domain.pojo.SaEngineerPojo;
import inks.service.sa.pms.mapper.SaEngineerMapper;
import inks.service.sa.pms.service.SaEngineerService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 处理节点(SaEngineer)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-29 16:04:12
 */
@Service("saEngineerService")
public class SaEngineerServiceImpl implements SaEngineerService {
    @Resource
    private SaEngineerMapper saEngineerMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaEngineerPojo getEntity(String key) {
        return this.saEngineerMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaEngineerPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaEngineerPojo> lst = saEngineerMapper.getPageList(queryParam);
            PageInfo<SaEngineerPojo> pageInfo = new PageInfo<SaEngineerPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saEngineerPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaEngineerPojo insert(SaEngineerPojo saEngineerPojo) {
        //初始化NULL字段
        if (saEngineerPojo.getEmail() == null) saEngineerPojo.setEmail("");
        if (saEngineerPojo.getUserid() == null) saEngineerPojo.setUserid("");
        if (saEngineerPojo.getGengroupid() == null) saEngineerPojo.setGengroupid("");
        if (saEngineerPojo.getEngineertype() == null) saEngineerPojo.setEngineertype("");
        if (saEngineerPojo.getEngineercode() == null) saEngineerPojo.setEngineercode("");
        if (saEngineerPojo.getEngineername() == null) saEngineerPojo.setEngineername("");
        if (saEngineerPojo.getEnabledmark() == null) saEngineerPojo.setEnabledmark(0);
        if (saEngineerPojo.getRownum() == null) saEngineerPojo.setRownum(0);
        if (saEngineerPojo.getRemark() == null) saEngineerPojo.setRemark("");
        if (saEngineerPojo.getCreateby() == null) saEngineerPojo.setCreateby("");
        if (saEngineerPojo.getCreatebyid() == null) saEngineerPojo.setCreatebyid("");
        if (saEngineerPojo.getCreatedate() == null) saEngineerPojo.setCreatedate(new Date());
        if (saEngineerPojo.getLister() == null) saEngineerPojo.setLister("");
        if (saEngineerPojo.getListerid() == null) saEngineerPojo.setListerid("");
        if (saEngineerPojo.getModifydate() == null) saEngineerPojo.setModifydate(new Date());
        if (saEngineerPojo.getCustom1() == null) saEngineerPojo.setCustom1("");
        if (saEngineerPojo.getCustom2() == null) saEngineerPojo.setCustom2("");
        if (saEngineerPojo.getCustom3() == null) saEngineerPojo.setCustom3("");
        if (saEngineerPojo.getCustom4() == null) saEngineerPojo.setCustom4("");
        if (saEngineerPojo.getCustom5() == null) saEngineerPojo.setCustom5("");
        if (saEngineerPojo.getCustom6() == null) saEngineerPojo.setCustom6("");
        if (saEngineerPojo.getCustom7() == null) saEngineerPojo.setCustom7("");
        if (saEngineerPojo.getCustom8() == null) saEngineerPojo.setCustom8("");
        if (saEngineerPojo.getCustom9() == null) saEngineerPojo.setCustom9("");
        if (saEngineerPojo.getCustom10() == null) saEngineerPojo.setCustom10("");
        if (saEngineerPojo.getDeptid() == null) saEngineerPojo.setDeptid("");
        if (saEngineerPojo.getTenantid() == null) saEngineerPojo.setTenantid("");
        if (saEngineerPojo.getRevision() == null) saEngineerPojo.setRevision(0);
        SaEngineerEntity saEngineerEntity = new SaEngineerEntity();
        BeanUtils.copyProperties(saEngineerPojo, saEngineerEntity);

        saEngineerEntity.setId(UUID.randomUUID().toString());
        saEngineerEntity.setRevision(1);  //乐观锁
        this.saEngineerMapper.insert(saEngineerEntity);
        return this.getEntity(saEngineerEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saEngineerPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaEngineerPojo update(SaEngineerPojo saEngineerPojo) {
        SaEngineerEntity saEngineerEntity = new SaEngineerEntity();
        BeanUtils.copyProperties(saEngineerPojo, saEngineerEntity);
        this.saEngineerMapper.update(saEngineerEntity);
        return this.getEntity(saEngineerEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.saEngineerMapper.delete(key, tid);
    }


}
