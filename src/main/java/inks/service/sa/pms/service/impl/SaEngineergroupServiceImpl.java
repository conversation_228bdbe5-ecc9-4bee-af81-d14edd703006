package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaEngineergroupPojo;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemPojo;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemdetailPojo;
import inks.service.sa.pms.domain.SaEngineergroupEntity;
import inks.service.sa.pms.domain.SaEngineergroupitemEntity;
import inks.service.sa.pms.mapper.SaEngineergroupMapper;
import inks.service.sa.pms.service.SaEngineergroupService;
import inks.service.sa.pms.service.SaEngineergroupitemService;
import inks.service.sa.pms.mapper.SaEngineergroupitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 工程组主表(SaEngineergroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-15 12:57:58
 */
@Service("saEngineergroupService")
public class SaEngineergroupServiceImpl implements SaEngineergroupService {
    @Resource
    private SaEngineergroupMapper saEngineergroupMapper;
    
    @Resource
    private SaEngineergroupitemMapper saEngineergroupitemMapper;
    

    @Resource
    private SaEngineergroupitemService saEngineergroupitemService;
    

    @Override
    public SaEngineergroupPojo getEntity(String key) {
        return this.saEngineergroupMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaEngineergroupitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaEngineergroupitemdetailPojo> lst = saEngineergroupMapper.getPageList(queryParam);
            PageInfo<SaEngineergroupitemdetailPojo> pageInfo = new PageInfo<SaEngineergroupitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaEngineergroupPojo getBillEntity(String key) {
       try {
        //读取主表
        SaEngineergroupPojo saEngineergroupPojo = this.saEngineergroupMapper.getEntity(key);
        //读取子表
        saEngineergroupPojo.setItem(saEngineergroupitemMapper.getList(saEngineergroupPojo.getId()));
        return saEngineergroupPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaEngineergroupPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaEngineergroupPojo> lst = saEngineergroupMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saEngineergroupitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaEngineergroupPojo> pageInfo = new PageInfo<SaEngineergroupPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaEngineergroupPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaEngineergroupPojo> lst = saEngineergroupMapper.getPageTh(queryParam);
            PageInfo<SaEngineergroupPojo> pageInfo = new PageInfo<SaEngineergroupPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaEngineergroupPojo insert(SaEngineergroupPojo saEngineergroupPojo) {
        //初始化NULL字段
        cleanNull(saEngineergroupPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaEngineergroupEntity saEngineergroupEntity = new SaEngineergroupEntity(); 
        BeanUtils.copyProperties(saEngineergroupPojo,saEngineergroupEntity);
        //设置id和新建日期
        saEngineergroupEntity.setId(id);
        saEngineergroupEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saEngineergroupMapper.insert(saEngineergroupEntity);
        //Item子表处理
        List<SaEngineergroupitemPojo> lst = saEngineergroupPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaEngineergroupitemPojo itemPojo =this.saEngineergroupitemService.clearNull(lst.get(i));
               SaEngineergroupitemEntity saEngineergroupitemEntity = new SaEngineergroupitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saEngineergroupitemEntity);
               //设置id和Pid
               saEngineergroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saEngineergroupitemEntity.setPid(id);
               saEngineergroupitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saEngineergroupitemMapper.insert(saEngineergroupitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saEngineergroupEntity.getId());
    }


    @Override
    @Transactional
    public SaEngineergroupPojo update(SaEngineergroupPojo saEngineergroupPojo) {
        //主表更改
        SaEngineergroupEntity saEngineergroupEntity = new SaEngineergroupEntity(); 
        BeanUtils.copyProperties(saEngineergroupPojo,saEngineergroupEntity);
        this.saEngineergroupMapper.update(saEngineergroupEntity);
        if (saEngineergroupPojo.getItem() != null) {
        //Item子表处理
        List<SaEngineergroupitemPojo> lst = saEngineergroupPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saEngineergroupMapper.getDelItemIds(saEngineergroupPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saEngineergroupitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaEngineergroupitemEntity saEngineergroupitemEntity = new SaEngineergroupitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaEngineergroupitemPojo itemPojo =this.saEngineergroupitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saEngineergroupitemEntity);
               //设置id和Pid
               saEngineergroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saEngineergroupitemEntity.setPid(saEngineergroupEntity.getId());  // 主表 id
               saEngineergroupitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saEngineergroupitemMapper.insert(saEngineergroupitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saEngineergroupitemEntity);             
               this.saEngineergroupitemMapper.update(saEngineergroupitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saEngineergroupEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaEngineergroupPojo saEngineergroupPojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaEngineergroupitemPojo> lst = saEngineergroupPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saEngineergroupitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.saEngineergroupMapper.delete(key) ;
    }
    

    

    private static void cleanNull(SaEngineergroupPojo saEngineergroupPojo) {
        if(saEngineergroupPojo.getGroupcode()==null) saEngineergroupPojo.setGroupcode("");
        if(saEngineergroupPojo.getGroupname()==null) saEngineergroupPojo.setGroupname("");
        if(saEngineergroupPojo.getGrouptype()==null) saEngineergroupPojo.setGrouptype("");
        if(saEngineergroupPojo.getGroupdesc()==null) saEngineergroupPojo.setGroupdesc("");
        if(saEngineergroupPojo.getGroupleader()==null) saEngineergroupPojo.setGroupleader("");
        if(saEngineergroupPojo.getGroupleaderid()==null) saEngineergroupPojo.setGroupleaderid("");
        if(saEngineergroupPojo.getEnabledmark()==null) saEngineergroupPojo.setEnabledmark(0);
        if(saEngineergroupPojo.getRownum()==null) saEngineergroupPojo.setRownum(0);
        if(saEngineergroupPojo.getRemark()==null) saEngineergroupPojo.setRemark("");
        if(saEngineergroupPojo.getCreateby()==null) saEngineergroupPojo.setCreateby("");
        if(saEngineergroupPojo.getCreatebyid()==null) saEngineergroupPojo.setCreatebyid("");
        if(saEngineergroupPojo.getCreatedate()==null) saEngineergroupPojo.setCreatedate(new Date());
        if(saEngineergroupPojo.getLister()==null) saEngineergroupPojo.setLister("");
        if(saEngineergroupPojo.getListerid()==null) saEngineergroupPojo.setListerid("");
        if(saEngineergroupPojo.getModifydate()==null) saEngineergroupPojo.setModifydate(new Date());
        if(saEngineergroupPojo.getCustom1()==null) saEngineergroupPojo.setCustom1("");
        if(saEngineergroupPojo.getCustom2()==null) saEngineergroupPojo.setCustom2("");
        if(saEngineergroupPojo.getCustom3()==null) saEngineergroupPojo.setCustom3("");
        if(saEngineergroupPojo.getCustom4()==null) saEngineergroupPojo.setCustom4("");
        if(saEngineergroupPojo.getCustom5()==null) saEngineergroupPojo.setCustom5("");
        if(saEngineergroupPojo.getTenantid()==null) saEngineergroupPojo.setTenantid("");
        if(saEngineergroupPojo.getRevision()==null) saEngineergroupPojo.setRevision(0);
   }

}
