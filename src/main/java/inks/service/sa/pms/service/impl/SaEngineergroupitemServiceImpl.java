package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaEngineergroupitemPojo;
import inks.service.sa.pms.domain.SaEngineergroupitemEntity;
import inks.service.sa.pms.mapper.SaEngineergroupitemMapper;
import inks.service.sa.pms.service.SaEngineergroupitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 工程组成员子表(SaEngineergroupitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-15 12:58:10
 */
@Service("saEngineergroupitemService")
public class SaEngineergroupitemServiceImpl implements SaEngineergroupitemService {
    @Resource
    private SaEngineergroupitemMapper saEngineergroupitemMapper;

    @Override
    public SaEngineergroupitemPojo getEntity(String key) {
        return this.saEngineergroupitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaEngineergroupitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaEngineergroupitemPojo> lst = saEngineergroupitemMapper.getPageList(queryParam);
            PageInfo<SaEngineergroupitemPojo> pageInfo = new PageInfo<SaEngineergroupitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaEngineergroupitemPojo> getList(String Pid) { 
        try {
            List<SaEngineergroupitemPojo> lst = saEngineergroupitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaEngineergroupitemPojo insert(SaEngineergroupitemPojo saEngineergroupitemPojo) {
        //初始化item的NULL
        SaEngineergroupitemPojo itempojo =this.clearNull(saEngineergroupitemPojo);
        SaEngineergroupitemEntity saEngineergroupitemEntity = new SaEngineergroupitemEntity(); 
        BeanUtils.copyProperties(itempojo,saEngineergroupitemEntity);
         //生成雪花id
          saEngineergroupitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saEngineergroupitemEntity.setRevision(1);  //乐观锁      
          this.saEngineergroupitemMapper.insert(saEngineergroupitemEntity);
        return this.getEntity(saEngineergroupitemEntity.getId());
  
    }

    @Override
    public SaEngineergroupitemPojo update(SaEngineergroupitemPojo saEngineergroupitemPojo) {
        SaEngineergroupitemEntity saEngineergroupitemEntity = new SaEngineergroupitemEntity(); 
        BeanUtils.copyProperties(saEngineergroupitemPojo,saEngineergroupitemEntity);
        this.saEngineergroupitemMapper.update(saEngineergroupitemEntity);
        return this.getEntity(saEngineergroupitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saEngineergroupitemMapper.delete(key) ;
    }

     @Override
     public SaEngineergroupitemPojo clearNull(SaEngineergroupitemPojo saEngineergroupitemPojo){
     //初始化NULL字段
     if(saEngineergroupitemPojo.getPid()==null) saEngineergroupitemPojo.setPid("");
     if(saEngineergroupitemPojo.getEngineerid()==null) saEngineergroupitemPojo.setEngineerid("");
     if(saEngineergroupitemPojo.getEngineername()==null) saEngineergroupitemPojo.setEngineername("");
     if(saEngineergroupitemPojo.getRownum()==null) saEngineergroupitemPojo.setRownum(0);
     if(saEngineergroupitemPojo.getRemark()==null) saEngineergroupitemPojo.setRemark("");
     if(saEngineergroupitemPojo.getCustom1()==null) saEngineergroupitemPojo.setCustom1("");
     if(saEngineergroupitemPojo.getCustom2()==null) saEngineergroupitemPojo.setCustom2("");
     if(saEngineergroupitemPojo.getCustom3()==null) saEngineergroupitemPojo.setCustom3("");
     if(saEngineergroupitemPojo.getCustom4()==null) saEngineergroupitemPojo.setCustom4("");
     if(saEngineergroupitemPojo.getCustom5()==null) saEngineergroupitemPojo.setCustom5("");
     if(saEngineergroupitemPojo.getTenantid()==null) saEngineergroupitemPojo.setTenantid("");
     if(saEngineergroupitemPojo.getRevision()==null) saEngineergroupitemPojo.setRevision(0);
     return saEngineergroupitemPojo;
     }
}
