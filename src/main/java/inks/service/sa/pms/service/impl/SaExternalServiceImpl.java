package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaExternalEntity;
import inks.service.sa.pms.domain.pojo.SaExternalPojo;
import inks.service.sa.pms.mapper.SaExternalMapper;
import inks.service.sa.pms.service.SaExternalService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 扩展应用(SaExternal)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-11-14 09:26:10
 */
@Service("saExternalService")
public class SaExternalServiceImpl implements SaExternalService {
    @Resource
    private SaExternalMapper saExternalMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaExternalPojo getEntity(String key) {
        return this.saExternalMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaExternalPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaExternalPojo> lst = saExternalMapper.getPageList(queryParam);
            PageInfo<SaExternalPojo> pageInfo = new PageInfo<SaExternalPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saExternalPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaExternalPojo insert(SaExternalPojo saExternalPojo) {
        //初始化NULL字段
        if (saExternalPojo.getExttype() == null) saExternalPojo.setExttype("");
        if (saExternalPojo.getExtcode() == null) saExternalPojo.setExtcode("");
        if (saExternalPojo.getExtname() == null) saExternalPojo.setExtname("");
        if (saExternalPojo.getExttitle() == null) saExternalPojo.setExttitle("");
        if (saExternalPojo.getFrontphoto() == null) saExternalPojo.setFrontphoto("");
        if (saExternalPojo.getImagecss() == null) saExternalPojo.setImagecss("");
        if (saExternalPojo.getExturl() == null) saExternalPojo.setExturl("");
        if (saExternalPojo.getExtpd() == null) saExternalPojo.setExtpd("");
        if (saExternalPojo.getAppurl() == null) saExternalPojo.setAppurl("");
        if (saExternalPojo.getApppd() == null) saExternalPojo.setApppd("");
        if (saExternalPojo.getRownum() == null) saExternalPojo.setRownum(0);
        if (saExternalPojo.getEnabledmark() == null) saExternalPojo.setEnabledmark(0);
        if (saExternalPojo.getIspublic() == null) saExternalPojo.setIspublic(0);
        if (saExternalPojo.getPermissioncode() == null) saExternalPojo.setPermissioncode("");
        if (saExternalPojo.getRemark() == null) saExternalPojo.setRemark("");
        if (saExternalPojo.getOperator() == null) saExternalPojo.setOperator("");
        if (saExternalPojo.getOperatorid() == null) saExternalPojo.setOperatorid("");
        if (saExternalPojo.getCreateby() == null) saExternalPojo.setCreateby("");
        if (saExternalPojo.getCreatebyid() == null) saExternalPojo.setCreatebyid("");
        if (saExternalPojo.getCreatedate() == null) saExternalPojo.setCreatedate(new Date());
        if (saExternalPojo.getLister() == null) saExternalPojo.setLister("");
        if (saExternalPojo.getListerid() == null) saExternalPojo.setListerid("");
        if (saExternalPojo.getModifydate() == null) saExternalPojo.setModifydate(new Date());
        if (saExternalPojo.getCustom1() == null) saExternalPojo.setCustom1("");
        if (saExternalPojo.getCustom2() == null) saExternalPojo.setCustom2("");
        if (saExternalPojo.getCustom3() == null) saExternalPojo.setCustom3("");
        if (saExternalPojo.getCustom4() == null) saExternalPojo.setCustom4("");
        if (saExternalPojo.getCustom5() == null) saExternalPojo.setCustom5("");
        if (saExternalPojo.getTenantid() == null) saExternalPojo.setTenantid("");
        if (saExternalPojo.getTenantname() == null) saExternalPojo.setTenantname("");
        if (saExternalPojo.getRevision() == null) saExternalPojo.setRevision(0);
        SaExternalEntity saExternalEntity = new SaExternalEntity();
        BeanUtils.copyProperties(saExternalPojo, saExternalEntity);
        //生成雪花id
        saExternalEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saExternalEntity.setRevision(1);  //乐观锁
        this.saExternalMapper.insert(saExternalEntity);
        return this.getEntity(saExternalEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saExternalPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaExternalPojo update(SaExternalPojo saExternalPojo) {
        SaExternalEntity saExternalEntity = new SaExternalEntity();
        BeanUtils.copyProperties(saExternalPojo, saExternalEntity);
        this.saExternalMapper.update(saExternalEntity);
        return this.getEntity(saExternalEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saExternalMapper.delete(key);
    }


}
