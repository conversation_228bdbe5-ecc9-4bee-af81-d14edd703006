package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaFeedbackEntity;
import inks.service.sa.pms.domain.SaFeedbackitemEntity;
import inks.service.sa.pms.domain.pojo.SaFeedbackPojo;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemPojo;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemdetailPojo;
import inks.service.sa.pms.mapper.SaFeedbackMapper;
import inks.service.sa.pms.mapper.SaFeedbackitemMapper;
import inks.service.sa.pms.service.SaFeedbackService;
import inks.service.sa.pms.service.SaFeedbackitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 反馈单(SaFeedback)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-17 13:56:05
 */
@Service("saFeedbackService")
public class SaFeedbackServiceImpl implements SaFeedbackService {
    @Resource
    private SaFeedbackMapper saFeedbackMapper;

    @Resource
    private SaFeedbackitemMapper saFeedbackitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private SaFeedbackitemService saFeedbackitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFeedbackPojo getEntity(String key) {
        return this.saFeedbackMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFeedbackitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFeedbackitemdetailPojo> lst = saFeedbackMapper.getPageList(queryParam);
            PageInfo<SaFeedbackitemdetailPojo> pageInfo = new PageInfo<SaFeedbackitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaFeedbackPojo getBillEntity(String key) {
        try {
            //读取主表
            SaFeedbackPojo saFeedbackPojo = this.saFeedbackMapper.getEntity(key);
            //读取子表
            saFeedbackPojo.setItem(saFeedbackitemMapper.getList(saFeedbackPojo.getId()));
            return saFeedbackPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaFeedbackPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFeedbackPojo> lst = saFeedbackMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saFeedbackitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaFeedbackPojo> pageInfo = new PageInfo<SaFeedbackPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaFeedbackPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFeedbackPojo> lst = saFeedbackMapper.getPageTh(queryParam);
            PageInfo<SaFeedbackPojo> pageInfo = new PageInfo<SaFeedbackPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public SaFeedbackPojo insert(SaFeedbackPojo saFeedbackPojo) {
        //初始化NULL字段
        cleanNull(saFeedbackPojo);
        saFeedbackPojo.setItemcount(saFeedbackPojo.getItem() != null ? saFeedbackPojo.getItem().size() : 0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaFeedbackEntity saFeedbackEntity = new SaFeedbackEntity();
        BeanUtils.copyProperties(saFeedbackPojo, saFeedbackEntity);
        //设置id和新建日期
        saFeedbackEntity.setId(id);
        saFeedbackEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saFeedbackMapper.insert(saFeedbackEntity);
        //Item子表处理
        List<SaFeedbackitemPojo> lst = saFeedbackPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaFeedbackitemPojo saFeedbackitemPojo : lst) {
                //初始化item的NULL
                SaFeedbackitemPojo itemPojo = this.saFeedbackitemService.clearNull(saFeedbackitemPojo);
                SaFeedbackitemEntity saFeedbackitemEntity = new SaFeedbackitemEntity();
                BeanUtils.copyProperties(itemPojo, saFeedbackitemEntity);
                //设置id和Pid
                saFeedbackitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saFeedbackitemEntity.setPid(id);
                saFeedbackitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saFeedbackitemMapper.insert(saFeedbackitemEntity);
            }
        }
        // 刷新完成行数FinishCount
        saFeedbackMapper.syncFinishcount(id);
        //返回Bill实例
        return this.getBillEntity(saFeedbackEntity.getId());

    }


    @Override
    @Transactional
    public SaFeedbackPojo update(SaFeedbackPojo saFeedbackPojo) {
        saFeedbackPojo.setItemcount(saFeedbackPojo.getItem() != null ? saFeedbackPojo.getItem().size() : 0);
        //主表更改
        SaFeedbackEntity saFeedbackEntity = new SaFeedbackEntity();
        BeanUtils.copyProperties(saFeedbackPojo, saFeedbackEntity);
        this.saFeedbackMapper.update(saFeedbackEntity);
        if (saFeedbackPojo.getItem() != null) {
            //Item子表处理
            List<SaFeedbackitemPojo> lst = saFeedbackPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saFeedbackMapper.getDelItemIds(saFeedbackPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.saFeedbackitemMapper.delete(lstDelId);
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (SaFeedbackitemPojo saFeedbackitemPojo : lst) {
                    SaFeedbackitemEntity saFeedbackitemEntity = new SaFeedbackitemEntity();
                    if ("".equals(saFeedbackitemPojo.getId()) || saFeedbackitemPojo.getId() == null) {
                        //初始化item的NULL
                        SaFeedbackitemPojo itemPojo = this.saFeedbackitemService.clearNull(saFeedbackitemPojo);
                        BeanUtils.copyProperties(itemPojo, saFeedbackitemEntity);
                        //设置id和Pid
                        saFeedbackitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saFeedbackitemEntity.setPid(saFeedbackEntity.getId());  // 主表 id
                        saFeedbackitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saFeedbackitemMapper.insert(saFeedbackitemEntity);
                    } else {
                        BeanUtils.copyProperties(saFeedbackitemPojo, saFeedbackitemEntity);
                        this.saFeedbackitemMapper.update(saFeedbackitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saFeedbackEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
        SaFeedbackPojo saFeedbackPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaFeedbackitemPojo> lst = saFeedbackPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.saFeedbackitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saFeedbackMapper.delete(key);
    }



    @Override
    @Transactional
    public SaFeedbackPojo approval(SaFeedbackPojo saFeedbackPojo) {
        //主表更改
        SaFeedbackEntity saFeedbackEntity = new SaFeedbackEntity();
        BeanUtils.copyProperties(saFeedbackPojo, saFeedbackEntity);
        this.saFeedbackMapper.approval(saFeedbackEntity);
        //返回Bill实例
        return this.getBillEntity(saFeedbackEntity.getId());
    }

    private static void cleanNull(SaFeedbackPojo saFeedbackPojo) {
        if(saFeedbackPojo.getRefno()==null) saFeedbackPojo.setRefno("");
        if(saFeedbackPojo.getBilltitle()==null) saFeedbackPojo.setBilltitle("");
        if(saFeedbackPojo.getBilltype()==null) saFeedbackPojo.setBilltype("");
        if(saFeedbackPojo.getBilldate()==null) saFeedbackPojo.setBilldate(new Date());
        if(saFeedbackPojo.getProjectid()==null) saFeedbackPojo.setProjectid("");
        if(saFeedbackPojo.getProjectname()==null) saFeedbackPojo.setProjectname("");
        if(saFeedbackPojo.getGroupid()==null) saFeedbackPojo.setGroupid("");
        if(saFeedbackPojo.getCustomer()==null) saFeedbackPojo.setCustomer("");
        if(saFeedbackPojo.getLocation()==null) saFeedbackPojo.setLocation("");
        if(saFeedbackPojo.getPersonnel()==null) saFeedbackPojo.setPersonnel("");
        if(saFeedbackPojo.getStatus()==null) saFeedbackPojo.setStatus("");
        if(saFeedbackPojo.getStatusdate()==null) saFeedbackPojo.setStatusdate(new Date());
        if(saFeedbackPojo.getItemcount()==null) saFeedbackPojo.setItemcount(0);
        if(saFeedbackPojo.getFinishcount()==null) saFeedbackPojo.setFinishcount(0);
        if(saFeedbackPojo.getSummary()==null) saFeedbackPojo.setSummary("");
        if(saFeedbackPojo.getCreatebyid()==null) saFeedbackPojo.setCreatebyid("");
        if(saFeedbackPojo.getCreateby()==null) saFeedbackPojo.setCreateby("");
        if(saFeedbackPojo.getCreatedate()==null) saFeedbackPojo.setCreatedate(new Date());
        if(saFeedbackPojo.getListerid()==null) saFeedbackPojo.setListerid("");
        if(saFeedbackPojo.getLister()==null) saFeedbackPojo.setLister("");
        if(saFeedbackPojo.getModifydate()==null) saFeedbackPojo.setModifydate(new Date());
        if(saFeedbackPojo.getAssessorid()==null) saFeedbackPojo.setAssessorid("");
        if(saFeedbackPojo.getAssessor()==null) saFeedbackPojo.setAssessor("");
        if(saFeedbackPojo.getAssessdate()==null) saFeedbackPojo.setAssessdate(new Date());
        if(saFeedbackPojo.getCustom1()==null) saFeedbackPojo.setCustom1("");
        if(saFeedbackPojo.getCustom2()==null) saFeedbackPojo.setCustom2("");
        if(saFeedbackPojo.getCustom3()==null) saFeedbackPojo.setCustom3("");
        if(saFeedbackPojo.getCustom4()==null) saFeedbackPojo.setCustom4("");
        if(saFeedbackPojo.getCustom5()==null) saFeedbackPojo.setCustom5("");
        if(saFeedbackPojo.getRevision()==null) saFeedbackPojo.setRevision(0);
   }

}
