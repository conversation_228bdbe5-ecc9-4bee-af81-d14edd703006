package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaFeedbackitemEntity;
import inks.service.sa.pms.domain.pojo.SaFeedbackitemPojo;
import inks.service.sa.pms.mapper.SaFeedbackitemMapper;
import inks.service.sa.pms.service.SaFeedbackitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 反馈单子表(SaFeedbackitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-17 13:59:53
 */
@Service("saFeedbackitemService")
public class SaFeedbackitemServiceImpl implements SaFeedbackitemService {
    @Resource
    private SaFeedbackitemMapper saFeedbackitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaFeedbackitemPojo getEntity(String key) {
        return this.saFeedbackitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaFeedbackitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFeedbackitemPojo> lst = saFeedbackitemMapper.getPageList(queryParam);
            PageInfo<SaFeedbackitemPojo> pageInfo = new PageInfo<SaFeedbackitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaFeedbackitemPojo> getList(String Pid) {
        try {
            List<SaFeedbackitemPojo> lst = saFeedbackitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saFeedbackitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFeedbackitemPojo insert(SaFeedbackitemPojo saFeedbackitemPojo) {
        //初始化item的NULL
        SaFeedbackitemPojo itempojo = this.clearNull(saFeedbackitemPojo);
        SaFeedbackitemEntity saFeedbackitemEntity = new SaFeedbackitemEntity();
        BeanUtils.copyProperties(itempojo, saFeedbackitemEntity);
        //生成雪花id
        saFeedbackitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saFeedbackitemEntity.setRevision(1);  //乐观锁
        this.saFeedbackitemMapper.insert(saFeedbackitemEntity);
        return this.getEntity(saFeedbackitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saFeedbackitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFeedbackitemPojo update(SaFeedbackitemPojo saFeedbackitemPojo) {
        SaFeedbackitemEntity saFeedbackitemEntity = new SaFeedbackitemEntity();
        BeanUtils.copyProperties(saFeedbackitemPojo, saFeedbackitemEntity);
        this.saFeedbackitemMapper.update(saFeedbackitemEntity);
        return this.getEntity(saFeedbackitemEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saFeedbackitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saFeedbackitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFeedbackitemPojo clearNull(SaFeedbackitemPojo saFeedbackitemPojo) {
        //初始化NULL字段
        if (saFeedbackitemPojo.getPid() == null) saFeedbackitemPojo.setPid("");
        if (saFeedbackitemPojo.getItemtype() == null) saFeedbackitemPojo.setItemtype("");
        if (saFeedbackitemPojo.getStory() == null) saFeedbackitemPojo.setStory("");
        if (saFeedbackitemPojo.getOpinion() == null) saFeedbackitemPojo.setOpinion("");
        if (saFeedbackitemPojo.getExponent() == null) saFeedbackitemPojo.setExponent(0);
     if(saFeedbackitemPojo.getFinishmark()==null) saFeedbackitemPojo.setFinishmark(0);
     if(saFeedbackitemPojo.getSubmitmark()==null) saFeedbackitemPojo.setSubmitmark(0);
        if (saFeedbackitemPojo.getRownum() == null) saFeedbackitemPojo.setRownum(0);
        if (saFeedbackitemPojo.getRemark() == null) saFeedbackitemPojo.setRemark("");
        if (saFeedbackitemPojo.getCustom1() == null) saFeedbackitemPojo.setCustom1("");
        if (saFeedbackitemPojo.getCustom2() == null) saFeedbackitemPojo.setCustom2("");
        if (saFeedbackitemPojo.getCustom3() == null) saFeedbackitemPojo.setCustom3("");
        if (saFeedbackitemPojo.getCustom4() == null) saFeedbackitemPojo.setCustom4("");
        if (saFeedbackitemPojo.getCustom5() == null) saFeedbackitemPojo.setCustom5("");
        if (saFeedbackitemPojo.getRevision() == null) saFeedbackitemPojo.setRevision(0);
        return saFeedbackitemPojo;
    }
}
