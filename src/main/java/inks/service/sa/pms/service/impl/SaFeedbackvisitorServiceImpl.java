package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaFeedbackvisitorPojo;
import inks.service.sa.pms.domain.SaFeedbackvisitorEntity;
import inks.service.sa.pms.mapper.SaFeedbackvisitorMapper;
import inks.service.sa.pms.service.SaFeedbackvisitorService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 游客反馈(SaFeedbackvisitor)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-03-26 15:24:47
 */
@Service("saFeedbackvisitorService")
public class SaFeedbackvisitorServiceImpl implements SaFeedbackvisitorService {
    @Resource
    private SaFeedbackvisitorMapper saFeedbackvisitorMapper;

    @Override
    public SaFeedbackvisitorPojo getEntity(String key) {
        return this.saFeedbackvisitorMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaFeedbackvisitorPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFeedbackvisitorPojo> lst = saFeedbackvisitorMapper.getPageList(queryParam);
            PageInfo<SaFeedbackvisitorPojo> pageInfo = new PageInfo<SaFeedbackvisitorPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaFeedbackvisitorPojo insert(SaFeedbackvisitorPojo saFeedbackvisitorPojo) {
        //初始化NULL字段
        cleanNull(saFeedbackvisitorPojo);
        SaFeedbackvisitorEntity saFeedbackvisitorEntity = new SaFeedbackvisitorEntity(); 
        BeanUtils.copyProperties(saFeedbackvisitorPojo,saFeedbackvisitorEntity);
        //生成雪花id
          saFeedbackvisitorEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFeedbackvisitorEntity.setRevision(1);  //乐观锁
          this.saFeedbackvisitorMapper.insert(saFeedbackvisitorEntity);
        return this.getEntity(saFeedbackvisitorEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFeedbackvisitorPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFeedbackvisitorPojo update(SaFeedbackvisitorPojo saFeedbackvisitorPojo) {
        SaFeedbackvisitorEntity saFeedbackvisitorEntity = new SaFeedbackvisitorEntity(); 
        BeanUtils.copyProperties(saFeedbackvisitorPojo,saFeedbackvisitorEntity);
        this.saFeedbackvisitorMapper.update(saFeedbackvisitorEntity);
        return this.getEntity(saFeedbackvisitorEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saFeedbackvisitorMapper.delete(key) ;
    }
    

    private static void cleanNull(SaFeedbackvisitorPojo saFeedbackvisitorPojo) {
        if(saFeedbackvisitorPojo.getCustname()==null) saFeedbackvisitorPojo.setCustname("");
        if(saFeedbackvisitorPojo.getGroupid()==null) saFeedbackvisitorPojo.setGroupid("");
        if(saFeedbackvisitorPojo.getPhone()==null) saFeedbackvisitorPojo.setPhone("");
        if(saFeedbackvisitorPojo.getEmail()==null) saFeedbackvisitorPojo.setEmail("");
        if(saFeedbackvisitorPojo.getTitle()==null) saFeedbackvisitorPojo.setTitle("");
        if(saFeedbackvisitorPojo.getIssue()==null) saFeedbackvisitorPojo.setIssue("");
        if(saFeedbackvisitorPojo.getAttachment()==null) saFeedbackvisitorPojo.setAttachment("");
        if(saFeedbackvisitorPojo.getPhotos()==null) saFeedbackvisitorPojo.setPhotos("");
        if(saFeedbackvisitorPojo.getSubmitmark()==null) saFeedbackvisitorPojo.setSubmitmark(0);
        if(saFeedbackvisitorPojo.getFinishmark()==null) saFeedbackvisitorPojo.setFinishmark(0);
        if(saFeedbackvisitorPojo.getFinishdesc()==null) saFeedbackvisitorPojo.setFinishdesc("");
        if(saFeedbackvisitorPojo.getEngineer()==null) saFeedbackvisitorPojo.setEngineer("");
        if(saFeedbackvisitorPojo.getClosedate()==null) saFeedbackvisitorPojo.setClosedate(new Date());
        if(saFeedbackvisitorPojo.getCreatebyid()==null) saFeedbackvisitorPojo.setCreatebyid("");
        if(saFeedbackvisitorPojo.getCreateby()==null) saFeedbackvisitorPojo.setCreateby("");
        if(saFeedbackvisitorPojo.getCreatedate()==null) saFeedbackvisitorPojo.setCreatedate(new Date());
        if(saFeedbackvisitorPojo.getModifydate()==null) saFeedbackvisitorPojo.setModifydate(new Date());
        if(saFeedbackvisitorPojo.getListerid()==null) saFeedbackvisitorPojo.setListerid("");
        if(saFeedbackvisitorPojo.getLister()==null) saFeedbackvisitorPojo.setLister("");
        if(saFeedbackvisitorPojo.getCustom1()==null) saFeedbackvisitorPojo.setCustom1("");
        if(saFeedbackvisitorPojo.getCustom2()==null) saFeedbackvisitorPojo.setCustom2("");
        if(saFeedbackvisitorPojo.getCustom3()==null) saFeedbackvisitorPojo.setCustom3("");
        if(saFeedbackvisitorPojo.getCustom4()==null) saFeedbackvisitorPojo.setCustom4("");
        if(saFeedbackvisitorPojo.getCustom5()==null) saFeedbackvisitorPojo.setCustom5("");
        if(saFeedbackvisitorPojo.getRevision()==null) saFeedbackvisitorPojo.setRevision(0);
   }

}
