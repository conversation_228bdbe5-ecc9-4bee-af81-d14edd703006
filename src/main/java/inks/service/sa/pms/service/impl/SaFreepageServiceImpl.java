package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaFreepagePojo;
import inks.service.sa.pms.domain.SaFreepageEntity;
import inks.service.sa.pms.domain.pojo.SaNotebookPojo;
import inks.service.sa.pms.mapper.SaFreepageMapper;
import inks.service.sa.pms.service.SaFreepageService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 自由页面(SaFreepage)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-07-22 10:20:59
 */
@Service("saFreepageService")
public class SaFreepageServiceImpl implements SaFreepageService {
    @Resource
    private SaFreepageMapper saFreepageMapper;

    @Override
    public SaFreepagePojo getEntity(String key) {
        return this.saFreepageMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaFreepagePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFreepagePojo> lst = saFreepageMapper.getPageList(queryParam);
            PageInfo<SaFreepagePojo> pageInfo = new PageInfo<SaFreepagePojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaFreepagePojo insert(SaFreepagePojo saFreepagePojo) {
        //初始化NULL字段
        cleanNull(saFreepagePojo);
        SaFreepageEntity saFreepageEntity = new SaFreepageEntity(); 
        BeanUtils.copyProperties(saFreepagePojo,saFreepageEntity);
        //生成雪花id
          saFreepageEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFreepageEntity.setRevision(1);  //乐观锁
          this.saFreepageMapper.insert(saFreepageEntity);
        return this.getEntity(saFreepageEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFreepagePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFreepagePojo update(SaFreepagePojo saFreepagePojo) {
        SaFreepageEntity saFreepageEntity = new SaFreepageEntity(); 
        BeanUtils.copyProperties(saFreepagePojo,saFreepageEntity);
        this.saFreepageMapper.update(saFreepageEntity);
        return this.getEntity(saFreepageEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saFreepageMapper.delete(key) ;
    }
    

    private static void cleanNull(SaFreepagePojo saFreepagePojo) {
        if(saFreepagePojo.getRefno()==null) saFreepagePojo.setRefno("");
        if(saFreepagePojo.getBilltype()==null) saFreepagePojo.setBilltype("");
        if(saFreepagePojo.getBilldate()==null) saFreepagePojo.setBilldate(new Date());
        if(saFreepagePojo.getBilltitle()==null) saFreepagePojo.setBilltitle("");
        if(saFreepagePojo.getSharekey()==null) saFreepagePojo.setSharekey("");
        if(saFreepagePojo.getPagetemp()==null) saFreepagePojo.setPagetemp("");
        if(saFreepagePojo.getPagedata()==null) saFreepagePojo.setPagedata("");
        if(saFreepagePojo.getRownum()==null) saFreepagePojo.setRownum(0);
        if(saFreepagePojo.getRemark()==null) saFreepagePojo.setRemark("");
        if(saFreepagePojo.getCreateby()==null) saFreepagePojo.setCreateby("");
        if(saFreepagePojo.getCreatebyid()==null) saFreepagePojo.setCreatebyid("");
        if(saFreepagePojo.getCreatedate()==null) saFreepagePojo.setCreatedate(new Date());
        if(saFreepagePojo.getLister()==null) saFreepagePojo.setLister("");
        if(saFreepagePojo.getListerid()==null) saFreepagePojo.setListerid("");
        if(saFreepagePojo.getModifydate()==null) saFreepagePojo.setModifydate(new Date());
        if(saFreepagePojo.getCustom1()==null) saFreepagePojo.setCustom1("");
        if(saFreepagePojo.getCustom2()==null) saFreepagePojo.setCustom2("");
        if(saFreepagePojo.getCustom3()==null) saFreepagePojo.setCustom3("");
        if(saFreepagePojo.getCustom4()==null) saFreepagePojo.setCustom4("");
        if(saFreepagePojo.getCustom5()==null) saFreepagePojo.setCustom5("");
        if(saFreepagePojo.getCustom6()==null) saFreepagePojo.setCustom6("");
        if(saFreepagePojo.getCustom7()==null) saFreepagePojo.setCustom7("");
        if(saFreepagePojo.getCustom8()==null) saFreepagePojo.setCustom8("");
        if(saFreepagePojo.getCustom9()==null) saFreepagePojo.setCustom9("");
        if(saFreepagePojo.getCustom10()==null) saFreepagePojo.setCustom10("");
        if(saFreepagePojo.getTenantid()==null) saFreepagePojo.setTenantid("");
        if(saFreepagePojo.getTenantname()==null) saFreepagePojo.setTenantname("");
        if(saFreepagePojo.getRevision()==null) saFreepagePojo.setRevision(0);
   }


}
