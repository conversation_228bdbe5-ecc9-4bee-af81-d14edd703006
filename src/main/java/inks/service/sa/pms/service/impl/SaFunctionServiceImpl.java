package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaFunctionPojo;
import inks.service.sa.pms.domain.SaFunctionEntity;
import inks.service.sa.pms.mapper.SaFunctionMapper;
import inks.service.sa.pms.service.SaFunctionService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 功能中心表(SaFunction)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-01-03 15:38:20
 */
@Service("saFunctionService")
public class SaFunctionServiceImpl implements SaFunctionService {
    @Resource
    private SaFunctionMapper saFunctionMapper;

    @Override
    public SaFunctionPojo getEntity(String key) {
        return this.saFunctionMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaFunctionPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaFunctionPojo> lst = saFunctionMapper.getPageList(queryParam);
            PageInfo<SaFunctionPojo> pageInfo = new PageInfo<SaFunctionPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaFunctionPojo insert(SaFunctionPojo saFunctionPojo) {
        //初始化NULL字段
        cleanNull(saFunctionPojo);
        SaFunctionEntity saFunctionEntity = new SaFunctionEntity(); 
        BeanUtils.copyProperties(saFunctionPojo,saFunctionEntity);
        //生成雪花id
          saFunctionEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saFunctionEntity.setRevision(1);  //乐观锁
          this.saFunctionMapper.insert(saFunctionEntity);
        return this.getEntity(saFunctionEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saFunctionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaFunctionPojo update(SaFunctionPojo saFunctionPojo) {
        SaFunctionEntity saFunctionEntity = new SaFunctionEntity(); 
        BeanUtils.copyProperties(saFunctionPojo,saFunctionEntity);
        this.saFunctionMapper.update(saFunctionEntity);
        return this.getEntity(saFunctionEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saFunctionMapper.delete(key) ;
    }
    

    private static void cleanNull(SaFunctionPojo saFunctionPojo) {
        if(saFunctionPojo.getProjectid()==null) saFunctionPojo.setProjectid("");
        if(saFunctionPojo.getProjname()==null) saFunctionPojo.setProjname("");
        if(saFunctionPojo.getFunctioncode()==null) saFunctionPojo.setFunctioncode("");
        if(saFunctionPojo.getFunctionname()==null) saFunctionPojo.setFunctionname("");
        if(saFunctionPojo.getFunctiontitle()==null) saFunctionPojo.setFunctiontitle("");
        if(saFunctionPojo.getFunctiondesc()==null) saFunctionPojo.setFunctiondesc("");
        if(saFunctionPojo.getRownum()==null) saFunctionPojo.setRownum(0);
        if(saFunctionPojo.getRemark()==null) saFunctionPojo.setRemark("");
        if(saFunctionPojo.getCreateby()==null) saFunctionPojo.setCreateby("");
        if(saFunctionPojo.getCreatebyid()==null) saFunctionPojo.setCreatebyid("");
        if(saFunctionPojo.getCreatedate()==null) saFunctionPojo.setCreatedate(new Date());
        if(saFunctionPojo.getLister()==null) saFunctionPojo.setLister("");
        if(saFunctionPojo.getListerid()==null) saFunctionPojo.setListerid("");
        if(saFunctionPojo.getModifydate()==null) saFunctionPojo.setModifydate(new Date());
        if(saFunctionPojo.getCustom1()==null) saFunctionPojo.setCustom1("");
        if(saFunctionPojo.getCustom2()==null) saFunctionPojo.setCustom2("");
        if(saFunctionPojo.getCustom3()==null) saFunctionPojo.setCustom3("");
        if(saFunctionPojo.getCustom4()==null) saFunctionPojo.setCustom4("");
        if(saFunctionPojo.getCustom5()==null) saFunctionPojo.setCustom5("");
        if(saFunctionPojo.getTenantid()==null) saFunctionPojo.setTenantid("");
        if(saFunctionPojo.getTenantname()==null) saFunctionPojo.setTenantname("");
        if(saFunctionPojo.getRevision()==null) saFunctionPojo.setRevision(0);
   }

}
