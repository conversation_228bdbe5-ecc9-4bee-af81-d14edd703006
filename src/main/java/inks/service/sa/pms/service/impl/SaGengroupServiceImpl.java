package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaGengroupEntity;
import inks.service.sa.pms.domain.pojo.SaGengroupPojo;
import inks.service.sa.pms.mapper.SaGengroupMapper;
import inks.service.sa.pms.service.SaGengroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 代码生成器分组(SaGengroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-07 13:39:35
 */
@Service("saGengroupService")
public class SaGengroupServiceImpl implements SaGengroupService {
    @Resource
    private SaGengroupMapper saGengroupMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaGengroupPojo getEntity(String key) {
        return this.saGengroupMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaGengroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaGengroupPojo> lst = saGengroupMapper.getPageList(queryParam);
            PageInfo<SaGengroupPojo> pageInfo = new PageInfo<SaGengroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saGengroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaGengroupPojo insert(SaGengroupPojo saGengroupPojo) {
        //初始化NULL字段
        if (saGengroupPojo.getParentid() == null) saGengroupPojo.setParentid("");
        if (saGengroupPojo.getModulecode() == null) saGengroupPojo.setModulecode("");
        if (saGengroupPojo.getGroupcode() == null) saGengroupPojo.setGroupcode("");
        if (saGengroupPojo.getGroupname() == null) saGengroupPojo.setGroupname("");
//     if(saGengroupPojo.getVelocityid()==null) saGengroupPojo.setVelocityid("");  Velocityid可以为null(不是文件)
//     if(saGengroupPojo.getDirmark()==null) saGengroupPojo.setDirmark(0);   Dirmark可以为null(顶级父id)
        if (saGengroupPojo.getEnabledmark() == null) saGengroupPojo.setEnabledmark(0);
        if (saGengroupPojo.getRownum() == null) saGengroupPojo.setRownum(0);
        if (saGengroupPojo.getRemark() == null) saGengroupPojo.setRemark("");
        if (saGengroupPojo.getLister() == null) saGengroupPojo.setLister("");
        if (saGengroupPojo.getCreatedate() == null) saGengroupPojo.setCreatedate(new Date());
        if (saGengroupPojo.getModifydate() == null) saGengroupPojo.setModifydate(new Date());
        if (saGengroupPojo.getTenantid() == null) saGengroupPojo.setTenantid("");
        SaGengroupEntity saGengroupEntity = new SaGengroupEntity();
        BeanUtils.copyProperties(saGengroupPojo, saGengroupEntity);
        //生成雪花id
        saGengroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
//          saGengroupEntity.setRevision(1);  //乐观锁
        this.saGengroupMapper.insert(saGengroupEntity);
        return this.getEntity(saGengroupEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saGengroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaGengroupPojo update(SaGengroupPojo saGengroupPojo) {
        SaGengroupEntity saGengroupEntity = new SaGengroupEntity();
        BeanUtils.copyProperties(saGengroupPojo, saGengroupEntity);
        this.saGengroupMapper.update(saGengroupEntity);
        return this.getEntity(saGengroupEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saGengroupMapper.delete(key);
    }


}
