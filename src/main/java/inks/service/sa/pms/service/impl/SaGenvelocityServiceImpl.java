package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaGenvelocityEntity;
import inks.service.sa.pms.domain.pojo.SaGenvelocityPojo;
import inks.service.sa.pms.mapper.SaGenvelocityMapper;
import inks.service.sa.pms.service.SaGenvelocityService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * (SaGenvelocity)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-02 13:14:50
 */
@Service("saGenvelocityService")
public class SaGenvelocityServiceImpl implements SaGenvelocityService {
    @Resource
    private SaGenvelocityMapper saGenvelocityMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaGenvelocityPojo getEntity(String key) {
        return this.saGenvelocityMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaGenvelocityPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaGenvelocityPojo> lst = saGenvelocityMapper.getPageList(queryParam);
            PageInfo<SaGenvelocityPojo> pageInfo = new PageInfo<SaGenvelocityPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saGenvelocityPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaGenvelocityPojo insert(SaGenvelocityPojo saGenvelocityPojo) {
        //初始化NULL字段
        if (saGenvelocityPojo.getName() == null) saGenvelocityPojo.setName("");
        if (saGenvelocityPojo.getVelocity() == null) saGenvelocityPojo.setVelocity("");
        if (saGenvelocityPojo.getGengroupid() == null) saGenvelocityPojo.setGengroupid("");
        if (saGenvelocityPojo.getRevion() == null) saGenvelocityPojo.setRevion(0);
        if (saGenvelocityPojo.getRownum() == null) saGenvelocityPojo.setRownum(0);
        if (saGenvelocityPojo.getAssessor() == null) saGenvelocityPojo.setAssessor("");
        if (saGenvelocityPojo.getAssessorid() == null) saGenvelocityPojo.setAssessorid("");
        if (saGenvelocityPojo.getAssessdate() == null) saGenvelocityPojo.setAssessdate(new Date());
        if (saGenvelocityPojo.getCreatebyid() == null) saGenvelocityPojo.setCreatebyid("");
        if (saGenvelocityPojo.getCreateby() == null) saGenvelocityPojo.setCreateby("");
        if (saGenvelocityPojo.getCreatedate() == null) saGenvelocityPojo.setCreatedate(new Date());
        if (saGenvelocityPojo.getListerid() == null) saGenvelocityPojo.setListerid("");
        if (saGenvelocityPojo.getLister() == null) saGenvelocityPojo.setLister("");
        if (saGenvelocityPojo.getModifydate() == null) saGenvelocityPojo.setModifydate(new Date());
        if (saGenvelocityPojo.getCustom1() == null) saGenvelocityPojo.setCustom1("");
        if (saGenvelocityPojo.getCustom2() == null) saGenvelocityPojo.setCustom2("");
        if (saGenvelocityPojo.getCustom3() == null) saGenvelocityPojo.setCustom3("");
        if (saGenvelocityPojo.getCustom4() == null) saGenvelocityPojo.setCustom4("");
        if (saGenvelocityPojo.getCustom5() == null) saGenvelocityPojo.setCustom5("");
        if (saGenvelocityPojo.getTenantid() == null) saGenvelocityPojo.setTenantid("");
        if (saGenvelocityPojo.getRevision() == null) saGenvelocityPojo.setRevision(0);
        SaGenvelocityEntity saGenvelocityEntity = new SaGenvelocityEntity();
        BeanUtils.copyProperties(saGenvelocityPojo, saGenvelocityEntity);
        //生成雪花id
        saGenvelocityEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saGenvelocityEntity.setRevision(1);  //乐观锁
        this.saGenvelocityMapper.insert(saGenvelocityEntity);
        return this.getEntity(saGenvelocityEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saGenvelocityPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaGenvelocityPojo update(SaGenvelocityPojo saGenvelocityPojo) {
        SaGenvelocityEntity saGenvelocityEntity = new SaGenvelocityEntity();
        BeanUtils.copyProperties(saGenvelocityPojo, saGenvelocityEntity);
        this.saGenvelocityMapper.update(saGenvelocityEntity);
        return this.getEntity(saGenvelocityEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saGenvelocityMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saGenvelocityPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaGenvelocityPojo approval(SaGenvelocityPojo saGenvelocityPojo) {
        //主表更改
        SaGenvelocityEntity saGenvelocityEntity = new SaGenvelocityEntity();
        BeanUtils.copyProperties(saGenvelocityPojo, saGenvelocityEntity);
        this.saGenvelocityMapper.approval(saGenvelocityEntity);
        //返回Bill实例
        return this.getEntity(saGenvelocityEntity.getId());
    }

    @Override
    public List<String> getAllVelocity() {
        return this.saGenvelocityMapper.getAllVelocity();
    }

    @Override
    public List<SaGenvelocityPojo> getAllEntity() {
        return this.saGenvelocityMapper.getAllEntity();
    }

    @Override
    public List<String> databases() {
        return this.saGenvelocityMapper.databases();
    }

    @Override
    public PageInfo<Map<String, Object>> table(QueryParam queryParam, String database) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<Map<String, Object>> table = saGenvelocityMapper.table(queryParam, database);
            PageInfo<Map<String, Object>> pageInfo = new PageInfo<Map<String, Object>>(table);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public List<Map<String, String>> getTableFields(String databases, String tableName) {
        return this.saGenvelocityMapper.getTableFields(databases, tableName);
    }
}
