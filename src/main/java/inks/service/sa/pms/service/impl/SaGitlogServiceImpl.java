package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaGitlogPojo;
import inks.service.sa.pms.domain.SaGitlogEntity;
import inks.service.sa.pms.mapper.SaGitlogMapper;
import inks.service.sa.pms.service.SaGitlogService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 代码提交日志(SaGitlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-24 10:47:36
 */
@Service("saGitlogService")
public class SaGitlogServiceImpl implements SaGitlogService {
    @Resource
    private SaGitlogMapper saGitlogMapper;

    @Override
    public SaGitlogPojo getEntity(String key) {
        return this.saGitlogMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaGitlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaGitlogPojo> lst = saGitlogMapper.getPageList(queryParam);
            PageInfo<SaGitlogPojo> pageInfo = new PageInfo<SaGitlogPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaGitlogPojo insert(SaGitlogPojo saGitlogPojo) {
        //初始化NULL字段
        cleanNull(saGitlogPojo);
        SaGitlogEntity saGitlogEntity = new SaGitlogEntity(); 
        BeanUtils.copyProperties(saGitlogPojo,saGitlogEntity);
        //生成雪花id
          saGitlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saGitlogEntity.setRevision(1);  //乐观锁
          this.saGitlogMapper.insert(saGitlogEntity);
        return this.getEntity(saGitlogEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saGitlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaGitlogPojo update(SaGitlogPojo saGitlogPojo) {
        SaGitlogEntity saGitlogEntity = new SaGitlogEntity(); 
        BeanUtils.copyProperties(saGitlogPojo,saGitlogEntity);
        this.saGitlogMapper.update(saGitlogEntity);
        return this.getEntity(saGitlogEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saGitlogMapper.delete(key) ;
    }
    

    private static void cleanNull(SaGitlogPojo saGitlogPojo) {
        if(saGitlogPojo.getEventname()==null) saGitlogPojo.setEventname("");
        if(saGitlogPojo.getUsername()==null) saGitlogPojo.setUsername("");
        if(saGitlogPojo.getProjectname()==null) saGitlogPojo.setProjectname("");
        if(saGitlogPojo.getProjecturl()==null) saGitlogPojo.setProjecturl("");
        if(saGitlogPojo.getProjectdesc()==null) saGitlogPojo.setProjectdesc("");
        if(saGitlogPojo.getProjectnamespace()==null) saGitlogPojo.setProjectnamespace("");
        if(saGitlogPojo.getCommitmessage()==null) saGitlogPojo.setCommitmessage("");
        if(saGitlogPojo.getCommitdate()==null) saGitlogPojo.setCommitdate(new Date());
        if(saGitlogPojo.getCommitauthor()==null) saGitlogPojo.setCommitauthor("");
        if(saGitlogPojo.getCommitemail()==null) saGitlogPojo.setCommitemail("");
        if(saGitlogPojo.getRemark()==null) saGitlogPojo.setRemark("");
        if(saGitlogPojo.getCreateby()==null) saGitlogPojo.setCreateby("");
        if(saGitlogPojo.getCreatebyid()==null) saGitlogPojo.setCreatebyid("");
        if(saGitlogPojo.getCreatedate()==null) saGitlogPojo.setCreatedate(new Date());
        if(saGitlogPojo.getLister()==null) saGitlogPojo.setLister("");
        if(saGitlogPojo.getListerid()==null) saGitlogPojo.setListerid("");
        if(saGitlogPojo.getModifydate()==null) saGitlogPojo.setModifydate(new Date());
        if(saGitlogPojo.getTenantid()==null) saGitlogPojo.setTenantid("");
        if(saGitlogPojo.getTenantname()==null) saGitlogPojo.setTenantname("");
        if(saGitlogPojo.getRevision()==null) saGitlogPojo.setRevision(0);
   }

}
