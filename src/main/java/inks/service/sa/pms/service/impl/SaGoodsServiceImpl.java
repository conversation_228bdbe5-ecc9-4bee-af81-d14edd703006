package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaGoodsEntity;
import inks.service.sa.pms.domain.pojo.SaGoodsPojo;
import inks.service.sa.pms.mapper.SaGoodsMapper;
import inks.service.sa.pms.service.SaGoodsService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 产品(SaGoods)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-17 17:25:57
 */
@Service("saGoodsService")
public class SaGoodsServiceImpl implements SaGoodsService {
    @Resource
    private SaGoodsMapper saGoodsMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaGoodsPojo getEntity(String key) {
        return this.saGoodsMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaGoodsPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaGoodsPojo> lst = saGoodsMapper.getPageList(queryParam);
            PageInfo<SaGoodsPojo> pageInfo = new PageInfo<SaGoodsPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saGoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaGoodsPojo insert(SaGoodsPojo saGoodsPojo) {
        //初始化NULL字段
        if (saGoodsPojo.getGengroupid() == null) saGoodsPojo.setGengroupid("");
        if (saGoodsPojo.getGoodscode() == null) saGoodsPojo.setGoodscode("");
        if (saGoodsPojo.getGoodsname() == null) saGoodsPojo.setGoodsname("");
        if (saGoodsPojo.getGoodsspec() == null) saGoodsPojo.setGoodsspec("");
        if (saGoodsPojo.getGoodsunit() == null) saGoodsPojo.setGoodsunit("");
        if (saGoodsPojo.getGoodsprice() == null) saGoodsPojo.setGoodsprice(0D);
        if (saGoodsPojo.getGoodscost() == null) saGoodsPojo.setGoodscost(0D);
        if (saGoodsPojo.getDesciption() == null) saGoodsPojo.setDesciption("");
        if (saGoodsPojo.getEnabledmark() == null) saGoodsPojo.setEnabledmark(0);
        if (saGoodsPojo.getGoodsphote() == null) saGoodsPojo.setGoodsphote("");
        if (saGoodsPojo.getAuthor() == null) saGoodsPojo.setAuthor("");
        if (saGoodsPojo.getMarkdowndata() == null) saGoodsPojo.setMarkdowndata("");
        if (saGoodsPojo.getMdurl() == null) saGoodsPojo.setMdurl("");
        if (saGoodsPojo.getMdlooktimes() == null) saGoodsPojo.setMdlooktimes(0);
        if (saGoodsPojo.getPublicmark() == null) saGoodsPojo.setPublicmark(0);

        if (saGoodsPojo.getAppurl() == null) saGoodsPojo.setAppurl("");
        if (saGoodsPojo.getRateval() == null) saGoodsPojo.setRateval(0);
        if (saGoodsPojo.getDescjson() == null) saGoodsPojo.setDescjson("");
        if (saGoodsPojo.getRownum() == null) saGoodsPojo.setRownum(0);
        if (saGoodsPojo.getRemark() == null) saGoodsPojo.setRemark("");
        if (saGoodsPojo.getCreateby() == null) saGoodsPojo.setCreateby("");
        if (saGoodsPojo.getCreatebyid() == null) saGoodsPojo.setCreatebyid("");
        if (saGoodsPojo.getCreatedate() == null) saGoodsPojo.setCreatedate(new Date());
        if (saGoodsPojo.getLister() == null) saGoodsPojo.setLister("");
        if (saGoodsPojo.getListerid() == null) saGoodsPojo.setListerid("");
        if (saGoodsPojo.getModifydate() == null) saGoodsPojo.setModifydate(new Date());
        if (saGoodsPojo.getCustom1() == null) saGoodsPojo.setCustom1("");
        if (saGoodsPojo.getCustom2() == null) saGoodsPojo.setCustom2("");
        if (saGoodsPojo.getCustom3() == null) saGoodsPojo.setCustom3("");
        if (saGoodsPojo.getCustom4() == null) saGoodsPojo.setCustom4("");
        if (saGoodsPojo.getCustom5() == null) saGoodsPojo.setCustom5("");
        if (saGoodsPojo.getCustom6() == null) saGoodsPojo.setCustom6("");
        if (saGoodsPojo.getCustom7() == null) saGoodsPojo.setCustom7("");
        if (saGoodsPojo.getCustom8() == null) saGoodsPojo.setCustom8("");
        if (saGoodsPojo.getCustom9() == null) saGoodsPojo.setCustom9("");
        if (saGoodsPojo.getCustom10() == null) saGoodsPojo.setCustom10("");
        if (saGoodsPojo.getTenantid() == null) saGoodsPojo.setTenantid("");
        if (saGoodsPojo.getTenantname() == null) saGoodsPojo.setTenantname("");
        if (saGoodsPojo.getRevision() == null) saGoodsPojo.setRevision(0);
        SaGoodsEntity saGoodsEntity = new SaGoodsEntity();
        BeanUtils.copyProperties(saGoodsPojo, saGoodsEntity);
        //生成雪花id
        saGoodsEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saGoodsEntity.setRevision(1);  //乐观锁
        this.saGoodsMapper.insert(saGoodsEntity);
        return this.getEntity(saGoodsEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saGoodsPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaGoodsPojo update(SaGoodsPojo saGoodsPojo) {
        SaGoodsEntity saGoodsEntity = new SaGoodsEntity();
        BeanUtils.copyProperties(saGoodsPojo, saGoodsEntity);
        this.saGoodsMapper.update(saGoodsEntity);
        return this.getEntity(saGoodsEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saGoodsMapper.delete(key);
    }


}
