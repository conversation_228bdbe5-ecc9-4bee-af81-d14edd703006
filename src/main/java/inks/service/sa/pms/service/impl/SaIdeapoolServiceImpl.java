package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaIdeapoolPojo;
import inks.service.sa.pms.domain.SaIdeapoolEntity;
import inks.service.sa.pms.mapper.SaIdeapoolMapper;
import inks.service.sa.pms.service.SaIdeapoolService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
/**
 * 创意池(SaIdeapool)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-12-04 15:23:23
 */
@Service("saIdeapoolService")
public class SaIdeapoolServiceImpl implements SaIdeapoolService {
    @Resource
    private SaIdeapoolMapper saIdeapoolMapper;

    @Override
    public SaIdeapoolPojo getEntity(String key) {
        return this.saIdeapoolMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaIdeapoolPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaIdeapoolPojo> lst = saIdeapoolMapper.getPageList(queryParam);
            PageInfo<SaIdeapoolPojo> pageInfo = new PageInfo<SaIdeapoolPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaIdeapoolPojo insert(SaIdeapoolPojo saIdeapoolPojo) {
        //初始化NULL字段
        cleanNull(saIdeapoolPojo);
        SaIdeapoolEntity saIdeapoolEntity = new SaIdeapoolEntity(); 
        BeanUtils.copyProperties(saIdeapoolPojo,saIdeapoolEntity);
        //生成雪花id
          saIdeapoolEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saIdeapoolEntity.setRevision(1);  //乐观锁
          this.saIdeapoolMapper.insert(saIdeapoolEntity);
        return this.getEntity(saIdeapoolEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saIdeapoolPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaIdeapoolPojo update(SaIdeapoolPojo saIdeapoolPojo) {
        SaIdeapoolEntity saIdeapoolEntity = new SaIdeapoolEntity(); 
        BeanUtils.copyProperties(saIdeapoolPojo,saIdeapoolEntity);
        this.saIdeapoolMapper.update(saIdeapoolEntity);
        return this.getEntity(saIdeapoolEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saIdeapoolMapper.delete(key) ;
    }
    

    private static void cleanNull(SaIdeapoolPojo saIdeapoolPojo) {
        if(saIdeapoolPojo.getRefno()==null) saIdeapoolPojo.setRefno("");
        if(saIdeapoolPojo.getBilldate()==null) saIdeapoolPojo.setBilldate(new Date());
        if(saIdeapoolPojo.getIdeatitle()==null) saIdeapoolPojo.setIdeatitle("");
        if(saIdeapoolPojo.getIdeatype()==null) saIdeapoolPojo.setIdeatype("");
        if(saIdeapoolPojo.getIdeajson()==null) saIdeapoolPojo.setIdeajson("");
        if(saIdeapoolPojo.getPublicmark()==null) saIdeapoolPojo.setPublicmark(0);
        if(saIdeapoolPojo.getFinishmark()==null) saIdeapoolPojo.setFinishmark(0);
        if(saIdeapoolPojo.getOperator()==null) saIdeapoolPojo.setOperator("");
        if(saIdeapoolPojo.getOperatorid()==null) saIdeapoolPojo.setOperatorid("");
        if(saIdeapoolPojo.getItemcount()==null) saIdeapoolPojo.setItemcount(0);
        if(saIdeapoolPojo.getRownum()==null) saIdeapoolPojo.setRownum(0);
        if(saIdeapoolPojo.getRemark()==null) saIdeapoolPojo.setRemark("");
        if(saIdeapoolPojo.getCreateby()==null) saIdeapoolPojo.setCreateby("");
        if(saIdeapoolPojo.getCreatebyid()==null) saIdeapoolPojo.setCreatebyid("");
        if(saIdeapoolPojo.getCreatedate()==null) saIdeapoolPojo.setCreatedate(new Date());
        if(saIdeapoolPojo.getLister()==null) saIdeapoolPojo.setLister("");
        if(saIdeapoolPojo.getListerid()==null) saIdeapoolPojo.setListerid("");
        if(saIdeapoolPojo.getModifydate()==null) saIdeapoolPojo.setModifydate(new Date());
        if(saIdeapoolPojo.getCustom1()==null) saIdeapoolPojo.setCustom1("");
        if(saIdeapoolPojo.getCustom2()==null) saIdeapoolPojo.setCustom2("");
        if(saIdeapoolPojo.getCustom3()==null) saIdeapoolPojo.setCustom3("");
        if(saIdeapoolPojo.getCustom4()==null) saIdeapoolPojo.setCustom4("");
        if(saIdeapoolPojo.getCustom5()==null) saIdeapoolPojo.setCustom5("");
        if(saIdeapoolPojo.getDeptid()==null) saIdeapoolPojo.setDeptid("");
        if(saIdeapoolPojo.getTenantid()==null) saIdeapoolPojo.setTenantid("");
        if(saIdeapoolPojo.getTenantname()==null) saIdeapoolPojo.setTenantname("");
        if(saIdeapoolPojo.getRevision()==null) saIdeapoolPojo.setRevision(0);
   }

    @Override
    public List<SaIdeapoolPojo> getAllList() {
        return this.saIdeapoolMapper.getAllList();
    }
}
