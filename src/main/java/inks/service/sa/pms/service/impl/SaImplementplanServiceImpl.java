package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaImplementplanPojo;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemPojo;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemdetailPojo;
import inks.service.sa.pms.domain.SaImplementplanEntity;
import inks.service.sa.pms.domain.SaImplementplanitemEntity;
import inks.service.sa.pms.mapper.SaImplementplanMapper;
import inks.service.sa.pms.service.SaImplementplanService;
import inks.service.sa.pms.service.SaImplementplanitemService;
import inks.service.sa.pms.mapper.SaImplementplanitemMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 实施活动计划(SaImplementplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-06 14:40:19
 */
@Service("saImplementplanService")
public class SaImplementplanServiceImpl implements SaImplementplanService {
    @Resource
    private SaImplementplanMapper saImplementplanMapper;
    
    @Resource
    private SaImplementplanitemMapper saImplementplanitemMapper;
    

    @Resource
    private SaImplementplanitemService saImplementplanitemService;
    

    @Override
    public SaImplementplanPojo getEntity(String key) {
        return this.saImplementplanMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaImplementplanitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaImplementplanitemdetailPojo> lst = saImplementplanMapper.getPageList(queryParam);
            PageInfo<SaImplementplanitemdetailPojo> pageInfo = new PageInfo<SaImplementplanitemdetailPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public SaImplementplanPojo getBillEntity(String key) {
       try {
        //读取主表
        SaImplementplanPojo saImplementplanPojo = this.saImplementplanMapper.getEntity(key);
        //读取子表
        saImplementplanPojo.setItem(saImplementplanitemMapper.getList(saImplementplanPojo.getId()));
        return saImplementplanPojo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public PageInfo<SaImplementplanPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaImplementplanPojo> lst = saImplementplanMapper.getPageTh(queryParam);
             //循环设置每个主表对象的item子表
            for(int i=0;i<lst.size();i++){
                lst.get(i).setItem(saImplementplanitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaImplementplanPojo> pageInfo = new PageInfo<SaImplementplanPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }
    

    @Override
    public PageInfo<SaImplementplanPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaImplementplanPojo> lst = saImplementplanMapper.getPageTh(queryParam);
            PageInfo<SaImplementplanPojo> pageInfo = new PageInfo<SaImplementplanPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    @Transactional
    public SaImplementplanPojo insert(SaImplementplanPojo saImplementplanPojo) {
        //初始化NULL字段
        cleanNull(saImplementplanPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaImplementplanEntity saImplementplanEntity = new SaImplementplanEntity(); 
        BeanUtils.copyProperties(saImplementplanPojo,saImplementplanEntity);
        //设置id和新建日期
        saImplementplanEntity.setId(id);
        saImplementplanEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saImplementplanMapper.insert(saImplementplanEntity);
        //Item子表处理
        List<SaImplementplanitemPojo> lst = saImplementplanPojo.getItem();
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               //初始化item的NULL
               SaImplementplanitemPojo itemPojo =this.saImplementplanitemService.clearNull(lst.get(i));
               SaImplementplanitemEntity saImplementplanitemEntity = new SaImplementplanitemEntity(); 
               BeanUtils.copyProperties(itemPojo,saImplementplanitemEntity);
               //设置id和Pid
               saImplementplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
               saImplementplanitemEntity.setPid(id);
               saImplementplanitemEntity.setRevision(1);  //乐观锁
               //插入子表
               this.saImplementplanitemMapper.insert(saImplementplanitemEntity);
            }
        } 
        //返回Bill实例
        return this.getBillEntity(saImplementplanEntity.getId());
    }


    @Override
    @Transactional
    public SaImplementplanPojo update(SaImplementplanPojo saImplementplanPojo) {
        //主表更改
        SaImplementplanEntity saImplementplanEntity = new SaImplementplanEntity(); 
        BeanUtils.copyProperties(saImplementplanPojo,saImplementplanEntity);
        this.saImplementplanMapper.update(saImplementplanEntity);
        if (saImplementplanPojo.getItem() != null) {
        //Item子表处理
        List<SaImplementplanitemPojo> lst = saImplementplanPojo.getItem();
        //获取被删除的Item
         List<String> lstDelIds =saImplementplanMapper.getDelItemIds(saImplementplanPojo);
        if (lstDelIds != null){
            //循环每个删除item子表
            for(int i=0;i<lstDelIds.size();i++){
             this.saImplementplanitemMapper.delete(lstDelIds.get(i));
            }
        }
        if (lst != null){
            //循环每个item子表
            for(int i=0;i<lst.size();i++){
               SaImplementplanitemEntity saImplementplanitemEntity = new SaImplementplanitemEntity(); 
               if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null){
                //初始化item的NULL
               SaImplementplanitemPojo itemPojo =this.saImplementplanitemService.clearNull(lst.get(i));
               BeanUtils.copyProperties(itemPojo,saImplementplanitemEntity);
               //设置id和Pid
               saImplementplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
               saImplementplanitemEntity.setPid(saImplementplanEntity.getId());  // 主表 id
               saImplementplanitemEntity.setRevision(1);  // 乐观锁
               //插入子表
               this.saImplementplanitemMapper.insert(saImplementplanitemEntity);
               }
               else
               {
               BeanUtils.copyProperties(lst.get(i),saImplementplanitemEntity);             
               this.saImplementplanitemMapper.update(saImplementplanitemEntity);
               }
            }
        } 
        }
        //返回Bill实例
        return this.getBillEntity(saImplementplanEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
       SaImplementplanPojo saImplementplanPojo =  this.getBillEntity(key);
        //Item子表处理
        List<SaImplementplanitemPojo> lst = saImplementplanPojo.getItem();
        if (lst != null){
            //循环每个删除item子表
            for(int i=0;i<lst.size();i++){
             this.saImplementplanitemMapper.delete(lst.get(i).getId());
            }
        }        
        return this.saImplementplanMapper.delete(key) ;
    }
    

    

    private static void cleanNull(SaImplementplanPojo saImplementplanPojo) {
        if(saImplementplanPojo.getName()==null) saImplementplanPojo.setName("");
        if(saImplementplanPojo.getPrincipalid()==null) saImplementplanPojo.setPrincipalid("");
        if(saImplementplanPojo.getPrincipal()==null) saImplementplanPojo.setPrincipal("");
        if(saImplementplanPojo.getCompletionrate()==null) saImplementplanPojo.setCompletionrate(0);
        if(saImplementplanPojo.getDeadline()==null) saImplementplanPojo.setDeadline(new Date());
        if(saImplementplanPojo.getClosed()==null) saImplementplanPojo.setClosed(0);
        if(saImplementplanPojo.getOnlinemode()==null) saImplementplanPojo.setOnlinemode("");
        if(saImplementplanPojo.getRownum()==null) saImplementplanPojo.setRownum(0);
        if(saImplementplanPojo.getCreatebyid()==null) saImplementplanPojo.setCreatebyid("");
        if(saImplementplanPojo.getCreateby()==null) saImplementplanPojo.setCreateby("");
        if(saImplementplanPojo.getCreatedate()==null) saImplementplanPojo.setCreatedate(new Date());
        if(saImplementplanPojo.getListerid()==null) saImplementplanPojo.setListerid("");
        if(saImplementplanPojo.getLister()==null) saImplementplanPojo.setLister("");
        if(saImplementplanPojo.getModifydate()==null) saImplementplanPojo.setModifydate(new Date());
        if(saImplementplanPojo.getCustom1()==null) saImplementplanPojo.setCustom1("");
        if(saImplementplanPojo.getCustom2()==null) saImplementplanPojo.setCustom2("");
        if(saImplementplanPojo.getCustom3()==null) saImplementplanPojo.setCustom3("");
        if(saImplementplanPojo.getCustom4()==null) saImplementplanPojo.setCustom4("");
        if(saImplementplanPojo.getCustom5()==null) saImplementplanPojo.setCustom5("");
        if(saImplementplanPojo.getTenantid()==null) saImplementplanPojo.setTenantid("");
        if(saImplementplanPojo.getRevision()==null) saImplementplanPojo.setRevision(0);
   }

}
