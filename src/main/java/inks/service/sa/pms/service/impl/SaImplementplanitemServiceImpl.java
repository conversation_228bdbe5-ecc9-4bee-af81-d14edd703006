package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaImplementplanitemPojo;
import inks.service.sa.pms.domain.SaImplementplanitemEntity;
import inks.service.sa.pms.mapper.SaImplementplanitemMapper;
import inks.service.sa.pms.service.SaImplementplanitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 实施计划子表(SaImplementplanitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-10-06 14:40:34
 */
@Service("saImplementplanitemService")
public class SaImplementplanitemServiceImpl implements SaImplementplanitemService {
    @Resource
    private SaImplementplanitemMapper saImplementplanitemMapper;

    @Override
    public SaImplementplanitemPojo getEntity(String key) {
        return this.saImplementplanitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaImplementplanitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaImplementplanitemPojo> lst = saImplementplanitemMapper.getPageList(queryParam);
            PageInfo<SaImplementplanitemPojo> pageInfo = new PageInfo<SaImplementplanitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaImplementplanitemPojo> getList(String Pid) { 
        try {
            List<SaImplementplanitemPojo> lst = saImplementplanitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaImplementplanitemPojo insert(SaImplementplanitemPojo saImplementplanitemPojo) {
        //初始化item的NULL
        SaImplementplanitemPojo itempojo =this.clearNull(saImplementplanitemPojo);
        SaImplementplanitemEntity saImplementplanitemEntity = new SaImplementplanitemEntity(); 
        BeanUtils.copyProperties(itempojo,saImplementplanitemEntity);
         //生成雪花id
          saImplementplanitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saImplementplanitemEntity.setRevision(1);  //乐观锁      
          this.saImplementplanitemMapper.insert(saImplementplanitemEntity);
        return this.getEntity(saImplementplanitemEntity.getId());
  
    }

    @Override
    public SaImplementplanitemPojo update(SaImplementplanitemPojo saImplementplanitemPojo) {
        SaImplementplanitemEntity saImplementplanitemEntity = new SaImplementplanitemEntity(); 
        BeanUtils.copyProperties(saImplementplanitemPojo,saImplementplanitemEntity);
        this.saImplementplanitemMapper.update(saImplementplanitemEntity);
        return this.getEntity(saImplementplanitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saImplementplanitemMapper.delete(key) ;
    }

     @Override
     public SaImplementplanitemPojo clearNull(SaImplementplanitemPojo saImplementplanitemPojo){
     //初始化NULL字段
     if(saImplementplanitemPojo.getPid()==null) saImplementplanitemPojo.setPid("");
     if(saImplementplanitemPojo.getItemtype()==null) saImplementplanitemPojo.setItemtype("");
     if(saImplementplanitemPojo.getItemname()==null) saImplementplanitemPojo.setItemname("");
     if(saImplementplanitemPojo.getItemvalue()==null) saImplementplanitemPojo.setItemvalue("");
     if(saImplementplanitemPojo.getRownum()==null) saImplementplanitemPojo.setRownum(0);
     if(saImplementplanitemPojo.getRemark()==null) saImplementplanitemPojo.setRemark("");
     if(saImplementplanitemPojo.getCustom1()==null) saImplementplanitemPojo.setCustom1("");
     if(saImplementplanitemPojo.getCustom2()==null) saImplementplanitemPojo.setCustom2("");
     if(saImplementplanitemPojo.getCustom3()==null) saImplementplanitemPojo.setCustom3("");
     if(saImplementplanitemPojo.getCustom4()==null) saImplementplanitemPojo.setCustom4("");
     if(saImplementplanitemPojo.getCustom5()==null) saImplementplanitemPojo.setCustom5("");
     if(saImplementplanitemPojo.getRevision()==null) saImplementplanitemPojo.setRevision(0);
     return saImplementplanitemPojo;
     }
}
