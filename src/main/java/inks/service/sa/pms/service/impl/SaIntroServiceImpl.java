package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaIntroEntity;
import inks.service.sa.pms.domain.pojo.SaIntroPojo;
import inks.service.sa.pms.mapper.SaIntroMapper;
import inks.service.sa.pms.service.SaIntroService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 功能简介(SaIntro)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-24 08:56:14
 */
@Service("saIntroService")
public class SaIntroServiceImpl implements SaIntroService {
    @Resource
    private SaIntroMapper saIntroMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaIntroPojo getEntity(String key) {
        return this.saIntroMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaIntroPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaIntroPojo> lst = saIntroMapper.getPageList(queryParam);
            PageInfo<SaIntroPojo> pageInfo = new PageInfo<SaIntroPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saIntroPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaIntroPojo insert(SaIntroPojo saIntroPojo) {
        //初始化NULL字段
        if (saIntroPojo.getGengroupid() == null) saIntroPojo.setGengroupid("");
        if (saIntroPojo.getModulecode() == null) saIntroPojo.setModulecode("");
        if (saIntroPojo.getIntroname() == null) saIntroPojo.setIntroname("");
        if (saIntroPojo.getIntrocontent() == null) saIntroPojo.setIntrocontent("");
        if (saIntroPojo.getRemark() == null) saIntroPojo.setRemark("");
        if (saIntroPojo.getCreateby() == null) saIntroPojo.setCreateby("");
        if (saIntroPojo.getCreatebyid() == null) saIntroPojo.setCreatebyid("");
        if (saIntroPojo.getCreatedate() == null) saIntroPojo.setCreatedate(new Date());
        if (saIntroPojo.getLister() == null) saIntroPojo.setLister("");
        if (saIntroPojo.getListerid() == null) saIntroPojo.setListerid("");
        if (saIntroPojo.getModifydate() == null) saIntroPojo.setModifydate(new Date());
        SaIntroEntity saIntroEntity = new SaIntroEntity();
        BeanUtils.copyProperties(saIntroPojo, saIntroEntity);
        //生成雪花id
        saIntroEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.saIntroMapper.insert(saIntroEntity);
        return this.getEntity(saIntroEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saIntroPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaIntroPojo update(SaIntroPojo saIntroPojo) {
        SaIntroEntity saIntroEntity = new SaIntroEntity();
        BeanUtils.copyProperties(saIntroPojo, saIntroEntity);
        this.saIntroMapper.update(saIntroEntity);
        return this.getEntity(saIntroEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saIntroMapper.delete(key);
    }

    @Override
    public SaIntroPojo getEntityByCode(String key) {
        return this.saIntroMapper.getEntityByCode(key);
    }
}
