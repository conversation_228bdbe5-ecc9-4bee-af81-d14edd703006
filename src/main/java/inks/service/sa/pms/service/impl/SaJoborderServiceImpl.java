package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaJoborderEntity;
import inks.service.sa.pms.domain.pojo.SaJoborderPojo;
import inks.service.sa.pms.mapper.SaJoborderMapper;
import inks.service.sa.pms.service.SaJoborderService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 服务派工(SaJoborder)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-15 13:21:56
 */
@Service("saJoborderService")
public class SaJoborderServiceImpl implements SaJoborderService {
    @Resource
    private SaJoborderMapper saJoborderMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaJoborderPojo getEntity(String key) {
        return this.saJoborderMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaJoborderPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaJoborderPojo> lst = saJoborderMapper.getPageList(queryParam);
            PageInfo<SaJoborderPojo> pageInfo = new PageInfo<SaJoborderPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saJoborderPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaJoborderPojo insert(SaJoborderPojo saJoborderPojo) {
        //初始化NULL字段
        if (saJoborderPojo.getRefno() == null) saJoborderPojo.setRefno("");
        if (saJoborderPojo.getBilltype() == null) saJoborderPojo.setBilltype("");
        if (saJoborderPojo.getBilldate() == null) saJoborderPojo.setBilldate(new Date());
        if (saJoborderPojo.getBilltitle() == null) saJoborderPojo.setBilltitle("");
        if (saJoborderPojo.getProjectid() == null) saJoborderPojo.setProjectid("");
        if (saJoborderPojo.getItemcode() == null) saJoborderPojo.setItemcode("");
        if (saJoborderPojo.getItemname() == null) saJoborderPojo.setItemname("");
        if (saJoborderPojo.getGroupname() == null) saJoborderPojo.setGroupname("");
        if(saJoborderPojo.getGroupid()==null) saJoborderPojo.setGroupid("");
        if(saJoborderPojo.getLinkman()==null) saJoborderPojo.setLinkman("");
        if (saJoborderPojo.getTelephone() == null) saJoborderPojo.setTelephone("");
        if (saJoborderPojo.getSeradd() == null) saJoborderPojo.setSeradd("");
        if (saJoborderPojo.getSerdate() == null) saJoborderPojo.setSerdate(new Date());
        if (saJoborderPojo.getSerclass() == null) saJoborderPojo.setSerclass("");
        if (saJoborderPojo.getOperator() == null) saJoborderPojo.setOperator("");
        if (saJoborderPojo.getOperatorid() == null) saJoborderPojo.setOperatorid("");
        if (saJoborderPojo.getSercontent() == null) saJoborderPojo.setSercontent("");
        if (saJoborderPojo.getSerprocess() == null) saJoborderPojo.setSerprocess("");
        if (saJoborderPojo.getSerloss() == null) saJoborderPojo.setSerloss("");
        if (saJoborderPojo.getConfidential() == null) saJoborderPojo.setConfidential("");
        if (saJoborderPojo.getMatjson() == null) saJoborderPojo.setMatjson("");
        if (saJoborderPojo.getGengroupid() == null) saJoborderPojo.setGengroupid("");
        if (saJoborderPojo.getScore1() == null) saJoborderPojo.setScore1(0);
        if (saJoborderPojo.getScore2() == null) saJoborderPojo.setScore2(0);
        if (saJoborderPojo.getScore3() == null) saJoborderPojo.setScore3(0);
        if (saJoborderPojo.getLicenseplate() == null) saJoborderPojo.setLicenseplate("");
        if (saJoborderPojo.getMileage() == null) saJoborderPojo.setMileage(0D);
        if(saJoborderPojo.getAttachment()==null) saJoborderPojo.setAttachment("");
        if(saJoborderPojo.getRemark()==null) saJoborderPojo.setRemark("");
        if (saJoborderPojo.getCreateby() == null) saJoborderPojo.setCreateby("");
        if (saJoborderPojo.getCreatebyid() == null) saJoborderPojo.setCreatebyid("");
        if (saJoborderPojo.getCreatedate() == null) saJoborderPojo.setCreatedate(new Date());
        if (saJoborderPojo.getLister() == null) saJoborderPojo.setLister("");
        if (saJoborderPojo.getListerid() == null) saJoborderPojo.setListerid("");
        if (saJoborderPojo.getModifydate() == null) saJoborderPojo.setModifydate(new Date());
        if (saJoborderPojo.getAssessor() == null) saJoborderPojo.setAssessor("");
        if (saJoborderPojo.getAssessorid() == null) saJoborderPojo.setAssessorid("");
        if (saJoborderPojo.getAssessdate() == null) saJoborderPojo.setAssessdate(new Date());
        if (saJoborderPojo.getGroupcode() == null) saJoborderPojo.setGroupcode("");
        if (saJoborderPojo.getDisannulmark() == null) saJoborderPojo.setDisannulmark(0);
        if (saJoborderPojo.getFinishmark() == null) saJoborderPojo.setFinishmark(0);
        if (saJoborderPojo.getItemcount() == null) saJoborderPojo.setItemcount(0);
        if (saJoborderPojo.getPrintcount() == null) saJoborderPojo.setPrintcount(0);
        if (saJoborderPojo.getCustom1() == null) saJoborderPojo.setCustom1("");
        if (saJoborderPojo.getCustom2() == null) saJoborderPojo.setCustom2("");
        if (saJoborderPojo.getCustom3() == null) saJoborderPojo.setCustom3("");
        if (saJoborderPojo.getCustom4() == null) saJoborderPojo.setCustom4("");
        if (saJoborderPojo.getCustom5() == null) saJoborderPojo.setCustom5("");
        if (saJoborderPojo.getCustom6() == null) saJoborderPojo.setCustom6("");
        if (saJoborderPojo.getCustom7() == null) saJoborderPojo.setCustom7("");
        if (saJoborderPojo.getCustom8() == null) saJoborderPojo.setCustom8("");
        if (saJoborderPojo.getCustom9() == null) saJoborderPojo.setCustom9("");
        if (saJoborderPojo.getCustom10() == null) saJoborderPojo.setCustom10("");
        if (saJoborderPojo.getTenantid() == null) saJoborderPojo.setTenantid("");
        if (saJoborderPojo.getTenantname() == null) saJoborderPojo.setTenantname("");
        if (saJoborderPojo.getRevision() == null) saJoborderPojo.setRevision(0);
        SaJoborderEntity saJoborderEntity = new SaJoborderEntity();
        BeanUtils.copyProperties(saJoborderPojo, saJoborderEntity);
        //生成雪花id
        saJoborderEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saJoborderEntity.setRevision(1);  //乐观锁
        this.saJoborderMapper.insert(saJoborderEntity);
        return this.getEntity(saJoborderEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saJoborderPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaJoborderPojo update(SaJoborderPojo saJoborderPojo) {
        SaJoborderEntity saJoborderEntity = new SaJoborderEntity();
        BeanUtils.copyProperties(saJoborderPojo, saJoborderEntity);
        this.saJoborderMapper.update(saJoborderEntity);
        return this.getEntity(saJoborderEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saJoborderMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saJoborderPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaJoborderPojo approval(SaJoborderPojo saJoborderPojo) {
        //主表更改
        SaJoborderEntity saJoborderEntity = new SaJoborderEntity();
        BeanUtils.copyProperties(saJoborderPojo, saJoborderEntity);
        this.saJoborderMapper.approval(saJoborderEntity);
        //返回Bill实例
        return this.getEntity(saJoborderEntity.getId());
    }

}
