package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.SaKnowledgeEntity;
import inks.service.sa.pms.domain.pojo.SaKnowledgePojo;
import inks.service.sa.pms.mapper.SaKnowledgeMapper;
import inks.service.sa.pms.service.SaKnowledgeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 知识库(SaKnowledge)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-11-23 13:12:49
 */
@Service("saKnowledgeService")
public class SaKnowledgeServiceImpl implements SaKnowledgeService {
    @Resource
    private SaKnowledgeMapper saKnowledgeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaKnowledgePojo getEntity(String key) {
        return this.saKnowledgeMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaKnowledgePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaKnowledgePojo> lst = saKnowledgeMapper.getPageList(queryParam);
            PageInfo<SaKnowledgePojo> pageInfo = new PageInfo<SaKnowledgePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saKnowledgePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaKnowledgePojo insert(SaKnowledgePojo saKnowledgePojo) {
        //初始化NULL字段
        if (saKnowledgePojo.getTextgoodnum() == null) saKnowledgePojo.setTextgoodnum(0);
        if (saKnowledgePojo.getTextngnum() == null) saKnowledgePojo.setTextngnum(0);
        if (saKnowledgePojo.getTextlooktimes() == null) saKnowledgePojo.setTextlooktimes(0);
        if (saKnowledgePojo.getTextlevel() == null) saKnowledgePojo.setTextlevel(0);


        if (saKnowledgePojo.getGengroupid() == null) saKnowledgePojo.setGengroupid("");
        if (saKnowledgePojo.getRefno() == null) saKnowledgePojo.setRefno("");
        if (saKnowledgePojo.getBilltype() == null) saKnowledgePojo.setBilltype("");
        if (saKnowledgePojo.getBilltitle() == null) saKnowledgePojo.setBilltitle("");
        if (saKnowledgePojo.getBilldate() == null) saKnowledgePojo.setBilldate(new Date());
        if (saKnowledgePojo.getKnowurl() == null) saKnowledgePojo.setKnowurl("");
        if (saKnowledgePojo.getVideourl() == null) saKnowledgePojo.setVideourl("");
        if (saKnowledgePojo.getFrontphoto() == null) saKnowledgePojo.setFrontphoto("");
        if (saKnowledgePojo.getPublicmark() == null) saKnowledgePojo.setPublicmark(0);
        if (saKnowledgePojo.getReleasemark() == null) saKnowledgePojo.setReleasemark(0);
        if (saKnowledgePojo.getRownum() == null) saKnowledgePojo.setRownum(0);
        if (saKnowledgePojo.getBucketname() == null) saKnowledgePojo.setBucketname("");
        if (saKnowledgePojo.getDirname() == null) saKnowledgePojo.setDirname("");
        if (saKnowledgePojo.getFilename() == null) saKnowledgePojo.setFilename("");
        if (saKnowledgePojo.getContenttype() == null) saKnowledgePojo.setContenttype("");
        if (saKnowledgePojo.getStorage() == null) saKnowledgePojo.setStorage("");
        if (saKnowledgePojo.getRelateid() == null) saKnowledgePojo.setRelateid("");
        if (saKnowledgePojo.getRemark() == null) saKnowledgePojo.setRemark("");
        if (saKnowledgePojo.getCreateby() == null) saKnowledgePojo.setCreateby("");
        if (saKnowledgePojo.getCreatebyid() == null) saKnowledgePojo.setCreatebyid("");
        if (saKnowledgePojo.getCreatedate() == null) saKnowledgePojo.setCreatedate(new Date());
        if (saKnowledgePojo.getLister() == null) saKnowledgePojo.setLister("");
        if (saKnowledgePojo.getListerid() == null) saKnowledgePojo.setListerid("");
        if (saKnowledgePojo.getModifydate() == null) saKnowledgePojo.setModifydate(new Date());
        if (saKnowledgePojo.getAssessor() == null) saKnowledgePojo.setAssessor("");
        if (saKnowledgePojo.getAssessorid() == null) saKnowledgePojo.setAssessorid("");
        if (saKnowledgePojo.getAssessdate() == null) saKnowledgePojo.setAssessdate(new Date());
        if (saKnowledgePojo.getDeletemark() == null) saKnowledgePojo.setDeletemark(0);
        if (saKnowledgePojo.getDeletelister() == null) saKnowledgePojo.setDeletelister("");
        if (saKnowledgePojo.getDeletelisterid() == null) saKnowledgePojo.setDeletelisterid("");
        if (saKnowledgePojo.getDeletedate() == null) saKnowledgePojo.setDeletedate(new Date());
        if (saKnowledgePojo.getCustom1() == null) saKnowledgePojo.setCustom1("");
        if (saKnowledgePojo.getCustom2() == null) saKnowledgePojo.setCustom2("");
        if (saKnowledgePojo.getCustom3() == null) saKnowledgePojo.setCustom3("");
        if (saKnowledgePojo.getCustom4() == null) saKnowledgePojo.setCustom4("");
        if (saKnowledgePojo.getCustom5() == null) saKnowledgePojo.setCustom5("");
        if (saKnowledgePojo.getCustom6() == null) saKnowledgePojo.setCustom6("");
        if (saKnowledgePojo.getCustom7() == null) saKnowledgePojo.setCustom7("");
        if (saKnowledgePojo.getCustom8() == null) saKnowledgePojo.setCustom8("");
        if (saKnowledgePojo.getCustom9() == null) saKnowledgePojo.setCustom9("");
        if (saKnowledgePojo.getCustom10() == null) saKnowledgePojo.setCustom10("");
        if (saKnowledgePojo.getDeptid() == null) saKnowledgePojo.setDeptid("");
        if (saKnowledgePojo.getTenantid() == null) saKnowledgePojo.setTenantid("");
        if (saKnowledgePojo.getTenantname() == null) saKnowledgePojo.setTenantname("");
        if (saKnowledgePojo.getRevision() == null) saKnowledgePojo.setRevision(0);
        SaKnowledgeEntity saKnowledgeEntity = new SaKnowledgeEntity();
        BeanUtils.copyProperties(saKnowledgePojo, saKnowledgeEntity);

        saKnowledgeEntity.setId(UUID.randomUUID().toString());
        saKnowledgeEntity.setRevision(1);  //乐观锁
        this.saKnowledgeMapper.insert(saKnowledgeEntity);
        return this.getEntity(saKnowledgeEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saKnowledgePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaKnowledgePojo update(SaKnowledgePojo saKnowledgePojo) {
        SaKnowledgeEntity saKnowledgeEntity = new SaKnowledgeEntity();
        BeanUtils.copyProperties(saKnowledgePojo, saKnowledgeEntity);
        this.saKnowledgeMapper.update(saKnowledgeEntity);
        return this.getEntity(saKnowledgeEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.saKnowledgeMapper.delete(key, tid);
    }

    /**
     * 审核数据
     *
     * @param saKnowledgePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaKnowledgePojo approval(SaKnowledgePojo saKnowledgePojo) {
        //主表更改
        SaKnowledgeEntity saKnowledgeEntity = new SaKnowledgeEntity();
        BeanUtils.copyProperties(saKnowledgePojo, saKnowledgeEntity);
        this.saKnowledgeMapper.approval(saKnowledgeEntity);
        //返回Bill实例
        return this.getEntity(saKnowledgeEntity.getId());
    }

}
