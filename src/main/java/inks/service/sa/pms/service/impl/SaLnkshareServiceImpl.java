package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaLnkshareEntity;
import inks.service.sa.pms.domain.pojo.SaLnksharePojo;
import inks.service.sa.pms.mapper.SaLnkshareMapper;
import inks.service.sa.pms.service.SaLnkshareService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Lnk简书分享表(SaLnkshare)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-12 10:29:08
 */
@Service("saLnkshareService")
public class SaLnkshareServiceImpl implements SaLnkshareService {
    @Resource
    private SaLnkshareMapper saLnkshareMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaLnksharePojo getEntity(String key) {
        return this.saLnkshareMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaLnksharePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaLnksharePojo> lst = saLnkshareMapper.getPageList(queryParam);
            PageInfo<SaLnksharePojo> pageInfo = new PageInfo<SaLnksharePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saLnksharePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLnksharePojo insert(SaLnksharePojo saLnksharePojo) {
        //初始化NULL字段
        if (saLnksharePojo.getLnkmarkdownid() == null) saLnksharePojo.setLnkmarkdownid("");
        if (saLnksharePojo.getDeadtime() == null) saLnksharePojo.setDeadtime(new Date());
        if (saLnksharePojo.getPasswordmark() == null) saLnksharePojo.setPasswordmark(0);
        if (saLnksharePojo.getPassword() == null) saLnksharePojo.setPassword("");
        if (saLnksharePojo.getRemark() == null) saLnksharePojo.setRemark("");
        if (saLnksharePojo.getCreateby() == null) saLnksharePojo.setCreateby("");
        if (saLnksharePojo.getCreatebyid() == null) saLnksharePojo.setCreatebyid("");
        if (saLnksharePojo.getCreatedate() == null) saLnksharePojo.setCreatedate(new Date());
        if (saLnksharePojo.getLister() == null) saLnksharePojo.setLister("");
        if (saLnksharePojo.getListerid() == null) saLnksharePojo.setListerid("");
        if (saLnksharePojo.getModifydate() == null) saLnksharePojo.setModifydate(new Date());
        if (saLnksharePojo.getCustom1() == null) saLnksharePojo.setCustom1("");
        if (saLnksharePojo.getCustom2() == null) saLnksharePojo.setCustom2("");
        if (saLnksharePojo.getCustom3() == null) saLnksharePojo.setCustom3("");
        if (saLnksharePojo.getCustom4() == null) saLnksharePojo.setCustom4("");
        if (saLnksharePojo.getCustom5() == null) saLnksharePojo.setCustom5("");
        if (saLnksharePojo.getTenantid() == null) saLnksharePojo.setTenantid("");
        if (saLnksharePojo.getRevision() == null) saLnksharePojo.setRevision(0);
        SaLnkshareEntity saLnkshareEntity = new SaLnkshareEntity();
        BeanUtils.copyProperties(saLnksharePojo, saLnkshareEntity);
        //生成雪花id
        saLnkshareEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saLnkshareEntity.setRevision(1);  //乐观锁
        this.saLnkshareMapper.insert(saLnkshareEntity);
        return this.getEntity(saLnkshareEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saLnksharePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLnksharePojo update(SaLnksharePojo saLnksharePojo) {
        SaLnkshareEntity saLnkshareEntity = new SaLnkshareEntity();
        BeanUtils.copyProperties(saLnksharePojo, saLnkshareEntity);
        this.saLnkshareMapper.update(saLnkshareEntity);
        return this.getEntity(saLnkshareEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saLnkshareMapper.delete(key);
    }


}
