package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaLnkstarEntity;
import inks.service.sa.pms.domain.pojo.SaLnkstarPojo;
import inks.service.sa.pms.mapper.SaLnkstarMapper;
import inks.service.sa.pms.service.SaLnkstarService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaLnkstar)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-18 10:46:50
 */
@Service("saLnkstarService")
public class SaLnkstarServiceImpl implements SaLnkstarService {
    @Resource
    private SaLnkstarMapper saLnkstarMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaLnkstarPojo getEntity(String key) {
        return this.saLnkstarMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaLnkstarPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaLnkstarPojo> lst = saLnkstarMapper.getPageList(queryParam);
            PageInfo<SaLnkstarPojo> pageInfo = new PageInfo<SaLnkstarPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saLnkstarPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLnkstarPojo insert(SaLnkstarPojo saLnkstarPojo) {
        //初始化NULL字段
        if (saLnkstarPojo.getUserid() == null) saLnkstarPojo.setUserid("");
        if (saLnkstarPojo.getUsername() == null) saLnkstarPojo.setUsername("");
        if (saLnkstarPojo.getLnkmarkdownid() == null) saLnkstarPojo.setLnkmarkdownid("");
        if (saLnkstarPojo.getRownum() == null) saLnkstarPojo.setRownum(0);
        if (saLnkstarPojo.getRemark() == null) saLnkstarPojo.setRemark("");
        if (saLnkstarPojo.getCreateby() == null) saLnkstarPojo.setCreateby("");
        if (saLnkstarPojo.getCreatebyid() == null) saLnkstarPojo.setCreatebyid("");
        if (saLnkstarPojo.getCreatedate() == null) saLnkstarPojo.setCreatedate(new Date());
        if (saLnkstarPojo.getLister() == null) saLnkstarPojo.setLister("");
        if (saLnkstarPojo.getListerid() == null) saLnkstarPojo.setListerid("");
        if (saLnkstarPojo.getModifydate() == null) saLnkstarPojo.setModifydate(new Date());
        if (saLnkstarPojo.getCustom1() == null) saLnkstarPojo.setCustom1("");
        if (saLnkstarPojo.getCustom2() == null) saLnkstarPojo.setCustom2("");
        if (saLnkstarPojo.getCustom3() == null) saLnkstarPojo.setCustom3("");
        if (saLnkstarPojo.getCustom4() == null) saLnkstarPojo.setCustom4("");
        if (saLnkstarPojo.getCustom5() == null) saLnkstarPojo.setCustom5("");
        if (saLnkstarPojo.getTenantid() == null) saLnkstarPojo.setTenantid("");
        if (saLnkstarPojo.getTenantname() == null) saLnkstarPojo.setTenantname("");
        if (saLnkstarPojo.getRevision() == null) saLnkstarPojo.setRevision(0);
        SaLnkstarEntity saLnkstarEntity = new SaLnkstarEntity();
        BeanUtils.copyProperties(saLnkstarPojo, saLnkstarEntity);
        //生成雪花id
        saLnkstarEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saLnkstarEntity.setRevision(1);  //乐观锁
        this.saLnkstarMapper.insert(saLnkstarEntity);
        return this.getEntity(saLnkstarEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saLnkstarPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaLnkstarPojo update(SaLnkstarPojo saLnkstarPojo) {
        SaLnkstarEntity saLnkstarEntity = new SaLnkstarEntity();
        BeanUtils.copyProperties(saLnkstarPojo, saLnkstarEntity);
        this.saLnkstarMapper.update(saLnkstarEntity);
        return this.getEntity(saLnkstarEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saLnkstarMapper.delete(key);
    }


}
