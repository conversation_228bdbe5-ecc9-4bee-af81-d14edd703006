package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMarkdownEntity;
import inks.service.sa.pms.domain.pojo.SaMarkdownPojo;
import inks.service.sa.pms.mapper.SaMarkdownMapper;
import inks.service.sa.pms.service.SaMarkdownService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * MarkDown(LNK简书)(SaMarkdown)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-19 09:48:02
 */
@Service("saMarkdownService")
public class SaMarkdownServiceImpl implements SaMarkdownService {
    @Resource
    private SaMarkdownMapper saMarkdownMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaMarkdownPojo getEntity(String key) {
        return this.saMarkdownMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaMarkdownPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMarkdownPojo> lst = saMarkdownMapper.getPageList(queryParam);
            PageInfo<SaMarkdownPojo> pageInfo = new PageInfo<SaMarkdownPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public PageInfo<SaMarkdownPojo> getMdByProjectId(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMarkdownPojo> lst = saMarkdownMapper.getMdByProjectId(queryParam);
            PageInfo<SaMarkdownPojo> pageInfo = new PageInfo<SaMarkdownPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saMarkdownPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMarkdownPojo insert(SaMarkdownPojo saMarkdownPojo) {
        //初始化NULL字段
        if (saMarkdownPojo.getMdgroupid() == null) saMarkdownPojo.setMdgroupid("");
        if (saMarkdownPojo.getRefno() == null) saMarkdownPojo.setRefno("");
        if (saMarkdownPojo.getBilltype() == null) saMarkdownPojo.setBilltype("");
        if (saMarkdownPojo.getBilltitle() == null) saMarkdownPojo.setBilltitle("");
        if (saMarkdownPojo.getBilldate() == null) saMarkdownPojo.setBilldate(new Date());
        if (saMarkdownPojo.getIntroduction() == null) saMarkdownPojo.setIntroduction("");
        if (saMarkdownPojo.getMdurl() == null) saMarkdownPojo.setMdurl("");
        if (saMarkdownPojo.getStarcount() == null) saMarkdownPojo.setStarcount(0);
        if (saMarkdownPojo.getMdngnum() == null) saMarkdownPojo.setMdngnum(0);
        if (saMarkdownPojo.getMdlooktimes() == null) saMarkdownPojo.setMdlooktimes(0);
        if (saMarkdownPojo.getMdlevel() == null) saMarkdownPojo.setMdlevel(0);
        if (saMarkdownPojo.getFrontphoto() == null) saMarkdownPojo.setFrontphoto("");
        if (saMarkdownPojo.getPublicmark() == null) saMarkdownPojo.setPublicmark(0);
        if (saMarkdownPojo.getReleasemark() == null) saMarkdownPojo.setReleasemark(0);
        if (saMarkdownPojo.getRownum() == null) saMarkdownPojo.setRownum(0);
        if (saMarkdownPojo.getFilesize() == null) saMarkdownPojo.setFilesize(0L);
        if (saMarkdownPojo.getContenttype() == null) saMarkdownPojo.setContenttype("");
        if (saMarkdownPojo.getStorage() == null) saMarkdownPojo.setStorage("");
        if (saMarkdownPojo.getRelateid() == null) saMarkdownPojo.setRelateid("");
        if (saMarkdownPojo.getRemark() == null) saMarkdownPojo.setRemark("");
        if (saMarkdownPojo.getCreateby() == null) saMarkdownPojo.setCreateby("");
        if (saMarkdownPojo.getCreatebyid() == null) saMarkdownPojo.setCreatebyid("");
        if (saMarkdownPojo.getCreatedate() == null) saMarkdownPojo.setCreatedate(new Date());
        if (saMarkdownPojo.getLister() == null) saMarkdownPojo.setLister("");
        if (saMarkdownPojo.getListerid() == null) saMarkdownPojo.setListerid("");
        if (saMarkdownPojo.getModifydate() == null) saMarkdownPojo.setModifydate(new Date());
        if (saMarkdownPojo.getAssessorid() == null) saMarkdownPojo.setAssessorid("");
        if (saMarkdownPojo.getAssessor() == null) saMarkdownPojo.setAssessor("");
        if (saMarkdownPojo.getAssessdate() == null) saMarkdownPojo.setAssessdate(new Date());
        if (saMarkdownPojo.getDeletemark() == null) saMarkdownPojo.setDeletemark(0);
        if (saMarkdownPojo.getDeletelister() == null) saMarkdownPojo.setDeletelister("");
        if (saMarkdownPojo.getDeletelisterid() == null) saMarkdownPojo.setDeletelisterid("");
        if (saMarkdownPojo.getDeletedate() == null) saMarkdownPojo.setDeletedate(new Date());
        if (saMarkdownPojo.getCustom1() == null) saMarkdownPojo.setCustom1("");
        if (saMarkdownPojo.getCustom2() == null) saMarkdownPojo.setCustom2("");
        if (saMarkdownPojo.getCustom3() == null) saMarkdownPojo.setCustom3("");
        if (saMarkdownPojo.getCustom4() == null) saMarkdownPojo.setCustom4("");
        if (saMarkdownPojo.getCustom5() == null) saMarkdownPojo.setCustom5("");
        if (saMarkdownPojo.getCustom6() == null) saMarkdownPojo.setCustom6("");
        if (saMarkdownPojo.getCustom7() == null) saMarkdownPojo.setCustom7("");
        if (saMarkdownPojo.getCustom8() == null) saMarkdownPojo.setCustom8("");
        if (saMarkdownPojo.getCustom9() == null) saMarkdownPojo.setCustom9("");
        if (saMarkdownPojo.getCustom10() == null) saMarkdownPojo.setCustom10("");
        if (saMarkdownPojo.getTenantid() == null) saMarkdownPojo.setTenantid("");
        if (saMarkdownPojo.getRevision() == null) saMarkdownPojo.setRevision(0);
        SaMarkdownEntity saMarkdownEntity = new SaMarkdownEntity();
        BeanUtils.copyProperties(saMarkdownPojo, saMarkdownEntity);
        //生成雪花id
        saMarkdownEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saMarkdownEntity.setRevision(1);  //乐观锁
        this.saMarkdownMapper.insert(saMarkdownEntity);
        return this.getEntity(saMarkdownEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saMarkdownPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMarkdownPojo update(SaMarkdownPojo saMarkdownPojo) {
        SaMarkdownEntity saMarkdownEntity = new SaMarkdownEntity();
        BeanUtils.copyProperties(saMarkdownPojo, saMarkdownEntity);
        this.saMarkdownMapper.update(saMarkdownEntity);
        return this.getEntity(saMarkdownEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saMarkdownMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saMarkdownPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaMarkdownPojo approval(SaMarkdownPojo saMarkdownPojo) {
        //主表更改
        SaMarkdownEntity saMarkdownEntity = new SaMarkdownEntity();
        BeanUtils.copyProperties(saMarkdownPojo, saMarkdownEntity);
        this.saMarkdownMapper.approval(saMarkdownEntity);
        //返回Bill实例
        return this.getEntity(saMarkdownEntity.getId());
    }

}
