package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMarkdowndocEntity;
import inks.service.sa.pms.domain.pojo.SaMarkdowndocPojo;
import inks.service.sa.pms.mapper.SaMarkdowndocMapper;
import inks.service.sa.pms.service.SaMarkdowndocService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * MarkDown(Doc文档)(SaMarkdowndoc)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:26
 */
@Service("saMarkdowndocService")
public class SaMarkdowndocServiceImpl implements SaMarkdowndocService {
    @Resource
    private SaMarkdowndocMapper saMarkdowndocMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaMarkdowndocPojo getEntity(String key) {
        return this.saMarkdowndocMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaMarkdowndocPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMarkdowndocPojo> lst = saMarkdowndocMapper.getPageList(queryParam);
            PageInfo<SaMarkdowndocPojo> pageInfo = new PageInfo<SaMarkdowndocPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saMarkdowndocPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMarkdowndocPojo insert(SaMarkdowndocPojo saMarkdowndocPojo) {
        //初始化NULL字段
        if (saMarkdowndocPojo.getMdgroupid() == null) saMarkdowndocPojo.setMdgroupid("");
        if (saMarkdowndocPojo.getRefno() == null) saMarkdowndocPojo.setRefno("");
        if (saMarkdowndocPojo.getBilltype() == null) saMarkdowndocPojo.setBilltype("");
        if (saMarkdowndocPojo.getBilltitle() == null) saMarkdowndocPojo.setBilltitle("");
        if (saMarkdowndocPojo.getBilldate() == null) saMarkdowndocPojo.setBilldate(new Date());
        if (saMarkdowndocPojo.getIntroduction() == null) saMarkdowndocPojo.setIntroduction("");
        if (saMarkdowndocPojo.getMdurl() == null) saMarkdowndocPojo.setMdurl("");
        if (saMarkdowndocPojo.getStarcount() == null) saMarkdowndocPojo.setStarcount(0);
        if (saMarkdowndocPojo.getMdngnum() == null) saMarkdowndocPojo.setMdngnum(0);
        if (saMarkdowndocPojo.getMdlooktimes() == null) saMarkdowndocPojo.setMdlooktimes(0);
        if (saMarkdowndocPojo.getMdlevel() == null) saMarkdowndocPojo.setMdlevel(0);
        if (saMarkdowndocPojo.getFrontphoto() == null) saMarkdowndocPojo.setFrontphoto("");
        if (saMarkdowndocPojo.getPublicmark() == null) saMarkdowndocPojo.setPublicmark(0);
        if (saMarkdowndocPojo.getReleasemark() == null) saMarkdowndocPojo.setReleasemark(0);
        if (saMarkdowndocPojo.getRownum() == null) saMarkdowndocPojo.setRownum(0);
        if (saMarkdowndocPojo.getContenttype() == null) saMarkdowndocPojo.setContenttype("");
        if (saMarkdowndocPojo.getStorage() == null) saMarkdowndocPojo.setStorage("");
        if (saMarkdowndocPojo.getRelateid() == null) saMarkdowndocPojo.setRelateid("");
        if (saMarkdowndocPojo.getRemark() == null) saMarkdowndocPojo.setRemark("");
        if (saMarkdowndocPojo.getCreateby() == null) saMarkdowndocPojo.setCreateby("");
        if (saMarkdowndocPojo.getCreatebyid() == null) saMarkdowndocPojo.setCreatebyid("");
        if (saMarkdowndocPojo.getCreatedate() == null) saMarkdowndocPojo.setCreatedate(new Date());
        if (saMarkdowndocPojo.getLister() == null) saMarkdowndocPojo.setLister("");
        if (saMarkdowndocPojo.getListerid() == null) saMarkdowndocPojo.setListerid("");
        if (saMarkdowndocPojo.getFilesize() == null) saMarkdowndocPojo.setFilesize(0L);
        if (saMarkdowndocPojo.getModifydate() == null) saMarkdowndocPojo.setModifydate(new Date());
        if (saMarkdowndocPojo.getAssessorid() == null) saMarkdowndocPojo.setAssessorid("");
        if (saMarkdowndocPojo.getAssessor() == null) saMarkdowndocPojo.setAssessor("");
        if (saMarkdowndocPojo.getAssessdate() == null) saMarkdowndocPojo.setAssessdate(new Date());
        if (saMarkdowndocPojo.getDeletemark() == null) saMarkdowndocPojo.setDeletemark(0);
        if (saMarkdowndocPojo.getDeletelister() == null) saMarkdowndocPojo.setDeletelister("");
        if (saMarkdowndocPojo.getDeletelisterid() == null) saMarkdowndocPojo.setDeletelisterid("");
        if (saMarkdowndocPojo.getDeletedate() == null) saMarkdowndocPojo.setDeletedate(new Date());
        if (saMarkdowndocPojo.getCustom1() == null) saMarkdowndocPojo.setCustom1("");
        if (saMarkdowndocPojo.getCustom2() == null) saMarkdowndocPojo.setCustom2("");
        if (saMarkdowndocPojo.getCustom3() == null) saMarkdowndocPojo.setCustom3("");
        if (saMarkdowndocPojo.getCustom4() == null) saMarkdowndocPojo.setCustom4("");
        if (saMarkdowndocPojo.getCustom5() == null) saMarkdowndocPojo.setCustom5("");
        if (saMarkdowndocPojo.getCustom6() == null) saMarkdowndocPojo.setCustom6("");
        if (saMarkdowndocPojo.getCustom7() == null) saMarkdowndocPojo.setCustom7("");
        if (saMarkdowndocPojo.getCustom8() == null) saMarkdowndocPojo.setCustom8("");
        if (saMarkdowndocPojo.getCustom9() == null) saMarkdowndocPojo.setCustom9("");
        if (saMarkdowndocPojo.getCustom10() == null) saMarkdowndocPojo.setCustom10("");
        if (saMarkdowndocPojo.getTenantid() == null) saMarkdowndocPojo.setTenantid("");
        if (saMarkdowndocPojo.getRevision() == null) saMarkdowndocPojo.setRevision(0);
        SaMarkdowndocEntity saMarkdowndocEntity = new SaMarkdowndocEntity();
        BeanUtils.copyProperties(saMarkdowndocPojo, saMarkdowndocEntity);
        //生成雪花id
        saMarkdowndocEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saMarkdowndocEntity.setRevision(1);  //乐观锁
        this.saMarkdowndocMapper.insert(saMarkdowndocEntity);
        return this.getEntity(saMarkdowndocEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saMarkdowndocPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMarkdowndocPojo update(SaMarkdowndocPojo saMarkdowndocPojo) {
        SaMarkdowndocEntity saMarkdowndocEntity = new SaMarkdowndocEntity();
        BeanUtils.copyProperties(saMarkdowndocPojo, saMarkdowndocEntity);
        this.saMarkdowndocMapper.update(saMarkdowndocEntity);
        return this.getEntity(saMarkdowndocEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saMarkdowndocMapper.delete(key);
    }

    /**
     * 审核数据
     *
     * @param saMarkdowndocPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaMarkdowndocPojo approval(SaMarkdowndocPojo saMarkdowndocPojo) {
        //主表更改
        SaMarkdowndocEntity saMarkdowndocEntity = new SaMarkdowndocEntity();
        BeanUtils.copyProperties(saMarkdowndocPojo, saMarkdowndocEntity);
        this.saMarkdowndocMapper.approval(saMarkdowndocEntity);
        //返回Bill实例
        return this.getEntity(saMarkdowndocEntity.getId());
    }

    @Override
    public PageInfo<SaMarkdowndocPojo> getMdByProjectId(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMarkdowndocPojo> lst = saMarkdowndocMapper.getMdByProjectId(queryParam);
            PageInfo<SaMarkdowndocPojo> pageInfo = new PageInfo<SaMarkdowndocPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }
}
