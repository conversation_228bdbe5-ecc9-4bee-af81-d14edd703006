package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMdgroupEntity;
import inks.service.sa.pms.domain.pojo.SaMarkdownPojo;
import inks.service.sa.pms.domain.pojo.SaMdgroupPojo;
import inks.service.sa.pms.mapper.SaMdgroupMapper;
import inks.service.sa.pms.service.SaMdgroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * MD文档分组(SaMdgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-08 11:23:51
 */
@Service("saMdgroupService")
public class SaMdgroupServiceImpl implements SaMdgroupService {
    @Resource
    private SaMdgroupMapper saMdgroupMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaMdgroupPojo getEntity(String key) {
        return this.saMdgroupMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaMdgroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMdgroupPojo> lst = saMdgroupMapper.getPageList(queryParam);
            PageInfo<SaMdgroupPojo> pageInfo = new PageInfo<SaMdgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saMdgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMdgroupPojo insert(SaMdgroupPojo saMdgroupPojo) {
        //初始化NULL字段
        if (saMdgroupPojo.getMdprojectid() == null) saMdgroupPojo.setMdprojectid("");
        if (saMdgroupPojo.getParentid() == null) saMdgroupPojo.setParentid("");
        if (saMdgroupPojo.getModulecode() == null) saMdgroupPojo.setModulecode("");
        if (saMdgroupPojo.getGroupcode() == null) saMdgroupPojo.setGroupcode("");
        if (saMdgroupPojo.getGroupname() == null) saMdgroupPojo.setGroupname("");
        if (saMdgroupPojo.getEnabledmark() == null) saMdgroupPojo.setEnabledmark(0);
        if (saMdgroupPojo.getRownum() == null) saMdgroupPojo.setRownum(0);
        if (saMdgroupPojo.getRemark() == null) saMdgroupPojo.setRemark("");
        if (saMdgroupPojo.getLister() == null) saMdgroupPojo.setLister("");
        if (saMdgroupPojo.getCreatedate() == null) saMdgroupPojo.setCreatedate(new Date());
        if (saMdgroupPojo.getModifydate() == null) saMdgroupPojo.setModifydate(new Date());
        if (saMdgroupPojo.getTenantid() == null) saMdgroupPojo.setTenantid("");
        SaMdgroupEntity saMdgroupEntity = new SaMdgroupEntity();
        BeanUtils.copyProperties(saMdgroupPojo, saMdgroupEntity);
        //生成雪花id
        saMdgroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.saMdgroupMapper.insert(saMdgroupEntity);
        return this.getEntity(saMdgroupEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saMdgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMdgroupPojo update(SaMdgroupPojo saMdgroupPojo) {
        SaMdgroupEntity saMdgroupEntity = new SaMdgroupEntity();
        BeanUtils.copyProperties(saMdgroupPojo, saMdgroupEntity);
        this.saMdgroupMapper.update(saMdgroupEntity);
        return this.getEntity(saMdgroupEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saMdgroupMapper.delete(key);
    }

    @Override
    public List<SaMarkdownPojo> getMarkdownsByGroupId(String key) {
        return this.saMdgroupMapper.getMarkdownsByGroupId(key);
    }
}
