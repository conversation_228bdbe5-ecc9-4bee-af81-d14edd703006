package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMdgroupdocEntity;
import inks.service.sa.pms.domain.pojo.SaMarkdowndocPojo;
import inks.service.sa.pms.domain.pojo.SaMdgroupdocPojo;
import inks.service.sa.pms.mapper.SaMdgroupdocMapper;
import inks.service.sa.pms.service.SaMdgroupdocService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * MarkDown分组(Doc文档)(SaMdgroupdoc)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-17 14:58:40
 */
@Service("saMdgroupdocService")
public class SaMdgroupdocServiceImpl implements SaMdgroupdocService {
    @Resource
    private SaMdgroupdocMapper saMdgroupdocMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaMdgroupdocPojo getEntity(String key) {
        return this.saMdgroupdocMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaMdgroupdocPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMdgroupdocPojo> lst = saMdgroupdocMapper.getPageList(queryParam);
            PageInfo<SaMdgroupdocPojo> pageInfo = new PageInfo<SaMdgroupdocPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saMdgroupdocPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMdgroupdocPojo insert(SaMdgroupdocPojo saMdgroupdocPojo) {
        //初始化NULL字段
        if (saMdgroupdocPojo.getMdprojectid() == null) saMdgroupdocPojo.setMdprojectid("");
        if (saMdgroupdocPojo.getParentid() == null) saMdgroupdocPojo.setParentid("");
        if (saMdgroupdocPojo.getModulecode() == null) saMdgroupdocPojo.setModulecode("");
        if (saMdgroupdocPojo.getGroupcode() == null) saMdgroupdocPojo.setGroupcode("");
        if (saMdgroupdocPojo.getGroupname() == null) saMdgroupdocPojo.setGroupname("");
        if (saMdgroupdocPojo.getEnabledmark() == null) saMdgroupdocPojo.setEnabledmark(0);
        if (saMdgroupdocPojo.getRownum() == null) saMdgroupdocPojo.setRownum(0);
        if (saMdgroupdocPojo.getRemark() == null) saMdgroupdocPojo.setRemark("");
        if (saMdgroupdocPojo.getLister() == null) saMdgroupdocPojo.setLister("");
        if (saMdgroupdocPojo.getCreatedate() == null) saMdgroupdocPojo.setCreatedate(new Date());
        if (saMdgroupdocPojo.getModifydate() == null) saMdgroupdocPojo.setModifydate(new Date());
        if (saMdgroupdocPojo.getTenantid() == null) saMdgroupdocPojo.setTenantid("");
        SaMdgroupdocEntity saMdgroupdocEntity = new SaMdgroupdocEntity();
        BeanUtils.copyProperties(saMdgroupdocPojo, saMdgroupdocEntity);
        //生成雪花id
        saMdgroupdocEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        this.saMdgroupdocMapper.insert(saMdgroupdocEntity);
        return this.getEntity(saMdgroupdocEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saMdgroupdocPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMdgroupdocPojo update(SaMdgroupdocPojo saMdgroupdocPojo) {
        SaMdgroupdocEntity saMdgroupdocEntity = new SaMdgroupdocEntity();
        BeanUtils.copyProperties(saMdgroupdocPojo, saMdgroupdocEntity);
        this.saMdgroupdocMapper.update(saMdgroupdocEntity);
        return this.getEntity(saMdgroupdocEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saMdgroupdocMapper.delete(key);
    }

    @Override
    public List<SaMarkdowndocPojo> getMarkdownsByGroupId(String key) {
        return this.saMdgroupdocMapper.getMarkdownsByGroupId(key);
    }
}
