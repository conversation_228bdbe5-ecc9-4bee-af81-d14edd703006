package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMdprojectEntity;
import inks.service.sa.pms.domain.pojo.SaMdgroupPojo;
import inks.service.sa.pms.domain.pojo.SaMdprojectPojo;
import inks.service.sa.pms.domain.pojo.SaMdprojectuserPojo;
import inks.service.sa.pms.domain.pojo.SaUserPojo;
import inks.service.sa.pms.mapper.SaMdprojectMapper;
import inks.service.sa.pms.mapper.SaMdprojectuserMapper;
import inks.service.sa.pms.mapper.pmsSaUserMapper;
import inks.service.sa.pms.service.SaMdprojectService;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * MD文档项目(SaMdproject)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-08 13:29:08
 */
@Service("saMdprojectService")
public class SaMdprojectServiceImpl implements SaMdprojectService {
    @Resource
    private SaMdprojectMapper saMdprojectMapper;
    @Resource
    private SaMdprojectuserMapper saMdprojectuserMapper;
    @Resource
    private pmsSaUserMapper pmsSaUserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaMdprojectPojo getEntity(String key) {
        SaMdprojectPojo saMdprojectPojo = saMdprojectMapper.getEntity(key);
        List<SaMdprojectuserPojo> list = saMdprojectuserMapper.getListByProjectId(key);
        //userPojoList:需要拿到关联的用户信息List（加一个用户-MD项目关联表的id）
        List<SaUserPojo> userPojoList = list.stream().map(item -> {
            SaUserPojo saUserPojo = pmsSaUserMapper.getEntity(item.getUserid());
            saUserPojo.setUserprojectid(item.getId());
            return saUserPojo;
        }).collect(Collectors.toList());
        saMdprojectPojo.setUserList(userPojoList);
        return saMdprojectPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaMdprojectPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMdprojectPojo> lst = saMdprojectMapper.getPageList(queryParam);
            PageInfo<SaMdprojectPojo> pageInfo = new PageInfo<SaMdprojectPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saMdprojectPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMdprojectPojo insert(SaMdprojectPojo saMdprojectPojo) {
        //初始化NULL字段
        if (saMdprojectPojo.getProname() == null) saMdprojectPojo.setProname("");
        if (saMdprojectPojo.getProjcode() == null) saMdprojectPojo.setProjcode("");
        if (saMdprojectPojo.getProjtype() == null) saMdprojectPojo.setProjtype("");
        if (saMdprojectPojo.getProjpinyin() == null) saMdprojectPojo.setProjpinyin("");
        if (saMdprojectPojo.getPublicmark() == null) saMdprojectPojo.setPublicmark(0);
        if (saMdprojectPojo.getEnabledmark() == null) saMdprojectPojo.setEnabledmark(0);
        if (saMdprojectPojo.getCoverimage() == null) saMdprojectPojo.setCoverimage("");
        if (saMdprojectPojo.getRownum() == null) saMdprojectPojo.setRownum(0);
        if (saMdprojectPojo.getRemark() == null) saMdprojectPojo.setRemark("");
        if (saMdprojectPojo.getCreateby() == null) saMdprojectPojo.setCreateby("");
        if (saMdprojectPojo.getCreatebyid() == null) saMdprojectPojo.setCreatebyid("");
        if (saMdprojectPojo.getCreatedate() == null) saMdprojectPojo.setCreatedate(new Date());
        if (saMdprojectPojo.getLister() == null) saMdprojectPojo.setLister("");
        if (saMdprojectPojo.getListerid() == null) saMdprojectPojo.setListerid("");
        if (saMdprojectPojo.getModifydate() == null) saMdprojectPojo.setModifydate(new Date());
        if (saMdprojectPojo.getCustom1() == null) saMdprojectPojo.setCustom1("");
        if (saMdprojectPojo.getCustom2() == null) saMdprojectPojo.setCustom2("");
        if (saMdprojectPojo.getCustom3() == null) saMdprojectPojo.setCustom3("");
        if (saMdprojectPojo.getCustom4() == null) saMdprojectPojo.setCustom4("");
        if (saMdprojectPojo.getCustom5() == null) saMdprojectPojo.setCustom5("");
        if (saMdprojectPojo.getTenantid() == null) saMdprojectPojo.setTenantid("");
        if (saMdprojectPojo.getRevision() == null) saMdprojectPojo.setRevision(0);
        SaMdprojectEntity saMdprojectEntity = new SaMdprojectEntity();
        BeanUtils.copyProperties(saMdprojectPojo, saMdprojectEntity);
        //生成雪花id
        saMdprojectEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saMdprojectEntity.setRevision(1);  //乐观锁
        this.saMdprojectMapper.insert(saMdprojectEntity);
        return this.getEntity(saMdprojectEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saMdprojectPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMdprojectPojo update(SaMdprojectPojo saMdprojectPojo) {
        SaMdprojectEntity saMdprojectEntity = new SaMdprojectEntity();
        BeanUtils.copyProperties(saMdprojectPojo, saMdprojectEntity);
        this.saMdprojectMapper.update(saMdprojectEntity);
        return this.getEntity(saMdprojectEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saMdprojectMapper.delete(key);
    }

    @Override
    public List<SaMdgroupPojo> getMdgroupsByProjectId(String key) {
        return this.saMdprojectMapper.getMdgroupsByProjectId(key);
    }
}
