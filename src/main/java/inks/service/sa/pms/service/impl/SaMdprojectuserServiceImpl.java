package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMdprojectuserEntity;
import inks.service.sa.pms.domain.pojo.SaMdprojectuserPojo;
import inks.service.sa.pms.mapper.SaMdprojectuserMapper;
import inks.service.sa.pms.service.SaMdprojectuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (用户-MD项目)可视权限(SaMdprojectuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-08 13:07:40
 */
@Service("saMdprojectuserService")
public class SaMdprojectuserServiceImpl implements SaMdprojectuserService {
    @Resource
    private SaMdprojectuserMapper saMdprojectuserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaMdprojectuserPojo getEntity(String key) {
        return this.saMdprojectuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaMdprojectuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMdprojectuserPojo> lst = saMdprojectuserMapper.getPageList(queryParam);
            PageInfo<SaMdprojectuserPojo> pageInfo = new PageInfo<SaMdprojectuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saMdprojectuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMdprojectuserPojo insert(SaMdprojectuserPojo saMdprojectuserPojo) {
        //初始化NULL字段
        if (saMdprojectuserPojo.getUserid() == null) saMdprojectuserPojo.setUserid("");
        if (saMdprojectuserPojo.getMdprojectid() == null) saMdprojectuserPojo.setMdprojectid("");
        if (saMdprojectuserPojo.getRownum() == null) saMdprojectuserPojo.setRownum(0);
        if (saMdprojectuserPojo.getRemark() == null) saMdprojectuserPojo.setRemark("");
        if (saMdprojectuserPojo.getCreateby() == null) saMdprojectuserPojo.setCreateby("");
        if (saMdprojectuserPojo.getCreatebyid() == null) saMdprojectuserPojo.setCreatebyid("");
        if (saMdprojectuserPojo.getCreatedate() == null) saMdprojectuserPojo.setCreatedate(new Date());
        if (saMdprojectuserPojo.getLister() == null) saMdprojectuserPojo.setLister("");
        if (saMdprojectuserPojo.getListerid() == null) saMdprojectuserPojo.setListerid("");
        if (saMdprojectuserPojo.getModifydate() == null) saMdprojectuserPojo.setModifydate(new Date());
        if (saMdprojectuserPojo.getCustom1() == null) saMdprojectuserPojo.setCustom1("");
        if (saMdprojectuserPojo.getCustom2() == null) saMdprojectuserPojo.setCustom2("");
        if (saMdprojectuserPojo.getCustom3() == null) saMdprojectuserPojo.setCustom3("");
        if (saMdprojectuserPojo.getCustom4() == null) saMdprojectuserPojo.setCustom4("");
        if (saMdprojectuserPojo.getCustom5() == null) saMdprojectuserPojo.setCustom5("");
        if (saMdprojectuserPojo.getTenantid() == null) saMdprojectuserPojo.setTenantid("");
        if (saMdprojectuserPojo.getTenantname() == null) saMdprojectuserPojo.setTenantname("");
        if (saMdprojectuserPojo.getRevision() == null) saMdprojectuserPojo.setRevision(0);
        SaMdprojectuserEntity saMdprojectuserEntity = new SaMdprojectuserEntity();
        BeanUtils.copyProperties(saMdprojectuserPojo, saMdprojectuserEntity);
        //生成雪花id
        saMdprojectuserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saMdprojectuserEntity.setRevision(1);  //乐观锁
        this.saMdprojectuserMapper.insert(saMdprojectuserEntity);
        return this.getEntity(saMdprojectuserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saMdprojectuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMdprojectuserPojo update(SaMdprojectuserPojo saMdprojectuserPojo) {
        SaMdprojectuserEntity saMdprojectuserEntity = new SaMdprojectuserEntity();
        BeanUtils.copyProperties(saMdprojectuserPojo, saMdprojectuserEntity);
        this.saMdprojectuserMapper.update(saMdprojectuserEntity);
        return this.getEntity(saMdprojectuserEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saMdprojectuserMapper.delete(key);
    }


}
