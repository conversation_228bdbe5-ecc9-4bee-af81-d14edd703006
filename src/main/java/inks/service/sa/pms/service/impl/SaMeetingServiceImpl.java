package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMeetingEntity;
import inks.service.sa.pms.domain.SaMeetingitemEntity;
import inks.service.sa.pms.domain.SaMeetinguserEntity;
import inks.service.sa.pms.domain.pojo.SaMeetingPojo;
import inks.service.sa.pms.domain.pojo.SaMeetingitemPojo;
import inks.service.sa.pms.domain.pojo.SaMeetingitemdetailPojo;
import inks.service.sa.pms.domain.pojo.SaMeetinguserPojo;
import inks.service.sa.pms.mapper.SaMeetingMapper;
import inks.service.sa.pms.mapper.SaMeetingitemMapper;
import inks.service.sa.pms.mapper.SaMeetinguserMapper;
import inks.service.sa.pms.service.SaMeetingService;
import inks.service.sa.pms.service.SaMeetingitemService;
import inks.service.sa.pms.service.SaMeetinguserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 会议主表(SaMeeting)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:52:47
 */
@Service("saMeetingService")
public class SaMeetingServiceImpl implements SaMeetingService {
    @Resource
    private SaMeetingMapper saMeetingMapper;

    @Resource
    private SaMeetingitemMapper saMeetingitemMapper;
    @Resource
    private SaMeetinguserMapper saMeetinguserMapper;

    @Resource
    private SaMeetingitemService saMeetingitemService;
    @Resource
    private SaMeetinguserService saMeetinguserService;

    @Override
    public SaMeetingPojo getEntity(String key) {
        return this.saMeetingMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaMeetingitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMeetingitemdetailPojo> lst = saMeetingMapper.getPageList(queryParam);
            PageInfo<SaMeetingitemdetailPojo> pageInfo = new PageInfo<SaMeetingitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaMeetingPojo getBillEntity(String key) {
        try {
            //读取主表
            SaMeetingPojo saMeetingPojo = this.saMeetingMapper.getEntity(key);
            //读取子表
            saMeetingPojo.setItem(saMeetingitemMapper.getList(saMeetingPojo.getId()));
            //读取user子表
            saMeetingPojo.setUser(saMeetinguserMapper.getList(saMeetingPojo.getId()));

            return saMeetingPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaMeetingPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMeetingPojo> lst = saMeetingMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表、user子表
            for (SaMeetingPojo saMeetingPojo : lst) {
                saMeetingPojo.setItem(saMeetingitemMapper.getList(saMeetingPojo.getId()));
                saMeetingPojo.setUser(saMeetinguserMapper.getList(saMeetingPojo.getId()));
            }
            PageInfo<SaMeetingPojo> pageInfo = new PageInfo<SaMeetingPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public PageInfo<SaMeetingPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMeetingPojo> lst = saMeetingMapper.getPageTh(queryParam);
            PageInfo<SaMeetingPojo> pageInfo = new PageInfo<SaMeetingPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    @Transactional
    public SaMeetingPojo insert(SaMeetingPojo saMeetingPojo) {
        //初始化NULL字段
        cleanNull(saMeetingPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaMeetingEntity saMeetingEntity = new SaMeetingEntity();
        BeanUtils.copyProperties(saMeetingPojo, saMeetingEntity);
        //设置id和新建日期
        saMeetingEntity.setId(id);
        saMeetingEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saMeetingMapper.insert(saMeetingEntity);
        //Item子表处理
        List<SaMeetingitemPojo> lst = saMeetingPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaMeetingitemPojo saMeetingitemPojo : lst) {
                //初始化item的NULL
                SaMeetingitemPojo itemPojo = this.saMeetingitemService.clearNull(saMeetingitemPojo);
                SaMeetingitemEntity saMeetingitemEntity = new SaMeetingitemEntity();
                BeanUtils.copyProperties(itemPojo, saMeetingitemEntity);
                //设置id和Pid
                saMeetingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saMeetingitemEntity.setPid(id);
                saMeetingitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saMeetingitemMapper.insert(saMeetingitemEntity);
            }
        }
        // user子表处理
        List<SaMeetinguserPojo> lstUser = saMeetingPojo.getUser();
        if (lstUser != null) {
            //循环每个user子表
            for (SaMeetinguserPojo saMeetinguserPojo : lstUser) {
                //初始化user的NULL
                SaMeetinguserPojo userPojo = this.saMeetinguserService.clearNull(saMeetinguserPojo);
                SaMeetinguserEntity saMeetinguserEntity = new SaMeetinguserEntity();
                BeanUtils.copyProperties(userPojo, saMeetinguserEntity);
                //设置id和Pid
                saMeetinguserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saMeetinguserEntity.setPid(id);
                saMeetinguserEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saMeetinguserMapper.insert(saMeetinguserEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(saMeetingEntity.getId());
    }


    @Override
    @Transactional
    public SaMeetingPojo update(SaMeetingPojo saMeetingPojo) {
        //主表更改
        SaMeetingEntity saMeetingEntity = new SaMeetingEntity();
        BeanUtils.copyProperties(saMeetingPojo, saMeetingEntity);
        this.saMeetingMapper.update(saMeetingEntity);
        //Item子表处理
        if (saMeetingPojo.getItem() != null) {
            List<SaMeetingitemPojo> lst = saMeetingPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saMeetingMapper.getDelItemIds(saMeetingPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.saMeetingitemMapper.delete(lstDelId);
                }
            }
            //循环每个item子表
            for (SaMeetingitemPojo saMeetingitemPojo : lst) {
                SaMeetingitemEntity saMeetingitemEntity = new SaMeetingitemEntity();
                if ("".equals(saMeetingitemPojo.getId()) || saMeetingitemPojo.getId() == null) {
                    //初始化item的NULL
                    SaMeetingitemPojo itemPojo = this.saMeetingitemService.clearNull(saMeetingitemPojo);
                    BeanUtils.copyProperties(itemPojo, saMeetingitemEntity);
                    //设置id和Pid
                    saMeetingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                    saMeetingitemEntity.setPid(saMeetingEntity.getId());  // 主表 id
                    saMeetingitemEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.saMeetingitemMapper.insert(saMeetingitemEntity);
                } else {
                    BeanUtils.copyProperties(saMeetingitemPojo, saMeetingitemEntity);
                    this.saMeetingitemMapper.update(saMeetingitemEntity);
                }
            }
        }
        //user子表处理
        if (saMeetingPojo.getUser() != null) {
            List<SaMeetinguserPojo> lst = saMeetingPojo.getUser();
            //获取被删除的user
            List<String> lstDelIds = saMeetingMapper.getDelUserIds(saMeetingPojo);
            if (lstDelIds != null) {
                //循环每个删除user子表
                for (String lstDelId : lstDelIds) {
                    this.saMeetinguserMapper.delete(lstDelId);
                }
            }
            //循环每个user子表
            for (SaMeetinguserPojo saMeetinguserPojo : lst) {
                SaMeetinguserEntity saMeetinguserEntity = new SaMeetinguserEntity();
                if ("".equals(saMeetinguserPojo.getId()) || saMeetinguserPojo.getId() == null) {
                    //初始化user的NULL
                    SaMeetinguserPojo userPojo = this.saMeetinguserService.clearNull(saMeetinguserPojo);
                    BeanUtils.copyProperties(userPojo, saMeetinguserEntity);
                    //设置id和Pid
                    saMeetinguserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // user id
                    saMeetinguserEntity.setPid(saMeetingEntity.getId());  // 主表 id
                    saMeetinguserEntity.setRevision(1);  // 乐观锁
                    //插入子表
                    this.saMeetinguserMapper.insert(saMeetinguserEntity);
                } else {
                    BeanUtils.copyProperties(saMeetinguserPojo, saMeetinguserEntity);
                    this.saMeetinguserMapper.update(saMeetinguserEntity);
                }
            }
        }

        //返回Bill实例
        return this.getBillEntity(saMeetingEntity.getId());
    }


    @Override
    @Transactional
    public int delete(String key) {
        SaMeetingPojo saMeetingPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaMeetingitemPojo> lst = saMeetingPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.saMeetingitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saMeetingMapper.delete(key);
    }


    @Override
    @Transactional
    public SaMeetingPojo approval(SaMeetingPojo saMeetingPojo) {
        //主表更改
        SaMeetingEntity saMeetingEntity = new SaMeetingEntity();
        BeanUtils.copyProperties(saMeetingPojo, saMeetingEntity);
        this.saMeetingMapper.approval(saMeetingEntity);
        //返回Bill实例
        return this.getBillEntity(saMeetingEntity.getId());
    }

    private static void cleanNull(SaMeetingPojo saMeetingPojo) {
        if (saMeetingPojo.getMeetingtitle() == null) saMeetingPojo.setMeetingtitle("");
        if (saMeetingPojo.getMeetingdesc() == null) saMeetingPojo.setMeetingdesc("");
        if (saMeetingPojo.getStatus() == null) saMeetingPojo.setStatus(0);
        //if (saMeetingPojo.getStarttime() == null) saMeetingPojo.setStarttime(new Date());
        //if (saMeetingPojo.getEndtime() == null) saMeetingPojo.setEndtime(new Date());
        if (saMeetingPojo.getLocation() == null) saMeetingPojo.setLocation("");
        if (saMeetingPojo.getAttendeecount() == null) saMeetingPojo.setAttendeecount(0);
        if (saMeetingPojo.getActattendeecount() == null) saMeetingPojo.setActattendeecount(0);
        if (saMeetingPojo.getAssessor() == null) saMeetingPojo.setAssessor("");
        if (saMeetingPojo.getAssessorid() == null) saMeetingPojo.setAssessorid("");
        if (saMeetingPojo.getAssessdate() == null) saMeetingPojo.setAssessdate(new Date());
        if (saMeetingPojo.getAssesscomment() == null) saMeetingPojo.setAssesscomment("");
        if (saMeetingPojo.getItemcount() == null) saMeetingPojo.setItemcount(0);
        if (saMeetingPojo.getRownum() == null) saMeetingPojo.setRownum(0);
        if (saMeetingPojo.getRemark() == null) saMeetingPojo.setRemark("");
        if (saMeetingPojo.getCreateby() == null) saMeetingPojo.setCreateby("");
        if (saMeetingPojo.getCreatebyid() == null) saMeetingPojo.setCreatebyid("");
        if (saMeetingPojo.getCreatedate() == null) saMeetingPojo.setCreatedate(new Date());
        if (saMeetingPojo.getLister() == null) saMeetingPojo.setLister("");
        if (saMeetingPojo.getListerid() == null) saMeetingPojo.setListerid("");
        if (saMeetingPojo.getModifydate() == null) saMeetingPojo.setModifydate(new Date());
        if (saMeetingPojo.getCustom1() == null) saMeetingPojo.setCustom1("");
        if (saMeetingPojo.getCustom2() == null) saMeetingPojo.setCustom2("");
        if (saMeetingPojo.getCustom3() == null) saMeetingPojo.setCustom3("");
        if (saMeetingPojo.getCustom4() == null) saMeetingPojo.setCustom4("");
        if (saMeetingPojo.getCustom5() == null) saMeetingPojo.setCustom5("");
        if (saMeetingPojo.getTenantid() == null) saMeetingPojo.setTenantid("");
        if (saMeetingPojo.getRevision() == null) saMeetingPojo.setRevision(0);
    }

}
