package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaMeetingitemPojo;
import inks.service.sa.pms.domain.SaMeetingitemEntity;
import inks.service.sa.pms.mapper.SaMeetingitemMapper;
import inks.service.sa.pms.service.SaMeetingitemService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 会议子表-议题(SaMeetingitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:50:16
 */
@Service("saMeetingitemService")
public class SaMeetingitemServiceImpl implements SaMeetingitemService {
    @Resource
    private SaMeetingitemMapper saMeetingitemMapper;

    @Override
    public SaMeetingitemPojo getEntity(String key) {
        return this.saMeetingitemMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaMeetingitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMeetingitemPojo> lst = saMeetingitemMapper.getPageList(queryParam);
            PageInfo<SaMeetingitemPojo> pageInfo = new PageInfo<SaMeetingitemPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaMeetingitemPojo> getList(String Pid) { 
        try {
            List<SaMeetingitemPojo> lst = saMeetingitemMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaMeetingitemPojo insert(SaMeetingitemPojo saMeetingitemPojo) {
        //初始化item的NULL
        SaMeetingitemPojo itempojo =this.clearNull(saMeetingitemPojo);
        SaMeetingitemEntity saMeetingitemEntity = new SaMeetingitemEntity(); 
        BeanUtils.copyProperties(itempojo,saMeetingitemEntity);
         //生成雪花id
          saMeetingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saMeetingitemEntity.setRevision(1);  //乐观锁      
          this.saMeetingitemMapper.insert(saMeetingitemEntity);
        return this.getEntity(saMeetingitemEntity.getId());
  
    }

    @Override
    public SaMeetingitemPojo update(SaMeetingitemPojo saMeetingitemPojo) {
        SaMeetingitemEntity saMeetingitemEntity = new SaMeetingitemEntity(); 
        BeanUtils.copyProperties(saMeetingitemPojo,saMeetingitemEntity);
        this.saMeetingitemMapper.update(saMeetingitemEntity);
        return this.getEntity(saMeetingitemEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saMeetingitemMapper.delete(key) ;
    }

     @Override
     public SaMeetingitemPojo clearNull(SaMeetingitemPojo saMeetingitemPojo){
     //初始化NULL字段
     if(saMeetingitemPojo.getPid()==null) saMeetingitemPojo.setPid("");
     if(saMeetingitemPojo.getTopictitle()==null) saMeetingitemPojo.setTopictitle("");
     if(saMeetingitemPojo.getTopiccontent()==null) saMeetingitemPojo.setTopiccontent("");
     if(saMeetingitemPojo.getTopicresult()==null) saMeetingitemPojo.setTopicresult("");
     if(saMeetingitemPojo.getPrincipal()==null) saMeetingitemPojo.setPrincipal("");
     if(saMeetingitemPojo.getPrincipalid()==null) saMeetingitemPojo.setPrincipalid("");
     //if(saMeetingitemPojo.getDeadlinedate()==null) saMeetingitemPojo.setDeadlinedate(new Date());
     if(saMeetingitemPojo.getPriority()==null) saMeetingitemPojo.setPriority(0);
     if(saMeetingitemPojo.getTopicstatus()==null) saMeetingitemPojo.setTopicstatus(0);
     if(saMeetingitemPojo.getDuration()==null) saMeetingitemPojo.setDuration(0);
     if(saMeetingitemPojo.getRownum()==null) saMeetingitemPojo.setRownum(0);
     if(saMeetingitemPojo.getRemark()==null) saMeetingitemPojo.setRemark("");
     if(saMeetingitemPojo.getCustom1()==null) saMeetingitemPojo.setCustom1("");
     if(saMeetingitemPojo.getCustom2()==null) saMeetingitemPojo.setCustom2("");
     if(saMeetingitemPojo.getCustom3()==null) saMeetingitemPojo.setCustom3("");
     if(saMeetingitemPojo.getCustom4()==null) saMeetingitemPojo.setCustom4("");
     if(saMeetingitemPojo.getCustom5()==null) saMeetingitemPojo.setCustom5("");
     if(saMeetingitemPojo.getTenantid()==null) saMeetingitemPojo.setTenantid("");
     if(saMeetingitemPojo.getRevision()==null) saMeetingitemPojo.setRevision(0);
     return saMeetingitemPojo;
     }
}
