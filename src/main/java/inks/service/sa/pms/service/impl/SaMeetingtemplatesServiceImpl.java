package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaMeetingtemplatesPojo;
import inks.service.sa.pms.domain.SaMeetingtemplatesEntity;
import inks.service.sa.pms.mapper.SaMeetingtemplatesMapper;
import inks.service.sa.pms.service.SaMeetingtemplatesService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * 会议模板表(SaMeetingtemplates)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:46:22
 */
@Service("saMeetingtemplatesService")
public class SaMeetingtemplatesServiceImpl implements SaMeetingtemplatesService {
    @Resource
    private SaMeetingtemplatesMapper saMeetingtemplatesMapper;

    @Override
    public SaMeetingtemplatesPojo getEntity(String key) {
        return this.saMeetingtemplatesMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaMeetingtemplatesPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMeetingtemplatesPojo> lst = saMeetingtemplatesMapper.getPageList(queryParam);
            PageInfo<SaMeetingtemplatesPojo> pageInfo = new PageInfo<SaMeetingtemplatesPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaMeetingtemplatesPojo insert(SaMeetingtemplatesPojo saMeetingtemplatesPojo) {
        //初始化NULL字段
        cleanNull(saMeetingtemplatesPojo);
        SaMeetingtemplatesEntity saMeetingtemplatesEntity = new SaMeetingtemplatesEntity(); 
        BeanUtils.copyProperties(saMeetingtemplatesPojo,saMeetingtemplatesEntity);
        //生成雪花id
          saMeetingtemplatesEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saMeetingtemplatesEntity.setRevision(1);  //乐观锁
          this.saMeetingtemplatesMapper.insert(saMeetingtemplatesEntity);
        return this.getEntity(saMeetingtemplatesEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saMeetingtemplatesPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMeetingtemplatesPojo update(SaMeetingtemplatesPojo saMeetingtemplatesPojo) {
        SaMeetingtemplatesEntity saMeetingtemplatesEntity = new SaMeetingtemplatesEntity(); 
        BeanUtils.copyProperties(saMeetingtemplatesPojo,saMeetingtemplatesEntity);
        this.saMeetingtemplatesMapper.update(saMeetingtemplatesEntity);
        return this.getEntity(saMeetingtemplatesEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saMeetingtemplatesMapper.delete(key) ;
    }
    

    private static void cleanNull(SaMeetingtemplatesPojo saMeetingtemplatesPojo) {
        if(saMeetingtemplatesPojo.getTemplatename()==null) saMeetingtemplatesPojo.setTemplatename("");
        if(saMeetingtemplatesPojo.getTemplatetype()==null) saMeetingtemplatesPojo.setTemplatetype(0);
        if(saMeetingtemplatesPojo.getDescription()==null) saMeetingtemplatesPojo.setDescription("");
        if(saMeetingtemplatesPojo.getDeftduration()==null) saMeetingtemplatesPojo.setDeftduration(0);
        if(saMeetingtemplatesPojo.getDeftlocation()==null) saMeetingtemplatesPojo.setDeftlocation("");
        if(saMeetingtemplatesPojo.getTopictemplate()==null) saMeetingtemplatesPojo.setTopictemplate("");
        if(saMeetingtemplatesPojo.getParttemplate()==null) saMeetingtemplatesPojo.setParttemplate("");
        if(saMeetingtemplatesPojo.getIspublic()==null) saMeetingtemplatesPojo.setIspublic(0);
        if(saMeetingtemplatesPojo.getUsagecount()==null) saMeetingtemplatesPojo.setUsagecount(0);
        if(saMeetingtemplatesPojo.getIsactive()==null) saMeetingtemplatesPojo.setIsactive(0);
        if(saMeetingtemplatesPojo.getRownum()==null) saMeetingtemplatesPojo.setRownum(0);
        if(saMeetingtemplatesPojo.getRemark()==null) saMeetingtemplatesPojo.setRemark("");
        if(saMeetingtemplatesPojo.getCreateby()==null) saMeetingtemplatesPojo.setCreateby("");
        if(saMeetingtemplatesPojo.getCreatebyid()==null) saMeetingtemplatesPojo.setCreatebyid("");
        if(saMeetingtemplatesPojo.getCreatedate()==null) saMeetingtemplatesPojo.setCreatedate(new Date());
        if(saMeetingtemplatesPojo.getLister()==null) saMeetingtemplatesPojo.setLister("");
        if(saMeetingtemplatesPojo.getListerid()==null) saMeetingtemplatesPojo.setListerid("");
        if(saMeetingtemplatesPojo.getModifydate()==null) saMeetingtemplatesPojo.setModifydate(new Date());
        if(saMeetingtemplatesPojo.getCustom1()==null) saMeetingtemplatesPojo.setCustom1("");
        if(saMeetingtemplatesPojo.getCustom2()==null) saMeetingtemplatesPojo.setCustom2("");
        if(saMeetingtemplatesPojo.getCustom3()==null) saMeetingtemplatesPojo.setCustom3("");
        if(saMeetingtemplatesPojo.getCustom4()==null) saMeetingtemplatesPojo.setCustom4("");
        if(saMeetingtemplatesPojo.getCustom5()==null) saMeetingtemplatesPojo.setCustom5("");
        if(saMeetingtemplatesPojo.getTenantid()==null) saMeetingtemplatesPojo.setTenantid("");
        if(saMeetingtemplatesPojo.getRevision()==null) saMeetingtemplatesPojo.setRevision(0);
   }

}
