package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaMeetinguserPojo;
import inks.service.sa.pms.domain.SaMeetinguserEntity;
import inks.service.sa.pms.mapper.SaMeetinguserMapper;
import inks.service.sa.pms.service.SaMeetinguserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import inks.common.core.text.inksSnowflake;
/**
 * 会议子表-人员(SaMeetinguser)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-06-17 09:50:18
 */
@Service("saMeetinguserService")
public class SaMeetinguserServiceImpl implements SaMeetinguserService {
    @Resource
    private SaMeetinguserMapper saMeetinguserMapper;

    @Override
    public SaMeetinguserPojo getEntity(String key) {
        return this.saMeetinguserMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaMeetinguserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMeetinguserPojo> lst = saMeetinguserMapper.getPageList(queryParam);
            PageInfo<SaMeetinguserPojo> pageInfo = new PageInfo<SaMeetinguserPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }

    @Override
    public List<SaMeetinguserPojo> getList(String Pid) { 
        try {
            List<SaMeetinguserPojo> lst = saMeetinguserMapper.getList(Pid);
            return lst;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }      

    @Override
    public SaMeetinguserPojo insert(SaMeetinguserPojo saMeetinguserPojo) {
        //初始化item的NULL
        SaMeetinguserPojo itempojo =this.clearNull(saMeetinguserPojo);
        SaMeetinguserEntity saMeetinguserEntity = new SaMeetinguserEntity(); 
        BeanUtils.copyProperties(itempojo,saMeetinguserEntity);
         //生成雪花id
          saMeetinguserEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saMeetinguserEntity.setRevision(1);  //乐观锁      
          this.saMeetinguserMapper.insert(saMeetinguserEntity);
        return this.getEntity(saMeetinguserEntity.getId());
  
    }

    @Override
    public SaMeetinguserPojo update(SaMeetinguserPojo saMeetinguserPojo) {
        SaMeetinguserEntity saMeetinguserEntity = new SaMeetinguserEntity(); 
        BeanUtils.copyProperties(saMeetinguserPojo,saMeetinguserEntity);
        this.saMeetinguserMapper.update(saMeetinguserEntity);
        return this.getEntity(saMeetinguserEntity.getId());
    }

    @Override
    public int delete(String key) {
        return this.saMeetinguserMapper.delete(key) ;
    }

     @Override
     public SaMeetinguserPojo clearNull(SaMeetinguserPojo saMeetinguserPojo){
     //初始化NULL字段
     if(saMeetinguserPojo.getPid()==null) saMeetinguserPojo.setPid("");
     if(saMeetinguserPojo.getUserid()==null) saMeetinguserPojo.setUserid("");
     if(saMeetinguserPojo.getUsername()==null) saMeetinguserPojo.setUsername("");
     if(saMeetinguserPojo.getUserdept()==null) saMeetinguserPojo.setUserdept("");
     if(saMeetinguserPojo.getUserposition()==null) saMeetinguserPojo.setUserposition("");
     if(saMeetinguserPojo.getParticipanttype()==null) saMeetinguserPojo.setParticipanttype(0);
     if(saMeetinguserPojo.getActualattendance()==null) saMeetinguserPojo.setActualattendance(0);
     //if(saMeetinguserPojo.getAttendancetime()==null) saMeetinguserPojo.setAttendancetime(new Date());
     //if(saMeetinguserPojo.getLeavetime()==null) saMeetinguserPojo.setLeavetime(new Date());
     if(saMeetinguserPojo.getPhone()==null) saMeetinguserPojo.setPhone("");
     if(saMeetinguserPojo.getEmail()==null) saMeetinguserPojo.setEmail("");
     if(saMeetinguserPojo.getInvitestatus()==null) saMeetinguserPojo.setInvitestatus(0);
     if(saMeetinguserPojo.getRownum()==null) saMeetinguserPojo.setRownum(0);
     if(saMeetinguserPojo.getRemark()==null) saMeetinguserPojo.setRemark("");
     if(saMeetinguserPojo.getCustom1()==null) saMeetinguserPojo.setCustom1("");
     if(saMeetinguserPojo.getCustom2()==null) saMeetinguserPojo.setCustom2("");
     if(saMeetinguserPojo.getCustom3()==null) saMeetinguserPojo.setCustom3("");
     if(saMeetinguserPojo.getCustom4()==null) saMeetinguserPojo.setCustom4("");
     if(saMeetinguserPojo.getCustom5()==null) saMeetinguserPojo.setCustom5("");
     if(saMeetinguserPojo.getTenantid()==null) saMeetinguserPojo.setTenantid("");
     if(saMeetinguserPojo.getRevision()==null) saMeetinguserPojo.setRevision(0);
     return saMeetinguserPojo;
     }
}
