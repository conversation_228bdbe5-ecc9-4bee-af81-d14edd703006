package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMenufrmEntity;
import inks.service.sa.pms.domain.pojo.SaMenufrmPojo;
import inks.service.sa.pms.mapper.SaMenufrmMapper;
import inks.service.sa.pms.service.SaMenufrmService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * Frm导航(SaMenufrm)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-05-14 09:00:49
 */
@Service("saMenufrmService")
public class SaMenufrmServiceImpl implements SaMenufrmService {
    @Resource
    private SaMenufrmMapper saMenufrmMapper;

    private static void cleanNull(SaMenufrmPojo saMenufrmPojo) {
        if (saMenufrmPojo.getNavpid() == null) saMenufrmPojo.setNavpid("");
        if (saMenufrmPojo.getNavtype() == null) saMenufrmPojo.setNavtype("");
        if (saMenufrmPojo.getNavcode() == null) saMenufrmPojo.setNavcode("");
        if (saMenufrmPojo.getNavname() == null) saMenufrmPojo.setNavname("");
        if (saMenufrmPojo.getNavgroup() == null) saMenufrmPojo.setNavgroup("");
        if (saMenufrmPojo.getRownum() == null) saMenufrmPojo.setRownum(0);
        if (saMenufrmPojo.getAssemblyname() == null) saMenufrmPojo.setAssemblyname("");
        if (saMenufrmPojo.getFormname() == null) saMenufrmPojo.setFormname("");
        if (saMenufrmPojo.getImagecss() == null) saMenufrmPojo.setImagecss("");
        if (saMenufrmPojo.getModuletype() == null) saMenufrmPojo.setModuletype("");
        if (saMenufrmPojo.getModulecode() == null) saMenufrmPojo.setModulecode("");
        if (saMenufrmPojo.getRolecode() == null) saMenufrmPojo.setRolecode("");
        if (saMenufrmPojo.getImageindex() == null) saMenufrmPojo.setImageindex("");
        if (saMenufrmPojo.getImagestyle() == null) saMenufrmPojo.setImagestyle("");
        if (saMenufrmPojo.getEnabledmark() == null) saMenufrmPojo.setEnabledmark(0);
        if (saMenufrmPojo.getIconurl() == null) saMenufrmPojo.setIconurl("");
        if (saMenufrmPojo.getNavigateurl() == null) saMenufrmPojo.setNavigateurl("");
        if (saMenufrmPojo.getMvcurl() == null) saMenufrmPojo.setMvcurl("");
        if (saMenufrmPojo.getRemark() == null) saMenufrmPojo.setRemark("");
        if (saMenufrmPojo.getPermissioncode() == null) saMenufrmPojo.setPermissioncode("");
        if (saMenufrmPojo.getPageindex() == null) saMenufrmPojo.setPageindex(0);
        if (saMenufrmPojo.getCreateby() == null) saMenufrmPojo.setCreateby("");
        if (saMenufrmPojo.getCreatebyid() == null) saMenufrmPojo.setCreatebyid("");
        if (saMenufrmPojo.getCreatedate() == null) saMenufrmPojo.setCreatedate(new Date());
        if (saMenufrmPojo.getLister() == null) saMenufrmPojo.setLister("");
        if (saMenufrmPojo.getListerid() == null) saMenufrmPojo.setListerid("");
        if (saMenufrmPojo.getModifydate() == null) saMenufrmPojo.setModifydate(new Date());
        if (saMenufrmPojo.getRevision() == null) saMenufrmPojo.setRevision(0);
    }

    @Override
    public SaMenufrmPojo getEntity(String key) {
        return this.saMenufrmMapper.getEntity(key);
    }

    @Override
    public PageInfo<SaMenufrmPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMenufrmPojo> lst = saMenufrmMapper.getPageList(queryParam);
            PageInfo<SaMenufrmPojo> pageInfo = new PageInfo<SaMenufrmPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaMenufrmPojo insert(SaMenufrmPojo saMenufrmPojo) {
        //初始化NULL字段
        cleanNull(saMenufrmPojo);
        SaMenufrmEntity saMenufrmEntity = new SaMenufrmEntity();
        BeanUtils.copyProperties(saMenufrmPojo, saMenufrmEntity);
        //生成雪花id
        saMenufrmEntity.setNavid(inksSnowflake.getSnowflake().nextIdStr());
        saMenufrmEntity.setRevision(1);  //乐观锁
        this.saMenufrmMapper.insert(saMenufrmEntity);
        return this.getEntity(saMenufrmEntity.getNavid());

    }

    /**
     * 修改数据
     *
     * @param saMenufrmPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMenufrmPojo update(SaMenufrmPojo saMenufrmPojo) {
        SaMenufrmEntity saMenufrmEntity = new SaMenufrmEntity();
        BeanUtils.copyProperties(saMenufrmPojo, saMenufrmEntity);
        this.saMenufrmMapper.update(saMenufrmEntity);
        return this.getEntity(saMenufrmEntity.getNavid());
    }

    @Override
    public int delete(String key) {
        return this.saMenufrmMapper.delete(key);
    }

    @Override
    public List<SaMenufrmPojo> getAllMenus() {
        return saMenufrmMapper.getAllMenus();
    }

}
