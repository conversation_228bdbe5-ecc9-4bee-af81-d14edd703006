package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaMqttlogEntity;
import inks.service.sa.pms.domain.pojo.SaMqttlogPojo;
import inks.service.sa.pms.mapper.SaMqttlogMapper;
import inks.service.sa.pms.service.SaMqttlogService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * MQTT日志(SaMqttlog)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-02-27 13:27:02
 */
@Service("saMqttlogService")
public class SaMqttlogServiceImpl implements SaMqttlogService {
    @Resource
    private SaMqttlogMapper saMqttlogMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaMqttlogPojo getEntity(String key) {
        return this.saMqttlogMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaMqttlogPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaMqttlogPojo> lst = saMqttlogMapper.getPageList(queryParam);
            PageInfo<SaMqttlogPojo> pageInfo = new PageInfo<SaMqttlogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saMqttlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMqttlogPojo insert(SaMqttlogPojo saMqttlogPojo) {
        //初始化NULL字段
        if (saMqttlogPojo.getDevsn() == null) saMqttlogPojo.setDevsn("");
        if (saMqttlogPojo.getSoftware() == null) saMqttlogPojo.setSoftware("");
        if (saMqttlogPojo.getMsgtype() == null) saMqttlogPojo.setMsgtype("");
        if (saMqttlogPojo.getMsgcontent() == null) saMqttlogPojo.setMsgcontent("");
        if (saMqttlogPojo.getRownum() == null) saMqttlogPojo.setRownum(0);
        if (saMqttlogPojo.getRemark() == null) saMqttlogPojo.setRemark("");
        if (saMqttlogPojo.getCreateby() == null) saMqttlogPojo.setCreateby("");
        if (saMqttlogPojo.getCreatebyid() == null) saMqttlogPojo.setCreatebyid("");
        if (saMqttlogPojo.getCreatedate() == null) saMqttlogPojo.setCreatedate(new Date());
        if (saMqttlogPojo.getLister() == null) saMqttlogPojo.setLister("");
        if (saMqttlogPojo.getListerid() == null) saMqttlogPojo.setListerid("");
        if (saMqttlogPojo.getModifydate() == null) saMqttlogPojo.setModifydate(new Date());
        if (saMqttlogPojo.getCustom1() == null) saMqttlogPojo.setCustom1("");
        if (saMqttlogPojo.getCustom2() == null) saMqttlogPojo.setCustom2("");
        if (saMqttlogPojo.getCustom3() == null) saMqttlogPojo.setCustom3("");
        if (saMqttlogPojo.getCustom4() == null) saMqttlogPojo.setCustom4("");
        if (saMqttlogPojo.getCustom5() == null) saMqttlogPojo.setCustom5("");
        if (saMqttlogPojo.getTenantid() == null) saMqttlogPojo.setTenantid("");
        if (saMqttlogPojo.getTenantname() == null) saMqttlogPojo.setTenantname("");
        if (saMqttlogPojo.getRevision() == null) saMqttlogPojo.setRevision(0);
        SaMqttlogEntity saMqttlogEntity = new SaMqttlogEntity();
        BeanUtils.copyProperties(saMqttlogPojo, saMqttlogEntity);
        //生成雪花id
        saMqttlogEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saMqttlogEntity.setRevision(1);  //乐观锁
        this.saMqttlogMapper.insert(saMqttlogEntity);
        return this.getEntity(saMqttlogEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saMqttlogPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaMqttlogPojo update(SaMqttlogPojo saMqttlogPojo) {
        SaMqttlogEntity saMqttlogEntity = new SaMqttlogEntity();
        BeanUtils.copyProperties(saMqttlogPojo, saMqttlogEntity);
        this.saMqttlogMapper.update(saMqttlogEntity);
        return this.getEntity(saMqttlogEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saMqttlogMapper.delete(key);
    }


}
