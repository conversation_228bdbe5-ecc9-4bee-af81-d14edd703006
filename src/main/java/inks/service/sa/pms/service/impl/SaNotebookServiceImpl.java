package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaNotebookEntity;
import inks.service.sa.pms.domain.pojo.SaNotebookPojo;
import inks.service.sa.pms.mapper.SaNotebookMapper;
import inks.service.sa.pms.service.SaNotebookService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 笔记本(SaNotebook)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-06-22 16:39:51
 */
@Service("saNotebookService")
public class SaNotebookServiceImpl implements SaNotebookService {
    @Resource
    private SaNotebookMapper saNotebookMapper;

    @Override
    public SaNotebookPojo getEntity(String key) {
        return this.saNotebookMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaNotebookPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaNotebookPojo> lst = saNotebookMapper.getPageList(queryParam);
            PageInfo<SaNotebookPojo> pageInfo = new PageInfo<SaNotebookPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaNotebookPojo insert(SaNotebookPojo saNotebookPojo) {
        //初始化NULL字段
        cleanNull(saNotebookPojo);
        SaNotebookEntity saNotebookEntity = new SaNotebookEntity();
        BeanUtils.copyProperties(saNotebookPojo, saNotebookEntity);
        //生成雪花id
        saNotebookEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saNotebookEntity.setRevision(1);  //乐观锁
        this.saNotebookMapper.insert(saNotebookEntity);
        return this.getEntity(saNotebookEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saNotebookPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaNotebookPojo update(SaNotebookPojo saNotebookPojo) {
        SaNotebookEntity saNotebookEntity = new SaNotebookEntity();
        BeanUtils.copyProperties(saNotebookPojo, saNotebookEntity);
        this.saNotebookMapper.update(saNotebookEntity);
        return this.getEntity(saNotebookEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saNotebookMapper.delete(key);
    }

    @Override
    public SaNotebookPojo getMyNotebook(String userid) {
        SaNotebookPojo saNotebookDB = this.saNotebookMapper.getMyNotebook(userid);
        //如果没有笔记本，则初始化一个
        if (saNotebookDB != null) {
            return saNotebookDB;
        } else {
            SaNotebookPojo saNotebookPojo = new SaNotebookPojo();
            saNotebookPojo.setUserid(userid);
            saNotebookPojo.setContent("init notebook");
            return this.insert(saNotebookPojo);
        }
    }


    private static void cleanNull(SaNotebookPojo saNotebookPojo) {
        if (saNotebookPojo.getUserid() == null) saNotebookPojo.setUserid("");
        if (saNotebookPojo.getContent() == null) saNotebookPojo.setContent("");
        if (saNotebookPojo.getRemark() == null) saNotebookPojo.setRemark("");
        if (saNotebookPojo.getCreateby() == null) saNotebookPojo.setCreateby("");
        if (saNotebookPojo.getCreatebyid() == null) saNotebookPojo.setCreatebyid("");
        if (saNotebookPojo.getCreatedate() == null) saNotebookPojo.setCreatedate(new Date());
        if (saNotebookPojo.getLister() == null) saNotebookPojo.setLister("");
        if (saNotebookPojo.getListerid() == null) saNotebookPojo.setListerid("");
        if (saNotebookPojo.getModifydate() == null) saNotebookPojo.setModifydate(new Date());
        if (saNotebookPojo.getTenantid() == null) saNotebookPojo.setTenantid("");
        if (saNotebookPojo.getTenantname() == null) saNotebookPojo.setTenantname("");
        if (saNotebookPojo.getRevision() == null) saNotebookPojo.setRevision(0);
    }

}
