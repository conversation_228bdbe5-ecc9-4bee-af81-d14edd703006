package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaProjectEntity;
import inks.service.sa.pms.domain.SaProjectitemEntity;
import inks.service.sa.pms.domain.SaProjectlabelEntity;
import inks.service.sa.pms.domain.SaProjectstatusEntity;
import inks.service.sa.pms.domain.pojo.*;
import inks.service.sa.pms.mapper.*;
import inks.service.sa.pms.service.*;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * 工程项目(SaProject)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-26 14:31:00
 */
@Service("saProjectService")
public class SaProjectServiceImpl implements SaProjectService {
    @Resource
    private SaProjectMapper saProjectMapper;

    @Resource
    private SaProjectitemMapper saProjectitemMapper;
    @Resource
    private SaProjectstatusMapper saProjectstatusMapper;
    @Resource
    private SaProjectlabelMapper saProjectlabelMapper;
    @Resource
    private SaDemandMapper saDemandMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private SaProjectitemService saProjectitemService;
    @Resource
    private SaProjectstatusService saProjectstatusService;
    private SaProjectlabelService saProjectlabelService;
    @Resource
    private SaProjectsortService saProjectsortService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaProjectPojo getEntity(String key) {
        return this.saProjectMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaProjectitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaProjectitemdetailPojo> lst = saProjectMapper.getPageList(queryParam);
            PageInfo<SaProjectitemdetailPojo> pageInfo = new PageInfo<SaProjectitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaProjectPojo getBillEntity(String key) {
        try {
            //读取主表
            SaProjectPojo saProjectPojo = this.saProjectMapper.getEntity(key);
            //读取子表 人员子表
            saProjectPojo.setItem(saProjectitemMapper.getList(saProjectPojo.getId()));
            //status子表
            saProjectPojo.setStatus(saProjectstatusMapper.getList(saProjectPojo.getId()));
            //label子表
            saProjectPojo.setLabel(saProjectlabelMapper.getList(saProjectPojo.getId()));
            return saProjectPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaProjectPojo getBillEntityByProjectName(String name) {
        try {
            //读取主表
            SaProjectPojo saProjectPojo = this.saProjectMapper.getEntityByProjectName(name);
            //读取子表 人员子表
            saProjectPojo.setItem(saProjectitemMapper.getList(saProjectPojo.getId()));
            //status子表
            saProjectPojo.setStatus(saProjectstatusMapper.getList(saProjectPojo.getId()));
            //label子表
            saProjectPojo.setLabel(saProjectlabelMapper.getList(saProjectPojo.getId()));
            return saProjectPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaProjectPojo> getBillList(QueryParam queryParam, String projectFilter, boolean isadmin, String engineerid) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaProjectPojo> lst = saProjectMapper.getPageTh(queryParam, isadmin, engineerid);
            //循环设置每个主表对象的item子表
            for (SaProjectPojo saProjectPojo : lst) {
                saProjectPojo.setItem(saProjectitemMapper.getList(saProjectPojo.getId()));
                saProjectPojo.setStatus(saProjectstatusMapper.getList(saProjectPojo.getId()));
                saProjectPojo.setLabel(saProjectlabelMapper.getList(saProjectPojo.getId()));

                Map<String, BigDecimal> countTodoAndOverdue = saDemandMapper.getCountTodoAndOverdue(saProjectPojo.getId(), projectFilter);
                //OverdueCount,TodoCount
                //SQL查询中使用了SUM函数，SUM函数的返回类型是BigDecima，以使用intValue()方法来将BigDecimal转换为Integer
                saProjectPojo.setOverduecount(countTodoAndOverdue.get("OverdueCount").intValue());
                saProjectPojo.setTodocount(countTodoAndOverdue.get("TodoCount").intValue());
            }
            PageInfo<SaProjectPojo> pageInfo = new PageInfo<SaProjectPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaProjectPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaProjectPojo> lst = saProjectMapper.getPageTh(queryParam, false, null);
            PageInfo<SaProjectPojo> pageInfo = new PageInfo<SaProjectPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saProjectPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaProjectPojo insert(SaProjectPojo saProjectPojo,String engineerid) {
//初始化NULL字段
        if (saProjectPojo.getGengroupid() == null) saProjectPojo.setGengroupid("");
        if (saProjectPojo.getProjcode() == null) saProjectPojo.setProjcode("");
        if (saProjectPojo.getProjtype() == null) saProjectPojo.setProjtype("");
        if (saProjectPojo.getProjname() == null) saProjectPojo.setProjname("");
        if (saProjectPojo.getProjspec() == null) saProjectPojo.setProjspec("");
        if (saProjectPojo.getProjunit() == null) saProjectPojo.setProjunit("");
        if (saProjectPojo.getProjpinyin() == null) saProjectPojo.setProjpinyin("");
        if (saProjectPojo.getVersionnum() == null) saProjectPojo.setVersionnum("");
        if (saProjectPojo.getBarcode() == null) saProjectPojo.setBarcode("");
        if (saProjectPojo.getOperator() == null) saProjectPojo.setOperator("");
        if (saProjectPojo.getEnabledmark() == null) saProjectPojo.setEnabledmark(0);
        if (saProjectPojo.getCoverimage() == null) saProjectPojo.setCoverimage("");
        if (saProjectPojo.getStarmark() == null) saProjectPojo.setStarmark(0);
        if (saProjectPojo.getRownum() == null) saProjectPojo.setRownum(0);
        if (saProjectPojo.getRemark() == null) saProjectPojo.setRemark("");
        if (saProjectPojo.getCreateby() == null) saProjectPojo.setCreateby("");
        if (saProjectPojo.getCreatebyid() == null) saProjectPojo.setCreatebyid("");
        if (saProjectPojo.getCreatedate() == null) saProjectPojo.setCreatedate(new Date());
        if (saProjectPojo.getLister() == null) saProjectPojo.setLister("");
        if (saProjectPojo.getListerid() == null) saProjectPojo.setListerid("");
        if (saProjectPojo.getModifydate() == null) saProjectPojo.setModifydate(new Date());
        if (saProjectPojo.getCustom1() == null) saProjectPojo.setCustom1("");
        if (saProjectPojo.getCustom2() == null) saProjectPojo.setCustom2("");
        if (saProjectPojo.getCustom3() == null) saProjectPojo.setCustom3("");
        if (saProjectPojo.getCustom4() == null) saProjectPojo.setCustom4("");
        if (saProjectPojo.getCustom5() == null) saProjectPojo.setCustom5("");
        if (saProjectPojo.getCustom6() == null) saProjectPojo.setCustom6("");
        if (saProjectPojo.getCustom7() == null) saProjectPojo.setCustom7("");
        if (saProjectPojo.getCustom8() == null) saProjectPojo.setCustom8("");
        if (saProjectPojo.getCustom9() == null) saProjectPojo.setCustom9("");
        if (saProjectPojo.getCustom10() == null) saProjectPojo.setCustom10("");
        if (saProjectPojo.getDeptid() == null) saProjectPojo.setDeptid("");
        if (saProjectPojo.getTenantid() == null) saProjectPojo.setTenantid("");
        if (saProjectPojo.getTenantname() == null) saProjectPojo.setTenantname("");
        if (saProjectPojo.getRevision() == null) saProjectPojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaProjectEntity saProjectEntity = new SaProjectEntity();
        BeanUtils.copyProperties(saProjectPojo, saProjectEntity);
        //设置id和新建日期
        saProjectEntity.setId(id);
        saProjectEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saProjectMapper.insert(saProjectEntity);
        //Item子表处理
        List<SaProjectitemPojo> lst = saProjectPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaProjectitemPojo projectitemPojo : lst) {
                //初始化item的NULL
                SaProjectitemPojo itemPojo = this.saProjectitemService.clearNull(projectitemPojo);
                SaProjectitemEntity saProjectitemEntity = new SaProjectitemEntity();
                BeanUtils.copyProperties(itemPojo, saProjectitemEntity);
                //设置id和Pid
                saProjectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saProjectitemEntity.setPid(id);
                saProjectitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saProjectitemMapper.insert(saProjectitemEntity);
                // 项目排序表处理Sa_ProjectSort: Projectid和Engineerid关联
                SaProjectsortPojo saProjectsortPojo = new SaProjectsortPojo();
                saProjectsortPojo.setProjectid(id);
                saProjectsortPojo.setEngineerid(itemPojo.getEngineerid()); // 修复：使用当前工程师ID而不是创建者ID
                saProjectsortService.insert(saProjectsortPojo);
            }

            // 修复：在Sa_ProjectItem表中插入一条记录 关联当前项目创建者工程师id和项目Pid（只执行一次）
            SaProjectitemPojo creatorProjectitemPojo = new SaProjectitemPojo();
            creatorProjectitemPojo.setEngineerid(engineerid);
            creatorProjectitemPojo.setPid(id);
            saProjectitemService.insert(creatorProjectitemPojo);

            // 为创建者添加项目排序表记录
            SaProjectsortPojo creatorProjectsortPojo = new SaProjectsortPojo();
            creatorProjectsortPojo.setProjectid(id);
            creatorProjectsortPojo.setEngineerid(engineerid);
            saProjectsortService.insert(creatorProjectsortPojo);
        }
        // status子表处理
        List<SaProjectstatusPojo> status = saProjectPojo.getStatus();
        if (status != null) {
            for (SaProjectstatusPojo saProjectstatusPojo : status) {
                //初始化status的NULL
                SaProjectstatusPojo statusPojo = this.saProjectstatusService.clearNull(saProjectstatusPojo);
                SaProjectstatusEntity saProjectstatusEntity = new SaProjectstatusEntity();
                BeanUtils.copyProperties(statusPojo, saProjectstatusEntity);
                //设置id和Pid
                saProjectstatusEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saProjectstatusEntity.setPid(id);
                saProjectstatusEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saProjectstatusMapper.insert(saProjectstatusEntity);
            }
        }
        // label子表处理
        List<SaProjectlabelPojo> label = saProjectPojo.getLabel();
        if (label != null) {
            for (SaProjectlabelPojo saProjectlabelPojo : label) {
                //初始化label的NULL
                SaProjectlabelPojo labelPojo = this.saProjectlabelService.clearNull(saProjectlabelPojo);
                SaProjectlabelEntity saProjectlabelEntity = new SaProjectlabelEntity();
                BeanUtils.copyProperties(labelPojo, saProjectlabelEntity);
                //设置id和Pid
                saProjectlabelEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saProjectlabelEntity.setPid(id);
                saProjectlabelEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saProjectlabelMapper.insert(saProjectlabelEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(saProjectEntity.getId());
    }

    /**
     * 修改数据
     *
     * @param saProjectPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaProjectPojo update(SaProjectPojo saProjectPojo) {
        //主表更改
        SaProjectEntity saProjectEntity = new SaProjectEntity();
        BeanUtils.copyProperties(saProjectPojo, saProjectEntity);
        this.saProjectMapper.update(saProjectEntity);
        //Item子表处理
        if (saProjectPojo.getItem() != null) {
            List<SaProjectitemPojo> lst = saProjectPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saProjectMapper.getDelItemIds(saProjectPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    // 根据项目id和工程师id删除Sa_ProjectSort表中的记录    lstDelIds.get(i)是item的id,可以查出engineerid
                    saProjectsortService.deleteByProjectIdAndProjectItemId(saProjectPojo.getId(), lstDelIds.get(i));
                    this.saProjectitemMapper.delete(lstDelIds.get(i));
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SaProjectitemEntity saProjectitemEntity = new SaProjectitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SaProjectitemPojo itemPojo = this.saProjectitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, saProjectitemEntity);
                        //设置id和Pid
                        saProjectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saProjectitemEntity.setPid(saProjectEntity.getId());  // 主表 id
                        saProjectitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saProjectitemMapper.insert(saProjectitemEntity);
                        // 项目排序表处理Sa_ProjectSort: Projectid和Engineerid关联
                        SaProjectsortPojo saProjectsortPojo = new SaProjectsortPojo();
                        saProjectsortPojo.setProjectid(saProjectEntity.getId());
                        saProjectsortPojo.setEngineerid(itemPojo.getEngineerid());
                        saProjectsortService.insert(saProjectsortPojo);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), saProjectitemEntity);
                        this.saProjectitemMapper.update(saProjectitemEntity);
                    }
                }
            }
        }

        // status子表处理
        if (saProjectPojo.getStatus() != null) {
            List<SaProjectstatusPojo> status = saProjectPojo.getStatus();
            //获取被删除的status
            List<String> lstDelIds = saProjectMapper.getDelStatusIds(saProjectPojo);
            if (lstDelIds != null) {
                //循环每个删除status子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saProjectstatusMapper.delete(lstDelIds.get(i));
                }
            }
            if (status != null) {
                //循环每个status子表
                for (int i = 0; i < status.size(); i++) {
                    SaProjectstatusEntity saProjectstatusEntity = new SaProjectstatusEntity();
                    if ("".equals(status.get(i).getId()) || status.get(i).getId() == null) {
                        //初始化status的NULL
                        SaProjectstatusPojo statusPojo = this.saProjectstatusService.clearNull(status.get(i));
                        BeanUtils.copyProperties(statusPojo, saProjectstatusEntity);
                        //设置id和Pid
                        saProjectstatusEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // status id
                        saProjectstatusEntity.setPid(saProjectEntity.getId());  // 主表 id
                        saProjectstatusEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saProjectstatusMapper.insert(saProjectstatusEntity);
                    } else {
                        BeanUtils.copyProperties(status.get(i), saProjectstatusEntity);
                        this.saProjectstatusMapper.update(saProjectstatusEntity);
                    }
                }
            }
        }

        // label子表处理
        if (saProjectPojo.getLabel() != null) {
            List<SaProjectlabelPojo> label = saProjectPojo.getLabel();
            //获取被删除的label
            List<String> lstDelIds = saProjectMapper.getDelLabelIds(saProjectPojo);
            if (lstDelIds != null) {
                //循环每个删除label子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saProjectlabelMapper.delete(lstDelIds.get(i));
                }
            }
            if (label != null) {
                //循环每个label子表
                for (int i = 0; i < label.size(); i++) {
                    SaProjectlabelEntity saProjectlabelEntity = new SaProjectlabelEntity();
                    if ("".equals(label.get(i).getId()) || label.get(i).getId() == null) {
                        //初始化label的NULL
                        SaProjectlabelPojo labelPojo = this.saProjectlabelService.clearNull(label.get(i));
                        BeanUtils.copyProperties(labelPojo, saProjectlabelEntity);
                        //设置id和Pid
                        saProjectlabelEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // label id
                        saProjectlabelEntity.setPid(saProjectEntity.getId());  // 主表 id
                        saProjectlabelEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saProjectlabelMapper.insert(saProjectlabelEntity);
                    } else {
                        BeanUtils.copyProperties(label.get(i), saProjectlabelEntity);
                        this.saProjectlabelMapper.update(saProjectlabelEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saProjectEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
        SaProjectPojo saProjectPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaProjectitemPojo> lst = saProjectPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                // 根据项目id和工程师id删除Sa_ProjectSort表中的记录    lst.get(i).getId()是item的id,可以查出engineerid
                saProjectsortService.deleteByProjectIdAndProjectItemId(saProjectPojo.getId(), lst.get(i).getId());
                this.saProjectitemMapper.delete(lst.get(i).getId());
            }
        }
        // status子表处理
        List<SaProjectstatusPojo> status = saProjectPojo.getStatus();
        if (status != null) {
            //循环每个删除status子表
            for (int i = 0; i < status.size(); i++) {
                this.saProjectstatusMapper.delete(status.get(i).getId());
            }
        }
        // label子表处理
        List<SaProjectlabelPojo> label = saProjectPojo.getLabel();
        if (label != null) {
            //循环每个删除label子表
            for (int i = 0; i < label.size(); i++) {
                this.saProjectlabelMapper.delete(label.get(i).getId());
            }
        }
        return this.saProjectMapper.delete(key);
    }


}
