package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaProjectitemEntity;
import inks.service.sa.pms.domain.pojo.SaProjectitemPojo;
import inks.service.sa.pms.mapper.SaProjectitemMapper;
import inks.service.sa.pms.service.SaProjectitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 项目参与人员(SaProjectitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-26 14:28:41
 */
@Service("saProjectitemService")
public class SaProjectitemServiceImpl implements SaProjectitemService {
    @Resource
    private SaProjectitemMapper saProjectitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaProjectitemPojo getEntity(String key) {
        return this.saProjectitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaProjectitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaProjectitemPojo> lst = saProjectitemMapper.getPageList(queryParam);
            PageInfo<SaProjectitemPojo> pageInfo = new PageInfo<SaProjectitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaProjectitemPojo> getList(String Pid) {
        try {
            List<SaProjectitemPojo> lst = saProjectitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saProjectitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectitemPojo insert(SaProjectitemPojo saProjectitemPojo) {
        //初始化item的NULL
        SaProjectitemPojo itempojo = this.clearNull(saProjectitemPojo);
        SaProjectitemEntity saProjectitemEntity = new SaProjectitemEntity();
        BeanUtils.copyProperties(itempojo, saProjectitemEntity);
        //生成雪花id
        saProjectitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saProjectitemEntity.setRevision(1);  //乐观锁
        this.saProjectitemMapper.insert(saProjectitemEntity);
        return this.getEntity(saProjectitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saProjectitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectitemPojo update(SaProjectitemPojo saProjectitemPojo) {
        SaProjectitemEntity saProjectitemEntity = new SaProjectitemEntity();
        BeanUtils.copyProperties(saProjectitemPojo, saProjectitemEntity);
        this.saProjectitemMapper.update(saProjectitemEntity);
        return this.getEntity(saProjectitemEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saProjectitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saProjectitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectitemPojo clearNull(SaProjectitemPojo saProjectitemPojo) {
        //初始化NULL字段
        if (saProjectitemPojo.getPid() == null) saProjectitemPojo.setPid("");
        if (saProjectitemPojo.getEngineerid() == null) saProjectitemPojo.setEngineerid("");
        if (saProjectitemPojo.getRoletype() == null) saProjectitemPojo.setRoletype(0);
        if (saProjectitemPojo.getRownum() == null) saProjectitemPojo.setRownum(0);
        if (saProjectitemPojo.getRemark() == null) saProjectitemPojo.setRemark("");
        if (saProjectitemPojo.getCreateby() == null) saProjectitemPojo.setCreateby("");
        if (saProjectitemPojo.getCreatebyid() == null) saProjectitemPojo.setCreatebyid("");
        if (saProjectitemPojo.getCreatedate() == null) saProjectitemPojo.setCreatedate(new Date());
        if (saProjectitemPojo.getLister() == null) saProjectitemPojo.setLister("");
        if (saProjectitemPojo.getListerid() == null) saProjectitemPojo.setListerid("");
        if (saProjectitemPojo.getModifydate() == null) saProjectitemPojo.setModifydate(new Date());
        if (saProjectitemPojo.getCustom1() == null) saProjectitemPojo.setCustom1("");
        if (saProjectitemPojo.getCustom2() == null) saProjectitemPojo.setCustom2("");
        if (saProjectitemPojo.getCustom3() == null) saProjectitemPojo.setCustom3("");
        if (saProjectitemPojo.getCustom4() == null) saProjectitemPojo.setCustom4("");
        if (saProjectitemPojo.getCustom5() == null) saProjectitemPojo.setCustom5("");
        if (saProjectitemPojo.getDeptid() == null) saProjectitemPojo.setDeptid("");
        if (saProjectitemPojo.getTenantid() == null) saProjectitemPojo.setTenantid("");
        if (saProjectitemPojo.getTenantname() == null) saProjectitemPojo.setTenantname("");
        if (saProjectitemPojo.getRevision() == null) saProjectitemPojo.setRevision(0);
        return saProjectitemPojo;
    }
}
