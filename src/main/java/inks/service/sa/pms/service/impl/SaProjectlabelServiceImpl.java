package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaProjectlabelEntity;
import inks.service.sa.pms.domain.pojo.SaProjectlabelPojo;
import inks.service.sa.pms.mapper.SaProjectlabelMapper;
import inks.service.sa.pms.service.SaProjectlabelService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 项目标签(SaProjectlabel)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-26 14:28:41
 */
@Service("saProjectlabelService")
public class SaProjectlabelServiceImpl implements SaProjectlabelService {
    @Resource
    private SaProjectlabelMapper saProjectlabelMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaProjectlabelPojo getEntity(String key) {
        return this.saProjectlabelMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaProjectlabelPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaProjectlabelPojo> lst = saProjectlabelMapper.getPageList(queryParam);
            PageInfo<SaProjectlabelPojo> pageInfo = new PageInfo<SaProjectlabelPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaProjectlabelPojo> getList(String Pid) {
        try {
            List<SaProjectlabelPojo> lst = saProjectlabelMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saProjectlabelPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectlabelPojo insert(SaProjectlabelPojo saProjectlabelPojo) {
        //初始化item的NULL
        SaProjectlabelPojo itempojo = this.clearNull(saProjectlabelPojo);
        SaProjectlabelEntity saProjectlabelEntity = new SaProjectlabelEntity();
        BeanUtils.copyProperties(itempojo, saProjectlabelEntity);
        //生成雪花id
        saProjectlabelEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saProjectlabelEntity.setRevision(1);  //乐观锁
        this.saProjectlabelMapper.insert(saProjectlabelEntity);
        return this.getEntity(saProjectlabelEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saProjectlabelPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectlabelPojo update(SaProjectlabelPojo saProjectlabelPojo) {
        SaProjectlabelEntity saProjectlabelEntity = new SaProjectlabelEntity();
        BeanUtils.copyProperties(saProjectlabelPojo, saProjectlabelEntity);
        this.saProjectlabelMapper.update(saProjectlabelEntity);
        return this.getEntity(saProjectlabelEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saProjectlabelMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saProjectlabelPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectlabelPojo clearNull(SaProjectlabelPojo saProjectlabelPojo) {
        //初始化NULL字段
        if (saProjectlabelPojo.getPid() == null) saProjectlabelPojo.setPid("");
        if (saProjectlabelPojo.getLabelname() == null) saProjectlabelPojo.setLabelname("");
        if (saProjectlabelPojo.getLabelcolor() == null) saProjectlabelPojo.setLabelcolor("");
        if (saProjectlabelPojo.getRownum() == null) saProjectlabelPojo.setRownum(0);
        if (saProjectlabelPojo.getRemark() == null) saProjectlabelPojo.setRemark("");
        if (saProjectlabelPojo.getCreateby() == null) saProjectlabelPojo.setCreateby("");
        if (saProjectlabelPojo.getCreatebyid() == null) saProjectlabelPojo.setCreatebyid("");
        if (saProjectlabelPojo.getCreatedate() == null) saProjectlabelPojo.setCreatedate(new Date());
        if (saProjectlabelPojo.getLister() == null) saProjectlabelPojo.setLister("");
        if (saProjectlabelPojo.getListerid() == null) saProjectlabelPojo.setListerid("");
        if (saProjectlabelPojo.getModifydate() == null) saProjectlabelPojo.setModifydate(new Date());
        if (saProjectlabelPojo.getCustom1() == null) saProjectlabelPojo.setCustom1("");
        if (saProjectlabelPojo.getCustom2() == null) saProjectlabelPojo.setCustom2("");
        if (saProjectlabelPojo.getCustom3() == null) saProjectlabelPojo.setCustom3("");
        if (saProjectlabelPojo.getCustom4() == null) saProjectlabelPojo.setCustom4("");
        if (saProjectlabelPojo.getCustom5() == null) saProjectlabelPojo.setCustom5("");
        if (saProjectlabelPojo.getDeptid() == null) saProjectlabelPojo.setDeptid("");
        if (saProjectlabelPojo.getTenantid() == null) saProjectlabelPojo.setTenantid("");
        if (saProjectlabelPojo.getTenantname() == null) saProjectlabelPojo.setTenantname("");
        if (saProjectlabelPojo.getRevision() == null) saProjectlabelPojo.setRevision(0);
        return saProjectlabelPojo;
    }
}
