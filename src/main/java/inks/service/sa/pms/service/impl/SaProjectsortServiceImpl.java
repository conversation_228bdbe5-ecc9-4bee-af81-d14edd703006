package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaProjectsortEntity;
import inks.service.sa.pms.domain.pojo.SaProjectsortPojo;
import inks.service.sa.pms.mapper.SaProjectsortMapper;
import inks.service.sa.pms.service.SaProjectsortService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 个人常用项目排序(SaProjectsort)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-25 13:07:31
 */
@Service("saProjectsortService")
public class SaProjectsortServiceImpl implements SaProjectsortService {
    @Resource
    private SaProjectsortMapper saProjectsortMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaProjectsortPojo getEntity(String key) {
        return this.saProjectsortMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaProjectsortPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaProjectsortPojo> lst = saProjectsortMapper.getPageList(queryParam);
            PageInfo<SaProjectsortPojo> pageInfo = new PageInfo<SaProjectsortPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saProjectsortPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectsortPojo insert(SaProjectsortPojo saProjectsortPojo) {
        //初始化NULL字段
        if (saProjectsortPojo.getProjectid() == null) saProjectsortPojo.setProjectid("");
        if (saProjectsortPojo.getEngineerid() == null) saProjectsortPojo.setEngineerid("");
        if (saProjectsortPojo.getStarmark() == null) saProjectsortPojo.setStarmark(0);
        if (saProjectsortPojo.getLastaccesstime() == null) saProjectsortPojo.setLastaccesstime(new Date());
        if (saProjectsortPojo.getRownum() == null) saProjectsortPojo.setRownum(0);
        if (saProjectsortPojo.getRemark() == null) saProjectsortPojo.setRemark("");
        if (saProjectsortPojo.getCreateby() == null) saProjectsortPojo.setCreateby("");
        if (saProjectsortPojo.getCreatebyid() == null) saProjectsortPojo.setCreatebyid("");
        if (saProjectsortPojo.getCreatedate() == null) saProjectsortPojo.setCreatedate(new Date());
        if (saProjectsortPojo.getLister() == null) saProjectsortPojo.setLister("");
        if (saProjectsortPojo.getListerid() == null) saProjectsortPojo.setListerid("");
        if (saProjectsortPojo.getModifydate() == null) saProjectsortPojo.setModifydate(new Date());
        if (saProjectsortPojo.getCustom1() == null) saProjectsortPojo.setCustom1("");
        if (saProjectsortPojo.getCustom2() == null) saProjectsortPojo.setCustom2("");
        if (saProjectsortPojo.getCustom3() == null) saProjectsortPojo.setCustom3("");
        if (saProjectsortPojo.getCustom4() == null) saProjectsortPojo.setCustom4("");
        if (saProjectsortPojo.getCustom5() == null) saProjectsortPojo.setCustom5("");
        if (saProjectsortPojo.getDeptid() == null) saProjectsortPojo.setDeptid("");
        if (saProjectsortPojo.getTenantid() == null) saProjectsortPojo.setTenantid("");
        if (saProjectsortPojo.getTenantname() == null) saProjectsortPojo.setTenantname("");
        if (saProjectsortPojo.getRevision() == null) saProjectsortPojo.setRevision(0);
        SaProjectsortEntity saProjectsortEntity = new SaProjectsortEntity();
        BeanUtils.copyProperties(saProjectsortPojo, saProjectsortEntity);
        //生成雪花id
        saProjectsortEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saProjectsortEntity.setRevision(1);  //乐观锁
        this.saProjectsortMapper.insert(saProjectsortEntity);
        return this.getEntity(saProjectsortEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saProjectsortPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectsortPojo update(SaProjectsortPojo saProjectsortPojo) {
        SaProjectsortEntity saProjectsortEntity = new SaProjectsortEntity();
        BeanUtils.copyProperties(saProjectsortPojo, saProjectsortEntity);
        this.saProjectsortMapper.update(saProjectsortEntity);
        return this.getEntity(saProjectsortEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saProjectsortMapper.delete(key);
    }

    @Override
    public void deleteByProjectIdAndProjectItemId(String id, String itemid) {
        this.saProjectsortMapper.deleteByProjectIdAndProjectItemId(id, itemid);
    }
}
