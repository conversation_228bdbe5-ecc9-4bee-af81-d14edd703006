package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaProjectstatusEntity;
import inks.service.sa.pms.domain.pojo.SaProjectstatusPojo;
import inks.service.sa.pms.mapper.SaProjectstatusMapper;
import inks.service.sa.pms.service.SaProjectstatusService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 项目状态(SaProjectstatus)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-10-23 10:56:32
 */
@Service("saProjectstatusService")
public class SaProjectstatusServiceImpl implements SaProjectstatusService {
    @Resource
    private SaProjectstatusMapper saProjectstatusMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaProjectstatusPojo getEntity(String key) {
        return this.saProjectstatusMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaProjectstatusPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaProjectstatusPojo> lst = saProjectstatusMapper.getPageList(queryParam);
            PageInfo<SaProjectstatusPojo> pageInfo = new PageInfo<SaProjectstatusPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaProjectstatusPojo> getList(String Pid) {
        try {
            List<SaProjectstatusPojo> lst = saProjectstatusMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saProjectstatusPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectstatusPojo insert(SaProjectstatusPojo saProjectstatusPojo) {
        //初始化item的NULL
        SaProjectstatusPojo itempojo = this.clearNull(saProjectstatusPojo);
        SaProjectstatusEntity saProjectstatusEntity = new SaProjectstatusEntity();
        BeanUtils.copyProperties(itempojo, saProjectstatusEntity);
        //生成雪花id
        saProjectstatusEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saProjectstatusEntity.setRevision(1);  //乐观锁
        this.saProjectstatusMapper.insert(saProjectstatusEntity);
        return this.getEntity(saProjectstatusEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saProjectstatusPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectstatusPojo update(SaProjectstatusPojo saProjectstatusPojo) {
        SaProjectstatusEntity saProjectstatusEntity = new SaProjectstatusEntity();
        BeanUtils.copyProperties(saProjectstatusPojo, saProjectstatusEntity);
        this.saProjectstatusMapper.update(saProjectstatusEntity);
        return this.getEntity(saProjectstatusEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saProjectstatusMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saProjectstatusPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjectstatusPojo clearNull(SaProjectstatusPojo saProjectstatusPojo) {
        //初始化NULL字段
        if (saProjectstatusPojo.getPid() == null) saProjectstatusPojo.setPid("");
        if (saProjectstatusPojo.getStatusname() == null) saProjectstatusPojo.setStatusname("");
        if (saProjectstatusPojo.getStatustype() == null) saProjectstatusPojo.setStatustype("");
        if (saProjectstatusPojo.getRownum() == null) saProjectstatusPojo.setRownum(0);
        if (saProjectstatusPojo.getRemark() == null) saProjectstatusPojo.setRemark("");
        if (saProjectstatusPojo.getCreateby() == null) saProjectstatusPojo.setCreateby("");
        if (saProjectstatusPojo.getCreatebyid() == null) saProjectstatusPojo.setCreatebyid("");
        if (saProjectstatusPojo.getCreatedate() == null) saProjectstatusPojo.setCreatedate(new Date());
        if (saProjectstatusPojo.getLister() == null) saProjectstatusPojo.setLister("");
        if (saProjectstatusPojo.getListerid() == null) saProjectstatusPojo.setListerid("");
        if (saProjectstatusPojo.getModifydate() == null) saProjectstatusPojo.setModifydate(new Date());
        if (saProjectstatusPojo.getFinishmark() == null) saProjectstatusPojo.setFinishmark(0);
        if (saProjectstatusPojo.getCustom1() == null) saProjectstatusPojo.setCustom1("");
        if (saProjectstatusPojo.getCustom2() == null) saProjectstatusPojo.setCustom2("");
        if (saProjectstatusPojo.getCustom3() == null) saProjectstatusPojo.setCustom3("");
        if (saProjectstatusPojo.getCustom4() == null) saProjectstatusPojo.setCustom4("");
        if (saProjectstatusPojo.getCustom5() == null) saProjectstatusPojo.setCustom5("");
        if (saProjectstatusPojo.getDeptid() == null) saProjectstatusPojo.setDeptid("");
        if (saProjectstatusPojo.getTenantid() == null) saProjectstatusPojo.setTenantid("");
        if (saProjectstatusPojo.getTenantname() == null) saProjectstatusPojo.setTenantname("");
        if (saProjectstatusPojo.getRevision() == null) saProjectstatusPojo.setRevision(0);
        return saProjectstatusPojo;
    }
}
