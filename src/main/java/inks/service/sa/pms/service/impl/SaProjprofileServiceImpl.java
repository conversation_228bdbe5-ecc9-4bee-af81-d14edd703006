package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.SaProjprofileEntity;
import inks.service.sa.pms.domain.pojo.SaProjprofilePojo;
import inks.service.sa.pms.mapper.SaProjprofileMapper;
import inks.service.sa.pms.service.SaProjprofileService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * (SaProjprofile)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-01-05 16:25:35
 */
@Service("saProjprofileService")
public class SaProjprofileServiceImpl implements SaProjprofileService {
    @Resource
    private SaProjprofileMapper saProjprofileMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaProjprofilePojo getEntity(String key) {
        return this.saProjprofileMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaProjprofilePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaProjprofilePojo> lst = saProjprofileMapper.getPageList(queryParam);
            PageInfo<SaProjprofilePojo> pageInfo = new PageInfo<SaProjprofilePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saProjprofilePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjprofilePojo insert(SaProjprofilePojo saProjprofilePojo) {
        //初始化NULL字段
        if (saProjprofilePojo.getProjectid() == null) saProjprofilePojo.setProjectid("");
        if (saProjprofilePojo.getProjectname() == null) saProjprofilePojo.setProjectname("");
        if (saProjprofilePojo.getTitle() == null) saProjprofilePojo.setTitle("");
        if (saProjprofilePojo.getContent() == null) saProjprofilePojo.setContent("");
        if (saProjprofilePojo.getPicture() == null) saProjprofilePojo.setPicture("");
        if (saProjprofilePojo.getLooktimes() == null) saProjprofilePojo.setLooktimes(0);
        if (saProjprofilePojo.getGoodnum() == null) saProjprofilePojo.setGoodnum(0);
        if (saProjprofilePojo.getNgnum() == null) saProjprofilePojo.setNgnum(0);
        if (saProjprofilePojo.getDirname() == null) saProjprofilePojo.setDirname("");
        if (saProjprofilePojo.getFilename() == null) saProjprofilePojo.setFilename("");
        if (saProjprofilePojo.getPublicmark() == null) saProjprofilePojo.setPublicmark(0);
        if (saProjprofilePojo.getCreateby() == null) saProjprofilePojo.setCreateby("");
        if (saProjprofilePojo.getCreatebyid() == null) saProjprofilePojo.setCreatebyid("");
        if (saProjprofilePojo.getCreatedate() == null) saProjprofilePojo.setCreatedate(new Date());
        if (saProjprofilePojo.getLister() == null) saProjprofilePojo.setLister("");
        if (saProjprofilePojo.getListerid() == null) saProjprofilePojo.setListerid("");
        if (saProjprofilePojo.getModifydate() == null) saProjprofilePojo.setModifydate(new Date());
        if (saProjprofilePojo.getCustom1() == null) saProjprofilePojo.setCustom1("");
        if (saProjprofilePojo.getCustom2() == null) saProjprofilePojo.setCustom2("");
        if (saProjprofilePojo.getCustom3() == null) saProjprofilePojo.setCustom3("");
        if (saProjprofilePojo.getCustom4() == null) saProjprofilePojo.setCustom4("");
        if (saProjprofilePojo.getCustom5() == null) saProjprofilePojo.setCustom5("");
        if (saProjprofilePojo.getCustom6() == null) saProjprofilePojo.setCustom6("");
        if (saProjprofilePojo.getCustom7() == null) saProjprofilePojo.setCustom7("");
        if (saProjprofilePojo.getCustom8() == null) saProjprofilePojo.setCustom8("");
        if (saProjprofilePojo.getCustom9() == null) saProjprofilePojo.setCustom9("");
        if (saProjprofilePojo.getCustom10() == null) saProjprofilePojo.setCustom10("");
        if (saProjprofilePojo.getTenantid() == null) saProjprofilePojo.setTenantid("");
        if (saProjprofilePojo.getRevision() == null) saProjprofilePojo.setRevision(0);
        if (saProjprofilePojo.getVideoid() == null) saProjprofilePojo.setVideoid("");
        if (saProjprofilePojo.getVideoname() == null) saProjprofilePojo.setVideoname("");
        SaProjprofileEntity saProjprofileEntity = new SaProjprofileEntity();
        BeanUtils.copyProperties(saProjprofilePojo, saProjprofileEntity);

        saProjprofileEntity.setId(UUID.randomUUID().toString());
        saProjprofileEntity.setRevision(1);  //乐观锁
        this.saProjprofileMapper.insert(saProjprofileEntity);
        return this.getEntity(saProjprofileEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saProjprofilePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaProjprofilePojo update(SaProjprofilePojo saProjprofilePojo) {
        SaProjprofileEntity saProjprofileEntity = new SaProjprofileEntity();
        BeanUtils.copyProperties(saProjprofilePojo, saProjprofileEntity);
        this.saProjprofileMapper.update(saProjprofileEntity);
        return this.getEntity(saProjprofileEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saProjprofileMapper.delete(key);
    }


}
