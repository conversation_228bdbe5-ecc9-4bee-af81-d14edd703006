package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaReleaseEntity;
import inks.service.sa.pms.domain.SaReleaseitemEntity;
import inks.service.sa.pms.domain.pojo.SaReleasePojo;
import inks.service.sa.pms.domain.pojo.SaReleaseitemPojo;
import inks.service.sa.pms.domain.pojo.SaReleaseitemdetailPojo;
import inks.service.sa.pms.mapper.SaReleaseMapper;
import inks.service.sa.pms.mapper.SaReleaseitemMapper;
import inks.service.sa.pms.service.SaReleaseService;
import inks.service.sa.pms.service.SaReleaseitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaRelease)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-05-02 10:33:17
 */
@Service("saReleaseService")
public class SaReleaseServiceImpl implements SaReleaseService {
    @Resource
    private SaReleaseMapper saReleaseMapper;

    @Resource
    private SaReleaseitemMapper saReleaseitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private SaReleaseitemService saReleaseitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaReleasePojo getEntity(String key) {
        return this.saReleaseMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaReleaseitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReleaseitemdetailPojo> lst = saReleaseMapper.getPageList(queryParam);
            PageInfo<SaReleaseitemdetailPojo> pageInfo = new PageInfo<SaReleaseitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaReleasePojo getBillEntity(String key) {
        try {
            //读取主表
            SaReleasePojo saReleasePojo = this.saReleaseMapper.getEntity(key);
            //读取子表
            saReleasePojo.setItem(saReleaseitemMapper.getList(saReleasePojo.getId()));
            return saReleasePojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaReleasePojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReleasePojo> lst = saReleaseMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saReleaseitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaReleasePojo> pageInfo = new PageInfo<SaReleasePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaReleasePojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReleasePojo> lst = saReleaseMapper.getPageTh(queryParam);
            PageInfo<SaReleasePojo> pageInfo = new PageInfo<SaReleasePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saReleasePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaReleasePojo insert(SaReleasePojo saReleasePojo) {
//初始化NULL字段
        if (saReleasePojo.getReleasename() == null) saReleasePojo.setReleasename("");
        if (saReleasePojo.getProjectname() == null) saReleasePojo.setProjectname("");
        if (saReleasePojo.getReleasedate() == null) saReleasePojo.setReleasedate(new Date());
        if (saReleasePojo.getVersion() == null) saReleasePojo.setVersion("");
        if (saReleasePojo.getDescription() == null) saReleasePojo.setDescription("");
        if (saReleasePojo.getMilestone() == null) saReleasePojo.setMilestone(0);
        if (saReleasePojo.getProjectid() == null) saReleasePojo.setProjectid("");
        if (saReleasePojo.getProjectcode() == null) saReleasePojo.setProjectcode("");
        if (saReleasePojo.getRownum() == null) saReleasePojo.setRownum(0);
        if (saReleasePojo.getRemark() == null) saReleasePojo.setRemark("");
        if (saReleasePojo.getFinishmark() == null) saReleasePojo.setFinishmark(0);
        if (saReleasePojo.getClosed() == null) saReleasePojo.setClosed(0);
        if (saReleasePojo.getPhotourl1() == null) saReleasePojo.setPhotourl1("");
        if (saReleasePojo.getPhotourl2() == null) saReleasePojo.setPhotourl2("");
        if (saReleasePojo.getPhotourl3() == null) saReleasePojo.setPhotourl3("");
        if (saReleasePojo.getCreatebyid() == null) saReleasePojo.setCreatebyid("");
        if (saReleasePojo.getCreateby() == null) saReleasePojo.setCreateby("");
        if (saReleasePojo.getCreatedate() == null) saReleasePojo.setCreatedate(new Date());
        if (saReleasePojo.getListerid() == null) saReleasePojo.setListerid("");
        if (saReleasePojo.getLister() == null) saReleasePojo.setLister("");
        if (saReleasePojo.getModifydate() == null) saReleasePojo.setModifydate(new Date());
        if (saReleasePojo.getAssessor() == null) saReleasePojo.setAssessor("");
        if (saReleasePojo.getAssessorid() == null) saReleasePojo.setAssessorid("");
        if (saReleasePojo.getAssessdate() == null) saReleasePojo.setAssessdate(new Date());
        if (saReleasePojo.getCustom1() == null) saReleasePojo.setCustom1("");
        if (saReleasePojo.getCustom2() == null) saReleasePojo.setCustom2("");
        if (saReleasePojo.getCustom3() == null) saReleasePojo.setCustom3("");
        if (saReleasePojo.getCustom4() == null) saReleasePojo.setCustom4("");
        if (saReleasePojo.getCustom5() == null) saReleasePojo.setCustom5("");
        if (saReleasePojo.getCustom6() == null) saReleasePojo.setCustom6("");
        if (saReleasePojo.getCustom7() == null) saReleasePojo.setCustom7("");
        if (saReleasePojo.getCustom8() == null) saReleasePojo.setCustom8("");
        if (saReleasePojo.getCustom9() == null) saReleasePojo.setCustom9("");
        if (saReleasePojo.getCustom10() == null) saReleasePojo.setCustom10("");
        if (saReleasePojo.getTenantid() == null) saReleasePojo.setTenantid("");
        if (saReleasePojo.getTenantname() == null) saReleasePojo.setTenantname("");
        if (saReleasePojo.getRevision() == null) saReleasePojo.setRevision(0);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaReleaseEntity saReleaseEntity = new SaReleaseEntity();
        BeanUtils.copyProperties(saReleasePojo, saReleaseEntity);
        //设置id和新建日期
        saReleaseEntity.setId(id);
        saReleaseEntity.setRevision(1);  //乐观锁
        //插入主表
        this.saReleaseMapper.insert(saReleaseEntity);
        //Item子表处理
        List<SaReleaseitemPojo> lst = saReleasePojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaReleaseitemPojo saReleaseitemPojo : lst) {
                //初始化item的NULL
                SaReleaseitemPojo itemPojo = this.saReleaseitemService.clearNull(saReleaseitemPojo);
                SaReleaseitemEntity saReleaseitemEntity = new SaReleaseitemEntity();
                BeanUtils.copyProperties(itemPojo, saReleaseitemEntity);
                //设置id和Pid
                saReleaseitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saReleaseitemEntity.setPid(id);
                saReleaseitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saReleaseitemMapper.insert(saReleaseitemEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(saReleaseEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saReleasePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaReleasePojo update(SaReleasePojo saReleasePojo) {
        //主表更改
        SaReleaseEntity saReleaseEntity = new SaReleaseEntity();
        BeanUtils.copyProperties(saReleasePojo, saReleaseEntity);
        this.saReleaseMapper.update(saReleaseEntity);
        if (saReleasePojo.getItem() != null) {
            //Item子表处理
            List<SaReleaseitemPojo> lst = saReleasePojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saReleaseMapper.getDelItemIds(saReleasePojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saReleaseitemMapper.delete(lstDelIds.get(i));
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SaReleaseitemEntity saReleaseitemEntity = new SaReleaseitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SaReleaseitemPojo itemPojo = this.saReleaseitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, saReleaseitemEntity);
                        //设置id和Pid
                        saReleaseitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saReleaseitemEntity.setPid(saReleaseEntity.getId());  // 主表 id
                        saReleaseitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saReleaseitemMapper.insert(saReleaseitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), saReleaseitemEntity);
                        this.saReleaseitemMapper.update(saReleaseitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saReleaseEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
        SaReleasePojo saReleasePojo = this.getBillEntity(key);
        //Item子表处理
        List<SaReleaseitemPojo> lst = saReleasePojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                this.saReleaseitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saReleaseMapper.delete(key);
    }


    /**
     * 审核数据
     *
     * @param saReleasePojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaReleasePojo approval(SaReleasePojo saReleasePojo) {
        //主表更改
        SaReleaseEntity saReleaseEntity = new SaReleaseEntity();
        BeanUtils.copyProperties(saReleasePojo, saReleaseEntity);
        this.saReleaseMapper.approval(saReleaseEntity);
        //返回Bill实例
        return this.getBillEntity(saReleaseEntity.getId());
    }

    @Override
    public boolean checkExist(String remark) {
        int i = saReleaseMapper.countByRemark(remark);
        return i > 0;
    }
}
