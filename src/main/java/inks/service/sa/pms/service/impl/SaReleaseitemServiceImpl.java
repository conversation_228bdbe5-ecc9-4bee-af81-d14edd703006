package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaReleaseitemEntity;
import inks.service.sa.pms.domain.pojo.SaReleaseitemPojo;
import inks.service.sa.pms.mapper.SaReleaseitemMapper;
import inks.service.sa.pms.service.SaReleaseitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaReleaseitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-14 10:05:56
 */
@Service("saReleaseitemService")
public class SaReleaseitemServiceImpl implements SaReleaseitemService {
    @Resource
    private SaReleaseitemMapper saReleaseitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaReleaseitemPojo getEntity(String key) {
        return this.saReleaseitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaReleaseitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReleaseitemPojo> lst = saReleaseitemMapper.getPageList(queryParam);
            PageInfo<SaReleaseitemPojo> pageInfo = new PageInfo<SaReleaseitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaReleaseitemPojo> getList(String Pid) {
        try {
            List<SaReleaseitemPojo> lst = saReleaseitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saReleaseitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReleaseitemPojo insert(SaReleaseitemPojo saReleaseitemPojo) {
        //初始化item的NULL
        SaReleaseitemPojo itempojo = this.clearNull(saReleaseitemPojo);
        SaReleaseitemEntity saReleaseitemEntity = new SaReleaseitemEntity();
        BeanUtils.copyProperties(itempojo, saReleaseitemEntity);
        //生成雪花id
        saReleaseitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saReleaseitemEntity.setRevision(1);  //乐观锁
        this.saReleaseitemMapper.insert(saReleaseitemEntity);
        return this.getEntity(saReleaseitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saReleaseitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReleaseitemPojo update(SaReleaseitemPojo saReleaseitemPojo) {
        SaReleaseitemEntity saReleaseitemEntity = new SaReleaseitemEntity();
        BeanUtils.copyProperties(saReleaseitemPojo, saReleaseitemEntity);
        this.saReleaseitemMapper.update(saReleaseitemEntity);
        return this.getEntity(saReleaseitemEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saReleaseitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saReleaseitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReleaseitemPojo clearNull(SaReleaseitemPojo saReleaseitemPojo) {
        //初始化NULL字段
        if (saReleaseitemPojo.getPid() == null) saReleaseitemPojo.setPid("");
        if (saReleaseitemPojo.getRefno() == null) saReleaseitemPojo.setRefno("");
        if (saReleaseitemPojo.getBilldate() == null) saReleaseitemPojo.setBilldate(new Date());
        if (saReleaseitemPojo.getBilltype() == null) saReleaseitemPojo.setBilltype("");
        if (saReleaseitemPojo.getBilltitle() == null) saReleaseitemPojo.setBilltitle("");
        if (saReleaseitemPojo.getTodoid() == null) saReleaseitemPojo.setTodoid("");
        if (saReleaseitemPojo.getTdcontent() == null) saReleaseitemPojo.setTdcontent("");
        if (saReleaseitemPojo.getRownum() == null) saReleaseitemPojo.setRownum(0);
        if (saReleaseitemPojo.getRemark() == null) saReleaseitemPojo.setRemark("");
        if (saReleaseitemPojo.getFinishmark() == null) saReleaseitemPojo.setFinishmark(0);
        if (saReleaseitemPojo.getClosed() == null) saReleaseitemPojo.setClosed(0);
        if (saReleaseitemPojo.getImportantmark() == null) saReleaseitemPojo.setImportantmark(0);
        if (saReleaseitemPojo.getUrgentmark() == null) saReleaseitemPojo.setUrgentmark(0);
        if (saReleaseitemPojo.getTargetjson() == null) saReleaseitemPojo.setTargetjson("");
        if (saReleaseitemPojo.getPhotourl1() == null) saReleaseitemPojo.setPhotourl1("");
        if (saReleaseitemPojo.getPhotourl2() == null) saReleaseitemPojo.setPhotourl2("");
        if (saReleaseitemPojo.getPhotourl3() == null) saReleaseitemPojo.setPhotourl3("");
        if (saReleaseitemPojo.getPhotoname1() == null) saReleaseitemPojo.setPhotoname1("");
        if (saReleaseitemPojo.getPhotoname2() == null) saReleaseitemPojo.setPhotoname2("");
        if (saReleaseitemPojo.getPhotoname3() == null) saReleaseitemPojo.setPhotoname3("");
        if (saReleaseitemPojo.getCreatebyid() == null) saReleaseitemPojo.setCreatebyid("");
        if (saReleaseitemPojo.getCreateby() == null) saReleaseitemPojo.setCreateby("");
        if (saReleaseitemPojo.getCreatedate() == null) saReleaseitemPojo.setCreatedate(new Date());
        if (saReleaseitemPojo.getListerid() == null) saReleaseitemPojo.setListerid("");
        if (saReleaseitemPojo.getLister() == null) saReleaseitemPojo.setLister("");
        if (saReleaseitemPojo.getModifydate() == null) saReleaseitemPojo.setModifydate(new Date());
        if (saReleaseitemPojo.getAccepterid() == null) saReleaseitemPojo.setAccepterid("");
        if (saReleaseitemPojo.getAccepter() == null) saReleaseitemPojo.setAccepter("");
        if (saReleaseitemPojo.getCustom1() == null) saReleaseitemPojo.setCustom1("");
        if (saReleaseitemPojo.getCustom2() == null) saReleaseitemPojo.setCustom2("");
        if (saReleaseitemPojo.getCustom3() == null) saReleaseitemPojo.setCustom3("");
        if (saReleaseitemPojo.getCustom4() == null) saReleaseitemPojo.setCustom4("");
        if (saReleaseitemPojo.getCustom5() == null) saReleaseitemPojo.setCustom5("");
        if (saReleaseitemPojo.getCustom6() == null) saReleaseitemPojo.setCustom6("");
        if (saReleaseitemPojo.getCustom7() == null) saReleaseitemPojo.setCustom7("");
        if (saReleaseitemPojo.getCustom8() == null) saReleaseitemPojo.setCustom8("");
        if (saReleaseitemPojo.getCustom9() == null) saReleaseitemPojo.setCustom9("");
        if (saReleaseitemPojo.getCustom10() == null) saReleaseitemPojo.setCustom10("");
        if (saReleaseitemPojo.getTenantid() == null) saReleaseitemPojo.setTenantid("");
        if (saReleaseitemPojo.getDeptid() == null) saReleaseitemPojo.setDeptid("");
        if (saReleaseitemPojo.getTenantname() == null) saReleaseitemPojo.setTenantname("");
        if (saReleaseitemPojo.getRevision() == null) saReleaseitemPojo.setRevision(0);
        return saReleaseitemPojo;
    }
}
