package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaReportsdemandEntity;
import inks.service.sa.pms.domain.pojo.SaReportsdemandPojo;
import inks.service.sa.pms.mapper.SaReportsdemandMapper;
import inks.service.sa.pms.service.SaReportsdemandService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 报表模版需求(SaReportsdemand)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-22 15:54:02
 */
@Service("saReportsdemandService")
public class SaReportsdemandServiceImpl implements SaReportsdemandService {
    @Resource
    private SaReportsdemandMapper saReportsdemandMapper;

    @Override
    public SaReportsdemandPojo getEntity(String key) {
        return this.saReportsdemandMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaReportsdemandPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReportsdemandPojo> lst = saReportsdemandMapper.getPageList(queryParam);
            PageInfo<SaReportsdemandPojo> pageInfo = new PageInfo<SaReportsdemandPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaReportsdemandPojo insert(SaReportsdemandPojo saReportsdemandPojo) {
        //初始化NULL字段
        cleanNull(saReportsdemandPojo);
        // 报表模版需求再创建时自动生成随机 6位数字SerCode【服务码】,注意检查重复
        String serCode;
        do {
            serCode = String.format("%06d", ThreadLocalRandom.current().nextInt(1000000));
        } while (saReportsdemandMapper.checkSerCodeExists(serCode) > 0);
        saReportsdemandPojo.setSercode(serCode);

        SaReportsdemandEntity saReportsdemandEntity = new SaReportsdemandEntity();
        BeanUtils.copyProperties(saReportsdemandPojo, saReportsdemandEntity);
        //生成雪花id
        saReportsdemandEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saReportsdemandEntity.setRevision(1);  //乐观锁
        this.saReportsdemandMapper.insert(saReportsdemandEntity);
        return this.getEntity(saReportsdemandEntity.getId());
    }

    /**
     * 修改数据
     *
     * @param saReportsdemandPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReportsdemandPojo update(SaReportsdemandPojo saReportsdemandPojo) {
        SaReportsdemandEntity saReportsdemandEntity = new SaReportsdemandEntity();
        BeanUtils.copyProperties(saReportsdemandPojo, saReportsdemandEntity);
        this.saReportsdemandMapper.update(saReportsdemandEntity);
        return this.getEntity(saReportsdemandEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saReportsdemandMapper.delete(key);
    }

    @Override
    public SaReportsdemandPojo getEntityBySerCode(String key) {
        return this.saReportsdemandMapper.getEntityBySerCode(key);
    }

    private static void cleanNull(SaReportsdemandPojo saReportsdemandPojo) {
        if (saReportsdemandPojo.getModulecode() == null) saReportsdemandPojo.setModulecode("");
        if (saReportsdemandPojo.getRpttype() == null) saReportsdemandPojo.setRpttype("");
        if (saReportsdemandPojo.getRptname() == null) saReportsdemandPojo.setRptname("");
        if (saReportsdemandPojo.getUserdemand() == null) saReportsdemandPojo.setUserdemand("");
        if (saReportsdemandPojo.getCompany() == null) saReportsdemandPojo.setCompany("");
        if (saReportsdemandPojo.getOrggrfdata() == null) saReportsdemandPojo.setOrggrfdata("");
        if (saReportsdemandPojo.getGrfdata() == null) saReportsdemandPojo.setGrfdata("");
        if (saReportsdemandPojo.getSercode() == null) saReportsdemandPojo.setSercode("");
        if (saReportsdemandPojo.getOperator() == null) saReportsdemandPojo.setOperator("");
        if (saReportsdemandPojo.getOperatorid() == null) saReportsdemandPojo.setOperatorid("");
        if (saReportsdemandPojo.getFinishdate() == null) saReportsdemandPojo.setFinishdate(new Date());
        if (saReportsdemandPojo.getFinishdesc() == null) saReportsdemandPojo.setFinishdesc("");
        if (saReportsdemandPojo.getFinishmark() == null) saReportsdemandPojo.setFinishmark(0);
        if (saReportsdemandPojo.getEnabledmark() == null) saReportsdemandPojo.setEnabledmark(0);
        if (saReportsdemandPojo.getRownum() == null) saReportsdemandPojo.setRownum(0);
        if (saReportsdemandPojo.getRemark() == null) saReportsdemandPojo.setRemark("");
        if (saReportsdemandPojo.getCreateby() == null) saReportsdemandPojo.setCreateby("");
        if (saReportsdemandPojo.getCreatebyid() == null) saReportsdemandPojo.setCreatebyid("");
        if (saReportsdemandPojo.getCreatedate() == null) saReportsdemandPojo.setCreatedate(new Date());
        if (saReportsdemandPojo.getLister() == null) saReportsdemandPojo.setLister("");
        if (saReportsdemandPojo.getListerid() == null) saReportsdemandPojo.setListerid("");
        if (saReportsdemandPojo.getModifydate() == null) saReportsdemandPojo.setModifydate(new Date());
        if (saReportsdemandPojo.getCustom1() == null) saReportsdemandPojo.setCustom1("");
        if (saReportsdemandPojo.getCustom2() == null) saReportsdemandPojo.setCustom2("");
        if (saReportsdemandPojo.getCustom3() == null) saReportsdemandPojo.setCustom3("");
        if (saReportsdemandPojo.getCustom4() == null) saReportsdemandPojo.setCustom4("");
        if (saReportsdemandPojo.getCustom5() == null) saReportsdemandPojo.setCustom5("");
        if (saReportsdemandPojo.getTenantid() == null) saReportsdemandPojo.setTenantid("");
        if (saReportsdemandPojo.getTenantname() == null) saReportsdemandPojo.setTenantname("");
        if (saReportsdemandPojo.getRevision() == null) saReportsdemandPojo.setRevision(0);
    }

}
