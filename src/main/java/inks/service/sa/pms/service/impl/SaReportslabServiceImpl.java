package inks.service.sa.pms.service.impl;

import cn.hutool.core.util.RandomUtil;
import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaReportslabEntity;
import inks.service.sa.pms.domain.pojo.SaReportslabPojo;
import inks.service.sa.pms.mapper.SaReportslabMapper;
import inks.service.sa.pms.service.SaReportslabService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 报表中心(SaReportslab)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-01-23 09:44:16
 */
@Service("saReportslabService")
public class SaReportslabServiceImpl implements SaReportslabService {
    @Resource
    private SaReportslabMapper saReportslabMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaReportslabPojo getEntity(String key) {
        return this.saReportslabMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaReportslabPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReportslabPojo> lst = saReportslabMapper.getPageList(queryParam);
            PageInfo<SaReportslabPojo> pageInfo = new PageInfo<SaReportslabPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saReportslabPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReportslabPojo insert(SaReportslabPojo saReportslabPojo) {
        //初始化NULL字段
        if (saReportslabPojo.getGengroupid() == null) saReportslabPojo.setGengroupid("");
        if (saReportslabPojo.getModulecode() == null) saReportslabPojo.setModulecode("");
        if (saReportslabPojo.getSharecode() == null) saReportslabPojo.setSharecode("");
        if (saReportslabPojo.getRpttype() == null) saReportslabPojo.setRpttype("");
        if (saReportslabPojo.getRptname() == null) saReportslabPojo.setRptname("");
        if (saReportslabPojo.getRptdata() == null) saReportslabPojo.setRptdata("");
        if (saReportslabPojo.getPagerow() == null) saReportslabPojo.setPagerow(0);
        if (saReportslabPojo.getTempurl() == null) saReportslabPojo.setTempurl("");
        if (saReportslabPojo.getFilename() == null) saReportslabPojo.setFilename("");
        if (saReportslabPojo.getPrintersn() == null) saReportslabPojo.setPrintersn("");
        if (saReportslabPojo.getRownum() == null) saReportslabPojo.setRownum(0);
        if (saReportslabPojo.getEnabledmark() == null) saReportslabPojo.setEnabledmark(0);
        if (saReportslabPojo.getGrfdata() == null) saReportslabPojo.setGrfdata("");
        if (saReportslabPojo.getPaperlength() == null) saReportslabPojo.setPaperlength(0D);
        if (saReportslabPojo.getPaperwidth() == null) saReportslabPojo.setPaperwidth(0D);
        if (saReportslabPojo.getCoverimage() == null) saReportslabPojo.setCoverimage("");
        if (saReportslabPojo.getStarrating() == null) saReportslabPojo.setStarrating(0);
        if (saReportslabPojo.getLikescount() == null) saReportslabPojo.setLikescount(0);
        if(saReportslabPojo.getDownloadscount()==null) saReportslabPojo.setDownloadscount(0);
        if(saReportslabPojo.getMockdata()==null) saReportslabPojo.setMockdata("");
        if (saReportslabPojo.getRemark() == null) saReportslabPojo.setRemark("");
        if (saReportslabPojo.getCreateby() == null) saReportslabPojo.setCreateby("");
        if (saReportslabPojo.getCreatebyid() == null) saReportslabPojo.setCreatebyid("");
        if (saReportslabPojo.getCreatedate() == null) saReportslabPojo.setCreatedate(new Date());
        if (saReportslabPojo.getLister() == null) saReportslabPojo.setLister("");
        if (saReportslabPojo.getListerid() == null) saReportslabPojo.setListerid("");
        if (saReportslabPojo.getModifydate() == null) saReportslabPojo.setModifydate(new Date());
        if (saReportslabPojo.getCustom1() == null) saReportslabPojo.setCustom1("");
        if (saReportslabPojo.getCustom2() == null) saReportslabPojo.setCustom2("");
        if (saReportslabPojo.getCustom3() == null) saReportslabPojo.setCustom3("");
        if (saReportslabPojo.getCustom4() == null) saReportslabPojo.setCustom4("");
        if (saReportslabPojo.getCustom5() == null) saReportslabPojo.setCustom5("");
        if (saReportslabPojo.getTenantid() == null) saReportslabPojo.setTenantid("");
        if (saReportslabPojo.getTenantname() == null) saReportslabPojo.setTenantname("");
        if (saReportslabPojo.getRevision() == null) saReportslabPojo.setRevision(0);
        SaReportslabEntity saReportslabEntity = new SaReportslabEntity();
        // 生成随机分享码 6位数的小写字母和数字混合
        if (StringUtils.isBlank(saReportslabPojo.getSharecode())) {
            String shareCode = getShareCode();
            saReportslabPojo.setSharecode(shareCode);
        }else {
            // 检查分享码是否已存在
            int count = saReportslabMapper.countShareCode(saReportslabPojo.getSharecode());
            if (count > 0) {
                throw new BaseBusinessException("分享码已存在，请更换！");
            }
        }
        BeanUtils.copyProperties(saReportslabPojo, saReportslabEntity);
        //生成雪花id
        saReportslabEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saReportslabEntity.setRevision(1);  //乐观锁
        this.saReportslabMapper.insert(saReportslabEntity);
        return this.getEntity(saReportslabEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saReportslabPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaReportslabPojo update(SaReportslabPojo saReportslabPojo) {
        SaReportslabEntity saReportslabEntity = new SaReportslabEntity();
        // 检查分享码是否已存在
        String shareCode = saReportslabPojo.getSharecode();
        if (StringUtils.isNotBlank(shareCode)) {
            int count = saReportslabMapper.countShareCodeNoid(shareCode, saReportslabPojo.getId());
            if (count > 0) {
                throw new BaseBusinessException("分享码已存在，请更换！");
            }
        }
        saReportslabPojo.setSharecode(shareCode);
        BeanUtils.copyProperties(saReportslabPojo, saReportslabEntity);
        this.saReportslabMapper.update(saReportslabEntity);
        return this.getEntity(saReportslabEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saReportslabMapper.delete(key);
    }

    @Override
    public List<SaReportslabPojo> pullDefault(String moduleCode, LoginUser loginUser) {
        try {
            //搜索默认科目模板
            List<SaReportslabPojo> lstnew = new ArrayList<>();
            List<SaReportslabPojo> lstdef = this.saReportslabMapper.getListByDef(moduleCode);
            for (SaReportslabPojo item : lstdef) {
                SaReportslabPojo dbpojo = saReportslabMapper.getEntityByNameCode(item.getRptname(), item.getModulecode());
                if (dbpojo == null) {
                    SaReportslabPojo saReportsPojo = new SaReportslabPojo();
                    BeanUtils.copyProperties(item, saReportsPojo);
                    saReportsPojo.setId(UUID.randomUUID().toString());
//                    saReportsPojo.setTenantid(loginUser.getTenantid());
//                    saReportsPojo.setTenantname(loginUser.getTenantinfo().getTenantname());
//                    saReportsPojo.setCreatebyid(loginUser.getUserid());
//                    saReportsPojo.setCreateby(loginUser.getUsername());
                    saReportsPojo.setCreatedate(new Date());
//                    saReportsPojo.setListerid(loginUser.getUserid());
//                    saReportsPojo.setLister(loginUser.getUsername());
                    saReportsPojo.setModifydate(new Date());
                    SaReportslabEntity saReportsEntity = new SaReportslabEntity();
                    BeanUtils.copyProperties(saReportsPojo, saReportsEntity);
                    this.saReportslabMapper.insert(saReportsEntity);
                    lstnew.add(saReportsPojo);
                }
            }
            return lstnew;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaReportslabPojo> getPageListAll(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaReportslabPojo> lst = saReportslabMapper.getPageListAll(queryParam);
            PageInfo<SaReportslabPojo> pageInfo = new PageInfo<SaReportslabPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 通过功能号获得报表样式
     *
     * @param moduleCode 主键
     * @return 是否成功
     */
    @Override
    public List<SaReportslabPojo> getListByModuleCode(String moduleCode) {
        try {
            //自定义报表
            List<SaReportslabPojo> lst = saReportslabMapper.getListByModuleCode(moduleCode);
            //默认格式
            // List<CireportsPojo> lstdef = cireportsMapper.getListByModuleCode(moduleCode, InksConstants.DEFAULT_TENANT);
            //lst.addAll(lstdef);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public String getGrfDataByShareCode(String sharecode) {
        return this.saReportslabMapper.getGrfDataByShareCode(sharecode);
    }

    @Override
    public String initShareCode() {
        List<SaReportslabPojo> list = saReportslabMapper.getListNoShareCode();
        for (SaReportslabPojo pojo : list) {
            if (StringUtils.isBlank(pojo.getSharecode())) {
                String shareCode = getShareCode();
                pojo.setSharecode(shareCode);
                update(pojo);
            }
        }
        return "成功设置分享码：" + list.size() + "条";
    }

    // 生成随机分享码 6位数的小写字母和数字混合
    @Override
    public String getShareCode() {
        // 最多尝试生成 10 次，避免无限循环
        for (int attempt = 0; attempt < 10; attempt++) {
            // 生成随机分享码
            String shareCode = RandomUtil.randomString(6);

            // 检查分享码是否已存在
            int count = saReportslabMapper.countShareCode(shareCode);
            if (count == 0) {
                // 如果分享码不存在，直接返回
                return shareCode;
            }
        }
        // 如果生成的 10 次都存在，抛出异常
        throw new BaseBusinessException("无法生成唯一的分享码，请稍后重试！");
    }

}
