package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaRmsjustauthPojo;
import inks.service.sa.pms.domain.SaRmsjustauthEntity;
import inks.service.sa.pms.domain.pojo.SaScmjustauthPojo;
import inks.service.sa.pms.mapper.SaRmsjustauthMapper;
import inks.service.sa.pms.service.SaRmsjustauthService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * RMS第三方登录(SaRmsjustauth)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-29 10:59:38
 */
@Service("saRmsjustauthService")
public class SaRmsjustauthServiceImpl implements SaRmsjustauthService {
    @Resource
    private SaRmsjustauthMapper saRmsjustauthMapper;

    @Override
    public SaRmsjustauthPojo getEntity(String key) {
        return this.saRmsjustauthMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaRmsjustauthPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaRmsjustauthPojo> lst = saRmsjustauthMapper.getPageList(queryParam);
            PageInfo<SaRmsjustauthPojo> pageInfo = new PageInfo<SaRmsjustauthPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaRmsjustauthPojo insert(SaRmsjustauthPojo saRmsjustauthPojo) {
        // 检查ding/wxe/openid是否已存在
        if (isNotBlank(saRmsjustauthPojo.getAuthuuid())) {
            SaRmsjustauthPojo justauthPojo = saRmsjustauthMapper.getEntityByAuthuuid(saRmsjustauthPojo.getAuthuuid());
            if (justauthPojo != null) {
                throw new BaseBusinessException("该AuthUuid已绑定Rms账号:" + justauthPojo.getUsername());
            }
        }
        //初始化NULL字段
        cleanNull(saRmsjustauthPojo);
        SaRmsjustauthEntity saRmsjustauthEntity = new SaRmsjustauthEntity(); 
        BeanUtils.copyProperties(saRmsjustauthPojo,saRmsjustauthEntity);
        //生成雪花id
          saRmsjustauthEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saRmsjustauthEntity.setRevision(1);  //乐观锁
          this.saRmsjustauthMapper.insert(saRmsjustauthEntity);
        return this.getEntity(saRmsjustauthEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saRmsjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaRmsjustauthPojo update(SaRmsjustauthPojo saRmsjustauthPojo) {
        SaRmsjustauthEntity saRmsjustauthEntity = new SaRmsjustauthEntity(); 
        BeanUtils.copyProperties(saRmsjustauthPojo,saRmsjustauthEntity);
        this.saRmsjustauthMapper.update(saRmsjustauthEntity);
        return this.getEntity(saRmsjustauthEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saRmsjustauthMapper.delete(key) ;
    }
    

    private static void cleanNull(SaRmsjustauthPojo saRmsjustauthPojo) {
        if(saRmsjustauthPojo.getUserid()==null) saRmsjustauthPojo.setUserid("");
        if(saRmsjustauthPojo.getUsername()==null) saRmsjustauthPojo.setUsername("");
        if(saRmsjustauthPojo.getRealname()==null) saRmsjustauthPojo.setRealname("");
        if(saRmsjustauthPojo.getNickname()==null) saRmsjustauthPojo.setNickname("");
        if(saRmsjustauthPojo.getAuthtype()==null) saRmsjustauthPojo.setAuthtype("");
        if(saRmsjustauthPojo.getAuthuuid()==null) saRmsjustauthPojo.setAuthuuid("");
        if(saRmsjustauthPojo.getUnionid()==null) saRmsjustauthPojo.setUnionid("");
        if(saRmsjustauthPojo.getAuthavatar()==null) saRmsjustauthPojo.setAuthavatar("");
        if(saRmsjustauthPojo.getCreateby()==null) saRmsjustauthPojo.setCreateby("");
        if(saRmsjustauthPojo.getCreatebyid()==null) saRmsjustauthPojo.setCreatebyid("");
        if(saRmsjustauthPojo.getCreatedate()==null) saRmsjustauthPojo.setCreatedate(new Date());
        if(saRmsjustauthPojo.getLister()==null) saRmsjustauthPojo.setLister("");
        if(saRmsjustauthPojo.getListerid()==null) saRmsjustauthPojo.setListerid("");
        if(saRmsjustauthPojo.getModifydate()==null) saRmsjustauthPojo.setModifydate(new Date());
        if(saRmsjustauthPojo.getCustom1()==null) saRmsjustauthPojo.setCustom1("");
        if(saRmsjustauthPojo.getCustom2()==null) saRmsjustauthPojo.setCustom2("");
        if(saRmsjustauthPojo.getCustom3()==null) saRmsjustauthPojo.setCustom3("");
        if(saRmsjustauthPojo.getCustom4()==null) saRmsjustauthPojo.setCustom4("");
        if(saRmsjustauthPojo.getCustom5()==null) saRmsjustauthPojo.setCustom5("");
        if(saRmsjustauthPojo.getTenantid()==null) saRmsjustauthPojo.setTenantid("");
        if(saRmsjustauthPojo.getTenantname()==null) saRmsjustauthPojo.setTenantname("");
        if(saRmsjustauthPojo.getRevision()==null) saRmsjustauthPojo.setRevision(0);
   }
    @Override
    public int deleteByOpenid(String openid) {
        return this.saRmsjustauthMapper.deleteByOpenid(openid);
    }

    @Override
    public SaRmsjustauthPojo getEntityByUserid(String userid, String type) {
        return this.saRmsjustauthMapper.getEntityByUserid(userid, type);
    }

    @Override
    public SaRmsjustauthPojo getJustauthByUuid(String callbackuuid, String type) {
        return this.saRmsjustauthMapper.getJustauthByUuid(callbackuuid, type);
    }
}
