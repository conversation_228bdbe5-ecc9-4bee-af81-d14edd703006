package inks.service.sa.pms.service.impl;

import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.IpUtils;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import inks.service.sa.pms.domain.SaRmsuserEntity;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import inks.service.sa.pms.mapper.SaRmsuserMapper;
import inks.service.sa.pms.service.SaRmsuserService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
/**
 * RMS用户(SaRmsuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2024-09-29 10:59:38
 */
@Service("saRmsuserService")
public class SaRmsuserServiceImpl implements SaRmsuserService {
    @Resource
    private SaRmsuserMapper saRmsuserMapper;

    @Override
    public SaRmsuserPojo getEntity(String key) {
        return this.saRmsuserMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaRmsuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaRmsuserPojo> lst = saRmsuserMapper.getPageList(queryParam);
            PageInfo<SaRmsuserPojo> pageInfo = new PageInfo<SaRmsuserPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaRmsuserPojo insert(SaRmsuserPojo saRmsuserPojo) {
        //初始化NULL字段
        cleanNull(saRmsuserPojo);
        SaRmsuserEntity saRmsuserEntity = new SaRmsuserEntity(); 
        BeanUtils.copyProperties(saRmsuserPojo,saRmsuserEntity);
        //生成雪花id
          saRmsuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
          saRmsuserEntity.setRevision(1);  //乐观锁
          this.saRmsuserMapper.insert(saRmsuserEntity);
        return this.getEntity(saRmsuserEntity.getUserid());
  
    }

    /**
     * 修改数据
     *
     * @param saRmsuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaRmsuserPojo update(SaRmsuserPojo saRmsuserPojo) {
        SaRmsuserEntity saRmsuserEntity = new SaRmsuserEntity(); 
        BeanUtils.copyProperties(saRmsuserPojo,saRmsuserEntity);
        this.saRmsuserMapper.update(saRmsuserEntity);
        return this.getEntity(saRmsuserEntity.getUserid());
    }


    @Override
    public int delete(String key) {
        return this.saRmsuserMapper.delete(key) ;
    }
    

    private static void cleanNull(SaRmsuserPojo saRmsuserPojo) {
        if(saRmsuserPojo.getUsername()==null) saRmsuserPojo.setUsername("");
        if(saRmsuserPojo.getRealname()==null) saRmsuserPojo.setRealname("");
        if(saRmsuserPojo.getNickname()==null) saRmsuserPojo.setNickname("");
        if(saRmsuserPojo.getUserpassword()==null) saRmsuserPojo.setUserpassword("");
        if(saRmsuserPojo.getMobile()==null) saRmsuserPojo.setMobile("");
        if(saRmsuserPojo.getEmail()==null) saRmsuserPojo.setEmail("");
        if(saRmsuserPojo.getSex()==null) saRmsuserPojo.setSex(0);
        if(saRmsuserPojo.getLangcode()==null) saRmsuserPojo.setLangcode("");
        if(saRmsuserPojo.getAvatar()==null) saRmsuserPojo.setAvatar("");
        if(saRmsuserPojo.getUsertype()==null) saRmsuserPojo.setUsertype(0);
        if(saRmsuserPojo.getIsadmin()==null) saRmsuserPojo.setIsadmin(0);
        if(saRmsuserPojo.getDeptid()==null) saRmsuserPojo.setDeptid("");
        if(saRmsuserPojo.getDeptcode()==null) saRmsuserPojo.setDeptcode("");
        if(saRmsuserPojo.getDeptname()==null) saRmsuserPojo.setDeptname("");
        if(saRmsuserPojo.getIsdeptadmin()==null) saRmsuserPojo.setIsdeptadmin(0);
        if(saRmsuserPojo.getDeptrownum()==null) saRmsuserPojo.setDeptrownum(0);
        if(saRmsuserPojo.getRownum()==null) saRmsuserPojo.setRownum(0);
        if(saRmsuserPojo.getUserstatus()==null) saRmsuserPojo.setUserstatus(0);
        if(saRmsuserPojo.getUsercode()==null) saRmsuserPojo.setUsercode("");
        if(saRmsuserPojo.getGroupids()==null) saRmsuserPojo.setGroupids("");
        if(saRmsuserPojo.getGroupnames()==null) saRmsuserPojo.setGroupnames("");
        if(saRmsuserPojo.getRemark()==null) saRmsuserPojo.setRemark("");
        if(saRmsuserPojo.getCreateby()==null) saRmsuserPojo.setCreateby("");
        if(saRmsuserPojo.getCreatebyid()==null) saRmsuserPojo.setCreatebyid("");
        if(saRmsuserPojo.getCreatedate()==null) saRmsuserPojo.setCreatedate(new Date());
        if(saRmsuserPojo.getLister()==null) saRmsuserPojo.setLister("");
        if(saRmsuserPojo.getListerid()==null) saRmsuserPojo.setListerid("");
        if(saRmsuserPojo.getModifydate()==null) saRmsuserPojo.setModifydate(new Date());
        if(saRmsuserPojo.getTenantid()==null) saRmsuserPojo.setTenantid("");
        if(saRmsuserPojo.getTenantname()==null) saRmsuserPojo.setTenantname("");
        if(saRmsuserPojo.getRevision()==null) saRmsuserPojo.setRevision(0);
   }

    @Override
    public SaRmsuserPojo getEntityByUserName(String username) {
        return this.saRmsuserMapper.getEntityByUserName(username);
    }

    @Override
    public List<SaRmsuserPojo> getPageListByCustomer(String groupid) {
        try {
            List<SaRmsuserPojo> lst = saRmsuserMapper.getPageListByCustomer(groupid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaRmsuserPojo getEntityByOpenid(String openid) {
        return this.saRmsuserMapper.getEntityByOpenid(openid);
    }

    @Override
    public LoginUser login(String username, String password, HttpServletRequest request) {
        try {
            LoginUser loginUser = new LoginUser();
            //SELECT userid,username,password,realname
            SaRmsuserPojo saRmsuserPojo = this.saRmsuserMapper.getEntityByUNameAndPass(username, AESUtil.Encrypt(password));
            //如果loginUserDB不为空，登录成功
            if (saRmsuserPojo != null) {
                loginUser.setGroupids(saRmsuserPojo.getGroupids());
                loginUser.setUserid(saRmsuserPojo.getUserid());
                loginUser.setUsername(saRmsuserPojo.getUsername());
                loginUser.setAvatar(saRmsuserPojo.getAvatar());
                loginUser.setIpaddr(IpUtils.getIpAddr(request));
                loginUser.setRealname(saRmsuserPojo.getRealname());
                return loginUser;
            } else {
                throw new BaseBusinessException("用户名或密码错误");
            }
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public LoginUser scanLogin(String openid, String key, HttpServletRequest request) {
        //开始扫码登录(rms直接传入了openid)
        //通过openid获取是否有用户信息
        SaRmsuserPojo rmsUserDB = saRmsuserMapper.getEntityByOpenid(openid);
        return this.login(rmsUserDB.getUsername(), rmsUserDB.getUserpassword(), request);
    }

}
