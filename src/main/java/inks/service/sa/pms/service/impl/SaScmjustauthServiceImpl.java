package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaScmjustauthEntity;
import inks.service.sa.pms.domain.pojo.SaScmjustauthPojo;
import inks.service.sa.pms.mapper.SaScmjustauthMapper;
import inks.service.sa.pms.service.SaScmjustauthService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

import static org.apache.commons.lang3.StringUtils.*;

/**
 * SCM第三方登录(SaScmjustauth)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:31
 */
@Service("saScmjustauthService")
public class SaScmjustauthServiceImpl implements SaScmjustauthService {
    @Resource
    private SaScmjustauthMapper saScmjustauthMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaScmjustauthPojo getEntity(String key) {
        return this.saScmjustauthMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaScmjustauthPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaScmjustauthPojo> lst = saScmjustauthMapper.getPageList(queryParam);
            PageInfo<SaScmjustauthPojo> pageInfo = new PageInfo<SaScmjustauthPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saScmjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaScmjustauthPojo insert(SaScmjustauthPojo saScmjustauthPojo) {
        // 检查ding/wxe/openid是否已存在
        if (isNotBlank(saScmjustauthPojo.getAuthuuid())) {
            SaScmjustauthPojo justauthPojo = saScmjustauthMapper.getEntityByAuthuuid(saScmjustauthPojo.getAuthuuid());
            if (justauthPojo != null) {
                throw new BaseBusinessException("该AuthUuid已绑定Scm账号:" + justauthPojo.getUsername());
            }
        }
        //初始化NULL字段
        if (saScmjustauthPojo.getUserid() == null) saScmjustauthPojo.setUserid("");
        if (saScmjustauthPojo.getUsername() == null) saScmjustauthPojo.setUsername("");
        if (saScmjustauthPojo.getRealname() == null) saScmjustauthPojo.setRealname("");
        if (saScmjustauthPojo.getNickname() == null) saScmjustauthPojo.setNickname("");
        if (saScmjustauthPojo.getAuthtype() == null) saScmjustauthPojo.setAuthtype("");
        if (saScmjustauthPojo.getAuthuuid() == null) saScmjustauthPojo.setAuthuuid("");
        if (saScmjustauthPojo.getUnionid() == null) saScmjustauthPojo.setUnionid("");
        if (saScmjustauthPojo.getAuthavatar() == null) saScmjustauthPojo.setAuthavatar("");
        if (saScmjustauthPojo.getCreateby() == null) saScmjustauthPojo.setCreateby("");
        if (saScmjustauthPojo.getCreatebyid() == null) saScmjustauthPojo.setCreatebyid("");
        if (saScmjustauthPojo.getCreatedate() == null) saScmjustauthPojo.setCreatedate(new Date());
        if (saScmjustauthPojo.getLister() == null) saScmjustauthPojo.setLister("");
        if (saScmjustauthPojo.getListerid() == null) saScmjustauthPojo.setListerid("");
        if (saScmjustauthPojo.getModifydate() == null) saScmjustauthPojo.setModifydate(new Date());
        if (saScmjustauthPojo.getCustom1() == null) saScmjustauthPojo.setCustom1("");
        if (saScmjustauthPojo.getCustom2() == null) saScmjustauthPojo.setCustom2("");
        if (saScmjustauthPojo.getCustom3() == null) saScmjustauthPojo.setCustom3("");
        if (saScmjustauthPojo.getCustom4() == null) saScmjustauthPojo.setCustom4("");
        if (saScmjustauthPojo.getCustom5() == null) saScmjustauthPojo.setCustom5("");
        if (saScmjustauthPojo.getTenantid() == null) saScmjustauthPojo.setTenantid("");
        if (saScmjustauthPojo.getTenantname() == null) saScmjustauthPojo.setTenantname("");
        if (saScmjustauthPojo.getRevision() == null) saScmjustauthPojo.setRevision(0);
        SaScmjustauthEntity saScmjustauthEntity = new SaScmjustauthEntity();
        BeanUtils.copyProperties(saScmjustauthPojo, saScmjustauthEntity);
        //生成雪花id
        saScmjustauthEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saScmjustauthEntity.setRevision(1);  //乐观锁
        this.saScmjustauthMapper.insert(saScmjustauthEntity);
        return this.getEntity(saScmjustauthEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saScmjustauthPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaScmjustauthPojo update(SaScmjustauthPojo saScmjustauthPojo) {
        SaScmjustauthEntity saScmjustauthEntity = new SaScmjustauthEntity();
        BeanUtils.copyProperties(saScmjustauthPojo, saScmjustauthEntity);
        this.saScmjustauthMapper.update(saScmjustauthEntity);
        return this.getEntity(saScmjustauthEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saScmjustauthMapper.delete(key);
    }

    @Override
    public int deleteByOpenid(String openid) {
        return this.saScmjustauthMapper.deleteByOpenid(openid);
    }
}
