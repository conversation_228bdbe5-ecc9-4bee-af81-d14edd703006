package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.common.core.utils.AESUtil;
import inks.common.core.utils.IpUtils;
import inks.service.sa.pms.domain.SaScmuserEntity;
import inks.service.sa.pms.domain.pojo.SaRmsuserPojo;
import inks.service.sa.pms.domain.pojo.SaScmuserPojo;
import inks.service.sa.pms.mapper.SaScmuserMapper;
import inks.sa.common.core.service.SaRedisService;
import inks.service.sa.pms.service.SaScmuserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Date;
import java.util.List;

/**
 * SCM用户(SaScmuser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-08-29 10:02:31
 */
@Service("saScmuserService")
public class SaScmuserServiceImpl implements SaScmuserService {
    @Resource
    private SaScmuserMapper saScmuserMapper;
    @Resource
    private SaRedisService saRedisService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaScmuserPojo getEntity(String key) {
        return this.saScmuserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaScmuserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaScmuserPojo> lst = saScmuserMapper.getPageList(queryParam);
            PageInfo<SaScmuserPojo> pageInfo = new PageInfo<SaScmuserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saScmuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaScmuserPojo insert(SaScmuserPojo saScmuserPojo) {
        //初始化NULL字段
        if (saScmuserPojo.getUsername() == null) saScmuserPojo.setUsername("");
        if (saScmuserPojo.getRealname() == null) saScmuserPojo.setRealname("");
        if (saScmuserPojo.getNickname() == null) saScmuserPojo.setNickname("");
        if (saScmuserPojo.getUserpassword() == null) saScmuserPojo.setUserpassword("");
        if (saScmuserPojo.getMobile() == null) saScmuserPojo.setMobile("");
        if (saScmuserPojo.getEmail() == null) saScmuserPojo.setEmail("");
        if (saScmuserPojo.getSex() == null) saScmuserPojo.setSex(0);
        if (saScmuserPojo.getLangcode() == null) saScmuserPojo.setLangcode("");
        if (saScmuserPojo.getAvatar() == null) saScmuserPojo.setAvatar("");
        if (saScmuserPojo.getUsertype() == null) saScmuserPojo.setUsertype(0);
        if (saScmuserPojo.getGoodsid() == null) saScmuserPojo.setGoodsid("");
        if (saScmuserPojo.getIsadmin() == null) saScmuserPojo.setIsadmin(0);
        if (saScmuserPojo.getDeptid() == null) saScmuserPojo.setDeptid("");
        if (saScmuserPojo.getDeptcode() == null) saScmuserPojo.setDeptcode("");
        if (saScmuserPojo.getDeptname() == null) saScmuserPojo.setDeptname("");
        if (saScmuserPojo.getIsdeptadmin() == null) saScmuserPojo.setIsdeptadmin(0);
        if (saScmuserPojo.getDeptrownum() == null) saScmuserPojo.setDeptrownum(0);
        if (saScmuserPojo.getRownum() == null) saScmuserPojo.setRownum(0);
        if (saScmuserPojo.getUserstatus() == null) saScmuserPojo.setUserstatus(0);
        if (saScmuserPojo.getUsercode() == null) saScmuserPojo.setUsercode("");
        if (saScmuserPojo.getGroupids() == null) saScmuserPojo.setGroupids("");
        if (saScmuserPojo.getGroupnames() == null) saScmuserPojo.setGroupnames("");
        if (saScmuserPojo.getRemark() == null) saScmuserPojo.setRemark("");
        if (saScmuserPojo.getCreateby() == null) saScmuserPojo.setCreateby("");
        if (saScmuserPojo.getCreatebyid() == null) saScmuserPojo.setCreatebyid("");
        if (saScmuserPojo.getCreatedate() == null) saScmuserPojo.setCreatedate(new Date());
        if (saScmuserPojo.getLister() == null) saScmuserPojo.setLister("");
        if (saScmuserPojo.getListerid() == null) saScmuserPojo.setListerid("");
        if (saScmuserPojo.getModifydate() == null) saScmuserPojo.setModifydate(new Date());
        if (saScmuserPojo.getTenantid() == null) saScmuserPojo.setTenantid("");
        if (saScmuserPojo.getTenantname() == null) saScmuserPojo.setTenantname("");
        if (saScmuserPojo.getRevision() == null) saScmuserPojo.setRevision(0);
        SaScmuserEntity saScmuserEntity = new SaScmuserEntity();
        BeanUtils.copyProperties(saScmuserPojo, saScmuserEntity);
        //生成雪花id
        saScmuserEntity.setUserid(inksSnowflake.getSnowflake().nextIdStr());
        saScmuserEntity.setRevision(1);  //乐观锁
        this.saScmuserMapper.insert(saScmuserEntity);
        return this.getEntity(saScmuserEntity.getUserid());

    }

    /**
     * 修改数据
     *
     * @param saScmuserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaScmuserPojo update(SaScmuserPojo saScmuserPojo) {
        SaScmuserEntity saScmuserEntity = new SaScmuserEntity();
        BeanUtils.copyProperties(saScmuserPojo, saScmuserEntity);
        this.saScmuserMapper.update(saScmuserEntity);
        return this.getEntity(saScmuserEntity.getUserid());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saScmuserMapper.delete(key);
    }

    @Override
    public SaScmuserPojo getEntityByUserName(String username) {
        return this.saScmuserMapper.getEntityByUserName(username);
    }

    @Override
    public List<SaScmuserPojo> getPageListByCustomer(String groupid) {
        try {
            List<SaScmuserPojo> lst = saScmuserMapper.getPageListByCustomer(groupid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public SaScmuserPojo getEntityByOpenid(String openid) {
        return this.saScmuserMapper.getEntityByOpenid(openid);
    }

    @Override
    public LoginUser login(String username, String password, HttpServletRequest request) {
        try {
            LoginUser loginUser = new LoginUser();
            //SELECT userid,username,password,realname
            SaScmuserPojo saScmuserPojo = this.saScmuserMapper.getEntityByUNameAndPass(username, AESUtil.Encrypt(password));
            //如果loginUserDB不为空，登录成功
            if (saScmuserPojo != null) {
                loginUser.setGroupids(saScmuserPojo.getGroupids());
                loginUser.setUserid(saScmuserPojo.getUserid());
                loginUser.setUsername(saScmuserPojo.getUsername());
                loginUser.setAvatar(saScmuserPojo.getAvatar());
                loginUser.setIpaddr(IpUtils.getIpAddr(request));
                loginUser.setRealname(saScmuserPojo.getRealname());
                return loginUser;
            } else {
                throw new BaseBusinessException("用户名或密码错误");
            }
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    @Override
    public LoginUser scanLogin(String openid, String key, HttpServletRequest request) {

        //开始扫码登录(scm直接传入了openid)
        //通过openid获取是否有用户信息
        SaScmuserPojo scmUserDB = saScmuserMapper.getEntityByOpenid(openid);
        return this.login(scmUserDB.getUsername(), scmUserDB.getUserpassword(), request);
    }
}
