package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaSolutionEntity;
import inks.service.sa.pms.domain.pojo.SaSolutionPojo;
import inks.service.sa.pms.mapper.SaSolutionMapper;
import inks.service.sa.pms.service.SaSolutionService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 解决方案(SaSolution)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-14 16:03:36
 */
@Service("saSolutionService")
public class SaSolutionServiceImpl implements SaSolutionService {
    @Resource
    private SaSolutionMapper saSolutionMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaSolutionPojo getEntity(String key) {
        return this.saSolutionMapper.getEntity(key);
    }

    @Override
    public SaSolutionPojo getEntityBySolutionCode(String code) {
        return this.saSolutionMapper.getEntityBySolutionCode(code);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaSolutionPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSolutionPojo> lst = saSolutionMapper.getPageList(queryParam);
            PageInfo<SaSolutionPojo> pageInfo = new PageInfo<SaSolutionPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saSolutionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaSolutionPojo insert(SaSolutionPojo saSolutionPojo) {
        //初始化NULL字段
        if (saSolutionPojo.getGengroupid() == null) saSolutionPojo.setGengroupid("");
        if (saSolutionPojo.getSolutioncode() == null) saSolutionPojo.setSolutioncode("");
        if (saSolutionPojo.getSolutionname() == null) saSolutionPojo.setSolutionname("");
        if (saSolutionPojo.getDesciption() == null) saSolutionPojo.setDesciption("");
        if (saSolutionPojo.getEnabledmark() == null) saSolutionPojo.setEnabledmark(0);
        if (saSolutionPojo.getSolutionphote() == null) saSolutionPojo.setSolutionphote("");
        if (saSolutionPojo.getAuthor() == null) saSolutionPojo.setAuthor("");
        if (saSolutionPojo.getMarkdowndata() == null) saSolutionPojo.setMarkdowndata("");
        if (saSolutionPojo.getMdurl() == null) saSolutionPojo.setMdurl("");
        if (saSolutionPojo.getMdlooktimes() == null) saSolutionPojo.setMdlooktimes(0);
        if (saSolutionPojo.getAppurl() == null) saSolutionPojo.setAppurl("");
        if (saSolutionPojo.getDescjson() == null) saSolutionPojo.setDescjson("{}");
        if (saSolutionPojo.getRateval() == null) saSolutionPojo.setRateval(0);
        if (saSolutionPojo.getRownum() == null) saSolutionPojo.setRownum(0);
        if (saSolutionPojo.getRemark() == null) saSolutionPojo.setRemark("");
        if (saSolutionPojo.getPublicmark() == null) saSolutionPojo.setPublicmark(0);
        if (saSolutionPojo.getCreateby() == null) saSolutionPojo.setCreateby("");
        if (saSolutionPojo.getCreatebyid() == null) saSolutionPojo.setCreatebyid("");
        if (saSolutionPojo.getCreatedate() == null) saSolutionPojo.setCreatedate(new Date());
        if (saSolutionPojo.getLister() == null) saSolutionPojo.setLister("");
        if (saSolutionPojo.getListerid() == null) saSolutionPojo.setListerid("");
        if (saSolutionPojo.getModifydate() == null) saSolutionPojo.setModifydate(new Date());
        if (saSolutionPojo.getCustom1() == null) saSolutionPojo.setCustom1("");
        if (saSolutionPojo.getCustom2() == null) saSolutionPojo.setCustom2("");
        if (saSolutionPojo.getCustom3() == null) saSolutionPojo.setCustom3("");
        if (saSolutionPojo.getCustom4() == null) saSolutionPojo.setCustom4("");
        if (saSolutionPojo.getCustom5() == null) saSolutionPojo.setCustom5("");
        if (saSolutionPojo.getCustom6() == null) saSolutionPojo.setCustom6("");
        if (saSolutionPojo.getCustom7() == null) saSolutionPojo.setCustom7("");
        if (saSolutionPojo.getCustom8() == null) saSolutionPojo.setCustom8("");
        if (saSolutionPojo.getCustom9() == null) saSolutionPojo.setCustom9("");
        if (saSolutionPojo.getCustom10() == null) saSolutionPojo.setCustom10("");
        if (saSolutionPojo.getTenantid() == null) saSolutionPojo.setTenantid("");
        if (saSolutionPojo.getTenantname() == null) saSolutionPojo.setTenantname("");
        if (saSolutionPojo.getRevision() == null) saSolutionPojo.setRevision(0);

        if (StringUtils.isBlank(saSolutionPojo.getSolutioncode())) {
            throw new BaseBusinessException("解决方案编码不能为空");
        }
        // 校验SolutionCode唯一
        int count = saSolutionMapper.checkSolutionCodeUnique(saSolutionPojo.getSolutioncode(), null);
        if (count > 0) {
            throw new BaseBusinessException("解决方案编码已存在");
        }

        SaSolutionEntity saSolutionEntity = new SaSolutionEntity();
        BeanUtils.copyProperties(saSolutionPojo, saSolutionEntity);
        //生成雪花id
        saSolutionEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saSolutionEntity.setRevision(1);  //乐观锁
        this.saSolutionMapper.insert(saSolutionEntity);
        return this.getEntity(saSolutionEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saSolutionPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaSolutionPojo update(SaSolutionPojo saSolutionPojo) {
        if (StringUtils.isBlank(saSolutionPojo.getSolutioncode())) {
            throw new BaseBusinessException("解决方案编码不能为空");
        }
        // 校验SolutionCode唯一
        int count = saSolutionMapper.checkSolutionCodeUnique(saSolutionPojo.getSolutioncode(), saSolutionPojo.getId());
        if (count > 0) {
            throw new BaseBusinessException("解决方案编码已存在");
        }
        SaSolutionEntity saSolutionEntity = new SaSolutionEntity();
        BeanUtils.copyProperties(saSolutionPojo, saSolutionEntity);
        this.saSolutionMapper.update(saSolutionEntity);
        return this.getEntity(saSolutionEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saSolutionMapper.delete(key);
    }


}
