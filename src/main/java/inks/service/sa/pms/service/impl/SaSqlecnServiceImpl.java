package inks.service.sa.pms.service.impl;

import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.pojo.SaSqlecnPojo;
import inks.service.sa.pms.domain.SaSqlecnEntity;
import inks.service.sa.pms.mapper.SaSqlecnMapper;
import inks.service.sa.pms.service.SaSqlecnService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.PageHelper;
import org.springframework.beans.BeanUtils;
import inks.common.core.text.inksSnowflake;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
/**
 * SQL变更(SaSqlecn)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-02-19 14:27:49
 */
@Service("saSqlecnService")
public class SaSqlecnServiceImpl implements SaSqlecnService {
    @Resource
    private SaSqlecnMapper saSqlecnMapper;

    @Override
    public SaSqlecnPojo getEntity(String key) {
        return this.saSqlecnMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaSqlecnPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaSqlecnPojo> lst = saSqlecnMapper.getPageList(queryParam);
            PageInfo<SaSqlecnPojo> pageInfo = new PageInfo<SaSqlecnPojo>(lst);
            return pageInfo;
        }catch (Exception e){
            throw new BaseBusinessException(e.getMessage());
        } 
    }


    @Override
    public SaSqlecnPojo insert(SaSqlecnPojo saSqlecnPojo) {
        //初始化NULL字段
        cleanNull(saSqlecnPojo);
        SaSqlecnEntity saSqlecnEntity = new SaSqlecnEntity(); 
        BeanUtils.copyProperties(saSqlecnPojo,saSqlecnEntity);
        //生成雪花id
          saSqlecnEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
          saSqlecnEntity.setRevision(1);  //乐观锁
          this.saSqlecnMapper.insert(saSqlecnEntity);
        return this.getEntity(saSqlecnEntity.getId());
  
    }

    /**
     * 修改数据
     *
     * @param saSqlecnPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaSqlecnPojo update(SaSqlecnPojo saSqlecnPojo) {
        SaSqlecnEntity saSqlecnEntity = new SaSqlecnEntity(); 
        BeanUtils.copyProperties(saSqlecnPojo,saSqlecnEntity);
        this.saSqlecnMapper.update(saSqlecnEntity);
        return this.getEntity(saSqlecnEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saSqlecnMapper.delete(key) ;
    }
    
    @Override
    @Transactional
    public SaSqlecnPojo approval(SaSqlecnPojo saSqlecnPojo) {
        //主表更改
        SaSqlecnEntity saSqlecnEntity = new SaSqlecnEntity();
        BeanUtils.copyProperties(saSqlecnPojo,saSqlecnEntity);
        this.saSqlecnMapper.approval(saSqlecnEntity);
        //返回Bill实例
        return this.getEntity(saSqlecnEntity.getId());
    }

    private static void cleanNull(SaSqlecnPojo saSqlecnPojo) {
        if(saSqlecnPojo.getEcntype()==null) saSqlecnPojo.setEcntype("");
        if(saSqlecnPojo.getEcnreason()==null) saSqlecnPojo.setEcnreason("");
        if(saSqlecnPojo.getEcndesc()==null) saSqlecnPojo.setEcndesc("");
        if(saSqlecnPojo.getSqltext()==null) saSqlecnPojo.setSqltext("");
        if(saSqlecnPojo.getCode()==null) saSqlecnPojo.setCode("");
        if(saSqlecnPojo.getTimestamp()==null) saSqlecnPojo.setTimestamp(0L);
        if(saSqlecnPojo.getRollbacktext()==null) saSqlecnPojo.setRollbacktext("");
        if(saSqlecnPojo.getProjectid()==null) saSqlecnPojo.setProjectid("");
        if(saSqlecnPojo.getImpactscope()==null) saSqlecnPojo.setImpactscope("");
        if(saSqlecnPojo.getRisk()==null) saSqlecnPojo.setRisk("");
        if(saSqlecnPojo.getOaflowmark()==null) saSqlecnPojo.setOaflowmark(0);
        if(saSqlecnPojo.getAssessor()==null) saSqlecnPojo.setAssessor("");
        if(saSqlecnPojo.getAssessorid()==null) saSqlecnPojo.setAssessorid("");
        if(saSqlecnPojo.getAssessdate()==null) saSqlecnPojo.setAssessdate(new Date());
        if(saSqlecnPojo.getRownum()==null) saSqlecnPojo.setRownum(0);
        if(saSqlecnPojo.getRemark()==null) saSqlecnPojo.setRemark("");
        if(saSqlecnPojo.getCreateby()==null) saSqlecnPojo.setCreateby("");
        if(saSqlecnPojo.getCreatebyid()==null) saSqlecnPojo.setCreatebyid("");
        if(saSqlecnPojo.getCreatedate()==null) saSqlecnPojo.setCreatedate(new Date());
        if(saSqlecnPojo.getLister()==null) saSqlecnPojo.setLister("");
        if(saSqlecnPojo.getListerid()==null) saSqlecnPojo.setListerid("");
        if(saSqlecnPojo.getModifydate()==null) saSqlecnPojo.setModifydate(new Date());
        if(saSqlecnPojo.getCustom1()==null) saSqlecnPojo.setCustom1("");
        if(saSqlecnPojo.getCustom2()==null) saSqlecnPojo.setCustom2("");
        if(saSqlecnPojo.getCustom3()==null) saSqlecnPojo.setCustom3("");
        if(saSqlecnPojo.getCustom4()==null) saSqlecnPojo.setCustom4("");
        if(saSqlecnPojo.getCustom5()==null) saSqlecnPojo.setCustom5("");
        if(saSqlecnPojo.getTenantid()==null) saSqlecnPojo.setTenantid("");
        if(saSqlecnPojo.getTenantname()==null) saSqlecnPojo.setTenantname("");
        if(saSqlecnPojo.getRevision()==null) saSqlecnPojo.setRevision(0);
   }

    @Override
    public void updateOaflowmark(SaSqlecnPojo saDemandsubmitPojo) {
        this.saSqlecnMapper.updateOaflowmark(saDemandsubmitPojo);
    }

    @Override
    public List<SaSqlecnPojo> pullList(List<String> codeList, Long timestamp, int size) {
        return this.saSqlecnMapper.pullList(codeList, timestamp, size);
    }
}
