package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.SaDemandEntity;
import inks.service.sa.pms.domain.SaTodoEntity;
import inks.service.sa.pms.domain.pojo.SaTodoPojo;
import inks.service.sa.pms.mapper.SaDemandMapper;
import inks.service.sa.pms.mapper.SaTodoMapper;
import inks.service.sa.pms.service.SaTodoService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Todo(SaTodo)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-02-18 11:12:38
 */
@Service("saTodoService")
public class SaTodoServiceImpl implements SaTodoService {
    @Resource
    private SaTodoMapper saTodoMapper;
    @Resource
    private SaDemandMapper saDemandMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTodoPojo getEntity(String key) {
        return this.saTodoMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTodoPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTodoPojo> lst = saTodoMapper.getPageList(queryParam);
            PageInfo<SaTodoPojo> pageInfo = new PageInfo<SaTodoPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saTodoPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTodoPojo insert(SaTodoPojo saTodoPojo) {
        //初始化NULL字段
        if (saTodoPojo.getTdgroupid() == null) saTodoPojo.setTdgroupid("");
        if (saTodoPojo.getParentid() == null) saTodoPojo.setParentid("");
        if (saTodoPojo.getRefno() == null) saTodoPojo.setRefno("");
        if (saTodoPojo.getBilldate() == null) saTodoPojo.setBilldate(new Date());
        if (saTodoPojo.getBilltype() == null) saTodoPojo.setBilltype("");
        if (saTodoPojo.getBilltitle() == null) saTodoPojo.setBilltitle("");
        if(saTodoPojo.getTdcontent()==null) saTodoPojo.setTdcontent("");
        if(saTodoPojo.getFinishmark()==null) saTodoPojo.setFinishmark(0);
        if(saTodoPojo.getLevel()==null) saTodoPojo.setLevel(0);
        if(saTodoPojo.getGoodsid()==null) saTodoPojo.setGoodsid("");
        if (saTodoPojo.getRownum() == null) saTodoPojo.setRownum(0);
        if (saTodoPojo.getPlandate() == null) saTodoPojo.setPlandate(new Date());
        if (saTodoPojo.getRemark() == null) saTodoPojo.setRemark("");
        if (saTodoPojo.getClosed() == null) saTodoPojo.setClosed(0);
        if (saTodoPojo.getLevel() == null) saTodoPojo.setLevel(0);
        if (saTodoPojo.getGoodsid() == null) saTodoPojo.setGoodsid("");
        if (saTodoPojo.getPublicmark() == null) saTodoPojo.setPublicmark(0);
        if (saTodoPojo.getImportantmark() == null) saTodoPojo.setImportantmark(0);
        if (saTodoPojo.getUrgentmark() == null) saTodoPojo.setUrgentmark(0);
        if (saTodoPojo.getTargetjson() == null) saTodoPojo.setTargetjson("");
        if (saTodoPojo.getStatecode() == null) saTodoPojo.setStatecode("");
        if (saTodoPojo.getStatedate() == null) saTodoPojo.setStatedate(new Date());
        if (saTodoPojo.getPhotourl1() == null) saTodoPojo.setPhotourl1("");
        if (saTodoPojo.getPhotourl2() == null) saTodoPojo.setPhotourl2("");
        if (saTodoPojo.getPhotourl3() == null) saTodoPojo.setPhotourl3("");
        if (saTodoPojo.getPhotoname1() == null) saTodoPojo.setPhotoname1("");
        if (saTodoPojo.getPhotoname2() == null) saTodoPojo.setPhotoname2("");
        if (saTodoPojo.getPhotoname3() == null) saTodoPojo.setPhotoname3("");
        if (saTodoPojo.getCreatebyid() == null) saTodoPojo.setCreatebyid("");
        if (saTodoPojo.getCreateby() == null) saTodoPojo.setCreateby("");
        if (saTodoPojo.getCreatedate() == null) saTodoPojo.setCreatedate(new Date());
        if (saTodoPojo.getListerid() == null) saTodoPojo.setListerid("");
        if (saTodoPojo.getLister() == null) saTodoPojo.setLister("");
        if (saTodoPojo.getModifydate() == null) saTodoPojo.setModifydate(new Date());
        if (saTodoPojo.getModulecode() == null) saTodoPojo.setModulecode("");
        if (saTodoPojo.getCiteuid() == null) saTodoPojo.setCiteuid("");
        if (saTodoPojo.getCiteid() == null) saTodoPojo.setCiteid(0);
        if (saTodoPojo.getAccepterid() == null) saTodoPojo.setAccepterid("");
        if (saTodoPojo.getAccepter() == null) saTodoPojo.setAccepter("");
        if (saTodoPojo.getStartplan() == null) saTodoPojo.setStartplan(new Date());
        if (saTodoPojo.getPlanhours() == null) saTodoPojo.setPlanhours(0);
        if (saTodoPojo.getFinishhours() == null) saTodoPojo.setFinishhours(0);
        if (saTodoPojo.getEndplan() == null) saTodoPojo.setEndplan(new Date());
        //if (saTodoPojo.getStartactual() == null) saTodoPojo.setStartactual(new Date());
        //if (saTodoPojo.getEndactual() == null) saTodoPojo.setEndactual(new Date());
        if (saTodoPojo.getStartremark() == null) saTodoPojo.setStartremark("");
        if (saTodoPojo.getEndremark() == null) saTodoPojo.setEndremark("");
        if (saTodoPojo.getCustom1() == null) saTodoPojo.setCustom1("");
        if (saTodoPojo.getCustom2() == null) saTodoPojo.setCustom2("");
        if (saTodoPojo.getCustom3() == null) saTodoPojo.setCustom3("");
        if (saTodoPojo.getCustom4() == null) saTodoPojo.setCustom4("");
        if (saTodoPojo.getCustom5() == null) saTodoPojo.setCustom5("");
        if (saTodoPojo.getCustom6() == null) saTodoPojo.setCustom6("");
        if (saTodoPojo.getCustom7() == null) saTodoPojo.setCustom7("");
        if (saTodoPojo.getCustom8() == null) saTodoPojo.setCustom8("");
        if (saTodoPojo.getCustom9() == null) saTodoPojo.setCustom9("");
        if (saTodoPojo.getCustom10() == null) saTodoPojo.setCustom10("");
        if (saTodoPojo.getTenantid() == null) saTodoPojo.setTenantid("");
        if (saTodoPojo.getDeptid() == null) saTodoPojo.setDeptid("");
        if (saTodoPojo.getTenantname() == null) saTodoPojo.setTenantname("");
        if (saTodoPojo.getRevision() == null) saTodoPojo.setRevision(0);
        SaTodoEntity saTodoEntity = new SaTodoEntity();
        BeanUtils.copyProperties(saTodoPojo, saTodoEntity);

        saTodoEntity.setId(UUID.randomUUID().toString());
        saTodoEntity.setRevision(1);  //乐观锁
        this.saTodoMapper.insert(saTodoEntity);
        SaTodoPojo todoPojoDB = this.getEntity(saTodoEntity.getId());
        //如果有Parentid，则为需求单转为Todo：则更新需求单的Todoid,表示已转为Todo
        if (StringUtils.isNotBlank(saTodoPojo.getParentid())) {
            SaDemandEntity saDemandEntity = new SaDemandEntity();
            saDemandEntity.setId(saTodoPojo.getParentid());
            saDemandEntity.setTodoid(todoPojoDB.getId());
            this.saDemandMapper.update(saDemandEntity);
        }
        return todoPojoDB;

    }

    /**
     * 修改数据
     *
     * @param saTodoPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTodoPojo update(SaTodoPojo saTodoPojo) {
        SaTodoEntity saTodoEntity = new SaTodoEntity();
        BeanUtils.copyProperties(saTodoPojo, saTodoEntity);
        this.saTodoMapper.update(saTodoEntity);
        return this.getEntity(saTodoEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saTodoMapper.delete(key);
    }

    //开始To do StartActual赋值
    @Override
    @Transactional
    public SaTodoPojo start(String key, LoginUser loginUser) {
        Date now = new Date();
        SaTodoPojo saTodoPojo = new SaTodoPojo();
        saTodoPojo.setTenantid(loginUser.getTenantid());
        saTodoPojo.setId(key);
        saTodoPojo.setStartactual(now);
        saTodoPojo.setModifydate(now);
        saTodoPojo.setLister(loginUser.getRealname());
        saTodoPojo.setListerid(loginUser.getUserid());
        return this.update(saTodoPojo);
    }

    //完成To do EndActual赋值 FinishMark=1
    @Override
    @Transactional
    public SaTodoPojo finish(String key, LoginUser loginUser) {
        Date now = new Date();
        SaTodoPojo saTodoPojo = new SaTodoPojo();
        saTodoPojo.setTenantid(loginUser.getTenantid());
        saTodoPojo.setId(key);
        saTodoPojo.setEndactual(now);
        saTodoPojo.setModifydate(now);
        saTodoPojo.setLister(loginUser.getRealname());
        saTodoPojo.setListerid(loginUser.getUserid());
        saTodoPojo.setFinishmark(1);
        return this.update(saTodoPojo);
    }

    //"撤回完成To do EndActual赋值null FinishMark=0
    @Override
    @Transactional
    public SaTodoPojo recall(String key, LoginUser loginUser) {
        Date now = new Date();
        SaTodoPojo saTodoPojo = new SaTodoPojo();
        saTodoPojo.setTenantid(loginUser.getTenantid());
        saTodoPojo.setId(key);
        //saTodoPojo.setEndactual(null); 赋值null
        saTodoMapper.setNullEndActual(key);
        saTodoPojo.setModifydate(now);
        saTodoPojo.setLister(loginUser.getRealname());
        saTodoPojo.setListerid(loginUser.getUserid());
        saTodoPojo.setFinishmark(0);
        return this.update(saTodoPojo);
    }
}
