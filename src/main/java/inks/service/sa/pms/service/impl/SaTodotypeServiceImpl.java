package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaTodotypeEntity;
import inks.service.sa.pms.domain.pojo.SaTodotypePojo;
import inks.service.sa.pms.mapper.SaTodotypeMapper;
import inks.service.sa.pms.service.SaTodotypeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaTodotype)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-10 10:53:33
 */
@Service("saTodotypeService")
public class SaTodotypeServiceImpl implements SaTodotypeService {
    @Resource
    private SaTodotypeMapper saTodotypeMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTodotypePojo getEntity(String key) {
        return this.saTodotypeMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTodotypePojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTodotypePojo> lst = saTodotypeMapper.getPageList(queryParam);
            PageInfo<SaTodotypePojo> pageInfo = new PageInfo<SaTodotypePojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saTodotypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTodotypePojo insert(SaTodotypePojo saTodotypePojo) {
        //初始化NULL字段
        if (saTodotypePojo.getTypename() == null) saTodotypePojo.setTypename("");
        if (saTodotypePojo.getTypecode() == null) saTodotypePojo.setTypecode("");
        if (saTodotypePojo.getTypecolor() == null) saTodotypePojo.setTypecolor("");
        if (saTodotypePojo.getTypeheight() == null) saTodotypePojo.setTypeheight(0);
        if (saTodotypePojo.getTypeicon() == null) saTodotypePojo.setTypeicon("");
        if (saTodotypePojo.getRownum() == null) saTodotypePojo.setRownum(0);
        if (saTodotypePojo.getCreatebyid() == null) saTodotypePojo.setCreatebyid("");
        if (saTodotypePojo.getCreateby() == null) saTodotypePojo.setCreateby("");
        if (saTodotypePojo.getCreatedate() == null) saTodotypePojo.setCreatedate(new Date());
        if (saTodotypePojo.getListerid() == null) saTodotypePojo.setListerid("");
        if (saTodotypePojo.getLister() == null) saTodotypePojo.setLister("");
        if (saTodotypePojo.getModifydate() == null) saTodotypePojo.setModifydate(new Date());
        if (saTodotypePojo.getModulecode() == null) saTodotypePojo.setModulecode("");
        if (saTodotypePojo.getCustom1() == null) saTodotypePojo.setCustom1("");
        if (saTodotypePojo.getCustom2() == null) saTodotypePojo.setCustom2("");
        if (saTodotypePojo.getCustom3() == null) saTodotypePojo.setCustom3("");
        if (saTodotypePojo.getCustom4() == null) saTodotypePojo.setCustom4("");
        if (saTodotypePojo.getCustom5() == null) saTodotypePojo.setCustom5("");
        if (saTodotypePojo.getCustom6() == null) saTodotypePojo.setCustom6("");
        if (saTodotypePojo.getCustom7() == null) saTodotypePojo.setCustom7("");
        if (saTodotypePojo.getCustom8() == null) saTodotypePojo.setCustom8("");
        if (saTodotypePojo.getCustom9() == null) saTodotypePojo.setCustom9("");
        if (saTodotypePojo.getCustom10() == null) saTodotypePojo.setCustom10("");
        if (saTodotypePojo.getTenantid() == null) saTodotypePojo.setTenantid("");
        if (saTodotypePojo.getDeptid() == null) saTodotypePojo.setDeptid("");
        if (saTodotypePojo.getTenantname() == null) saTodotypePojo.setTenantname("");
        if (saTodotypePojo.getRevision() == null) saTodotypePojo.setRevision(0);
        SaTodotypeEntity saTodotypeEntity = new SaTodotypeEntity();
        BeanUtils.copyProperties(saTodotypePojo, saTodotypeEntity);
        //生成雪花id
        saTodotypeEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saTodotypeEntity.setRevision(1);  //乐观锁
        this.saTodotypeMapper.insert(saTodotypeEntity);
        return this.getEntity(saTodotypeEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saTodotypePojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTodotypePojo update(SaTodotypePojo saTodotypePojo) {
        SaTodotypeEntity saTodotypeEntity = new SaTodotypeEntity();
        BeanUtils.copyProperties(saTodotypePojo, saTodotypeEntity);
        this.saTodotypeMapper.update(saTodotypeEntity);
        return this.getEntity(saTodotypeEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saTodotypeMapper.delete(key);
    }


}
