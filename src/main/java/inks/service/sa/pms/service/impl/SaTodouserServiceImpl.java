package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.SaTodouserEntity;
import inks.service.sa.pms.domain.pojo.SaTodouserPojo;
import inks.service.sa.pms.mapper.SaTodouserMapper;
import inks.service.sa.pms.service.SaTodouserService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * Todo用户列表(SaTodouser)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-03-03 14:07:16
 */
@Service("saTodouserService")
public class SaTodouserServiceImpl implements SaTodouserService {
    @Resource
    private SaTodouserMapper saTodouserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTodouserPojo getEntity(String key) {
        return this.saTodouserMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTodouserPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTodouserPojo> lst = saTodouserMapper.getPageList(queryParam);
            PageInfo<SaTodouserPojo> pageInfo = new PageInfo<SaTodouserPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saTodouserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTodouserPojo insert(SaTodouserPojo saTodouserPojo) {
        //初始化NULL字段
        if (saTodouserPojo.getName() == null) saTodouserPojo.setName("");
        if (saTodouserPojo.getUserid() == null) saTodouserPojo.setUserid("");
        if (saTodouserPojo.getBackcolorargb() == null) saTodouserPojo.setBackcolorargb("");
        if (saTodouserPojo.getAvatar() == null) saTodouserPojo.setAvatar("");
        if (saTodouserPojo.getFilename() == null) saTodouserPojo.setFilename("");
        if (saTodouserPojo.getDirname() == null) saTodouserPojo.setDirname("");
        if (saTodouserPojo.getPhone() == null) saTodouserPojo.setPhone("");
        if (saTodouserPojo.getEmail() == null) saTodouserPojo.setEmail("");
        if (saTodouserPojo.getSex() == null) saTodouserPojo.setSex(0);
        if (saTodouserPojo.getUserstate() == null) saTodouserPojo.setUserstate(0);
        if (saTodouserPojo.getRemark() == null) saTodouserPojo.setRemark("");
        if (saTodouserPojo.getRownum() == null) saTodouserPojo.setRownum(0);
        if (saTodouserPojo.getCreatebyid() == null) saTodouserPojo.setCreatebyid("");
        if (saTodouserPojo.getCreateby() == null) saTodouserPojo.setCreateby("");
        if (saTodouserPojo.getCreatedate() == null) saTodouserPojo.setCreatedate(new Date());
        if (saTodouserPojo.getListerid() == null) saTodouserPojo.setListerid("");
        if (saTodouserPojo.getLister() == null) saTodouserPojo.setLister("");
        if (saTodouserPojo.getModifydate() == null) saTodouserPojo.setModifydate(new Date());
        if (saTodouserPojo.getModulecode() == null) saTodouserPojo.setModulecode("");
        if (saTodouserPojo.getCustom1() == null) saTodouserPojo.setCustom1("");
        if (saTodouserPojo.getCustom2() == null) saTodouserPojo.setCustom2("");
        if (saTodouserPojo.getCustom3() == null) saTodouserPojo.setCustom3("");
        if (saTodouserPojo.getCustom4() == null) saTodouserPojo.setCustom4("");
        if (saTodouserPojo.getCustom5() == null) saTodouserPojo.setCustom5("");
        if (saTodouserPojo.getCustom6() == null) saTodouserPojo.setCustom6("");
        if (saTodouserPojo.getCustom7() == null) saTodouserPojo.setCustom7("");
        if (saTodouserPojo.getCustom8() == null) saTodouserPojo.setCustom8("");
        if (saTodouserPojo.getCustom9() == null) saTodouserPojo.setCustom9("");
        if (saTodouserPojo.getCustom10() == null) saTodouserPojo.setCustom10("");
        if (saTodouserPojo.getTenantid() == null) saTodouserPojo.setTenantid("");
        if (saTodouserPojo.getDeptid() == null) saTodouserPojo.setDeptid("");
        if (saTodouserPojo.getTenantname() == null) saTodouserPojo.setTenantname("");
        if (saTodouserPojo.getRevision() == null) saTodouserPojo.setRevision(0);
        SaTodouserEntity saTodouserEntity = new SaTodouserEntity();
        BeanUtils.copyProperties(saTodouserPojo, saTodouserEntity);

        saTodouserEntity.setId(UUID.randomUUID().toString());
        saTodouserEntity.setRevision(1);  //乐观锁
        this.saTodouserMapper.insert(saTodouserEntity);
        return this.getEntity(saTodouserEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saTodouserPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTodouserPojo update(SaTodouserPojo saTodouserPojo) {
        SaTodouserEntity saTodouserEntity = new SaTodouserEntity();
        BeanUtils.copyProperties(saTodouserPojo, saTodouserEntity);
        this.saTodouserMapper.update(saTodouserEntity);
        return this.getEntity(saTodouserEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saTodouserMapper.delete(key);
    }


}
