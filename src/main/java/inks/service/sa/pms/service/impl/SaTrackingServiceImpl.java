package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.LoginUser;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaTrackingEntity;
import inks.service.sa.pms.domain.SaTrackingitemEntity;
import inks.service.sa.pms.domain.pojo.SaTrackingPojo;
import inks.service.sa.pms.domain.pojo.SaTrackingitemPojo;
import inks.service.sa.pms.domain.pojo.SaTrackingitemdetailPojo;
import inks.service.sa.pms.mapper.SaTrackingMapper;
import inks.service.sa.pms.mapper.SaTrackingitemMapper;
import inks.service.sa.pms.service.SaTrackingService;
import inks.service.sa.pms.service.SaTrackingitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * 跟踪表主表(SaTracking)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-26 09:07:46
 */
@Service("saTrackingService")
public class SaTrackingServiceImpl implements SaTrackingService {
    @Resource
    private SaTrackingMapper saTrackingMapper;

    @Resource
    private SaTrackingitemMapper saTrackingitemMapper;

    /**
     * 服务对象Item
     */
    @Resource
    private SaTrackingitemService saTrackingitemService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTrackingPojo getEntity(String key) {
        return this.saTrackingMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTrackingitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTrackingitemdetailPojo> lst = saTrackingMapper.getPageList(queryParam);
            PageInfo<SaTrackingitemdetailPojo> pageInfo = new PageInfo<SaTrackingitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTrackingPojo getBillEntity(String key) {
        try {
            //读取主表
            SaTrackingPojo saTrackingPojo = this.saTrackingMapper.getEntity(key);
            //读取子表
            saTrackingPojo.setItem(saTrackingitemMapper.getList(saTrackingPojo.getId()));
            return saTrackingPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTrackingPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTrackingPojo> lst = saTrackingMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saTrackingitemMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaTrackingPojo> pageInfo = new PageInfo<SaTrackingPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTrackingPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTrackingPojo> lst = saTrackingMapper.getPageTh(queryParam);
            PageInfo<SaTrackingPojo> pageInfo = new PageInfo<SaTrackingPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saTrackingPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaTrackingPojo insert(SaTrackingPojo saTrackingPojo) {
        //初始化NULL字段
        cleanNull(saTrackingPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaTrackingEntity saTrackingEntity = new SaTrackingEntity();
        BeanUtils.copyProperties(saTrackingPojo, saTrackingEntity);
        //设置id和新建日期
        saTrackingEntity.setId(id);
        saTrackingEntity.setRevision(1);  //乐观锁
        // 获取子表
        List<SaTrackingitemPojo> lst = saTrackingPojo.getItem();
        Date maxPlanEndDate = null;
        int planFinishCount = 0;
        Date maxCompletionDate = null;
        int billFinishCount = 0;
        // 判断lst中每个元素的Completiondate是否全部都不为空
        boolean allCompletionDatesNotNull = true;
        for (SaTrackingitemPojo item : lst) {
            Date planEndDate = item.getPlanend();
            Date completionDate = item.getCompletiondate();
            // 处理 Planend 字段
            if (planEndDate != null) {
                // 更新item最大计划结束时间
                if (maxPlanEndDate == null || planEndDate.compareTo(maxPlanEndDate) > 0) {
                    maxPlanEndDate = planEndDate;
                }
                // 统计 item 中非空 Planend 的条数赋值给主表的 BillPlanCount 字段
                planFinishCount++;
            }
            // 处理 Completiondate 字段
            if (completionDate != null) {
                // 更新item最大完工时间
                if (maxCompletionDate == null || completionDate.compareTo(maxCompletionDate) > 0) {
                    maxCompletionDate = completionDate;
                }
                // 统计 item 中非空 Completiondate 的条数赋值给主表的 BillFinishCount 字段
                billFinishCount++;
            } else {
                // 存在 Completiondate 为空的情况
                allCompletionDatesNotNull = false;
            }
        }
        saTrackingEntity.setBillplandate(maxPlanEndDate);
        // 当Completiondate都不为空，则将最大完工时间赋值给主表的BillFinishDate字段
        if (allCompletionDatesNotNull) {
            saTrackingEntity.setBillfinishdate(maxCompletionDate);
        }
        saTrackingEntity.setItemcount(lst.size());
        saTrackingEntity.setPlanfinishcount(planFinishCount);
        saTrackingEntity.setBillfinishcount(billFinishCount);
        //插入主表
        this.saTrackingMapper.insert(saTrackingEntity);
        //循环每个item子表
        for (SaTrackingitemPojo saTrackingitemPojo : lst) {
            //初始化item的NULL
            SaTrackingitemPojo itemPojo = this.saTrackingitemService.clearNull(saTrackingitemPojo);
            SaTrackingitemEntity saTrackingitemEntity = new SaTrackingitemEntity();
            BeanUtils.copyProperties(itemPojo, saTrackingitemEntity);
            //设置id和Pid
            saTrackingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
            saTrackingitemEntity.setPid(id);
            saTrackingitemEntity.setRevision(1);  //乐观锁
            //插入子表
            this.saTrackingitemMapper.insert(saTrackingitemEntity);
        }
        //返回Bill实例
        return this.getBillEntity(saTrackingEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saTrackingPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaTrackingPojo update(SaTrackingPojo saTrackingPojo) {
        //主表更改
        SaTrackingEntity saTrackingEntity = new SaTrackingEntity();
        BeanUtils.copyProperties(saTrackingPojo, saTrackingEntity);
        // 获取子表
        List<SaTrackingitemPojo> lst = saTrackingPojo.getItem();
        Date maxPlanEndDate = null;
        int planFinishCount = 0;
        Date maxCompletionDate = null;
        int billFinishCount = 0;
        // 判断lst中每个元素的Completiondate是否全部都不为空
        boolean allCompletionDatesNotNull = true;
        for (SaTrackingitemPojo item : lst) {
            Date planEndDate = item.getPlanend();
            Date completionDate = item.getCompletiondate();
            // 处理 Planend 字段
            if (planEndDate != null) {
                // 更新item最大计划结束时间
                if (maxPlanEndDate == null || planEndDate.compareTo(maxPlanEndDate) > 0) {
                    maxPlanEndDate = planEndDate;
                }
                // 统计 item 中非空 Planend 的条数赋值给主表的 BillPlanCount 字段
                planFinishCount++;
            }
            // 处理 Completiondate 字段
            if (completionDate != null) {
                // 更新item最大完工时间
                if (maxCompletionDate == null || completionDate.compareTo(maxCompletionDate) > 0) {
                    maxCompletionDate = completionDate;
                }
                // 统计 item 中非空 Completiondate 的条数赋值给主表的 BillFinishCount 字段
                billFinishCount++;
            } else {
                // 存在 Completiondate 为空的情况
                allCompletionDatesNotNull = false;
            }
        }
        saTrackingEntity.setBillplandate(maxPlanEndDate);
        // 当Completiondate都不为空，则将最大完工时间赋值给主表的BillFinishDate字段
        if (allCompletionDatesNotNull) {
            saTrackingEntity.setBillfinishdate(maxCompletionDate);
        }
        saTrackingEntity.setItemcount(lst.size());
        saTrackingEntity.setPlanfinishcount(planFinishCount);
        saTrackingEntity.setBillfinishcount(billFinishCount);
        // 更新主表数据
        this.saTrackingMapper.update(saTrackingEntity);

        if (saTrackingPojo.getItem() != null) {
            //获取被删除的Item
            List<String> lstDelIds = saTrackingMapper.getDelItemIds(saTrackingPojo);
            if (lstDelIds != null) {
                //循环每个删除item子表
                for (int i = 0; i < lstDelIds.size(); i++) {
                    this.saTrackingitemMapper.delete(lstDelIds.get(i));
                }
            }
            if (lst != null) {
                //循环每个item子表
                for (int i = 0; i < lst.size(); i++) {
                    SaTrackingitemEntity saTrackingitemEntity = new SaTrackingitemEntity();
                    if ("".equals(lst.get(i).getId()) || lst.get(i).getId() == null) {
                        //初始化item的NULL
                        SaTrackingitemPojo itemPojo = this.saTrackingitemService.clearNull(lst.get(i));
                        BeanUtils.copyProperties(itemPojo, saTrackingitemEntity);
                        //设置id和Pid
                        saTrackingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saTrackingitemEntity.setPid(saTrackingEntity.getId());  // 主表 id
                        saTrackingitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saTrackingitemMapper.insert(saTrackingitemEntity);
                    } else {
                        BeanUtils.copyProperties(lst.get(i), saTrackingitemEntity);
                        this.saTrackingitemMapper.update(saTrackingitemEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saTrackingEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
        SaTrackingPojo saTrackingPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaTrackingitemPojo> lst = saTrackingPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (int i = 0; i < lst.size(); i++) {
                if (lst.get(i).getPlanend() != null) {
                    throw new RuntimeException("存在已排程(计划结束)的数据，不能删除！");
                }
                this.saTrackingitemMapper.delete(lst.get(i).getId());
            }
        }
        return this.saTrackingMapper.delete(key);
    }


    private static void cleanNull(SaTrackingPojo saTrackingPojo) {
        if(saTrackingPojo.getType()==null) saTrackingPojo.setType("");
        if(saTrackingPojo.getCustomer()==null) saTrackingPojo.setCustomer("");
        if(saTrackingPojo.getProject()==null) saTrackingPojo.setProject("");
        //if(saTrackingPojo.getItemcount()==null) saTrackingPojo.setItemcount(0);
        if(saTrackingPojo.getFinishcount()==null) saTrackingPojo.setFinishcount(0);
        if(saTrackingPojo.getSubject()==null) saTrackingPojo.setSubject("");
        //if(saTrackingPojo.getBillplandate()==null) saTrackingPojo.setBillplandate(new Date());
        //if(saTrackingPojo.getBillfinishdate()==null) saTrackingPojo.setBillfinishdate(new Date());
        if(saTrackingPojo.getPlanfinishcount()==null) saTrackingPojo.setPlanfinishcount(0);
        if(saTrackingPojo.getBillfinishcount()==null) saTrackingPojo.setBillfinishcount(0);
        if(saTrackingPojo.getClosed()==null) saTrackingPojo.setClosed(0);
        if(saTrackingPojo.getPublicmark()==null) saTrackingPojo.setPublicmark(0);
        if(saTrackingPojo.getOperator()==null) saTrackingPojo.setOperator("");
        if(saTrackingPojo.getOperatorid()==null) saTrackingPojo.setOperatorid("");
        if(saTrackingPojo.getAssistantids()==null) saTrackingPojo.setAssistantids("");
        if(saTrackingPojo.getAssistantnames()==null) saTrackingPojo.setAssistantnames("");
        if(saTrackingPojo.getCreatebyid()==null) saTrackingPojo.setCreatebyid("");
        if(saTrackingPojo.getCreateby()==null) saTrackingPojo.setCreateby("");
        if(saTrackingPojo.getCreatedate()==null) saTrackingPojo.setCreatedate(new Date());
        if(saTrackingPojo.getListerid()==null) saTrackingPojo.setListerid("");
        if(saTrackingPojo.getLister()==null) saTrackingPojo.setLister("");
        if(saTrackingPojo.getModifydate()==null) saTrackingPojo.setModifydate(new Date());
        if(saTrackingPojo.getDisannulcount()==null) saTrackingPojo.setDisannulcount(0);
        if(saTrackingPojo.getCustom1()==null) saTrackingPojo.setCustom1("");
        if(saTrackingPojo.getCustom2()==null) saTrackingPojo.setCustom2("");
        if(saTrackingPojo.getCustom3()==null) saTrackingPojo.setCustom3("");
        if(saTrackingPojo.getCustom4()==null) saTrackingPojo.setCustom4("");
        if(saTrackingPojo.getCustom5()==null) saTrackingPojo.setCustom5("");
        if(saTrackingPojo.getRevision()==null) saTrackingPojo.setRevision(0);
   }

    @Override
    public SaTrackingPojo disannul(List<SaTrackingitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "作废" : "恢复";
        for (int i = 0; i < lst.size(); i++) {
            SaTrackingitemPojo Pojo = lst.get(i);
            SaTrackingitemPojo dbPojo = this.saTrackingitemMapper.getEntity(Pojo.getId());
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getDisannulmark(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getClosed() == 1) {
                        throw new RuntimeException(i + 1 + "行已关闭,禁止作废操作");
                    }
                    SaTrackingitemEntity entity = new SaTrackingitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setDisannulmark(type);
                    this.saTrackingitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.saTrackingMapper.updateDisannulCount(Pid, tid);
            //主表更改
            SaTrackingEntity saTrackingEntity = new SaTrackingEntity();
            saTrackingEntity.setId(Pid);
            saTrackingEntity.setLister(loginUser.getRealname());
            saTrackingEntity.setListerid(loginUser.getUserid());
            saTrackingEntity.setModifydate(new Date());
            this.saTrackingMapper.update(saTrackingEntity);
            //返回Bill实例
            return this.getBillEntity(saTrackingEntity.getId());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }

    @Override
    public SaTrackingPojo closed(List<SaTrackingitemPojo> lst, Integer type, LoginUser loginUser) {
        int disNum = 0;
        String tid = loginUser.getTenantid();
        String Pid = "";
        String strType = type == 1 ? "关闭" : "开启";
        for (int i = 0; i < lst.size(); i++) {
            SaTrackingitemPojo Pojo = lst.get(i);
            SaTrackingitemPojo dbPojo = this.saTrackingitemMapper.getEntity(Pojo.getId());
            if (dbPojo != null) {
                if (!Objects.equals(dbPojo.getClosed(), type)) {
                    if (Pid.isEmpty()) Pid = dbPojo.getPid();
                    if (dbPojo.getDisannulmark() == 1) {
                        throw new RuntimeException(i + 1 + "行已作废,禁止操作");
                    }
                    SaTrackingitemEntity entity = new SaTrackingitemEntity();
                    entity.setId(dbPojo.getId());
                    entity.setClosed(type);
                    this.saTrackingitemMapper.update(entity);
                    disNum++;
                } else {
                    throw new RuntimeException(i + 1 + "行已" + strType + ",无需操作");
                }
            }
        }
        if (disNum > 0) {
            this.saTrackingMapper.updateFinishCount(Pid, tid);
            //主表更改
            SaTrackingEntity saTrackingEntity = new SaTrackingEntity();
            saTrackingEntity.setId(Pid);
            saTrackingEntity.setLister(loginUser.getRealname());
            saTrackingEntity.setListerid(loginUser.getUserid());
            saTrackingEntity.setModifydate(new Date());
            this.saTrackingMapper.update(saTrackingEntity);
            //返回Bill实例
            return this.getBillEntity(saTrackingEntity.getId());
        } else {
            throw new RuntimeException("未查到可更改的记录");
        }
    }
}
