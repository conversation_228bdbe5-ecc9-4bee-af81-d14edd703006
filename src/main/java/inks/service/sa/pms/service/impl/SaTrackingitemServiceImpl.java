package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaTrackingitemEntity;
import inks.service.sa.pms.domain.pojo.SaTrackingitemPojo;
import inks.service.sa.pms.mapper.SaTrackingitemMapper;
import inks.service.sa.pms.service.SaTrackingitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 跟踪表子表(SaTrackingitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-25 14:45:30
 */
@Service("saTrackingitemService")
public class SaTrackingitemServiceImpl implements SaTrackingitemService {
    @Resource
    private SaTrackingitemMapper saTrackingitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTrackingitemPojo getEntity(String key) {
        return this.saTrackingitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTrackingitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTrackingitemPojo> lst = saTrackingitemMapper.getPageList(queryParam);
            PageInfo<SaTrackingitemPojo> pageInfo = new PageInfo<SaTrackingitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaTrackingitemPojo> getList(String Pid) {
        try {
            List<SaTrackingitemPojo> lst = saTrackingitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saTrackingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTrackingitemPojo insert(SaTrackingitemPojo saTrackingitemPojo) {
        //初始化item的NULL
        SaTrackingitemPojo itempojo = this.clearNull(saTrackingitemPojo);
        SaTrackingitemEntity saTrackingitemEntity = new SaTrackingitemEntity();
        BeanUtils.copyProperties(itempojo, saTrackingitemEntity);
        //生成雪花id
        saTrackingitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saTrackingitemEntity.setRevision(1);  //乐观锁
        this.saTrackingitemMapper.insert(saTrackingitemEntity);
        return this.getEntity(saTrackingitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saTrackingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTrackingitemPojo update(SaTrackingitemPojo saTrackingitemPojo) {
        SaTrackingitemEntity saTrackingitemEntity = new SaTrackingitemEntity();
        BeanUtils.copyProperties(saTrackingitemPojo, saTrackingitemEntity);
        this.saTrackingitemMapper.update(saTrackingitemEntity);
        return this.getEntity(saTrackingitemEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saTrackingitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saTrackingitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTrackingitemPojo clearNull(SaTrackingitemPojo saTrackingitemPojo) {
        //初始化NULL字段
        if (saTrackingitemPojo.getPid() == null) saTrackingitemPojo.setPid("");
        if (saTrackingitemPojo.getPhasename() == null) saTrackingitemPojo.setPhasename("");
        if (saTrackingitemPojo.getTaskname() == null) saTrackingitemPojo.setTaskname("");
        if (saTrackingitemPojo.getDescription() == null) saTrackingitemPojo.setDescription("");
//        if (saTrackingitemPojo.getPlanstart() == null) saTrackingitemPojo.setPlanstart(new Date());
//        if (saTrackingitemPojo.getPlanend() == null) saTrackingitemPojo.setPlanend(new Date());
        if (saTrackingitemPojo.getMilestone() == null) saTrackingitemPojo.setMilestone("");
//        if (saTrackingitemPojo.getActualstart() == null) saTrackingitemPojo.setActualstart(new Date());
//        if (saTrackingitemPojo.getCompletiondate() == null) saTrackingitemPojo.setCompletiondate(new Date());
        if (saTrackingitemPojo.getOutputresult() == null) saTrackingitemPojo.setOutputresult("");
        if (saTrackingitemPojo.getMainresponsibleperson() == null) saTrackingitemPojo.setMainresponsibleperson("");
     if(saTrackingitemPojo.getSubresponsibleperson()==null) saTrackingitemPojo.setSubresponsibleperson("");
     if(saTrackingitemPojo.getClosed()==null) saTrackingitemPojo.setClosed(0);
     if(saTrackingitemPojo.getDisannulmark()==null) saTrackingitemPojo.setDisannulmark(0);
        if (saTrackingitemPojo.getRownum() == null) saTrackingitemPojo.setRownum(0);
        if (saTrackingitemPojo.getRemark() == null) saTrackingitemPojo.setRemark("");
        if (saTrackingitemPojo.getCustom1() == null) saTrackingitemPojo.setCustom1("");
        if (saTrackingitemPojo.getCustom2() == null) saTrackingitemPojo.setCustom2("");
        if (saTrackingitemPojo.getCustom3() == null) saTrackingitemPojo.setCustom3("");
        if (saTrackingitemPojo.getCustom4() == null) saTrackingitemPojo.setCustom4("");
        if (saTrackingitemPojo.getCustom5() == null) saTrackingitemPojo.setCustom5("");
        if (saTrackingitemPojo.getRevision() == null) saTrackingitemPojo.setRevision(0);
        return saTrackingitemPojo;
    }
}
