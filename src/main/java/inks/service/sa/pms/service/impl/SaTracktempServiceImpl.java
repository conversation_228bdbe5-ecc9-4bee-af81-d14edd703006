package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaTracktempEntity;
import inks.service.sa.pms.domain.pojo.SaTracktempPojo;
import inks.service.sa.pms.mapper.SaTracktempMapper;
import inks.service.sa.pms.service.SaTracktempService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 跟踪模板表(SaTracktemp)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-06-25 14:03:40
 */
@Service("saTracktempService")
public class SaTracktempServiceImpl implements SaTracktempService {
    @Resource
    private SaTracktempMapper saTracktempMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaTracktempPojo getEntity(String key) {
        return this.saTracktempMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaTracktempPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaTracktempPojo> lst = saTracktempMapper.getPageList(queryParam);
            PageInfo<SaTracktempPojo> pageInfo = new PageInfo<SaTracktempPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saTracktempPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTracktempPojo insert(SaTracktempPojo saTracktempPojo) {
        //初始化NULL字段
        if (saTracktempPojo.getName() == null) saTracktempPojo.setName("");
        if (saTracktempPojo.getCode() == null) saTracktempPojo.setCode("");
        if (saTracktempPojo.getItemjson() == null) saTracktempPojo.setItemjson("");
        if (saTracktempPojo.getRownum() == null) saTracktempPojo.setRownum(0);
        if (saTracktempPojo.getRemark() == null) saTracktempPojo.setRemark("");
        if (saTracktempPojo.getCreatebyid() == null) saTracktempPojo.setCreatebyid("");
        if (saTracktempPojo.getCreateby() == null) saTracktempPojo.setCreateby("");
        if (saTracktempPojo.getCreatedate() == null) saTracktempPojo.setCreatedate(new Date());
        if (saTracktempPojo.getListerid() == null) saTracktempPojo.setListerid("");
        if (saTracktempPojo.getLister() == null) saTracktempPojo.setLister("");
        if (saTracktempPojo.getModifydate() == null) saTracktempPojo.setModifydate(new Date());
        if (saTracktempPojo.getCustom1() == null) saTracktempPojo.setCustom1("");
        if (saTracktempPojo.getCustom2() == null) saTracktempPojo.setCustom2("");
        if (saTracktempPojo.getCustom3() == null) saTracktempPojo.setCustom3("");
        if (saTracktempPojo.getCustom4() == null) saTracktempPojo.setCustom4("");
        if (saTracktempPojo.getCustom5() == null) saTracktempPojo.setCustom5("");
        if (saTracktempPojo.getRevision() == null) saTracktempPojo.setRevision(0);
        SaTracktempEntity saTracktempEntity = new SaTracktempEntity();
        BeanUtils.copyProperties(saTracktempPojo, saTracktempEntity);
        //生成雪花id
        saTracktempEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saTracktempEntity.setRevision(1);  //乐观锁
        this.saTracktempMapper.insert(saTracktempEntity);
        return this.getEntity(saTracktempEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saTracktempPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaTracktempPojo update(SaTracktempPojo saTracktempPojo) {
        SaTracktempEntity saTracktempEntity = new SaTracktempEntity();
        BeanUtils.copyProperties(saTracktempPojo, saTracktempEntity);
        this.saTracktempMapper.update(saTracktempEntity);
        return this.getEntity(saTracktempEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saTracktempMapper.delete(key);
    }


}
