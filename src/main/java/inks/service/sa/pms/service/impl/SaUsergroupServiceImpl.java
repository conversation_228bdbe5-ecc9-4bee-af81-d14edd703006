package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaUsergroupEntity;
import inks.service.sa.pms.domain.pojo.SaUsergroupPojo;
import inks.service.sa.pms.mapper.SaUsergroupMapper;
import inks.service.sa.pms.service.SaUsergroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaUsergroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-24 16:01:27
 */
@Service("saUsergroupService")
public class SaUsergroupServiceImpl implements SaUsergroupService {
    @Resource
    private SaUsergroupMapper saUsergroupMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaUsergroupPojo getEntity(String key) {
        return this.saUsergroupMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaUsergroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaUsergroupPojo> lst = saUsergroupMapper.getPageList(queryParam);
            PageInfo<SaUsergroupPojo> pageInfo = new PageInfo<SaUsergroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saUsergroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUsergroupPojo insert(SaUsergroupPojo saUsergroupPojo) {
        //初始化NULL字段
        if (saUsergroupPojo.getUserid() == null) saUsergroupPojo.setUserid("");
        if (saUsergroupPojo.getGroupid() == null) saUsergroupPojo.setGroupid("");
        if (saUsergroupPojo.getRownum() == null) saUsergroupPojo.setRownum(0);
        if (saUsergroupPojo.getRemark() == null) saUsergroupPojo.setRemark("");
        if (saUsergroupPojo.getCreateby() == null) saUsergroupPojo.setCreateby("");
        if (saUsergroupPojo.getCreatebyid() == null) saUsergroupPojo.setCreatebyid("");
        if (saUsergroupPojo.getCreatedate() == null) saUsergroupPojo.setCreatedate(new Date());
        if (saUsergroupPojo.getLister() == null) saUsergroupPojo.setLister("");
        if (saUsergroupPojo.getListerid() == null) saUsergroupPojo.setListerid("");
        if (saUsergroupPojo.getModifydate() == null) saUsergroupPojo.setModifydate(new Date());
        if (saUsergroupPojo.getCustom1() == null) saUsergroupPojo.setCustom1("");
        if (saUsergroupPojo.getCustom2() == null) saUsergroupPojo.setCustom2("");
        if (saUsergroupPojo.getCustom3() == null) saUsergroupPojo.setCustom3("");
        if (saUsergroupPojo.getCustom4() == null) saUsergroupPojo.setCustom4("");
        if (saUsergroupPojo.getCustom5() == null) saUsergroupPojo.setCustom5("");
        if (saUsergroupPojo.getTenantid() == null) saUsergroupPojo.setTenantid("");
        if (saUsergroupPojo.getTenantname() == null) saUsergroupPojo.setTenantname("");
        if (saUsergroupPojo.getRevision() == null) saUsergroupPojo.setRevision(0);
        SaUsergroupEntity saUsergroupEntity = new SaUsergroupEntity();
        BeanUtils.copyProperties(saUsergroupPojo, saUsergroupEntity);
        //生成雪花id
        saUsergroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saUsergroupEntity.setRevision(1);  //乐观锁
        this.saUsergroupMapper.insert(saUsergroupEntity);
        return this.getEntity(saUsergroupEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saUsergroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUsergroupPojo update(SaUsergroupPojo saUsergroupPojo) {
        SaUsergroupEntity saUsergroupEntity = new SaUsergroupEntity();
        BeanUtils.copyProperties(saUsergroupPojo, saUsergroupEntity);
        this.saUsergroupMapper.update(saUsergroupEntity);
        return this.getEntity(saUsergroupEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saUsergroupMapper.delete(key);
    }


}
