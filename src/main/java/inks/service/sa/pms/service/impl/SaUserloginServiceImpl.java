package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.SaUserloginEntity;
import inks.service.sa.pms.domain.pojo.SaUserloginPojo;
import inks.service.sa.pms.mapper.SaUserloginMapper;
import inks.service.sa.pms.service.SaUserloginService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 用户登录(SaUserlogin)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-27 16:50:31
 */
@Service("saUserloginService")
public class SaUserloginServiceImpl implements SaUserloginService {
    @Resource
    private SaUserloginMapper saUserloginMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaUserloginPojo getEntity(String key) {
        return this.saUserloginMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaUserloginPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaUserloginPojo> lst = saUserloginMapper.getPageList(queryParam);
            PageInfo<SaUserloginPojo> pageInfo = new PageInfo<SaUserloginPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saUserloginPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserloginPojo insert(SaUserloginPojo saUserloginPojo) {
        //初始化NULL字段
        if (saUserloginPojo.getUserid() == null) saUserloginPojo.setUserid("");
        if (saUserloginPojo.getUserpassword() == null) saUserloginPojo.setUserpassword("");
        if (saUserloginPojo.getListerid() == null) saUserloginPojo.setListerid("");
        if (saUserloginPojo.getLister() == null) saUserloginPojo.setLister("");
        if (saUserloginPojo.getCreatedate() == null) saUserloginPojo.setCreatedate(new Date());
        if (saUserloginPojo.getModifydate() == null) saUserloginPojo.setModifydate(new Date());
        SaUserloginEntity saUserloginEntity = new SaUserloginEntity();
        BeanUtils.copyProperties(saUserloginPojo, saUserloginEntity);

        saUserloginEntity.setId(UUID.randomUUID().toString());
//        saUserloginEntity.setRevision(1);  //乐观锁
        this.saUserloginMapper.insert(saUserloginEntity);
        return this.getEntity(saUserloginEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saUserloginPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaUserloginPojo update(SaUserloginPojo saUserloginPojo) {
        SaUserloginEntity saUserloginEntity = new SaUserloginEntity();
        BeanUtils.copyProperties(saUserloginPojo, saUserloginEntity);
        this.saUserloginMapper.update(saUserloginEntity);
        return this.getEntity(saUserloginEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key, String tid) {
        return this.saUserloginMapper.delete(key, tid);
    }


}
