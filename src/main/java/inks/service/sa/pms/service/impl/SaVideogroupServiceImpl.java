package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.SaVideogroupEntity;
import inks.service.sa.pms.domain.pojo.SaVideogroupPojo;
import inks.service.sa.pms.mapper.SaVideogroupMapper;
import inks.service.sa.pms.service.SaVideogroupService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 视频目录(SaVideogroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-12-09 08:58:49
 */
@Service("saVideogroupService")
public class SaVideogroupServiceImpl implements SaVideogroupService {
    @Resource
    private SaVideogroupMapper saVideogroupMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaVideogroupPojo getEntity(String key) {
        return this.saVideogroupMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaVideogroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaVideogroupPojo> lst = saVideogroupMapper.getPageList(queryParam);
            PageInfo<SaVideogroupPojo> pageInfo = new PageInfo<SaVideogroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saVideogroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaVideogroupPojo insert(SaVideogroupPojo saVideogroupPojo) {
        //初始化NULL字段
        if (saVideogroupPojo.getParentid() == null) saVideogroupPojo.setParentid("");
        if (saVideogroupPojo.getGroupcode() == null) saVideogroupPojo.setGroupcode("");
        if (saVideogroupPojo.getGroupname() == null) saVideogroupPojo.setGroupname("");
        if (saVideogroupPojo.getPublicmark() == null) saVideogroupPojo.setPublicmark(0);
        if (saVideogroupPojo.getRownum() == null) saVideogroupPojo.setRownum(0);
        if (saVideogroupPojo.getRemark() == null) saVideogroupPojo.setRemark("");
        if (saVideogroupPojo.getCreateby() == null) saVideogroupPojo.setCreateby("");
        if (saVideogroupPojo.getCreatebyid() == null) saVideogroupPojo.setCreatebyid("");
        if (saVideogroupPojo.getCreatedate() == null) saVideogroupPojo.setCreatedate(new Date());
        if (saVideogroupPojo.getLister() == null) saVideogroupPojo.setLister("");
        if (saVideogroupPojo.getListerid() == null) saVideogroupPojo.setListerid("");
        if (saVideogroupPojo.getModifydate() == null) saVideogroupPojo.setModifydate(new Date());
        if (saVideogroupPojo.getCustom1() == null) saVideogroupPojo.setCustom1("");
        if (saVideogroupPojo.getCustom2() == null) saVideogroupPojo.setCustom2("");
        if (saVideogroupPojo.getCustom3() == null) saVideogroupPojo.setCustom3("");
        if (saVideogroupPojo.getCustom4() == null) saVideogroupPojo.setCustom4("");
        if (saVideogroupPojo.getCustom5() == null) saVideogroupPojo.setCustom5("");
        if (saVideogroupPojo.getCustom6() == null) saVideogroupPojo.setCustom6("");
        if (saVideogroupPojo.getCustom7() == null) saVideogroupPojo.setCustom7("");
        if (saVideogroupPojo.getCustom8() == null) saVideogroupPojo.setCustom8("");
        if (saVideogroupPojo.getCustom9() == null) saVideogroupPojo.setCustom9("");
        if (saVideogroupPojo.getCustom10() == null) saVideogroupPojo.setCustom10("");
        if (saVideogroupPojo.getTenantid() == null) saVideogroupPojo.setTenantid("");
        if (saVideogroupPojo.getTenantname() == null) saVideogroupPojo.setTenantname("");
        if (saVideogroupPojo.getRevision() == null) saVideogroupPojo.setRevision(0);
        SaVideogroupEntity saVideogroupEntity = new SaVideogroupEntity();
        BeanUtils.copyProperties(saVideogroupPojo, saVideogroupEntity);

        saVideogroupEntity.setId(UUID.randomUUID().toString());
        saVideogroupEntity.setRevision(1);  //乐观锁
        this.saVideogroupMapper.insert(saVideogroupEntity);
        return this.getEntity(saVideogroupEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saVideogroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaVideogroupPojo update(SaVideogroupPojo saVideogroupPojo) {
        SaVideogroupEntity saVideogroupEntity = new SaVideogroupEntity();
        BeanUtils.copyProperties(saVideogroupPojo, saVideogroupEntity);
        this.saVideogroupMapper.update(saVideogroupEntity);
        return this.getEntity(saVideogroupEntity.getId());
    }

    @Override
    public List<SaVideogroupPojo> getAllGroup() {
        return this.saVideogroupMapper.getAllGroup();
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saVideogroupMapper.delete(key);
    }


}
