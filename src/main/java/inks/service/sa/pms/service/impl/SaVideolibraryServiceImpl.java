package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.service.sa.pms.domain.SaVideolibraryEntity;
import inks.service.sa.pms.domain.pojo.SaVideolibraryPojo;
import inks.service.sa.pms.mapper.SaVideolibraryMapper;
import inks.service.sa.pms.service.SaVideolibraryService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.UUID;

/**
 * 视频信息表(SaVideolibrary)表服务实现类
 *
 * <AUTHOR>
 * @since 2022-10-20 10:25:26
 */
@Service("saVideolibraryService")
public class SaVideolibraryServiceImpl implements SaVideolibraryService {


    @Resource
    private SaVideolibraryMapper saVideolibraryMapper;




    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaVideolibraryPojo getEntity(String key) {
        return this.saVideolibraryMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaVideolibraryPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaVideolibraryPojo> lst = saVideolibraryMapper.getPageList(queryParam);
            PageInfo<SaVideolibraryPojo> pageInfo = new PageInfo<SaVideolibraryPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saVideolibraryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaVideolibraryPojo insert(SaVideolibraryPojo saVideolibraryPojo) {
        //初始化NULL字段
        //新加字段   累计播放量，点赞量，点踩量，描述，等级
        if (saVideolibraryPojo.getDescription() == null) saVideolibraryPojo.setDescription("");
        if (saVideolibraryPojo.getVideolevel() == null) saVideolibraryPojo.setVideolevel(0);
        if (saVideolibraryPojo.getVideogoodnum() == null) saVideolibraryPojo.setVideogoodnum(0);
        if (saVideolibraryPojo.getVideongnum() == null) saVideolibraryPojo.setVideongnum(0);
        if (saVideolibraryPojo.getVideoplaytimes() == null) saVideolibraryPojo.setVideoplaytimes(0);
        if (saVideolibraryPojo.getGoodsid() == null) saVideolibraryPojo.setGoodsid("");


        if (saVideolibraryPojo.getVideotitle() == null) saVideolibraryPojo.setVideotitle("");
        if (saVideolibraryPojo.getVideoplayurl() == null) saVideolibraryPojo.setVideoplayurl("");
        if (saVideolibraryPojo.getVideocoverurl() == null) saVideolibraryPojo.setVideocoverurl("");
        if (saVideolibraryPojo.getVideoduration() == null) saVideolibraryPojo.setVideoduration(0);
        if (saVideolibraryPojo.getVideodesc() == null) saVideolibraryPojo.setVideodesc("");
        if (saVideolibraryPojo.getFisrtlevelgroupid() == null) saVideolibraryPojo.setFisrtlevelgroupid("");
        if (saVideolibraryPojo.getSeclevelgroupid() == null) saVideolibraryPojo.setSeclevelgroupid("");
        if (saVideolibraryPojo.getSecretkey() == null) saVideolibraryPojo.setSecretkey("");
        if (saVideolibraryPojo.getUploadtime() == null) saVideolibraryPojo.setUploadtime(new Date());
        if (saVideolibraryPojo.getTexttutorial() == null) saVideolibraryPojo.setTexttutorial("");
        if (saVideolibraryPojo.getVideotag() == null) saVideolibraryPojo.setVideotag("");
        if (saVideolibraryPojo.getBackcolorargb() == null) saVideolibraryPojo.setBackcolorargb("");
        if (saVideolibraryPojo.getForecolorargb() == null) saVideolibraryPojo.setForecolorargb("");
        if (saVideolibraryPojo.getPublicmark() == null) saVideolibraryPojo.setPublicmark(0);
        if (saVideolibraryPojo.getEnabledmark() == null) saVideolibraryPojo.setEnabledmark(0);
        if (saVideolibraryPojo.getRownum() == null) saVideolibraryPojo.setRownum(0);
        if (saVideolibraryPojo.getRemark() == null) saVideolibraryPojo.setRemark("");
        if (saVideolibraryPojo.getCreateby() == null) saVideolibraryPojo.setCreateby("");
        if (saVideolibraryPojo.getCreatebyid() == null) saVideolibraryPojo.setCreatebyid("");
        if (saVideolibraryPojo.getCreatedate() == null) saVideolibraryPojo.setCreatedate(new Date());
        if (saVideolibraryPojo.getLister() == null) saVideolibraryPojo.setLister("");
        if (saVideolibraryPojo.getListerid() == null) saVideolibraryPojo.setListerid("");
        if (saVideolibraryPojo.getModifydate() == null) saVideolibraryPojo.setModifydate(new Date());
        if (saVideolibraryPojo.getAssessor() == null) saVideolibraryPojo.setAssessor("");
        if (saVideolibraryPojo.getAssessorid() == null) saVideolibraryPojo.setAssessorid("");
        if (saVideolibraryPojo.getAssessdate() == null) saVideolibraryPojo.setAssessdate(new Date());
        if (saVideolibraryPojo.getCustom1() == null) saVideolibraryPojo.setCustom1("");
        if (saVideolibraryPojo.getCustom2() == null) saVideolibraryPojo.setCustom2("");
        if (saVideolibraryPojo.getCustom3() == null) saVideolibraryPojo.setCustom3("");
        if (saVideolibraryPojo.getCustom4() == null) saVideolibraryPojo.setCustom4("");
        if (saVideolibraryPojo.getCustom5() == null) saVideolibraryPojo.setCustom5("");
        if (saVideolibraryPojo.getCustom6() == null) saVideolibraryPojo.setCustom6("");
        if (saVideolibraryPojo.getCustom7() == null) saVideolibraryPojo.setCustom7("");
        if (saVideolibraryPojo.getCustom8() == null) saVideolibraryPojo.setCustom8("");
        if (saVideolibraryPojo.getCustom9() == null) saVideolibraryPojo.setCustom9("");
        if (saVideolibraryPojo.getCustom10() == null) saVideolibraryPojo.setCustom10("");
        if (saVideolibraryPojo.getTenantid() == null) saVideolibraryPojo.setTenantid("");
        if (saVideolibraryPojo.getTenantname() == null) saVideolibraryPojo.setTenantname("");
        if (saVideolibraryPojo.getRevision() == null) saVideolibraryPojo.setRevision(0);
        if (saVideolibraryPojo.getDirname() == null) saVideolibraryPojo.setDirname("");
        if (saVideolibraryPojo.getFilename() == null) saVideolibraryPojo.setFilename("");
        SaVideolibraryEntity saVideolibraryEntity = new SaVideolibraryEntity();
        BeanUtils.copyProperties(saVideolibraryPojo, saVideolibraryEntity);

        saVideolibraryEntity.setId(UUID.randomUUID().toString());
        saVideolibraryEntity.setRevision(1);  //乐观锁
        this.saVideolibraryMapper.insert(saVideolibraryEntity);
        return this.getEntity(saVideolibraryEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saVideolibraryPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaVideolibraryPojo update(SaVideolibraryPojo saVideolibraryPojo) throws Exception {
        SaVideolibraryEntity saVideolibraryEntity = new SaVideolibraryEntity();
        BeanUtils.copyProperties(saVideolibraryPojo, saVideolibraryEntity);
        //获取更新前的数据
        SaVideolibraryPojo oriSaVideolibraryPojo = this.saVideolibraryMapper.getEntity(saVideolibraryPojo.getId());
        this.saVideolibraryMapper.update(saVideolibraryEntity);
        ////判断是否更新了视频，如果视频播放地址不一样则删除原视频
        //if (StringUtils.isNotEmpty(oriSaVideolibraryPojo.getVideoplayurl()) && !oriSaVideolibraryPojo.getVideoplayurl().equals(saVideolibraryPojo.getVideoplayurl())) {
        //    this.deleteVideo(oriSaVideolibraryPojo.getVideoplayurl());
        //}
        return this.getEntity(saVideolibraryEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) throws Exception {
        SaVideolibraryPojo oriSaVideolibraryPojo = this.saVideolibraryMapper.getEntity(key);
        int delete = this.saVideolibraryMapper.delete(key);
        //if (delete > 0) {
        //    this.deleteVideo(oriSaVideolibraryPojo.getVideoplayurl());
        //}
        return delete;
    }

    /**
     * 审核数据
     *
     * @param saVideolibraryPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaVideolibraryPojo approval(SaVideolibraryPojo saVideolibraryPojo) {
        //主表更改
        SaVideolibraryEntity saVideolibraryEntity = new SaVideolibraryEntity();
        BeanUtils.copyProperties(saVideolibraryPojo, saVideolibraryEntity);
        this.saVideolibraryMapper.approval(saVideolibraryEntity);
        //返回Bill实例
        return this.getEntity(saVideolibraryEntity.getId());
    }

}
