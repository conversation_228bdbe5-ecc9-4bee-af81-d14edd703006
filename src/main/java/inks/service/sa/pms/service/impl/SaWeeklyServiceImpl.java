package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaWeeklyEntity;
import inks.service.sa.pms.domain.pojo.SaWeeklyPojo;
import inks.service.sa.pms.mapper.SaWeeklyMapper;
import inks.service.sa.pms.service.SaWeeklyService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.DayOfWeek;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.WeekFields;
import java.util.Date;
import java.util.List;

/**
 * 工作周报(SaWeekly)表服务实现类
 *
 * <AUTHOR>
 * @since 2025-07-21 15:06:44
 */
@Service("saWeeklyService")
public class SaWeeklyServiceImpl implements SaWeeklyService {
    @Resource
    private SaWeeklyMapper saWeeklyMapper;

    @Override
    public SaWeeklyPojo getEntity(String key) {
        return this.saWeeklyMapper.getEntity(key);
    }


    @Override
    public PageInfo<SaWeeklyPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWeeklyPojo> lst = saWeeklyMapper.getPageList(queryParam);
            PageInfo<SaWeeklyPojo> pageInfo = new PageInfo<SaWeeklyPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    @Override
    public SaWeeklyPojo insert(SaWeeklyPojo saWeeklyPojo) {
        //初始化NULL字段
        cleanNull(saWeeklyPojo);
        String billcode = saWeeklyPojo.getBillcode();
        //billcode：年，第几周
        if (StringUtils.isBlank(billcode)) {
            Date billdate = saWeeklyPojo.getBilldate();
            if (billdate == null) {
                billdate = new Date();
            }
            LocalDate localDate = billdate.toInstant()
                    .atZone(ZoneId.systemDefault())
                    .toLocalDate();
            // 设置周的起始为周一，且第一周必须包含4天（ISO 8601标准）
            WeekFields weekFields = WeekFields.of(DayOfWeek.MONDAY, 4);
            int year = localDate.get(weekFields.weekBasedYear());
            int week = localDate.get(weekFields.weekOfWeekBasedYear());
            billcode = year + "-" + week;
            saWeeklyPojo.setBillcode(billcode);
        }
        SaWeeklyEntity saWeeklyEntity = new SaWeeklyEntity();
        BeanUtils.copyProperties(saWeeklyPojo, saWeeklyEntity);
        //生成雪花id
        saWeeklyEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saWeeklyEntity.setRevision(1);  //乐观锁
        this.saWeeklyMapper.insert(saWeeklyEntity);
        return this.getEntity(saWeeklyEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saWeeklyPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWeeklyPojo update(SaWeeklyPojo saWeeklyPojo) {
        SaWeeklyEntity saWeeklyEntity = new SaWeeklyEntity();
        BeanUtils.copyProperties(saWeeklyPojo, saWeeklyEntity);
        this.saWeeklyMapper.update(saWeeklyEntity);
        return this.getEntity(saWeeklyEntity.getId());
    }


    @Override
    public int delete(String key) {
        return this.saWeeklyMapper.delete(key);
    }

    @Override
    @Transactional
    public SaWeeklyPojo approval(SaWeeklyPojo saWeeklyPojo) {
        //主表更改
        SaWeeklyEntity saWeeklyEntity = new SaWeeklyEntity();
        BeanUtils.copyProperties(saWeeklyPojo, saWeeklyEntity);
        this.saWeeklyMapper.approval(saWeeklyEntity);
        //返回Bill实例
        return this.getEntity(saWeeklyEntity.getId());
    }

    private static void cleanNull(SaWeeklyPojo saWeeklyPojo) {
        if (saWeeklyPojo.getRefno() == null) saWeeklyPojo.setRefno("");
        if (saWeeklyPojo.getBilltitle() == null) saWeeklyPojo.setBilltitle("");
        if (saWeeklyPojo.getBilltype() == null) saWeeklyPojo.setBilltype("");
        if (saWeeklyPojo.getBilldate() == null) saWeeklyPojo.setBilldate(new Date());
        if (saWeeklyPojo.getBillcode() == null) saWeeklyPojo.setBillcode("");
        if (saWeeklyPojo.getDeptid() == null) saWeeklyPojo.setDeptid("");
        if (saWeeklyPojo.getDeptname() == null) saWeeklyPojo.setDeptname("");
        if (saWeeklyPojo.getReporter() == null) saWeeklyPojo.setReporter("");
        if (saWeeklyPojo.getReporterid() == null) saWeeklyPojo.setReporterid("");
        if (saWeeklyPojo.getStatetext() == null) saWeeklyPojo.setStatetext("");
        if (saWeeklyPojo.getStatedate() == null) saWeeklyPojo.setStatedate(new Date());
        if (saWeeklyPojo.getCompjson() == null) saWeeklyPojo.setCompjson("");
        if (saWeeklyPojo.getRecordjson() == null) saWeeklyPojo.setRecordjson("");
        if (saWeeklyPojo.getPlanjson() == null) saWeeklyPojo.setPlanjson("");
        if (saWeeklyPojo.getIssuesjson() == null) saWeeklyPojo.setIssuesjson("");
        if (saWeeklyPojo.getRownum() == null) saWeeklyPojo.setRownum(0);
        if (saWeeklyPojo.getRemark() == null) saWeeklyPojo.setRemark("");
        if (saWeeklyPojo.getAssessorid() == null) saWeeklyPojo.setAssessorid("");
        if (saWeeklyPojo.getAssessor() == null) saWeeklyPojo.setAssessor("");
        if (saWeeklyPojo.getAssessdate() == null) saWeeklyPojo.setAssessdate(new Date());
        if (saWeeklyPojo.getCreateby() == null) saWeeklyPojo.setCreateby("");
        if (saWeeklyPojo.getCreatebyid() == null) saWeeklyPojo.setCreatebyid("");
        if (saWeeklyPojo.getCreatedate() == null) saWeeklyPojo.setCreatedate(new Date());
        if (saWeeklyPojo.getLister() == null) saWeeklyPojo.setLister("");
        if (saWeeklyPojo.getListerid() == null) saWeeklyPojo.setListerid("");
        if (saWeeklyPojo.getModifydate() == null) saWeeklyPojo.setModifydate(new Date());
        if (saWeeklyPojo.getCustom1() == null) saWeeklyPojo.setCustom1("");
        if (saWeeklyPojo.getCustom2() == null) saWeeklyPojo.setCustom2("");
        if (saWeeklyPojo.getCustom3() == null) saWeeklyPojo.setCustom3("");
        if (saWeeklyPojo.getCustom4() == null) saWeeklyPojo.setCustom4("");
        if (saWeeklyPojo.getCustom5() == null) saWeeklyPojo.setCustom5("");
        if (saWeeklyPojo.getCustom6() == null) saWeeklyPojo.setCustom6("");
        if (saWeeklyPojo.getCustom7() == null) saWeeklyPojo.setCustom7("");
        if (saWeeklyPojo.getCustom8() == null) saWeeklyPojo.setCustom8("");
        if (saWeeklyPojo.getCustom9() == null) saWeeklyPojo.setCustom9("");
        if (saWeeklyPojo.getCustom10() == null) saWeeklyPojo.setCustom10("");
        if (saWeeklyPojo.getTenantid() == null) saWeeklyPojo.setTenantid("");
        if (saWeeklyPojo.getTenantname() == null) saWeeklyPojo.setTenantname("");
        if (saWeeklyPojo.getRevision() == null) saWeeklyPojo.setRevision(0);
    }

}
