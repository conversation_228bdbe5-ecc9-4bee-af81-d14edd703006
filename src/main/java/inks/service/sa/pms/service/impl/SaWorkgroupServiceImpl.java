package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaWorkgroupEntity;
import inks.service.sa.pms.domain.pojo.SaUserPojo;
import inks.service.sa.pms.domain.pojo.SaUsergroupPojo;
import inks.service.sa.pms.domain.pojo.SaWorkgroupPojo;
import inks.service.sa.pms.mapper.SaUsergroupMapper;
import inks.service.sa.pms.mapper.SaWorkgroupMapper;
import inks.service.sa.pms.mapper.pmsSaUserMapper;
import inks.service.sa.pms.service.SaWorkgroupService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 往来单位(SaWorkgroup)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-04-24 16:01:27
 */
@Service("saWorkgroupService")
public class SaWorkgroupServiceImpl implements SaWorkgroupService {
    @Resource
    private SaWorkgroupMapper saWorkgroupMapper;
    @Resource
    private SaUsergroupMapper saUsergroupMapper;
    @Resource
    private pmsSaUserMapper pmsSaUserMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaWorkgroupPojo getEntity(String key) {
        SaWorkgroupPojo saWorkgroupPojo = this.saWorkgroupMapper.getEntity(key);
        List<SaUsergroupPojo> list = saUsergroupMapper.getListByGroupid(key);
        //userPojoList:需要拿到关联的用户信息List（加一个用户-客户关联表的id）
        List<SaUserPojo> userPojoList = list.stream().map(item -> {
            SaUserPojo saUserPojo = pmsSaUserMapper.getEntity(item.getUserid());
            saUserPojo.setUsergroupid(item.getId());
            return saUserPojo;
        }).collect(Collectors.toList());
        saWorkgroupPojo.setUserList(userPojoList);
        return saWorkgroupPojo;
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaWorkgroupPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWorkgroupPojo> lst = saWorkgroupMapper.getPageList(queryParam);
            PageInfo<SaWorkgroupPojo> pageInfo = new PageInfo<SaWorkgroupPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saWorkgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWorkgroupPojo insert(SaWorkgroupPojo saWorkgroupPojo) {
        //初始化NULL字段
        if (saWorkgroupPojo.getWggroupid() == null) saWorkgroupPojo.setWggroupid("");
        if (saWorkgroupPojo.getGroupuid() == null) saWorkgroupPojo.setGroupuid("");
        if (saWorkgroupPojo.getGroupname() == null) saWorkgroupPojo.setGroupname("");
        if (saWorkgroupPojo.getAbbreviate() == null) saWorkgroupPojo.setAbbreviate("");
        if (saWorkgroupPojo.getGroupclass() == null) saWorkgroupPojo.setGroupclass("");
        if (saWorkgroupPojo.getLinkman() == null) saWorkgroupPojo.setLinkman("");
        if (saWorkgroupPojo.getTelephone() == null) saWorkgroupPojo.setTelephone("");
        if (saWorkgroupPojo.getGroupfax() == null) saWorkgroupPojo.setGroupfax("");
        if (saWorkgroupPojo.getGroupadd() == null) saWorkgroupPojo.setGroupadd("");
        if (saWorkgroupPojo.getRemark() == null) saWorkgroupPojo.setRemark("");
        if (saWorkgroupPojo.getInvaliddate() == null) saWorkgroupPojo.setInvaliddate(new Date());
        if (saWorkgroupPojo.getGrouptype() == null) saWorkgroupPojo.setGrouptype("");
        if (saWorkgroupPojo.getRownum() == null) saWorkgroupPojo.setRownum(0);
        if (saWorkgroupPojo.getCreateby() == null) saWorkgroupPojo.setCreateby("");
        if (saWorkgroupPojo.getCreatebyid() == null) saWorkgroupPojo.setCreatebyid("");
        if (saWorkgroupPojo.getCreatedate() == null) saWorkgroupPojo.setCreatedate(new Date());
        if (saWorkgroupPojo.getLister() == null) saWorkgroupPojo.setLister("");
        if (saWorkgroupPojo.getListerid() == null) saWorkgroupPojo.setListerid("");
        if (saWorkgroupPojo.getModifydate() == null) saWorkgroupPojo.setModifydate(new Date());
        if (saWorkgroupPojo.getMobile() == null) saWorkgroupPojo.setMobile("");
        if (saWorkgroupPojo.getCountry() == null) saWorkgroupPojo.setCountry("");
        if (saWorkgroupPojo.getProvince() == null) saWorkgroupPojo.setProvince("");
        if (saWorkgroupPojo.getGroupzip() == null) saWorkgroupPojo.setGroupzip("");
        if (saWorkgroupPojo.getSeller() == null) saWorkgroupPojo.setSeller("");
        if (saWorkgroupPojo.getEnabledmark() == null) saWorkgroupPojo.setEnabledmark(0);
        if (saWorkgroupPojo.getOperator() == null) saWorkgroupPojo.setOperator("");
        if (saWorkgroupPojo.getOperatorid() == null) saWorkgroupPojo.setOperatorid("");
        if (saWorkgroupPojo.getCollaboratorids() == null) saWorkgroupPojo.setCollaboratorids("");
        if (saWorkgroupPojo.getCollaborators() == null) saWorkgroupPojo.setCollaborators("");

        // 客户创建时自动生成随机 8位数字SerCode【服务码】,注意检查重复
        String serCode;
        do {
            // 生成8位随机数字字符串
            serCode = String.format("%08d", ThreadLocalRandom.current().nextInt(100_000_000));
        } while (saWorkgroupMapper.countBySerCode(serCode) > 0);
        saWorkgroupPojo.setSercode(serCode);

        if (saWorkgroupPojo.getCustom1() == null) saWorkgroupPojo.setCustom1("");
        if (saWorkgroupPojo.getCustom2() == null) saWorkgroupPojo.setCustom2("");
        if (saWorkgroupPojo.getCustom3() == null) saWorkgroupPojo.setCustom3("");
        if (saWorkgroupPojo.getCustom4() == null) saWorkgroupPojo.setCustom4("");
        if (saWorkgroupPojo.getCustom5() == null) saWorkgroupPojo.setCustom5("");
        if (saWorkgroupPojo.getCustom6() == null) saWorkgroupPojo.setCustom6("");
        if (saWorkgroupPojo.getCustom7() == null) saWorkgroupPojo.setCustom7("");
        if (saWorkgroupPojo.getCustom8() == null) saWorkgroupPojo.setCustom8("");
        if (saWorkgroupPojo.getCustom9() == null) saWorkgroupPojo.setCustom9("");
        if (saWorkgroupPojo.getCustom10() == null) saWorkgroupPojo.setCustom10("");
        if (saWorkgroupPojo.getTenantid() == null) saWorkgroupPojo.setTenantid("");
        if (saWorkgroupPojo.getRevision() == null) saWorkgroupPojo.setRevision(0);
        SaWorkgroupEntity saWorkgroupEntity = new SaWorkgroupEntity();
        BeanUtils.copyProperties(saWorkgroupPojo, saWorkgroupEntity);
        //生成雪花id
        saWorkgroupEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saWorkgroupEntity.setRevision(1);  //乐观锁
        this.saWorkgroupMapper.insert(saWorkgroupEntity);
        return this.getEntity(saWorkgroupEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saWorkgroupPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWorkgroupPojo update(SaWorkgroupPojo saWorkgroupPojo) {
        // 检查服务码是否重复（排除自己）
        if (StringUtils.isNotBlank(saWorkgroupPojo.getSercode())) {
            SaWorkgroupPojo existingBySerCode = saWorkgroupMapper.findBySerCode(saWorkgroupPojo.getSercode());
            if (existingBySerCode != null && !existingBySerCode.getId().equals(saWorkgroupPojo.getId())) {
                throw new BaseBusinessException("服务码 '" + saWorkgroupPojo.getSercode() + "' 已被其他客户使用。");
            }
        }

        SaWorkgroupEntity saWorkgroupEntity = new SaWorkgroupEntity();
        BeanUtils.copyProperties(saWorkgroupPojo, saWorkgroupEntity);
        this.saWorkgroupMapper.update(saWorkgroupEntity);
        return this.getEntity(saWorkgroupEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saWorkgroupMapper.delete(key);
    }
}
