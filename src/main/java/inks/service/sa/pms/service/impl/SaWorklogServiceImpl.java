package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaWorklogEntity;
import inks.service.sa.pms.domain.SaWorklogitemEntity;
import inks.service.sa.pms.domain.SaWorklogplanEntity;
import inks.service.sa.pms.domain.pojo.SaWorklogPojo;
import inks.service.sa.pms.domain.pojo.SaWorklogitemPojo;
import inks.service.sa.pms.domain.pojo.SaWorklogitemdetailPojo;
import inks.service.sa.pms.domain.pojo.SaWorklogplanPojo;
import inks.service.sa.pms.mapper.SaWorklogMapper;
import inks.service.sa.pms.mapper.SaWorklogitemMapper;
import inks.service.sa.pms.mapper.SaWorklogplanMapper;
import inks.service.sa.pms.service.SaWorklogService;
import inks.service.sa.pms.service.SaWorklogitemService;
import inks.service.sa.pms.service.SaWorklogplanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * (SaWorklog)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-04 13:59:14
 */
@Service("saWorklogService")
public class SaWorklogServiceImpl implements SaWorklogService {
    @Resource
    private SaWorklogMapper saWorklogMapper;

    @Resource
    private SaWorklogitemMapper saWorklogitemMapper;
    @Resource
    private SaWorklogplanMapper saWorklogplanMapper;
    /**
     * 服务对象Item
     */
    @Resource
    private SaWorklogitemService saWorklogitemService;
    @Resource
    private SaWorklogplanService saWorklogPlanService;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaWorklogPojo getEntity(String key) {
        return this.saWorklogMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaWorklogitemdetailPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWorklogitemdetailPojo> lst = saWorklogMapper.getPageList(queryParam);
            PageInfo<SaWorklogitemdetailPojo> pageInfo = new PageInfo<SaWorklogitemdetailPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaWorklogPojo getBillEntity(String key) {
        try {
            //读取主表
            SaWorklogPojo saWorklogPojo = this.saWorklogMapper.getEntity(key);
            //读取子表
            saWorklogPojo.setItem(saWorklogitemMapper.getList(saWorklogPojo.getId()));
            // 读取plan子表
            saWorklogPojo.setPlan(saWorklogplanMapper.getList(saWorklogPojo.getId()));
            return saWorklogPojo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaWorklogPojo> getBillList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWorklogPojo> lst = saWorklogMapper.getPageTh(queryParam);
            //循环设置每个主表对象的item子表 和 plan子表
            for (int i = 0; i < lst.size(); i++) {
                lst.get(i).setItem(saWorklogitemMapper.getList(lst.get(i).getId()));
                lst.get(i).setPlan(saWorklogplanMapper.getList(lst.get(i).getId()));
            }
            PageInfo<SaWorklogPojo> pageInfo = new PageInfo<SaWorklogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaWorklogPojo> getPageTh(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWorklogPojo> lst = saWorklogMapper.getPageTh(queryParam);
            PageInfo<SaWorklogPojo> pageInfo = new PageInfo<SaWorklogPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 新增数据
     *
     * @param saWorklogPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaWorklogPojo insert(SaWorklogPojo saWorklogPojo) {
//初始化NULL字段
        cleanNull(saWorklogPojo);
        //生成id
        String id = inksSnowflake.getSnowflake().nextIdStr();
        SaWorklogEntity saWorklogEntity = new SaWorklogEntity();
        BeanUtils.copyProperties(saWorklogPojo, saWorklogEntity);
        //设置id和新建日期
        saWorklogEntity.setId(id);
        saWorklogEntity.setRevision(1);  //乐观锁
        //插入主表
        // 通过日志两个子表构建主表的WorkToday、WorkTomorrow
        buildWorkTodayAndTomorrow(saWorklogPojo.getItem(), saWorklogPojo.getPlan(), saWorklogEntity);
        this.saWorklogMapper.insert(saWorklogEntity);
        //Item子表处理
        List<SaWorklogitemPojo> lst = saWorklogPojo.getItem();
        if (lst != null) {
            //循环每个item子表
            for (SaWorklogitemPojo saWorklogitemPojo : lst) {
                //初始化item的NULL
                SaWorklogitemPojo itemPojo = this.saWorklogitemService.clearNull(saWorklogitemPojo);
                SaWorklogitemEntity saWorklogitemEntity = new SaWorklogitemEntity();
                BeanUtils.copyProperties(itemPojo, saWorklogitemEntity);
                //设置id和Pid
                saWorklogitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saWorklogitemEntity.setPid(id);
                saWorklogitemEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saWorklogitemMapper.insert(saWorklogitemEntity);
            }
        }
        // plan子表处理
        List<SaWorklogplanPojo> plan = saWorklogPojo.getPlan();
        if (plan != null) {
            //循环每个plan子表
            for (SaWorklogplanPojo saWorklogplanPojo : plan) {
                //初始化plan的NULL
                SaWorklogplanPojo planPojo = this.saWorklogPlanService.clearNull(saWorklogplanPojo);
                SaWorklogplanEntity saWorklogplanEntity = new SaWorklogplanEntity();
                BeanUtils.copyProperties(planPojo, saWorklogplanEntity);
                //设置id和Pid
                saWorklogplanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
                saWorklogplanEntity.setPid(id);
                saWorklogplanEntity.setRevision(1);  //乐观锁
                //插入子表
                this.saWorklogplanMapper.insert(saWorklogplanEntity);
            }
        }
        //返回Bill实例
        return this.getBillEntity(saWorklogEntity.getId());

    }

    // 通过日志两个子表构建主表的WorkToday、WorkTomorrow
    private static void buildWorkTodayAndTomorrow(List<SaWorklogitemPojo> lst, List<SaWorklogplanPojo> plan, SaWorklogEntity saWorklogEntity) {
        // 今日日志内容、明日日志内容
        if (lst != null) {
            StringBuilder worktodayBuilder = new StringBuilder("<ol>");
            for (SaWorklogitemPojo saWorklogitemPojo : lst) {
                // 拼接<li>标签内容
                worktodayBuilder.append("<li style=\"line-height: 2;\">")
                        .append(saWorklogitemPojo.getItemdesc()) //"oam: /wx/menu{appid}}/createByJson自定义菜单构建``"
                        .append(" (")
                        .append(saWorklogitemPojo.getWorktime()) //2
                        .append("H)</li>");
            }
            worktodayBuilder.append("</ol>");
            saWorklogEntity.setWorktoday(worktodayBuilder.toString());
        }

        if (plan != null) {
            StringBuilder worktomorrowBuilder = new StringBuilder("<ol>");
            for (SaWorklogplanPojo saWorklogplanPojo : plan) {
                // 拼接<li>标签内容
                worktomorrowBuilder.append("<li style=\"line-height: 2;\">")
                        .append(saWorklogplanPojo.getItemdesc())
                        .append(" (")
                        .append(saWorklogplanPojo.getWorktimeexpect())
                        .append("H)</li>");
            }
            worktomorrowBuilder.append("</ol>");
            saWorklogEntity.setWorktomorrow(worktomorrowBuilder.toString());
        }
    }


    /**
     * 修改数据
     *
     * @param saWorklogPojo 实例对象
     * @return 实例对象
     */
    @Override
    @Transactional
    public SaWorklogPojo update(SaWorklogPojo saWorklogPojo) {
        //主表更改
        SaWorklogEntity saWorklogEntity = new SaWorklogEntity();
        BeanUtils.copyProperties(saWorklogPojo, saWorklogEntity);
        // 通过日志两个子表构建主表的WorkToday、WorkTomorrow
        buildWorkTodayAndTomorrow(saWorklogPojo.getItem(), saWorklogPojo.getPlan(), saWorklogEntity);
        this.saWorklogMapper.update(saWorklogEntity);
        if (saWorklogPojo.getItem() != null) {
            //1.Item子表处理
            List<SaWorklogitemPojo> lst = saWorklogPojo.getItem();
            //获取被删除的Item
            List<String> lstDelIds = saWorklogMapper.getDelItemIds(saWorklogPojo);

            if (lstDelIds != null) {
                //循环每个删除item子表
                for (String lstDelId : lstDelIds) {
                    this.saWorklogitemMapper.delete(lstDelId);
                }
            }

            if (lst != null) {
                //循环每个item子表
                for (SaWorklogitemPojo saWorklogitemPojo : lst) {
                    SaWorklogitemEntity saWorklogitemEntity = new SaWorklogitemEntity();
                    if ("".equals(saWorklogitemPojo.getId()) || saWorklogitemPojo.getId() == null) {
                        //初始化item的NULL
                        SaWorklogitemPojo itemPojo = this.saWorklogitemService.clearNull(saWorklogitemPojo);
                        BeanUtils.copyProperties(itemPojo, saWorklogitemEntity);
                        //设置id和Pid
                        saWorklogitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // item id
                        saWorklogitemEntity.setPid(saWorklogEntity.getId());  // 主表 id
                        saWorklogitemEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saWorklogitemMapper.insert(saWorklogitemEntity);
                    } else {
                        BeanUtils.copyProperties(saWorklogitemPojo, saWorklogitemEntity);
                        this.saWorklogitemMapper.update(saWorklogitemEntity);
                    }
                }
            }

            //2.获取被删除的plan---------------------------------
            List<String> lstDelPlanIds = saWorklogMapper.getDelPlanIds(saWorklogPojo);
            if (lstDelPlanIds != null) {
                //循环每个删除plan子表
                for (String lstDelPlanId : lstDelPlanIds) {
                    this.saWorklogplanMapper.delete(lstDelPlanId);
                }
            }
            // plan子表处理
            List<SaWorklogplanPojo> plan = saWorklogPojo.getPlan();
            if (plan != null) {
                //循环每个plan子表
                for (SaWorklogplanPojo saWorklogplanPojo : plan) {
                    SaWorklogplanEntity saWorklogplanEntity = new SaWorklogplanEntity();
                    if ("".equals(saWorklogplanPojo.getId()) || saWorklogplanPojo.getId() == null) {
                        //初始化plan的NULL
                        SaWorklogplanPojo planPojo = this.saWorklogPlanService.clearNull(saWorklogplanPojo);
                        BeanUtils.copyProperties(planPojo, saWorklogplanEntity);
                        //设置id和Pid
                        saWorklogplanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());  // plan id
                        saWorklogplanEntity.setPid(saWorklogEntity.getId());  // 主表 id
                        saWorklogplanEntity.setRevision(1);  // 乐观锁
                        //插入子表
                        this.saWorklogplanMapper.insert(saWorklogplanEntity);
                    } else {
                        BeanUtils.copyProperties(saWorklogplanPojo, saWorklogplanEntity);
                        this.saWorklogplanMapper.update(saWorklogplanEntity);
                    }
                }
            }
        }
        //返回Bill实例
        return this.getBillEntity(saWorklogEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    @Transactional
    public int delete(String key) {
        SaWorklogPojo saWorklogPojo = this.getBillEntity(key);
        //Item子表处理
        List<SaWorklogitemPojo> lst = saWorklogPojo.getItem();
        if (lst != null) {
            //循环每个删除item子表
            for (SaWorklogitemPojo saWorklogitemPojo : lst) {
                this.saWorklogitemMapper.delete(saWorklogitemPojo.getId());
            }
        }
        // plan子表处理
        List<SaWorklogplanPojo> plan = saWorklogPojo.getPlan();
        if (plan != null) {
            //循环每个删除plan子表
            for (SaWorklogplanPojo saWorklogplanPojo : plan) {
                this.saWorklogplanMapper.delete(saWorklogplanPojo.getId());
            }
        }
        return this.saWorklogMapper.delete(key);
    }

    @Override
    public boolean checkWorklogTitle(String title) {
        int count = saWorklogMapper.checkWorklogTitle(title);
        return count > 0;
    }


    private static void cleanNull(SaWorklogPojo saWorklogPojo) {
        if (saWorklogPojo.getId() == null) saWorklogPojo.setId("");
        if (saWorklogPojo.getWorkdate() == null) saWorklogPojo.setWorkdate(new Date());
        if(saWorklogPojo.getWorktype()==null) saWorklogPojo.setWorktype(0);
        if(saWorklogPojo.getWeather()==null) saWorklogPojo.setWeather("");
        if (saWorklogPojo.getWorktoday() == null) saWorklogPojo.setWorktoday("");
        if (saWorklogPojo.getWorktomorrow() == null) saWorklogPojo.setWorktomorrow("");
        if (saWorklogPojo.getTitle() == null) saWorklogPojo.setTitle("");
        if (saWorklogPojo.getSendemailnum() == null) saWorklogPojo.setSendemailnum(0);
        if (saWorklogPojo.getToemail() == null) saWorklogPojo.setToemail("");
        if (saWorklogPojo.getOtheremails() == null) saWorklogPojo.setOtheremails("");
        if (saWorklogPojo.getSummary() == null) saWorklogPojo.setSummary("");
        if (saWorklogPojo.getCreateby() == null) saWorklogPojo.setCreateby("");
        if (saWorklogPojo.getCreatebyid() == null) saWorklogPojo.setCreatebyid("");
        if (saWorklogPojo.getCreatedate() == null) saWorklogPojo.setCreatedate(new Date());
        if (saWorklogPojo.getLister() == null) saWorklogPojo.setLister("");
        if (saWorklogPojo.getListerid() == null) saWorklogPojo.setListerid("");
        if (saWorklogPojo.getModifydate() == null) saWorklogPojo.setModifydate(new Date());
        if (saWorklogPojo.getCustom1() == null) saWorklogPojo.setCustom1("");
        if (saWorklogPojo.getCustom2() == null) saWorklogPojo.setCustom2("");
        if (saWorklogPojo.getCustom3() == null) saWorklogPojo.setCustom3("");
        if (saWorklogPojo.getCustom4() == null) saWorklogPojo.setCustom4("");
        if (saWorklogPojo.getCustom5() == null) saWorklogPojo.setCustom5("");
        if (saWorklogPojo.getCustom6() == null) saWorklogPojo.setCustom6("");
        if (saWorklogPojo.getCustom7() == null) saWorklogPojo.setCustom7("");
        if (saWorklogPojo.getCustom8() == null) saWorklogPojo.setCustom8("");
        if (saWorklogPojo.getCustom9() == null) saWorklogPojo.setCustom9("");
        if (saWorklogPojo.getCustom10() == null) saWorklogPojo.setCustom10("");
        if (saWorklogPojo.getTenantid() == null) saWorklogPojo.setTenantid("");
        if (saWorklogPojo.getRevision() == null) saWorklogPojo.setRevision(0);
    }


}
