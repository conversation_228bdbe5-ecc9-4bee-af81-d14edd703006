package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaWorklogitemEntity;
import inks.service.sa.pms.domain.pojo.SaWorklogitemPojo;
import inks.service.sa.pms.mapper.SaWorklogitemMapper;
import inks.service.sa.pms.service.SaWorklogitemService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工作日志今日子表(SaWorklogitem)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-04 12:58:20
 */
@Service("saWorklogitemService")
public class SaWorklogitemServiceImpl implements SaWorklogitemService {
    @Resource
    private SaWorklogitemMapper saWorklogitemMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaWorklogitemPojo getEntity(String key) {
        return this.saWorklogitemMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaWorklogitemPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWorklogitemPojo> lst = saWorklogitemMapper.getPageList(queryParam);
            PageInfo<SaWorklogitemPojo> pageInfo = new PageInfo<SaWorklogitemPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaWorklogitemPojo> getList(String Pid) {
        try {
            List<SaWorklogitemPojo> lst = saWorklogitemMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saWorklogitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWorklogitemPojo insert(SaWorklogitemPojo saWorklogitemPojo) {
        //初始化item的NULL
        SaWorklogitemPojo itempojo = this.clearNull(saWorklogitemPojo);
        SaWorklogitemEntity saWorklogitemEntity = new SaWorklogitemEntity();
        BeanUtils.copyProperties(itempojo, saWorklogitemEntity);
        //生成雪花id
        saWorklogitemEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saWorklogitemEntity.setRevision(1);  //乐观锁
        this.saWorklogitemMapper.insert(saWorklogitemEntity);
        return this.getEntity(saWorklogitemEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saWorklogitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWorklogitemPojo update(SaWorklogitemPojo saWorklogitemPojo) {
        SaWorklogitemEntity saWorklogitemEntity = new SaWorklogitemEntity();
        BeanUtils.copyProperties(saWorklogitemPojo, saWorklogitemEntity);
        this.saWorklogitemMapper.update(saWorklogitemEntity);
        return this.getEntity(saWorklogitemEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saWorklogitemMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saWorklogitemPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWorklogitemPojo clearNull(SaWorklogitemPojo saWorklogitemPojo) {
        //初始化NULL字段
        if (saWorklogitemPojo.getPid() == null) saWorklogitemPojo.setPid("");
        if (saWorklogitemPojo.getItemtype() == null) saWorklogitemPojo.setItemtype("");
        if (saWorklogitemPojo.getProjectid() == null) saWorklogitemPojo.setProjectid("");
        if (saWorklogitemPojo.getWorktime() == null) saWorklogitemPojo.setWorktime(0D);
        if (saWorklogitemPojo.getItemdesc() == null) saWorklogitemPojo.setItemdesc("");
        if (saWorklogitemPojo.getModulecode() == null) saWorklogitemPojo.setModulecode("");
        if (saWorklogitemPojo.getWorkcomprate() == null) saWorklogitemPojo.setWorkcomprate(0);
        if (saWorklogitemPojo.getRownum() == null) saWorklogitemPojo.setRownum(0);
        if (saWorklogitemPojo.getRemark() == null) saWorklogitemPojo.setRemark("");
        if (saWorklogitemPojo.getCustom1() == null) saWorklogitemPojo.setCustom1("");
        if (saWorklogitemPojo.getCustom2() == null) saWorklogitemPojo.setCustom2("");
        if (saWorklogitemPojo.getCustom3() == null) saWorklogitemPojo.setCustom3("");
        if (saWorklogitemPojo.getCustom4() == null) saWorklogitemPojo.setCustom4("");
        if (saWorklogitemPojo.getCustom5() == null) saWorklogitemPojo.setCustom5("");
        if (saWorklogitemPojo.getRevision() == null) saWorklogitemPojo.setRevision(0);
        return saWorklogitemPojo;
    }
}
