package inks.service.sa.pms.service.impl;

import com.github.pagehelper.PageHelper;
import com.github.pagehelper.PageInfo;
import inks.common.core.domain.QueryParam;
import inks.common.core.exception.BaseBusinessException;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.SaWorklogplanEntity;
import inks.service.sa.pms.domain.pojo.SaWorklogplanPojo;
import inks.service.sa.pms.mapper.SaWorklogplanMapper;
import inks.service.sa.pms.service.SaWorklogplanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 工作日志计划子表(SaWorklogplan)表服务实现类
 *
 * <AUTHOR>
 * @since 2023-09-04 14:31:24
 */
@Service("saWorklogplanService")
public class SaWorklogplanServiceImpl implements SaWorklogplanService {
    @Resource
    private SaWorklogplanMapper saWorklogplanMapper;

    /**
     * 通过ID查询单条数据
     *
     * @param key 主键
     * @return 实例对象
     */
    @Override
    public SaWorklogplanPojo getEntity(String key) {
        return this.saWorklogplanMapper.getEntity(key);
    }

    /**
     * 分页查询
     *
     * @param queryParam 筛选条件
     * @return 查询结果
     */
    @Override
    public PageInfo<SaWorklogplanPojo> getPageList(QueryParam queryParam) {
        PageHelper.startPage(queryParam.getPageNum(), queryParam.getPageSize());
        try {
            List<SaWorklogplanPojo> lst = saWorklogplanMapper.getPageList(queryParam);
            PageInfo<SaWorklogplanPojo> pageInfo = new PageInfo<SaWorklogplanPojo>(lst);
            return pageInfo;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }

    /**
     * 查询 所有Item
     *
     * @param Pid 筛选条件
     * @return 查询结果
     */
    @Override
    public List<SaWorklogplanPojo> getList(String Pid) {
        try {
            List<SaWorklogplanPojo> lst = saWorklogplanMapper.getList(Pid);
            return lst;
        } catch (Exception e) {
            throw new BaseBusinessException(e.getMessage());
        }
    }


    /**
     * 新增数据
     *
     * @param saWorklogplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWorklogplanPojo insert(SaWorklogplanPojo saWorklogplanPojo) {
        //初始化item的NULL
        SaWorklogplanPojo itempojo = this.clearNull(saWorklogplanPojo);
        SaWorklogplanEntity saWorklogplanEntity = new SaWorklogplanEntity();
        BeanUtils.copyProperties(itempojo, saWorklogplanEntity);
        //生成雪花id
        saWorklogplanEntity.setId(inksSnowflake.getSnowflake().nextIdStr());
        saWorklogplanEntity.setRevision(1);  //乐观锁
        this.saWorklogplanMapper.insert(saWorklogplanEntity);
        return this.getEntity(saWorklogplanEntity.getId());

    }

    /**
     * 修改数据
     *
     * @param saWorklogplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWorklogplanPojo update(SaWorklogplanPojo saWorklogplanPojo) {
        SaWorklogplanEntity saWorklogplanEntity = new SaWorklogplanEntity();
        BeanUtils.copyProperties(saWorklogplanPojo, saWorklogplanEntity);
        this.saWorklogplanMapper.update(saWorklogplanEntity);
        return this.getEntity(saWorklogplanEntity.getId());
    }

    /**
     * 通过主键删除数据
     *
     * @param key 主键
     * @return 是否成功
     */
    @Override
    public int delete(String key) {
        return this.saWorklogplanMapper.delete(key);
    }

    /**
     * 修改数据
     *
     * @param saWorklogplanPojo 实例对象
     * @return 实例对象
     */
    @Override
    public SaWorklogplanPojo clearNull(SaWorklogplanPojo saWorklogplanPojo) {
        //初始化NULL字段
        if (saWorklogplanPojo.getPid() == null) saWorklogplanPojo.setPid("");
        if (saWorklogplanPojo.getItemtype() == null) saWorklogplanPojo.setItemtype("");
        if (saWorklogplanPojo.getProjectid() == null) saWorklogplanPojo.setProjectid("");
        if (saWorklogplanPojo.getWorktimeexpect() == null) saWorklogplanPojo.setWorktimeexpect(0D);
        if (saWorklogplanPojo.getItemdesc() == null) saWorklogplanPojo.setItemdesc("");
        if (saWorklogplanPojo.getModulecode() == null) saWorklogplanPojo.setModulecode("");
        if (saWorklogplanPojo.getWorkcomprate() == null) saWorklogplanPojo.setWorkcomprate(0);
        if (saWorklogplanPojo.getRownum() == null) saWorklogplanPojo.setRownum(0);
        if (saWorklogplanPojo.getRemark() == null) saWorklogplanPojo.setRemark("");
        if (saWorklogplanPojo.getCustom1() == null) saWorklogplanPojo.setCustom1("");
        if (saWorklogplanPojo.getCustom2() == null) saWorklogplanPojo.setCustom2("");
        if (saWorklogplanPojo.getCustom3() == null) saWorklogplanPojo.setCustom3("");
        if (saWorklogplanPojo.getCustom4() == null) saWorklogplanPojo.setCustom4("");
        if (saWorklogplanPojo.getCustom5() == null) saWorklogplanPojo.setCustom5("");
        if (saWorklogplanPojo.getRevision() == null) saWorklogplanPojo.setRevision(0);
        return saWorklogplanPojo;
    }
}
