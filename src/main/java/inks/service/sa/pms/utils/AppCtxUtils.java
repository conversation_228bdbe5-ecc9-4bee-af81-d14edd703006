package inks.service.sa.pms.utils;

import org.springframework.beans.BeansException;
import org.springframework.context.ApplicationContext;
import org.springframework.context.ApplicationContextAware;
import org.springframework.stereotype.Component;

@Component
public class AppCtxUtils implements ApplicationContextAware {

    private static ApplicationContext appCtx;

    public static ApplicationContext getAppCtx() {
        return appCtx;
    }

    public static <T> T getBean(Class<T> clazz) {
        return appCtx.getBean(clazz);
    }

    public static <T> T getBean(String beanName) {
        return (T) appCtx.getBean(beanName);
    }

    @Override
    public void setApplicationContext(ApplicationContext appCtx) throws BeansException {
        AppCtxUtils.appCtx = appCtx;
    }
}