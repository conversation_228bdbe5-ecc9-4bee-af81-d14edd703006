//
//
//package inks.service.sa.pms.utils;
//
//import inks.common.redis.service.RedisService;
//import org.junit.Test;
//import org.springframework.boot.test.context.SpringBootTest;
//import org.springframework.data.redis.core.RedisTemplate;
//
//
//import javax.annotation.Resource;
//import javax.mail.MessagingException;
//import java.io.UnsupportedEncodingException;
//import java.util.HashMap;
//import java.util.Map;
//import java.util.concurrent.TimeUnit;
//
//
//@SpringBootTest
//public class MyPmsTest {
//    @Resource
//    private SaRedisService saRedisService;
//    @Resource
//    private RedisTemplate redisTemplate;
//    @Resource
//    private SendEmail sendEmail;
//
//    @Test
//    public void t2() throws MessagingException, UnsupportedEncodingException {
//        SendEmail sendEmail = new SendEmail();
//        //发送邮件
//        try {
//            sendEmail.sendEmail("<EMAIL>","ecjMGZCpFEveWxho","fromName/nanno","<EMAIL>","",
//                    "snbject/工作日志-nanno-20230306","content/内容");
//        } catch (UnsupportedEncodingException | MessagingException e) {
//            throw new RuntimeException(e);
//        }
//    }
//    @Test
//    public void t3() throws MessagingException, UnsupportedEncodingException {
//        SendEmail sendEmail = new SendEmail();
//        //发送邮件
//        try {
//            sendEmail.sendEmail("<EMAIL>","pQKDBeLhWflxoqPa","fromName/nanno","<EMAIL>","<EMAIL>;<EMAIL>;<EMAIL>",
//                    "snbject/工作日志-nanno-20230306","content/内容");
//        } catch (UnsupportedEncodingException | MessagingException e) {
//            throw new RuntimeException(e);
//        }
//    }
//    @Test
//    public void t4() throws MessagingException, UnsupportedEncodingException {
//        SendEmail sendEmail = new SendEmail();
//        //发送邮件
//        try {
//            sendEmail.sendEmail("<EMAIL>","ffNunH4HV2EOBMl2","","<EMAIL>","<EMAIL>;<EMAIL>",
//                    "snbject/工作日志-abc-20230306","content/内容");
//        } catch (UnsupportedEncodingException | MessagingException e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    @Test
//    public void t5() throws MessagingException, UnsupportedEncodingException {
//        SendEmail sendEmail = new SendEmail();
//        //发送邮件
//        try {
//            sendEmail.sendEmail("<EMAIL>","8LfwiBq1zjncZJT6","","<EMAIL>","",
//                    "snbject/工作日志-abc-20230306","content/内容");
//        } catch (UnsupportedEncodingException | MessagingException e) {
//            throw new RuntimeException(e);
//        }
//    }
//    @Test
//    public void t6() throws MessagingException, UnsupportedEncodingException {
//        SendEmail sendEmail = new SendEmail();
//        //发送邮件
//        try {
//            sendEmail.sendEmail("<EMAIL>","ypxGZ3rkPtjVBMaJ","","<EMAIL>","",
//                    "snbject/工作日志-abc-20230306","content/内容");
//        } catch (UnsupportedEncodingException | MessagingException e) {
//            throw new RuntimeException(e);
//        }
//    }
//
//    @Test
//    public void t1() {
//        //SendEmail sendEmail = new SendEmail();
//        //sendEmail.sendEmail("<EMAIL>",fromUserName,toemail,otheremails,
//        //        "标题111","content/内容");    }
//    }
//}
