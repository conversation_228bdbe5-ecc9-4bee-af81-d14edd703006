package inks.service.sa.pms.utils;

import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

@Component
public class MyRedisTemplate {

    @Resource
    private RedisTemplate<String, Object> redisTemplate;

    // 存储一个键值对
    public void set(String key, Object value) {
        redisTemplate.opsForValue().set(key, value);
    }

    // 存储一个键值对并设置过期时间
    public void setWithExpiry(String key, Object value, long timeout, TimeUnit unit) {
        redisTemplate.opsForValue().set(key, value, timeout, unit);
    }

    // 为一个键设置过期时间
    public void expireKey(String key, long timeout, TimeUnit unit) {
        redisTemplate.expire(key, timeout, unit);
    }

    // 获取键对应的值
    public Object get(String key) {
        return redisTemplate.opsForValue().get(key);
    }

    // 检查键是否存在
    public boolean hasKey(String key) {
        return redisTemplate.hasKey(key);
    }

    // 删除键
    public void delete(String key) {
        redisTemplate.delete(key);
    }

    // 自增操作
    public Long increment(String key, long delta) {
        return redisTemplate.opsForValue().increment(key, delta);
    }

    // 自减操作
    public Long decrement(String key, long delta) {
        return redisTemplate.opsForValue().decrement(key, delta);
    }

    // 刷新缓存，清空所有键
    public void flushAll() {
        redisTemplate.getConnectionFactory().getConnection().flushDb();
    }

    // 获取键的过期时间
    public long getTTL(String key) {
        return redisTemplate.getExpire(key);
    }
}
