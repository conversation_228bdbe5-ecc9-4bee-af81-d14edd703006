package inks.service.sa.pms.utils.RSA;

public class MyRSA {
    // 邮件的RSA私钥 +公钥 (邮件名: 回复: 关于软件注册码解析测试-20231111 2023年11月17日周五9:46)
    public static final String PRIVATE_KEY =
            "MIIEvwIBADANBgkqhkiG9w0BAQEFAASCBKkwggSlAgEAAoIBAQDY6FQFTEVL+lTE" +
                    "B5fAfLsDx7MyBJVmkfb70iFWD7IATn7GoSV5X4+XPHzDlBi/cm/av8op/1oCxfZK" +
                    "fmHFthxJcQPCB1gE6C9ZmrhkPl8/MtR6oBaXLVB4e9EViv5FEE1SbCZxImql4Qhi" +
                    "M+Nu42CAAb7jUogynASXD8MB9cx09A6y5zjUTEbrhLqNPgPW7l4QdbLAer1SKoOe" +
                    "x4/Wgl5RgbLoG3b2uaBmBJQu4/irtn9M1GiUPraMx11UdN4+FWWco89cfwbniO6w" +
                    "FJq2HoP4c4aB2pWOVXJ6yripFfRRAZbT8c/2xvicTsBGZBhRPZktVcgVX3qHXdEz" +
                    "xb6/SdgHAgMBAAECggEBAK9eFyA5bAVjnUjoQp7TuXfy60RTRF8rQB6U43LrOaTZ" +
                    "0dHR07RvuG4z70NI50FRynF3goG1/BYl61yLFY1+ERynjLxAp78/4Zh2aQJTEzrL" +
                    "Y9LIv7r2XgsrP/5E5BL+fSVYXzQ0+Tin2dlbzlOG7ixQPP8ZaRhR37wqeCWLjwQ2" +
                    "F8TOWtiw22QWPfOXwVus0mj+nTLyaANceG6z4DTmKHCdIAeRfRiqF78JaGpCBF2f" +
                    "je0R0RHVxJj4A229v2Yn1W9mgDkSMOnpYW4PJ0A1Ptho9/7nIZcT40OTD2aIHsWz" +
                    "no+AVxf537WY3d2AMGhdocq/6YCpjWAhOcIibm+cZrECgYEA/qbe6HqOSM38PZvg" +
                    "5u5Q1bKqVWswmch+EaLTYRgz3kUFLo3kYb3j08nNDNBRtkZQ9f/ba1b2ahfyxJtT" +
                    "vcAqlQY5Ct4asqZNACGq1gq9qYpkXS7XGrcVKzOTEQRdyODpKMWQCWV8s2URLmlR" +
                    "ic1TDDSiRBOzTN3M5KF2ux/Izz0CgYEA2g5Ne5jGXriExv2bqogDoXsxmPImXK0C" +
                    "NGp/C05A6Fs7UW9hGpCbgq7wJsBbq6MEe+ocx7BphIlVDbuWd9D0WqVCdx1zjtc2" +
                    "BXXMgeRbtR0EYqK/JCdXCHFYoTW7up/ASIyG8jGzIk5hZJJW0OqQuSr6mE46FAwn" +
                    "cbDt93KTuJMCgYEAkZIi6xLkM4pNH4r0Tg799qtj4+Car86FAt/pph+Hxw/9nldO" +
                    "rvE2Q+OgbCrKC0C3F2ljrUDRLGT0Sp/ghyBLN1WPVj8RU/7tFZ4pYqJROZH7PamB" +
                    "Hax7VW7uK1QrqKGpp70IUdbKKmMkJzvdeMD/MIMGH5SDcvV4I1qJ3P6WdKECgYBX" +
                    "9wke4SS1NW6q78gKZVBBpnb9okIDhO+swYF6yBi7I4KseWgM1WVGtNfAaj0Q4nR+" +
                    "oJT3oZ0PkG81WQV8VFke8Cf98W2apArw7PKRO9QbHheUyt6c8RnVGBSPBCQ1RxjE" +
                    "q5wMvCvtnP0BG1/Wa6nyaPoq0vPu/nAzFxMIjbo17QKBgQDtnzxiilHJHMK6ru8a" +
                    "EfOza1Ac/txKVJHpJxW7QZse9bbKHIHiHPV6Vt/B+Qu99nSmAIKrEXLQ0oPY0an5" +
                    "DPBHHvoDDxq2o6FOBDK3jCEnJBl6DL075n84DYITBFb+NHyY+lggykMpTVu0VLxb" +
                    "1Yxm52i/4cnz4rRVe0jc2QYn1Q==";
    public static final String PUBLIC_KEY = "MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA2OhUBUxFS/pUxAeXwHy7" +
            "A8ezMgSVZpH2+9IhVg+yAE5+xqEleV+Plzx8w5QYv3Jv2r/KKf9aAsX2Sn5hxbYc" +
            "SXEDwgdYBOgvWZq4ZD5fPzLUeqAWly1QeHvRFYr+RRBNUmwmcSJqpeEIYjPjbuNg" +
            "gAG+41KIMpwElw/DAfXMdPQOsuc41ExG64S6jT4D1u5eEHWywHq9UiqDnseP1oJe" +
            "UYGy6Bt29rmgZgSULuP4q7Z/TNRolD62jMddVHTePhVlnKPPXH8G54jusBSath6D" +
            "+HOGgdqVjlVyesq4qRX0UQGW0/HP9sb4nE7ARmQYUT2ZLVXIFV96h13RM8W+v0nY" +
            "BwIDAQAB";
}
