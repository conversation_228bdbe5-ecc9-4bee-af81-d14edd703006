package inks.service.sa.pms.utils.RSA;


import javax.crypto.Cipher;
import java.security.KeyFactory;
import java.security.PublicKey;
import java.security.spec.X509EncodedKeySpec;
import java.util.Base64;

//公钥加密   Encryptor:加密器
public class RSAEncryptor {
    public static String encrypt(String plainText, String publicKeyStr) throws Exception {
        byte[] publicKeyBytes = Base64.getDecoder().decode(publicKeyStr);
        X509EncodedKeySpec keySpec = new X509EncodedKeySpec(publicKeyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        PublicKey publicKey = keyFactory.generatePublic(keySpec);

        Cipher cipher = Cipher.getInstance("RSA");
        cipher.init(Cipher.ENCRYPT_MODE, publicKey);

        byte[] encryptedBytes = cipher.doFinal(plainText.getBytes());
        return Base64.getEncoder().encodeToString(encryptedBytes);
    }

    public static void main(String[] args) {
        try {
            String publicKeyStr = MyRSA.PUBLIC_KEY;
            String plainText = "Hello, RSA encryption!";
            String encryptedText = encrypt(plainText, publicKeyStr);
            System.out.println("Encrypted Text: " + encryptedText);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


//    public static void main(String[] args) {
//        try {
//            String publicKeyStr = "your_public_key_here";
//            String plainText = "Hello, RSA encryption!";
//            String encryptedText = encrypt(plainText, publicKeyStr);
//            System.out.println("Encrypted Text: " + encryptedText);
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
}
