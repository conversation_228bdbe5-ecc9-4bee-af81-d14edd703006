package inks.service.sa.pms.utils.RSA;


/**
 * <AUTHOR>
 * @Description RSA使用示例
 * @time 2023/11/17 10:22
 */
public class RSATest {


    // 已经有了公钥和私钥字符串: 直接使用加密解密 (没有公钥和私钥字符串,需先生成公钥和私钥字符串(见下面另一个方法))
    public static void main(String[] args) {
        try {
            // 公钥,私钥字符串
            String publicKeyStr = MyRSA.PUBLIC_KEY;
            String privateKeyStr = MyRSA.PRIVATE_KEY;
            // 待加密的文本
            String plainText = "你好，RSA加密解密！";
            // 使用公钥加密
            String encryptedText = RSAEncryptor.encrypt(plainText, publicKeyStr);
            System.out.println("加密后的文本: " + encryptedText);
            // 使用私钥解密
            String decryptedText = RSADecryptor.decrypt(encryptedText, privateKeyStr);
            System.out.println("解密后的文本: " + decryptedText);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

//    还没有公钥和私钥字符串: 先生成公钥和私钥字符串,再使用加密解密
//    public static void main(String[] args) throws NoSuchAlgorithmException {
//        // 每次都随机生成密钥对
//        KeyPair keyPair = RSAKeyGenerator.generateKeyPair();
//        PublicKey publicKey = keyPair.getPublic();
//        PrivateKey privateKey = keyPair.getPrivate();
//        // 显示生成的公钥和私钥
//        System.out.println("公钥: " + publicKey);//都是数字
//        System.out.println("私钥: " + privateKey);
//        // PublicKey,PrivateKey转String
//        String publicKeyStr = Base64.getEncoder().encodeToString(publicKey.getEncoded());
//        String privateKeyStr = Base64.getEncoder().encodeToString(privateKey.getEncoded());
//        System.out.println("privateKeyStr = " + privateKeyStr);//都是数字+英文
//        System.out.println("publicKeyStr = " + publicKeyStr);
//    }


}
