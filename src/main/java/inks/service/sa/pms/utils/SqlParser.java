package inks.service.sa.pms.utils;

import inks.common.core.domain.LoginUser;
import inks.service.sa.pms.domain.pojo.SaDbdesignPojo;
import inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

public class SqlParser {


    /**
     * 解析建表语句，提取表设计相关信息----构建主表Sa_DbDesign信息
     */
    public static SaDbdesignPojo parseCreateTableSql(String createTableSql, LoginUser loginUser) {
        createTableSql = createTableSql.replace("`", ""); // 移除反引号

        // 此方法移除字符串中最外层括号内的内容，并返回处理后的字符串。
        String sql = removeOuterParenthesesContent(createTableSql);

        String tableName = "";
        String tableComment = "";
        String charset = "";
        String collate = "";

        // 匹配表名的正则表达式
        String tablePattern = "CREATE TABLE\\s+([\\w_]+)";
        // 匹配表注释的正则表达式，处理不同的引号和空格
        String commentPattern = "COMMENT\\s*(=|')?\\s*['\"]?([^'\"]+)['\"]?";
        // 匹配CHARSET的正则表达式
        String charsetPattern = "CHARSET\\s*=\\s*([\\w_]+)";
        // 匹配COLLATE的正则表达式
        String collatePattern = "COLLATE\\s*=\\s*([\\w_]+)";

        // 使用正则表达式提取表名，忽略大小写
        Pattern tableRegex = Pattern.compile(tablePattern, Pattern.CASE_INSENSITIVE);
        Matcher tableMatcher = tableRegex.matcher(sql);
        if (tableMatcher.find()) {
            tableName = tableMatcher.group(1);  // 表名
        }

        // 使用正则表达式提取表注释，忽略大小写
        Pattern commentRegex = Pattern.compile(commentPattern, Pattern.CASE_INSENSITIVE);
        Matcher commentMatcher = commentRegex.matcher(sql);
        if (commentMatcher.find()) {
            tableComment = commentMatcher.group(2).trim();  // 表注释
        }

        // 使用正则表达式提取字符集，忽略大小写
        Pattern charsetRegex = Pattern.compile(charsetPattern, Pattern.CASE_INSENSITIVE);
        Matcher charsetMatcher = charsetRegex.matcher(sql);
        if (charsetMatcher.find()) {
            charset = charsetMatcher.group(1).trim();  // 字符集
        }

        // 使用正则表达式提取排序规则，忽略大小写
        Pattern collateRegex = Pattern.compile(collatePattern, Pattern.CASE_INSENSITIVE);
        Matcher collateMatcher = collateRegex.matcher(sql);
        if (collateMatcher.find()) {
            collate = collateMatcher.group(1).trim();  // 排序规则
        }

        PrintColor.yellow("表名: " + tableName + ", 表注释: " + tableComment + ", 字符集: " + charset + ", 排序规则: " + collate);
        SaDbdesignPojo tableInfo = new SaDbdesignPojo();
        tableInfo.setTablename(tableName); // 设置表名
        tableInfo.setComment(tableComment); // 设置表注释
        tableInfo.setCharacterset(charset); // 设置字符集
        tableInfo.setCollation(collate); // 设置排序规则
        // 设置版本号
        tableInfo.setVersionnumber(String.valueOf(LocalDate.now()));

        return tableInfo;
    }

    /**
     * 解析字段信息       ----构建字段子表Sa_DbDesignItem信息
     */
    public static List<SaDbdesignitemPojo> parseFieldInfo(String createTableSql, LoginUser loginUser) {
        createTableSql = createTableSql.replace("`", "");
        List<SaDbdesignitemPojo> fieldInfos = new ArrayList<>();

        // 提取括号内的内容
        String fieldsDefinition = extractOuterParenthesesContent(createTableSql);
        if (fieldsDefinition == null) {
            return fieldInfos;
        }

        // 分割每个字段定义
        List<String> fieldDefinitions = splitFieldDefinitions(fieldsDefinition);

        int rowNum = 1;
        for (String fieldDef : fieldDefinitions) {
            // 跳过主键定义行（如果是单独的主键定义）
            if (!fieldDef.trim().toUpperCase().startsWith("PRIMARY KEY")) {
                SaDbdesignitemPojo fieldInfo = parseFieldDefinition(fieldDef.trim());
                if (fieldInfo != null) {
                    fieldInfo.setRownum(rowNum++);
                    fieldInfo.setPrimarymark(isPrimaryKey(fieldDef, createTableSql, fieldInfo.getFieldname()) ? 1 : 0);
                    if (fieldInfo.getFieldname().contains("索引")) {
                        fieldInfo.setIndexmark(1);
                    }
                    fieldInfos.add(fieldInfo);
                }
            }
        }

        return fieldInfos;
    }



    private static List<String> splitFieldDefinitions(String fieldsDefinition) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;
        int parenthesesCount = 0;

        for (int i = 0; i < fieldsDefinition.length(); i++) {
            char c = fieldsDefinition.charAt(i);

            if (c == '\'') {
                inQuotes = !inQuotes;
            } else if (!inQuotes) {
                if (c == '(') {
                    parenthesesCount++;
                } else if (c == ')') {
                    parenthesesCount--;
                }
            }

            if (c == ',' && !inQuotes && parenthesesCount == 0) {
                fields.add(currentField.toString().trim());
                currentField = new StringBuilder();
            } else {
                currentField.append(c);
            }
        }

        if (currentField.length() > 0) {
            fields.add(currentField.toString().trim());
        }

        return fields;
    }


    private static SaDbdesignitemPojo parseFieldDefinition(String fieldDef) {
        // First check if this is an index definition
        if (fieldDef.trim().toUpperCase().startsWith("KEY")) {
            return parseIndexDefinition(fieldDef);
        }

        // Existing field parsing logic
        Pattern pattern = Pattern.compile(
                "^\\s*`?([^`\\s]+)`?\\s+" +                  // 字段名（可选的反引号）
                        "([a-zA-Z]+(?:\\([^\\)]+\\))?)" +        // 字段类型（包括精度，如 decimal(18, 4)）
                        "(?:\\s+CHARACTER\\s+SET\\s+([^\\s]+))?" +   // 字符集
                        "(?:\\s+COLLATE\\s+([^\\s]+))?" +           // 排序规则
                        "(?:\\s+((?:NOT\\s+)?NULL))?" +             // NULL约束
                        "(?:\\s+DEFAULT\\s+([^\\s']+|'[^']*'))?" +  // 默认值
                        "(?:\\s+COMMENT\\s+'([^']+)')?" +           // 注释
                        "(?:\\s+PRIMARY\\s+KEY)?",                  // 可选的主键声明
                Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(fieldDef);
        if (matcher.find()) {
            SaDbdesignitemPojo fieldInfo = new SaDbdesignitemPojo();

            // 设置字段名
            fieldInfo.setFieldname(matcher.group(1));

            // 设置字段类型（完整保留，包括精度）
            String fieldType = matcher.group(2);
            fieldInfo.setFieldtype(fieldType);

            // 设置字符集
            String charSet = matcher.group(3);
            fieldInfo.setCharacterset(charSet != null ? charSet : "");

            // 设置排序规则
            String collation = matcher.group(4);
            fieldInfo.setCollation(collation != null ? collation : "");

            // 设置非空标记
            String nullability = matcher.group(5);
            fieldInfo.setNotnullmark(nullability != null && nullability.toUpperCase().contains("NOT NULL") ? 1 : 0);

            // 设置默认值
            String defaultValue = matcher.group(6);
            if (defaultValue != null) {
                defaultValue = defaultValue.replaceAll("^'|'$", ""); // 去掉包裹的引号
                fieldInfo.setDefvalue(defaultValue);
            } else {
                fieldInfo.setDefvalue("");
            }

            // 设置注释
            String comment = matcher.group(7);
            fieldInfo.setComment(comment != null ? comment : "");

            return fieldInfo;
        }
        return null;
    }

    private static boolean isPrimaryKey(String fieldDef, String createTableSql, String fieldName) {
        // 检查行内主键声明
        if (fieldDef.toLowerCase().contains("primary key")) {
            return true;
        }

        // 检查独立的主键声明（包括换行的情况）
        Pattern primaryKeyPattern = Pattern.compile(
                "primary\\s+key\\s*\\(\\s*`?" + Pattern.quote(fieldName) + "`?\\s*\\)",
                Pattern.CASE_INSENSITIVE
        );
        return primaryKeyPattern.matcher(createTableSql).find();
    }


    /**
     * 此方法移除字符串中最外层括号内的内容，并返回处理后的字符串。
     *
     * @param input 输入的字符串，可能包含括号。
     * @return 移除最外层括号及其内容后的字符串。
     */
    public static String removeOuterParenthesesContent(String input) {
        StringBuilder result = new StringBuilder();
        int level = 0;  // 用于跟踪括号的嵌套层级

        // 遍历字符串中的每个字符
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);

            // 如果是左括号，增加嵌套层级
            if (c == '(') {
                level++;
            }
            // 如果是右括号，减少嵌套层级
            else if (c == ')') {
                level--;
            }
            // 如果不是括号，且不在括号内，添加到结果中
            else {
                if (level == 0) {
                    result.append(c);
                }
            }
        }

        // 返回移除最外层括号内容后的字符串，并去掉前后空格
        return result.toString().trim();
    }


    /**
     * 提取最外层括号内的内容。
     *
     * @param input 输入的字符串，可能包含括号。
     * @return 最外层括号内的内容。
     */
    public static String extractOuterParenthesesContent(String input) {
        StringBuilder result = new StringBuilder();
        int level = 0;  // 用于跟踪括号的嵌套层级
        boolean inOuterParentheses = false;  // 是否在最外层括号内

        // 遍历字符串中的每个字符
        for (int i = 0; i < input.length(); i++) {
            char c = input.charAt(i);

            // 如果是左括号，增加嵌套层级
            if (c == '(') {
                if (level == 0) {
                    inOuterParentheses = true;  // 进入最外层括号
                }
                level++;
                if (level > 1) {
                    result.append(c);  // 如果是嵌套括号，加入结果
                }
            }
            // 如果是右括号，减少嵌套层级
            else if (c == ')') {
                level--;
                if (level > 0) {
                    result.append(c);  // 如果是嵌套括号，加入结果
                }
                if (level == 0) {
                    inOuterParentheses = false;  // 离开最外层括号
                }
            }
            // 如果当前在最外层括号内，收集字符
            else if (inOuterParentheses) {
                result.append(c);
            }
        }

        // 返回最外层括号内的内容
        return result.toString().trim();
    }


    /**
     * 解析索引定义
     */
    private static SaDbdesignitemPojo parseIndexDefinition(String indexDef) {
        // 匹配索引名称和包含的字段，支持可选的反引号
        Pattern pattern = Pattern.compile(
                "KEY\\s+(?:`)?([^`\\s]+)(?:`)?\\s*\\(([^)]+)\\)",
                Pattern.CASE_INSENSITIVE
        );

        Matcher matcher = pattern.matcher(indexDef);
        if (matcher.find()) {
            SaDbdesignitemPojo indexInfo = new SaDbdesignitemPojo();

            // 获取索引名称和字段列表
            String indexName = matcher.group(1);
            String indexColumns = matcher.group(2);

            // 设置字段名（添加"索引"前缀）
            indexInfo.setFieldname("索引" + indexName);

            // 设置字段类型（保持原始的字段列表格式）
            indexInfo.setFieldtype(indexColumns);

            // 其他字段设置为默认值
            indexInfo.setCharacterset("");
            indexInfo.setCollation("");
            indexInfo.setNotnullmark(0);
            indexInfo.setDefvalue("");
            indexInfo.setComment("");
            indexInfo.setPrimarymark(0);

            return indexInfo;
        }

        // 添加调试信息
        System.out.println("无法匹配的索引定义: " + indexDef);
        return null;
    }
}