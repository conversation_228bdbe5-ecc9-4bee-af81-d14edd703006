package inks.service.sa.pms.utils;

import inks.common.core.domain.LoginUser;
import inks.common.core.text.inksSnowflake;
import inks.service.sa.pms.domain.pojo.SaApiinfoPojo;
import io.swagger.models.*;
import io.swagger.models.parameters.AbstractSerializableParameter;
import io.swagger.models.parameters.Parameter;
import io.swagger.parser.SwaggerParser;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

public class SwaggerImportUtils {
    public static void main(String[] args) {
        // 实例化 SwaggerImportService
        SwaggerImportUtils swaggerImportUtils = new SwaggerImportUtils();

        // 调用 importSwagger 方法并传入 Swagger 文档的 URL
        List<SaApiinfoPojo> saApiinfoPojos = swaggerImportUtils.importSwagger("http://dev.inksyun.com:31080/auth/v2/api-docs", null, null, null);

        // 打印数量
        System.out.println("导入的接口数量: " + saApiinfoPojos.size());


    }

    public static List<SaApiinfoPojo> importSwagger(String swaggerUrl, String fnid, String fncode, LoginUser loginUser) {
        // 最终返回的结果
        List<SaApiinfoPojo> resultApiinfoList = new ArrayList<>();

        String realname = "";
        String userid = "";
        String tid = "";
        Date date = new Date();
        if (loginUser != null) {
            realname = loginUser.getRealname();
            userid = loginUser.getUserid();
            tid = loginUser.getTenantid();
        }
        // 初始化计数器
        int importedCount = 0;

        // 解析Swagger文档
        Swagger swagger = new SwaggerParser().read(swaggerUrl);

        // 获取所有路径
        Map<String, Path> paths = swagger.getPaths();

        // 遍历路径
        for (Map.Entry<String, Path> pathEntry : paths.entrySet()) {
            String apiUrl = pathEntry.getKey();
            Path path = pathEntry.getValue();

            // 遍历HTTP方法
            Map<HttpMethod, Operation> operations = path.getOperationMap();
            if (operations != null) {
                for (Map.Entry<HttpMethod, Operation> operationEntry : operations.entrySet()) {
                    String httpMethod = String.valueOf(operationEntry.getKey());
                    Operation operation = operationEntry.getValue();

                    // 打印当前是第几个接口
                    System.out.println("这是第 " + (importedCount + 1) + " 个接口");

                    // 打印接口详细信息
                    System.out.println("接口路径: " + apiUrl);
                    System.out.println("请求方式: " + httpMethod);
                    System.out.println("操作ID: " + operation.getOperationId());
                    System.out.println("摘要: " + operation.getSummary());
                    System.out.println("描述: " + operation.getDescription());
                    System.out.println("请求参数: " + getRequestParams(operation));
                    System.out.println("响应参数: " + getResponseParams(operation));
                    System.out.println("响应示例: " + getResponseExample(operation));
                    System.out.println("返回格式: " + operation.getProduces());
                    System.out.println("请求格式: " + operation.getConsumes());
                    System.out.println("标签: " + operation.getTags());
                    System.out.println("是否已废弃: " + operation.isDeprecated());
                    System.out.println("----------------------------------------");

                    // 创建SaApiinfoPojo对象
                    SaApiinfoPojo saApiinfoPojo = new SaApiinfoPojo();
                    saApiinfoPojo.setId(inksSnowflake.getSnowflake().nextIdStr());
                    saApiinfoPojo.setFnid(fnid);
                    saApiinfoPojo.setFncode(fncode);
                    saApiinfoPojo.setApiname(operation.getSummary());
                    saApiinfoPojo.setApidescription(operation.getDescription());
                    saApiinfoPojo.setApiurl(apiUrl);
                    saApiinfoPojo.setHttpmethod(httpMethod.toUpperCase());
                    saApiinfoPojo.setRequestparams(getRequestParams(operation));
                    saApiinfoPojo.setResponseparams(getResponseParams(operation));
                    saApiinfoPojo.setResponseexample(getResponseExample(operation));
                    saApiinfoPojo.setStatuscode("");
                    saApiinfoPojo.setOperationid(operation.getOperationId());
                    saApiinfoPojo.setProduces(String.join(",", operation.getProduces() == null ? "" : operation.getProduces().toString()));
                    saApiinfoPojo.setConsumes(String.join(",", operation.getConsumes() == null ? "" : operation.getConsumes().toString()));
                    saApiinfoPojo.setTags(String.join(",", operation.getTags()));
                    saApiinfoPojo.setIsdeprecated(operation.isDeprecated() ? 1 : 0);
                    saApiinfoPojo.setCurl(generateCurl(apiUrl, httpMethod));
                    saApiinfoPojo.setRownum(importedCount + 1);
                    saApiinfoPojo.setRemark("Imported from Swagger");
                    saApiinfoPojo.setCreateby(realname);
                    saApiinfoPojo.setCreatebyid(userid);
                    saApiinfoPojo.setCreatedate(date);
                    saApiinfoPojo.setLister(realname);
                    saApiinfoPojo.setListerid(userid);
                    saApiinfoPojo.setModifydate(date);
                    saApiinfoPojo.setTenantid(tid);
                    saApiinfoPojo.setTenantname("");
                    saApiinfoPojo.setRevision(0);

                    resultApiinfoList.add(saApiinfoPojo);

                    // 增加计数器
                    importedCount++;
                }
            }
        }

        // 打印导入的接口数量
        System.out.println("成功导入 " + importedCount + " 个接口");
        return resultApiinfoList;
    }

    private static String getRequestParams(Operation operation) {
        // 解析请求参数
        StringBuilder requestParams = new StringBuilder();
        List<Parameter> parameters = operation.getParameters();
        if (parameters != null) {
            for (Parameter parameter : parameters) {
                requestParams.append(parameter.getName())
                        .append(": ").append(parameter.getIn())  // 使用 getIn() 获取参数位置
                        .append(", required: ").append(parameter.getRequired())
                        .append(", type: ").append(getParameterType(parameter))  // 使用 getParameterType 方法获取参数类型
                        .append("\n");
            }
        }
        return requestParams.toString();
    }

    private static String getParameterType(Parameter parameter) {
        // 获取参数类型
        if (parameter instanceof AbstractSerializableParameter) {
            return ((AbstractSerializableParameter<?>) parameter).getType();
        }
        return "unknown";
    }

    private static String getResponseParams(Operation operation) {
        // 解析响应参数
        StringBuilder responseParams = new StringBuilder();
        if (operation.getResponses() != null) {
            for (Map.Entry<String, Response> responseEntry : operation.getResponses().entrySet()) {
                responseParams.append(responseEntry.getKey()).append(": ").append(responseEntry.getValue().getDescription())
                        .append(", schema: ").append(responseEntry.getValue().getSchema())
                        .append("\n");
            }
        }
        return responseParams.toString();
    }

    private static String getResponseExample(Operation operation) {
        // 解析响应示例
        return null; // 根据实际情况解析响应示例
    }

    private static String generateCurl(String apiUrl, String httpMethod) {
        // 生成Curl命令
        return String.format("curl -X %s \"%s\"", httpMethod.toUpperCase(), apiUrl);
    }

    private void insert(SaApiinfoPojo saApiinfoPojo) {
        // 插入到数据库的逻辑
    }
}
