package inks.service.sa.pms.utils.email;

import inks.service.sa.pms.domain.vo.EmailReceiverVO;
import inks.service.sa.pms.utils.PrintColor;
import org.jsoup.Jsoup;
import org.springframework.mail.javamail.JavaMailSenderImpl;

import javax.mail.*;
import javax.mail.internet.InternetAddress;
import javax.mail.internet.MimeMultipart;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class EmailReceiverUtils {

    private static final String USERNAME = "<EMAIL>";
    private static final String PASSWORD = "ASDqwe@123";

    public static void main(String[] args) {
        // 配置IMAP和POP3服务器信息
        String[][] protocols = {
                {"imap", "imap.qiye.aliyun.com", "993"},
                {"pop3", "pop.qiye.aliyun.com", "995"}
        };

        // 获取当前日期
        LocalDate today = LocalDate.now();
        // 获取昨天的日期
        LocalDate yesterday = today.minusDays(100);
        // 将 LocalDate 转换为 Date
        Date startDate = Date.from(yesterday.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date endDate = Date.from(today.atStartOfDay(ZoneId.systemDefault()).toInstant());

        for (int i = 0; i < protocols.length; i++) {
            String protocol = protocols[i][0];
            String host = protocols[i][1];
            int port = Integer.parseInt(protocols[i][2]);
            PrintColor.red("第" + (i + 1) + "次 测试" + protocol.toUpperCase() + "邮件接收：");
            receiveEmails(protocol, host, port, USERNAME, PASSWORD, 3, startDate, endDate);
        }
    }

    public static List<EmailReceiverVO> receiveEmails(String protocol, String host, int port, String username, String password,
                                                      Integer iwant, Date startDate, Date endDate) {
        Store store = null;
        Folder emailFolder = null;
        List<EmailReceiverVO> emailReceivers = new ArrayList<>();

        try {
            Properties properties = new Properties();
            properties.put("mail.store.protocol", protocol);
            properties.put("mail." + protocol + ".host", host);
            properties.put("mail." + protocol + ".port", port);
            properties.put("mail." + protocol + ".auth", "true");
            properties.put("mail." + protocol + ".ssl.enable", "true");

            Session emailSession = Session.getDefaultInstance(properties);
            store = emailSession.getStore(protocol);
            store.connect(host, username, password);

            emailFolder = store.getFolder("INBOX");
            emailFolder.open(Folder.READ_ONLY);

            Message[] messages;

            if (iwant == null) {
                messages = emailFolder.getMessages();
            } else {
                int messageCount = emailFolder.getMessageCount();
                int start = Math.max(1, messageCount - (iwant - 1));
                messages = emailFolder.getMessages(start, messageCount);
            }

            if (startDate != null || endDate == null) {
                startDate = startDate == null ? new Date(0) : startDate;
                endDate = endDate == null ? new Date() : endDate;
                messages = filterEmailsBetweenDate(messages, startDate, endDate);
            }

            for (Message message : messages) {
                EmailReceiverVO emailReceiverVO = EmailReceiverVO.builder()
                        .subject(message.getSubject())
                        .sender(decodeTextArray(message.getFrom()))
                        .recipients(decodeTextArray(message.getAllRecipients()))
                        .sentDate(formatDate(message.getSentDate()))
                        .receivedDate(formatDate(message.getReceivedDate()))
                        .ccRecipients(decodeTextArray(message.getRecipients(Message.RecipientType.CC)))
                        .bccRecipients(decodeTextArray(message.getRecipients(Message.RecipientType.BCC)))
                        .replyTo(decodeTextArray(message.getReplyTo()))
                        .content(getTextFromMessage(message))
                        .size(message.getSize())
                        .flags(message.getFlags().toString())
                        .build();

                emailReceivers.add(emailReceiverVO);
                soutMessage(protocol, message);
            }

            return emailReceivers;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            closeResources(emailFolder, store);
        }

        return null;
    }

    public static Message[] filterEmailsBetweenDate(Message[] messages, Date startDate, Date endDate) {
        startDate = (startDate == null) ? new Date(0) : startDate;
        endDate = (endDate == null) ? new Date() : endDate;

        List<Message> filteredList = new ArrayList<>();
        for (Message message : messages) {
            try {
                Date sentDate = message.getSentDate();
                if (sentDate != null && !sentDate.before(startDate) && !sentDate.after(endDate)) {
                    filteredList.add(message);
                }
            } catch (MessagingException e) {
                e.printStackTrace();
            }
        }

        return filteredList.toArray(new Message[0]);
    }

    public static String decodeTextArray(Address[] addresses) {
        if (addresses == null || addresses.length == 0) {
            return "";
        }
        return Stream.of(addresses)
                .map(address -> {
                    try {
                        return ((InternetAddress)address).toUnicodeString();
                    } catch (Exception e) {
                        return address.toString();
                    }
                })
                .collect(Collectors.joining(", "));
    }

    public static String formatDate(Date date) {
        if (date == null) {
            return "";
        }
        SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        dateFormat.setTimeZone(TimeZone.getTimeZone("GMT+8"));
        return dateFormat.format(date);
    }

    private static void soutMessage(String protocol, Message message) throws MessagingException, IOException {
        System.out.println("==========================================");
        PrintColor.red(protocol + " 邮件主题: " + message.getSubject());
        System.out.println(protocol + " 发件人: " + decodeTextArray(message.getFrom()));
        System.out.println(protocol + " 接收人: " + decodeTextArray(message.getAllRecipients()));
        System.out.println(protocol + " 发送日期: " + formatDate(message.getSentDate()));
        System.out.println(protocol + " 接收日期: " + formatDate(message.getReceivedDate()));
        System.out.println(protocol + " 抄送人: " + decodeTextArray(message.getRecipients(Message.RecipientType.CC)));
        System.out.println(protocol + " 密送人: " + decodeTextArray(message.getRecipients(Message.RecipientType.BCC)));
        System.out.println(protocol + " 回复给: " + decodeTextArray(message.getReplyTo()));
        System.out.println(protocol + " 邮件内容: " + getTextFromMessage(message));
        System.out.println(protocol + " 邮件大小: " + message.getSize() + " bytes");
        System.out.println(protocol + " 邮件标记: " + message.getFlags().toString());
        System.out.println("==========================================");
    }

    private static void closeResources(Folder emailFolder, Store store) {
        try {
            if (emailFolder != null && emailFolder.isOpen()) {
                emailFolder.close(false);
            }
            if (store != null) {
                store.close();
            }
        } catch (MessagingException e) {
            e.printStackTrace();
        }
    }

    public static String getTextFromMessage(Message message) throws MessagingException, IOException {
        if (message.isMimeType("text/plain")) {
            return message.getContent().toString();
        } else if (message.isMimeType("text/html")) {
            return Jsoup.parse(message.getContent().toString()).text();
        } else if (message.isMimeType("multipart/*")) {
            return getTextFromMimeMultipart((MimeMultipart) message.getContent());
        }
        return "";
    }

    public static String getTextFromMimeMultipart(MimeMultipart mimeMultipart) throws MessagingException, IOException {
        StringBuilder result = new StringBuilder();
        int count = mimeMultipart.getCount();
        for (int i = 0; i < count; i++) {
            BodyPart bodyPart = mimeMultipart.getBodyPart(i);
            result.append(getTextFromBodyPart(bodyPart));
        }
        return result.toString();
    }

    public static String getTextFromBodyPart(BodyPart bodyPart) throws MessagingException, IOException {
        if (bodyPart.isMimeType("text/plain")) {
            return bodyPart.getContent().toString();
        } else if (bodyPart.isMimeType("text/html")) {
            return Jsoup.parse(bodyPart.getContent().toString()).text();
        } else if (bodyPart.getContent() instanceof MimeMultipart) {
            return getTextFromMimeMultipart((MimeMultipart) bodyPart.getContent());
        }
        return "";
    }
}