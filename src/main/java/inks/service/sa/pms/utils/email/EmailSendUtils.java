package inks.service.sa.pms.utils.email;

import inks.sa.common.core.utils.PrintColor;
import org.springframework.mail.javamail.JavaMailSender;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Component;

import javax.mail.MessagingException;
import javax.mail.internet.MimeMessage;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;


/**
 * <AUTHOR>
 * jakarta.mail依赖版本 一直报错：Provider com.sun.mail.imap.IMAPProvider not a subtype 和javax.mail冲突
 * 改为使用依赖：spring-boot-starter-mail:jar:2.5.3:compile
 * @date 2023年01月02日 16:48
 */
@Component
public class EmailSendUtils {

    public static void main(String[] args) {
        // 配置邮件发送相关参数
        String emailHost = "smtp.qiye.aliyun.com";
        String fromEmail = "<EMAIL>";
        String authCode = "ecjMGZCpFEveWxho";  // 发件人邮箱的授权码
        String fromUserName = "nanno.jiang";
        String toEmail = "<EMAIL>";
        String subject = "工作日志-nanno-20230306";
        String content = "邮件内容...";
        String otherEmails = "<EMAIL>;<EMAIL>";

        try {
            // 创建并初始化 EmailSendUtils 对象
            EmailSendUtils emailSendUtils = new EmailSendUtils();
            // 调用 sendEmail 方法发送邮件
            emailSendUtils.sendEmail(fromEmail, authCode, fromUserName, toEmail, otherEmails, subject, content);
        } catch (MessagingException e) {
            // 如果发生异常，打印异常堆栈信息
            e.printStackTrace();
        }
    }

    /**
     * 发送邮件的方法
     *
     * @param fromEmail    发件人邮箱
     * @param authCode     发件人授权码
     * @param fromUserName 发件人用户名（昵称）
     * @param toEmail      收件人邮箱
     * @param otherEmails  抄送人邮箱（用分号分隔）
     * @param subject      邮件主题
     * @param content      邮件内容（支持HTML格式）
     * @throws MessagingException 如果邮件发送失败
     */
    public void sendEmail(String fromEmail, String authCode, String fromUserName, String toEmail, String otherEmails, String subject, String content) throws MessagingException {
        // 初始化邮件发送的 JavaMailSender
        JavaMailSenderImpl mailSenderImpl = new JavaMailSenderImpl();
        mailSenderImpl.setHost("smtp.qiye.aliyun.com"); // 使用企业邮箱服务器
        mailSenderImpl.setUsername(fromEmail); // 设置发件人邮箱
        mailSenderImpl.setPassword(authCode);  // 设置授权码
        mailSenderImpl.setDefaultEncoding(StandardCharsets.UTF_8.name()); // 设置编码格式为UTF-8

        // 配置邮件服务器的相关属性
        Properties properties = new Properties();
        properties.setProperty("mail.smtp.auth", "true"); // 开启SMTP认证
        properties.setProperty("mail.smtp.starttls.enable", "true"); // 启用TLS安全连接
        mailSenderImpl.setJavaMailProperties(properties);

        // 创建JavaMailSender对象
        JavaMailSender mailSender = mailSenderImpl;

        // 创建MimeMessage对象来表示邮件内容
        MimeMessage message = mailSender.createMimeMessage();
        MimeMessageHelper helper = new MimeMessageHelper(message, true); // true表示支持多部分内容（如HTML）

        // 设置发件人
        helper.setFrom(fromUserName + " <" + fromEmail + ">");

        // 设置收件人
        helper.setTo(toEmail);

        // 设置抄送人（如果有）
        List<String> validEmails = new ArrayList<>();
        if (otherEmails != null && !otherEmails.trim().isEmpty()) {
            String[] recipientsArr = otherEmails.split(";");
            for (String recipient : recipientsArr) {
                if (!recipient.trim().isEmpty()) {
                    validEmails.add(recipient.trim());
                }
            }
        }
        // 如果有有效的抄送人，设置抄送
        if (!validEmails.isEmpty()) {
            helper.setCc(validEmails.toArray(new String[0]));
        }

        // 设置邮件主题
        helper.setSubject(subject);

        // 设置邮件内容，第二个参数为true表示邮件内容是HTML格式
        helper.setText(content, true);

        // 发送邮件
        mailSender.send(message);

        // 打印发送成功信息，包括收件人、抄送人、主题和内容
        PrintColor.lv("邮件已成功发送!");
        PrintColor.lv("发件人: " + fromUserName + " <" + fromEmail + ">");
        PrintColor.lv("收件人: " + toEmail);

        if (!validEmails.isEmpty()) {
            PrintColor.lv("抄送人: " + String.join(", ", validEmails));
        } else {
            PrintColor.lv("无抄送人");
        }

        PrintColor.lv("邮件主题: " + subject);
        PrintColor.lv("邮件内容: " + content);
    }
}
