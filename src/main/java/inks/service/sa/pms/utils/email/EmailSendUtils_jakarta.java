//package inks.service.sa.pms.utils.email;
//
//import inks.sa.common.core.domain.pojo.SaConfigPojo;
//import inks.sa.common.core.service.SaConfigService;
//import jakarta.mail.Message;
//import jakarta.mail.MessagingException;
//import jakarta.mail.Session;
//import jakarta.mail.Transport;
//import jakarta.mail.internet.AddressException;
//import jakarta.mail.internet.InternetAddress;
//import jakarta.mail.internet.MimeMessage;
//import jakarta.mail.internet.MimeUtility;
//import org.springframework.stereotype.Component;
//
//import javax.annotation.Resource;
//import java.io.UnsupportedEncodingException;
//import java.util.ArrayList;
//import java.util.Arrays;
//import java.util.List;
//import java.util.Properties;
//
///**
// * <AUTHOR>
// * jakarta.mail依赖版本 一直报错：Provider com.sun.mail.imap.IMAPProvider not a subtype 和javax.mail冲突
// * 改为使用依赖：spring-boot-starter-mail:jar:2.5.3:compile
// * @date 2023年01月02日 16:48
// */
//@Component
//public class EmailSendUtils_jakarta {
//    public String emailHost;               //发送邮件的主机
//    public String fromUser;                //发件人名称
//    public String fromEmail;               //发件人邮箱
//    public String authCode;                //发件人邮箱授权码
//    public String toEmail;                 //收件人邮箱
//    public String subject;                 //主题信息
//    public String transportType = "smtp";  //邮件发送的协议 smtp/imap
//    @Resource
//    private SaConfigService saConfigService;
//
//    //  mail:
////    username: <EMAIL>
////    password: ASDqwe@123
////    host: smtp.qiye.aliyun.com
////    default-encoding: UTF-8
////    port: 993
////    protocol: imap
////    properties:
////      mail.smtp.socketFactory.fallback: true
////      mail.smtp.starttls.enable: false
////    # 查询收件箱的配置 方式一: imap 993 imap.qiye.aliyun.com 方式二: pop3 995 pop.qiye.aliyun.com
////    receiver:
////      protocol: imap
////      port: 993
////      host: imap.qiye.aliyun.com
////      scheduled: 0 0/30 * * * ?  # 定时读取收件箱****************转为需求单
//    public static void main(String[] args) {
//        EmailSendUtils_jakarta emailSendUtilsJakarta = new EmailSendUtils_jakarta();
//        emailSendUtilsJakarta.emailHost = "smtp.qiye.aliyun.com";
//        emailSendUtilsJakarta.fromUser = "<EMAIL>";
//        emailSendUtilsJakarta.fromEmail = "<EMAIL>";
//        emailSendUtilsJakarta.authCode = "ecjMGZCpFEveWxho";
//        emailSendUtilsJakarta.toEmail = "<EMAIL>";
//        emailSendUtilsJakarta.subject = "工作日志-nanno-20230306";
//        //emailSendUtils.content = "邮件内容...";
//        try {
//            emailSendUtilsJakarta.sendEmail(emailSendUtilsJakarta.fromEmail, emailSendUtilsJakarta.authCode, emailSendUtilsJakarta.fromUser, emailSendUtilsJakarta.toEmail, "", emailSendUtilsJakarta.subject, "emailSendUtils.content");
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        } catch (MessagingException e) {
//            e.printStackTrace();
//        }
//    }
//
//    /**
//     * fromEmail:    发件人邮箱   "<EMAIL>"
//     * authCode:     发件人邮箱授权码 "pcwwnfnesudmbbfd"
//     * formUserName: 发件人姓名   "nanno"
//     * toEmail:      收件人邮箱   "<EMAIL>"
//     * otherEmails : 抄送人邮箱List   以分号分隔
//     * subject:      邮件标题     "工作日志-nanno-20230306"
//     * content:      邮件内容     "邮件内容..."
//     */
//    public void sendEmail(String fromEmail, String authCode, String fromUserName, String toEmail, String otherEmails, String subject, String content) throws UnsupportedEncodingException, MessagingException {
//        //QQ邮箱
//        //   String emailHost = "smtp.qq.com";
//        //企业微信邮箱（阿里邮箱）
//        String emailHost = "smtp.qiye.aliyun.com";
//        //String authCode = "pcwwnfnesudmbbfd";
//        //String fromEmail = "<EMAIL>";
//        //初始化默认参数
//        Properties props = new Properties();
//        props.setProperty("mail.transport.protocol", transportType);
//        props.setProperty("mail.host", emailHost);
//        //发件人名字
//        props.setProperty("mail.user", fromUserName);
//        //发件人邮箱
//        props.setProperty("mail.from", fromEmail);
//        //获取Session对象
//        Session session = Session.getInstance(props, null);
//        //开启后有调试信息
//        session.setDebug(true);
//        //通过MimeMessage来创建Message接口的子类
//        MimeMessage message = new MimeMessage(session);
//        //设置发件人姓名:
//        String formName = MimeUtility.encodeWord(fromUserName) + " <" + fromEmail + ">";
//        InternetAddress from = new InternetAddress(formName);
//        message.setFrom(from);
//        //设置收件人：
//        InternetAddress to = new InternetAddress(toEmail);
//        message.setRecipient(Message.RecipientType.TO, to);
//        //设置抄送人,可有可无抄送人：
//        //String otherEmails = "<EMAIL>;<EMAIL>";
//        List<InternetAddress> addresses = new ArrayList<>();
//        String[] recipientsArr = otherEmails.split(";");
//        for (String recipient : recipientsArr) {
//            try {
//                InternetAddress address = new InternetAddress(recipient.trim());
//                addresses.add(address);
//            } catch (AddressException e) {
//                // 如果邮箱地址不合法，可以在这里处理异常
//                e.printStackTrace();
//            }
//        }
//        InternetAddress[] addressesArr = addresses.toArray(new InternetAddress[addresses.size()]);
//        message.setRecipients(Message.RecipientType.CC, addressesArr);
//        //设置邮件主题
//        message.setSubject(subject);
//        //设置邮件内容,这里我使用html格式，其实也可以使用纯文本；纯文本"text/plain"
//        message.setContent(content, "text/html;charset=UTF-8");
//        //保存上面设置的邮件内容
//        message.saveChanges();
//        //获取Transport对象
//        Transport transport = session.getTransport();
//        //smtp验证，就是你用来发邮件的邮箱用户名密码（若在之前的properties中指定默认值，这里可以不用再次设置）
//        transport.connect(emailHost, fromEmail, authCode);
//        //发送邮件
//        transport.sendMessage(message, message.getAllRecipients());
//    }
//
//
//    public void sendEmail(String content) throws UnsupportedEncodingException, MessagingException {
//
//        //初始化默认参数
//        Properties props = new Properties();
//        props.setProperty("mail.transport.protocol", transportType);
//        props.setProperty("mail.host", emailHost);
//        props.setProperty("mail.user", fromUser);
//        props.setProperty("mail.from", fromEmail);
//        //获取Session对象
//        Session session = Session.getInstance(props, null);
//        //开启后有调试信息
//        session.setDebug(true);
//
//        //通过MimeMessage来创建Message接口的子类
//        MimeMessage message = new MimeMessage(session);
//        //下面是对邮件的基本设置
//        //设置发件人：
//        //设置发件人第一种方式：直接显示：antladdie <<EMAIL>>
//        //InternetAddress from = new InternetAddress(sender_username);
//        //设置发件人第二种方式：发件人信息拼接显示：应凯平台 <<EMAIL>>
//        String formName = MimeUtility.encodeWord("应凯平台") + " <" + fromEmail + ">";
//        InternetAddress from = new InternetAddress(formName);
//        message.setFrom(from);
//
//        //设置收件人：
//        SaConfigPojo saConfigPojo = saConfigService.getEntityByCfgKey("pms.email");
//        String email = saConfigPojo.getCfgvalue();
//        InternetAddress to = new InternetAddress(email);
//        message.setRecipient(Message.RecipientType.TO, to);
//
//        //设置抄送人(两个)可有可无抄送人：
//        List<InternetAddress> addresses = Arrays.asList(new InternetAddress("<EMAIL>"), new InternetAddress("<EMAIL>"));
//        InternetAddress[] addressesArr = (InternetAddress[]) addresses.toArray();
//        message.setRecipients(Message.RecipientType.CC, addressesArr);
//
//        //设置密送人 可有可无密送人：
//        //InternetAddress toBCC = new InternetAddress(toEmail);
//        //message.setRecipient(Message.RecipientType.BCC, toBCC);
//
//        //设置邮件主题
//        message.setSubject(subject);
//
//        //设置邮件内容,这里我使用html格式，其实也可以使用纯文本；纯文本"text/plain"
//        message.setContent("<h3>" + content + "</h3>", "text/html;charset=UTF-8");
//
//        //保存上面设置的邮件内容
//        message.saveChanges();
//
//        //获取Transport对象
//        Transport transport = session.getTransport();
//        //smtp验证，就是你用来发邮件的邮箱用户名密码（若在之前的properties中指定默认值，这里可以不用再次设置）
//        transport.connect(emailHost, fromEmail, authCode);
//        //发送邮件
//        transport.sendMessage(message, message.getAllRecipients()); // 发送
//    }
//
//
//    //email:发送给哪个邮箱
//    public void sendEmail(String content, String email) throws UnsupportedEncodingException, MessagingException {
//
//        //初始化默认参数
//        Properties props = new Properties();
//        props.setProperty("mail.transport.protocol", transportType);
//        props.setProperty("mail.host", emailHost);
//        props.setProperty("mail.user", fromUser);
//        props.setProperty("mail.from", fromEmail);
//        //获取Session对象
//        Session session = Session.getInstance(props, null);
//        //开启后有调试信息
//        session.setDebug(true);
//
//        //通过MimeMessage来创建Message接口的子类
//        MimeMessage message = new MimeMessage(session);
//
//        String formName = MimeUtility.encodeWord("应凯平台") + " <" + fromEmail + ">";
//        InternetAddress from = new InternetAddress(formName);
//        message.setFrom(from);
//        //设置收件人：
//        InternetAddress to = new InternetAddress(email);
//        message.setRecipient(Message.RecipientType.TO, to);
//        //设置邮件主题
//        message.setSubject("您已被指派");
//        //设置邮件内容,这里我使用html格式，其实也可以使用纯文本；纯文本"text/plain"
//        message.setContent("<h3>" + content + "</h3>", "text/html;charset=UTF-8");
//        //保存上面设置的邮件内容
//        message.saveChanges();
//        //获取Transport对象
//        Transport transport = session.getTransport();
//        //smtp验证，就是你用来发邮件的邮箱用户名密码（若在之前的properties中指定默认值，这里可以不用再次设置）
//        transport.connect(emailHost, fromEmail, authCode);
//        //发送邮件
//        transport.sendMessage(message, message.getAllRecipients()); // 发送
//    }
//
//
//}
