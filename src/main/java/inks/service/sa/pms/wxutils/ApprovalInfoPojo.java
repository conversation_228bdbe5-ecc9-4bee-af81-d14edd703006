package inks.service.sa.pms.wxutils; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/12
 * @param 审批回调转换对象
 */

import java.util.List;

public class ApprovalInfoPojo {
    private Integer spStatus;
    private Long applyTime;
    private Integer statuChangeEvent;
    private Long spNo;
    private String spName;
    private String templateId;

    public Long getSpNo() {
        return spNo;
    }

    public void setSpNo(Long spNo) {
        this.spNo = spNo;
    }

    public String getSpName() {
        return spName;
    }

    public void setSpName(String spName) {
        this.spName = spName;
    }

    public String getTemplateId() {
        return templateId;
    }

    public void setTemplateId(String templateId) {
        this.templateId = templateId;
    }

    private ResultUserPojo applyer;
    private List<ResultUserPojo> notifyer;
    private List<SpRecordPojo> spRecord;

    public Integer getSpStatus() {
        return spStatus;
    }

    public void setSpStatus(Integer spStatus) {
        this.spStatus = spStatus;
    }

    public Long getApplyTime() {
        return applyTime;
    }

    public void setApplyTime(Long applyTime) {
        this.applyTime = applyTime;
    }

    public Integer getStatuChangeEvent() {
        return statuChangeEvent;
    }

    public void setStatuChangeEvent(Integer statuChangeEvent) {
        this.statuChangeEvent = statuChangeEvent;
    }

    public ResultUserPojo getApplyer() {
        return applyer;
    }

    public void setApplyer(ResultUserPojo applyer) {
        this.applyer = applyer;
    }

    public List<ResultUserPojo> getNotifyer() {
        return notifyer;
    }

    public void setNotifyer(List<ResultUserPojo> notifyer) {
        this.notifyer = notifyer;
    }

    public List<SpRecordPojo> getSpRecord() {
        return spRecord;
    }

    public void setSpRecord(List<SpRecordPojo> spRecord) {
        this.spRecord = spRecord;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"spStatus\":")
                .append(spStatus);
        sb.append(",\"applyTime\":")
                .append(applyTime);
        sb.append(",\"statuChangeEvent\":")
                .append(statuChangeEvent);
        sb.append(",\"spNo\":")
                .append(spNo);
        sb.append(",\"spName\":\"")
                .append(spName).append('\"');
        sb.append(",\"templateId\":\"")
                .append(templateId).append('\"');
        sb.append(",\"applyer\":")
                .append(applyer);
        sb.append(",\"notifyer\":")
                .append(notifyer);
        sb.append(",\"spRecord\":")
                .append(spRecord);
        sb.append('}');
        return sb.toString();
    }
}
