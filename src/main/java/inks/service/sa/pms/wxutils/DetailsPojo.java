package inks.service.sa.pms.wxutils; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/12
 * @param
 */

public class DetailsPojo {
    private Integer spStatus;
    private String speech;
    private ResultUserPojo approver;
    private String spTime;


    public Integer getSpStatus() {
        return spStatus;
    }

    public void setSpStatus(Integer spStatus) {
        this.spStatus = spStatus;
    }

    public String getSpeech() {
        return speech;
    }

    public void setSpeech(String speech) {
        this.speech = speech;
    }

    public ResultUserPojo getApprover() {
        return approver;
    }

    public void setApprover(ResultUserPojo approver) {
        this.approver = approver;
    }

    public String getSpTime() {
        return spTime;
    }

    public void setSpTime(String spTime) {
        this.spTime = spTime;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"spStatus\":")
                .append(spStatus);
        sb.append(",\"speech\":\"")
                .append(speech).append('\"');
        sb.append(",\"approver\":")
                .append(approver);
        sb.append(",\"spTime\":\"")
                .append(spTime).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
