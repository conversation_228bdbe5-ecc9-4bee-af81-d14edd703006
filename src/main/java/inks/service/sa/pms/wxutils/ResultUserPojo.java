package inks.service.sa.pms.wxutils; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/12
 * @param 审批回调转换对象
 */

public class ResultUserPojo {
    private String userId;
    private String party;

    public String getUserId() {
        return userId;
    }

    public void setUserId(String userId) {
        this.userId = userId;
    }

    public String getParty() {
        return party;
    }

    public void setParty(String party) {
        this.party = party;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"userId\":\"")
                .append(userId).append('\"');
        sb.append(",\"party\":\"")
                .append(party).append('\"');
        sb.append('}');
        return sb.toString();
    }
}
