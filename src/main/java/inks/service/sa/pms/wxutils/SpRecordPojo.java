package inks.service.sa.pms.wxutils; /*
 *功能描述
 * <AUTHOR>
 * @date  2022/1/12
 * @param
 */

public class SpRecordPojo {
    private Integer spStatus;
    private Integer approverAttr;
    private DetailsPojo details;

    public Integer getSpStatus() {
        return spStatus;
    }

    public void setSpStatus(Integer spStatus) {
        this.spStatus = spStatus;
    }

    public Integer getApproverAttr() {
        return approverAttr;
    }

    public void setApproverAttr(Integer approverAttr) {
        this.approverAttr = approverAttr;
    }

    public DetailsPojo getDetails() {
        return details;
    }

    public void setDetails(DetailsPojo details) {
        this.details = details;
    }

    @Override
    public String toString() {
        final StringBuilder sb = new StringBuilder("{");
        sb.append("\"spStatus\":")
                .append(spStatus);
        sb.append(",\"approverAttr\":")
                .append(approverAttr);
        sb.append(",\"details\":")
                .append(details);
        sb.append('}');
        return sb.toString();
    }
}
