function exec() {
    // 读取用户输入的内容
    var input = quickerGetVar('input');
    var cmdPrefix = "idea ";

    // 如果输入包含数字 "2"，使用指定版本的 IDEA，并移除所有 "2"
    if (input.indexOf("2") !== -1) {
        cmdPrefix = "\"E:\\jetbrains\\IntelliJ IDEA 243.12818.47\\bin\\idea64.exe\" ";
        input = input.replace(/2/g, "");
    }

    var projectName;
    var path;

    // 根据修改后的输入判断项目路径
    switch (input) {
        case "oms":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-sa-oms-master";
            break;
        case "mes":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-sa-mes-master";
            break;
        case "pms":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-sa-pms";
            break;
        case "sys":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-cloud-master";
            break;
        case "sysh":
        case "syshub":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-cloud-hub";
            break;
        case "common":
        case "com":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-sa-common-master";
            break;
        case "uts":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-sa-uts";
            break;
        case "oembr":
        case "br":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-oem-br";
            break;
        case "oemrs":
        case "rs":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-oem-rs";
            break;
        case "oemgm":
        case "gm":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-oem-gm";
            break;
        case "pcb":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\**********************";
            break;
        case "sd":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\*********************";
            break;
        case "tree":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-patch-tree";
            break;
        case "std":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\**********************";
            break;
        case "sto":
        case "st":
        case "4":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-store-master";
            break;
        case "sale":
        case "1":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-sale-master";
            break;
        case "3":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-buy-master";
            break;
        case "6":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-qms-master";
            break;
        case "7":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-fm-master";
            break;
        case "91":
        case "good":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-goods-master";
            break;
        case "96":
        case "ut":
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-utils-master";
            break;
        //     默认的sa项目和普通oms微服务的拼接方式：
        // 1. 如果输入以 "sa" 开头，则认为是 sa 项目，项目名为 "sa-" 后面的内容
        // 2. 否则，认为是普通微服务，项目名为输入内容加上 "-master" 后缀
        default:
            if (input.startsWith("sa") && input.length > 2) {
                projectName = "sa-" + input.substring(2);
            } else {
                projectName = input + "-master";
            }
            path = "D:\\nanno\\WORK-CODE\\GitLab\\inks-service-" + projectName;
            break;
    }

    // 拼接最终命令，项目路径也用双引号括起来
    var cmd = cmdPrefix + "\"" + path + "\"";
    quickerSetVar('projname', cmd);

    return 0; // 返回0表示成功
}
