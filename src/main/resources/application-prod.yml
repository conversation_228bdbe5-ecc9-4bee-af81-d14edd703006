server:
  tomcat:
    uri-encoding: UTF-8
#spring数据源
spring:
  main:
    allow-bean-definition-overriding: true #允许覆盖bean
  datasource:
    #MYsql连接字符串
    url: jdbc:mysql://${DATABASE_SER:**************:53308/inkspms}?useUnicode=true&characterEncoding=utf-8&allowMutilQueries=true&serverTimezone=Asia/Shanghai&useSSL=false
    username: ${DATABASE_USER:root}
    password: ${DATABASE_PD:asd@123456}
    driver-class-name: com.mysql.cj.jdbc.Driver
    hikari:
      connection-test-query: SELECT 1
      maximum-pool-size: 10
  flyway:
    enabled: false    #是否开启flyway，默认true.

  mail:
    username: <EMAIL>
    password: ASDqwe@123
    host: smtp.qiye.aliyun.com
    default-encoding: UTF-8
    port: 993
    protocol: imap
    properties:
      mail.smtp.socketFactory.fallback: true
      mail.smtp.starttls.enable: false
    # 查询收件箱的配置 方式一: imap 993 imap.qiye.aliyun.com 方式二: pop3 995 pop.qiye.aliyun.com
    receiver:
      protocol: imap
      port: 993
      host: imap.qiye.aliyun.com
      scheduled: 0 0/30 * * * ?  # 定时读取收件箱****************转为需求单

  web: #配置静态资源访问路径
    resources:
      static-locations: classpath:/
  mvc:
    view:
      suffix: .html


mybatis:
  mapper-locations: classpath*:mapper/*.xml
  configuration:
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl
    map-underscore-to-camel-case: true
  type-aliases-package: inks.service.sa.store.**.domain
  #配置打印SQL语句到控制台

wechat:
  miniapp:
    config:
      #微信小程序的appid
      appid: wx3866e1b5f1524d0f
      #微信小程序的Secret
      secret: 27158463ac80520f28ebca7c79a9ae57
      #微信小程序消息服务器配置的token
      token: 123456
      #微信小程序消息服务器配置的EncodingAESKey
      aseKey: 123456
      msgDataFormat: JSON
# 代码生成
gen:
  # 作者
  author: author-inks
  # 默认生成包路径 system 需改成自己的模块名称 如 system monitor tool
  packageName: com.ruoyi.system
  # 自动去除表前缀，默认是false
  autoRemovePre: false
  # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
  tablePrefix: sys_

# MQTT##
mqtt:
  host: tcp://dev.inksyun.com:1883
  username: admin
  password: public
  #  host: tcp://mqtt.inksyun.com:1883
  #  userName: inksinfo
  #  passWord: inksinfo@123
  qos: 2
  clientId: ClientId_local96pms_#{T(java.util.UUID).randomUUID().toString()}
  timeout: 10
  keepalive: 20
  enableTopic:     # 指定开启监听哪几个topic,逗号分隔如: 1,2 注意:项目启动后需要开启监听几个topic在此方法中定义: connectComplete(boolean reconnect, String serverURI)
  topic1: inks/client/#
  topic2: /#
  topic3: inks/#
  topic4: $SYS/brokers/+/clients/#


inks:
  license: ${LICENSE_KEY:}
  redisType: mysql
  user:
    wxscan-create: true    #微信公众号扫码可直接创建admin权限用户，默认false
  feign:
    GrfUrl: ${GRFURL:http://dev.inksyun.com:18801}
    UtsUrl: ${UTSURL:http://*************:10684}
  # 调用oam公众号接口获取openid #内网测试号:http://*************:10677 [wx58c9e35cc9fb9be5] 公网应凯科技:http://oam.inksyun.com [wx7850d75f765d0dce]
  oam:
    api: http://oam.inksyun.com
    appid: wx7850d75f765d0dce
  tid: tid-inks-pms
  justauth:
    api: https://dev.inksyun.com:10656
