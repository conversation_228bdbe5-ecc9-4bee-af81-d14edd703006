server:
  port: 10656 #服务端口

#  ssl:
#    key-store: classpath:ssl/dev/tomcat.jks #证书文件地址
#    key-store-password: 123456        #密钥
#    key-store-type: JKS            #加密算法
#    key-alias: dev.inksyun.com                 #key名
spring:
  application:
    name: sa-pms
  profiles:
    active: dev
  main:
    allow-bean-definition-overriding: true #当遇到同样名字的时候，是否允许覆盖注册
  servlet:
    multipart:
      max-file-size: 20MB
      max-request-size: 20MB


logging:
  level:
    org:
      springframework:
        security: info

pagehelper:
  helper-dialect: mysql
  reasonable: true
  support-methods-arguments: true

oss:
  bucket: inkspms
  minio:
    access-key: minioadmin
    secret-key: minioadmin
    endpoint: http://*************:9080

xxl.job:
  admin.addresses: http://**************:31084/xxl-job-admin
  ### xxl-job, access token
  accessToken: default_token
  ### 执行器应用名称
  executor.appname: inks-job-executor
  ### 执行器注册地址：默认使用注册地址，也可以使用配置的ip:端口
  executor:
    address:
    ### 执行器服务信息IP、端口
    ip:
    port: ${server.port}
    ### 执行器日志路径
    logpath: /home/<USER>/xxl-job/jobhandler
    ### 执行器日志清理天数
    logretentiondays: 7
