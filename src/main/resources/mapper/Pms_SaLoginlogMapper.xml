<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.Pms_SaLoginlogMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaLoginlogPojo">
        select
        id, Userid, UserName, RealName, IpAddr, LoginLocation, BrowserName, HostSystem, Direction, LoginStatus,
        LoginMsg, LoginTime, Tenantid, TenantName from Sa_LoginLog
        where Sa_LoginLog.id = #{key}
    </select>
    <sql id="selectSaLoginlogVo">
        select id,
               Userid,
               UserName,
               RealName,
               IpAddr,
               LoginLocation,
               BrowserName,
               HostSystem,
               Direction,
               LoginStatus,
               LoginMsg,
               LoginTime,
               Tenantid,
               TenantName
        from Sa_LoginLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaLoginlogPojo">
        <include refid="selectSaLoginlogVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_LoginLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.userid != null">
            and Sa_LoginLog.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null">
            and Sa_LoginLog.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null">
            and Sa_LoginLog.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.ipaddr != null">
            and Sa_LoginLog.IpAddr like concat('%', #{SearchPojo.ipaddr}, '%')
        </if>
        <if test="SearchPojo.loginlocation != null">
            and Sa_LoginLog.LoginLocation like concat('%', #{SearchPojo.loginlocation}, '%')
        </if>
        <if test="SearchPojo.browsername != null">
            and Sa_LoginLog.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
        </if>
        <if test="SearchPojo.hostsystem != null">
            and Sa_LoginLog.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
        </if>
        <if test="SearchPojo.direction != null">
            and Sa_LoginLog.Direction like concat('%', #{SearchPojo.direction}, '%')
        </if>
        <if test="SearchPojo.loginmsg != null">
            and Sa_LoginLog.LoginMsg like concat('%', #{SearchPojo.loginmsg}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_LoginLog.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.userid != null">
                or Sa_LoginLog.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.username != null">
                or Sa_LoginLog.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null">
                or Sa_LoginLog.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.ipaddr != null">
                or Sa_LoginLog.IpAddr like concat('%', #{SearchPojo.ipaddr}, '%')
            </if>
            <if test="SearchPojo.loginlocation != null">
                or Sa_LoginLog.LoginLocation like concat('%', #{SearchPojo.loginlocation}, '%')
            </if>
            <if test="SearchPojo.browsername != null">
                or Sa_LoginLog.BrowserName like concat('%', #{SearchPojo.browsername}, '%')
            </if>
            <if test="SearchPojo.hostsystem != null">
                or Sa_LoginLog.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
            </if>
            <if test="SearchPojo.direction != null">
                or Sa_LoginLog.Direction like concat('%', #{SearchPojo.direction}, '%')
            </if>
            <if test="SearchPojo.loginmsg != null">
                or Sa_LoginLog.LoginMsg like concat('%', #{SearchPojo.loginmsg}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_LoginLog.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_LoginLog(id, Userid, UserName, RealName, IpAddr, LoginLocation, BrowserName, HostSystem,
        Direction, LoginStatus, LoginMsg, LoginTime, Tenantid, TenantName)
        values (#{id}, #{userid}, #{username}, #{realname}, #{ipaddr}, #{loginlocation}, #{browsername}, #{hostsystem},
        #{direction}, #{loginstatus}, #{loginmsg}, #{logintime}, #{tenantid}, #{tenantname})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_LoginLog
        <set>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="username != null">
                UserName =#{username},
            </if>
            <if test="realname != null">
                RealName =#{realname},
            </if>
            <if test="ipaddr != null">
                IpAddr =#{ipaddr},
            </if>
            <if test="loginlocation != null">
                LoginLocation =#{loginlocation},
            </if>
            <if test="browsername != null">
                BrowserName =#{browsername},
            </if>
            <if test="hostsystem != null">
                HostSystem =#{hostsystem},
            </if>
            <if test="direction != null">
                Direction =#{direction},
            </if>
            <if test="loginstatus != null">
                LoginStatus =#{loginstatus},
            </if>
            <if test="loginmsg != null">
                LoginMsg =#{loginmsg},
            </if>
            <if test="logintime != null">
                LoginTime =#{logintime},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_LoginLog where id = #{key}
    </delete>
</mapper>

