<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaAcceptanceMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaAcceptancePojo">
        select
        id, Groupid, Name, RefNo, AcceptanceDate, AcceptanceLoc, PartyAInsp, PartyBInsp, Content, InstallMatch,
        DocumentComplete, AllFunc, PartyBEval, PartyBEvalDate, Issues, PartyAEval, PartyAEvalDate, Revion, RowNum,
        CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
        Tenantid, Revision from Sa_Acceptance
        where Sa_Acceptance.id = #{key}
    </select>
    <sql id="selectSaAcceptanceVo">
        select id,
               Groupid,
               Name,
               RefNo,
               AcceptanceDate,
               AcceptanceLoc,
               PartyAInsp,
               PartyBInsp,
               Content,
               InstallMatch,
               DocumentComplete,
               AllFunc,
               PartyBEval,
               PartyBEvalDate,
               Issues,
               PartyAEval,
               PartyAEvalDate,
               Revion,
               RowNum,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision
        from Sa_Acceptance
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaAcceptancePojo">
        <include refid="selectSaAcceptanceVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Acceptance.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.groupid != null">
            and Sa_Acceptance.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.name != null">
            and Sa_Acceptance.Name like concat('%', #{SearchPojo.name}, '%')
        </if>
        <if test="SearchPojo.refno != null">
            and Sa_Acceptance.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.acceptanceloc != null">
            and Sa_Acceptance.AcceptanceLoc like concat('%', #{SearchPojo.acceptanceloc}, '%')
        </if>
        <if test="SearchPojo.partyainsp != null">
            and Sa_Acceptance.PartyAInsp like concat('%', #{SearchPojo.partyainsp}, '%')
        </if>
        <if test="SearchPojo.partybinsp != null">
            and Sa_Acceptance.PartyBInsp like concat('%', #{SearchPojo.partybinsp}, '%')
        </if>
        <if test="SearchPojo.content != null">
            and Sa_Acceptance.Content like concat('%', #{SearchPojo.content}, '%')
        </if>
        <if test="SearchPojo.partybeval != null">
            and Sa_Acceptance.PartyBEval like concat('%', #{SearchPojo.partybeval}, '%')
        </if>
        <if test="SearchPojo.issues != null">
            and Sa_Acceptance.Issues like concat('%', #{SearchPojo.issues}, '%')
        </if>
        <if test="SearchPojo.partyaeval != null">
            and Sa_Acceptance.PartyAEval like concat('%', #{SearchPojo.partyaeval}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Acceptance.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Acceptance.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Acceptance.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Acceptance.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Acceptance.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Acceptance.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Acceptance.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Acceptance.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Acceptance.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.groupid != null">
                or Sa_Acceptance.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.name != null">
                or Sa_Acceptance.Name like concat('%', #{SearchPojo.name}, '%')
            </if>
            <if test="SearchPojo.refno != null">
                or Sa_Acceptance.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.acceptanceloc != null">
                or Sa_Acceptance.AcceptanceLoc like concat('%', #{SearchPojo.acceptanceloc}, '%')
            </if>
            <if test="SearchPojo.partyainsp != null">
                or Sa_Acceptance.PartyAInsp like concat('%', #{SearchPojo.partyainsp}, '%')
            </if>
            <if test="SearchPojo.partybinsp != null">
                or Sa_Acceptance.PartyBInsp like concat('%', #{SearchPojo.partybinsp}, '%')
            </if>
            <if test="SearchPojo.content != null">
                or Sa_Acceptance.Content like concat('%', #{SearchPojo.content}, '%')
            </if>
            <if test="SearchPojo.partybeval != null">
                or Sa_Acceptance.PartyBEval like concat('%', #{SearchPojo.partybeval}, '%')
            </if>
            <if test="SearchPojo.issues != null">
                or Sa_Acceptance.Issues like concat('%', #{SearchPojo.issues}, '%')
            </if>
            <if test="SearchPojo.partyaeval != null">
                or Sa_Acceptance.PartyAEval like concat('%', #{SearchPojo.partyaeval}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Acceptance.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Acceptance.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Acceptance.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Acceptance.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Acceptance.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Acceptance.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Acceptance.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Acceptance.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Acceptance.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Acceptance(id, Groupid, Name, RefNo, AcceptanceDate, AcceptanceLoc, PartyAInsp, PartyBInsp,
        Content, InstallMatch, DocumentComplete, AllFunc, PartyBEval, PartyBEvalDate, Issues, PartyAEval,
        PartyAEvalDate, Revion, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1,
        Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{groupid}, #{name}, #{refno}, #{acceptancedate}, #{acceptanceloc}, #{partyainsp}, #{partybinsp},
        #{content}, #{installmatch}, #{documentcomplete}, #{allfunc}, #{partybeval}, #{partybevaldate}, #{issues},
        #{partyaeval}, #{partyaevaldate}, #{revion}, #{rownum}, #{createbyid}, #{createby}, #{createdate}, #{listerid},
        #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Acceptance
        <set>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="name != null">
                Name =#{name},
            </if>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="acceptancedate != null">
                AcceptanceDate =#{acceptancedate},
            </if>
            <if test="acceptanceloc != null">
                AcceptanceLoc =#{acceptanceloc},
            </if>
            <if test="partyainsp != null">
                PartyAInsp =#{partyainsp},
            </if>
            <if test="partybinsp != null">
                PartyBInsp =#{partybinsp},
            </if>
            <if test="content != null">
                Content =#{content},
            </if>
            <if test="installmatch != null">
                InstallMatch =#{installmatch},
            </if>
            <if test="documentcomplete != null">
                DocumentComplete =#{documentcomplete},
            </if>
            <if test="allfunc != null">
                AllFunc =#{allfunc},
            </if>
            <if test="partybeval != null">
                PartyBEval =#{partybeval},
            </if>
            <if test="partybevaldate != null">
                PartyBEvalDate =#{partybevaldate},
            </if>
            <if test="issues != null">
                Issues =#{issues},
            </if>
            <if test="partyaeval != null">
                PartyAEval =#{partyaeval},
            </if>
            <if test="partyaevaldate != null">
                PartyAEvalDate =#{partyaevaldate},
            </if>
            <if test="revion != null">
                Revion =#{revion},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Acceptance where id = #{key}
    </delete>
</mapper>

