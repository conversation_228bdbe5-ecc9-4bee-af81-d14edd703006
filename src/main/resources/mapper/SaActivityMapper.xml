<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaActivityMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaActivityPojo">
        <include refid="selectbillVo"/>
        where Sa_Activity.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Sa_Activity.id,
               Sa_Activity.RefNo,
               Sa_Activity.BillType,
               Sa_Activity.BillTitle,
               Sa_Activity.BillDate,
               Sa_Activity.ActivityTheme,
               Sa_Activity.Exponent,
               Sa_Activity.Stageid,
               Sa_Activity.State,
               Sa_Activity.StateDate,
               Sa_Activity.Attachments,
               Sa_Activity.Summary,
               Sa_Activity.CreateBy,
               Sa_Activity.CreateByid,
               Sa_Activity.CreateDate,
               Sa_Activity.Lister,
               Sa_Activity.Listerid,
               Sa_Activity.ModifyDate,
               Sa_Activity.Assessor,
               Sa_Activity.Assessorid,
               Sa_Activity.AssessDate,
               Sa_Activity.Custom1,
               Sa_Activity.Custom2,
               Sa_Activity.Custom3,
               Sa_Activity.Custom4,
               Sa_Activity.Custom5,
               Sa_Activity.Custom6,
               Sa_Activity.Custom7,
               Sa_Activity.Custom8,
               Sa_Activity.Custom9,
               Sa_Activity.Custom10,
               Sa_Activity.Deptid,
               Sa_Activity.Tenantid,
               Sa_Activity.TenantName,
               Sa_Activity.Revision,
                Sa_ActivityStage.StageName
        from Sa_Activity
        Left join Sa_ActivityStage on Sa_Activity.Stageid = Sa_ActivityStage.id
    </sql>
    <sql id="selectdetailVo">
         select
id, RefNo, BillType, BillTitle, BillDate, ActivityTheme, Exponent, Stageid, State, StateDate, Attachments, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision        from Sa_Activity
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaActivityitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Activity.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_Activity.refno like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Activity.billtype like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Activity.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.activitytheme != null ">
   and Sa_Activity.activitytheme like concat('%', #{SearchPojo.activitytheme}, '%')
</if>
<if test="SearchPojo.stageid != null ">
   and Sa_Activity.stageid like concat('%', #{SearchPojo.stageid}, '%')
</if>
<if test="SearchPojo.state != null ">
   and Sa_Activity.state like concat('%', #{SearchPojo.state}, '%')
</if>
<if test="SearchPojo.attachments != null ">
   and Sa_Activity.attachments like concat('%', #{SearchPojo.attachments}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Activity.summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Activity.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Activity.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Activity.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Activity.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Activity.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Activity.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Activity.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Activity.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Activity.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Activity.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Activity.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Activity.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Activity.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Activity.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Activity.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Activity.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Activity.deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Activity.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Activity.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Activity.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Activity.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.activitytheme != null ">
   or Sa_Activity.ActivityTheme like concat('%', #{SearchPojo.activitytheme}, '%')
</if>
<if test="SearchPojo.stageid != null ">
   or Sa_Activity.Stageid like concat('%', #{SearchPojo.stageid}, '%')
</if>
<if test="SearchPojo.state != null ">
   or Sa_Activity.State like concat('%', #{SearchPojo.state}, '%')
</if>
<if test="SearchPojo.attachments != null ">
   or Sa_Activity.Attachments like concat('%', #{SearchPojo.attachments}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Activity.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Activity.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Activity.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Activity.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Activity.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Activity.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Activity.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Activity.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Activity.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Activity.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Activity.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Activity.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Activity.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Activity.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Activity.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Activity.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Activity.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Activity.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Activity.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaActivityPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Activity.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.refno != null ">
   and Sa_Activity.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Activity.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Activity.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.activitytheme != null ">
   and Sa_Activity.ActivityTheme like concat('%', #{SearchPojo.activitytheme}, '%')
</if>
<if test="SearchPojo.stageid != null ">
   and Sa_Activity.Stageid like concat('%', #{SearchPojo.stageid}, '%')
</if>
<if test="SearchPojo.state != null ">
   and Sa_Activity.State like concat('%', #{SearchPojo.state}, '%')
</if>
<if test="SearchPojo.attachments != null ">
   and Sa_Activity.Attachments like concat('%', #{SearchPojo.attachments}, '%')
</if>
<if test="SearchPojo.summary != null ">
   and Sa_Activity.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Activity.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Activity.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Activity.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Activity.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Activity.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Activity.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Activity.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Activity.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Activity.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Activity.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Activity.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Activity.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Activity.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Activity.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Activity.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Activity.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Activity.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Activity.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Activity.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Activity.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Activity.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.activitytheme != null ">
   or Sa_Activity.ActivityTheme like concat('%', #{SearchPojo.activitytheme}, '%')
</if>
<if test="SearchPojo.stageid != null ">
   or Sa_Activity.Stageid like concat('%', #{SearchPojo.stageid}, '%')
</if>
<if test="SearchPojo.state != null ">
   or Sa_Activity.State like concat('%', #{SearchPojo.state}, '%')
</if>
<if test="SearchPojo.attachments != null ">
   or Sa_Activity.Attachments like concat('%', #{SearchPojo.attachments}, '%')
</if>
<if test="SearchPojo.summary != null ">
   or Sa_Activity.Summary like concat('%', #{SearchPojo.summary}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Activity.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Activity.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Activity.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Activity.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Activity.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Activity.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Activity.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Activity.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Activity.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Activity.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Activity.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Activity.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Activity.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Activity.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Activity.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Activity.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Activity.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Activity.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Activity(id, RefNo, BillType, BillTitle, BillDate, ActivityTheme, Exponent, Stageid, State, StateDate, Attachments, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{activitytheme}, #{exponent}, #{stageid}, #{state}, #{statedate}, #{attachments}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Activity
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="activitytheme != null ">
                ActivityTheme =#{activitytheme},
            </if>
            <if test="exponent != null">
                Exponent =#{exponent},
            </if>
            <if test="stageid != null ">
                Stageid =#{stageid},
            </if>
            <if test="state != null ">
                State =#{state},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="attachments != null ">
                Attachments =#{attachments},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Activity where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Activity SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.pms.domain.pojo.SaActivityPojo">
        select
          id
        from Sa_ActivityItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

