<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaActivityitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaActivityitemPojo">
        <include refid="selectSaActivityitemVo"/>
        where Sa_ActivityItem.id = #{key} 
    </select>
    <sql id="selectSaActivityitemVo">
         select
id, Pid, Parentid, StageName, StageItemid, PlannDate, Operator, Operatorid, Collaborators, Collaboratorids, FinishMark, FinishDate, FinishDesc, MustMark, FileMark, Attachments, Lister, Listerid, ModifyDate, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Sa_ActivityItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaActivityitemPojo">
        <include refid="selectSaActivityitemVo"/>
        where Sa_ActivityItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaActivityitemPojo">
        <include refid="selectSaActivityitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_ActivityItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_ActivityItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
   and Sa_ActivityItem.parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.stagename != null and SearchPojo.stagename != ''">
   and Sa_ActivityItem.stagename like concat('%', #{SearchPojo.stagename}, '%')
</if>
<if test="SearchPojo.stageitemid != null and SearchPojo.stageitemid != ''">
   and Sa_ActivityItem.stageitemid like concat('%', #{SearchPojo.stageitemid}, '%')
</if>
<if test="SearchPojo.operator != null and SearchPojo.operator != ''">
   and Sa_ActivityItem.operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null and SearchPojo.operatorid != ''">
   and Sa_ActivityItem.operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.collaborators != null and SearchPojo.collaborators != ''">
   and Sa_ActivityItem.collaborators like concat('%', #{SearchPojo.collaborators}, '%')
</if>
<if test="SearchPojo.collaboratorids != null and SearchPojo.collaboratorids != ''">
   and Sa_ActivityItem.collaboratorids like concat('%', #{SearchPojo.collaboratorids}, '%')
</if>
<if test="SearchPojo.finishdesc != null and SearchPojo.finishdesc != ''">
   and Sa_ActivityItem.finishdesc like concat('%', #{SearchPojo.finishdesc}, '%')
</if>
<if test="SearchPojo.attachments != null and SearchPojo.attachments != ''">
   and Sa_ActivityItem.attachments like concat('%', #{SearchPojo.attachments}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Sa_ActivityItem.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   and Sa_ActivityItem.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_ActivityItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_ActivityItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_ActivityItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_ActivityItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_ActivityItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_ActivityItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   and Sa_ActivityItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   and Sa_ActivityItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   and Sa_ActivityItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   and Sa_ActivityItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   and Sa_ActivityItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_ActivityItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.parentid != null and SearchPojo.parentid != ''">
   or Sa_ActivityItem.Parentid like concat('%', #{SearchPojo.parentid}, '%')
</if>
<if test="SearchPojo.stagename != null and SearchPojo.stagename != ''">
   or Sa_ActivityItem.StageName like concat('%', #{SearchPojo.stagename}, '%')
</if>
<if test="SearchPojo.stageitemid != null and SearchPojo.stageitemid != ''">
   or Sa_ActivityItem.StageItemid like concat('%', #{SearchPojo.stageitemid}, '%')
</if>
<if test="SearchPojo.operator != null and SearchPojo.operator != ''">
   or Sa_ActivityItem.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null and SearchPojo.operatorid != ''">
   or Sa_ActivityItem.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.collaborators != null and SearchPojo.collaborators != ''">
   or Sa_ActivityItem.Collaborators like concat('%', #{SearchPojo.collaborators}, '%')
</if>
<if test="SearchPojo.collaboratorids != null and SearchPojo.collaboratorids != ''">
   or Sa_ActivityItem.Collaboratorids like concat('%', #{SearchPojo.collaboratorids}, '%')
</if>
<if test="SearchPojo.finishdesc != null and SearchPojo.finishdesc != ''">
   or Sa_ActivityItem.FinishDesc like concat('%', #{SearchPojo.finishdesc}, '%')
</if>
<if test="SearchPojo.attachments != null and SearchPojo.attachments != ''">
   or Sa_ActivityItem.Attachments like concat('%', #{SearchPojo.attachments}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Sa_ActivityItem.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Sa_ActivityItem.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_ActivityItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_ActivityItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_ActivityItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_ActivityItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_ActivityItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_ActivityItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
   or Sa_ActivityItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
   or Sa_ActivityItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
   or Sa_ActivityItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
   or Sa_ActivityItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
   or Sa_ActivityItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_ActivityItem(id, Pid, Parentid, StageName, StageItemid, PlannDate, Operator, Operatorid, Collaborators, Collaboratorids, FinishMark, FinishDate, FinishDesc, MustMark, FileMark, Attachments, Lister, Listerid, ModifyDate, Remark, RowNum, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{pid}, #{parentid}, #{stagename}, #{stageitemid}, #{planndate}, #{operator}, #{operatorid}, #{collaborators}, #{collaboratorids}, #{finishmark}, #{finishdate}, #{finishdesc}, #{mustmark}, #{filemark}, #{attachments}, #{lister}, #{listerid}, #{modifydate}, #{remark}, #{rownum}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ActivityItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="parentid != null ">
                Parentid = #{parentid},
            </if>
            <if test="stagename != null ">
                StageName = #{stagename},
            </if>
            <if test="stageitemid != null ">
                StageItemid = #{stageitemid},
            </if>
            <if test="planndate != null">
                PlannDate = #{planndate},
            </if>
            <if test="operator != null ">
                Operator = #{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid = #{operatorid},
            </if>
            <if test="collaborators != null ">
                Collaborators = #{collaborators},
            </if>
            <if test="collaboratorids != null ">
                Collaboratorids = #{collaboratorids},
            </if>
            <if test="finishmark != null">
                FinishMark = #{finishmark},
            </if>
            <if test="finishdate != null">
                FinishDate = #{finishdate},
            </if>
            <if test="finishdesc != null ">
                FinishDesc = #{finishdesc},
            </if>
            <if test="mustmark != null">
                MustMark = #{mustmark},
            </if>
            <if test="filemark != null">
                FileMark = #{filemark},
            </if>
            <if test="attachments != null ">
                Attachments = #{attachments},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="listerid != null ">
                Listerid = #{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 = #{custom10},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ActivityItem where id = #{key} 
    </delete>

    <update id="syncFinishByid">
        update Sa_ActivityItem
        set FinishMark = #{finishmark},
            FinishDate = #{date},
            Revision = Revision + 1
        where id = #{actiitemid}
    </update>
</mapper>

