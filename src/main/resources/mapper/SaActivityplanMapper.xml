<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaActivityplanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaActivityplanPojo">
        <include refid="selectSaActivityplanVo"/>
        where Sa_ActivityPlan.id = #{key} 
    </select>
    <sql id="selectSaActivityplanVo">
         select
id, Pid, Type, ActiItemid, PlanDate, FinishDate, FinishMark, Description, Attachments, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_ActivityPlan
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaActivityplanPojo">
        <include refid="selectSaActivityplanVo"/>
        where Sa_ActivityPlan.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaActivityplanPojo">
        <include refid="selectSaActivityplanVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_ActivityPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_ActivityPlan.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.type != null and SearchPojo.type != ''">
   and Sa_ActivityPlan.type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.actiitemid != null and SearchPojo.actiitemid != ''">
   and Sa_ActivityPlan.actiitemid like concat('%', #{SearchPojo.actiitemid}, '%')
</if>
<if test="SearchPojo.description != null and SearchPojo.description != ''">
   and Sa_ActivityPlan.description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.attachments != null and SearchPojo.attachments != ''">
   and Sa_ActivityPlan.attachments like concat('%', #{SearchPojo.attachments}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_ActivityPlan.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_ActivityPlan.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_ActivityPlan.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_ActivityPlan.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_ActivityPlan.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_ActivityPlan.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_ActivityPlan.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.type != null and SearchPojo.type != ''">
   or Sa_ActivityPlan.Type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.actiitemid != null and SearchPojo.actiitemid != ''">
   or Sa_ActivityPlan.ActiItemid like concat('%', #{SearchPojo.actiitemid}, '%')
</if>
<if test="SearchPojo.description != null and SearchPojo.description != ''">
   or Sa_ActivityPlan.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.attachments != null and SearchPojo.attachments != ''">
   or Sa_ActivityPlan.Attachments like concat('%', #{SearchPojo.attachments}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_ActivityPlan.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_ActivityPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_ActivityPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_ActivityPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_ActivityPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_ActivityPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_ActivityPlan(id, Pid, Type, ActiItemid, PlanDate, FinishDate, FinishMark, Description, Attachments, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{type}, #{actiitemid}, #{plandate}, #{finishdate}, #{finishmark}, #{description}, #{attachments}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ActivityPlan
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="type != null ">
                Type = #{type},
            </if>
            <if test="actiitemid != null ">
                ActiItemid = #{actiitemid},
            </if>
            <if test="plandate != null">
                PlanDate = #{plandate},
            </if>
            <if test="finishdate != null">
                FinishDate = #{finishdate},
            </if>
            <if test="finishmark != null">
                FinishMark = #{finishmark},
            </if>
            <if test="description != null ">
                Description = #{description},
            </if>
            <if test="attachments != null ">
                Attachments = #{attachments},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ActivityPlan where id = #{key} 
    </delete>

</mapper>

