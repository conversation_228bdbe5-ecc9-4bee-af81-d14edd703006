<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaApiinfoMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaApiinfoPojo">
        <include refid="selectSaApiinfoVo"/>
        where Sa_ApiInfo.id = #{key} 
    </select>
    <sql id="selectSaApiinfoVo">
         select
id, Fnid, FnCode, ApiName, ApiDescription, ApiUrl, HttpMethod, RequestParams, ResponseParams, ResponseExample, StatusCode, Operationid, Produces, Consumes, Tags, IsDeprecated, Curl, RowNum, Remark, CreateBy, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ate, <PERSON>er, <PERSON><PERSON>d, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from Sa_ApiInfo
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaApiinfoPojo">
        <include refid="selectSaApiinfoVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_ApiInfo.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.fnid != null ">
   and Sa_ApiInfo.Fnid like concat('%', #{SearchPojo.fnid}, '%')
</if>
<if test="SearchPojo.fncode != null ">
   and Sa_ApiInfo.FnCode like concat('%', #{SearchPojo.fncode}, '%')
</if>
<if test="SearchPojo.apiname != null ">
   and Sa_ApiInfo.ApiName like concat('%', #{SearchPojo.apiname}, '%')
</if>
<if test="SearchPojo.apidescription != null ">
   and Sa_ApiInfo.ApiDescription like concat('%', #{SearchPojo.apidescription}, '%')
</if>
<if test="SearchPojo.apiurl != null ">
   and Sa_ApiInfo.ApiUrl like concat('%', #{SearchPojo.apiurl}, '%')
</if>
<if test="SearchPojo.httpmethod != null ">
   and Sa_ApiInfo.HttpMethod like concat('%', #{SearchPojo.httpmethod}, '%')
</if>
<if test="SearchPojo.requestparams != null ">
   and Sa_ApiInfo.RequestParams like concat('%', #{SearchPojo.requestparams}, '%')
</if>
<if test="SearchPojo.responseparams != null ">
   and Sa_ApiInfo.ResponseParams like concat('%', #{SearchPojo.responseparams}, '%')
</if>
<if test="SearchPojo.responseexample != null ">
   and Sa_ApiInfo.ResponseExample like concat('%', #{SearchPojo.responseexample}, '%')
</if>
<if test="SearchPojo.statuscode != null ">
   and Sa_ApiInfo.StatusCode like concat('%', #{SearchPojo.statuscode}, '%')
</if>
<if test="SearchPojo.operationid != null ">
   and Sa_ApiInfo.Operationid like concat('%', #{SearchPojo.operationid}, '%')
</if>
<if test="SearchPojo.produces != null ">
   and Sa_ApiInfo.Produces like concat('%', #{SearchPojo.produces}, '%')
</if>
<if test="SearchPojo.consumes != null ">
   and Sa_ApiInfo.Consumes like concat('%', #{SearchPojo.consumes}, '%')
</if>
<if test="SearchPojo.tags != null ">
   and Sa_ApiInfo.Tags like concat('%', #{SearchPojo.tags}, '%')
</if>
<if test="SearchPojo.curl != null ">
   and Sa_ApiInfo.Curl like concat('%', #{SearchPojo.curl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_ApiInfo.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_ApiInfo.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_ApiInfo.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_ApiInfo.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_ApiInfo.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_ApiInfo.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_ApiInfo.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_ApiInfo.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_ApiInfo.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_ApiInfo.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_ApiInfo.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.fnid != null ">
   or Sa_ApiInfo.Fnid like concat('%', #{SearchPojo.fnid}, '%')
</if>
<if test="SearchPojo.fncode != null ">
   or Sa_ApiInfo.FnCode like concat('%', #{SearchPojo.fncode}, '%')
</if>
<if test="SearchPojo.apiname != null ">
   or Sa_ApiInfo.ApiName like concat('%', #{SearchPojo.apiname}, '%')
</if>
<if test="SearchPojo.apidescription != null ">
   or Sa_ApiInfo.ApiDescription like concat('%', #{SearchPojo.apidescription}, '%')
</if>
<if test="SearchPojo.apiurl != null ">
   or Sa_ApiInfo.ApiUrl like concat('%', #{SearchPojo.apiurl}, '%')
</if>
<if test="SearchPojo.httpmethod != null ">
   or Sa_ApiInfo.HttpMethod like concat('%', #{SearchPojo.httpmethod}, '%')
</if>
<if test="SearchPojo.requestparams != null ">
   or Sa_ApiInfo.RequestParams like concat('%', #{SearchPojo.requestparams}, '%')
</if>
<if test="SearchPojo.responseparams != null ">
   or Sa_ApiInfo.ResponseParams like concat('%', #{SearchPojo.responseparams}, '%')
</if>
<if test="SearchPojo.responseexample != null ">
   or Sa_ApiInfo.ResponseExample like concat('%', #{SearchPojo.responseexample}, '%')
</if>
<if test="SearchPojo.statuscode != null ">
   or Sa_ApiInfo.StatusCode like concat('%', #{SearchPojo.statuscode}, '%')
</if>
<if test="SearchPojo.operationid != null ">
   or Sa_ApiInfo.Operationid like concat('%', #{SearchPojo.operationid}, '%')
</if>
<if test="SearchPojo.produces != null ">
   or Sa_ApiInfo.Produces like concat('%', #{SearchPojo.produces}, '%')
</if>
<if test="SearchPojo.consumes != null ">
   or Sa_ApiInfo.Consumes like concat('%', #{SearchPojo.consumes}, '%')
</if>
<if test="SearchPojo.tags != null ">
   or Sa_ApiInfo.Tags like concat('%', #{SearchPojo.tags}, '%')
</if>
<if test="SearchPojo.curl != null ">
   or Sa_ApiInfo.Curl like concat('%', #{SearchPojo.curl}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_ApiInfo.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_ApiInfo.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_ApiInfo.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_ApiInfo.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_ApiInfo.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_ApiInfo.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_ApiInfo.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_ApiInfo.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_ApiInfo.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_ApiInfo.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_ApiInfo.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_ApiInfo(id, Fnid, FnCode, ApiName, ApiDescription, ApiUrl, HttpMethod, RequestParams, ResponseParams, ResponseExample, StatusCode, Operationid, Produces, Consumes, Tags, IsDeprecated, Curl, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{fnid}, #{fncode}, #{apiname}, #{apidescription}, #{apiurl}, #{httpmethod}, #{requestparams}, #{responseparams}, #{responseexample}, #{statuscode}, #{operationid}, #{produces}, #{consumes}, #{tags}, #{isdeprecated}, #{curl}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <insert id="insertBatch">
        insert into Sa_ApiInfo (
        id, Fnid, FnCode, ApiName, ApiDescription, ApiUrl, HttpMethod, RequestParams,
        ResponseParams, ResponseExample, StatusCode, Operationid, Produces, Consumes,
        Tags, IsDeprecated, Curl, RowNum, Remark, CreateBy, CreateByid, CreateDate,
        Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
        Tenantid, TenantName, Revision
        )
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.id}, #{item.fnid}, #{item.fncode}, #{item.apiname}, #{item.apidescription},
            #{item.apiurl}, #{item.httpmethod}, #{item.requestparams}, #{item.responseparams},
            #{item.responseexample}, #{item.statuscode}, #{item.operationid}, #{item.produces},
            #{item.consumes}, #{item.tags}, #{item.isdeprecated}, #{item.curl}, #{item.rownum},
            #{item.remark}, #{item.createby}, #{item.createbyid}, #{item.createdate},
            #{item.lister}, #{item.listerid}, #{item.modifydate}, #{item.custom1},
            #{item.custom2}, #{item.custom3}, #{item.custom4}, #{item.custom5},
            #{item.tenantid}, #{item.tenantname}, #{item.revision})
        </foreach>
    </insert>


    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ApiInfo
        <set>
            <if test="fnid != null ">
                Fnid =#{fnid},
            </if>
            <if test="fncode != null ">
                FnCode =#{fncode},
            </if>
            <if test="apiname != null ">
                ApiName =#{apiname},
            </if>
            <if test="apidescription != null ">
                ApiDescription =#{apidescription},
            </if>
            <if test="apiurl != null ">
                ApiUrl =#{apiurl},
            </if>
            <if test="httpmethod != null ">
                HttpMethod =#{httpmethod},
            </if>
            <if test="requestparams != null ">
                RequestParams =#{requestparams},
            </if>
            <if test="responseparams != null ">
                ResponseParams =#{responseparams},
            </if>
            <if test="responseexample != null ">
                ResponseExample =#{responseexample},
            </if>
            <if test="statuscode != null ">
                StatusCode =#{statuscode},
            </if>
            <if test="operationid != null ">
                Operationid =#{operationid},
            </if>
            <if test="produces != null ">
                Produces =#{produces},
            </if>
            <if test="consumes != null ">
                Consumes =#{consumes},
            </if>
            <if test="tags != null ">
                Tags =#{tags},
            </if>
            <if test="isdeprecated != null">
                IsDeprecated =#{isdeprecated},
            </if>
            <if test="curl != null ">
                Curl =#{curl},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ApiInfo where id = #{key} 
    </delete>
</mapper>

