<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaCasecenterMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaCasecenterPojo">
        select
        id, Title, Category, CoverImages, Brief, Content, PublicMark, RowNum, Remark, CreateBy, CreateByid, CreateDate,
        Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision from Sa_CaseCenter
        where Sa_CaseCenter.id = #{key}
    </select>
    <sql id="selectSaCasecenterVo">
        select id,
               Title,
               Category,
               CoverImages,
               Brief,
               Content,
               PublicMark,
               RowNum,
               Remark,
               Create<PERSON><PERSON>,
               <PERSON><PERSON><PERSON>yid,
               <PERSON><PERSON><PERSON><PERSON>,
               Lister,
               <PERSON><PERSON><PERSON>,
               <PERSON>dify<PERSON>ate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               Revision
        from Sa_CaseCenter
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaCasecenterPojo">
        <include refid="selectSaCasecenterVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_CaseCenter.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.title != null">
            and Sa_CaseCenter.Title like concat('%', #{SearchPojo.title}, '%')
        </if>
        <if test="SearchPojo.category != null">
            and Sa_CaseCenter.Category like concat('%', #{SearchPojo.category}, '%')
        </if>
        <if test="SearchPojo.coverimages != null">
            and Sa_CaseCenter.CoverImages like concat('%', #{SearchPojo.coverimages}, '%')
        </if>
        <if test="SearchPojo.brief != null">
            and Sa_CaseCenter.Brief like concat('%', #{SearchPojo.brief}, '%')
        </if>
        <if test="SearchPojo.content != null">
            and Sa_CaseCenter.Content like concat('%', #{SearchPojo.content}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_CaseCenter.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_CaseCenter.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_CaseCenter.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_CaseCenter.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_CaseCenter.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_CaseCenter.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_CaseCenter.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_CaseCenter.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_CaseCenter.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_CaseCenter.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.title != null">
                or Sa_CaseCenter.Title like concat('%', #{SearchPojo.title}, '%')
            </if>
            <if test="SearchPojo.category != null">
                or Sa_CaseCenter.Category like concat('%', #{SearchPojo.category}, '%')
            </if>
            <if test="SearchPojo.coverimages != null">
                or Sa_CaseCenter.CoverImages like concat('%', #{SearchPojo.coverimages}, '%')
            </if>
            <if test="SearchPojo.brief != null">
                or Sa_CaseCenter.Brief like concat('%', #{SearchPojo.brief}, '%')
            </if>
            <if test="SearchPojo.content != null">
                or Sa_CaseCenter.Content like concat('%', #{SearchPojo.content}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_CaseCenter.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_CaseCenter.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_CaseCenter.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_CaseCenter.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_CaseCenter.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_CaseCenter.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_CaseCenter.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_CaseCenter.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_CaseCenter.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_CaseCenter.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_CaseCenter(id, Title, Category, CoverImages, Brief, Content, PublicMark, RowNum, Remark,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
        Tenantid, Revision)
        values (#{id}, #{title}, #{category}, #{coverimages}, #{brief}, #{content}, #{publicmark}, #{rownum}, #{remark},
        #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2},
        #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_CaseCenter
        <set>
            <if test="title != null">
                Title =#{title},
            </if>
            <if test="category != null">
                Category =#{category},
            </if>
            <if test="coverimages != null">
                CoverImages =#{coverimages},
            </if>
            <if test="brief != null">
                Brief =#{brief},
            </if>
            <if test="content != null">
                Content =#{content},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_CaseCenter where id = #{key}
    </delete>
</mapper>

