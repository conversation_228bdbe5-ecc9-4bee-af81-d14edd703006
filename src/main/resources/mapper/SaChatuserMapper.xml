<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaChatuserMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaChatuserPojo">
        select
        id, UserName, Userid, RealName, Avatar, LoginTime, IsAdmin, IpAddr, Groupids, Logid, HostSystem, RowNum, Remark,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
        Tenantid, TenantName, Revision from Sa_ChatUser
        where Sa_ChatUser.id = #{key}
    </select>
    <sql id="selectSaChatuserVo">
        select id,
               UserName,
               Userid,
               RealName,
               Avatar,
               LoginTime,
               IsAdmin,
               IpAddr,
               Groupids,
               Logid,
               HostSystem,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from Sa_ChatUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaChatuserPojo">
        <include refid="selectSaChatuserVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_ChatUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null">
            and Sa_ChatUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.userid != null">
            and Sa_ChatUser.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.realname != null">
            and Sa_ChatUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.avatar != null">
            and Sa_ChatUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.ipaddr != null">
            and Sa_ChatUser.IpAddr like concat('%', #{SearchPojo.ipaddr}, '%')
        </if>
        <if test="SearchPojo.groupids != null">
            and Sa_ChatUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.logid != null">
            and Sa_ChatUser.Logid like concat('%', #{SearchPojo.logid}, '%')
        </if>
        <if test="SearchPojo.hostsystem != null">
            and Sa_ChatUser.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_ChatUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_ChatUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_ChatUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_ChatUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_ChatUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_ChatUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_ChatUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_ChatUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_ChatUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_ChatUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_ChatUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.username != null">
                or Sa_ChatUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.userid != null">
                or Sa_ChatUser.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.realname != null">
                or Sa_ChatUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.avatar != null">
                or Sa_ChatUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
            </if>
            <if test="SearchPojo.ipaddr != null">
                or Sa_ChatUser.IpAddr like concat('%', #{SearchPojo.ipaddr}, '%')
            </if>
            <if test="SearchPojo.groupids != null">
                or Sa_ChatUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.logid != null">
                or Sa_ChatUser.Logid like concat('%', #{SearchPojo.logid}, '%')
            </if>
            <if test="SearchPojo.hostsystem != null">
                or Sa_ChatUser.HostSystem like concat('%', #{SearchPojo.hostsystem}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_ChatUser.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_ChatUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_ChatUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_ChatUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_ChatUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_ChatUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_ChatUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_ChatUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_ChatUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_ChatUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_ChatUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_ChatUser(id, UserName, Userid, RealName, Avatar, LoginTime, IsAdmin, IpAddr, Groupids, Logid,
        HostSystem, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
        Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{username}, #{userid}, #{realname}, #{avatar}, #{logintime}, #{isadmin}, #{ipaddr}, #{groupids},
        #{logid}, #{hostsystem}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister},
        #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid},
        #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ChatUser
        <set>
            <if test="username != null">
                UserName =#{username},
            </if>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="realname != null">
                RealName =#{realname},
            </if>
            <if test="avatar != null">
                Avatar =#{avatar},
            </if>
            <if test="logintime != null">
                LoginTime =#{logintime},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="ipaddr != null">
                IpAddr =#{ipaddr},
            </if>
            <if test="groupids != null">
                Groupids =#{groupids},
            </if>
            <if test="logid != null">
                Logid =#{logid},
            </if>
            <if test="hostsystem != null">
                HostSystem =#{hostsystem},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ChatUser where id = #{key}
    </delete>

    <select id="checkUserId" resultType="int">
        select count(1) from Sa_ChatUser where Userid = #{userid}
    </select>

    <select id="getEntityByUserId" resultType="inks.service.sa.pms.domain.pojo.SaChatuserPojo">
        <include refid="selectSaChatuserVo"/>
        where Sa_ChatUser.Userid = #{userid}
    </select>

    <select id="getIdByUserId" resultType="java.lang.String">
        select id from Sa_ChatUser where Userid = #{userid}
    </select>
</mapper>

