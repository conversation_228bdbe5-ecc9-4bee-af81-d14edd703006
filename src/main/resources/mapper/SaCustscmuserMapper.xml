<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaCustscmuserMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaCustscmuserPojo">
        select Sa_CustScmUser.id,
        Sa_CustScmUser.Tenantid,
        Sa_CustScmUser.TenantName,
        Sa_CustScmUser.Userid,
        Sa_CustScmUser.UserName,
        Sa_CustScmUser.RealName,
        Sa_CustScmUser.UserType,
        Sa_CustScmUser.IsAdmin,
        Sa_CustScmUser.Deptid,
        Sa_CustScmUser.DeptCode,
        Sa_CustScmUser.DeptName,
        Sa_CustScmUser.IsDeptAdmin,
        Sa_CustScmUser.DeptRowNum,
        Sa_CustScmUser.RowNum,
        Sa_CustScmUser.UserStatus,
        Sa_CustScmUser.UserCode,
        Sa_CustScmUser.Groupids,
        Sa_CustScmUser.GroupNames,
        Sa_CustScmUser.CreateBy,
        Sa_CustScmUser.CreateByid,
        Sa_CustScmUser.CreateDate,
        Sa_CustScmUser.Lister,
        Sa_CustScmUser.Listerid,
        Sa_CustScmUser.ModifyDate,
        Sa_CustScmUser.Custom1,
        Sa_CustScmUser.Custom2,
        Sa_CustScmUser.Custom3,
        Sa_CustScmUser.Custom4,
        Sa_CustScmUser.Custom5,
        Sa_CustScmUser.Revision,
        Sa_ScmUser.NickName,
        Sa_ScmUser.Mobile,
        Sa_ScmUser.Email,
        Sa_ScmUser.Sex,
        Sa_ScmUser.Avatar
        from Sa_CustScmUser
        LEFT JOIN Sa_ScmUser ON Sa_CustScmUser.Userid = Sa_ScmUser.Userid
        where Sa_CustScmUser.id = #{key}
    </select>
    <sql id="selectSaCustscmuserVo">
        select Sa_CustScmUser.id,
               Sa_CustScmUser.Tenantid,
               Sa_CustScmUser.TenantName,
               Sa_CustScmUser.Userid,
               Sa_CustScmUser.UserName,
               Sa_CustScmUser.RealName,
               Sa_CustScmUser.UserType,
               Sa_CustScmUser.IsAdmin,
               Sa_CustScmUser.Deptid,
               Sa_CustScmUser.DeptCode,
               Sa_CustScmUser.DeptName,
               Sa_CustScmUser.IsDeptAdmin,
               Sa_CustScmUser.DeptRowNum,
               Sa_CustScmUser.RowNum,
               Sa_CustScmUser.UserStatus,
               Sa_CustScmUser.UserCode,
               Sa_CustScmUser.Groupids,
               Sa_CustScmUser.GroupNames,
               Sa_CustScmUser.CreateBy,
               Sa_CustScmUser.CreateByid,
               Sa_CustScmUser.CreateDate,
               Sa_CustScmUser.Lister,
               Sa_CustScmUser.Listerid,
               Sa_CustScmUser.ModifyDate,
               Sa_CustScmUser.Custom1,
               Sa_CustScmUser.Custom2,
               Sa_CustScmUser.Custom3,
               Sa_CustScmUser.Custom4,
               Sa_CustScmUser.Custom5,
               Sa_CustScmUser.Revision,
               Sa_ScmUser.NickName,
               Sa_ScmUser.Mobile,
               Sa_ScmUser.Email,
               Sa_ScmUser.Sex,
               Sa_ScmUser.Avatar
        from Sa_CustScmUser
                 LEFT JOIN Sa_ScmUser ON Sa_CustScmUser.Userid = Sa_ScmUser.Userid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaCustscmuserPojo">
        <include refid="selectSaCustscmuserVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_CustScmUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.tenantname != null">
            and Sa_CustScmUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
        <if test="SearchPojo.userid != null">
            and Sa_CustScmUser.Userid like concat('%',
            #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null">
            and Sa_CustScmUser.UserName like concat('%',
            #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null">
            and Sa_CustScmUser.RealName like concat('%',
            #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_CustScmUser.Deptid like concat('%',
            #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.deptcode != null">
            and Sa_CustScmUser.DeptCode like concat('%',
            #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null">
            and Sa_CustScmUser.DeptName like concat('%',
            #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.usercode != null">
            and Sa_CustScmUser.UserCode like concat('%',
            #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.groupids != null">
            and Sa_CustScmUser.Groupids like concat('%',
            #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.groupnames != null">
            and Sa_CustScmUser.GroupNames like concat('%',
            #{SearchPojo.groupnames}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_CustScmUser.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_CustScmUser.CreateByid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_CustScmUser.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_CustScmUser.Listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_CustScmUser.Custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_CustScmUser.Custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_CustScmUser.Custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_CustScmUser.Custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_CustScmUser.Custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.tenantname != null">
                or Sa_CustScmUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
            <if test="SearchPojo.userid != null">
                or Sa_CustScmUser.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.username != null">
                or Sa_CustScmUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null">
                or Sa_CustScmUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_CustScmUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.deptcode != null">
                or Sa_CustScmUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null">
                or Sa_CustScmUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.usercode != null">
                or Sa_CustScmUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
            </if>
            <if test="SearchPojo.groupids != null">
                or Sa_CustScmUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.groupnames != null">
                or Sa_CustScmUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_CustScmUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_CustScmUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_CustScmUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_CustScmUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_CustScmUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_CustScmUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_CustScmUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_CustScmUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_CustScmUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_CustScmUser(id, Tenantid, TenantName, Userid, UserName, RealName, UserType, IsAdmin, Deptid,
        DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum, UserStatus, UserCode, Groupids,
        GroupNames, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1,
        Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{tenantid}, #{tenantname}, #{userid}, #{username}, #{realname}, #{usertype}, #{isadmin},
        #{deptid}, #{deptcode}, #{deptname}, #{isdeptadmin}, #{deptrownum}, #{rownum}, #{userstatus},
        #{usercode}, #{groupids}, #{groupnames}, #{createby}, #{createbyid}, #{createdate}, #{lister},
        #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_CustScmUser
        <set>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="username != null">
                UserName =#{username},
            </if>
            <if test="realname != null">
                RealName =#{realname},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null">
                GroupNames =#{groupnames},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_CustScmUser
        where id = #{key}
    </delete>

    <select id="getEntityByUserid" resultType="inks.service.sa.pms.domain.pojo.SaCustscmuserPojo">
        <include refid="selectSaCustscmuserVo"/>
        where Sa_CustScmUser.Userid = #{key}
    </select>

    <select id="getListByUserid" resultType="inks.service.sa.pms.domain.pojo.SaCustscmuserPojo">
        <include refid="selectSaCustscmuserVo"/>
        where Sa_CustScmUser.Userid = #{userid}
        Order by id
    </select>
</mapper>

