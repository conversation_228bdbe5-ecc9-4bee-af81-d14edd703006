<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaDatabaseMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaDatabasePojo">
        select
        id, Title, Url, UserName, Password, DriverClassName, EnabledMark, RowNum, Remark, Lister, CreateDate, ModifyDate
        from Sa_Database
        where Sa_Database.id = #{key}
    </select>
    <sql id="selectSaDatabaseVo">
        select id,
               Title,
               Url,
               UserName,
               Password,
               DriverClassName,
               EnabledMark,
               RowNum,
               Remark,
               Lister,
               CreateDate,
               ModifyDate
        from Sa_Database
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaDatabasePojo">
        <include refid="selectSaDatabaseVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Database.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.title != null">
            and Sa_Database.Title like concat('%', #{SearchPojo.title}, '%')
        </if>
        <if test="SearchPojo.url != null">
            and Sa_Database.Url like concat('%', #{SearchPojo.url}, '%')
        </if>
        <if test="SearchPojo.username != null">
            and Sa_Database.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.password != null">
            and Sa_Database.Password like concat('%', #{SearchPojo.password}, '%')
        </if>
        <if test="SearchPojo.driverclassname != null">
            and Sa_Database.DriverClassName like concat('%', #{SearchPojo.driverclassname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Database.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Database.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.title != null">
                or Sa_Database.Title like concat('%', #{SearchPojo.title}, '%')
            </if>
            <if test="SearchPojo.url != null">
                or Sa_Database.Url like concat('%', #{SearchPojo.url}, '%')
            </if>
            <if test="SearchPojo.username != null">
                or Sa_Database.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.password != null">
                or Sa_Database.Password like concat('%', #{SearchPojo.password}, '%')
            </if>
            <if test="SearchPojo.driverclassname != null">
                or Sa_Database.DriverClassName like concat('%', #{SearchPojo.driverclassname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Database.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Database.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Database(id, Title, Url, UserName, Password, DriverClassName, EnabledMark, RowNum, Remark,
        Lister, CreateDate, ModifyDate)
        values (#{id}, #{title}, #{url}, #{username}, #{password}, #{driverclassname}, #{enabledmark}, #{rownum},
        #{remark}, #{lister}, #{createdate}, #{modifydate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Database
        <set>
            <if test="title != null">
                Title =#{title},
            </if>
            <if test="url != null">
                Url =#{url},
            </if>
            <if test="username != null">
                UserName =#{username},
            </if>
            <if test="password != null">
                Password =#{password},
            </if>
            <if test="driverclassname != null">
                DriverClassName =#{driverclassname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Database where id = #{key}
    </delete>
</mapper>

