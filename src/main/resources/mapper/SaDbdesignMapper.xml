<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaDbdesignMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaDbdesignPojo">
        <include refid="selectbillVo"/>
        where Sa_DbDesign.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, Fnid, TableName, Comment, CharacterSet, Collation, VersionNumber, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from Sa_DbDesign
    </sql>
    <sql id="selectdetailVo">
         select
id, Fnid, TableName, Comment, CharacterSet, Collation, VersionNumber, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision        from Sa_DbDesign
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaDbdesignitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DbDesign.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.fnid != null ">
   and Sa_DbDesign.fnid like concat('%', #{SearchPojo.fnid}, '%')
</if>
<if test="SearchPojo.tablename != null ">
   and Sa_DbDesign.tablename like concat('%', #{SearchPojo.tablename}, '%')
</if>
<if test="SearchPojo.comment != null ">
   and Sa_DbDesign.comment like concat('%', #{SearchPojo.comment}, '%')
</if>
<if test="SearchPojo.versionnumber != null ">
   and Sa_DbDesign.versionnumber like concat('%', #{SearchPojo.versionnumber}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DbDesign.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DbDesign.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DbDesign.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DbDesign.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DbDesign.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_DbDesign.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_DbDesign.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_DbDesign.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_DbDesign.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_DbDesign.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DbDesign.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.fnid != null ">
   or Sa_DbDesign.Fnid like concat('%', #{SearchPojo.fnid}, '%')
</if>
<if test="SearchPojo.tablename != null ">
   or Sa_DbDesign.TableName like concat('%', #{SearchPojo.tablename}, '%')
</if>
<if test="SearchPojo.comment != null ">
   or Sa_DbDesign.Comment like concat('%', #{SearchPojo.comment}, '%')
</if>
<if test="SearchPojo.versionnumber != null ">
   or Sa_DbDesign.VersionNumber like concat('%', #{SearchPojo.versionnumber}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DbDesign.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DbDesign.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DbDesign.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DbDesign.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DbDesign.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_DbDesign.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_DbDesign.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_DbDesign.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_DbDesign.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_DbDesign.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DbDesign.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaDbdesignPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DbDesign.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.fnid != null ">
   and Sa_DbDesign.Fnid like concat('%', #{SearchPojo.fnid}, '%')
</if>
<if test="SearchPojo.tablename != null ">
   and Sa_DbDesign.TableName like concat('%', #{SearchPojo.tablename}, '%')
</if>
<if test="SearchPojo.comment != null ">
   and Sa_DbDesign.Comment like concat('%', #{SearchPojo.comment}, '%')
</if>
<if test="SearchPojo.versionnumber != null ">
   and Sa_DbDesign.VersionNumber like concat('%', #{SearchPojo.versionnumber}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DbDesign.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DbDesign.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DbDesign.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DbDesign.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DbDesign.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_DbDesign.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_DbDesign.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_DbDesign.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_DbDesign.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_DbDesign.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DbDesign.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.fnid != null ">
   or Sa_DbDesign.Fnid like concat('%', #{SearchPojo.fnid}, '%')
</if>
<if test="SearchPojo.tablename != null ">
   or Sa_DbDesign.TableName like concat('%', #{SearchPojo.tablename}, '%')
</if>
<if test="SearchPojo.comment != null ">
   or Sa_DbDesign.Comment like concat('%', #{SearchPojo.comment}, '%')
</if>
<if test="SearchPojo.versionnumber != null ">
   or Sa_DbDesign.VersionNumber like concat('%', #{SearchPojo.versionnumber}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DbDesign.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DbDesign.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DbDesign.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DbDesign.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DbDesign.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_DbDesign.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_DbDesign.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_DbDesign.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_DbDesign.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_DbDesign.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DbDesign.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DbDesign(id, Fnid, TableName, Comment, CharacterSet, Collation, VersionNumber, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{fnid}, #{tablename}, #{comment}, #{characterset}, #{collation}, #{versionnumber}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DbDesign
        <set>
            <if test="fnid != null ">
                Fnid =#{fnid},
            </if>
            <if test="tablename != null ">
                TableName =#{tablename},
            </if>
            <if test="comment != null ">
                Comment =#{comment},
            </if>
            <if test="characterset != null ">
                CharacterSet =#{characterset},
            </if>
            <if test="collation != null ">
                Collation =#{collation},
            </if>
            <if test="versionnumber != null ">
                VersionNumber =#{versionnumber},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DbDesign where id = #{key} 
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.pms.domain.pojo.SaDbdesignPojo">
        select
          id
        from Sa_DbDesignItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

