<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaDbdesignitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo">
        <include refid="selectSaDbdesignitemVo"/>
        where Sa_DbDesignItem.id = #{key} 
    </select>
    <sql id="selectSaDbdesignitemVo">
         select
id, Pid, FieldName, FieldType, Comment, PrimaryMark, NotNullMark, CharacterSet, Collation, DefValue, CalcMark, CalcDesc, CalcFormula, IndexMark, SensitiveMark, RowNum, Remark, CreateBy, C<PERSON><PERSON><PERSON>d, <PERSON><PERSON><PERSON><PERSON>, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>antid, TenantName, Revision        from Sa_DbDesignItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo">
        <include refid="selectSaDbdesignitemVo"/>
        where Sa_DbDesignItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaDbdesignitemPojo">
        <include refid="selectSaDbdesignitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DbDesignItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_DbDesignItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.fieldname != null and SearchPojo.fieldname != ''">
   and Sa_DbDesignItem.fieldname like concat('%', #{SearchPojo.fieldname}, '%')
</if>
<if test="SearchPojo.fieldtype != null and SearchPojo.fieldtype != ''">
   and Sa_DbDesignItem.fieldtype like concat('%', #{SearchPojo.fieldtype}, '%')
</if>
<if test="SearchPojo.comment != null and SearchPojo.comment != ''">
   and Sa_DbDesignItem.comment like concat('%', #{SearchPojo.comment}, '%')
</if>
<if test="SearchPojo.characterset != null and SearchPojo.characterset != ''">
   and Sa_DbDesignItem.characterset like concat('%', #{SearchPojo.characterset}, '%')
</if>
<if test="SearchPojo.collation != null and SearchPojo.collation != ''">
   and Sa_DbDesignItem.collation like concat('%', #{SearchPojo.collation}, '%')
</if>
<if test="SearchPojo.defvalue != null and SearchPojo.defvalue != ''">
   and Sa_DbDesignItem.defvalue like concat('%', #{SearchPojo.defvalue}, '%')
</if>
<if test="SearchPojo.calcmark != null and SearchPojo.calcmark != ''">
   and Sa_DbDesignItem.calcmark like concat('%', #{SearchPojo.calcmark}, '%')
</if>
<if test="SearchPojo.calcdesc != null and SearchPojo.calcdesc != ''">
   and Sa_DbDesignItem.calcdesc like concat('%', #{SearchPojo.calcdesc}, '%')
</if>
<if test="SearchPojo.calcformula != null and SearchPojo.calcformula != ''">
   and Sa_DbDesignItem.calcformula like concat('%', #{SearchPojo.calcformula}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_DbDesignItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   and Sa_DbDesignItem.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   and Sa_DbDesignItem.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   and Sa_DbDesignItem.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   and Sa_DbDesignItem.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_DbDesignItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_DbDesignItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_DbDesignItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_DbDesignItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_DbDesignItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   and Sa_DbDesignItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_DbDesignItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.fieldname != null and SearchPojo.fieldname != ''">
   or Sa_DbDesignItem.FieldName like concat('%', #{SearchPojo.fieldname}, '%')
</if>
<if test="SearchPojo.fieldtype != null and SearchPojo.fieldtype != ''">
   or Sa_DbDesignItem.FieldType like concat('%', #{SearchPojo.fieldtype}, '%')
</if>
<if test="SearchPojo.comment != null and SearchPojo.comment != ''">
   or Sa_DbDesignItem.Comment like concat('%', #{SearchPojo.comment}, '%')
</if>
<if test="SearchPojo.characterset != null and SearchPojo.characterset != ''">
   or Sa_DbDesignItem.CharacterSet like concat('%', #{SearchPojo.characterset}, '%')
</if>
<if test="SearchPojo.collation != null and SearchPojo.collation != ''">
   or Sa_DbDesignItem.Collation like concat('%', #{SearchPojo.collation}, '%')
</if>
<if test="SearchPojo.defvalue != null and SearchPojo.defvalue != ''">
   or Sa_DbDesignItem.DefValue like concat('%', #{SearchPojo.defvalue}, '%')
</if>
<if test="SearchPojo.calcmark != null and SearchPojo.calcmark != ''">
   or Sa_DbDesignItem.CalcMark like concat('%', #{SearchPojo.calcmark}, '%')
</if>
<if test="SearchPojo.calcdesc != null and SearchPojo.calcdesc != ''">
   or Sa_DbDesignItem.CalcDesc like concat('%', #{SearchPojo.calcdesc}, '%')
</if>
<if test="SearchPojo.calcformula != null and SearchPojo.calcformula != ''">
   or Sa_DbDesignItem.CalcFormula like concat('%', #{SearchPojo.calcformula}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_DbDesignItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null and SearchPojo.createby != ''">
   or Sa_DbDesignItem.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
   or Sa_DbDesignItem.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null and SearchPojo.lister != ''">
   or Sa_DbDesignItem.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
   or Sa_DbDesignItem.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_DbDesignItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_DbDesignItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_DbDesignItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_DbDesignItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_DbDesignItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
   or Sa_DbDesignItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DbDesignItem(id, Pid, FieldName, FieldType, Comment, PrimaryMark, NotNullMark, CharacterSet, Collation, DefValue, CalcMark, CalcDesc, CalcFormula, IndexMark, SensitiveMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{pid}, #{fieldname}, #{fieldtype}, #{comment}, #{primarymark}, #{notnullmark}, #{characterset}, #{collation}, #{defvalue}, #{calcmark}, #{calcdesc}, #{calcformula}, #{indexmark}, #{sensitivemark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DbDesignItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="fieldname != null ">
                FieldName = #{fieldname},
            </if>
            <if test="fieldtype != null ">
                FieldType = #{fieldtype},
            </if>
            <if test="comment != null ">
                Comment = #{comment},
            </if>
            <if test="primarymark != null">
                PrimaryMark = #{primarymark},
            </if>
            <if test="notnullmark != null">
                NotNullMark = #{notnullmark},
            </if>
            <if test="characterset != null ">
                CharacterSet = #{characterset},
            </if>
            <if test="collation != null ">
                Collation = #{collation},
            </if>
            <if test="defvalue != null ">
                DefValue = #{defvalue},
            </if>
            <if test="calcmark != null ">
                CalcMark = #{calcmark},
            </if>
            <if test="calcdesc != null ">
                CalcDesc = #{calcdesc},
            </if>
            <if test="calcformula != null ">
                CalcFormula = #{calcformula},
            </if>
            <if test="indexmark != null">
                IndexMark = #{indexmark},
            </if>
            <if test="sensitivemark != null">
                SensitiveMark = #{sensitivemark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="createby != null ">
                CreateBy = #{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid = #{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="lister != null ">
                Lister = #{lister},
            </if>
            <if test="listerid != null ">
                Listerid = #{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName = #{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DbDesignItem where id = #{key} 
    </delete>

    <select id="getPid" resultType="java.lang.String">
        select Pid from Sa_DbDesignItem where id = #{key}
    </select>
</mapper>

