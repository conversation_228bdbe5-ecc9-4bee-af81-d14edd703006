<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaDefectreportMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaDefectreportPojo">
        select
        id, RefNo, SoftwareName, SoftwareVersion, DiscoveryDate, Tester, Description, Attachment, DefectType, Severity,
        Priority, TestEnvironment, ReproductionSteps, Remark, CreateByid, CreateBy, CreateDate, Listerid, Lister,
        ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision from Sa_DefectReport
        where Sa_DefectReport.id = #{key}
    </select>
    <sql id="selectSaDefectreportVo">
        select id,
               RefNo,
               SoftwareName,
               SoftwareVersion,
               DiscoveryDate,
               Tester,
               Description,
               Attachment,
               DefectType,
               Severity,
               Priority,
               TestEnvironment,
               ReproductionSteps,
               Remark,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Revision
        from Sa_DefectReport
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaDefectreportPojo">
        <include refid="selectSaDefectreportVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_DefectReport.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Sa_DefectReport.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.softwarename != null">
            and Sa_DefectReport.SoftwareName like concat('%', #{SearchPojo.softwarename}, '%')
        </if>
        <if test="SearchPojo.softwareversion != null">
            and Sa_DefectReport.SoftwareVersion like concat('%', #{SearchPojo.softwareversion}, '%')
        </if>
        <if test="SearchPojo.tester != null">
            and Sa_DefectReport.Tester like concat('%', #{SearchPojo.tester}, '%')
        </if>
        <if test="SearchPojo.description != null">
            and Sa_DefectReport.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.attachment != null">
            and Sa_DefectReport.Attachment like concat('%', #{SearchPojo.attachment}, '%')
        </if>
        <if test="SearchPojo.defecttype != null">
            and Sa_DefectReport.DefectType like concat('%', #{SearchPojo.defecttype}, '%')
        </if>
        <if test="SearchPojo.severity != null">
            and Sa_DefectReport.Severity like concat('%', #{SearchPojo.severity}, '%')
        </if>
        <if test="SearchPojo.priority != null">
            and Sa_DefectReport.Priority like concat('%', #{SearchPojo.priority}, '%')
        </if>
        <if test="SearchPojo.testenvironment != null">
            and Sa_DefectReport.TestEnvironment like concat('%', #{SearchPojo.testenvironment}, '%')
        </if>
        <if test="SearchPojo.reproductionsteps != null">
            and Sa_DefectReport.ReproductionSteps like concat('%', #{SearchPojo.reproductionsteps}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_DefectReport.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_DefectReport.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_DefectReport.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_DefectReport.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_DefectReport.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_DefectReport.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_DefectReport.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_DefectReport.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_DefectReport.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_DefectReport.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Sa_DefectReport.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.softwarename != null">
                or Sa_DefectReport.SoftwareName like concat('%', #{SearchPojo.softwarename}, '%')
            </if>
            <if test="SearchPojo.softwareversion != null">
                or Sa_DefectReport.SoftwareVersion like concat('%', #{SearchPojo.softwareversion}, '%')
            </if>
            <if test="SearchPojo.tester != null">
                or Sa_DefectReport.Tester like concat('%', #{SearchPojo.tester}, '%')
            </if>
            <if test="SearchPojo.description != null">
                or Sa_DefectReport.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.attachment != null">
                or Sa_DefectReport.Attachment like concat('%', #{SearchPojo.attachment}, '%')
            </if>
            <if test="SearchPojo.defecttype != null">
                or Sa_DefectReport.DefectType like concat('%', #{SearchPojo.defecttype}, '%')
            </if>
            <if test="SearchPojo.severity != null">
                or Sa_DefectReport.Severity like concat('%', #{SearchPojo.severity}, '%')
            </if>
            <if test="SearchPojo.priority != null">
                or Sa_DefectReport.Priority like concat('%', #{SearchPojo.priority}, '%')
            </if>
            <if test="SearchPojo.testenvironment != null">
                or Sa_DefectReport.TestEnvironment like concat('%', #{SearchPojo.testenvironment}, '%')
            </if>
            <if test="SearchPojo.reproductionsteps != null">
                or Sa_DefectReport.ReproductionSteps like concat('%', #{SearchPojo.reproductionsteps}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_DefectReport.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_DefectReport.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_DefectReport.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_DefectReport.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_DefectReport.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_DefectReport.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_DefectReport.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_DefectReport.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_DefectReport.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_DefectReport.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_DefectReport(id, RefNo, SoftwareName, SoftwareVersion, DiscoveryDate, Tester, Description,
        Attachment, DefectType, Severity, Priority, TestEnvironment, ReproductionSteps, Remark, CreateByid, CreateBy,
        CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{refno}, #{softwarename}, #{softwareversion}, #{discoverydate}, #{tester}, #{description},
        #{attachment}, #{defecttype}, #{severity}, #{priority}, #{testenvironment}, #{reproductionsteps}, #{remark},
        #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2},
        #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DefectReport
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="softwarename != null">
                SoftwareName =#{softwarename},
            </if>
            <if test="softwareversion != null">
                SoftwareVersion =#{softwareversion},
            </if>
            <if test="discoverydate != null">
                DiscoveryDate =#{discoverydate},
            </if>
            <if test="tester != null">
                Tester =#{tester},
            </if>
            <if test="description != null">
                Description =#{description},
            </if>
            <if test="attachment != null">
                Attachment =#{attachment},
            </if>
            <if test="defecttype != null">
                DefectType =#{defecttype},
            </if>
            <if test="severity != null">
                Severity =#{severity},
            </if>
            <if test="priority != null">
                Priority =#{priority},
            </if>
            <if test="testenvironment != null">
                TestEnvironment =#{testenvironment},
            </if>
            <if test="reproductionsteps != null">
                ReproductionSteps =#{reproductionsteps},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DefectReport where id = #{key}
    </delete>
</mapper>

