<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaDemandMapper">
    <!--查询单个-->
    <!--注意需求的CreateByid存的是loginuser.userid createengineername是CreateByid关联查询到的工程师名字-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaDemandPojo">
        select Sa_Demand.id,
        Sa_Demand.Parentid,
        Sa_Demand.RefNo,
        Sa_Demand.BillDate,
        Sa_Demand.BillType,
        Sa_Demand.BillTitle,
        Sa_Demand.Groupid,
        Sa_Demand.Todoid,
        Sa_Demand.Projectid,
        Sa_Demand.ItemCode,
        Sa_Demand.ItemName,
        Sa_Demand.Description,
        Sa_Demand.<PERSON><PERSON><PERSON>,
        Sa_Demand.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
        Sa_Demand.Status,
        Sa_Demand.DemandStatus,
        Sa_Demand.TimeStatus,
        Sa_Demand.StartDate,
        Sa_Demand.DeadDate,
        Sa_Demand.FinishDate,
        Sa_Demand.WorkTime,
        Sa_Demand.DemandSubmitid,
        Sa_Demand.Appointeeid,
        Sa_Demand.Appointee,
        Sa_Demand.Operator,
        Sa_Demand.Operatorid,
        Sa_Demand.Collaborators,
        Sa_Demand.Collaboratorids,
        Sa_Demand.Longitude,
        Sa_Demand.Latitude,
        Sa_Demand.Location,
        Sa_Demand.Phone,
        Sa_Demand.Level,
        Sa_Demand.WeekMark,
        Sa_Demand.Closer,
        Sa_Demand.Closerid,
        Sa_Demand.ProcessComment,
        Sa_Demand.Praise,
        Sa_Demand.PictureUrl1,
        Sa_Demand.PictureUrl2,
        Sa_Demand.PictureUrl3,
        Sa_Demand.PictureUrl4,
        Sa_Demand.PictureUrl5,
        Sa_Demand.FinPictureUrl1,
        Sa_Demand.FinPictureUrl2,
        Sa_Demand.FinPictureUrl3,
        Sa_Demand.FinPictureUrl4,
        Sa_Demand.FinPictureUrl5,
        Sa_Demand.FinishDes,
        Sa_Demand.Remark,
        Sa_Demand.DisannulMark,
        Sa_Demand.FinishMark,
        Sa_Demand.CreateByid,
        Sa_Demand.CreateBy,
        Sa_Demand.CreateDate,
        Sa_Demand.Lister,
        Sa_Demand.Listerid,
        Sa_Demand.ModifyDate,
        Sa_Demand.StatusModifyDate,
        Sa_Demand.EngineerGroupid,
        Sa_Demand.Custom1,
        Sa_Demand.Custom2,
        Sa_Demand.Custom3,
        Sa_Demand.Custom4,
        Sa_Demand.Custom5,
        Sa_Demand.Custom6,
        Sa_Demand.Custom7,
        Sa_Demand.Custom8,
        Sa_Demand.Custom9,
        Sa_Demand.Custom10,
        Sa_Demand.Deptid,
        Sa_Demand.Tenantid,
        Sa_Demand.TenantName,
        Sa_Demand.Revision,
        Sa_Workgroup.GroupName,
        Sa_Workgroup.Abbreviate,
        Sa_Workgroup.GroupUid,
        Sa_Project.ProjName
        from Sa_Demand
        left join inkspms.Sa_Workgroup on Sa_Demand.Groupid = Sa_Workgroup.id
        left join Sa_Project on Sa_Demand.Projectid = Sa_Project.id
        where Sa_Demand.id = #{key}
    </select>

    <!--注意需求的CreateByid存的是loginuser.userid createengineername是CreateByid关联查询到的工程师名字-->
    <sql id="selectSaDemandVo">
        select Sa_Demand.id,
               Sa_Demand.Parentid,
               Sa_Demand.RefNo,
               Sa_Demand.BillDate,
               Sa_Demand.BillType,
               Sa_Demand.BillTitle,
               Sa_Demand.Groupid,
               Sa_Demand.Todoid,
               Sa_Demand.Level,
               Sa_Demand.WeekMark,
               Sa_Demand.Projectid,
               Sa_Demand.ItemCode,
               Sa_Demand.ItemName,
               Sa_Demand.Description,
               Sa_Demand.LabelJson,
               Sa_Demand.DemandLabelJson,
               Sa_Demand.Status,
               Sa_Demand.DemandStatus,
               Sa_Demand.TimeStatus,
               Sa_Demand.StartDate,
               Sa_Demand.DeadDate,
               Sa_Demand.FinishDate,
               Sa_Demand.WorkTime,
               Sa_Demand.DemandSubmitid,
               Sa_Demand.Appointeeid,
               Sa_Demand.Appointee,
               Sa_Demand.Operator,
               Sa_Demand.Operatorid,
               Sa_Demand.Collaborators,
               Sa_Demand.Collaboratorids,
               Sa_Demand.Longitude,
               Sa_Demand.Latitude,
               Sa_Demand.Location,
               Sa_Demand.Phone,
               Sa_Demand.Closer,
               Sa_Demand.Closerid,
               Sa_Demand.Praise,
               Sa_Demand.ProcessComment,
               Sa_Demand.PictureUrl1,
               Sa_Demand.PictureUrl2,
               Sa_Demand.PictureUrl3,
               Sa_Demand.PictureUrl4,
               Sa_Demand.PictureUrl5,
               Sa_Demand.FinPictureUrl1,
               Sa_Demand.FinPictureUrl2,
               Sa_Demand.FinPictureUrl3,
               Sa_Demand.FinPictureUrl4,
               Sa_Demand.FinPictureUrl5,
               Sa_Demand.FinishDes,
               Sa_Demand.Remark,
               Sa_Demand.DisannulMark,
               Sa_Demand.FinishMark,
               Sa_Demand.CreateBy,
               Sa_Demand.CreateByid,
               Sa_Demand.CreateDate,
               Sa_Demand.Lister,
               Sa_Demand.Listerid,
               Sa_Demand.ModifyDate,
               Sa_Demand.StatusModifyDate,
               Sa_Demand.EngineerGroupid,
               Sa_Demand.Custom1,
               Sa_Demand.Custom2,
               Sa_Demand.Custom3,
               Sa_Demand.Custom4,
               Sa_Demand.Custom5,
               Sa_Demand.Custom6,
               Sa_Demand.Custom7,
               Sa_Demand.Custom8,
               Sa_Demand.Custom9,
               Sa_Demand.Custom10,
               Sa_Demand.Deptid,
               Sa_Demand.Tenantid,
               Sa_Demand.TenantName,
               Sa_Demand.Revision,
               Sa_Workgroup.GroupName,
               Sa_Workgroup.Abbreviate,
               Sa_Workgroup.GroupUid
        from Sa_Demand
                 left join Sa_Workgroup on Sa_Demand.Groupid = Sa_Workgroup.id
    </sql>


    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaDemandPojo">
        <include refid="selectSaDemandVo"/>
        WHERE 1=1
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_Demand.CreateDate BETWEEN
                    #{queryParam.DateRange.StartDate}
                    and
                    #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn}
                    BETWEEN
                    #{queryParam.DateRange.StartDate}
                    and
                    #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${queryParam.orderBy}
        <if test="queryParam.OrderType == 0">
            asc
        </if>
        <if test="queryParam.OrderType == 1">
            desc
        </if>
    </select>


    <select id="getAllPageList" resultType="inks.service.sa.pms.domain.pojo.SaDemandPojo">
        <include refid="selectSaDemandVo"/>
        WHERE 1=1
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_Demand.CreateDate BETWEEN
                #{queryParam.DateRange.StartDate}
                and
                #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn}
                BETWEEN
                #{queryParam.DateRange.StartDate}
                and
                #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${queryParam.orderBy}
        <if test="queryParam.OrderType == 0">
            asc
        </if>
        <if test="queryParam.OrderType == 1">
            desc
        </if>
    </select>


    <!--查询指定行数据-->
    <select id="getPageListUNIONALL" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaDemandPojo">
        SELECT * FROM (
        (SELECT Sa_Demand.*,
                e1.EngineerName AS appointeename,
                e2.EngineerName AS createengineername
         FROM Sa_Demand
                  LEFT JOIN Sa_Engineer e1 ON Sa_Demand.Appointeeid = e1.id
                  LEFT JOIN Sa_Engineer e2 ON Sa_Demand.CreateByid = e2.Userid
        WHERE Sa_Demand.Status NOT IN (SELECT id
                                       FROM Sa_ProjectStatus
                                       WHERE StatusType = '已完成'
                                         AND FinishMark = 1)
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_Demand.CreateDate BETWEEN
                    #{queryParam.DateRange.StartDate}
                    and
                    #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn}
                    BETWEEN
                    #{queryParam.DateRange.StartDate}
                    and
                    #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${queryParam.orderBy}
        <if test="queryParam.OrderType == 0">
            asc
        </if>
        <if test="queryParam.OrderType == 1">
            desc
        </if>
        )
        UNION ALL(
        SELECT Sa_Demand.*,
               e1.EngineerName AS appointeename,
               e2.EngineerName AS createengineername
        FROM Sa_Demand
                 LEFT JOIN Sa_Engineer e1 ON Sa_Demand.Appointeeid = e1.id
                 LEFT JOIN Sa_Engineer e2 ON Sa_Demand.CreateByid = e2.Userid
        WHERE Sa_Demand.Status IN (SELECT id
                                   FROM Sa_ProjectStatus
                                   WHERE StatusType = '已完成'
                                     AND FinishMark = 1)
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}

        </if>
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_Demand.CreateDate BETWEEN
                    #{queryParam.DateRange.StartDate}
                    and
                    #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn}
                    BETWEEN
                    #{queryParam.DateRange.StartDate}
                    and
                    #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${queryParam.orderBy}
        <if test="queryParam.OrderType == 0">
            asc
        </if>
        <if test="queryParam.OrderType == 1">
            desc
        </if>
        <if test="finishlimit != null">
            limit #{finishlimit}
        </if>
        )
        ) AS result
        ORDER BY statusmodifydate DESC
    </select>

    <select id="getTodoPageList" resultType="inks.service.sa.pms.domain.pojo.SaDemandPojo">
        SELECT Sa_Demand.*,
        e1.EngineerName AS appointeename,
        e2.EngineerName AS createengineername
        FROM Sa_Demand
        LEFT JOIN Sa_Engineer e1 ON Sa_Demand.Appointeeid = e1.id
        LEFT JOIN Sa_Engineer e2 ON Sa_Demand.CreateByid = e2.Userid
        WHERE 1=1
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_Demand.StartDate BETWEEN
                #{queryParam.DateRange.StartDate}
                and
                #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn}
                BETWEEN
                #{queryParam.DateRange.StartDate}
                and
                #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${queryParam.orderBy}
        <if test="queryParam.OrderType == 0">
            asc
        </if>
        <if test="queryParam.OrderType == 1">
            desc
        </if>
    </select>


    <select id="getCalendarPageList" resultType="java.util.Map">
        SELECT Sa_Demand.id,
        Sa_Demand.BillTitle as billtitle,
        Sa_Demand.StartDate as startdate,
        Sa_Demand.DeadDate as deaddate,
        Sa_Demand.CreateBy as createby,
        Sa_Demand.Status as status,
        Sa_Demand.Projectid as projectid,
        Sa_Demand.Description as description,
        Sa_Demand.WorkTime as worktime,
        Sa_Engineer.EngineerName as appointeename,
        Sa_ProjectStatus.StatusType as statustype,
        Sa_ProjectStatus.FinishMark as finishmark,
        Sa_Project.ProjName as projname
        FROM Sa_Demand
        Left join Sa_ProjectStatus on Sa_Demand.Status = Sa_ProjectStatus.id
        Left join Sa_Engineer on Sa_Demand.Appointeeid = Sa_Engineer.id
        Left join Sa_Project on Sa_Demand.Projectid = Sa_Project.id
        WHERE 1 = 1
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_Demand.StartDate BETWEEN #{queryParam.DateRange.StartDate}
                and #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn}
                BETWEEN #{queryParam.DateRange.StartDate}
                and #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${queryParam.orderBy}
        <if test="queryParam.OrderType == 0">
            asc
        </if>
        <if test="queryParam.OrderType == 1">
            desc
        </if>
    </select>


    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Sa_Demand.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Sa_Demand.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Sa_Demand.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.groupid != null">
            and Sa_Demand.Groupid like concat('%', #{SearchPojo.groupid}, '%')
        </if>
        <if test="SearchPojo.todoid != null">
            and Sa_Demand.Todoid like concat('%', #{SearchPojo.todoid}, '%')
        </if>
        <if test="SearchPojo.projectid != null">
            and Sa_Demand.Projectid like concat('%', #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null">
            and Sa_Demand.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Sa_Demand.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.description != null">
            and Sa_Demand.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.labeljson != null">
            and Sa_Demand.LabelJson like concat('%', #{SearchPojo.labeljson}, '%')
        </if>
        <if test="SearchPojo.status != null">
            and Sa_Demand.Status like concat('%', #{SearchPojo.status}, '%')
        </if>
        <if test="SearchPojo.appointeeid != null">
            and Sa_Demand.Appointeeid like concat('%', #{SearchPojo.appointeeid}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_Demand.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Sa_Demand.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.longitude != null">
            and Sa_Demand.Longitude like concat('%', #{SearchPojo.longitude}, '%')
        </if>
        <if test="SearchPojo.latitude != null">
            and Sa_Demand.Latitude like concat('%', #{SearchPojo.latitude}, '%')
        </if>
        <if test="SearchPojo.location != null">
            and Sa_Demand.Location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.phone != null">
            and Sa_Demand.Phone like concat('%', #{SearchPojo.phone}, '%')
        </if>
        <if test="SearchPojo.closer != null">
            and Sa_Demand.Closer like concat('%', #{SearchPojo.closer}, '%')
        </if>
        <if test="SearchPojo.closerid != null">
            and Sa_Demand.Closerid like concat('%', #{SearchPojo.closerid}, '%')
        </if>
        <if test="SearchPojo.processcomment != null">
            and Sa_Demand.ProcessComment like concat('%', #{SearchPojo.processcomment}, '%')
        </if>
        <if test="SearchPojo.pictureurl1 != null">
            and Sa_Demand.PictureUrl1 like concat('%', #{SearchPojo.pictureurl1}, '%')
        </if>
        <if test="SearchPojo.pictureurl2 != null">
            and Sa_Demand.PictureUrl2 like concat('%', #{SearchPojo.pictureurl2}, '%')
        </if>
        <if test="SearchPojo.pictureurl3 != null">
            and Sa_Demand.PictureUrl3 like concat('%', #{SearchPojo.pictureurl3}, '%')
        </if>
        <if test="SearchPojo.pictureurl4 != null">
            and Sa_Demand.PictureUrl4 like concat('%', #{SearchPojo.pictureurl4}, '%')
        </if>
        <if test="SearchPojo.pictureurl5 != null">
            and Sa_Demand.PictureUrl5 like concat('%', #{SearchPojo.pictureurl5}, '%')
        </if>
        <if test="SearchPojo.finpictureurl1 != null">
            and Sa_Demand.FinPictureUrl1 like concat('%', #{SearchPojo.finpictureurl1}, '%')
        </if>
        <if test="SearchPojo.finpictureurl2 != null">
            and Sa_Demand.FinPictureUrl2 like concat('%', #{SearchPojo.finpictureurl2}, '%')
        </if>
        <if test="SearchPojo.finpictureurl3 != null">
            and Sa_Demand.FinPictureUrl3 like concat('%', #{SearchPojo.finpictureurl3}, '%')
        </if>
        <if test="SearchPojo.finpictureurl4 != null">
            and Sa_Demand.FinPictureUrl4 like concat('%', #{SearchPojo.finpictureurl4}, '%')
        </if>
        <if test="SearchPojo.finpictureurl5 != null">
            and Sa_Demand.FinPictureUrl5 like concat('%', #{SearchPojo.finpictureurl5}, '%')
        </if>
        <if test="SearchPojo.finishdes != null">
            and Sa_Demand.FinishDes like concat('%', #{SearchPojo.finishdes}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Demand.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Demand.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Demand.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Demand.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Demand.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Demand.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Demand.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Demand.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Demand.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Demand.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Demand.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Demand.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Demand.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Demand.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Demand.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_Demand.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Demand.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Sa_Demand.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Sa_Demand.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Sa_Demand.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.groupid != null">
                or Sa_Demand.Groupid like concat('%', #{SearchPojo.groupid}, '%')
            </if>
            <if test="SearchPojo.todoid != null">
                or Sa_Demand.Todoid like concat('%', #{SearchPojo.todoid}, '%')
            </if>
            <if test="SearchPojo.projectid != null">
                or Sa_Demand.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null">
                or Sa_Demand.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Sa_Demand.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.description != null">
                or Sa_Demand.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.labeljson != null">
                or Sa_Demand.LabelJson like concat('%', #{SearchPojo.labeljson}, '%')
            </if>
            <if test="SearchPojo.status != null">
                or Sa_Demand.Status like concat('%', #{SearchPojo.status}, '%')
            </if>
            <if test="SearchPojo.appointeeid != null">
                or Sa_Demand.Appointeeid like concat('%', #{SearchPojo.appointeeid}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_Demand.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Sa_Demand.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.longitude != null">
                or Sa_Demand.Longitude like concat('%', #{SearchPojo.longitude}, '%')
            </if>
            <if test="SearchPojo.latitude != null">
                or Sa_Demand.Latitude like concat('%', #{SearchPojo.latitude}, '%')
            </if>
            <if test="SearchPojo.location != null">
                or Sa_Demand.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.phone != null">
                or Sa_Demand.Phone like concat('%', #{SearchPojo.phone}, '%')
            </if>
            <if test="SearchPojo.closer != null">
                or Sa_Demand.Closer like concat('%', #{SearchPojo.closer}, '%')
            </if>
            <if test="SearchPojo.closerid != null">
                or Sa_Demand.Closerid like concat('%', #{SearchPojo.closerid}, '%')
            </if>
            <if test="SearchPojo.processcomment != null">
                or Sa_Demand.ProcessComment like concat('%', #{SearchPojo.processcomment}, '%')
            </if>
            <if test="SearchPojo.pictureurl1 != null">
                or Sa_Demand.PictureUrl1 like concat('%', #{SearchPojo.pictureurl1}, '%')
            </if>
            <if test="SearchPojo.pictureurl2 != null">
                or Sa_Demand.PictureUrl2 like concat('%', #{SearchPojo.pictureurl2}, '%')
            </if>
            <if test="SearchPojo.pictureurl3 != null">
                or Sa_Demand.PictureUrl3 like concat('%', #{SearchPojo.pictureurl3}, '%')
            </if>
            <if test="SearchPojo.pictureurl4 != null">
                or Sa_Demand.PictureUrl4 like concat('%', #{SearchPojo.pictureurl4}, '%')
            </if>
            <if test="SearchPojo.pictureurl5 != null">
                or Sa_Demand.PictureUrl5 like concat('%', #{SearchPojo.pictureurl5}, '%')
            </if>
            <if test="SearchPojo.finpictureurl1 != null">
                or Sa_Demand.FinPictureUrl1 like concat('%', #{SearchPojo.finpictureurl1}, '%')
            </if>
            <if test="SearchPojo.finpictureurl2 != null">
                or Sa_Demand.FinPictureUrl2 like concat('%', #{SearchPojo.finpictureurl2}, '%')
            </if>
            <if test="SearchPojo.finpictureurl3 != null">
                or Sa_Demand.FinPictureUrl3 like concat('%', #{SearchPojo.finpictureurl3}, '%')
            </if>
            <if test="SearchPojo.finpictureurl4 != null">
                or Sa_Demand.FinPictureUrl4 like concat('%', #{SearchPojo.finpictureurl4}, '%')
            </if>
            <if test="SearchPojo.finpictureurl5 != null">
                or Sa_Demand.FinPictureUrl5 like concat('%', #{SearchPojo.finpictureurl5}, '%')
            </if>
            <if test="SearchPojo.finishdes != null">
                or Sa_Demand.FinishDes like concat('%', #{SearchPojo.finishdes}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Demand.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Demand.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Demand.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Demand.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Demand.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Demand.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Demand.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Demand.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Demand.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Demand.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Demand.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Demand.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Demand.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Demand.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Demand.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_Demand.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Demand.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Demand(id, Parentid, RefNo, BillDate, BillType, BillTitle, Groupid, Todoid, Level, WeekMark, Projectid, ItemCode, ItemName, Description, LabelJson, DemandLabelJson, Status, DemandStatus, TimeStatus, StartDate, DeadDate, FinishDate, WorkTime, DemandSubmitid, Appointeeid, Appointee, Operator, Operatorid, Collaboratorids, Collaborators, Longitude, Latitude, Location, Phone, Closer, Closerid, Praise, ProcessComment, PictureUrl1, PictureUrl2, PictureUrl3, PictureUrl4, PictureUrl5, FinPictureUrl1, FinPictureUrl2, FinPictureUrl3, FinPictureUrl4, FinPictureUrl5, FinishDes, Remark, DisannulMark, FinishMark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, StatusModifyDate, EngineerGroupid, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{parentid}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{groupid}, #{todoid}, #{level}, #{weekmark}, #{projectid}, #{itemcode}, #{itemname}, #{description}, #{labeljson}, #{demandlabeljson}, #{status}, #{demandstatus}, #{timestatus}, #{startdate}, #{deaddate}, #{finishdate}, #{worktime}, #{demandsubmitid}, #{appointeeid}, #{appointee}, #{operator}, #{operatorid}, #{collaboratorids}, #{collaborators}, #{longitude}, #{latitude}, #{location}, #{phone}, #{closer}, #{closerid}, #{praise}, #{processcomment}, #{pictureurl1}, #{pictureurl2}, #{pictureurl3}, #{pictureurl4}, #{pictureurl5}, #{finpictureurl1}, #{finpictureurl2}, #{finpictureurl3}, #{finpictureurl4}, #{finpictureurl5}, #{finishdes}, #{remark}, #{disannulmark}, #{finishmark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{statusmodifydate}, #{engineergroupid}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Demand
        <set>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="groupid != null">
                Groupid =#{groupid},
            </if>
            <if test="todoid != null">
                Todoid =#{todoid},
            </if>
            <if test="level != null">
                Level =#{level},
            </if>
            <if test="weekmark != null">
                WeekMark =#{weekmark},
            </if>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="itemcode != null">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null">
                ItemName =#{itemname},
            </if>
            <if test="description != null">
                Description =#{description},
            </if>
            <if test="labeljson != null">
                LabelJson =#{labeljson},
            </if>
            <if test="demandlabeljson != null ">
                DemandLabelJson =#{demandlabeljson},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="demandstatus != null ">
                DemandStatus =#{demandstatus},
            </if>
            <if test="timestatus != null ">
                TimeStatus =#{timestatus},
            </if>
            <if test="startdate != null">
                StartDate =#{startdate},
            </if>
            <if test="deaddate != null">
                DeadDate =#{deaddate},
            </if>
            <if test="finishdate != null">
                FinishDate =#{finishdate},
            </if>
            <if test="worktime != null">
                WorkTime =#{worktime},
            </if>
            <if test="demandsubmitid != null ">
                DemandSubmitid =#{demandsubmitid},
            </if>
            <if test="appointeeid != null">
                Appointeeid =#{appointeeid},
            </if>
            <if test="appointee != null ">
                Appointee =#{appointee},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="collaboratorids != null ">
                Collaboratorids =#{collaboratorids},
            </if>
            <if test="collaborators != null ">
                Collaborators =#{collaborators},
            </if>
            <if test="longitude != null">
                Longitude =#{longitude},
            </if>
            <if test="latitude != null">
                Latitude =#{latitude},
            </if>
            <if test="location != null">
                Location =#{location},
            </if>
            <if test="phone != null">
                Phone =#{phone},
            </if>
            <if test="closer != null">
                Closer =#{closer},
            </if>
            <if test="closerid != null">
                Closerid =#{closerid},
            </if>
            <if test="praise != null">
                Praise =#{praise},
            </if>
            <if test="processcomment != null">
                ProcessComment =#{processcomment},
            </if>
            <if test="pictureurl1 != null">
                PictureUrl1 =#{pictureurl1},
            </if>
            <if test="pictureurl2 != null">
                PictureUrl2 =#{pictureurl2},
            </if>
            <if test="pictureurl3 != null">
                PictureUrl3 =#{pictureurl3},
            </if>
            <if test="pictureurl4 != null">
                PictureUrl4 =#{pictureurl4},
            </if>
            <if test="pictureurl5 != null">
                PictureUrl5 =#{pictureurl5},
            </if>
            <if test="finpictureurl1 != null">
                FinPictureUrl1 =#{finpictureurl1},
            </if>
            <if test="finpictureurl2 != null">
                FinPictureUrl2 =#{finpictureurl2},
            </if>
            <if test="finpictureurl3 != null">
                FinPictureUrl3 =#{finpictureurl3},
            </if>
            <if test="finpictureurl4 != null">
                FinPictureUrl4 =#{finpictureurl4},
            </if>
            <if test="finpictureurl5 != null">
                FinPictureUrl5 =#{finpictureurl5},
            </if>
            <if test="finishdes != null">
                FinishDes =#{finishdes},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="statusmodifydate != null">
                StatusModifyDate =#{statusmodifydate},
            </if>
            <if test="engineergroupid != null ">
                EngineerGroupid =#{engineergroupid},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Demand where id = #{key}
    </delete>
    <!-- 下面4个SQL:   今日、本周、本月的需求单总数&完成数&未完成数&耗费工时-->
    <select id="allCountInTime" resultType="int">
        select count(*) as count
        from Sa_Demand
        where Sa_Demand.DeadDate BETWEEN #{startdate} and #{enddate}
    </select>

    <select id="finishCountInTime" resultType="int">
        select count(*) as count
        from Sa_Demand
        where Sa_Demand.FinishMark = 1
        and Sa_Demand.DeadDate BETWEEN #{startdate} and #{enddate}
    </select>

    <select id="unFinishCountInTime" resultType="int">
        select count(*) as count
        from Sa_Demand
        where Sa_Demand.FinishMark = 0
        and Sa_Demand.DeadDate BETWEEN #{startdate} and #{enddate}
    </select>

    <select id="workTimeInTime" resultType="double">
        select COALESCE(SUM(WorkTime), 0) as worktime
        from Sa_Demand
        where Sa_Demand.DeadDate BETWEEN #{startdate} and #{enddate}
    </select>


    <select id="getOnlineCount" resultType="int">
        select count(1) as count
        from Sa_Demand
        where Sa_Demand.FinishMark = 0
    </select>


    <update id="updateStatusModifyDateAndFinishMark">
        update Sa_Demand
        set StatusModifyDate = #{date},FinishMark = #{finishMark}
        where id = #{id}
    </update>

    <update id="cleanDeadDate">
        update Sa_Demand
        set DeadDate = null
        where id = #{id}
    </update>

    <update id="cleanStartdate">
        update Sa_Demand
        set StartDate = null
        where id = #{id}
    </update>

    <select id="getCountTodoAndOverdue" resultType="java.util.Map">
        SELECT IFNULL(SUM(IF((SELECT StatusType FROM Sa_ProjectStatus WHERE id = Sa_Demand.Status) = '开始', 1, 0)),
        0) AS TodoCount,
        IFNULL(SUM(IF((SELECT StatusType FROM Sa_ProjectStatus WHERE id = Sa_Demand.Status) != '已完成' AND
        NOW() > DeadDate, 1, 0)), 0) AS OverdueCount
        FROM Sa_Demand
        where Projectid = #{id}
        <if test="projectFilter != null">
            ${projectFilter}
        </if>
    </select>

    <select id="getListByParentid" resultType="inks.service.sa.pms.domain.pojo.SaDemandPojo">
        select Sa_Demand.*,
        e1.EngineerName as appointeename,
        e2.EngineerName as createengineername
        from Sa_Demand
        left join Sa_Engineer e1 on Sa_Demand.Appointeeid = e1.id
        left join Sa_Engineer e2 on Sa_Demand.CreateByid = e2.Userid
        where Parentid = #{key}
    </select>

    <select id="getTitle" resultType="java.lang.String">
        select BillTitle
        from Sa_Demand
        where id = #{key}
    </select>

    <select id="getAppointeeid" resultType="java.lang.String">
        select Appointeeid
        from Sa_Demand
        where id = #{key}
    </select>

    <select id="getFinishDemandQtyGroupByUser" resultType="java.util.HashMap">
        select count(*) as value,Sa_Engineer.EngineerName as name,Sa_Engineer.id as engineerid
        from Sa_Demand join Sa_Engineer on Sa_Demand.Appointeeid = Sa_Engineer.id
        where FinishMark = 1 and Sa_Demand.Appointeeid is not null
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_Demand.FinishDate BETWEEN
                #{queryParam.DateRange.StartDate}
                and
                #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn}
                BETWEEN
                #{queryParam.DateRange.StartDate}
                and
                #{queryParam.DateRange.EndDate}
            </if>
        </if>
        group by Sa_Engineer.id,Sa_Engineer.EngineerName
    </select>

    <select id="toBeDealtCount" resultType="double">
        select count(*) as count
        from Sa_Demand
                 join Sa_ProjectStatus on Sa_Demand.Status = Sa_ProjectStatus.id
        where Sa_ProjectStatus.StatusType = '开始'
    </select>

    <select id="onlineCount" resultType="int">
        select count(*) as count
        from Sa_Demand
                 join Sa_ProjectStatus on Sa_Demand.Status = Sa_ProjectStatus.id
        where Sa_ProjectStatus.StatusType != '已完成';
    </select>

    <select id="checkTitleExist" resultType="int">
        select count(*)
        from Sa_Demand
        where BillTitle = #{commitTitle}
        and DATE(CreateDate) = DATE(NOW())
    </select>

    <select id="checkTitleWithRemark" resultType="int">
        select count(*)
        from Sa_Demand
        where BillTitle = #{commitTitle}
        and Remark = #{remark}
    </select>

    <select id="getOnlineStatus" resultType="java.util.Map">
        SELECT e.EngineerName                                         AS engineername,
               SUM(IF(d.FinishMark = 0, 1, 0))                        AS onlinecount,
               SUM(IF(CURRENT_DATE > d.DeadDate, 1, 0))               AS overduecount,
               SUM(IF(d.FinishMark = 1 AND MONTH(d.CreateDate) = MONTH(CURRENT_DATE) AND
                      YEAR(d.CreateDate) = YEAR(CURRENT_DATE), 1, 0)) AS completedmonthlycount
        FROM Sa_Engineer e
                 LEFT JOIN Sa_Demand d ON d.Appointeeid = e.id AND d.CreateDate > '2024-06-01 17:30:00'
        GROUP BY e.EngineerName

        UNION ALL
        SELECT '其他'                                                 AS engineername,
               SUM(IF(d.FinishMark = 0, 1, 0))                        AS onlinecount,
               SUM(IF(CURRENT_DATE > d.DeadDate, 1, 0))               AS overduecount,
               SUM(IF(d.FinishMark = 1 AND MONTH(d.CreateDate) = MONTH(CURRENT_DATE) AND
                      YEAR(d.CreateDate) = YEAR(CURRENT_DATE), 1, 0)) AS completedmonthlycount
        FROM Sa_Demand d
        WHERE d.Appointeeid = ''
          AND d.CreateDate > '2024-06-01 17:30:00'
    </select>

    <select id="getFinishMark" resultType="java.lang.Integer">
        select FinishMark
        from Sa_Demand
        where id = #{id}
    </select>

    <update id="syncSubmitDemandMark">
        update Sa_DemandSubmit
        set DemandMark = #{demandmark}
        where id = #{demandsubmitid}
    </update>

    <select id="newCountToday" resultType="java.lang.Integer">
        select count(*)
        from Sa_Demand
        where DATE(CreateDate) = DATE(NOW())
    </select>

<update id="syncWorkTimeByDemandTime">
    update Sa_Demand
    set WorkTime = (SELECT SUM(Sa_DemandTime.WorkHours) FROM Sa_DemandTime WHERE Sa_DemandTime.Demandid = Sa_Demand.id),
    TimeStatus = #{timestatus}
    where Sa_Demand.id = #{demandid}
</update>

    <select id="getCountByBillType" resultType="java.util.Map">
        SELECT COUNT(1) AS count, billtype
        FROM Sa_Demand
        WHERE 1 = 1 ${qpfilter}
        GROUP BY BillType
    </select>
</mapper>

