<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaDemandsubmitMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaDemandsubmitPojo">
        <include refid="selectSaDemandsubmitVo"/>
        where Sa_DemandSubmit.id = #{key} 
    </select>
    <sql id="selectSaDemandsubmitVo">
        select Sa_DemandSubmit.id,
               Sa_DemandSubmit.Type,
               Sa_DemandSubmit.Description,
               Sa_DemandSubmit.Level,
               Sa_DemandSubmit.Status,
               Sa_DemandSubmit.Assignee,
               Sa_DemandSubmit.Participants,
               Sa_DemandSubmit.DeadlineDate,
               Sa_DemandSubmit.Groupid,
               Sa_DemandSubmit.Source,
               Sa_DemandSubmit.StateText,
               Sa_DemandSubmit.StateDate,
               Sa_DemandSubmit.RowNum,
               Sa_DemandSubmit.Remark,
               Sa_DemandSubmit.OaFlowMark,
               Sa_DemandSubmit.FeedbackItemid,
               Sa_DemandSubmit.DemandMark,
               Sa_DemandSubmit.TestCaseMark,
               Sa_DemandSubmit.ReleaseMark,
               Sa_DemandSubmit.Assessor,
               Sa_DemandSubmit.Assessorid,
               Sa_DemandSubmit.AssessDate,
               Sa_DemandSubmit.CreateBy,
               Sa_DemandSubmit.CreateByid,
               Sa_DemandSubmit.CreateDate,
               Sa_DemandSubmit.Lister,
               Sa_DemandSubmit.Listerid,
               Sa_DemandSubmit.ModifyDate,
               Sa_DemandSubmit.Custom1,
               Sa_DemandSubmit.Custom2,
               Sa_DemandSubmit.Custom3,
               Sa_DemandSubmit.Custom4,
               Sa_DemandSubmit.Custom5,
               Sa_DemandSubmit.Tenantid,
               Sa_DemandSubmit.TenantName,
               Sa_DemandSubmit.Revision,
               Sa_Workgroup.GroupName,
               Sa_Workgroup.GroupUid,
               Sa_Workgroup.Abbreviate
        from Sa_DemandSubmit
             Left JOIN Sa_Workgroup ON Sa_DemandSubmit.Groupid = Sa_Workgroup.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaDemandsubmitPojo">
        <include refid="selectSaDemandsubmitVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_DemandSubmit.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.type != null ">
   and Sa_DemandSubmit.Type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Sa_DemandSubmit.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.status != null ">
   and Sa_DemandSubmit.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.assignee != null ">
   and Sa_DemandSubmit.Assignee like concat('%', #{SearchPojo.assignee}, '%')
</if>
<if test="SearchPojo.participants != null ">
   and Sa_DemandSubmit.Participants like concat('%', #{SearchPojo.participants}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_DemandSubmit.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.source != null ">
   and Sa_DemandSubmit.Source like concat('%', #{SearchPojo.source}, '%')
</if>
<if test="SearchPojo.statetext != null ">
   and Sa_DemandSubmit.StateText like concat('%', #{SearchPojo.statetext}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_DemandSubmit.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_DemandSubmit.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_DemandSubmit.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_DemandSubmit.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_DemandSubmit.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_DemandSubmit.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_DemandSubmit.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_DemandSubmit.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_DemandSubmit.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_DemandSubmit.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_DemandSubmit.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_DemandSubmit.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_DemandSubmit.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.type != null ">
   or Sa_DemandSubmit.Type like concat('%', #{SearchPojo.type}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Sa_DemandSubmit.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.status != null ">
   or Sa_DemandSubmit.Status like concat('%', #{SearchPojo.status}, '%')
</if>
<if test="SearchPojo.assignee != null ">
   or Sa_DemandSubmit.Assignee like concat('%', #{SearchPojo.assignee}, '%')
</if>
<if test="SearchPojo.participants != null ">
   or Sa_DemandSubmit.Participants like concat('%', #{SearchPojo.participants}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_DemandSubmit.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.source != null ">
   or Sa_DemandSubmit.Source like concat('%', #{SearchPojo.source}, '%')
</if>
<if test="SearchPojo.statetext != null ">
   or Sa_DemandSubmit.StateText like concat('%', #{SearchPojo.statetext}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_DemandSubmit.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_DemandSubmit.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_DemandSubmit.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_DemandSubmit.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_DemandSubmit.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_DemandSubmit.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_DemandSubmit.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_DemandSubmit.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_DemandSubmit.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_DemandSubmit.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_DemandSubmit.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_DemandSubmit.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_DemandSubmit.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_DemandSubmit(id, Type, Description, Level, Status, Assignee, Participants, DeadlineDate, Groupid, Source, StateText, StateDate, RowNum, Remark, OaFlowMark, FeedbackItemid, DemandMark, TestCaseMark, ReleaseMark, Assessor, Assessorid, AssessDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{type}, #{description}, #{level}, #{status}, #{assignee}, #{participants}, #{deadlinedate}, #{groupid}, #{source}, #{statetext}, #{statedate}, #{rownum}, #{remark}, #{oaflowmark}, #{feedbackitemid}, #{demandmark}, #{testcasemark}, #{releasemark}, #{assessor}, #{assessorid}, #{assessdate}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DemandSubmit
        <set>
            <if test="type != null ">
                Type =#{type},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="level != null">
                Level =#{level},
            </if>
            <if test="status != null ">
                Status =#{status},
            </if>
            <if test="assignee != null ">
                Assignee =#{assignee},
            </if>
            <if test="participants != null ">
                Participants =#{participants},
            </if>
            <if test="deadlinedate != null">
                DeadlineDate =#{deadlinedate},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="source != null ">
                Source =#{source},
            </if>
            <if test="statetext != null ">
                StateText =#{statetext},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="feedbackitemid != null ">
                FeedbackItemid =#{feedbackitemid},
            </if>
            <if test="demandmark != null">
                DemandMark =#{demandmark},
            </if>
            <if test="testcasemark != null">
                TestCaseMark =#{testcasemark},
            </if>
            <if test="releasemark != null">
                ReleaseMark =#{releasemark},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DemandSubmit where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_DemandSubmit SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>

    <update id="updateOaflowmark">
        update Sa_DemandSubmit SET
            OaFlowMark = #{oaflowmark}
        where id = #{id}
    </update>

    <update id="syncFeedbackItemSubmitMark">
        update Sa_FeedbackItem
        set SubmitMark=#{submitmark}
        where id = #{feedbackitemid}
    </update>

    <update id="syncFeedbackFinishCount">
        with Feedbackid as (select Pid
                             from Sa_FeedbackItem
                             where id = #{feedbackitemid})
        update Sa_Feedback
        set FinishCount = (select count(*)
                           from Sa_FeedbackItem
                           where Pid = (select Pid from Feedbackid)
                             and SubmitMark = 1)
        where id = (select Pid from Feedbackid);
    </update>



    <update id="syncFeedbackVisItemSubmitMark">
        update Sa_FeedbackVisitor
        set SubmitMark = #{submitmark}
        where id=#{feedbackitemid}
    </update>
</mapper>

