<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaDocreposMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaDocreposPojo">
        select
        id, Name, Type, Path, RealDocPath, VerCtrl, IsRemote, LocalMinioPath, MinioPath, MinioUser, MinioPwd, Version,
        VerCtrl1, Info, Pwd, Owner, State, LockBy, LockTime, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister,
        Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, Revision from Sa_DocRepos
        where Sa_DocRepos.id = #{key}
    </select>
    <sql id="selectSaDocreposVo">
        select id,
               Name,
               Type,
               Path,
               RealDocPath,
               VerCtrl,
               IsRemote,
               LocalMinioPath,
               MinioPath,
               MinioUser,
               MinioPwd,
               Version,
               VerCtrl1,
               Info,
               Pwd,
               Owner,
               State,
               LockBy,
               LockTime,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Deptid,
               Tenantid,
               Revision
        from Sa_DocRepos
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaDocreposPojo">
        <include refid="selectSaDocreposVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_DocRepos.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.name != null">
            and Sa_DocRepos.Name like concat('%', #{SearchPojo.name}, '%')
        </if>
        <if test="SearchPojo.path != null">
            and Sa_DocRepos.Path like concat('%', #{SearchPojo.path}, '%')
        </if>
        <if test="SearchPojo.realdocpath != null">
            and Sa_DocRepos.RealDocPath like concat('%', #{SearchPojo.realdocpath}, '%')
        </if>
        <if test="SearchPojo.localminiopath != null">
            and Sa_DocRepos.LocalMinioPath like concat('%', #{SearchPojo.localminiopath}, '%')
        </if>
        <if test="SearchPojo.miniopath != null">
            and Sa_DocRepos.MinioPath like concat('%', #{SearchPojo.miniopath}, '%')
        </if>
        <if test="SearchPojo.miniouser != null">
            and Sa_DocRepos.MinioUser like concat('%', #{SearchPojo.miniouser}, '%')
        </if>
        <if test="SearchPojo.miniopwd != null">
            and Sa_DocRepos.MinioPwd like concat('%', #{SearchPojo.miniopwd}, '%')
        </if>
        <if test="SearchPojo.version != null">
            and Sa_DocRepos.Version like concat('%', #{SearchPojo.version}, '%')
        </if>
        <if test="SearchPojo.info != null">
            and Sa_DocRepos.Info like concat('%', #{SearchPojo.info}, '%')
        </if>
        <if test="SearchPojo.pwd != null">
            and Sa_DocRepos.Pwd like concat('%', #{SearchPojo.pwd}, '%')
        </if>
        <if test="SearchPojo.lockby != null">
            and Sa_DocRepos.LockBy like concat('%', #{SearchPojo.lockby}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_DocRepos.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_DocRepos.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_DocRepos.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_DocRepos.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_DocRepos.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_DocRepos.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_DocRepos.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_DocRepos.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_DocRepos.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_DocRepos.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_DocRepos.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.name != null">
                or Sa_DocRepos.Name like concat('%', #{SearchPojo.name}, '%')
            </if>
            <if test="SearchPojo.path != null">
                or Sa_DocRepos.Path like concat('%', #{SearchPojo.path}, '%')
            </if>
            <if test="SearchPojo.realdocpath != null">
                or Sa_DocRepos.RealDocPath like concat('%', #{SearchPojo.realdocpath}, '%')
            </if>
            <if test="SearchPojo.localminiopath != null">
                or Sa_DocRepos.LocalMinioPath like concat('%', #{SearchPojo.localminiopath}, '%')
            </if>
            <if test="SearchPojo.miniopath != null">
                or Sa_DocRepos.MinioPath like concat('%', #{SearchPojo.miniopath}, '%')
            </if>
            <if test="SearchPojo.miniouser != null">
                or Sa_DocRepos.MinioUser like concat('%', #{SearchPojo.miniouser}, '%')
            </if>
            <if test="SearchPojo.miniopwd != null">
                or Sa_DocRepos.MinioPwd like concat('%', #{SearchPojo.miniopwd}, '%')
            </if>
            <if test="SearchPojo.version != null">
                or Sa_DocRepos.Version like concat('%', #{SearchPojo.version}, '%')
            </if>
            <if test="SearchPojo.info != null">
                or Sa_DocRepos.Info like concat('%', #{SearchPojo.info}, '%')
            </if>
            <if test="SearchPojo.pwd != null">
                or Sa_DocRepos.Pwd like concat('%', #{SearchPojo.pwd}, '%')
            </if>
            <if test="SearchPojo.lockby != null">
                or Sa_DocRepos.LockBy like concat('%', #{SearchPojo.lockby}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_DocRepos.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_DocRepos.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_DocRepos.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_DocRepos.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_DocRepos.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_DocRepos.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_DocRepos.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_DocRepos.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_DocRepos.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_DocRepos.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_DocRepos.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_DocRepos(id, Name, Type, Path, RealDocPath, VerCtrl, IsRemote, LocalMinioPath, MinioPath,
        MinioUser, MinioPwd, Version, VerCtrl1, Info, Pwd, Owner, State, LockBy, LockTime, RowNum, Remark, CreateBy,
        CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid,
        Tenantid, Revision)
        values (#{id}, #{name}, #{type}, #{path}, #{realdocpath}, #{verctrl}, #{isremote}, #{localminiopath},
        #{miniopath}, #{miniouser}, #{miniopwd}, #{version}, #{verctrl1}, #{info}, #{pwd}, #{owner}, #{state},
        #{lockby}, #{locktime}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
        #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{deptid}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DocRepos
        <set>
            <if test="name != null">
                Name =#{name},
            </if>
            <if test="type != null">
                Type =#{type},
            </if>
            <if test="path != null">
                Path =#{path},
            </if>
            <if test="realdocpath != null">
                RealDocPath =#{realdocpath},
            </if>
            <if test="verctrl != null">
                VerCtrl =#{verctrl},
            </if>
            <if test="isremote != null">
                IsRemote =#{isremote},
            </if>
            <if test="localminiopath != null">
                LocalMinioPath =#{localminiopath},
            </if>
            <if test="miniopath != null">
                MinioPath =#{miniopath},
            </if>
            <if test="miniouser != null">
                MinioUser =#{miniouser},
            </if>
            <if test="miniopwd != null">
                MinioPwd =#{miniopwd},
            </if>
            <if test="version != null">
                Version =#{version},
            </if>
            <if test="verctrl1 != null">
                VerCtrl1 =#{verctrl1},
            </if>
            <if test="info != null">
                Info =#{info},
            </if>
            <if test="pwd != null">
                Pwd =#{pwd},
            </if>
            <if test="owner != null">
                Owner =#{owner},
            </if>
            <if test="state != null">
                State =#{state},
            </if>
            <if test="lockby != null">
                LockBy =#{lockby},
            </if>
            <if test="locktime != null">
                LockTime =#{locktime},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DocRepos where id = #{key}
    </delete>
</mapper>

