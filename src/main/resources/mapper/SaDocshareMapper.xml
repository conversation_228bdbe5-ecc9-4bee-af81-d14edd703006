<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaDocshareMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaDocsharePojo">
        select
        id, Shareid, Name, Path, Docid, Vid, ShareAuth, SharePwd, SharedBy, ExpireTime, RowNum, Remark, CreateBy,
        CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid,
        Tenantid, Revision from Sa_DocShare
        where Sa_DocShare.id = #{key}
    </select>
    <sql id="selectSaDocshareVo">
        select id,
               Shareid,
               Name,
               <PERSON>,
               <PERSON>id,
               Vid,
               <PERSON><PERSON><PERSON><PERSON>,
               SharePwd,
               <PERSON>hared<PERSON>y,
               ExpireTime,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Deptid,
               Tenantid,
               Revision
        from Sa_DocShare
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaDocsharePojo">
        <include refid="selectSaDocshareVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_DocShare.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.shareid != null">
            and Sa_DocShare.Shareid like concat('%', #{SearchPojo.shareid}, '%')
        </if>
        <if test="SearchPojo.name != null">
            and Sa_DocShare.Name like concat('%', #{SearchPojo.name}, '%')
        </if>
        <if test="SearchPojo.path != null">
            and Sa_DocShare.Path like concat('%', #{SearchPojo.path}, '%')
        </if>
        <if test="SearchPojo.docid != null">
            and Sa_DocShare.Docid like concat('%', #{SearchPojo.docid}, '%')
        </if>
        <if test="SearchPojo.vid != null">
            and Sa_DocShare.Vid like concat('%', #{SearchPojo.vid}, '%')
        </if>
        <if test="SearchPojo.shareauth != null">
            and Sa_DocShare.ShareAuth like concat('%', #{SearchPojo.shareauth}, '%')
        </if>
        <if test="SearchPojo.sharepwd != null">
            and Sa_DocShare.SharePwd like concat('%', #{SearchPojo.sharepwd}, '%')
        </if>
        <if test="SearchPojo.sharedby != null">
            and Sa_DocShare.SharedBy like concat('%', #{SearchPojo.sharedby}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_DocShare.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_DocShare.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_DocShare.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_DocShare.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_DocShare.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_DocShare.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_DocShare.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_DocShare.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_DocShare.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_DocShare.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_DocShare.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.shareid != null">
                or Sa_DocShare.Shareid like concat('%', #{SearchPojo.shareid}, '%')
            </if>
            <if test="SearchPojo.name != null">
                or Sa_DocShare.Name like concat('%', #{SearchPojo.name}, '%')
            </if>
            <if test="SearchPojo.path != null">
                or Sa_DocShare.Path like concat('%', #{SearchPojo.path}, '%')
            </if>
            <if test="SearchPojo.docid != null">
                or Sa_DocShare.Docid like concat('%', #{SearchPojo.docid}, '%')
            </if>
            <if test="SearchPojo.vid != null">
                or Sa_DocShare.Vid like concat('%', #{SearchPojo.vid}, '%')
            </if>
            <if test="SearchPojo.shareauth != null">
                or Sa_DocShare.ShareAuth like concat('%', #{SearchPojo.shareauth}, '%')
            </if>
            <if test="SearchPojo.sharepwd != null">
                or Sa_DocShare.SharePwd like concat('%', #{SearchPojo.sharepwd}, '%')
            </if>
            <if test="SearchPojo.sharedby != null">
                or Sa_DocShare.SharedBy like concat('%', #{SearchPojo.sharedby}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_DocShare.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_DocShare.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_DocShare.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_DocShare.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_DocShare.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_DocShare.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_DocShare.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_DocShare.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_DocShare.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_DocShare.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_DocShare.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_DocShare(id, Shareid, Name, Path, Docid, Vid, ShareAuth, SharePwd, SharedBy, ExpireTime, RowNum,
        Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4,
        Custom5, Deptid, Tenantid, Revision)
        values (#{id}, #{shareid}, #{name}, #{path}, #{docid}, #{vid}, #{shareauth}, #{sharepwd}, #{sharedby},
        #{expiretime}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid},
        #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{deptid}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_DocShare
        <set>
            <if test="shareid != null">
                Shareid =#{shareid},
            </if>
            <if test="name != null">
                Name =#{name},
            </if>
            <if test="path != null">
                Path =#{path},
            </if>
            <if test="docid != null">
                Docid =#{docid},
            </if>
            <if test="vid != null">
                Vid =#{vid},
            </if>
            <if test="shareauth != null">
                ShareAuth =#{shareauth},
            </if>
            <if test="sharepwd != null">
                SharePwd =#{sharepwd},
            </if>
            <if test="sharedby != null">
                SharedBy =#{sharedby},
            </if>
            <if test="expiretime != null">
                ExpireTime =#{expiretime},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_DocShare where id = #{key}
    </delete>
</mapper>

