<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaEngineerMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaEngineerPojo">
        <include refid="selectSaEngineerVo"/>
        where Sa_Engineer.id = #{key}
    </select>
    <sql id="selectSaEngineerVo">
        select Sa_Engineer.id,
               Sa_Engineer.Userid,
               Sa_Engineer.GenGroupid,
               Sa_Engineer.EngineerType,
               Sa_Engineer.EngineerCode,
               Sa_Engineer.EngineerName,
               Sa_Engineer.EnabledMark,
               Sa_Engineer.Email,
               Sa_Engineer.RowNum,
               Sa_Engineer.Remark,
               Sa_Engineer.CreateBy,
               Sa_Engineer.<PERSON><PERSON><PERSON><PERSON><PERSON>,
               Sa_Engineer.CreateDate,
               Sa_Engineer.Lister,
               Sa_Engineer.<PERSON><PERSON><PERSON>,
               Sa_Engineer.ModifyDate,
               Sa_Engineer.Custom1,
               Sa_Engineer.Custom2,
               Sa_Engineer.Custom3,
               Sa_Engineer.Custom4,
               Sa_Engineer.Custom5,
               Sa_Engineer.Custom6,
               Sa_Engineer.Custom7,
               Sa_Engineer.Custom8,
               Sa_Engineer.Custom9,
               Sa_Engineer.Custom10,
               Sa_Engineer.Deptid,
               Sa_Engineer.Tenantid,
               Sa_Engineer.Revision,
               Sa_User.UserName,
               Sa_User.RealName,
               Sa_User.RoleType,
               Sa_User.AdminMark
        from Sa_Engineer
                 left join Sa_User on Sa_Engineer.Userid = Sa_User.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaEngineerPojo">
        <include refid="selectSaEngineerVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Engineer.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null">
            and Sa_Engineer.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.engineertype != null">
            and Sa_Engineer.EngineerType like concat('%',
            #{SearchPojo.engineertype}, '%')
        </if>
        <if test="SearchPojo.engineercode != null">
            and Sa_Engineer.EngineerCode like concat('%',
            #{SearchPojo.engineercode}, '%')
        </if>
        <if test="SearchPojo.engineername != null">
            and Sa_Engineer.EngineerName like concat('%',
            #{SearchPojo.engineername}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Engineer.Remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Engineer.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Engineer.CreateByid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Engineer.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Engineer.Listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Engineer.Custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Engineer.Custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Engineer.Custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Engineer.Custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Engineer.Custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Engineer.Custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Engineer.Custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Engineer.Custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Engineer.Custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Engineer.Custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_Engineer.Deptid like concat('%',
            #{SearchPojo.deptid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null">
                or Sa_Engineer.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.engineertype != null">
                or Sa_Engineer.EngineerType like concat('%', #{SearchPojo.engineertype}, '%')
            </if>
            <if test="SearchPojo.engineercode != null">
                or Sa_Engineer.EngineerCode like concat('%', #{SearchPojo.engineercode}, '%')
            </if>
            <if test="SearchPojo.engineername != null">
                or Sa_Engineer.EngineerName like concat('%', #{SearchPojo.engineername}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Engineer.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Engineer.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Engineer.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Engineer.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Engineer.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Engineer.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Engineer.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Engineer.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Engineer.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Engineer.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Engineer.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Engineer.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Engineer.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Engineer.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Engineer.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_Engineer.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Engineer(id, Userid, GenGroupid, EngineerType, EngineerCode, EngineerName, EnabledMark, Email,
        RowNum, Remark,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2,
        Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Deptid,
        Tenantid, Revision)
        values (#{id}, #{userid}, #{gengroupid}, #{engineertype}, #{engineercode}, #{engineername}, #{enabledmark},
        #{email}, #{rownum},
        #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
        #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
        #{custom10}, #{deptid}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Engineer
        <set>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="engineertype != null">
                EngineerType =#{engineertype},
            </if>
            <if test="engineercode != null">
                EngineerCode =#{engineercode},
            </if>
            <if test="engineername != null">
                EngineerName =#{engineername},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="email != null">
                Email =#{email},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Engineer
        where id = #{key}
    </delete>

    <select id="getEngineerByUserid" resultType="inks.sa.common.core.domain.vo.SaEngineerPojo">
        select *
        from Sa_Engineer
        where Sa_Engineer.Userid = #{userid}
    </select>

    <select id="getUserid" resultType="java.lang.String">
        select Sa_Engineer.Userid
        from Sa_Engineer
        where Sa_Engineer.id = #{key}
    </select>

    <select id="getUseridList" resultType="java.lang.String">
        select Sa_Engineer.Userid
        from Sa_Engineer
        where Sa_Engineer.id in (${engineerids})
    </select>

    <select id="getIdByUserId" resultType="java.lang.String">
        select Sa_Engineer.id
        from Sa_Engineer
        where Sa_Engineer.Userid = #{userid}
    </select>

    <select id="getEntityByEmail" resultType="inks.service.sa.pms.domain.pojo.SaEngineerPojo">
        <include refid="selectSaEngineerVo"/>
            where Sa_User.Email = #{email}
    </select>

    <select id="getEntityByUserId" resultType="inks.service.sa.pms.domain.pojo.SaEngineerPojo">
        <include refid="selectSaEngineerVo"/>
            where Sa_Engineer.Userid = #{userid}
    </select>
</mapper>

