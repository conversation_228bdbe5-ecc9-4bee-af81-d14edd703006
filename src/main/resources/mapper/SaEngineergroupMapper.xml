<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaEngineergroupMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaEngineergroupPojo">
        <include refid="selectbillVo"/>
        where Sa_EngineerGroup.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, GroupCode, GroupName, GroupType, GroupDesc, GroupLeader, GroupLeaderid, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_EngineerGroup
    </sql>
    <sql id="selectdetailVo">
        select
               Sa_EngineerGroup.CreateBy,
               Sa_EngineerGroup.Lister,
               Sa_EngineerGroupItem.*
        from Sa_EngineerGroupItem left join Sa_EngineerGroup on Sa_EngineerGroup.id = Sa_EngineerGroupItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaEngineergroupitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1
       <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_EngineerGroup.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="and"></include>
         </if>
         <if test="SearchType==1">
           <include refid="or"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.groupcode != null ">
   and Sa_EngineerGroup.groupcode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Sa_EngineerGroup.groupname like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.grouptype != null ">
   and Sa_EngineerGroup.grouptype like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupdesc != null ">
   and Sa_EngineerGroup.groupdesc like concat('%', #{SearchPojo.groupdesc}, '%')
</if>
<if test="SearchPojo.groupleader != null ">
   and Sa_EngineerGroup.groupleader like concat('%', #{SearchPojo.groupleader}, '%')
</if>
<if test="SearchPojo.groupleaderid != null ">
   and Sa_EngineerGroup.groupleaderid like concat('%', #{SearchPojo.groupleaderid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_EngineerGroup.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_EngineerGroup.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_EngineerGroup.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_EngineerGroup.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_EngineerGroup.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_EngineerGroup.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_EngineerGroup.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_EngineerGroup.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_EngineerGroup.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_EngineerGroup.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.groupcode != null ">
   or Sa_EngineerGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Sa_EngineerGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.grouptype != null ">
   or Sa_EngineerGroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupdesc != null ">
   or Sa_EngineerGroup.GroupDesc like concat('%', #{SearchPojo.groupdesc}, '%')
</if>
<if test="SearchPojo.groupleader != null ">
   or Sa_EngineerGroup.GroupLeader like concat('%', #{SearchPojo.groupleader}, '%')
</if>
<if test="SearchPojo.groupleaderid != null ">
   or Sa_EngineerGroup.GroupLeaderid like concat('%', #{SearchPojo.groupleaderid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_EngineerGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_EngineerGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_EngineerGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_EngineerGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_EngineerGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_EngineerGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_EngineerGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_EngineerGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_EngineerGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_EngineerGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaEngineergroupPojo">
        <include refid="selectbillVo"/>
         where 1 = 1
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_EngineerGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">
           <include refid="thand"></include>
         </if>
         <if test="SearchType==1">
           <include refid="thor"></include>
         </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.groupcode != null ">
   and Sa_EngineerGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   and Sa_EngineerGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.grouptype != null ">
   and Sa_EngineerGroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupdesc != null ">
   and Sa_EngineerGroup.GroupDesc like concat('%', #{SearchPojo.groupdesc}, '%')
</if>
<if test="SearchPojo.groupleader != null ">
   and Sa_EngineerGroup.GroupLeader like concat('%', #{SearchPojo.groupleader}, '%')
</if>
<if test="SearchPojo.groupleaderid != null ">
   and Sa_EngineerGroup.GroupLeaderid like concat('%', #{SearchPojo.groupleaderid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_EngineerGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_EngineerGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_EngineerGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_EngineerGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_EngineerGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_EngineerGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_EngineerGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_EngineerGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_EngineerGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_EngineerGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.groupcode != null ">
   or Sa_EngineerGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
</if>
<if test="SearchPojo.groupname != null ">
   or Sa_EngineerGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
</if>
<if test="SearchPojo.grouptype != null ">
   or Sa_EngineerGroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
</if>
<if test="SearchPojo.groupdesc != null ">
   or Sa_EngineerGroup.GroupDesc like concat('%', #{SearchPojo.groupdesc}, '%')
</if>
<if test="SearchPojo.groupleader != null ">
   or Sa_EngineerGroup.GroupLeader like concat('%', #{SearchPojo.groupleader}, '%')
</if>
<if test="SearchPojo.groupleaderid != null ">
   or Sa_EngineerGroup.GroupLeaderid like concat('%', #{SearchPojo.groupleaderid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_EngineerGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_EngineerGroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_EngineerGroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_EngineerGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_EngineerGroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_EngineerGroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_EngineerGroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_EngineerGroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_EngineerGroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_EngineerGroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_EngineerGroup(id, GroupCode, GroupName, GroupType, GroupDesc, GroupLeader, GroupLeaderid, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{groupcode}, #{groupname}, #{grouptype}, #{groupdesc}, #{groupleader}, #{groupleaderid}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_EngineerGroup
        <set>
            <if test="groupcode != null ">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null ">
                GroupName =#{groupname},
            </if>
            <if test="grouptype != null ">
                GroupType =#{grouptype},
            </if>
            <if test="groupdesc != null ">
                GroupDesc =#{groupdesc},
            </if>
            <if test="groupleader != null ">
                GroupLeader =#{groupleader},
            </if>
            <if test="groupleaderid != null ">
                GroupLeaderid =#{groupleaderid},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_EngineerGroup where id = #{key}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.pms.domain.pojo.SaEngineergroupPojo">
        select
          id
        from Sa_EngineerGroupItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

    <select id="getName" resultType="java.lang.String">
        select GroupName
        from Sa_EngineerGroup
        where id = #{engineergroupid}
    </select>
</mapper>

