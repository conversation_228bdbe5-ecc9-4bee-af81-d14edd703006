<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaExternalMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaExternalPojo">
        select
        id, ExtType, ExtCode, ExtName, ExtTitle, FrontPhoto, ImageCss, ExtUrl, ExtPd, AppUrl, AppPd, RowNum,
        EnabledMark, IsPublic, PermissionCode, Remark, Operator, Operatorid, CreateBy, CreateByid, CreateDate, Lister,
        Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision from
        Sa_External
        where Sa_External.id = #{key}
    </select>
    <sql id="selectSaExternalVo">
        select id,
               ExtType,
               ExtCode,
               ExtName,
               ExtTitle,
               FrontPhoto,
               ImageCss,
               ExtUrl,
               ExtPd,
               AppUrl,
               AppPd,
               RowNum,
               EnabledMark,
               IsPublic,
               PermissionCode,
               Remark,
               Operator,
               Operatorid,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from Sa_External
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaExternalPojo">
        <include refid="selectSaExternalVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_External.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.exttype != null">
            and Sa_External.ExtType like concat('%', #{SearchPojo.exttype}, '%')
        </if>
        <if test="SearchPojo.extcode != null">
            and Sa_External.ExtCode like concat('%', #{SearchPojo.extcode}, '%')
        </if>
        <if test="SearchPojo.extname != null">
            and Sa_External.ExtName like concat('%', #{SearchPojo.extname}, '%')
        </if>
        <if test="SearchPojo.exttitle != null">
            and Sa_External.ExtTitle like concat('%', #{SearchPojo.exttitle}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null">
            and Sa_External.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.imagecss != null">
            and Sa_External.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
        </if>
        <if test="SearchPojo.exturl != null">
            and Sa_External.ExtUrl like concat('%', #{SearchPojo.exturl}, '%')
        </if>
        <if test="SearchPojo.extpd != null">
            and Sa_External.ExtPd like concat('%', #{SearchPojo.extpd}, '%')
        </if>
        <if test="SearchPojo.appurl != null">
            and Sa_External.AppUrl like concat('%', #{SearchPojo.appurl}, '%')
        </if>
        <if test="SearchPojo.apppd != null">
            and Sa_External.AppPd like concat('%', #{SearchPojo.apppd}, '%')
        </if>
        <if test="SearchPojo.permissioncode != null">
            and Sa_External.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_External.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_External.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Sa_External.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_External.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_External.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_External.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_External.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_External.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_External.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_External.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_External.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_External.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_External.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.exttype != null">
                or Sa_External.ExtType like concat('%', #{SearchPojo.exttype}, '%')
            </if>
            <if test="SearchPojo.extcode != null">
                or Sa_External.ExtCode like concat('%', #{SearchPojo.extcode}, '%')
            </if>
            <if test="SearchPojo.extname != null">
                or Sa_External.ExtName like concat('%', #{SearchPojo.extname}, '%')
            </if>
            <if test="SearchPojo.exttitle != null">
                or Sa_External.ExtTitle like concat('%', #{SearchPojo.exttitle}, '%')
            </if>
            <if test="SearchPojo.frontphoto != null">
                or Sa_External.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
            </if>
            <if test="SearchPojo.imagecss != null">
                or Sa_External.ImageCss like concat('%', #{SearchPojo.imagecss}, '%')
            </if>
            <if test="SearchPojo.exturl != null">
                or Sa_External.ExtUrl like concat('%', #{SearchPojo.exturl}, '%')
            </if>
            <if test="SearchPojo.extpd != null">
                or Sa_External.ExtPd like concat('%', #{SearchPojo.extpd}, '%')
            </if>
            <if test="SearchPojo.appurl != null">
                or Sa_External.AppUrl like concat('%', #{SearchPojo.appurl}, '%')
            </if>
            <if test="SearchPojo.apppd != null">
                or Sa_External.AppPd like concat('%', #{SearchPojo.apppd}, '%')
            </if>
            <if test="SearchPojo.permissioncode != null">
                or Sa_External.PermissionCode like concat('%', #{SearchPojo.permissioncode}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_External.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_External.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Sa_External.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_External.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_External.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_External.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_External.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_External.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_External.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_External.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_External.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_External.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_External.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_External(id, ExtType, ExtCode, ExtName, ExtTitle, FrontPhoto, ImageCss, ExtUrl, ExtPd, AppUrl,
        AppPd, RowNum, EnabledMark, IsPublic, PermissionCode, Remark, Operator, Operatorid, CreateBy, CreateByid,
        CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName,
        Revision)
        values (#{id}, #{exttype}, #{extcode}, #{extname}, #{exttitle}, #{frontphoto}, #{imagecss}, #{exturl}, #{extpd},
        #{appurl}, #{apppd}, #{rownum}, #{enabledmark}, #{ispublic}, #{permissioncode}, #{remark}, #{operator},
        #{operatorid}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
        #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_External
        <set>
            <if test="exttype != null">
                ExtType =#{exttype},
            </if>
            <if test="extcode != null">
                ExtCode =#{extcode},
            </if>
            <if test="extname != null">
                ExtName =#{extname},
            </if>
            <if test="exttitle != null">
                ExtTitle =#{exttitle},
            </if>
            <if test="frontphoto != null">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="imagecss != null">
                ImageCss =#{imagecss},
            </if>
            <if test="exturl != null">
                ExtUrl =#{exturl},
            </if>
            <if test="extpd != null">
                ExtPd =#{extpd},
            </if>
            <if test="appurl != null">
                AppUrl =#{appurl},
            </if>
            <if test="apppd != null">
                AppPd =#{apppd},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="ispublic != null">
                IsPublic =#{ispublic},
            </if>
            <if test="permissioncode != null">
                PermissionCode =#{permissioncode},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_External where id = #{key}
    </delete>
</mapper>

