<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaFeedbackMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaFeedbackPojo">
        <include refid="selectbillVo"/>
        where Sa_Feedback.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, RefNo, BillTitle, BillType, BillDate, Projectid, ProjectName, Groupid, Customer, Location, Personnel, Status, StatusDate, ItemCount, FinishCount, Summary, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Assessor<PERSON>, Assessor, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision        from Sa_Feedback
    </sql>
    <sql id="selectdetailVo">
        select Sa_FeedbackItem.id,
               Sa_FeedbackItem.Pid,
               Sa_FeedbackItem.ItemType,
               Sa_FeedbackItem.Story,
               Sa_FeedbackItem.Opinion,
               Sa_FeedbackItem.Exponent,
               Sa_FeedbackItem.FinishMark,
               Sa_FeedbackItem.RowNum,
               Sa_FeedbackItem.Remark,
               Sa_FeedbackItem.Custom1,
               Sa_FeedbackItem.Custom2,
               Sa_FeedbackItem.Custom3,
               Sa_FeedbackItem.Custom4,
               Sa_FeedbackItem.Custom5,
               Sa_FeedbackItem.Revision,
               Sa_Feedback.RefNo,
               Sa_Feedback.BillTitle,
               Sa_Feedback.BillType,
               Sa_Feedback.BillDate,
               Sa_Feedback.Groupid,
               Sa_Feedback.Projectid,
               Sa_Feedback.ProjectName
        from Sa_FeedbackItem
                 left join Sa_Feedback on Sa_FeedbackItem.Pid = Sa_Feedback.id
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaFeedbackitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Feedback.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Sa_Feedback.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Sa_Feedback.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Sa_Feedback.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.projectid != null">
            and Sa_Feedback.projectid like concat('%', #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.projectname != null">
            and Sa_Feedback.projectname like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.customer != null">
            and Sa_Feedback.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.location != null">
            and Sa_Feedback.location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.personnel != null">
            and Sa_Feedback.personnel like concat('%', #{SearchPojo.personnel}, '%')
        </if>
        <if test="SearchPojo.status != null">
            and Sa_Feedback.status like concat('%', #{SearchPojo.status}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Sa_Feedback.summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Feedback.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Feedback.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Feedback.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Feedback.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_Feedback.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_Feedback.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Feedback.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Feedback.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Feedback.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Feedback.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Feedback.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Sa_Feedback.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Sa_Feedback.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Sa_Feedback.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.projectid != null">
                or Sa_Feedback.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.projectname != null">
                or Sa_Feedback.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
            </if>
            <if test="SearchPojo.customer != null">
                or Sa_Feedback.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.location != null">
                or Sa_Feedback.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.personnel != null">
                or Sa_Feedback.Personnel like concat('%', #{SearchPojo.personnel}, '%')
            </if>
            <if test="SearchPojo.status != null">
                or Sa_Feedback.Status like concat('%', #{SearchPojo.status}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Sa_Feedback.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Feedback.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Feedback.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Feedback.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Feedback.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_Feedback.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_Feedback.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Feedback.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Feedback.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Feedback.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Feedback.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Feedback.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaFeedbackPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Feedback.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.refno != null">
            and Sa_Feedback.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Sa_Feedback.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Sa_Feedback.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.projectid != null">
            and Sa_Feedback.Projectid like concat('%', #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.projectname != null">
            and Sa_Feedback.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.customer != null">
            and Sa_Feedback.Customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.location != null">
            and Sa_Feedback.Location like concat('%', #{SearchPojo.location}, '%')
        </if>
        <if test="SearchPojo.personnel != null">
            and Sa_Feedback.Personnel like concat('%', #{SearchPojo.personnel}, '%')
        </if>
        <if test="SearchPojo.status != null">
            and Sa_Feedback.Status like concat('%', #{SearchPojo.status}, '%')
        </if>
        <if test="SearchPojo.summary != null">
            and Sa_Feedback.Summary like concat('%', #{SearchPojo.summary}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Feedback.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Feedback.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Feedback.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Feedback.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_Feedback.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_Feedback.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Feedback.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Feedback.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Feedback.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Feedback.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Feedback.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Sa_Feedback.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Sa_Feedback.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Sa_Feedback.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.projectid != null">
                or Sa_Feedback.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.projectname != null">
                or Sa_Feedback.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
            </if>
            <if test="SearchPojo.customer != null">
                or Sa_Feedback.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.location != null">
                or Sa_Feedback.Location like concat('%', #{SearchPojo.location}, '%')
            </if>
            <if test="SearchPojo.personnel != null">
                or Sa_Feedback.Personnel like concat('%', #{SearchPojo.personnel}, '%')
            </if>
            <if test="SearchPojo.status != null">
                or Sa_Feedback.Status like concat('%', #{SearchPojo.status}, '%')
            </if>
            <if test="SearchPojo.summary != null">
                or Sa_Feedback.Summary like concat('%', #{SearchPojo.summary}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Feedback.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Feedback.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Feedback.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Feedback.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_Feedback.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_Feedback.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Feedback.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Feedback.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Feedback.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Feedback.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Feedback.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Feedback(id, RefNo, BillTitle, BillType, BillDate, Projectid, ProjectName, Groupid, Customer, Location, Personnel, Status, StatusDate, ItemCount, FinishCount, Summary, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Assessorid, Assessor, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{refno}, #{billtitle}, #{billtype}, #{billdate}, #{projectid}, #{projectname}, #{groupid}, #{customer}, #{location}, #{personnel}, #{status}, #{statusdate}, #{itemcount}, #{finishcount}, #{summary}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{assessorid}, #{assessor}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Feedback
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="projectname != null">
                ProjectName =#{projectname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="customer != null">
                Customer =#{customer},
            </if>
            <if test="location != null">
                Location =#{location},
            </if>
            <if test="personnel != null">
                Personnel =#{personnel},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="statusdate != null">
                StatusDate =#{statusdate},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="summary != null">
                Summary =#{summary},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Feedback where id = #{key}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Feedback SET
        Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision+1
        where id = #{id}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.pms.domain.pojo.SaFeedbackPojo">
        select
        id
        from Sa_FeedbackItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <update id="syncFinishcount">
        update Sa_Feedback SET
        FinishCount = (select count(1) from Sa_FeedbackItem where Pid = #{id} and FinishMark = 100)
        where id = #{id}
    </update>
</mapper>

