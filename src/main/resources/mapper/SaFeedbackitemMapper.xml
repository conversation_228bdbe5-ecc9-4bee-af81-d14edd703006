<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaFeedbackitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaFeedbackitemPojo">
        <include refid="selectSaFeedbackitemVo"/>
        where Sa_FeedbackItem.id = #{key}
    </select>
    <sql id="selectSaFeedbackitemVo">
         select
id, Pid, ItemType, Story, Opinion, Exponent, FinishMark, SubmitMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Revision        from Sa_FeedbackItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaFeedbackitemPojo">
        <include refid="selectSaFeedbackitemVo"/>
        where Sa_FeedbackItem.Pid = #{Pid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaFeedbackitemPojo">
        <include refid="selectSaFeedbackitemVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_FeedbackItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Sa_FeedbackItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
            and Sa_FeedbackItem.itemtype like concat('%', #{SearchPojo.itemtype}, '%')
        </if>
        <if test="SearchPojo.story != null and SearchPojo.story != ''">
            and Sa_FeedbackItem.story like concat('%', #{SearchPojo.story}, '%')
        </if>
        <if test="SearchPojo.opinion != null and SearchPojo.opinion != ''">
            and Sa_FeedbackItem.opinion like concat('%', #{SearchPojo.opinion}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Sa_FeedbackItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Sa_FeedbackItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Sa_FeedbackItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Sa_FeedbackItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Sa_FeedbackItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Sa_FeedbackItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Sa_FeedbackItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
                or Sa_FeedbackItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
            </if>
            <if test="SearchPojo.story != null and SearchPojo.story != ''">
                or Sa_FeedbackItem.Story like concat('%', #{SearchPojo.story}, '%')
            </if>
            <if test="SearchPojo.opinion != null and SearchPojo.opinion != ''">
                or Sa_FeedbackItem.Opinion like concat('%', #{SearchPojo.opinion}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Sa_FeedbackItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Sa_FeedbackItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Sa_FeedbackItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Sa_FeedbackItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Sa_FeedbackItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Sa_FeedbackItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>


    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_FeedbackItem(id, Pid, ItemType, Story, Opinion, Exponent, FinishMark, SubmitMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{pid}, #{itemtype}, #{story}, #{opinion}, #{exponent}, #{finishmark}, #{submitmark}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_FeedbackItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="itemtype != null">
                ItemType = #{itemtype},
            </if>
            <if test="story != null">
                Story = #{story},
            </if>
            <if test="opinion != null">
                Opinion = #{opinion},
            </if>
            <if test="exponent != null">
                Exponent = #{exponent},
            </if>
            <if test="finishmark != null">
                FinishMark = #{finishmark},
            </if>
            <if test="submitmark != null">
                SubmitMark = #{submitmark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_FeedbackItem where id = #{key}
    </delete>
</mapper>

