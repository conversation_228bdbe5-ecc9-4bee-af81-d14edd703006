<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaFeedbackvisitorMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaFeedbackvisitorPojo">
        <include refid="selectSaFeedbackvisitorVo"/>
        where Sa_FeedbackVisitor.id = #{key} 
    </select>
    <sql id="selectSaFeedbackvisitorVo">
         select
id, CustName, Groupid, Phone, Email, Title, Issue, Attachment, Photos, SubmitMark, FinishMark, FinishDesc, Engineer, CloseDate, CreateByid, CreateBy, CreateDate, ModifyDate, <PERSON>erid, Lister, Custom1, Custom2, Custom3, Custom4, Custom5, Revision        from Sa_FeedbackVisitor
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaFeedbackvisitorPojo">
        <include refid="selectSaFeedbackvisitorVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_FeedbackVisitor.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.custname != null ">
   and Sa_FeedbackVisitor.CustName like concat('%', #{SearchPojo.custname}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   and Sa_FeedbackVisitor.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.phone != null ">
   and Sa_FeedbackVisitor.Phone like concat('%', #{SearchPojo.phone}, '%')
</if>
<if test="SearchPojo.email != null ">
   and Sa_FeedbackVisitor.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.title != null ">
   and Sa_FeedbackVisitor.Title like concat('%', #{SearchPojo.title}, '%')
</if>
<if test="SearchPojo.issue != null ">
   and Sa_FeedbackVisitor.Issue like concat('%', #{SearchPojo.issue}, '%')
</if>
<if test="SearchPojo.attachment != null ">
   and Sa_FeedbackVisitor.Attachment like concat('%', #{SearchPojo.attachment}, '%')
</if>
<if test="SearchPojo.photos != null ">
   and Sa_FeedbackVisitor.Photos like concat('%', #{SearchPojo.photos}, '%')
</if>
<if test="SearchPojo.finishdesc != null ">
   and Sa_FeedbackVisitor.FinishDesc like concat('%', #{SearchPojo.finishdesc}, '%')
</if>
<if test="SearchPojo.engineer != null ">
   and Sa_FeedbackVisitor.Engineer like concat('%', #{SearchPojo.engineer}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_FeedbackVisitor.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_FeedbackVisitor.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_FeedbackVisitor.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_FeedbackVisitor.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_FeedbackVisitor.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_FeedbackVisitor.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_FeedbackVisitor.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_FeedbackVisitor.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_FeedbackVisitor.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.custname != null ">
   or Sa_FeedbackVisitor.CustName like concat('%', #{SearchPojo.custname}, '%')
</if>
<if test="SearchPojo.groupid != null ">
   or Sa_FeedbackVisitor.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.phone != null ">
   or Sa_FeedbackVisitor.Phone like concat('%', #{SearchPojo.phone}, '%')
</if>
<if test="SearchPojo.email != null ">
   or Sa_FeedbackVisitor.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.title != null ">
   or Sa_FeedbackVisitor.Title like concat('%', #{SearchPojo.title}, '%')
</if>
<if test="SearchPojo.issue != null ">
   or Sa_FeedbackVisitor.Issue like concat('%', #{SearchPojo.issue}, '%')
</if>
<if test="SearchPojo.attachment != null ">
   or Sa_FeedbackVisitor.Attachment like concat('%', #{SearchPojo.attachment}, '%')
</if>
<if test="SearchPojo.photos != null ">
   or Sa_FeedbackVisitor.Photos like concat('%', #{SearchPojo.photos}, '%')
</if>
<if test="SearchPojo.finishdesc != null ">
   or Sa_FeedbackVisitor.FinishDesc like concat('%', #{SearchPojo.finishdesc}, '%')
</if>
<if test="SearchPojo.engineer != null ">
   or Sa_FeedbackVisitor.Engineer like concat('%', #{SearchPojo.engineer}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_FeedbackVisitor.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_FeedbackVisitor.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_FeedbackVisitor.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_FeedbackVisitor.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_FeedbackVisitor.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_FeedbackVisitor.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_FeedbackVisitor.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_FeedbackVisitor.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_FeedbackVisitor.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_FeedbackVisitor(id, CustName, Groupid, Phone, Email, Title, Issue, Attachment, Photos, SubmitMark, FinishMark, FinishDesc, Engineer, CloseDate, CreateByid, CreateBy, CreateDate, ModifyDate, Listerid, Lister, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{custname}, #{groupid}, #{phone}, #{email}, #{title}, #{issue}, #{attachment}, #{photos}, #{submitmark}, #{finishmark}, #{finishdesc}, #{engineer}, #{closedate}, #{createbyid}, #{createby}, #{createdate}, #{modifydate}, #{listerid}, #{lister}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_FeedbackVisitor
        <set>
            <if test="custname != null ">
                CustName =#{custname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="phone != null ">
                Phone =#{phone},
            </if>
            <if test="email != null ">
                Email =#{email},
            </if>
            <if test="title != null ">
                Title =#{title},
            </if>
            <if test="issue != null ">
                Issue =#{issue},
            </if>
            <if test="attachment != null ">
                Attachment =#{attachment},
            </if>
            <if test="photos != null ">
                Photos =#{photos},
            </if>
            <if test="submitmark != null">
                SubmitMark =#{submitmark},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="finishdesc != null ">
                FinishDesc =#{finishdesc},
            </if>
            <if test="engineer != null ">
                Engineer =#{engineer},
            </if>
            <if test="closedate != null">
                CloseDate =#{closedate},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_FeedbackVisitor where id = #{key} 
    </delete>
</mapper>

