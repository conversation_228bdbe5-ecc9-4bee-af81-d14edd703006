<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaFreepageMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaFreepagePojo">
        <include refid="selectSaFreepageVo"/>
        where Sa_FreePage.id = #{key} 
    </select>
    <sql id="selectSaFreepageVo">
         select
id, RefNo, BillType, <PERSON>Date, BillTitle, ShareKey, PageTemp, PageData, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, <PERSON>anti<PERSON>, TenantName, Revision        from Sa_FreePage
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaFreepagePojo">
        <include refid="selectSaFreepageVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_FreePage.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_FreePage.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_FreePage.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_FreePage.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.sharekey != null ">
   and Sa_FreePage.ShareKey like concat('%', #{SearchPojo.sharekey}, '%')
</if>
<if test="SearchPojo.pagetemp != null ">
   and Sa_FreePage.PageTemp like concat('%', #{SearchPojo.pagetemp}, '%')
</if>
<if test="SearchPojo.pagedata != null ">
   and Sa_FreePage.PageData like concat('%', #{SearchPojo.pagedata}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_FreePage.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_FreePage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_FreePage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_FreePage.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_FreePage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_FreePage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_FreePage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_FreePage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_FreePage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_FreePage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_FreePage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_FreePage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_FreePage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_FreePage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_FreePage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_FreePage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_FreePage.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_FreePage.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_FreePage.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.sharekey != null ">
   or Sa_FreePage.ShareKey like concat('%', #{SearchPojo.sharekey}, '%')
</if>
<if test="SearchPojo.pagetemp != null ">
   or Sa_FreePage.PageTemp like concat('%', #{SearchPojo.pagetemp}, '%')
</if>
<if test="SearchPojo.pagedata != null ">
   or Sa_FreePage.PageData like concat('%', #{SearchPojo.pagedata}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_FreePage.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_FreePage.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_FreePage.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_FreePage.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_FreePage.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_FreePage.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_FreePage.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_FreePage.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_FreePage.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_FreePage.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_FreePage.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_FreePage.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_FreePage.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_FreePage.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_FreePage.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_FreePage.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_FreePage(id, RefNo, BillType, BillDate, BillTitle, ShareKey, PageTemp, PageData, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{sharekey}, #{pagetemp}, #{pagedata}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_FreePage
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="sharekey != null ">
                ShareKey =#{sharekey},
            </if>
            <if test="pagetemp != null ">
                PageTemp =#{pagetemp},
            </if>
            <if test="pagedata != null ">
                PageData =#{pagedata},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_FreePage where id = #{key} 
    </delete>

    <update id="updateShareKey">
        update Sa_FreePage set ShareKey = #{randomMixedString} where id = #{key}
    </update>

    <select id="getEntityByShareKey" resultType="inks.service.sa.pms.domain.pojo.SaFreepagePojo">
        <include refid="selectSaFreepageVo"/>
        where Sa_FreePage.ShareKey = #{sharekey}
    </select>
</mapper>

