<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaGengroupMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaGengroupPojo">
        <include refid="selectSaGengroupVo"/>
        where Sa_GenGroup.id = #{key}
    </select>
    <sql id="selectSaGengroupVo">
        select Sa_GenGroup.id,
               Sa_GenGroup.Parentid,
               Sa_GenGroup.ModuleCode,
               Sa_GenGroup.GroupCode,
               Sa_GenGroup.GroupName,
               Sa_GenGroup.Velocityid,
               Sa_GenGroup.DirMark,
               Sa_GenGroup.EnabledMark,
               Sa_GenGroup.RowNum,
               Sa_GenGroup.Remark,
               Sa_GenGroup.Lister,
               Sa_GenGroup.CreateDate,
               Sa_GenGroup.ModifyDate,
               Sa_GenGroup.Tenantid,
               Sa_GenVelocity.Name as velocityname
        from Sa_GenGroup
                 left join Sa_GenVelocity on Sa_GenGroup.Velocityid = Sa_GenVelocity.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaGengroupPojo">
        <include refid="selectSaGengroupVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_GenGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.parentid != null">
            and Sa_GenGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Sa_GenGroup.ModuleCode like concat('%',
            #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.groupcode != null">
            and Sa_GenGroup.GroupCode like concat('%',
            #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.groupname != null">
            and Sa_GenGroup.GroupName like concat('%',
            #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_GenGroup.Remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_GenGroup.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.parentid != null">
                or Sa_GenGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Sa_GenGroup.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.groupcode != null">
                or Sa_GenGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
            </if>
            <if test="SearchPojo.groupname != null">
                or Sa_GenGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_GenGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_GenGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_GenGroup(id, Parentid, ModuleCode, GroupCode, GroupName, Velocityid, DirMark, EnabledMark,
        RowNum, Remark, Lister, CreateDate, ModifyDate, Tenantid)
        values (#{id}, #{parentid}, #{modulecode}, #{groupcode}, #{groupname}, #{velocityid}, #{dirmark},
        #{enabledmark}, #{rownum}, #{remark}, #{lister}, #{createdate}, #{modifydate}, #{tenantid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_GenGroup
        <set>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="groupcode != null">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null">
                GroupName =#{groupname},
            </if>
            <if test="velocityid != null">
                Velocityid =#{velocityid},
            </if>
            DirMark =#{dirmark},
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_GenGroup
        where id = #{key}
    </delete>

    <select id="getAllList" resultType="inks.service.sa.pms.domain.pojo.SaGengroupPojo">
        select *
        from Sa_GenGroup
    </select>

    <select id="getVelocityids" resultType="java.lang.String">
        select Velocityid
        from Sa_GenGroup where Velocityid is not null and Velocityid <![CDATA[<>]]> ''
        and id in
        <foreach collection="groupids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>

