<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaGenvelocityMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaGenvelocityPojo">
        select id,
        Name,
        Velocity,
        GenGroupid,
        Revion,
        RowNum,
        Assessor,
        Assessorid,
        AssessDate,
        CreateByid,
        CreateBy,
        CreateDate,
        Listerid,
        Lister,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Tenantid,
        Revision
        from Sa_GenVelocity
        where Sa_GenVelocity.id = #{key}
    </select>
    <sql id="selectSaGenvelocityVo">
        select id,
               Name,
               Velocity,
               GenGroupid,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON>s<PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               <PERSON><PERSON>d,
               Revision
        from Sa_GenVelocity
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaGenvelocityPojo">
        <include refid="selectSaGenvelocityVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_GenVelocity.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.name != null">
            and Sa_GenVelocity.Name like concat('%', #{SearchPojo.name}, '%')
        </if>
        <if test="SearchPojo.velocity != null">
            and Sa_GenVelocity.Velocity like concat('%', #{SearchPojo.velocity}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_GenVelocity.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_GenVelocity.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_GenVelocity.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_GenVelocity.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_GenVelocity.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_GenVelocity.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_GenVelocity.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_GenVelocity.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_GenVelocity.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.name != null">
                or Sa_GenVelocity.Name like concat('%', #{SearchPojo.name}, '%')
            </if>
            <if test="SearchPojo.velocity != null">
                or Sa_GenVelocity.Velocity like concat('%', #{SearchPojo.velocity}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_GenVelocity.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_GenVelocity.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_GenVelocity.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_GenVelocity.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_GenVelocity.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_GenVelocity.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_GenVelocity.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_GenVelocity.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_GenVelocity.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_GenVelocity(id, Name, Velocity,GenGroupid, Revion, RowNum, Assessor, Assessorid, AssessDate,
        CreateByid, CreateBy, CreateDate, Listerid,
        Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{name},#{gengroupid}, #{velocity}, #{revion}, #{rownum}, #{assessor}, #{assessorid},
        #{assessdate}, #{createbyid}, #{createby}, #{createdate},
        #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
        #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_GenVelocity
        <set>
            <if test="name != null">
                Name =#{name},
            </if>
            <if test="velocity != null">
                Velocity =#{velocity},
            </if>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="revion != null">
                Revion =#{revion},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_GenVelocity
        where id = #{key}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_GenVelocity
        SET Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
    </update>


    <select id="getAllVelocity" resultType="java.lang.String">
        select Velocity
        from Sa_GenVelocity
    </select>
    <select id="getAllEntity" resultType="inks.service.sa.pms.domain.pojo.SaGenvelocityPojo">
        select *
        from Sa_GenVelocity
    </select>
    <select id="databases" resultType="java.lang.String">
        SHOW DATABASES
    </select>
    <select id="table原始" resultType="java.util.Map">
        SELECT
        table_name AS tablename,
        table_comment AS tablecomment,
        create_time AS createtime,
        update_time AS updatetime
        FROM
        information_schema.TABLES
        WHERE
        table_name NOT LIKE 'qrtz_%'
        AND table_name NOT LIKE 'gen_%'
        AND table_schema = #{database}
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and222"/>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or222"/>
            </if>
        </if>
    </select>
    <select id="table" resultType="java.util.Map">
        SELECT
        distinct t1.table_name AS tablename,
        CASE WHEN t2.table_name IS NULL THEN NULL
        ELSE CONCAT(t2.table_name) END AS tableitemname,
        t1.table_comment AS tablecomment,
        t1.create_time AS createtime,
        t1.update_time AS updatetime
        FROM
        information_schema.TABLES t1
        LEFT JOIN information_schema.TABLES t2 ON CONCAT(t1.table_name, 'Item') = t2.table_name
        WHERE t1.table_schema = #{database}
        AND t1.table_name NOT LIKE '%Item'
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="and222"/>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="or222"/>
            </if>
        </if>
    </select>
    <sql id="and222">
        <if test="queryParam.SearchPojo.tablename != null">
            and LOWER(t1.table_name) LIKE concat('%', LOWER(#{queryParam.SearchPojo.tablename}), '%')
        </if>
    </sql>
    <sql id="or222">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="queryParam.SearchPojo.tablename != null">
                or LOWER(t1.table_name) LIKE concat('%', LOWER(#{queryParam.SearchPojo.tablename}), '%')
            </if>
        </trim>
    </sql>

    <!-- 查询一个数据表的所有字段信息的SQL语句 -->
    <select id="getTableFields" resultType="java.util.Map">
        SELECT
        column_name AS fieldName,
        column_comment AS fieldComment,
        column_type AS fieldType
        FROM
        information_schema.COLUMNS
        WHERE table_schema = #{database}
        and table_name = #{tableName}
    </select>


    <select id="getListInName" resultType="inks.service.sa.pms.domain.pojo.SaGenvelocityPojo">
        SELECT * FROM Sa_GenVelocity
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
</mapper>

