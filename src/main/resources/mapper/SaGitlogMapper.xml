<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaGitlogMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaGitlogPojo">
        <include refid="selectSaGitlogVo"/>
        where Sa_GitLog.id = #{key}
    </select>
    <sql id="selectSaGitlogVo">
        select
            id, EventName, UserName, ProjectName, ProjectUrl, ProjectDesc, ProjectNameSpace, CommitMessage, CommitDate, CommitAuthor, CommitEmail, Remark, CreateBy, CreateByid, CreateDate, Lister, <PERSON>erid, <PERSON>difyDate, Tenantid, TenantName, Revision        from Sa_GitLog
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaGitlogPojo">
        <include refid="selectSaGitlogVo"/>
        where 1 = 1
        <if test="filterstr != null ">
            ${filterstr}
        </if>
        <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                and Sa_GitLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null ">
            <if test="SearchType==0">
                <include refid="and"></include>
            </if>
            <if test="SearchType==1">
                <include refid="or"></include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
    <sql id="and">
        <if test="SearchPojo.eventname != null ">
            and Sa_GitLog.EventName like concat('%', #{SearchPojo.eventname}, '%')
        </if>
        <if test="SearchPojo.username != null ">
            and Sa_GitLog.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.projectname != null ">
            and Sa_GitLog.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.projecturl != null ">
            and Sa_GitLog.ProjectUrl like concat('%', #{SearchPojo.projecturl}, '%')
        </if>
        <if test="SearchPojo.projectdesc != null ">
            and Sa_GitLog.ProjectDesc like concat('%', #{SearchPojo.projectdesc}, '%')
        </if>
        <if test="SearchPojo.projectnamespace != null ">
            and Sa_GitLog.ProjectNameSpace like concat('%', #{SearchPojo.projectnamespace}, '%')
        </if>
        <if test="SearchPojo.commitmessage != null ">
            and Sa_GitLog.CommitMessage like concat('%', #{SearchPojo.commitmessage}, '%')
        </if>
        <if test="SearchPojo.commitauthor != null ">
            and Sa_GitLog.CommitAuthor like concat('%', #{SearchPojo.commitauthor}, '%')
        </if>
        <if test="SearchPojo.commitemail != null ">
            and Sa_GitLog.CommitEmail like concat('%', #{SearchPojo.commitemail}, '%')
        </if>
        <if test="SearchPojo.remark != null ">
            and Sa_GitLog.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null ">
            and Sa_GitLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null ">
            and Sa_GitLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null ">
            and Sa_GitLog.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null ">
            and Sa_GitLog.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null ">
            and Sa_GitLog.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.eventname != null ">
                or Sa_GitLog.EventName like concat('%', #{SearchPojo.eventname}, '%')
            </if>
            <if test="SearchPojo.username != null ">
                or Sa_GitLog.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.projectname != null ">
                or Sa_GitLog.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
            </if>
            <if test="SearchPojo.projecturl != null ">
                or Sa_GitLog.ProjectUrl like concat('%', #{SearchPojo.projecturl}, '%')
            </if>
            <if test="SearchPojo.projectdesc != null ">
                or Sa_GitLog.ProjectDesc like concat('%', #{SearchPojo.projectdesc}, '%')
            </if>
            <if test="SearchPojo.projectnamespace != null ">
                or Sa_GitLog.ProjectNameSpace like concat('%', #{SearchPojo.projectnamespace}, '%')
            </if>
            <if test="SearchPojo.commitmessage != null ">
                or Sa_GitLog.CommitMessage like concat('%', #{SearchPojo.commitmessage}, '%')
            </if>
            <if test="SearchPojo.commitauthor != null ">
                or Sa_GitLog.CommitAuthor like concat('%', #{SearchPojo.commitauthor}, '%')
            </if>
            <if test="SearchPojo.commitemail != null ">
                or Sa_GitLog.CommitEmail like concat('%', #{SearchPojo.commitemail}, '%')
            </if>
            <if test="SearchPojo.remark != null ">
                or Sa_GitLog.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null ">
                or Sa_GitLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null ">
                or Sa_GitLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null ">
                or Sa_GitLog.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null ">
                or Sa_GitLog.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null ">
                or Sa_GitLog.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_GitLog(id, EventName, UserName, ProjectName, ProjectUrl, ProjectDesc, ProjectNameSpace, CommitMessage, CommitDate, CommitAuthor, CommitEmail, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{id}, #{eventname}, #{username}, #{projectname}, #{projecturl}, #{projectdesc}, #{projectnamespace}, #{commitmessage}, #{commitdate}, #{commitauthor}, #{commitemail}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_GitLog
        <set>
            <if test="eventname != null ">
                EventName =#{eventname},
            </if>
            <if test="username != null ">
                UserName =#{username},
            </if>
            <if test="projectname != null ">
                ProjectName =#{projectname},
            </if>
            <if test="projecturl != null ">
                ProjectUrl =#{projecturl},
            </if>
            <if test="projectdesc != null ">
                ProjectDesc =#{projectdesc},
            </if>
            <if test="projectnamespace != null ">
                ProjectNameSpace =#{projectnamespace},
            </if>
            <if test="commitmessage != null ">
                CommitMessage =#{commitmessage},
            </if>
            <if test="commitdate != null">
                CommitDate =#{commitdate},
            </if>
            <if test="commitauthor != null ">
                CommitAuthor =#{commitauthor},
            </if>
            <if test="commitemail != null ">
                CommitEmail =#{commitemail},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_GitLog where id = #{key}
    </delete>
</mapper>

