<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaGoodsMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaGoodsPojo">
        select id,
        GenGroupid,
        GoodsCode,
        GoodsName,
        GoodsSpec,
        GoodsUnit,
        GoodsPrice,
        GoodsCost,
        Desciption,
        EnabledMark,
        GoodsPhote,
        Author,
        MarkdownData,
        MdUrl,
        MdLookTimes,
        AppUrl,
        DescJson,
        Rateval,
        RowNum,
        Remark,
        PublicMark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Custom6,
        Custom7,
        Custom8,
        Custom9,
        Custom10,
        Tenantid,
        TenantName,
        Revision
        from Sa_PmsGoods
        where Sa_PmsGoods.id = #{key}
    </select>
    <sql id="selectSaGoodsVo">
        select id,
               GenGroupid,
               GoodsCode,
               GoodsName,
               GoodsSpec,
               GoodsUnit,
               GoodsPrice,
               GoodsCost,
               Desciption,
               EnabledMark,
               GoodsPhote,
               Author,
               MarkdownData,
               MdUrl,
               MdLookTimes,
               AppUrl,
               DescJson,
               Rateval,
               RowNum,
               Remark,
               PublicMark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Sa_PmsGoods
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaGoodsPojo">
        <include refid="selectSaGoodsVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_PmsGoods.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null">
            and Sa_PmsGoods.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.goodscode != null">
            and Sa_PmsGoods.GoodsCode like concat('%',
            #{SearchPojo.goodscode}, '%')
        </if>
        <if test="SearchPojo.goodsname != null">
            and Sa_PmsGoods.GoodsName like concat('%',
            #{SearchPojo.goodsname}, '%')
        </if>
        <if test="SearchPojo.goodsspec != null">
            and Sa_PmsGoods.GoodsSpec like concat('%',
            #{SearchPojo.goodsspec}, '%')
        </if>
        <if test="SearchPojo.goodsunit != null">
            and Sa_PmsGoods.GoodsUnit like concat('%',
            #{SearchPojo.goodsunit}, '%')
        </if>
        <if test="SearchPojo.desciption != null">
            and Sa_PmsGoods.Desciption like concat('%',
            #{SearchPojo.desciption}, '%')
        </if>
        <if test="SearchPojo.goodsphote != null">
            and Sa_PmsGoods.GoodsPhote like concat('%',
            #{SearchPojo.goodsphote}, '%')
        </if>
        <if test="SearchPojo.author != null">
            and Sa_PmsGoods.Author like concat('%',
            #{SearchPojo.author}, '%')
        </if>
        <if test="SearchPojo.markdowndata != null">
            and Sa_PmsGoods.MarkdownData like concat('%',
            #{SearchPojo.markdowndata}, '%')
        </if>
        <if test="SearchPojo.mdurl != null">
            and Sa_PmsGoods.MdUrl like concat('%',
            #{SearchPojo.mdurl}, '%')
        </if>
        <if test="SearchPojo.appurl != null">
            and Sa_PmsGoods.AppUrl like concat('%',
            #{SearchPojo.appurl}, '%')
        </if>
        <if test="SearchPojo.descjson != null">
            and Sa_PmsGoods.DescJson like concat('%',
            #{SearchPojo.descjson}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_PmsGoods.Remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_PmsGoods.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_PmsGoods.CreateByid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_PmsGoods.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_PmsGoods.Listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_PmsGoods.Custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_PmsGoods.Custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_PmsGoods.Custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_PmsGoods.Custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_PmsGoods.Custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_PmsGoods.Custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_PmsGoods.Custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_PmsGoods.Custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_PmsGoods.Custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_PmsGoods.Custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_PmsGoods.TenantName like concat('%',
            #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null">
                or Sa_PmsGoods.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.goodscode != null">
                or Sa_PmsGoods.GoodsCode like concat('%', #{SearchPojo.goodscode}, '%')
            </if>
            <if test="SearchPojo.goodsname != null">
                or Sa_PmsGoods.GoodsName like concat('%', #{SearchPojo.goodsname}, '%')
            </if>
            <if test="SearchPojo.goodsspec != null">
                or Sa_PmsGoods.GoodsSpec like concat('%', #{SearchPojo.goodsspec}, '%')
            </if>
            <if test="SearchPojo.goodsunit != null">
                or Sa_PmsGoods.GoodsUnit like concat('%', #{SearchPojo.goodsunit}, '%')
            </if>
            <if test="SearchPojo.desciption != null">
                or Sa_PmsGoods.Desciption like concat('%', #{SearchPojo.desciption}, '%')
            </if>
            <if test="SearchPojo.goodsphote != null">
                or Sa_PmsGoods.GoodsPhote like concat('%', #{SearchPojo.goodsphote}, '%')
            </if>
            <if test="SearchPojo.author != null">
                or Sa_PmsGoods.Author like concat('%', #{SearchPojo.author}, '%')
            </if>
            <if test="SearchPojo.markdowndata != null">
                or Sa_PmsGoods.MarkdownData like concat('%', #{SearchPojo.markdowndata}, '%')
            </if>
            <if test="SearchPojo.mdurl != null">
                or Sa_PmsGoods.MdUrl like concat('%', #{SearchPojo.mdurl}, '%')
            </if>
            <if test="SearchPojo.appurl != null">
                or Sa_PmsGoods.AppUrl like concat('%', #{SearchPojo.appurl}, '%')
            </if>
            <if test="SearchPojo.descjson != null">
                or Sa_PmsGoods.DescJson like concat('%', #{SearchPojo.descjson}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_PmsGoods.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_PmsGoods.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_PmsGoods.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_PmsGoods.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_PmsGoods.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_PmsGoods.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_PmsGoods.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_PmsGoods.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_PmsGoods.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_PmsGoods.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_PmsGoods.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_PmsGoods.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_PmsGoods.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_PmsGoods.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_PmsGoods.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_PmsGoods.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_PmsGoods(id, GenGroupid, GoodsCode, GoodsName, GoodsSpec, GoodsUnit, GoodsPrice, GoodsCost,
        Desciption, EnabledMark, GoodsPhote, Author, MarkdownData, MdUrl, MdLookTimes, AppUrl,
        DescJson, Rateval, RowNum, Remark, PublicMark, CreateBy, CreateByid, CreateDate, Lister,
        Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7,
        Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{goodscode}, #{goodsname}, #{goodsspec}, #{goodsunit}, #{goodsprice},
        #{goodscost}, #{desciption}, #{enabledmark}, #{goodsphote}, #{author}, #{markdowndata}, #{mdurl},
        #{mdlooktimes}, #{appurl}, #{descjson}, #{rateval}, #{rownum}, #{remark}, #{publicmark}, #{createby},
        #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3},
        #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},
        #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_PmsGoods
        <set>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="goodscode != null">
                GoodsCode =#{goodscode},
            </if>
            <if test="goodsname != null">
                GoodsName =#{goodsname},
            </if>
            <if test="goodsspec != null">
                GoodsSpec =#{goodsspec},
            </if>
            <if test="goodsunit != null">
                GoodsUnit =#{goodsunit},
            </if>
            <if test="goodsprice != null">
                GoodsPrice =#{goodsprice},
            </if>
            <if test="goodscost != null">
                GoodsCost =#{goodscost},
            </if>
            <if test="desciption != null">
                Desciption =#{desciption},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="goodsphote != null">
                GoodsPhote =#{goodsphote},
            </if>
            <if test="author != null">
                Author =#{author},
            </if>
            <if test="markdowndata != null">
                MarkdownData =#{markdowndata},
            </if>
            <if test="mdurl != null">
                MdUrl =#{mdurl},
            </if>
            <if test="mdlooktimes != null">
                MdLookTimes =#{mdlooktimes},
            </if>
            <if test="appurl != null">
                AppUrl =#{appurl},
            </if>
            <if test="descjson != null">
                DescJson =#{descjson},
            </if>
            <if test="rateval != null">
                Rateval =#{rateval},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_PmsGoods
        where id = #{key}
    </delete>

    <select id="getEntityByGoodsCode" resultType="inks.service.sa.pms.domain.pojo.SaGoodsPojo">
        <include refid="selectSaGoodsVo"/>
        where Sa_PmsGoods.GoodsCode = #{goodscode}
    </select>
</mapper>

