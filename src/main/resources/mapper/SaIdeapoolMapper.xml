<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaIdeapoolMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaIdeapoolPojo">
        <include refid="selectSaIdeapoolVo"/>
        where Sa_IdeaPool.id = #{key} 
    </select>
    <sql id="selectSaIdeapoolVo">
         select
id, RefNo, BillDate, IdeaTitle, IdeaType, IdeaJson, PublicMark, FinishMark, Operator, Operatorid, ItemCount, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, <PERSON>erid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON><PERSON><PERSON>, Tenantid, TenantName, Revision        from Sa_IdeaPool
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaIdeapoolPojo">
        <include refid="selectSaIdeapoolVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_IdeaPool.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_IdeaPool.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.ideatitle != null ">
   and Sa_IdeaPool.IdeaTitle like concat('%', #{SearchPojo.ideatitle}, '%')
</if>
<if test="SearchPojo.ideatype != null ">
   and Sa_IdeaPool.IdeaType like concat('%', #{SearchPojo.ideatype}, '%')
</if>
<if test="SearchPojo.ideajson != null ">
   and Sa_IdeaPool.IdeaJson like concat('%', #{SearchPojo.ideajson}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_IdeaPool.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_IdeaPool.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_IdeaPool.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_IdeaPool.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_IdeaPool.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_IdeaPool.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_IdeaPool.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_IdeaPool.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_IdeaPool.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_IdeaPool.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_IdeaPool.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_IdeaPool.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_IdeaPool.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_IdeaPool.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.ideatitle != null ">
   or Sa_IdeaPool.IdeaTitle like concat('%', #{SearchPojo.ideatitle}, '%')
</if>
<if test="SearchPojo.ideatype != null ">
   or Sa_IdeaPool.IdeaType like concat('%', #{SearchPojo.ideatype}, '%')
</if>
<if test="SearchPojo.ideajson != null ">
   or Sa_IdeaPool.IdeaJson like concat('%', #{SearchPojo.ideajson}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_IdeaPool.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_IdeaPool.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_IdeaPool.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_IdeaPool.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_IdeaPool.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_IdeaPool.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_IdeaPool.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_IdeaPool.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_IdeaPool.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_IdeaPool.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_IdeaPool.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_IdeaPool.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_IdeaPool.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_IdeaPool(id, RefNo, BillDate, IdeaTitle, IdeaType, IdeaJson, PublicMark, FinishMark, Operator, Operatorid, ItemCount, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billdate}, #{ideatitle}, #{ideatype}, #{ideajson}, #{publicmark}, #{finishmark}, #{operator}, #{operatorid}, #{itemcount}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_IdeaPool
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="ideatitle != null ">
                IdeaTitle =#{ideatitle},
            </if>
            <if test="ideatype != null ">
                IdeaType =#{ideatype},
            </if>
            <if test="ideajson != null ">
                IdeaJson =#{ideajson},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_IdeaPool where id = #{key} 
    </delete>

    <select id="getAllList" resultType="inks.service.sa.pms.domain.pojo.SaIdeapoolPojo">
        <include refid="selectSaIdeapoolVo"/>
        order by RowNum
    </select>
</mapper>

