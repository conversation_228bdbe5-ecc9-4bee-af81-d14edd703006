<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaImplementplanMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaImplementplanPojo">
        <include refid="selectbillVo"/>
        where Sa_ImplementPlan.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, Name, Principalid, Principal, CompletionRate, Deadline, Closed, OnlineMode, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_ImplementPlan
    </sql>
    <sql id="selectdetailVo">
         select
id, Name, Principalid, Principal, CompletionRate, Deadline, Closed, OnlineMode, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_ImplementPlan
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaImplementplanitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_ImplementPlan.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.name != null ">
   and Sa_ImplementPlan.name like concat('%', #{SearchPojo.name}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   and Sa_ImplementPlan.principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   and Sa_ImplementPlan.principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.onlinemode != null ">
   and Sa_ImplementPlan.onlinemode like concat('%', #{SearchPojo.onlinemode}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_ImplementPlan.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_ImplementPlan.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_ImplementPlan.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_ImplementPlan.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_ImplementPlan.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_ImplementPlan.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_ImplementPlan.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_ImplementPlan.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_ImplementPlan.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.name != null ">
   or Sa_ImplementPlan.Name like concat('%', #{SearchPojo.name}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   or Sa_ImplementPlan.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   or Sa_ImplementPlan.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.onlinemode != null ">
   or Sa_ImplementPlan.OnlineMode like concat('%', #{SearchPojo.onlinemode}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_ImplementPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_ImplementPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_ImplementPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_ImplementPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_ImplementPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_ImplementPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_ImplementPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_ImplementPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_ImplementPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaImplementplanPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_ImplementPlan.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.name != null ">
   and Sa_ImplementPlan.Name like concat('%', #{SearchPojo.name}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   and Sa_ImplementPlan.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   and Sa_ImplementPlan.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.onlinemode != null ">
   and Sa_ImplementPlan.OnlineMode like concat('%', #{SearchPojo.onlinemode}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_ImplementPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_ImplementPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_ImplementPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_ImplementPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_ImplementPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_ImplementPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_ImplementPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_ImplementPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_ImplementPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.name != null ">
   or Sa_ImplementPlan.Name like concat('%', #{SearchPojo.name}, '%')
</if>
<if test="SearchPojo.principalid != null ">
   or Sa_ImplementPlan.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.principal != null ">
   or Sa_ImplementPlan.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.onlinemode != null ">
   or Sa_ImplementPlan.OnlineMode like concat('%', #{SearchPojo.onlinemode}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_ImplementPlan.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_ImplementPlan.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_ImplementPlan.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_ImplementPlan.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_ImplementPlan.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_ImplementPlan.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_ImplementPlan.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_ImplementPlan.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_ImplementPlan.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_ImplementPlan(id, Name, Principalid, Principal, CompletionRate, Deadline, Closed, OnlineMode, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{name}, #{principalid}, #{principal}, #{completionrate}, #{deadline}, #{closed}, #{onlinemode}, #{rownum}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ImplementPlan
        <set>
            <if test="name != null ">
                Name =#{name},
            </if>
            <if test="principalid != null ">
                Principalid =#{principalid},
            </if>
            <if test="principal != null ">
                Principal =#{principal},
            </if>
            <if test="completionrate != null">
                CompletionRate =#{completionrate},
            </if>
            <if test="deadline != null">
                Deadline =#{deadline},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="onlinemode != null ">
                OnlineMode =#{onlinemode},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ImplementPlan where id = #{key} 
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.pms.domain.pojo.SaImplementplanPojo">
        select
          id
        from Sa_ImplementPlanItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

</mapper>

