<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaIntroMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaIntroPojo">
        select id,
        GenGroupid,
        ModuleCode,
        IntroName,
        IntroContent,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate
        from Sa_Intro
        where Sa_Intro.id = #{key}
    </select>
    <sql id="selectSaIntroVo">
        select id,
               GenGroupid,
               ModuleCode,
               IntroName,
               IntroContent,
               Remark,
               CreateBy,
               CreateByid,
               <PERSON>reate<PERSON>ate,
               <PERSON>er,
               <PERSON>erid,
               ModifyDate
        from Sa_Intro
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaIntroPojo">
        <include refid="selectSaIntroVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Intro.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null">
            and Sa_Intro.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Sa_Intro.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.introname != null">
            and Sa_Intro.IntroName like concat('%', #{SearchPojo.introname}, '%')
        </if>
        <if test="SearchPojo.introcontent != null">
            and Sa_Intro.IntroContent like concat('%', #{SearchPojo.introcontent}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Intro.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Intro.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Intro.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Intro.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Intro.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null">
                or Sa_Intro.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Sa_Intro.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.introname != null">
                or Sa_Intro.IntroName like concat('%', #{SearchPojo.introname}, '%')
            </if>
            <if test="SearchPojo.introcontent != null">
                or Sa_Intro.IntroContent like concat('%', #{SearchPojo.introcontent}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Intro.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Intro.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Intro.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Intro.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Intro.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Intro(id, GenGroupid, ModuleCode, IntroName, IntroContent, Remark, CreateBy, CreateByid,
        CreateDate, Lister, Listerid, ModifyDate)
        values (#{id}, #{gengroupid}, #{modulecode}, #{introname}, #{introcontent}, #{remark}, #{createby},
        #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Intro
        <set>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="introname != null">
                IntroName =#{introname},
            </if>
            <if test="introcontent != null">
                IntroContent =#{introcontent},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Intro
        where id = #{key}
    </delete>

    <select id="getEntityByCode" resultType="inks.service.sa.pms.domain.pojo.SaIntroPojo">
        select id,
        GenGroupid,
        ModuleCode,
        IntroName,
        IntroContent,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate
        from CiIntro
        where CiIntro.ModuleCode = #{code}
        Order by ModifyDate Desc LIMIT 1
    </select>
</mapper>

