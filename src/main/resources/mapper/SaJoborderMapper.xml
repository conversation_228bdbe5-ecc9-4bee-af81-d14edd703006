<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaJoborderMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaJoborderPojo">
        <include refid="selectSaJoborderVo"/>
        where Sa_JobOrder.id = #{key}
    </select>
    <sql id="selectSaJoborderVo">
         select
id, RefNo, BillType, BillDate, BillTitle, Projectid, ItemCode, ItemName, GroupName, Groupid, Linkman, Telephone, SerAdd, SerDate, SerClass, Operator, Operatorid, SerContent, SerProcess, SerLoss, Confidential, MatJson, GenGroupid, Score1, Score2, Score3, LicenseP<PERSON>, <PERSON>age, Attachment, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, GroupCode, DisannulMark, FinishMark, ItemCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Sa_JobOrder
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaJoborderPojo">
        <include refid="selectSaJoborderVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_JobOrder.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.refno != null">
            and Sa_JobOrder.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Sa_JobOrder.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Sa_JobOrder.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.projectid != null">
            and Sa_JobOrder.Projectid like concat('%', #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.itemcode != null">
            and Sa_JobOrder.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
        </if>
        <if test="SearchPojo.itemname != null">
            and Sa_JobOrder.ItemName like concat('%', #{SearchPojo.itemname}, '%')
        </if>
        <if test="SearchPojo.groupname != null">
            and Sa_JobOrder.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
<if test="SearchPojo.groupid != null ">
   and Sa_JobOrder.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.linkman != null ">
            and Sa_JobOrder.Linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.telephone != null">
            and Sa_JobOrder.Telephone like concat('%', #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.seradd != null">
            and Sa_JobOrder.SerAdd like concat('%', #{SearchPojo.seradd}, '%')
        </if>
        <if test="SearchPojo.serclass != null">
            and Sa_JobOrder.SerClass like concat('%', #{SearchPojo.serclass}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_JobOrder.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.operatorid != null">
            and Sa_JobOrder.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
        </if>
        <if test="SearchPojo.sercontent != null">
            and Sa_JobOrder.SerContent like concat('%', #{SearchPojo.sercontent}, '%')
        </if>
        <if test="SearchPojo.serprocess != null">
            and Sa_JobOrder.SerProcess like concat('%', #{SearchPojo.serprocess}, '%')
        </if>
        <if test="SearchPojo.serloss != null">
            and Sa_JobOrder.SerLoss like concat('%', #{SearchPojo.serloss}, '%')
        </if>
        <if test="SearchPojo.confidential != null">
            and Sa_JobOrder.Confidential like concat('%', #{SearchPojo.confidential}, '%')
        </if>
        <if test="SearchPojo.matjson != null">
            and Sa_JobOrder.MatJson like concat('%', #{SearchPojo.matjson}, '%')
        </if>
        <if test="SearchPojo.gengroupid != null">
            and Sa_JobOrder.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.licenseplate != null">
            and Sa_JobOrder.LicensePlate like concat('%', #{SearchPojo.licenseplate}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_JobOrder.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_JobOrder.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_JobOrder.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_JobOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_JobOrder.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_JobOrder.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_JobOrder.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.groupcode != null">
            and Sa_JobOrder.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_JobOrder.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_JobOrder.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_JobOrder.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_JobOrder.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_JobOrder.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_JobOrder.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_JobOrder.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_JobOrder.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_JobOrder.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_JobOrder.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_JobOrder.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.refno != null">
                or Sa_JobOrder.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Sa_JobOrder.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Sa_JobOrder.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.projectid != null">
                or Sa_JobOrder.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.itemcode != null">
                or Sa_JobOrder.ItemCode like concat('%', #{SearchPojo.itemcode}, '%')
            </if>
            <if test="SearchPojo.itemname != null">
                or Sa_JobOrder.ItemName like concat('%', #{SearchPojo.itemname}, '%')
            </if>
            <if test="SearchPojo.groupname != null">
                or Sa_JobOrder.GroupName like concat('%', #{SearchPojo.groupname}, '%')
            </if>
<if test="SearchPojo.groupid != null ">
   or Sa_JobOrder.Groupid like concat('%', #{SearchPojo.groupid}, '%')
</if>
<if test="SearchPojo.linkman != null ">
                or Sa_JobOrder.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.telephone != null">
                or Sa_JobOrder.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.seradd != null">
                or Sa_JobOrder.SerAdd like concat('%', #{SearchPojo.seradd}, '%')
            </if>
            <if test="SearchPojo.serclass != null">
                or Sa_JobOrder.SerClass like concat('%', #{SearchPojo.serclass}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_JobOrder.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.operatorid != null">
                or Sa_JobOrder.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
            </if>
            <if test="SearchPojo.sercontent != null">
                or Sa_JobOrder.SerContent like concat('%', #{SearchPojo.sercontent}, '%')
            </if>
            <if test="SearchPojo.serprocess != null">
                or Sa_JobOrder.SerProcess like concat('%', #{SearchPojo.serprocess}, '%')
            </if>
            <if test="SearchPojo.serloss != null">
                or Sa_JobOrder.SerLoss like concat('%', #{SearchPojo.serloss}, '%')
            </if>
            <if test="SearchPojo.confidential != null">
                or Sa_JobOrder.Confidential like concat('%', #{SearchPojo.confidential}, '%')
            </if>
            <if test="SearchPojo.matjson != null">
                or Sa_JobOrder.MatJson like concat('%', #{SearchPojo.matjson}, '%')
            </if>
            <if test="SearchPojo.gengroupid != null">
                or Sa_JobOrder.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.licenseplate != null">
                or Sa_JobOrder.LicensePlate like concat('%', #{SearchPojo.licenseplate}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_JobOrder.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_JobOrder.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_JobOrder.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_JobOrder.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_JobOrder.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_JobOrder.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_JobOrder.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.groupcode != null">
                or Sa_JobOrder.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_JobOrder.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_JobOrder.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_JobOrder.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_JobOrder.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_JobOrder.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_JobOrder.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_JobOrder.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_JobOrder.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_JobOrder.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_JobOrder.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_JobOrder.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_JobOrder(id, RefNo, BillType, BillDate, BillTitle, Projectid, ItemCode, ItemName, GroupName, Groupid, Linkman, Telephone, SerAdd, SerDate, SerClass, Operator, Operatorid, SerContent, SerProcess, SerLoss, Confidential, MatJson, GenGroupid, Score1, Score2, Score3, LicensePlate, Mileage, Attachment, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Assessor, Assessorid, AssessDate, GroupCode, DisannulMark, FinishMark, ItemCount, PrintCount, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtype}, #{billdate}, #{billtitle}, #{projectid}, #{itemcode}, #{itemname}, #{groupname}, #{groupid}, #{linkman}, #{telephone}, #{seradd}, #{serdate}, #{serclass}, #{operator}, #{operatorid}, #{sercontent}, #{serprocess}, #{serloss}, #{confidential}, #{matjson}, #{gengroupid}, #{score1}, #{score2}, #{score3}, #{licenseplate}, #{mileage}, #{attachment}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate}, #{groupcode}, #{disannulmark}, #{finishmark}, #{itemcount}, #{printcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_JobOrder
        <set>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="itemcode != null">
                ItemCode =#{itemcode},
            </if>
            <if test="itemname != null">
                ItemName =#{itemname},
            </if>
            <if test="groupname != null">
                GroupName =#{groupname},
            </if>
            <if test="groupid != null ">
                Groupid =#{groupid},
            </if>
            <if test="linkman != null">
                Linkman =#{linkman},
            </if>
            <if test="telephone != null">
                Telephone =#{telephone},
            </if>
            <if test="seradd != null">
                SerAdd =#{seradd},
            </if>
            <if test="serdate != null">
                SerDate =#{serdate},
            </if>
            <if test="serclass != null">
                SerClass =#{serclass},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="operatorid != null">
                Operatorid =#{operatorid},
            </if>
            <if test="sercontent != null">
                SerContent =#{sercontent},
            </if>
            <if test="serprocess != null">
                SerProcess =#{serprocess},
            </if>
            <if test="serloss != null">
                SerLoss =#{serloss},
            </if>
            <if test="confidential != null">
                Confidential =#{confidential},
            </if>
            <if test="matjson != null">
                MatJson =#{matjson},
            </if>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="score1 != null">
                Score1 =#{score1},
            </if>
            <if test="score2 != null">
                Score2 =#{score2},
            </if>
            <if test="score3 != null">
                Score3 =#{score3},
            </if>
            <if test="licenseplate != null">
                LicensePlate =#{licenseplate},
            </if>
            <if test="mileage != null">
                Mileage =#{mileage},
            </if>
            <if test="attachment != null ">
                Attachment =#{attachment},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="groupcode != null">
                GroupCode =#{groupcode},
            </if>
            <if test="disannulmark != null">
                DisannulMark =#{disannulmark},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="printcount != null">
                PrintCount =#{printcount},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_JobOrder
        where id = #{key}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_JobOrder
        SET Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
    </update>
</mapper>

