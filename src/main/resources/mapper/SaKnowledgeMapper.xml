<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaKnowledgeMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaKnowledgePojo">
        select id,
        GenGroupid,
        RefNo,
        BillType,
        BillTitle,
        BillDate,
        KnowUrl,
        VideoUrl,
        TextGoodNum,
        TextNgNum,
        TextLookTimes,
        TextLevel,
        FrontPhoto,
        PublicMark,
        ReleaseMark,
        RowNum,
        BucketName,
        DirName,
        FileName,
        FileSize,
        ContentType,
        Storage,
        Relateid,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        <PERSON>erid,
        <PERSON>difyDate,
        <PERSON>sessor,
        Assessorid,
        <PERSON>sess<PERSON>ate,
        DeleteMark,
        Delete<PERSON><PERSON>,
        DeleteListerid,
        DeleteDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Custom6,
        Custom7,
        Custom8,
        Custom9,
        Custom10,
        Deptid,
        Tenantid,
        TenantName,
        Revision
        from Sa_Knowledge
        where Sa_Knowledge.id = #{key}
    </select>
    <sql id="selectSaKnowledgeVo">
        select id,
               GenGroupid,
               RefNo,
               BillType,
               BillTitle,
               BillDate,
               KnowUrl,
               VideoUrl,
               TextGoodNum,
               TextNgNum,
               TextLookTimes,
               TextLevel,
               FrontPhoto,
               PublicMark,
               ReleaseMark,
               RowNum,
               BucketName,
               DirName,
               FileName,
               FileSize,
               ContentType,
               Storage,
               Relateid,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               DeleteMark,
               DeleteLister,
               DeleteListerid,
               DeleteDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Deptid,
               Tenantid,
               TenantName,
               Revision
        from Sa_Knowledge
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaKnowledgePojo">
        <include refid="selectSaKnowledgeVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Knowledge.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null">
            and Sa_Knowledge.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.refno != null">
            and Sa_Knowledge.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Sa_Knowledge.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Sa_Knowledge.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.knowurl != null">
            and Sa_Knowledge.KnowUrl like concat('%', #{SearchPojo.knowurl}, '%')
        </if>
        <if test="SearchPojo.videourl != null">
            and Sa_Knowledge.VideoUrl like concat('%', #{SearchPojo.videourl}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null">
            and Sa_Knowledge.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.bucketname != null">
            and Sa_Knowledge.BucketName like concat('%', #{SearchPojo.bucketname}, '%')
        </if>
        <if test="SearchPojo.dirname != null">
            and Sa_Knowledge.DirName like concat('%', #{SearchPojo.dirname}, '%')
        </if>
        <if test="SearchPojo.filename != null">
            and Sa_Knowledge.FileName like concat('%', #{SearchPojo.filename}, '%')
        </if>
        <if test="SearchPojo.contenttype != null">
            and Sa_Knowledge.ContentType like concat('%', #{SearchPojo.contenttype}, '%')
        </if>
        <if test="SearchPojo.storage != null">
            and Sa_Knowledge.Storage like concat('%', #{SearchPojo.storage}, '%')
        </if>
        <if test="SearchPojo.relateid != null">
            and Sa_Knowledge.Relateid like concat('%', #{SearchPojo.relateid}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Knowledge.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Knowledge.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Knowledge.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Knowledge.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Knowledge.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_Knowledge.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_Knowledge.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.deletelister != null">
            and Sa_Knowledge.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null">
            and Sa_Knowledge.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Knowledge.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Knowledge.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Knowledge.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Knowledge.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Knowledge.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Knowledge.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Knowledge.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Knowledge.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Knowledge.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Knowledge.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_Knowledge.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Knowledge.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null">
                or Sa_Knowledge.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.refno != null">
                or Sa_Knowledge.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Sa_Knowledge.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Sa_Knowledge.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.knowurl != null">
                or Sa_Knowledge.KnowUrl like concat('%', #{SearchPojo.knowurl}, '%')
            </if>
            <if test="SearchPojo.videourl != null">
                or Sa_Knowledge.VideoUrl like concat('%', #{SearchPojo.videourl}, '%')
            </if>
            <if test="SearchPojo.frontphoto != null">
                or Sa_Knowledge.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
            </if>
            <if test="SearchPojo.bucketname != null">
                or Sa_Knowledge.BucketName like concat('%', #{SearchPojo.bucketname}, '%')
            </if>
            <if test="SearchPojo.dirname != null">
                or Sa_Knowledge.DirName like concat('%', #{SearchPojo.dirname}, '%')
            </if>
            <if test="SearchPojo.filename != null">
                or Sa_Knowledge.FileName like concat('%', #{SearchPojo.filename}, '%')
            </if>
            <if test="SearchPojo.contenttype != null">
                or Sa_Knowledge.ContentType like concat('%', #{SearchPojo.contenttype}, '%')
            </if>
            <if test="SearchPojo.storage != null">
                or Sa_Knowledge.Storage like concat('%', #{SearchPojo.storage}, '%')
            </if>
            <if test="SearchPojo.relateid != null">
                or Sa_Knowledge.Relateid like concat('%', #{SearchPojo.relateid}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Knowledge.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Knowledge.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Knowledge.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Knowledge.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Knowledge.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_Knowledge.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_Knowledge.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.deletelister != null">
                or Sa_Knowledge.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null">
                or Sa_Knowledge.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Knowledge.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Knowledge.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Knowledge.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Knowledge.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Knowledge.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Knowledge.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Knowledge.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Knowledge.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Knowledge.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Knowledge.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_Knowledge.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Knowledge.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Knowledge(id, GenGroupid, RefNo, BillType, BillTitle, BillDate, KnowUrl, VideoUrl, TextGoodNum,
        TextNgNum,
        TextLookTimes,
        TextLevel, FrontPhoto,
        PublicMark, ReleaseMark, RowNum, BucketName, DirName, FileName, FileSize, ContentType,
        Storage, Relateid, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid,
        ModifyDate, Assessor, Assessorid, AssessDate, DeleteMark, DeleteLister, DeleteListerid,
        DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8,
        Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{knowurl}, #{videourl},
        #{textgoodnum}, #{textngnum}, #{textlooktimes}, #{textlevel},
        #{frontphoto}, #{publicmark}, #{releasemark}, #{rownum}, #{bucketname}, #{dirname}, #{filename},
        #{filesize}, #{contenttype}, #{storage}, #{relateid}, #{remark}, #{createby}, #{createbyid},
        #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessor}, #{assessorid}, #{assessdate},
        #{deletemark}, #{deletelister}, #{deletelisterid}, #{deletedate}, #{custom1}, #{custom2}, #{custom3},
        #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{deptid},
        #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Knowledge
        <set>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="knowurl != null">
                KnowUrl =#{knowurl},
            </if>
            <if test="videourl != null">
                VideoUrl =#{videourl},
            </if>
            <!--            #{textgoodnum}, #{textngnum}, #{textlooktimes}, #{textlevel},-->
            <if test="textgoodnum != null">
                TextGoodNum =#{textgoodnum},
            </if>
            <if test="textngnum != null">
                TextNgNum =#{textngnum},
            </if>
            <if test="textlooktimes != null">
                TextLookTimes =#{textlooktimes},
            </if>
            <if test="textlevel != null">
                TextLevel =#{textlevel},
            </if>
            <if test="frontphoto != null">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="releasemark != null">
                ReleaseMark =#{releasemark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="bucketname != null">
                BucketName =#{bucketname},
            </if>
            <if test="dirname != null">
                DirName =#{dirname},
            </if>
            <if test="filename != null">
                FileName =#{filename},
            </if>
            <if test="filesize != null">
                FileSize =#{filesize},
            </if>
            <if test="contenttype != null">
                ContentType =#{contenttype},
            </if>
            <if test="storage != null">
                Storage =#{storage},
            </if>
            <if test="relateid != null">
                Relateid =#{relateid},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletelisterid != null">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Knowledge
        where id = #{key}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Knowledge
        SET Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
    </update>
</mapper>

