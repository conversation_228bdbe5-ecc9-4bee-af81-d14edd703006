<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaMarkdownMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaMarkdownPojo">
        select id,
        MdGroupid,
        RefNo,
        BillType,
        BillTitle,
        BillDate,
        Introduction,
        MDUrl,
        StarCount,
        MDNgNum,
        MDLookTimes,
        MDLevel,
        FrontPhoto,
        PublicMark,
        ReleaseMark,
        RowNum,
        FileSize,
        ContentType,
        Storage,
        Relateid,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Assessorid,
        Assessor,
        AssessDate,
        DeleteMark,
        DeleteLister,
        DeleteListerid,
        DeleteDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Custom6,
        Custom7,
        Custom8,
        Custom9,
        Custom10,
        Tenantid,
        Revision
        from Sa_Markdown
        where Sa_Markdown.id = #{key}
    </select>
    <sql id="selectSaMarkdownVo">
        select id,
               MdGroupid,
               RefNo,
               BillType,
               BillTitle,
               BillDate,
               Introduction,
               MDUrl,
               StarCount,
               MDNgNum,
               MDLookTimes,
               MDLevel,
               FrontPhoto,
               PublicMark,
               ReleaseMark,
               RowNum,
               FileSize,
               ContentType,
               Storage,
               Relateid,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessorid,
               Assessor,
               AssessDate,
               DeleteMark,
               DeleteLister,
               DeleteListerid,
               DeleteDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Sa_Markdown
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaMarkdownPojo">
        <include refid="selectSaMarkdownVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Markdown.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.mdgroupid != null">
            and Sa_Markdown.MdGroupid like concat('%', #{SearchPojo.mdgroupid}, '%')
        </if>
        <if test="SearchPojo.refno != null">
            and Sa_Markdown.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Sa_Markdown.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Sa_Markdown.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.introduction != null">
            and Sa_Markdown.Introduction like concat('%', #{SearchPojo.introduction}, '%')
        </if>
        <if test="SearchPojo.mdurl != null">
            and Sa_Markdown.MDUrl like concat('%', #{SearchPojo.mdurl}, '%')
        </if>
        <if test="SearchPojo.frontphoto != null">
            and Sa_Markdown.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
        </if>
        <if test="SearchPojo.contenttype != null">
            and Sa_Markdown.ContentType like concat('%', #{SearchPojo.contenttype}, '%')
        </if>
        <if test="SearchPojo.storage != null">
            and Sa_Markdown.Storage like concat('%', #{SearchPojo.storage}, '%')
        </if>
        <if test="SearchPojo.relateid != null">
            and Sa_Markdown.Relateid like concat('%', #{SearchPojo.relateid}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Markdown.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Markdown.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Markdown.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Markdown.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Markdown.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_Markdown.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_Markdown.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.deletelister != null">
            and Sa_Markdown.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
        </if>
        <if test="SearchPojo.deletelisterid != null">
            and Sa_Markdown.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Markdown.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Markdown.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Markdown.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Markdown.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Markdown.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Markdown.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Markdown.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Markdown.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Markdown.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Markdown.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.mdgroupid != null">
                or Sa_Markdown.MdGroupid like concat('%', #{SearchPojo.mdgroupid}, '%')
            </if>
            <if test="SearchPojo.refno != null">
                or Sa_Markdown.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Sa_Markdown.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Sa_Markdown.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.introduction != null">
                or Sa_Markdown.Introduction like concat('%', #{SearchPojo.introduction}, '%')
            </if>
            <if test="SearchPojo.mdurl != null">
                or Sa_Markdown.MDUrl like concat('%', #{SearchPojo.mdurl}, '%')
            </if>
            <if test="SearchPojo.frontphoto != null">
                or Sa_Markdown.FrontPhoto like concat('%', #{SearchPojo.frontphoto}, '%')
            </if>
            <if test="SearchPojo.contenttype != null">
                or Sa_Markdown.ContentType like concat('%', #{SearchPojo.contenttype}, '%')
            </if>
            <if test="SearchPojo.storage != null">
                or Sa_Markdown.Storage like concat('%', #{SearchPojo.storage}, '%')
            </if>
            <if test="SearchPojo.relateid != null">
                or Sa_Markdown.Relateid like concat('%', #{SearchPojo.relateid}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Markdown.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Markdown.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Markdown.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Markdown.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Markdown.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_Markdown.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_Markdown.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.deletelister != null">
                or Sa_Markdown.DeleteLister like concat('%', #{SearchPojo.deletelister}, '%')
            </if>
            <if test="SearchPojo.deletelisterid != null">
                or Sa_Markdown.DeleteListerid like concat('%', #{SearchPojo.deletelisterid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Markdown.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Markdown.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Markdown.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Markdown.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Markdown.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Markdown.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Markdown.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Markdown.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Markdown.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Markdown.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Markdown(id, MdGroupid, RefNo, BillType, BillTitle, BillDate, Introduction, MDUrl, StarCount,
        MDNgNum, MDLookTimes, MDLevel, FrontPhoto, PublicMark, ReleaseMark, RowNum, FileSize,
        ContentType, Storage, Relateid, Remark, CreateBy, CreateByid, CreateDate, Lister,
        Listerid, ModifyDate, Assessorid, Assessor, AssessDate, DeleteMark, DeleteLister,
        DeleteListerid, DeleteDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
        Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{mdgroupid}, #{refno}, #{billtype}, #{billtitle}, #{billdate}, #{introduction}, #{mdurl},
        #{starcount}, #{mdngnum}, #{mdlooktimes}, #{mdlevel}, #{frontphoto}, #{publicmark}, #{releasemark},
        #{rownum}, #{filesize}, #{contenttype}, #{storage}, #{relateid}, #{remark}, #{createby}, #{createbyid},
        #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{assessorid}, #{assessor}, #{assessdate},
        #{deletemark}, #{deletelister}, #{deletelisterid}, #{deletedate}, #{custom1}, #{custom2}, #{custom3},
        #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},
        #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Markdown
        <set>
            <if test="mdgroupid != null">
                MdGroupid =#{mdgroupid},
            </if>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="introduction != null">
                Introduction =#{introduction},
            </if>
            <if test="mdurl != null">
                MDUrl =#{mdurl},
            </if>
            <if test="starcount != null">
                StarCount =#{starcount},
            </if>
            <if test="mdngnum != null">
                MDNgNum =#{mdngnum},
            </if>
            <if test="mdlooktimes != null">
                MDLookTimes =#{mdlooktimes},
            </if>
            <if test="mdlevel != null">
                MDLevel =#{mdlevel},
            </if>
            <if test="frontphoto != null">
                FrontPhoto =#{frontphoto},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="releasemark != null">
                ReleaseMark =#{releasemark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="filesize != null">
                FileSize =#{filesize},
            </if>
            <if test="contenttype != null">
                ContentType =#{contenttype},
            </if>
            <if test="storage != null">
                Storage =#{storage},
            </if>
            <if test="relateid != null">
                Relateid =#{relateid},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="deletemark != null">
                DeleteMark =#{deletemark},
            </if>
            <if test="deletelister != null">
                DeleteLister =#{deletelister},
            </if>
            <if test="deletelisterid != null">
                DeleteListerid =#{deletelisterid},
            </if>
            <if test="deletedate != null">
                DeleteDate =#{deletedate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Markdown
        where id = #{key}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Markdown
        SET Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
    </update>
    <select id="getMdByProjectId" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaMarkdownPojo">
        select Sa_Markdown.id,
        Sa_Markdown.MdGroupid,
        Sa_Markdown.RefNo,
        Sa_Markdown.BillType,
        Sa_Markdown.BillTitle,
        Sa_Markdown.BillDate,
        Sa_Markdown.MDUrl,
        Sa_Markdown.StarCount,
        Sa_Markdown.MDNgNum,
        Sa_Markdown.MDLookTimes,
        Sa_Markdown.MDLevel,
        Sa_Markdown.FrontPhoto,
        Sa_Markdown.PublicMark,
        Sa_Markdown.ReleaseMark,
        Sa_Markdown.RowNum,
        Sa_Markdown.FileSize,
        Sa_Markdown.ContentType,
        Sa_Markdown.Storage,
        Sa_Markdown.Relateid,
        Sa_Markdown.Remark,
        Sa_Markdown.CreateBy,
        Sa_Markdown.CreateByid,
        Sa_Markdown.CreateDate,
        Sa_Markdown.Lister,
        Sa_Markdown.Listerid,
        Sa_Markdown.ModifyDate,
        Sa_Markdown.DeleteMark,
        Sa_Markdown.DeleteLister,
        Sa_Markdown.DeleteListerid,
        Sa_Markdown.DeleteDate,
        Sa_Markdown.Custom1,
        Sa_Markdown.Custom2,
        Sa_Markdown.Custom3,
        Sa_Markdown.Custom4,
        Sa_Markdown.Custom5,
        Sa_Markdown.Custom6,
        Sa_Markdown.Custom7,
        Sa_Markdown.Custom8,
        Sa_Markdown.Custom9,
        Sa_Markdown.Custom10,
        Sa_Markdown.Tenantid,
        Sa_Markdown.Revision
        from Sa_Markdown LEFT JOIN Sa_MdGroup ON Sa_Markdown.MdGroupid = Sa_MdGroup.id
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Markdown.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
</mapper>

