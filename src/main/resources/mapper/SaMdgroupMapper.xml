<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaMdgroupMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaMdgroupPojo">
        select id,
        MdProjectid,
        Parentid,
        ModuleCode,
        GroupCode,
        GroupName,
        EnabledMark,
        RowNum,
        Remark,
        Lister,
        CreateDate,
        ModifyDate,
        Tenantid
        from Sa_MdGroup
        where Sa_MdGroup.id = #{key}
    </select>
    <sql id="selectSaMdgroupVo">
        select id,
               MdProjectid,
               Parentid,
               ModuleCode,
               GroupCode,
               GroupName,
               EnabledMark,
               RowNum,
               Remark,
               Lister,
               CreateDate,
               ModifyDate,
               Tenantid
        from Sa_MdGroup
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaMdgroupPojo">
        <include refid="selectSaMdgroupVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_MdGroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.mdprojectid != null">
            and Sa_MdGroup.MdProjectid like concat('%', #{SearchPojo.mdprojectid}, '%')
        </if>
        <if test="SearchPojo.parentid != null">
            and Sa_MdGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Sa_MdGroup.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.groupcode != null">
            and Sa_MdGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
        </if>
        <if test="SearchPojo.groupname != null">
            and Sa_MdGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_MdGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_MdGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.mdprojectid != null">
                or Sa_MdGroup.MdProjectid like concat('%', #{SearchPojo.mdprojectid}, '%')
            </if>
            <if test="SearchPojo.parentid != null">
                or Sa_MdGroup.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Sa_MdGroup.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.groupcode != null">
                or Sa_MdGroup.GroupCode like concat('%', #{SearchPojo.groupcode}, '%')
            </if>
            <if test="SearchPojo.groupname != null">
                or Sa_MdGroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_MdGroup.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_MdGroup.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_MdGroup(id, MdProjectid, Parentid, ModuleCode, GroupCode, GroupName, EnabledMark, RowNum, Remark,
        Lister, CreateDate, ModifyDate, Tenantid)
        values (#{id}, #{mdprojectid}, #{parentid}, #{modulecode}, #{groupcode}, #{groupname}, #{enabledmark},
        #{rownum}, #{remark}, #{lister}, #{createdate}, #{modifydate}, #{tenantid})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_MdGroup
        <set>
            <if test="mdprojectid != null">
                MdProjectid =#{mdprojectid},
            </if>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="groupcode != null">
                GroupCode =#{groupcode},
            </if>
            <if test="groupname != null">
                GroupName =#{groupname},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_MdGroup
        where id = #{key}
    </delete>
    <select id="getMarkdownsByGroupId" resultType="inks.service.sa.pms.domain.pojo.SaMarkdownPojo">
        select *
        from Sa_Markdown
        where MdGroupid = #{key}
    </select>
</mapper>

