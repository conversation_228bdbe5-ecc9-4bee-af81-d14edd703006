<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaMeetingMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaMeetingPojo">
        <include refid="selectbillVo"/>
        where Sa_Meeting.id = #{key} 
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, MeetingTitle, MeetingDesc, Status, StartTime, EndTime, Location, AttendeeCount, ActAttendeeCount, Assessor, Assessorid, AssessDate, AssessComment, ItemCount, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_Meeting
    </sql>
    <sql id="selectdetailVo">
         select
id, MeetingTitle, MeetingDesc, Status, StartTime, EndTime, Location, AttendeeCount, ActAttendeeCount, Assessor, Assessorid, AssessDate, AssessComment, ItemCount, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_Meeting
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaMeetingitemdetailPojo">
        <include refid="selectdetailVo"/>
         where 1 = 1 
       <if test="filterstr != null ">
            ${filterstr}
        </if> 
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Meeting.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.meetingtitle != null ">
   and Sa_Meeting.meetingtitle like concat('%', #{SearchPojo.meetingtitle}, '%')
</if>
<if test="SearchPojo.meetingdesc != null ">
   and Sa_Meeting.meetingdesc like concat('%', #{SearchPojo.meetingdesc}, '%')
</if>
<if test="SearchPojo.location != null ">
   and Sa_Meeting.location like concat('%', #{SearchPojo.location}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Meeting.assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Meeting.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assesscomment != null ">
   and Sa_Meeting.assesscomment like concat('%', #{SearchPojo.assesscomment}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Meeting.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Meeting.createby like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Meeting.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Meeting.lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Meeting.listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Meeting.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Meeting.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Meeting.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Meeting.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Meeting.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.meetingtitle != null ">
   or Sa_Meeting.MeetingTitle like concat('%', #{SearchPojo.meetingtitle}, '%')
</if>
<if test="SearchPojo.meetingdesc != null ">
   or Sa_Meeting.MeetingDesc like concat('%', #{SearchPojo.meetingdesc}, '%')
</if>
<if test="SearchPojo.location != null ">
   or Sa_Meeting.Location like concat('%', #{SearchPojo.location}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Meeting.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Meeting.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assesscomment != null ">
   or Sa_Meeting.AssessComment like concat('%', #{SearchPojo.assesscomment}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Meeting.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Meeting.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Meeting.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Meeting.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Meeting.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Meeting.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Meeting.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Meeting.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Meeting.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Meeting.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
     
         <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaMeetingPojo">
        <include refid="selectbillVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Meeting.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="thand"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="thor"></include>        
         </if>
        </if> 
        order by ${orderBy}
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="thand">
<if test="SearchPojo.meetingtitle != null ">
   and Sa_Meeting.MeetingTitle like concat('%', #{SearchPojo.meetingtitle}, '%')
</if>
<if test="SearchPojo.meetingdesc != null ">
   and Sa_Meeting.MeetingDesc like concat('%', #{SearchPojo.meetingdesc}, '%')
</if>
<if test="SearchPojo.location != null ">
   and Sa_Meeting.Location like concat('%', #{SearchPojo.location}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Meeting.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Meeting.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assesscomment != null ">
   and Sa_Meeting.AssessComment like concat('%', #{SearchPojo.assesscomment}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Meeting.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Meeting.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Meeting.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Meeting.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Meeting.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Meeting.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Meeting.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Meeting.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Meeting.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Meeting.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="thor">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.meetingtitle != null ">
   or Sa_Meeting.MeetingTitle like concat('%', #{SearchPojo.meetingtitle}, '%')
</if>
<if test="SearchPojo.meetingdesc != null ">
   or Sa_Meeting.MeetingDesc like concat('%', #{SearchPojo.meetingdesc}, '%')
</if>
<if test="SearchPojo.location != null ">
   or Sa_Meeting.Location like concat('%', #{SearchPojo.location}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Meeting.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Meeting.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assesscomment != null ">
   or Sa_Meeting.AssessComment like concat('%', #{SearchPojo.assesscomment}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Meeting.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Meeting.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Meeting.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Meeting.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Meeting.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Meeting.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Meeting.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Meeting.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Meeting.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Meeting.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
     
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Meeting(id, MeetingTitle, MeetingDesc, Status, StartTime, EndTime, Location, AttendeeCount, ActAttendeeCount, Assessor, Assessorid, AssessDate, AssessComment, ItemCount, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{meetingtitle}, #{meetingdesc}, #{status}, #{starttime}, #{endtime}, #{location}, #{attendeecount}, #{actattendeecount}, #{assessor}, #{assessorid}, #{assessdate}, #{assesscomment}, #{itemcount}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Meeting
        <set>
            <if test="meetingtitle != null ">
                MeetingTitle =#{meetingtitle},
            </if>
            <if test="meetingdesc != null ">
                MeetingDesc =#{meetingdesc},
            </if>
            <if test="status != null">
                Status =#{status},
            </if>
            <if test="starttime != null">
                StartTime =#{starttime},
            </if>
            <if test="endtime != null">
                EndTime =#{endtime},
            </if>
            <if test="location != null ">
                Location =#{location},
            </if>
            <if test="attendeecount != null">
                AttendeeCount =#{attendeecount},
            </if>
            <if test="actattendeecount != null">
                ActAttendeeCount =#{actattendeecount},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="assesscomment != null ">
                AssessComment =#{assesscomment},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Meeting where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Meeting SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String" parameterType="inks.service.sa.pms.domain.pojo.SaMeetingPojo">
        select
          id
        from Sa_MeetingItem
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="item" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>

    <select id="getDelUserIds" resultType="java.lang.String">
        select
          id
        from Sa_MeetingUser
        where Pid = #{id}
        <if test="item !=null and item.size()>0">
         and id not in
        <foreach collection="user" open="(" close=")" separator="," item="item">
            <if test="item.id != null">
                #{item.id}
            </if>
            <if test="item.id == null">
                 ''
            </if>
        </foreach>
         </if>
    </select>
</mapper>

