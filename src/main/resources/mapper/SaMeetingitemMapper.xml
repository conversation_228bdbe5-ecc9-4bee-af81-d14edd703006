<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaMeetingitemMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaMeetingitemPojo">
        <include refid="selectSaMeetingitemVo"/>
        where Sa_MeetingItem.id = #{key} 
    </select>
    <sql id="selectSaMeetingitemVo">
         select
id, Pid, TopicTitle, TopicContent, TopicResult, Principal, Principalid, DeadlineDate, Priority, TopicStatus, Duration, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_MeetingItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaMeetingitemPojo">
        <include refid="selectSaMeetingitemVo"/>
        where Sa_MeetingItem.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaMeetingitemPojo">
        <include refid="selectSaMeetingitemVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_MeetingItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_MeetingItem.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.topictitle != null and SearchPojo.topictitle != ''">
   and Sa_MeetingItem.topictitle like concat('%', #{SearchPojo.topictitle}, '%')
</if>
<if test="SearchPojo.topiccontent != null and SearchPojo.topiccontent != ''">
   and Sa_MeetingItem.topiccontent like concat('%', #{SearchPojo.topiccontent}, '%')
</if>
<if test="SearchPojo.topicresult != null and SearchPojo.topicresult != ''">
   and Sa_MeetingItem.topicresult like concat('%', #{SearchPojo.topicresult}, '%')
</if>
<if test="SearchPojo.principal != null and SearchPojo.principal != ''">
   and Sa_MeetingItem.principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.principalid != null and SearchPojo.principalid != ''">
   and Sa_MeetingItem.principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_MeetingItem.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_MeetingItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_MeetingItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_MeetingItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_MeetingItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_MeetingItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_MeetingItem.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.topictitle != null and SearchPojo.topictitle != ''">
   or Sa_MeetingItem.TopicTitle like concat('%', #{SearchPojo.topictitle}, '%')
</if>
<if test="SearchPojo.topiccontent != null and SearchPojo.topiccontent != ''">
   or Sa_MeetingItem.TopicContent like concat('%', #{SearchPojo.topiccontent}, '%')
</if>
<if test="SearchPojo.topicresult != null and SearchPojo.topicresult != ''">
   or Sa_MeetingItem.TopicResult like concat('%', #{SearchPojo.topicresult}, '%')
</if>
<if test="SearchPojo.principal != null and SearchPojo.principal != ''">
   or Sa_MeetingItem.Principal like concat('%', #{SearchPojo.principal}, '%')
</if>
<if test="SearchPojo.principalid != null and SearchPojo.principalid != ''">
   or Sa_MeetingItem.Principalid like concat('%', #{SearchPojo.principalid}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_MeetingItem.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_MeetingItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_MeetingItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_MeetingItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_MeetingItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_MeetingItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_MeetingItem(id, Pid, TopicTitle, TopicContent, TopicResult, Principal, Principalid, DeadlineDate, Priority, TopicStatus, Duration, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{topictitle}, #{topiccontent}, #{topicresult}, #{principal}, #{principalid}, #{deadlinedate}, #{priority}, #{topicstatus}, #{duration}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_MeetingItem
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="topictitle != null ">
                TopicTitle = #{topictitle},
            </if>
            <if test="topiccontent != null ">
                TopicContent = #{topiccontent},
            </if>
            <if test="topicresult != null ">
                TopicResult = #{topicresult},
            </if>
            <if test="principal != null ">
                Principal = #{principal},
            </if>
            <if test="principalid != null ">
                Principalid = #{principalid},
            </if>
            <if test="deadlinedate != null">
                DeadlineDate = #{deadlinedate},
            </if>
            <if test="priority != null">
                Priority = #{priority},
            </if>
            <if test="topicstatus != null">
                TopicStatus = #{topicstatus},
            </if>
            <if test="duration != null">
                Duration = #{duration},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_MeetingItem where id = #{key} 
    </delete>

</mapper>

