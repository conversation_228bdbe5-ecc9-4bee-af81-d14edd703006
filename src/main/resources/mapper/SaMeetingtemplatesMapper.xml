<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaMeetingtemplatesMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaMeetingtemplatesPojo">
        <include refid="selectSaMeetingtemplatesVo"/>
        where Sa_MeetingTemplates.id = #{key} 
    </select>
    <sql id="selectSaMeetingtemplatesVo">
         select
id, TemplateName, TemplateType, Description, DeftDuration, DeftLocation, TopicTemplate, PartTemplate, IsPublic, UsageCount, IsActive, RowNum, Remark, CreateBy, <PERSON>reateByid, <PERSON>reateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_MeetingTemplates
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaMeetingtemplatesPojo">
        <include refid="selectSaMeetingtemplatesVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_MeetingTemplates.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.templatename != null ">
   and Sa_MeetingTemplates.TemplateName like concat('%', #{SearchPojo.templatename}, '%')
</if>
<if test="SearchPojo.description != null ">
   and Sa_MeetingTemplates.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.deftlocation != null ">
   and Sa_MeetingTemplates.DeftLocation like concat('%', #{SearchPojo.deftlocation}, '%')
</if>
<if test="SearchPojo.topictemplate != null ">
   and Sa_MeetingTemplates.TopicTemplate like concat('%', #{SearchPojo.topictemplate}, '%')
</if>
<if test="SearchPojo.parttemplate != null ">
   and Sa_MeetingTemplates.PartTemplate like concat('%', #{SearchPojo.parttemplate}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_MeetingTemplates.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_MeetingTemplates.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_MeetingTemplates.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_MeetingTemplates.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_MeetingTemplates.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_MeetingTemplates.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_MeetingTemplates.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_MeetingTemplates.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_MeetingTemplates.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_MeetingTemplates.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.templatename != null ">
   or Sa_MeetingTemplates.TemplateName like concat('%', #{SearchPojo.templatename}, '%')
</if>
<if test="SearchPojo.description != null ">
   or Sa_MeetingTemplates.Description like concat('%', #{SearchPojo.description}, '%')
</if>
<if test="SearchPojo.deftlocation != null ">
   or Sa_MeetingTemplates.DeftLocation like concat('%', #{SearchPojo.deftlocation}, '%')
</if>
<if test="SearchPojo.topictemplate != null ">
   or Sa_MeetingTemplates.TopicTemplate like concat('%', #{SearchPojo.topictemplate}, '%')
</if>
<if test="SearchPojo.parttemplate != null ">
   or Sa_MeetingTemplates.PartTemplate like concat('%', #{SearchPojo.parttemplate}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_MeetingTemplates.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_MeetingTemplates.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_MeetingTemplates.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_MeetingTemplates.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_MeetingTemplates.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_MeetingTemplates.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_MeetingTemplates.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_MeetingTemplates.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_MeetingTemplates.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_MeetingTemplates.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_MeetingTemplates(id, TemplateName, TemplateType, Description, DeftDuration, DeftLocation, TopicTemplate, PartTemplate, IsPublic, UsageCount, IsActive, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{templatename}, #{templatetype}, #{description}, #{deftduration}, #{deftlocation}, #{topictemplate}, #{parttemplate}, #{ispublic}, #{usagecount}, #{isactive}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_MeetingTemplates
        <set>
            <if test="templatename != null ">
                TemplateName =#{templatename},
            </if>
            <if test="templatetype != null">
                TemplateType =#{templatetype},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="deftduration != null">
                DeftDuration =#{deftduration},
            </if>
            <if test="deftlocation != null ">
                DeftLocation =#{deftlocation},
            </if>
            <if test="topictemplate != null ">
                TopicTemplate =#{topictemplate},
            </if>
            <if test="parttemplate != null ">
                PartTemplate =#{parttemplate},
            </if>
            <if test="ispublic != null">
                IsPublic =#{ispublic},
            </if>
            <if test="usagecount != null">
                UsageCount =#{usagecount},
            </if>
            <if test="isactive != null">
                IsActive =#{isactive},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_MeetingTemplates where id = #{key} 
    </delete>
</mapper>

