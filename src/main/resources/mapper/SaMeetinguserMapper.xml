<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaMeetinguserMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaMeetinguserPojo">
        <include refid="selectSaMeetinguserVo"/>
        where Sa_MeetingUser.id = #{key} 
    </select>
    <sql id="selectSaMeetinguserVo">
         select
id, Pid, Userid, UserName, UserDept, UserPosition, ParticipantType, ActualAttendance, AttendanceTime, LeaveTime, Phone, Email, InviteStatus, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision        from Sa_MeetingUser
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaMeetinguserPojo">
        <include refid="selectSaMeetinguserVo"/>
        where Sa_MeetingUser.Pid = #{Pid}
        order by RowNum 
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaMeetinguserPojo">
        <include refid="selectSaMeetinguserVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
              <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_MeetingUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   and Sa_MeetingUser.pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.userid != null and SearchPojo.userid != ''">
   and Sa_MeetingUser.userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.username != null and SearchPojo.username != ''">
   and Sa_MeetingUser.username like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.userdept != null and SearchPojo.userdept != ''">
   and Sa_MeetingUser.userdept like concat('%', #{SearchPojo.userdept}, '%')
</if>
<if test="SearchPojo.userposition != null and SearchPojo.userposition != ''">
   and Sa_MeetingUser.userposition like concat('%', #{SearchPojo.userposition}, '%')
</if>
<if test="SearchPojo.phone != null and SearchPojo.phone != ''">
   and Sa_MeetingUser.phone like concat('%', #{SearchPojo.phone}, '%')
</if>
<if test="SearchPojo.email != null and SearchPojo.email != ''">
   and Sa_MeetingUser.email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   and Sa_MeetingUser.remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   and Sa_MeetingUser.custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   and Sa_MeetingUser.custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   and Sa_MeetingUser.custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   and Sa_MeetingUser.custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   and Sa_MeetingUser.custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.pid != null and SearchPojo.pid != ''">
   or Sa_MeetingUser.Pid like concat('%', #{SearchPojo.pid}, '%')
</if>
<if test="SearchPojo.userid != null and SearchPojo.userid != ''">
   or Sa_MeetingUser.Userid like concat('%', #{SearchPojo.userid}, '%')
</if>
<if test="SearchPojo.username != null and SearchPojo.username != ''">
   or Sa_MeetingUser.UserName like concat('%', #{SearchPojo.username}, '%')
</if>
<if test="SearchPojo.userdept != null and SearchPojo.userdept != ''">
   or Sa_MeetingUser.UserDept like concat('%', #{SearchPojo.userdept}, '%')
</if>
<if test="SearchPojo.userposition != null and SearchPojo.userposition != ''">
   or Sa_MeetingUser.UserPosition like concat('%', #{SearchPojo.userposition}, '%')
</if>
<if test="SearchPojo.phone != null and SearchPojo.phone != ''">
   or Sa_MeetingUser.Phone like concat('%', #{SearchPojo.phone}, '%')
</if>
<if test="SearchPojo.email != null and SearchPojo.email != ''">
   or Sa_MeetingUser.Email like concat('%', #{SearchPojo.email}, '%')
</if>
<if test="SearchPojo.remark != null and SearchPojo.remark != ''">
   or Sa_MeetingUser.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
   or Sa_MeetingUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
   or Sa_MeetingUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
   or Sa_MeetingUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
   or Sa_MeetingUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
   or Sa_MeetingUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
</trim>
     </sql>

    
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_MeetingUser(id, Pid, Userid, UserName, UserDept, UserPosition, ParticipantType, ActualAttendance, AttendanceTime, LeaveTime, Phone, Email, InviteStatus, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, Revision)
        values (#{id}, #{pid}, #{userid}, #{username}, #{userdept}, #{userposition}, #{participanttype}, #{actualattendance}, #{attendancetime}, #{leavetime}, #{phone}, #{email}, #{invitestatus}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_MeetingUser
        <set>
            <if test="pid != null ">
                Pid = #{pid},
            </if>
            <if test="userid != null ">
                Userid = #{userid},
            </if>
            <if test="username != null ">
                UserName = #{username},
            </if>
            <if test="userdept != null ">
                UserDept = #{userdept},
            </if>
            <if test="userposition != null ">
                UserPosition = #{userposition},
            </if>
            <if test="participanttype != null">
                ParticipantType = #{participanttype},
            </if>
            <if test="actualattendance != null">
                ActualAttendance = #{actualattendance},
            </if>
            <if test="attendancetime != null">
                AttendanceTime = #{attendancetime},
            </if>
            <if test="leavetime != null">
                LeaveTime = #{leavetime},
            </if>
            <if test="phone != null ">
                Phone = #{phone},
            </if>
            <if test="email != null ">
                Email = #{email},
            </if>
            <if test="invitestatus != null">
                InviteStatus = #{invitestatus},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null ">
                Remark = #{remark},
            </if>
            <if test="custom1 != null ">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 = #{custom5},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_MeetingUser where id = #{key} 
    </delete>

</mapper>

