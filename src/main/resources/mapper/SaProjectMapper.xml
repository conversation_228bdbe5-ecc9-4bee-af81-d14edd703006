<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaProjectMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaProjectPojo">
        select id,
        GenGroupid,
        ProjCode,
        ProjType,
        ProjName,
        ProjSpec,
        ProjUnit,
        ProjPinyin,
        VersionNum,
        BarCode,
        Operator,
        EnabledMark,
        CoverImage,
        StarMark,
        RowNum,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Custom6,
        Custom7,
        Custom8,
        Custom9,
        Custom10,
        Deptid,
        Tenantid,
        TenantName,
        Revision
        from Sa_Project
        where Sa_Project.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
        select Sa_Project.id,
               Sa_Project.GenGroupid,
               Sa_Project.ProjCode,
               Sa_Project.ProjType,
               Sa_Project.ProjName,
               Sa_Project.ProjSpec,
               Sa_Project.ProjUnit,
               Sa_Project.ProjPinyin,
               Sa_Project.VersionNum,
               Sa_Project.BarCode,
               Sa_Project.Operator,
               Sa_Project.EnabledMark,
               Sa_Project.CoverImage,
               Sa_Project.StarMark,
               Sa_Project.RowNum,
               Sa_Project.Remark,
               Sa_Project.CreateBy,
               Sa_Project.CreateByid,
               Sa_Project.CreateDate,
               Sa_Project.Lister,
               Sa_Project.Listerid,
               Sa_Project.ModifyDate,
               Sa_Project.Custom1,
               Sa_Project.Custom2,
               Sa_Project.Custom3,
               Sa_Project.Custom4,
               Sa_Project.Custom5,
               Sa_Project.Custom6,
               Sa_Project.Custom7,
               Sa_Project.Custom8,
               Sa_Project.Custom9,
               Sa_Project.Custom10,
               Sa_Project.Deptid,
               Sa_Project.Tenantid,
               Sa_Project.TenantName,
               Sa_Project.Revision
        from Sa_Project
                 Join Sa_ProjectSort on Sa_Project.id = Sa_ProjectSort.Projectid
    </sql>
    <sql id="selectdetailVo">
        select Sa_ProjectItem.id,
               Sa_ProjectItem.Pid,
               Sa_ProjectItem.Engineerid,
               Sa_ProjectItem.RoleType,
               Sa_ProjectItem.RowNum,
               Sa_ProjectItem.Remark,
               Sa_ProjectItem.CreateBy,
               Sa_ProjectItem.CreateByid,
               Sa_ProjectItem.CreateDate,
               Sa_ProjectItem.Lister,
               Sa_ProjectItem.Listerid,
               Sa_ProjectItem.ModifyDate,
               Sa_ProjectItem.Custom1,
               Sa_ProjectItem.Custom2,
               Sa_ProjectItem.Custom3,
               Sa_ProjectItem.Custom4,
               Sa_ProjectItem.Custom5,
               Sa_ProjectItem.Deptid,
               Sa_ProjectItem.Tenantid,
               Sa_ProjectItem.TenantName,
               Sa_ProjectItem.Revision,
               Sa_Project.GenGroupid,
               Sa_Project.ProjCode,
               Sa_Project.ProjType,
               Sa_Project.ProjName,
               Sa_Project.ProjSpec,
               Sa_Project.ProjUnit,
               Sa_Project.ProjPinyin,
               Sa_Project.VersionNum,
               Sa_Project.BarCode,
               Sa_Project.Operator,
               Sa_Project.EnabledMark,
               Sa_Project.CoverImage,
               Sa_Project.StarMark,
               Sa_Project.RowNum,
               Sa_Project.Remark as projremark
        from Sa_Project
                 Right Join Sa_ProjectItem on Sa_Project.id = Sa_ProjectItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaProjectitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Project.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.gengroupid != null">
            and Sa_Project.gengroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.projcode != null">
            and Sa_Project.projcode like concat('%',
            #{SearchPojo.projcode}, '%')
        </if>
        <if test="SearchPojo.projtype != null">
            and Sa_Project.projtype like concat('%',
            #{SearchPojo.projtype}, '%')
        </if>
        <if test="SearchPojo.projname != null">
            and Sa_Project.projname like concat('%',
            #{SearchPojo.projname}, '%')
        </if>
        <if test="SearchPojo.projspec != null">
            and Sa_Project.projspec like concat('%',
            #{SearchPojo.projspec}, '%')
        </if>
        <if test="SearchPojo.projunit != null">
            and Sa_Project.projunit like concat('%',
            #{SearchPojo.projunit}, '%')
        </if>
        <if test="SearchPojo.projpinyin != null">
            and Sa_Project.projpinyin like concat('%',
            #{SearchPojo.projpinyin}, '%')
        </if>
        <if test="SearchPojo.versionnum != null">
            and Sa_Project.versionnum like concat('%',
            #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.barcode != null">
            and Sa_Project.barcode like concat('%',
            #{SearchPojo.barcode}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_Project.operator like concat('%',
            #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.coverimage != null">
            and Sa_Project.coverimage like concat('%',
            #{SearchPojo.coverimage}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Project.remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Project.createby like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Project.createbyid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Project.lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Project.listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Project.custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Project.custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Project.custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Project.custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Project.custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Project.custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Project.custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Project.custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Project.custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Project.custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_Project.deptid like concat('%',
            #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Project.tenantname like concat('%',
            #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null">
                or Sa_Project.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.projcode != null">
                or Sa_Project.ProjCode like concat('%', #{SearchPojo.projcode}, '%')
            </if>
            <if test="SearchPojo.projtype != null">
                or Sa_Project.ProjType like concat('%', #{SearchPojo.projtype}, '%')
            </if>
            <if test="SearchPojo.projname != null">
                or Sa_Project.ProjName like concat('%', #{SearchPojo.projname}, '%')
            </if>
            <if test="SearchPojo.projspec != null">
                or Sa_Project.ProjSpec like concat('%', #{SearchPojo.projspec}, '%')
            </if>
            <if test="SearchPojo.projunit != null">
                or Sa_Project.ProjUnit like concat('%', #{SearchPojo.projunit}, '%')
            </if>
            <if test="SearchPojo.projpinyin != null">
                or Sa_Project.ProjPinyin like concat('%', #{SearchPojo.projpinyin}, '%')
            </if>
            <if test="SearchPojo.versionnum != null">
                or Sa_Project.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.barcode != null">
                or Sa_Project.BarCode like concat('%', #{SearchPojo.barcode}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_Project.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.coverimage != null">
                or Sa_Project.CoverImage like concat('%', #{SearchPojo.coverimage}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Project.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Project.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Project.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Project.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Project.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Project.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Project.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Project.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Project.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Project.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Project.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Project.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Project.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Project.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Project.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_Project.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Project.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaProjectPojo">
        select * from ((select Sa_Project.*,Sa_ProjectSort.StarMark as sortstarmark,Sa_ProjectSort.LastAccessTime
        from Sa_Project
        Join Sa_ProjectSort on Sa_Project.id = Sa_ProjectSort.Projectid
        where 1 = 1
        <if test="queryParam.filterstr != null">
            ${queryParam.filterstr}
        </if>
        <if test="queryParam.DateRange != null">
            <if test="queryParam.DateRange.DateColumn == null or queryParam.DateRange.DateColumn == ''">
                and Sa_Project.CreateDate BETWEEN #{queryParam.DateRange.StartDate} and #{queryParam.DateRange.EndDate}
            </if>
            <if test="queryParam.DateRange.DateColumn != null and queryParam.DateRange.DateColumn != ''">
                and ${queryParam.DateRange.DateColumn} BETWEEN #{queryParam.DateRange.StartDate} and
                #{queryParam.DateRange.EndDate}
            </if>
        </if>
        <if test="queryParam.SearchPojo != null">
            <if test="queryParam.SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="queryParam.SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        <if test="isadmin == false">
            )) as Result order by Result.sortstarmark desc, Result.LastAccessTime desc
        </if>
        <if test="isadmin == true">
            )UNION ALL
            (select Sa_Project.*,0 AS sortstarmark,null as LastAccessTime
            from Sa_Project
            where Sa_Project.id not in
            (select Projectid from Sa_ProjectSort where Engineerid = #{engineerid})
            )) as Result order by Result.sortstarmark desc, Result.LastAccessTime desc,Result.CreateDate desc
        </if>
    </select>

    <sql id="thand">
        <if test="SearchPojo.gengroupid != null">
            and Sa_Project.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
        </if>
        <if test="SearchPojo.projcode != null">
            and Sa_Project.ProjCode like concat('%',
            #{SearchPojo.projcode}, '%')
        </if>
        <if test="SearchPojo.projtype != null">
            and Sa_Project.ProjType like concat('%',
            #{SearchPojo.projtype}, '%')
        </if>
        <if test="SearchPojo.projname != null">
            and Sa_Project.ProjName like concat('%',
            #{SearchPojo.projname}, '%')
        </if>
        <if test="SearchPojo.projspec != null">
            and Sa_Project.ProjSpec like concat('%',
            #{SearchPojo.projspec}, '%')
        </if>
        <if test="SearchPojo.projunit != null">
            and Sa_Project.ProjUnit like concat('%',
            #{SearchPojo.projunit}, '%')
        </if>
        <if test="SearchPojo.projpinyin != null">
            and Sa_Project.ProjPinyin like concat('%',
            #{SearchPojo.projpinyin}, '%')
        </if>
        <if test="SearchPojo.versionnum != null">
            and Sa_Project.VersionNum like concat('%',
            #{SearchPojo.versionnum}, '%')
        </if>
        <if test="SearchPojo.barcode != null">
            and Sa_Project.BarCode like concat('%',
            #{SearchPojo.barcode}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_Project.Operator like concat('%',
            #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.coverimage != null">
            and Sa_Project.CoverImage like concat('%',
            #{SearchPojo.coverimage}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Project.Remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Project.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Project.CreateByid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Project.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Project.Listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Project.Custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Project.Custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Project.Custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Project.Custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Project.Custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Project.Custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Project.Custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Project.Custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Project.Custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Project.Custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_Project.Deptid like concat('%',
            #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Project.TenantName like concat('%',
            #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.gengroupid != null">
                or Sa_Project.GenGroupid like concat('%', #{SearchPojo.gengroupid}, '%')
            </if>
            <if test="SearchPojo.projcode != null">
                or Sa_Project.ProjCode like concat('%', #{SearchPojo.projcode}, '%')
            </if>
            <if test="SearchPojo.projtype != null">
                or Sa_Project.ProjType like concat('%', #{SearchPojo.projtype}, '%')
            </if>
            <if test="SearchPojo.projname != null">
                or Sa_Project.ProjName like concat('%', #{SearchPojo.projname}, '%')
            </if>
            <if test="SearchPojo.projspec != null">
                or Sa_Project.ProjSpec like concat('%', #{SearchPojo.projspec}, '%')
            </if>
            <if test="SearchPojo.projunit != null">
                or Sa_Project.ProjUnit like concat('%', #{SearchPojo.projunit}, '%')
            </if>
            <if test="SearchPojo.projpinyin != null">
                or Sa_Project.ProjPinyin like concat('%', #{SearchPojo.projpinyin}, '%')
            </if>
            <if test="SearchPojo.versionnum != null">
                or Sa_Project.VersionNum like concat('%', #{SearchPojo.versionnum}, '%')
            </if>
            <if test="SearchPojo.barcode != null">
                or Sa_Project.BarCode like concat('%', #{SearchPojo.barcode}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_Project.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.coverimage != null">
                or Sa_Project.CoverImage like concat('%', #{SearchPojo.coverimage}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Project.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Project.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Project.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Project.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Project.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Project.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Project.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Project.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Project.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Project.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Project.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Project.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Project.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Project.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Project.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_Project.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Project.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Project(id, GenGroupid, ProjCode, ProjType, ProjName, ProjSpec, ProjUnit, ProjPinyin, VersionNum,
        BarCode, Operator, EnabledMark, CoverImage, StarMark, RowNum, Remark, CreateBy,
        CreateByid,
        CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
        Custom6, Custom7, Custom8, Custom9, Custom10, Deptid, Tenantid, TenantName, Revision)
        values (#{id}, #{gengroupid}, #{projcode}, #{projtype}, #{projname}, #{projspec}, #{projunit}, #{projpinyin},
        #{versionnum}, #{barcode}, #{operator}, #{enabledmark}, #{coverimage}, #{starmark}, #{rownum},
        #{remark},
        #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
        #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9},
        #{custom10}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Project
        <set>
            <if test="gengroupid != null">
                GenGroupid =#{gengroupid},
            </if>
            <if test="projcode != null">
                ProjCode =#{projcode},
            </if>
            <if test="projtype != null">
                ProjType =#{projtype},
            </if>
            <if test="projname != null">
                ProjName =#{projname},
            </if>
            <if test="projspec != null">
                ProjSpec =#{projspec},
            </if>
            <if test="projunit != null">
                ProjUnit =#{projunit},
            </if>
            <if test="projpinyin != null">
                ProjPinyin =#{projpinyin},
            </if>
            <if test="versionnum != null">
                VersionNum =#{versionnum},
            </if>
            <if test="barcode != null">
                BarCode =#{barcode},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="coverimage != null">
                CoverImage =#{coverimage},
            </if>
            <if test="starmark != null">
                StarMark =#{starmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Project
        where id = #{key}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.pms.domain.pojo.SaProjectPojo">
        select id
        from Sa_ProjectItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDelStatusIds" resultType="java.lang.String"
            parameterType="inks.service.sa.pms.domain.pojo.SaProjectPojo">
        select id
        from Sa_ProjectStatus
        where Pid = #{id}
        <if test="status != null and status.size() > 0">
            and id not in
            <foreach collection="status" open="(" close=")" separator="," item="status">
                <if test="status.id != null">
                    #{status.id}
                </if>
                <if test="status.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDelLabelIds" resultType="java.lang.String"
            parameterType="inks.service.sa.pms.domain.pojo.SaProjectPojo">
        select id
        from Sa_ProjectLabel
        where Pid = #{id}
        <if test="label != null and label.size() > 0">
            and id not in
            <foreach collection="label" open="(" close=")" separator="," item="label">
                <if test="label.id != null">
                    #{label.id}
                </if>
                <if test="label.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getEntityByProjectName" resultType="inks.service.sa.pms.domain.pojo.SaProjectPojo">
        select id,
        GenGroupid,
        ProjCode,
        ProjType,
        ProjName,
        ProjSpec,
        ProjUnit,
        ProjPinyin,
        VersionNum,
        BarCode,
        Operator,
        EnabledMark,
        CoverImage,
        StarMark,
        RowNum,
        Remark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Custom6,
        Custom7,
        Custom8,
        Custom9,
        Custom10,
        Deptid,
        Tenantid,
        TenantName,
        Revision
        from Sa_Project
        where Sa_Project.ProjName = #{projName}
    </select>
</mapper>

