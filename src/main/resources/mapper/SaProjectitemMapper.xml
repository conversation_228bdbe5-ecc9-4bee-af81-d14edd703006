<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaProjectitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaProjectitemPojo">
        select Sa_ProjectItem.id,
        Sa_ProjectItem.Pid,
        Sa_ProjectItem.Engineerid,
        Sa_ProjectItem.RoleType,
        Sa_ProjectItem.RowNum,
        Sa_ProjectItem.Remark,
        Sa_ProjectItem.CreateBy,
        Sa_ProjectItem.CreateByid,
        Sa_ProjectItem.CreateDate,
        Sa_ProjectItem.Lister,
        Sa_ProjectItem.Listerid,
        Sa_ProjectItem.ModifyDate,
        Sa_ProjectItem.Custom1,
        Sa_ProjectItem.Custom2,
        Sa_ProjectItem.Custom3,
        Sa_ProjectItem.Custom4,
        Sa_ProjectItem.Custom5,
        Sa_ProjectItem.Deptid,
        Sa_ProjectItem.Tenantid,
        Sa_ProjectItem.TenantName,
        Sa_ProjectItem.Revision,
        Sa_Engineer.EngineerName,
        Sa_Engineer.EngineerCode,
        Sa_Engineer.EngineerType
        from Sa_ProjectItem
        join Sa_Engineer on Sa_ProjectItem.Engineerid = Sa_Engineer.id
        where Sa_ProjectItem.id = #{key}
    </select>
    <sql id="selectSaProjectitemVo">
        select Sa_ProjectItem.id,
               Sa_ProjectItem.Pid,
               Sa_ProjectItem.Engineerid,
               Sa_ProjectItem.RoleType,
               Sa_ProjectItem.RowNum,
               Sa_ProjectItem.Remark,
               Sa_ProjectItem.CreateBy,
               Sa_ProjectItem.CreateByid,
               Sa_ProjectItem.CreateDate,
               Sa_ProjectItem.Lister,
               Sa_ProjectItem.Listerid,
               Sa_ProjectItem.ModifyDate,
               Sa_ProjectItem.Custom1,
               Sa_ProjectItem.Custom2,
               Sa_ProjectItem.Custom3,
               Sa_ProjectItem.Custom4,
               Sa_ProjectItem.Custom5,
               Sa_ProjectItem.Deptid,
               Sa_ProjectItem.Tenantid,
               Sa_ProjectItem.TenantName,
               Sa_ProjectItem.Revision,
               Sa_Engineer.EngineerName,
               Sa_Engineer.EngineerCode,
               Sa_Engineer.EngineerType
        from Sa_ProjectItem
                 join Sa_Engineer on Sa_ProjectItem.Engineerid = Sa_Engineer.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaProjectitemPojo">
        <include refid="selectSaProjectitemVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_ProjectItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Sa_ProjectItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.engineerid != null and SearchPojo.engineerid != ''">
            and Sa_ProjectItem.engineerid like concat('%',
            #{SearchPojo.engineerid}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Sa_ProjectItem.remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Sa_ProjectItem.createby like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Sa_ProjectItem.createbyid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Sa_ProjectItem.lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Sa_ProjectItem.listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Sa_ProjectItem.custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Sa_ProjectItem.custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Sa_ProjectItem.custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Sa_ProjectItem.custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Sa_ProjectItem.custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
            and Sa_ProjectItem.deptid like concat('%',
            #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Sa_ProjectItem.tenantname like concat('%',
            #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Sa_ProjectItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.engineerid != null and SearchPojo.engineerid != ''">
                or Sa_ProjectItem.Engineerid like concat('%', #{SearchPojo.engineerid}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Sa_ProjectItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Sa_ProjectItem.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Sa_ProjectItem.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Sa_ProjectItem.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Sa_ProjectItem.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Sa_ProjectItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Sa_ProjectItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Sa_ProjectItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Sa_ProjectItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Sa_ProjectItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
                or Sa_ProjectItem.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Sa_ProjectItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaProjectitemPojo">
        select Sa_ProjectItem.id,
        Sa_ProjectItem.Pid,
        Sa_ProjectItem.Engineerid,
        Sa_ProjectItem.RoleType,
        Sa_ProjectItem.RowNum,
        Sa_ProjectItem.Remark,
        Sa_ProjectItem.CreateBy,
        Sa_ProjectItem.CreateByid,
        Sa_ProjectItem.CreateDate,
        Sa_ProjectItem.Lister,
        Sa_ProjectItem.Listerid,
        Sa_ProjectItem.ModifyDate,
        Sa_ProjectItem.Custom1,
        Sa_ProjectItem.Custom2,
        Sa_ProjectItem.Custom3,
        Sa_ProjectItem.Custom4,
        Sa_ProjectItem.Custom5,
        Sa_ProjectItem.Deptid,
        Sa_ProjectItem.Tenantid,
        Sa_ProjectItem.TenantName,
        Sa_ProjectItem.Revision,
        Sa_Engineer.EngineerName,
        Sa_Engineer.EngineerCode,
        Sa_Engineer.EngineerType
        from Sa_ProjectItem
        join Sa_Engineer on Sa_ProjectItem.Engineerid = Sa_Engineer.id
        where Sa_ProjectItem.Pid = #{Pid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_ProjectItem(id, Pid, Engineerid, RoleType, RowNum, Remark, CreateBy, CreateByid, CreateDate,
        Lister,
        Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid,
        TenantName, Revision)
        values (#{id}, #{pid}, #{engineerid}, #{roletype}, #{rownum}, #{remark}, #{createby}, #{createbyid},
        #{createdate},
        #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
        #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ProjectItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="engineerid != null">
                Engineerid = #{engineerid},
            </if>
            <if test="roletype != null">
                RoleType = #{roletype},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="createby != null">
                CreateBy = #{createby},
            </if>
            <if test="createbyid != null">
                CreateByid = #{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="lister != null">
                Lister = #{lister},
            </if>
            <if test="listerid != null">
                Listerid = #{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            <if test="deptid != null">
                Deptid = #{deptid},
            </if>
            <if test="tenantname != null">
                TenantName = #{tenantname},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_ProjectItem
        where id = #{key}
    </delete>
</mapper>

