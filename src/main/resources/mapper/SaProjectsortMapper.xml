<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaProjectsortMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaProjectsortPojo">
        select
        id, Projectid, Engineerid, StarMark, LastAccessTime, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister,
        Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, TenantName, Revision from
        Sa_ProjectSort
        where Sa_ProjectSort.id = #{key}
    </select>
    <sql id="selectSaProjectsortVo">
        select id,
               Projectid,
               <PERSON>id,
               StarMark,
               LastAccessTime,
               <PERSON><PERSON><PERSON>,
               <PERSON>mark,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Dept<PERSON>,
               <PERSON><PERSON><PERSON>,
               TenantName,
               Revision
        from Sa_ProjectSort
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaProjectsortPojo">
        <include refid="selectSaProjectsortVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_ProjectSort.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.projectid != null">
            and Sa_ProjectSort.Projectid like concat('%', #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.engineerid != null">
            and Sa_ProjectSort.Engineerid like concat('%', #{SearchPojo.engineerid}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_ProjectSort.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_ProjectSort.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_ProjectSort.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_ProjectSort.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_ProjectSort.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_ProjectSort.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_ProjectSort.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_ProjectSort.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_ProjectSort.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_ProjectSort.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_ProjectSort.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_ProjectSort.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.projectid != null">
                or Sa_ProjectSort.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.engineerid != null">
                or Sa_ProjectSort.Engineerid like concat('%', #{SearchPojo.engineerid}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_ProjectSort.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_ProjectSort.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_ProjectSort.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_ProjectSort.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_ProjectSort.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_ProjectSort.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_ProjectSort.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_ProjectSort.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_ProjectSort.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_ProjectSort.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_ProjectSort.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_ProjectSort.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_ProjectSort(id, Projectid, Engineerid, StarMark, LastAccessTime, RowNum, Remark, CreateBy,
        CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid,
        Tenantid, TenantName, Revision)
        values (#{id}, #{projectid}, #{engineerid}, #{starmark}, #{lastaccesstime}, #{rownum}, #{remark}, #{createby},
        #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3},
        #{custom4}, #{custom5}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ProjectSort
        <set>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="engineerid != null">
                Engineerid =#{engineerid},
            </if>
            <if test="starmark != null">
                StarMark =#{starmark},
            </if>
            <if test="lastaccesstime != null">
                LastAccessTime =#{lastaccesstime},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ProjectSort where id = #{key}
    </delete>

    <delete id="deleteByProjectIdAndProjectItemId">
        delete from Sa_ProjectSort
        where Projectid = #{id} and Engineerid = (select Engineerid from Sa_ProjectItem where id=#{itemid})
    </delete>

    <update id="updateLastAccessTime">
        update Sa_ProjectSort
        set LastAccessTime = #{date}
        where Projectid = #{projectid} and Engineerid = #{engineerid}
    </update>

    <update id="SetStar">
        update Sa_ProjectSort
        set StarMark = #{starmark}
        where Projectid = #{projectid} and Engineerid = #{engineerid}
    </update>
</mapper>

