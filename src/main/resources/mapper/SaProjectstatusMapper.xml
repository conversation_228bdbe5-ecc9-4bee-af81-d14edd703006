<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaProjectstatusMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaProjectstatusPojo">
        select
        id, Pid, StatusName, StatusType, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
        FinishMark, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, TenantName, Revision from
        Sa_ProjectStatus
        where Sa_ProjectStatus.id = #{key}
    </select>
    <sql id="selectSaProjectstatusVo">
        select id,
               <PERSON>d,
               <PERSON>Name,
               StatusType,
               <PERSON><PERSON><PERSON>,
               <PERSON>mark,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON>er,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               FinishMark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Deptid,
               Tenantid,
               TenantName,
               Revision
        from Sa_ProjectStatus
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaProjectstatusPojo">
        <include refid="selectSaProjectstatusVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_ProjectStatus.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Sa_ProjectStatus.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.statusname != null and SearchPojo.statusname != ''">
            and Sa_ProjectStatus.statusname like concat('%', #{SearchPojo.statusname}, '%')
        </if>
        <if test="SearchPojo.statustype != null and SearchPojo.statustype != ''">
            and Sa_ProjectStatus.statustype like concat('%', #{SearchPojo.statustype}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Sa_ProjectStatus.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Sa_ProjectStatus.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Sa_ProjectStatus.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Sa_ProjectStatus.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Sa_ProjectStatus.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Sa_ProjectStatus.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Sa_ProjectStatus.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Sa_ProjectStatus.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Sa_ProjectStatus.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Sa_ProjectStatus.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
            and Sa_ProjectStatus.deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Sa_ProjectStatus.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Sa_ProjectStatus.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.statusname != null and SearchPojo.statusname != ''">
                or Sa_ProjectStatus.StatusName like concat('%', #{SearchPojo.statusname}, '%')
            </if>
            <if test="SearchPojo.statustype != null and SearchPojo.statustype != ''">
                or Sa_ProjectStatus.StatusType like concat('%', #{SearchPojo.statustype}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Sa_ProjectStatus.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Sa_ProjectStatus.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Sa_ProjectStatus.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Sa_ProjectStatus.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Sa_ProjectStatus.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Sa_ProjectStatus.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Sa_ProjectStatus.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Sa_ProjectStatus.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Sa_ProjectStatus.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Sa_ProjectStatus.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
                or Sa_ProjectStatus.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Sa_ProjectStatus.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaProjectstatusPojo">
        select
        id, Pid, StatusName, StatusType, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
        FinishMark, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid, TenantName, Revision from
        Sa_ProjectStatus
        where Sa_ProjectStatus.Pid = #{Pid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_ProjectStatus(id, Pid, StatusName, StatusType, RowNum, Remark, CreateBy, CreateByid, CreateDate,
        Lister, Listerid, ModifyDate, FinishMark, Custom1, Custom2, Custom3, Custom4, Custom5, Deptid, Tenantid,
        TenantName, Revision)
        values (#{id}, #{pid}, #{statusname}, #{statustype}, #{rownum}, #{remark}, #{createby}, #{createbyid},
        #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{finishmark}, #{custom1}, #{custom2}, #{custom3},
        #{custom4}, #{custom5}, #{deptid}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ProjectStatus
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="statusname != null">
                StatusName = #{statusname},
            </if>
            <if test="statustype != null">
                StatusType = #{statustype},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="createby != null">
                CreateBy = #{createby},
            </if>
            <if test="createbyid != null">
                CreateByid = #{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="lister != null">
                Lister = #{lister},
            </if>
            <if test="listerid != null">
                Listerid = #{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="finishmark != null">
                FinishMark = #{finishmark},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            <if test="deptid != null">
                Deptid = #{deptid},
            </if>
            <if test="tenantname != null">
                TenantName = #{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ProjectStatus where id = #{key}
    </delete>
</mapper>

