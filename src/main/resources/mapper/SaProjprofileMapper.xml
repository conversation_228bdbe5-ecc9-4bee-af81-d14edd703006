<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaProjprofileMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaProjprofilePojo">
        select id,
        Projectid,
        ProjectName,
        Title,
        Videoid,
        VideoName,
        Content,
        Picture,
        FileName,
        DirName,
        LookTimes,
        GoodNum,
        NgNum,
        PublicMark,
        CreateBy,
        CreateByid,
        CreateDate,
        Lister,
        Listerid,
        ModifyDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Custom6,
        Custom7,
        Custom8,
        Custom9,
        Custom10,
        Tenantid,
        Revision
        from Sa_ProjProfile
        where Sa_ProjProfile.id = #{key}
    </select>
    <sql id="selectSaProjprofileVo">
        select id,
               Projectid,
               ProjectName,
               Title,
               Videoid,
               VideoName,
               Content,
               Picture,
               FileName,
               DirName,
               LookTimes,
               GoodNum,
               NgNum,
               PublicMark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Revision
        from Sa_ProjProfile
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaProjprofilePojo">
        <include refid="selectSaProjprofileVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_ProjProfile.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.projectid != null">
            and Sa_ProjProfile.Projectid like concat('%', #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.projectname != null">
            and Sa_ProjProfile.ProjectName like concat('%',
            #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.title != null">
            and Sa_ProjProfile.Title like concat('%',
            #{SearchPojo.title}, '%')
        </if>
        <if test="SearchPojo.videoid != null">
            and Sa_ProjProfile.Videoid like concat('%',
            #{SearchPojo.videoid}, '%')
        </if>
        <if test="SearchPojo.videoname != null">
            and Sa_ProjProfile.VideoName like concat('%',
            #{SearchPojo.videoname}, '%')
        </if>
        <if test="SearchPojo.content != null">
            and Sa_ProjProfile.Content like concat('%',
            #{SearchPojo.content}, '%')
        </if>
        <if test="SearchPojo.picture != null">
            and Sa_ProjProfile.Picture like concat('%',
            #{SearchPojo.picture}, '%')
        </if>
        <if test="SearchPojo.filename != null">
            and Sa_ProjProfile.FileName like concat('%',
            #{SearchPojo.filename}, '%')
        </if>
        <if test="SearchPojo.dirname != null">
            and Sa_ProjProfile.DirName like concat('%',
            #{SearchPojo.dirname}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_ProjProfile.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_ProjProfile.CreateByid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_ProjProfile.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_ProjProfile.Listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_ProjProfile.Custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_ProjProfile.Custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_ProjProfile.Custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_ProjProfile.Custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_ProjProfile.Custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_ProjProfile.Custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_ProjProfile.Custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_ProjProfile.Custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_ProjProfile.Custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_ProjProfile.Custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.projectid != null">
                or Sa_ProjProfile.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.projectname != null">
                or Sa_ProjProfile.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
            </if>
            <if test="SearchPojo.title != null">
                or Sa_ProjProfile.Title like concat('%', #{SearchPojo.title}, '%')
            </if>
            <if test="SearchPojo.videoid != null">
                or Sa_ProjProfile.Videoid like concat('%', #{SearchPojo.videoid}, '%')
            </if>
            <if test="SearchPojo.videoname != null">
                or Sa_ProjProfile.VideoName like concat('%', #{SearchPojo.videoname}, '%')
            </if>
            <if test="SearchPojo.content != null">
                or Sa_ProjProfile.Content like concat('%', #{SearchPojo.content}, '%')
            </if>
            <if test="SearchPojo.picture != null">
                or Sa_ProjProfile.Picture like concat('%', #{SearchPojo.picture}, '%')
            </if>
            <if test="SearchPojo.filename != null">
                or Sa_ProjProfile.FileName like concat('%', #{SearchPojo.filename}, '%')
            </if>
            <if test="SearchPojo.dirname != null">
                or Sa_ProjProfile.DirName like concat('%', #{SearchPojo.dirname}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_ProjProfile.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_ProjProfile.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_ProjProfile.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_ProjProfile.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_ProjProfile.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_ProjProfile.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_ProjProfile.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_ProjProfile.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_ProjProfile.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_ProjProfile.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_ProjProfile.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_ProjProfile.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_ProjProfile.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_ProjProfile.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_ProjProfile(id, Projectid, ProjectName, Title, Videoid, VideoName, Content, Picture, FileName,
        DirName, LookTimes, GoodNum, NgNum, PublicMark, CreateBy, CreateByid, CreateDate,
        Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
        Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{projectid}, #{projectname}, #{title}, #{videoid}, #{videoname}, #{content}, #{picture},
        #{filename}, #{dirname}, #{looktimes}, #{goodnum}, #{ngnum}, #{publicmark}, #{createby}, #{createbyid},
        #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4},
        #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ProjProfile
        <set>
            <if test="projectid != null">
                Projectid =#{projectid},
            </if>
            <if test="projectname != null">
                ProjectName =#{projectname},
            </if>
            <if test="title != null">
                Title =#{title},
            </if>
            <if test="videoid != null">
                Videoid =#{videoid},
            </if>
            <if test="videoname != null">
                VideoName =#{videoname},
            </if>
            <if test="content != null">
                Content =#{content},
            </if>
            <if test="picture != null">
                Picture =#{picture},
            </if>
            <if test="filename != null">
                FileName =#{filename},
            </if>
            <if test="dirname != null">
                DirName =#{dirname},
            </if>
            <if test="looktimes != null">
                LookTimes =#{looktimes},
            </if>
            <if test="goodnum != null">
                GoodNum =#{goodnum},
            </if>
            <if test="ngnum != null">
                NgNum =#{ngnum},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_ProjProfile
        where id = #{key}
    </delete>
</mapper>

