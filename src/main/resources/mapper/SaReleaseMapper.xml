<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaReleaseMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaReleasePojo">
        <include refid="selectbillVo"/>
        where Sa_Release.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, ReleaseName, ProjectName, ReleaseDate, Version, Description, Remark, Milestone, FinishMark, Closed, PhotoUrl1, PhotoUrl2, PhotoUrl3, Projectid, ProjectCode, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Sa_Release
    </sql>
    <sql id="selectdetailVo">
         select
id, ReleaseName, ProjectName, ReleaseDate, Version, Description, Remark, Milestone, FinishMark, Closed, PhotoUrl1, PhotoUrl2, PhotoUrl3, Projectid, ProjectCode, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision        from Sa_Release
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaReleaseitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Release.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.releasename != null">
            and Sa_Release.releasename like concat('%', #{SearchPojo.releasename}, '%')
        </if>
        <if test="SearchPojo.version != null">
            and Sa_Release.version like concat('%', #{SearchPojo.version}, '%')
        </if>
        <if test="SearchPojo.description != null">
            and Sa_Release.description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.projectid != null">
            and Sa_Release.projectid like concat('%', #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.projectcode != null">
            and Sa_Release.projectcode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.projectname != null">
            and Sa_Release.projectname like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Release.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.photourl1 != null">
            and Sa_Release.photourl1 like concat('%', #{SearchPojo.photourl1}, '%')
        </if>
        <if test="SearchPojo.photourl2 != null">
            and Sa_Release.photourl2 like concat('%', #{SearchPojo.photourl2}, '%')
        </if>
        <if test="SearchPojo.photourl3 != null">
            and Sa_Release.photourl3 like concat('%', #{SearchPojo.photourl3}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Release.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Release.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Release.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Release.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_Release.assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_Release.assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Release.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Release.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Release.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Release.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Release.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Release.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Release.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Release.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Release.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Release.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Release.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.releasename != null">
                or Sa_Release.ReleaseName like concat('%', #{SearchPojo.releasename}, '%')
            </if>
            <if test="SearchPojo.version != null">
                or Sa_Release.Version like concat('%', #{SearchPojo.version}, '%')
            </if>
            <if test="SearchPojo.description != null">
                or Sa_Release.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.projectid != null">
                or Sa_Release.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.projectcode != null">
                or Sa_Release.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
            </if>
            <if test="SearchPojo.projectname != null">
                or Sa_Release.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Release.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.photourl1 != null">
                or Sa_Release.PhotoUrl1 like concat('%', #{SearchPojo.photourl1}, '%')
            </if>
            <if test="SearchPojo.photourl2 != null">
                or Sa_Release.PhotoUrl2 like concat('%', #{SearchPojo.photourl2}, '%')
            </if>
            <if test="SearchPojo.photourl3 != null">
                or Sa_Release.PhotoUrl3 like concat('%', #{SearchPojo.photourl3}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Release.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Release.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Release.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Release.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_Release.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_Release.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Release.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Release.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Release.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Release.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Release.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Release.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Release.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Release.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Release.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Release.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Release.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaReleasePojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Release.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.releasename != null">
            and Sa_Release.ReleaseName like concat('%', #{SearchPojo.releasename}, '%')
        </if>
        <if test="SearchPojo.version != null">
            and Sa_Release.Version like concat('%', #{SearchPojo.version}, '%')
        </if>
        <if test="SearchPojo.description != null">
            and Sa_Release.Description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.projectid != null">
            and Sa_Release.Projectid like concat('%', #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.projectcode != null">
            and Sa_Release.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
        </if>
        <if test="SearchPojo.projectname != null">
            and Sa_Release.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Release.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.photourl1 != null">
            and Sa_Release.PhotoUrl1 like concat('%', #{SearchPojo.photourl1}, '%')
        </if>
        <if test="SearchPojo.photourl2 != null">
            and Sa_Release.PhotoUrl2 like concat('%', #{SearchPojo.photourl2}, '%')
        </if>
        <if test="SearchPojo.photourl3 != null">
            and Sa_Release.PhotoUrl3 like concat('%', #{SearchPojo.photourl3}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Release.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Release.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Release.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Release.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_Release.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_Release.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Release.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Release.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Release.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Release.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Release.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Release.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Release.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Release.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Release.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Release.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Release.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.releasename != null">
                or Sa_Release.ReleaseName like concat('%', #{SearchPojo.releasename}, '%')
            </if>
            <if test="SearchPojo.version != null">
                or Sa_Release.Version like concat('%', #{SearchPojo.version}, '%')
            </if>
            <if test="SearchPojo.description != null">
                or Sa_Release.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.projectid != null">
                or Sa_Release.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.projectcode != null">
                or Sa_Release.ProjectCode like concat('%', #{SearchPojo.projectcode}, '%')
            </if>
            <if test="SearchPojo.projectname != null">
                or Sa_Release.ProjectName like concat('%', #{SearchPojo.projectname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Release.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.photourl1 != null">
                or Sa_Release.PhotoUrl1 like concat('%', #{SearchPojo.photourl1}, '%')
            </if>
            <if test="SearchPojo.photourl2 != null">
                or Sa_Release.PhotoUrl2 like concat('%', #{SearchPojo.photourl2}, '%')
            </if>
            <if test="SearchPojo.photourl3 != null">
                or Sa_Release.PhotoUrl3 like concat('%', #{SearchPojo.photourl3}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Release.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Release.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Release.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Release.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_Release.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_Release.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Release.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Release.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Release.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Release.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Release.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Release.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Release.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Release.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Release.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Release.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Release.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Release(id, ReleaseName, ReleaseDate, Version, Description, Milestone, Projectid,
        ProjectCode, ProjectName, RowNum, Remark, FinishMark, Closed, PhotoUrl1, PhotoUrl2, PhotoUrl3, CreateByid,
        CreateBy, CreateDate, Listerid, Lister, ModifyDate, Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3,
        Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{releasename}, #{releasedate}, #{version}, #{description}, #{milestone},
        #{projectid}, #{projectcode}, #{projectname}, #{rownum}, #{remark}, #{finishmark}, #{closed}, #{photourl1},
        #{photourl2}, #{photourl3}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate},
        #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
        #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Release
        <set>
            <if test="releasename != null ">
                ReleaseName =#{releasename},
            </if>
            <if test="projectname != null ">
                ProjectName =#{projectname},
            </if>
            <if test="releasedate != null">
                ReleaseDate =#{releasedate},
            </if>
            <if test="version != null ">
                Version =#{version},
            </if>
            <if test="description != null ">
                Description =#{description},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="milestone != null">
                Milestone =#{milestone},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="photourl1 != null ">
                PhotoUrl1 =#{photourl1},
            </if>
            <if test="photourl2 != null ">
                PhotoUrl2 =#{photourl2},
            </if>
            <if test="photourl3 != null ">
                PhotoUrl3 =#{photourl3},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="projectcode != null ">
                ProjectCode =#{projectcode},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Release where id = #{key}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Release SET
        Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision+1
        where id = #{id}
    </update>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.pms.domain.pojo.SaReleasePojo">
        select
        id
        from Sa_ReleaseItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="countByRemark" resultType="int">
        select count(*) from Sa_Release where Remark =#{remark}
    </select>
</mapper>

