<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaReleaseitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaReleaseitemPojo">
        select
        id, Pid, RefNo, BillDate, BillType, BillTitle, Todoid, TdContent, RowNum, Remark, FinishMark, Closed,
        ImportantMark, UrgentMark, TargetJson, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3,
        CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Accepterid, Accepter, Custom1, Custom2, Custom3,
        Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, <PERSON>antid, Deptid, TenantName, Revision from
        Sa_ReleaseItem
        where Sa_ReleaseItem.id = #{key}
    </select>
    <sql id="selectSaReleaseitemVo">
        select id,
               Pid,
               RefNo,
               BillDate,
               BillType,
               BillTitle,
               Todoid,
               TdContent,
               RowNum,
               Remark,
               FinishMark,
               Closed,
               ImportantMark,
               UrgentMark,
               TargetJson,
               PhotoUrl1,
               PhotoUrl2,
               PhotoUrl3,
               PhotoName1,
               PhotoName2,
               PhotoName3,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               Accepterid,
               Accepter,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Deptid,
               TenantName,
               Revision
        from Sa_ReleaseItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaReleaseitemPojo">
        <include refid="selectSaReleaseitemVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_ReleaseItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Sa_ReleaseItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
            and Sa_ReleaseItem.refno like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
            and Sa_ReleaseItem.billtype like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
            and Sa_ReleaseItem.billtitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.todoid != null and SearchPojo.todoid != ''">
            and Sa_ReleaseItem.todoid like concat('%', #{SearchPojo.todoid}, '%')
        </if>
        <if test="SearchPojo.tdcontent != null and SearchPojo.tdcontent != ''">
            and Sa_ReleaseItem.tdcontent like concat('%', #{SearchPojo.tdcontent}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Sa_ReleaseItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.targetjson != null and SearchPojo.targetjson != ''">
            and Sa_ReleaseItem.targetjson like concat('%', #{SearchPojo.targetjson}, '%')
        </if>
        <if test="SearchPojo.photourl1 != null and SearchPojo.photourl1 != ''">
            and Sa_ReleaseItem.photourl1 like concat('%', #{SearchPojo.photourl1}, '%')
        </if>
        <if test="SearchPojo.photourl2 != null and SearchPojo.photourl2 != ''">
            and Sa_ReleaseItem.photourl2 like concat('%', #{SearchPojo.photourl2}, '%')
        </if>
        <if test="SearchPojo.photourl3 != null and SearchPojo.photourl3 != ''">
            and Sa_ReleaseItem.photourl3 like concat('%', #{SearchPojo.photourl3}, '%')
        </if>
        <if test="SearchPojo.photoname1 != null and SearchPojo.photoname1 != ''">
            and Sa_ReleaseItem.photoname1 like concat('%', #{SearchPojo.photoname1}, '%')
        </if>
        <if test="SearchPojo.photoname2 != null and SearchPojo.photoname2 != ''">
            and Sa_ReleaseItem.photoname2 like concat('%', #{SearchPojo.photoname2}, '%')
        </if>
        <if test="SearchPojo.photoname3 != null and SearchPojo.photoname3 != ''">
            and Sa_ReleaseItem.photoname3 like concat('%', #{SearchPojo.photoname3}, '%')
        </if>
        <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
            and Sa_ReleaseItem.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
            and Sa_ReleaseItem.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
            and Sa_ReleaseItem.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
            and Sa_ReleaseItem.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.accepterid != null and SearchPojo.accepterid != ''">
            and Sa_ReleaseItem.accepterid like concat('%', #{SearchPojo.accepterid}, '%')
        </if>
        <if test="SearchPojo.accepter != null and SearchPojo.accepter != ''">
            and Sa_ReleaseItem.accepter like concat('%', #{SearchPojo.accepter}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Sa_ReleaseItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Sa_ReleaseItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Sa_ReleaseItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Sa_ReleaseItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Sa_ReleaseItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
            and Sa_ReleaseItem.custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
            and Sa_ReleaseItem.custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
            and Sa_ReleaseItem.custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
            and Sa_ReleaseItem.custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
            and Sa_ReleaseItem.custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
            and Sa_ReleaseItem.deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
            and Sa_ReleaseItem.tenantname like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Sa_ReleaseItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.refno != null and SearchPojo.refno != ''">
                or Sa_ReleaseItem.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null and SearchPojo.billtype != ''">
                or Sa_ReleaseItem.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null and SearchPojo.billtitle != ''">
                or Sa_ReleaseItem.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.todoid != null and SearchPojo.todoid != ''">
                or Sa_ReleaseItem.Todoid like concat('%', #{SearchPojo.todoid}, '%')
            </if>
            <if test="SearchPojo.tdcontent != null and SearchPojo.tdcontent != ''">
                or Sa_ReleaseItem.TdContent like concat('%', #{SearchPojo.tdcontent}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Sa_ReleaseItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.targetjson != null and SearchPojo.targetjson != ''">
                or Sa_ReleaseItem.TargetJson like concat('%', #{SearchPojo.targetjson}, '%')
            </if>
            <if test="SearchPojo.photourl1 != null and SearchPojo.photourl1 != ''">
                or Sa_ReleaseItem.PhotoUrl1 like concat('%', #{SearchPojo.photourl1}, '%')
            </if>
            <if test="SearchPojo.photourl2 != null and SearchPojo.photourl2 != ''">
                or Sa_ReleaseItem.PhotoUrl2 like concat('%', #{SearchPojo.photourl2}, '%')
            </if>
            <if test="SearchPojo.photourl3 != null and SearchPojo.photourl3 != ''">
                or Sa_ReleaseItem.PhotoUrl3 like concat('%', #{SearchPojo.photourl3}, '%')
            </if>
            <if test="SearchPojo.photoname1 != null and SearchPojo.photoname1 != ''">
                or Sa_ReleaseItem.PhotoName1 like concat('%', #{SearchPojo.photoname1}, '%')
            </if>
            <if test="SearchPojo.photoname2 != null and SearchPojo.photoname2 != ''">
                or Sa_ReleaseItem.PhotoName2 like concat('%', #{SearchPojo.photoname2}, '%')
            </if>
            <if test="SearchPojo.photoname3 != null and SearchPojo.photoname3 != ''">
                or Sa_ReleaseItem.PhotoName3 like concat('%', #{SearchPojo.photoname3}, '%')
            </if>
            <if test="SearchPojo.createbyid != null and SearchPojo.createbyid != ''">
                or Sa_ReleaseItem.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null and SearchPojo.createby != ''">
                or Sa_ReleaseItem.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null and SearchPojo.listerid != ''">
                or Sa_ReleaseItem.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null and SearchPojo.lister != ''">
                or Sa_ReleaseItem.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.accepterid != null and SearchPojo.accepterid != ''">
                or Sa_ReleaseItem.Accepterid like concat('%', #{SearchPojo.accepterid}, '%')
            </if>
            <if test="SearchPojo.accepter != null and SearchPojo.accepter != ''">
                or Sa_ReleaseItem.Accepter like concat('%', #{SearchPojo.accepter}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Sa_ReleaseItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Sa_ReleaseItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Sa_ReleaseItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Sa_ReleaseItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Sa_ReleaseItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null and SearchPojo.custom6 != ''">
                or Sa_ReleaseItem.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null and SearchPojo.custom7 != ''">
                or Sa_ReleaseItem.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null and SearchPojo.custom8 != ''">
                or Sa_ReleaseItem.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null and SearchPojo.custom9 != ''">
                or Sa_ReleaseItem.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null and SearchPojo.custom10 != ''">
                or Sa_ReleaseItem.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null and SearchPojo.deptid != ''">
                or Sa_ReleaseItem.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null and SearchPojo.tenantname != ''">
                or Sa_ReleaseItem.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaReleaseitemPojo">
        select
        id, Pid, RefNo, BillDate, BillType, BillTitle, Todoid, TdContent, RowNum, Remark, FinishMark, Closed,
        ImportantMark, UrgentMark, TargetJson, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3,
        CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Accepterid, Accepter, Custom1, Custom2, Custom3,
        Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Deptid, TenantName, Revision from
        Sa_ReleaseItem
        where Sa_ReleaseItem.Pid = #{Pid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_ReleaseItem(id, Pid, RefNo, BillDate, BillType, BillTitle, Todoid, TdContent, RowNum, Remark,
        FinishMark, Closed, ImportantMark, UrgentMark, TargetJson, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1,
        PhotoName2, PhotoName3, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, Accepterid, Accepter,
        Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Deptid,
        TenantName, Revision)
        values (#{id}, #{pid}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{todoid}, #{tdcontent}, #{rownum},
        #{remark}, #{finishmark}, #{closed}, #{importantmark}, #{urgentmark}, #{targetjson}, #{photourl1}, #{photourl2},
        #{photourl3}, #{photoname1}, #{photoname2}, #{photoname3}, #{createbyid}, #{createby}, #{createdate},
        #{listerid}, #{lister}, #{modifydate}, #{accepterid}, #{accepter}, #{custom1}, #{custom2}, #{custom3},
        #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{deptid},
        #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ReleaseItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="refno != null">
                RefNo = #{refno},
            </if>
            <if test="billdate != null">
                BillDate = #{billdate},
            </if>
            <if test="billtype != null">
                BillType = #{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle = #{billtitle},
            </if>
            <if test="todoid != null">
                Todoid = #{todoid},
            </if>
            <if test="tdcontent != null">
                TdContent = #{tdcontent},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="finishmark != null">
                FinishMark = #{finishmark},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="importantmark != null">
                ImportantMark = #{importantmark},
            </if>
            <if test="urgentmark != null">
                UrgentMark = #{urgentmark},
            </if>
            <if test="targetjson != null">
                TargetJson = #{targetjson},
            </if>
            <if test="photourl1 != null">
                PhotoUrl1 = #{photourl1},
            </if>
            <if test="photourl2 != null">
                PhotoUrl2 = #{photourl2},
            </if>
            <if test="photourl3 != null">
                PhotoUrl3 = #{photourl3},
            </if>
            <if test="photoname1 != null">
                PhotoName1 = #{photoname1},
            </if>
            <if test="photoname2 != null">
                PhotoName2 = #{photoname2},
            </if>
            <if test="photoname3 != null">
                PhotoName3 = #{photoname3},
            </if>
            <if test="createbyid != null">
                CreateByid = #{createbyid},
            </if>
            <if test="createby != null">
                CreateBy = #{createby},
            </if>
            <if test="createdate != null">
                CreateDate = #{createdate},
            </if>
            <if test="listerid != null">
                Listerid = #{listerid},
            </if>
            <if test="lister != null">
                Lister = #{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate = #{modifydate},
            </if>
            <if test="accepterid != null">
                Accepterid = #{accepterid},
            </if>
            <if test="accepter != null">
                Accepter = #{accepter},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 = #{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 = #{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 = #{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 = #{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 = #{custom10},
            </if>
            <if test="deptid != null">
                Deptid = #{deptid},
            </if>
            <if test="tenantname != null">
                TenantName = #{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ReleaseItem where id = #{key}
    </delete>
</mapper>

