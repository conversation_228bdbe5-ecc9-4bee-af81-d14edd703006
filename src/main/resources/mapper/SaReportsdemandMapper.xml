<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaReportsdemandMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaReportsdemandPojo">
        <include refid="selectSaReportsdemandVo"/>
        where Sa_ReportsDemand.id = #{key} 
    </select>
    <sql id="selectSaReportsdemandVo">
         select
id, ModuleCode, RptType, RptName, UserDemand, Company, OrgGrfData, GrfData, SerCode, Operator, Operatorid, FinishDate, FinishDesc, FinishMark, EnabledMark, RowNum, Remark, CreateBy, C<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ate, <PERSON>er, <PERSON><PERSON><PERSON>, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, <PERSON>anti<PERSON>, TenantName, Revision        from Sa_ReportsDemand
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaReportsdemandPojo">
        <include refid="selectSaReportsdemandVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_ReportsDemand.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.modulecode != null ">
   and Sa_ReportsDemand.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.rpttype != null ">
   and Sa_ReportsDemand.RptType like concat('%', #{SearchPojo.rpttype}, '%')
</if>
<if test="SearchPojo.rptname != null ">
   and Sa_ReportsDemand.RptName like concat('%', #{SearchPojo.rptname}, '%')
</if>
<if test="SearchPojo.userdemand != null ">
   and Sa_ReportsDemand.UserDemand like concat('%', #{SearchPojo.userdemand}, '%')
</if>
<if test="SearchPojo.company != null ">
   and Sa_ReportsDemand.Company like concat('%', #{SearchPojo.company}, '%')
</if>
<if test="SearchPojo.orggrfdata != null ">
   and Sa_ReportsDemand.OrgGrfData like concat('%', #{SearchPojo.orggrfdata}, '%')
</if>
<if test="SearchPojo.grfdata != null ">
   and Sa_ReportsDemand.GrfData like concat('%', #{SearchPojo.grfdata}, '%')
</if>
<if test="SearchPojo.sercode != null ">
   and Sa_ReportsDemand.SerCode like concat('%', #{SearchPojo.sercode}, '%')
</if>
<if test="SearchPojo.operator != null ">
   and Sa_ReportsDemand.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_ReportsDemand.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.finishdesc != null ">
   and Sa_ReportsDemand.FinishDesc like concat('%', #{SearchPojo.finishdesc}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_ReportsDemand.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_ReportsDemand.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_ReportsDemand.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_ReportsDemand.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_ReportsDemand.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_ReportsDemand.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_ReportsDemand.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_ReportsDemand.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_ReportsDemand.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_ReportsDemand.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_ReportsDemand.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.modulecode != null ">
   or Sa_ReportsDemand.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
</if>
<if test="SearchPojo.rpttype != null ">
   or Sa_ReportsDemand.RptType like concat('%', #{SearchPojo.rpttype}, '%')
</if>
<if test="SearchPojo.rptname != null ">
   or Sa_ReportsDemand.RptName like concat('%', #{SearchPojo.rptname}, '%')
</if>
<if test="SearchPojo.userdemand != null ">
   or Sa_ReportsDemand.UserDemand like concat('%', #{SearchPojo.userdemand}, '%')
</if>
<if test="SearchPojo.company != null ">
   or Sa_ReportsDemand.Company like concat('%', #{SearchPojo.company}, '%')
</if>
<if test="SearchPojo.orggrfdata != null ">
   or Sa_ReportsDemand.OrgGrfData like concat('%', #{SearchPojo.orggrfdata}, '%')
</if>
<if test="SearchPojo.grfdata != null ">
   or Sa_ReportsDemand.GrfData like concat('%', #{SearchPojo.grfdata}, '%')
</if>
<if test="SearchPojo.sercode != null ">
   or Sa_ReportsDemand.SerCode like concat('%', #{SearchPojo.sercode}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_ReportsDemand.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_ReportsDemand.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.finishdesc != null ">
   or Sa_ReportsDemand.FinishDesc like concat('%', #{SearchPojo.finishdesc}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_ReportsDemand.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_ReportsDemand.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_ReportsDemand.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_ReportsDemand.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_ReportsDemand.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_ReportsDemand.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_ReportsDemand.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_ReportsDemand.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_ReportsDemand.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_ReportsDemand.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_ReportsDemand.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_ReportsDemand(id, ModuleCode, RptType, RptName, UserDemand, Company, OrgGrfData, GrfData, SerCode, Operator, Operatorid, FinishDate, FinishDesc, FinishMark, EnabledMark, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{modulecode}, #{rpttype}, #{rptname}, #{userdemand}, #{company}, #{orggrfdata}, #{grfdata}, #{sercode}, #{operator}, #{operatorid}, #{finishdate}, #{finishdesc}, #{finishmark}, #{enabledmark}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ReportsDemand
        <set>
            <if test="modulecode != null ">
                ModuleCode =#{modulecode},
            </if>
            <if test="rpttype != null ">
                RptType =#{rpttype},
            </if>
            <if test="rptname != null ">
                RptName =#{rptname},
            </if>
            <if test="userdemand != null ">
                UserDemand =#{userdemand},
            </if>
            <if test="company != null ">
                Company =#{company},
            </if>
            <if test="orggrfdata != null ">
                OrgGrfData =#{orggrfdata},
            </if>
            <if test="grfdata != null ">
                GrfData =#{grfdata},
            </if>
            <if test="sercode != null ">
                SerCode =#{sercode},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="finishdate != null">
                FinishDate =#{finishdate},
            </if>
            <if test="finishdesc != null ">
                FinishDesc =#{finishdesc},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ReportsDemand where id = #{key} 
    </delete>

    <select id="checkSerCodeExists" resultType="int">
        select count(1) from Sa_ReportsDemand where SerCode = #{sercode}
    </select>

    <select id="getEntityBySerCode" resultType="inks.service.sa.pms.domain.pojo.SaReportsdemandPojo">
        <include refid="selectSaReportsdemandVo"/>
        where Sa_ReportsDemand.SerCode = #{sercode}
    </select>
</mapper>

