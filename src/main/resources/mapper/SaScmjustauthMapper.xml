<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaScmjustauthMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaScmjustauthPojo">
        select
        id, Userid, UserName, RealName, NickName, AuthType, AuthUuid, Unionid, AuthAvatar, CreateBy, CreateByid,
        CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName,
        Revision from Sa_ScmJustAuth
        where Sa_ScmJustAuth.id = #{key}
    </select>
    <sql id="selectSaScmjustauthVo">
        select id,
               <PERSON>rid,
               User<PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON>th<PERSON><PERSON>,
               <PERSON>th<PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON>th<PERSON>vatar,
               <PERSON><PERSON><PERSON>y,
               <PERSON>reate<PERSON>yid,
               <PERSON>reate<PERSON><PERSON>,
               Lister,
               Listerid,
               ModifyDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Tenantid,
               TenantName,
               Revision
        from Sa_ScmJustAuth
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaScmjustauthPojo">
        <include refid="selectSaScmjustauthVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_ScmJustAuth.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.userid != null">
            and Sa_ScmJustAuth.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.username != null">
            and Sa_ScmJustAuth.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null">
            and Sa_ScmJustAuth.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.nickname != null">
            and Sa_ScmJustAuth.NickName like concat('%', #{SearchPojo.nickname}, '%')
        </if>
        <if test="SearchPojo.authtype != null">
            and Sa_ScmJustAuth.AuthType like concat('%', #{SearchPojo.authtype}, '%')
        </if>
        <if test="SearchPojo.authuuid != null">
            and Sa_ScmJustAuth.AuthUuid like concat('%', #{SearchPojo.authuuid}, '%')
        </if>
        <if test="SearchPojo.unionid != null">
            and Sa_ScmJustAuth.Unionid like concat('%', #{SearchPojo.unionid}, '%')
        </if>
        <if test="SearchPojo.authavatar != null">
            and Sa_ScmJustAuth.AuthAvatar like concat('%', #{SearchPojo.authavatar}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_ScmJustAuth.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_ScmJustAuth.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_ScmJustAuth.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_ScmJustAuth.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_ScmJustAuth.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_ScmJustAuth.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_ScmJustAuth.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_ScmJustAuth.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_ScmJustAuth.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_ScmJustAuth.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.userid != null">
                or Sa_ScmJustAuth.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.username != null">
                or Sa_ScmJustAuth.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null">
                or Sa_ScmJustAuth.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.nickname != null">
                or Sa_ScmJustAuth.NickName like concat('%', #{SearchPojo.nickname}, '%')
            </if>
            <if test="SearchPojo.authtype != null">
                or Sa_ScmJustAuth.AuthType like concat('%', #{SearchPojo.authtype}, '%')
            </if>
            <if test="SearchPojo.authuuid != null">
                or Sa_ScmJustAuth.AuthUuid like concat('%', #{SearchPojo.authuuid}, '%')
            </if>
            <if test="SearchPojo.unionid != null">
                or Sa_ScmJustAuth.Unionid like concat('%', #{SearchPojo.unionid}, '%')
            </if>
            <if test="SearchPojo.authavatar != null">
                or Sa_ScmJustAuth.AuthAvatar like concat('%', #{SearchPojo.authavatar}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_ScmJustAuth.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_ScmJustAuth.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_ScmJustAuth.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_ScmJustAuth.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_ScmJustAuth.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_ScmJustAuth.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_ScmJustAuth.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_ScmJustAuth.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_ScmJustAuth.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_ScmJustAuth.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_ScmJustAuth(id, Userid, UserName, RealName, NickName, AuthType, AuthUuid, Unionid, AuthAvatar,
        CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5,
        Tenantid, TenantName, Revision)
        values (#{id}, #{userid}, #{username}, #{realname}, #{nickname}, #{authtype}, #{authuuid}, #{unionid},
        #{authavatar}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1},
        #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ScmJustAuth
        <set>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="username != null">
                UserName =#{username},
            </if>
            <if test="realname != null">
                RealName =#{realname},
            </if>
            <if test="nickname != null">
                NickName =#{nickname},
            </if>
            <if test="authtype != null">
                AuthType =#{authtype},
            </if>
            <if test="authuuid != null">
                AuthUuid =#{authuuid},
            </if>
            <if test="unionid != null">
                Unionid =#{unionid},
            </if>
            <if test="authavatar != null">
                AuthAvatar =#{authavatar},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ScmJustAuth where id = #{key}
    </delete>

    <delete id="deleteByOpenid">
        delete from Sa_ScmJustAuth where AuthUuid = #{openid}
    </delete>

    <select id="getEntityByAuthuuid" resultType="inks.service.sa.pms.domain.pojo.SaScmjustauthPojo">
        select * from Sa_ScmJustAuth where AuthUuid = #{authuuid}
    </select>
</mapper>

