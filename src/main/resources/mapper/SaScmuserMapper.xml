<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaScmuserMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaScmuserPojo">
        <include refid="selectSaScmuserVo"/>
        where Sa_ScmUser.Userid = #{key}
    </select>
    <sql id="selectSaScmuserVo">
        select Userid,
               UserName,
               RealName,
               NickName,
               UserPassword,
               Mobile,
               Email,
               Sex,
               LangCode,
               Avatar,
               UserType,
               Goodsid,
               IsAdmin,
               Deptid,
               DeptCode,
               DeptName,
               IsDeptAdmin,
               DeptRowNum,
               <PERSON><PERSON>um,
               UserStatus,
               UserCode,
               Groupids,
               GroupNames,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON><PERSON><PERSON>,
               <PERSON><PERSON>,
               <PERSON><PERSON><PERSON>,
               <PERSON>difyDate,
               <PERSON><PERSON>d,
               <PERSON>ant<PERSON><PERSON>,
               Revision
        from Sa_ScmUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaScmuserPojo">
        <include refid="selectSaScmuserVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_ScmUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null">
            and Sa_ScmUser.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null">
            and Sa_ScmUser.RealName like concat('%', #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.nickname != null">
            and Sa_ScmUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
        </if>
        <if test="SearchPojo.userpassword != null">
            and Sa_ScmUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
        </if>
        <if test="SearchPojo.mobile != null">
            and Sa_ScmUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.email != null">
            and Sa_ScmUser.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.langcode != null">
            and Sa_ScmUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
        </if>
        <if test="SearchPojo.avatar != null">
            and Sa_ScmUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_ScmUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.deptcode != null">
            and Sa_ScmUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
        </if>
        <if test="SearchPojo.deptname != null">
            and Sa_ScmUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
        </if>
        <if test="SearchPojo.usercode != null">
            and Sa_ScmUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
        </if>
        <if test="SearchPojo.groupids != null">
            and Sa_ScmUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
        </if>
        <if test="SearchPojo.groupnames != null">
            and Sa_ScmUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_ScmUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_ScmUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_ScmUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_ScmUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_ScmUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_ScmUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.username != null">
                or Sa_ScmUser.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null">
                or Sa_ScmUser.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.nickname != null">
                or Sa_ScmUser.NickName like concat('%', #{SearchPojo.nickname}, '%')
            </if>
            <if test="SearchPojo.userpassword != null">
                or Sa_ScmUser.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
            </if>
            <if test="SearchPojo.mobile != null">
                or Sa_ScmUser.Mobile like concat('%', #{SearchPojo.mobile}, '%')
            </if>
            <if test="SearchPojo.email != null">
                or Sa_ScmUser.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.langcode != null">
                or Sa_ScmUser.LangCode like concat('%', #{SearchPojo.langcode}, '%')
            </if>
            <if test="SearchPojo.avatar != null">
                or Sa_ScmUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_ScmUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.deptcode != null">
                or Sa_ScmUser.DeptCode like concat('%', #{SearchPojo.deptcode}, '%')
            </if>
            <if test="SearchPojo.deptname != null">
                or Sa_ScmUser.DeptName like concat('%', #{SearchPojo.deptname}, '%')
            </if>
            <if test="SearchPojo.usercode != null">
                or Sa_ScmUser.UserCode like concat('%', #{SearchPojo.usercode}, '%')
            </if>
            <if test="SearchPojo.groupids != null">
                or Sa_ScmUser.Groupids like concat('%', #{SearchPojo.groupids}, '%')
            </if>
            <if test="SearchPojo.groupnames != null">
                or Sa_ScmUser.GroupNames like concat('%', #{SearchPojo.groupnames}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_ScmUser.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_ScmUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_ScmUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_ScmUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_ScmUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_ScmUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_ScmUser(Userid, UserName, RealName, NickName, UserPassword, Mobile, Email, Sex, LangCode, Avatar,
        UserType, Goodsid, IsAdmin, Deptid, DeptCode, DeptName, IsDeptAdmin, DeptRowNum, RowNum,
        UserStatus, UserCode, Groupids, GroupNames, Remark, CreateBy, CreateByid, CreateDate,
        Lister, Listerid, ModifyDate, Tenantid, TenantName, Revision)
        values (#{userid}, #{username}, #{realname}, #{nickname}, #{userpassword}, #{mobile}, #{email}, #{sex},
        #{langcode}, #{avatar}, #{usertype}, #{goodsid}, #{isadmin}, #{deptid}, #{deptcode}, #{deptname},
        #{isdeptadmin}, #{deptrownum}, #{rownum}, #{userstatus}, #{usercode}, #{groupids}, #{groupnames},
        #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
        #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_ScmUser
        <set>
            <if test="username != null">
                UserName =#{username},
            </if>
            <if test="realname != null">
                RealName =#{realname},
            </if>
            <if test="nickname != null">
                NickName =#{nickname},
            </if>
            <if test="userpassword != null">
                UserPassword =#{userpassword},
            </if>
            <if test="mobile != null">
                Mobile =#{mobile},
            </if>
            <if test="email != null">
                Email =#{email},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="langcode != null">
                LangCode =#{langcode},
            </if>
            <if test="avatar != null">
                Avatar =#{avatar},
            </if>
            <if test="usertype != null">
                UserType =#{usertype},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="isadmin != null">
                IsAdmin =#{isadmin},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="deptcode != null">
                DeptCode =#{deptcode},
            </if>
            <if test="deptname != null">
                DeptName =#{deptname},
            </if>
            <if test="isdeptadmin != null">
                IsDeptAdmin =#{isdeptadmin},
            </if>
            <if test="deptrownum != null">
                DeptRowNum =#{deptrownum},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="userstatus != null">
                UserStatus =#{userstatus},
            </if>
            <if test="usercode != null">
                UserCode =#{usercode},
            </if>
            <if test="groupids != null">
                Groupids =#{groupids},
            </if>
            <if test="groupnames != null">
                GroupNames =#{groupnames},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where Userid = #{userid}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_ScmUser where Userid = #{key}
    </delete>

    <select id="getEntityByUserName" resultType="inks.service.sa.pms.domain.pojo.SaScmuserPojo">
        select *
        from Sa_ScmUser
        where Sa_ScmUser.Mobile = #{username} or Sa_ScmUser.Email = #{username}
    </select>

    <select id="getPageListByCustomer" resultType="inks.service.sa.pms.domain.pojo.SaScmuserPojo">
        select
        Sa_ScmUser.Userid,
        Sa_ScmUser.UserName,
        Sa_ScmUser.RealName,
        Sa_ScmUser.NickName,
        Sa_ScmUser.UserPassword,
        Sa_ScmUser.Mobile,
        Sa_ScmUser.Email,
        Sa_ScmUser.Sex,
        Sa_ScmUser.LangCode,
        Sa_ScmUser.Avatar,
        Sa_ScmUser.Remark,
        Sa_ScmUser.CreateBy,
        Sa_ScmUser.CreateByid,
        Sa_ScmUser.CreateDate,
        Sa_ScmUser.Lister,
        Sa_ScmUser.Listerid,
        Sa_ScmUser.ModifyDate,
        Sa_ScmUser.Revision
        from Sa_ScmUser RIGHT JOIN Sa_CustScmUser ON Sa_ScmUser.Userid = Sa_CustScmUser.Userid
        where Sa_CustScmUser.Groupids=#{groupid}
    </select>

    <select id="getEntityByOpenid" resultType="inks.service.sa.pms.domain.pojo.SaScmuserPojo">
        select
        Sa_ScmUser.*
        from Sa_ScmUser
        Left Join Sa_ScmJustAuth on Sa_ScmJustAuth.Userid = Sa_ScmUser.Userid
        where 1=1
        and Sa_ScmJustAuth.AuthUuid = #{openid}
    </select>

    <select id="getEntityByUNameAndPass" resultType="inks.service.sa.pms.domain.pojo.SaScmuserPojo">
        SELECT *
        FROM Sa_ScmUser
        WHERE username = #{username}
        and UserPassword = #{password} LIMIT 1
    </select>

    <select id="getListByOpenid" resultType="inks.service.sa.pms.domain.pojo.SaScmuserPojo">
        select Sa_ScmUser.*
        from Sa_ScmUser
        Left Join Sa_ScmJustAuth on Sa_ScmJustAuth.Userid = Sa_ScmUser.Userid
        where Sa_ScmJustAuth.AuthUuid = #{openid}
    </select>
</mapper>

