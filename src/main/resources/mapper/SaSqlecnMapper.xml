<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaSqlecnMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaSqlecnPojo">
        <include refid="selectSaSqlecnVo"/>
        where Sa_SqlEcn.id = #{key} 
    </select>
    <sql id="selectSaSqlecnVo">
        select Sa_SqlEcn.id,
               Sa_SqlEcn.EcnType,
               Sa_SqlEcn.EcnReason,
               Sa_SqlEcn.EcnDesc,
               Sa_SqlEcn.SqlText,
               Sa_SqlEcn.Code,
               Sa_SqlEcn.Timestamp,
               Sa_SqlEcn.RollbackText,
               Sa_SqlEcn.Projectid,
               Sa_SqlEcn.ImpactScope,
               Sa_SqlEcn.Risk,
               Sa_SqlEcn.OaFlowMark,
               Sa_SqlEcn.Assessor,
               Sa_SqlEcn.Assessorid,
               Sa_SqlEcn.AssessDate,
               Sa_SqlEcn.RowNum,
               Sa_SqlEcn.Remark,
               Sa_SqlEcn.CreateBy,
               Sa_SqlEcn.CreateByid,
               Sa_SqlEcn.CreateDate,
               Sa_SqlEcn.Lister,
               Sa_SqlEcn.Listerid,
               Sa_SqlEcn.ModifyDate,
               Sa_SqlEcn.Custom1,
               Sa_SqlEcn.Custom2,
               Sa_SqlEcn.Custom3,
               Sa_SqlEcn.Custom4,
               Sa_SqlEcn.Custom5,
               Sa_SqlEcn.Tenantid,
               Sa_SqlEcn.TenantName,
               Sa_SqlEcn.Revision,
                Sa_Project.ProjName as projectname
        from Sa_SqlEcn
        left join Sa_Project on Sa_SqlEcn.Projectid = Sa_Project.id
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaSqlecnPojo">
        <include refid="selectSaSqlecnVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_SqlEcn.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
         <if test="SearchPojo.ecntype != null">
             AND LOWER(Sa_SqlEcn.EcnType) LIKE CONCAT('%', LOWER(#{SearchPojo.ecntype}), '%')
         </if>
         <if test="SearchPojo.ecnreason != null">
             AND LOWER(Sa_SqlEcn.EcnReason) LIKE CONCAT('%', LOWER(#{SearchPojo.ecnreason}), '%')
         </if>
         <if test="SearchPojo.ecndesc != null">
             AND LOWER(Sa_SqlEcn.EcnDesc) LIKE CONCAT('%', LOWER(#{SearchPojo.ecndesc}), '%')
         </if>
         <if test="SearchPojo.sqltext != null">
             AND LOWER(Sa_SqlEcn.SqlText) LIKE CONCAT('%', LOWER(#{SearchPojo.sqltext}), '%')
         </if>
         <if test="SearchPojo.code != null">
             AND LOWER(Sa_SqlEcn.Code) LIKE CONCAT('%', LOWER(#{SearchPojo.code}), '%')
         </if>
<if test="SearchPojo.rollbacktext != null ">
   and Sa_SqlEcn.RollbackText like concat('%', #{SearchPojo.rollbacktext}, '%')
</if>
<if test="SearchPojo.projectid != null ">
   and Sa_SqlEcn.Projectid like concat('%', #{SearchPojo.projectid}, '%')
</if>
<if test="SearchPojo.impactscope != null ">
   and Sa_SqlEcn.ImpactScope like concat('%', #{SearchPojo.impactscope}, '%')
</if>
<if test="SearchPojo.risk != null ">
   and Sa_SqlEcn.Risk like concat('%', #{SearchPojo.risk}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_SqlEcn.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_SqlEcn.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_SqlEcn.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_SqlEcn.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_SqlEcn.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_SqlEcn.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_SqlEcn.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_SqlEcn.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_SqlEcn.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_SqlEcn.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_SqlEcn.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_SqlEcn.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_SqlEcn.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
      <if test="SearchPojo.ecntype != null">
          OR LOWER(Sa_SqlEcn.EcnType) LIKE CONCAT('%', LOWER(#{SearchPojo.ecntype}), '%')
      </if>
      <if test="SearchPojo.ecnreason != null">
          OR LOWER(Sa_SqlEcn.EcnReason) LIKE CONCAT('%', LOWER(#{SearchPojo.ecnreason}), '%')
      </if>
      <if test="SearchPojo.ecndesc != null">
          OR LOWER(Sa_SqlEcn.EcnDesc) LIKE CONCAT('%', LOWER(#{SearchPojo.ecndesc}), '%')
      </if>
      <if test="SearchPojo.sqltext != null">
          OR LOWER(Sa_SqlEcn.SqlText) LIKE CONCAT('%', LOWER(#{SearchPojo.sqltext}), '%')
      </if>
      <if test="SearchPojo.code != null">
          OR LOWER(Sa_SqlEcn.Code) LIKE CONCAT('%', LOWER(#{SearchPojo.code}), '%')
      </if>

<if test="SearchPojo.rollbacktext != null ">
   or Sa_SqlEcn.RollbackText like concat('%', #{SearchPojo.rollbacktext}, '%')
</if>
<if test="SearchPojo.projectid != null ">
   or Sa_SqlEcn.Projectid like concat('%', #{SearchPojo.projectid}, '%')
</if>
<if test="SearchPojo.impactscope != null ">
   or Sa_SqlEcn.ImpactScope like concat('%', #{SearchPojo.impactscope}, '%')
</if>
<if test="SearchPojo.risk != null ">
   or Sa_SqlEcn.Risk like concat('%', #{SearchPojo.risk}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_SqlEcn.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_SqlEcn.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_SqlEcn.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_SqlEcn.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_SqlEcn.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_SqlEcn.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_SqlEcn.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_SqlEcn.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_SqlEcn.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_SqlEcn.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_SqlEcn.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_SqlEcn.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_SqlEcn.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_SqlEcn(id, EcnType, EcnReason, EcnDesc, SqlText, Code, Timestamp, RollbackText, Projectid, ImpactScope, Risk, OaFlowMark, Assessor, Assessorid, AssessDate, RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Tenantid, TenantName, Revision)
        values (#{id}, #{ecntype}, #{ecnreason}, #{ecndesc}, #{sqltext}, #{code}, #{timestamp}, #{rollbacktext}, #{projectid}, #{impactscope}, #{risk}, #{oaflowmark}, #{assessor}, #{assessorid}, #{assessdate}, #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_SqlEcn
        <set>
            <if test="ecntype != null ">
                EcnType =#{ecntype},
            </if>
            <if test="ecnreason != null ">
                EcnReason =#{ecnreason},
            </if>
            <if test="ecndesc != null ">
                EcnDesc =#{ecndesc},
            </if>
            <if test="sqltext != null ">
                SqlText =#{sqltext},
            </if>
            <if test="code != null ">
                Code =#{code},
            </if>
            <if test="timestamp != null">
                Timestamp =#{timestamp},
            </if>
            <if test="rollbacktext != null ">
                RollbackText =#{rollbacktext},
            </if>
            <if test="projectid != null ">
                Projectid =#{projectid},
            </if>
            <if test="impactscope != null ">
                ImpactScope =#{impactscope},
            </if>
            <if test="risk != null ">
                Risk =#{risk},
            </if>
            <if test="oaflowmark != null">
                OaFlowMark =#{oaflowmark},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_SqlEcn where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_SqlEcn SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>

    <update id="updateOaflowmark">
        update Sa_SqlEcn SET
            OaFlowMark = #{oaflowmark},
            Revision=Revision+1
        where id = #{id}
    </update>

    <select id="pullList" resultType="inks.service.sa.pms.domain.pojo.SaSqlecnPojo">
        <include refid="selectSaSqlecnVo"/>
        where Sa_SqlEcn.Assessorid != ''
        and Sa_SqlEcn.Code in
        <foreach collection="codeList" item="code" open="(" separator="," close=")">
            #{code}
        </foreach>
        and Sa_SqlEcn.Timestamp > #{timestamp}
        and Sa_SqlEcn.Timestamp != 0
        order by Sa_SqlEcn.Timestamp
        limit #{size}
    </select>

</mapper>

