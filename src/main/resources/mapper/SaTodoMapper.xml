<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaTodoMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaTodoPojo">
        <include refid="selectSaTodoVo"/>
        where Sa_Todo.id = #{key}
    </select>
    <sql id="selectSaTodoVo">
         select
id, TdGroupid, Parentid, RefNo, BillDate, BillType, BillTitle, TdContent, FinishMark, Level, Goodsid, RowNum, PlanDate, Source, ProductName, GroupName, Remark, Closed, PublicMark, ImportantMark, UrgentMark, TargetJson, StateCode, StateDate, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, ModuleCode, CiteUid, Citeid, Accepterid, Accepter, StartPlan, PlanHours, FinishHours, EndPlan, StartActual, StartRemark, EndActual, EndRemark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Deptid, TenantName, Revision        from Sa_Todo
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaTodoPojo">
        <include refid="selectSaTodoVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Todo.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.tdgroupid != null">
            and Sa_Todo.TdGroupid like concat('%', #{SearchPojo.tdgroupid}, '%')
        </if>
        <if test="SearchPojo.parentid != null">
            and Sa_Todo.Parentid like concat('%', #{SearchPojo.parentid}, '%')
        </if>
        <if test="SearchPojo.refno != null">
            and Sa_Todo.RefNo like concat('%', #{SearchPojo.refno}, '%')
        </if>
        <if test="SearchPojo.billtype != null">
            and Sa_Todo.BillType like concat('%', #{SearchPojo.billtype}, '%')
        </if>
        <if test="SearchPojo.billtitle != null">
            and Sa_Todo.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
        </if>
        <if test="SearchPojo.tdcontent != null">
            and Sa_Todo.TdContent like concat('%', #{SearchPojo.tdcontent}, '%')
        </if>
        <if test="SearchPojo.goodsid != null">
            and Sa_Todo.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
        </if>
        <if test="SearchPojo.source != null">
            and Sa_Todo.Source like concat('%', #{SearchPojo.source}, '%')
        </if>
        <if test="SearchPojo.productname != null">
            and Sa_Todo.ProductName like concat('%', #{SearchPojo.productname}, '%')
        </if>
        <if test="SearchPojo.groupname != null">
            and Sa_Todo.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Todo.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.targetjson != null">
            and Sa_Todo.TargetJson like concat('%', #{SearchPojo.targetjson}, '%')
        </if>
        <if test="SearchPojo.statecode != null">
            and Sa_Todo.StateCode like concat('%', #{SearchPojo.statecode}, '%')
        </if>
        <if test="SearchPojo.photourl1 != null">
            and Sa_Todo.PhotoUrl1 like concat('%', #{SearchPojo.photourl1}, '%')
        </if>
        <if test="SearchPojo.photourl2 != null">
            and Sa_Todo.PhotoUrl2 like concat('%', #{SearchPojo.photourl2}, '%')
        </if>
        <if test="SearchPojo.photourl3 != null">
            and Sa_Todo.PhotoUrl3 like concat('%', #{SearchPojo.photourl3}, '%')
        </if>
        <if test="SearchPojo.photoname1 != null">
            and Sa_Todo.PhotoName1 like concat('%', #{SearchPojo.photoname1}, '%')
        </if>
        <if test="SearchPojo.photoname2 != null">
            and Sa_Todo.PhotoName2 like concat('%', #{SearchPojo.photoname2}, '%')
        </if>
        <if test="SearchPojo.photoname3 != null">
            and Sa_Todo.PhotoName3 like concat('%', #{SearchPojo.photoname3}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Todo.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Todo.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Todo.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Todo.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Sa_Todo.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.citeuid != null">
            and Sa_Todo.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
        </if>
        <if test="SearchPojo.accepterid != null">
            and Sa_Todo.Accepterid like concat('%', #{SearchPojo.accepterid}, '%')
        </if>
        <if test="SearchPojo.accepter != null">
            and Sa_Todo.Accepter like concat('%', #{SearchPojo.accepter}, '%')
        </if>
        <if test="SearchPojo.startremark != null">
            and Sa_Todo.StartRemark like concat('%', #{SearchPojo.startremark}, '%')
        </if>
        <if test="SearchPojo.endremark != null">
            and Sa_Todo.EndRemark like concat('%', #{SearchPojo.endremark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Todo.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Todo.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Todo.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Todo.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Todo.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Todo.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Todo.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Todo.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Todo.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Todo.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_Todo.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_Todo.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.tdgroupid != null">
                or Sa_Todo.TdGroupid like concat('%', #{SearchPojo.tdgroupid}, '%')
            </if>
            <if test="SearchPojo.parentid != null">
                or Sa_Todo.Parentid like concat('%', #{SearchPojo.parentid}, '%')
            </if>
            <if test="SearchPojo.refno != null">
                or Sa_Todo.RefNo like concat('%', #{SearchPojo.refno}, '%')
            </if>
            <if test="SearchPojo.billtype != null">
                or Sa_Todo.BillType like concat('%', #{SearchPojo.billtype}, '%')
            </if>
            <if test="SearchPojo.billtitle != null">
                or Sa_Todo.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
            </if>
            <if test="SearchPojo.tdcontent != null">
                or Sa_Todo.TdContent like concat('%', #{SearchPojo.tdcontent}, '%')
            </if>
            <if test="SearchPojo.goodsid != null">
                or Sa_Todo.Goodsid like concat('%', #{SearchPojo.goodsid}, '%')
            </if>
            <if test="SearchPojo.source != null">
                or Sa_Todo.Source like concat('%', #{SearchPojo.source}, '%')
            </if>
            <if test="SearchPojo.productname != null">
                or Sa_Todo.ProductName like concat('%', #{SearchPojo.productname}, '%')
            </if>
            <if test="SearchPojo.groupname != null">
                or Sa_Todo.GroupName like concat('%', #{SearchPojo.groupname}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Todo.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.targetjson != null">
                or Sa_Todo.TargetJson like concat('%', #{SearchPojo.targetjson}, '%')
            </if>
            <if test="SearchPojo.statecode != null">
                or Sa_Todo.StateCode like concat('%', #{SearchPojo.statecode}, '%')
            </if>
            <if test="SearchPojo.photourl1 != null">
                or Sa_Todo.PhotoUrl1 like concat('%', #{SearchPojo.photourl1}, '%')
            </if>
            <if test="SearchPojo.photourl2 != null">
                or Sa_Todo.PhotoUrl2 like concat('%', #{SearchPojo.photourl2}, '%')
            </if>
            <if test="SearchPojo.photourl3 != null">
                or Sa_Todo.PhotoUrl3 like concat('%', #{SearchPojo.photourl3}, '%')
            </if>
            <if test="SearchPojo.photoname1 != null">
                or Sa_Todo.PhotoName1 like concat('%', #{SearchPojo.photoname1}, '%')
            </if>
            <if test="SearchPojo.photoname2 != null">
                or Sa_Todo.PhotoName2 like concat('%', #{SearchPojo.photoname2}, '%')
            </if>
            <if test="SearchPojo.photoname3 != null">
                or Sa_Todo.PhotoName3 like concat('%', #{SearchPojo.photoname3}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Todo.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Todo.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Todo.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Todo.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Sa_Todo.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.citeuid != null">
                or Sa_Todo.CiteUid like concat('%', #{SearchPojo.citeuid}, '%')
            </if>
            <if test="SearchPojo.accepterid != null">
                or Sa_Todo.Accepterid like concat('%', #{SearchPojo.accepterid}, '%')
            </if>
            <if test="SearchPojo.accepter != null">
                or Sa_Todo.Accepter like concat('%', #{SearchPojo.accepter}, '%')
            </if>
            <if test="SearchPojo.startremark != null">
                or Sa_Todo.StartRemark like concat('%', #{SearchPojo.startremark}, '%')
            </if>
            <if test="SearchPojo.endremark != null">
                or Sa_Todo.EndRemark like concat('%', #{SearchPojo.endremark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Todo.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Todo.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Todo.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Todo.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Todo.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Todo.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Todo.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Todo.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Todo.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Todo.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_Todo.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_Todo.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Todo(id, TdGroupid, Parentid, RefNo, BillDate, BillType, BillTitle, TdContent, FinishMark, Level, Goodsid, RowNum, PlanDate, Source, ProductName, GroupName, Remark, Closed, PublicMark, ImportantMark, UrgentMark, TargetJson, StateCode, StateDate, PhotoUrl1, PhotoUrl2, PhotoUrl3, PhotoName1, PhotoName2, PhotoName3, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, ModuleCode, CiteUid, Citeid, Accepterid, Accepter, StartPlan, PlanHours, FinishHours, EndPlan, StartActual, StartRemark, EndActual, EndRemark, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Deptid, TenantName, Revision)
        values (#{id}, #{tdgroupid}, #{parentid}, #{refno}, #{billdate}, #{billtype}, #{billtitle}, #{tdcontent}, #{finishmark}, #{level}, #{goodsid}, #{rownum}, #{plandate}, #{source}, #{productname}, #{groupname}, #{remark}, #{closed}, #{publicmark}, #{importantmark}, #{urgentmark}, #{targetjson}, #{statecode}, #{statedate}, #{photourl1}, #{photourl2}, #{photourl3}, #{photoname1}, #{photoname2}, #{photoname3}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{modulecode}, #{citeuid}, #{citeid}, #{accepterid}, #{accepter}, #{startplan}, #{planhours}, #{finishhours}, #{endplan}, #{startactual}, #{startremark}, #{endactual}, #{endremark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{deptid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Todo
        <set>
            <if test="tdgroupid != null">
                TdGroupid =#{tdgroupid},
            </if>
            <if test="parentid != null">
                Parentid =#{parentid},
            </if>
            <if test="refno != null">
                RefNo =#{refno},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billtype != null">
                BillType =#{billtype},
            </if>
            <if test="billtitle != null">
                BillTitle =#{billtitle},
            </if>
            <if test="tdcontent != null">
                TdContent =#{tdcontent},
            </if>
            <if test="finishmark != null">
                FinishMark =#{finishmark},
            </if>
            <if test="level != null">
                Level =#{level},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="plandate != null">
                PlanDate =#{plandate},
            </if>
            <if test="source != null">
                Source =#{source},
            </if>
            <if test="productname != null">
                ProductName =#{productname},
            </if>
            <if test="groupname != null">
                GroupName =#{groupname},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="importantmark != null">
                ImportantMark =#{importantmark},
            </if>
            <if test="urgentmark != null">
                UrgentMark =#{urgentmark},
            </if>
            <if test="targetjson != null">
                TargetJson =#{targetjson},
            </if>
            <if test="statecode != null">
                StateCode =#{statecode},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="photourl1 != null">
                PhotoUrl1 =#{photourl1},
            </if>
            <if test="photourl2 != null">
                PhotoUrl2 =#{photourl2},
            </if>
            <if test="photourl3 != null">
                PhotoUrl3 =#{photourl3},
            </if>
            <if test="photoname1 != null">
                PhotoName1 =#{photoname1},
            </if>
            <if test="photoname2 != null">
                PhotoName2 =#{photoname2},
            </if>
            <if test="photoname3 != null">
                PhotoName3 =#{photoname3},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="citeuid != null">
                CiteUid =#{citeuid},
            </if>
            <if test="citeid != null">
                Citeid =#{citeid},
            </if>
            <if test="accepterid != null">
                Accepterid =#{accepterid},
            </if>
            <if test="accepter != null">
                Accepter =#{accepter},
            </if>
            <if test="startplan != null">
                StartPlan =#{startplan},
            </if>
            <if test="planhours != null">
                PlanHours =#{planhours},
            </if>
            <if test="finishhours != null">
                FinishHours =#{finishhours},
            </if>
            <if test="endplan != null">
                EndPlan =#{endplan},
            </if>
            <if test="startactual != null">
                StartActual =#{startactual},
            </if>
            <if test="startremark != null">
                StartRemark =#{startremark},
            </if>
            <if test="endactual != null">
                EndActual =#{endactual},
            </if>
            <if test="endremark != null">
                EndRemark =#{endremark},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_Todo
        where id = #{key}
    </delete>

    <update id="setNullEndActual">
        update Sa_Todo
        set EndActual = null
        where id = #{key}
    </update>
</mapper>

