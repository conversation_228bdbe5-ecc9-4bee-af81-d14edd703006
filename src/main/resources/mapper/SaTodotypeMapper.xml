<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaTodotypeMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaTodotypePojo">
        select
        id, TypeName, TypeCode, TypeColor, TypeHeight, TypeIcon, RowNum, CreateByid, CreateBy, CreateDate, Listerid,
        Lister, ModifyDate, ModuleCode, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9,
        Custom10, Tenantid, Deptid, TenantName, Revision from Sa_TodoType
        where Sa_TodoType.id = #{key}
    </select>
    <sql id="selectSaTodotypeVo">
        select id,
               TypeName,
               TypeCode,
               TypeColor,
               TypeHeight,
               TypeIcon,
               <PERSON>N<PERSON>,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               ModuleCode,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Deptid,
               TenantName,
               Revision
        from Sa_TodoType
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaTodotypePojo">
        <include refid="selectSaTodotypeVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_TodoType.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.typename != null">
            and Sa_TodoType.TypeName like concat('%', #{SearchPojo.typename}, '%')
        </if>
        <if test="SearchPojo.typecode != null">
            and Sa_TodoType.TypeCode like concat('%', #{SearchPojo.typecode}, '%')
        </if>
        <if test="SearchPojo.typecolor != null">
            and Sa_TodoType.TypeColor like concat('%', #{SearchPojo.typecolor}, '%')
        </if>
        <if test="SearchPojo.typeicon != null">
            and Sa_TodoType.TypeIcon like concat('%', #{SearchPojo.typeicon}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_TodoType.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_TodoType.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_TodoType.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_TodoType.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Sa_TodoType.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_TodoType.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_TodoType.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_TodoType.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_TodoType.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_TodoType.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_TodoType.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_TodoType.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_TodoType.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_TodoType.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_TodoType.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_TodoType.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_TodoType.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.typename != null">
                or Sa_TodoType.TypeName like concat('%', #{SearchPojo.typename}, '%')
            </if>
            <if test="SearchPojo.typecode != null">
                or Sa_TodoType.TypeCode like concat('%', #{SearchPojo.typecode}, '%')
            </if>
            <if test="SearchPojo.typecolor != null">
                or Sa_TodoType.TypeColor like concat('%', #{SearchPojo.typecolor}, '%')
            </if>
            <if test="SearchPojo.typeicon != null">
                or Sa_TodoType.TypeIcon like concat('%', #{SearchPojo.typeicon}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_TodoType.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_TodoType.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_TodoType.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_TodoType.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Sa_TodoType.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_TodoType.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_TodoType.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_TodoType.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_TodoType.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_TodoType.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_TodoType.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_TodoType.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_TodoType.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_TodoType.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_TodoType.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_TodoType.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_TodoType.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_TodoType(id, TypeName, TypeCode, TypeColor, TypeHeight, TypeIcon, RowNum, CreateByid, CreateBy,
        CreateDate, Listerid, Lister, ModifyDate, ModuleCode, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6,
        Custom7, Custom8, Custom9, Custom10, Tenantid, Deptid, TenantName, Revision)
        values (#{id}, #{typename}, #{typecode}, #{typecolor}, #{typeheight}, #{typeicon}, #{rownum}, #{createbyid},
        #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{modulecode}, #{custom1}, #{custom2},
        #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid},
        #{deptid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_TodoType
        <set>
            <if test="typename != null">
                TypeName =#{typename},
            </if>
            <if test="typecode != null">
                TypeCode =#{typecode},
            </if>
            <if test="typecolor != null">
                TypeColor =#{typecolor},
            </if>
            <if test="typeheight != null">
                TypeHeight =#{typeheight},
            </if>
            <if test="typeicon != null">
                TypeIcon =#{typeicon},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_TodoType where id = #{key}
    </delete>
</mapper>

