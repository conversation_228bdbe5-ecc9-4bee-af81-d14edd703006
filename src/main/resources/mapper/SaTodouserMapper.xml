<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaTodouserMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaTodouserPojo">
        select
        id, Name, Userid, BackColorArgb, Avatar, FileName, DirName, Phone, Email, Sex, UserState, Remark, RowNum,
        CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, ModuleCode, Custom1, Custom2, Custom3, Custom4,
        Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Deptid, TenantName, Revision from Sa_TodoUser
        where Sa_TodoUser.id = #{key}
    </select>
    <sql id="selectSaTodouserVo">
        select id,
               Name,
               Userid,
               BackColorArgb,
               Avatar,
               FileName,
               DirName,
               Phone,
               Email,
               Sex,
               UserState,
               Remark,
               RowNum,
               CreateByid,
               CreateBy,
               CreateDate,
               Listerid,
               Lister,
               ModifyDate,
               ModuleCode,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               Deptid,
               TenantName,
               Revision
        from Sa_TodoUser
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaTodouserPojo">
        <include refid="selectSaTodouserVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_TodoUser.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.name != null">
            and Sa_TodoUser.Name like concat('%', #{SearchPojo.name}, '%')
        </if>
        <if test="SearchPojo.userid != null">
            and Sa_TodoUser.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.backcolorargb != null">
            and Sa_TodoUser.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
        </if>
        <if test="SearchPojo.avatar != null">
            and Sa_TodoUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.filename != null">
            and Sa_TodoUser.FileName like concat('%', #{SearchPojo.filename}, '%')
        </if>
        <if test="SearchPojo.dirname != null">
            and Sa_TodoUser.DirName like concat('%', #{SearchPojo.dirname}, '%')
        </if>
        <if test="SearchPojo.phone != null">
            and Sa_TodoUser.Phone like concat('%', #{SearchPojo.phone}, '%')
        </if>
        <if test="SearchPojo.email != null">
            and Sa_TodoUser.Email like concat('%', #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_TodoUser.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_TodoUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_TodoUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_TodoUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_TodoUser.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.modulecode != null">
            and Sa_TodoUser.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_TodoUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_TodoUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_TodoUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_TodoUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_TodoUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_TodoUser.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_TodoUser.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_TodoUser.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_TodoUser.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_TodoUser.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.deptid != null">
            and Sa_TodoUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_TodoUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.name != null">
                or Sa_TodoUser.Name like concat('%', #{SearchPojo.name}, '%')
            </if>
            <if test="SearchPojo.userid != null">
                or Sa_TodoUser.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.backcolorargb != null">
                or Sa_TodoUser.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
            </if>
            <if test="SearchPojo.avatar != null">
                or Sa_TodoUser.Avatar like concat('%', #{SearchPojo.avatar}, '%')
            </if>
            <if test="SearchPojo.filename != null">
                or Sa_TodoUser.FileName like concat('%', #{SearchPojo.filename}, '%')
            </if>
            <if test="SearchPojo.dirname != null">
                or Sa_TodoUser.DirName like concat('%', #{SearchPojo.dirname}, '%')
            </if>
            <if test="SearchPojo.phone != null">
                or Sa_TodoUser.Phone like concat('%', #{SearchPojo.phone}, '%')
            </if>
            <if test="SearchPojo.email != null">
                or Sa_TodoUser.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_TodoUser.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_TodoUser.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_TodoUser.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_TodoUser.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_TodoUser.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.modulecode != null">
                or Sa_TodoUser.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_TodoUser.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_TodoUser.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_TodoUser.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_TodoUser.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_TodoUser.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_TodoUser.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_TodoUser.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_TodoUser.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_TodoUser.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_TodoUser.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.deptid != null">
                or Sa_TodoUser.Deptid like concat('%', #{SearchPojo.deptid}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_TodoUser.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_TodoUser(id, Name, Userid, BackColorArgb, Avatar, FileName, DirName, Phone, Email, Sex,
        UserState, Remark, RowNum, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, ModuleCode, Custom1,
        Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Deptid, TenantName,
        Revision)
        values (#{id}, #{name}, #{userid}, #{backcolorargb}, #{avatar}, #{filename}, #{dirname}, #{phone}, #{email},
        #{sex}, #{userstate}, #{remark}, #{rownum}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister},
        #{modifydate}, #{modulecode}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6},
        #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{deptid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_TodoUser
        <set>
            <if test="name != null">
                Name =#{name},
            </if>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="backcolorargb != null">
                BackColorArgb =#{backcolorargb},
            </if>
            <if test="avatar != null">
                Avatar =#{avatar},
            </if>
            <if test="filename != null">
                FileName =#{filename},
            </if>
            <if test="dirname != null">
                DirName =#{dirname},
            </if>
            <if test="phone != null">
                Phone =#{phone},
            </if>
            <if test="email != null">
                Email =#{email},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="userstate != null">
                UserState =#{userstate},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="modulecode != null">
                ModuleCode =#{modulecode},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="deptid != null">
                Deptid =#{deptid},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_TodoUser where id = #{key}
    </delete>
</mapper>

