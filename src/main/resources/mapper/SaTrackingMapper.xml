<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaTrackingMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaTrackingPojo">
        <include refid="selectbillVo"/>
        where Sa_Tracking.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, Type, Customer, Project, ItemCount, FinishCount, Subject, BillPlanDate, BillFinishDate, PlanFinishCount, BillFinishCount, Closed, PublicMark, Operator, Operatorid, Assistantids, AssistantNames, CreateByid, CreateBy, CreateDate, <PERSON>erid, Lister, ModifyDate, DisannulCount, Custom1, Custom2, Custom3, Custom4, Custom5, Revision        from Sa_Tracking
    </sql>
    <sql id="selectdetailVo">
        select Sa_TrackingItem.id,
               Sa_TrackingItem.Pid,
               Sa_TrackingItem.PhaseName,
               Sa_TrackingItem.TaskName,
               Sa_TrackingItem.Description,
               Sa_TrackingItem.PlanStart,
               Sa_TrackingItem.PlanEnd,
               Sa_TrackingItem.Milestone,
               Sa_TrackingItem.ActualStart,
               Sa_TrackingItem.CompletionDate,
               Sa_TrackingItem.OutputResult,
               Sa_TrackingItem.MainResponsiblePerson,
               Sa_TrackingItem.SubResponsiblePerson,
               Sa_TrackingItem.Closed,
               Sa_TrackingItem.DisannulMark,
               Sa_TrackingItem.RowNum,
               Sa_TrackingItem.Remark,
               Sa_TrackingItem.Custom1,
               Sa_TrackingItem.Custom2,
               Sa_TrackingItem.Custom3,
               Sa_TrackingItem.Custom4,
               Sa_TrackingItem.Custom5,
               Sa_TrackingItem.Revision,
               Sa_Tracking.Type,
               Sa_Tracking.Customer,
               Sa_Tracking.Project,
               Sa_Tracking.ItemCount,
               Sa_Tracking.FinishCount,
               Sa_Tracking.Subject,
               Sa_Tracking.BillPlanDate,
               Sa_Tracking.BillFinishDate,
               Sa_Tracking.PlanFinishCount,
               Sa_Tracking.BillFinishCount,
               Sa_Tracking.Closed,
               Sa_Tracking.PublicMark,
               Sa_Tracking.Operator,
               Sa_Tracking.CreateByid,
               Sa_Tracking.CreateBy,
               Sa_Tracking.CreateDate,
               Sa_Tracking.Listerid,
               Sa_Tracking.Lister,
               Sa_Tracking.ModifyDate
        from Sa_Tracking
                 Right Join Sa_TrackingItem on Sa_Tracking.id = Sa_TrackingItem.Pid
    </sql>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaTrackingitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Tracking.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.type != null">
            and Sa_Tracking.type like concat('%', #{SearchPojo.type}, '%')
        </if>
        <if test="SearchPojo.customer != null">
            and Sa_Tracking.customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.project != null">
            and Sa_Tracking.project like concat('%', #{SearchPojo.project}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_Tracking.operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Tracking.createbyid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Tracking.createby like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Tracking.listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Tracking.lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Tracking.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Tracking.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Tracking.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Tracking.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Tracking.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.type != null">
                or Sa_Tracking.Type like concat('%', #{SearchPojo.type}, '%')
            </if>
            <if test="SearchPojo.customer != null">
                or Sa_Tracking.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.project != null">
                or Sa_Tracking.Project like concat('%', #{SearchPojo.project}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_Tracking.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Tracking.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Tracking.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Tracking.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Tracking.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Tracking.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Tracking.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Tracking.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Tracking.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Tracking.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaTrackingPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Tracking.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.type != null">
            and Sa_Tracking.Type like concat('%', #{SearchPojo.type}, '%')
        </if>
        <if test="SearchPojo.customer != null">
            and Sa_Tracking.Customer like concat('%', #{SearchPojo.customer}, '%')
        </if>
        <if test="SearchPojo.project != null">
            and Sa_Tracking.Project like concat('%', #{SearchPojo.project}, '%')
        </if>
        <if test="SearchPojo.operator != null">
            and Sa_Tracking.Operator like concat('%', #{SearchPojo.operator}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Tracking.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Tracking.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Tracking.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Tracking.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_Tracking.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Tracking.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Tracking.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Tracking.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Tracking.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.type != null">
                or Sa_Tracking.Type like concat('%', #{SearchPojo.type}, '%')
            </if>
            <if test="SearchPojo.customer != null">
                or Sa_Tracking.Customer like concat('%', #{SearchPojo.customer}, '%')
            </if>
            <if test="SearchPojo.project != null">
                or Sa_Tracking.Project like concat('%', #{SearchPojo.project}, '%')
            </if>
            <if test="SearchPojo.operator != null">
                or Sa_Tracking.Operator like concat('%', #{SearchPojo.operator}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Tracking.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Tracking.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Tracking.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Tracking.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Tracking.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Tracking.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Tracking.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Tracking.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Tracking.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Tracking(id, Type, Customer, Project, ItemCount, FinishCount, Subject, BillPlanDate, BillFinishDate, PlanFinishCount, BillFinishCount, Closed, PublicMark, Operator, Operatorid, Assistantids, AssistantNames, CreateByid, CreateBy, CreateDate, Listerid, Lister, ModifyDate, DisannulCount, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{type}, #{customer}, #{project}, #{itemcount}, #{finishcount}, #{subject}, #{billplandate}, #{billfinishdate}, #{planfinishcount}, #{billfinishcount}, #{closed}, #{publicmark}, #{operator}, #{operatorid}, #{assistantids}, #{assistantnames}, #{createbyid}, #{createby}, #{createdate}, #{listerid}, #{lister}, #{modifydate}, #{disannulcount}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Tracking
        <set>
            <if test="type != null">
                Type =#{type},
            </if>
            <if test="customer != null">
                Customer =#{customer},
            </if>
            <if test="project != null">
                Project =#{project},
            </if>
            <if test="itemcount != null">
                ItemCount =#{itemcount},
            </if>
            <if test="finishcount != null">
                FinishCount =#{finishcount},
            </if>
            <if test="subject != null ">
                Subject =#{subject},
            </if>
            <if test="billplandate != null">
                BillPlanDate =#{billplandate},
            </if>
            <if test="billfinishdate != null">
                BillFinishDate =#{billfinishdate},
            </if>
            <if test="planfinishcount != null">
                PlanFinishCount =#{planfinishcount},
            </if>
            <if test="billfinishcount != null">
                BillFinishCount =#{billfinishcount},
            </if>
            <if test="closed != null">
                Closed =#{closed},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="operator != null">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="assistantids != null ">
                Assistantids =#{assistantids},
            </if>
            <if test="assistantnames != null ">
                AssistantNames =#{assistantnames},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="disannulcount != null">
                DisannulCount =#{disannulcount},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Tracking where id = #{key}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.pms.domain.pojo.SaTrackingPojo">
        select
        id
        from Sa_TrackingItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <update id="updateDisannulCount">
        update Sa_Tracking
        SET DisannulCount =COALESCE((SELECT COUNT(0)
                                     FROM Sa_TrackingItem
                                     where Sa_TrackingItem.Pid = #{pid}
                                       and Sa_TrackingItem.DisannulMark = 1), 0)
        where id = #{pid}
    </update>

    <update id="updateFinishCount">
        update Sa_Tracking
        SET FinishCount =COALESCE((SELECT COUNT(0)
                                   FROM Sa_TrackingItem
                                   where Sa_TrackingItem.Pid = #{pid}
                                     and Sa_TrackingItem.Closed = 1), 0)
        where id = #{pid}
    </update>
</mapper>

