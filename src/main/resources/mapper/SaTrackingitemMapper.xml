<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaTrackingitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaTrackingitemPojo">
        <include refid="selectSaTrackingitemVo"/>
        where Sa_TrackingItem.id = #{key}
    </select>
    <sql id="selectSaTrackingitemVo">
         select
id, Pid, PhaseName, TaskName, Description, PlanStart, PlanEnd, Milestone, ActualStart, CompletionDate, OutputResult, MainResponsiblePerson, SubResponsiblePerson, Closed, DisannulMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Revision        from Sa_TrackingItem
    </sql>
         <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaTrackingitemPojo">
        <include refid="selectSaTrackingitemVo"/>
        where Sa_TrackingItem.Pid = #{Pid}
        order by RowNum
    </select>
    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaTrackingitemPojo">
        <include refid="selectSaTrackingitemVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_TrackingItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Sa_TrackingItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.phasename != null and SearchPojo.phasename != ''">
            and Sa_TrackingItem.phasename like concat('%', #{SearchPojo.phasename}, '%')
        </if>
        <if test="SearchPojo.taskname != null and SearchPojo.taskname != ''">
            and Sa_TrackingItem.taskname like concat('%', #{SearchPojo.taskname}, '%')
        </if>
        <if test="SearchPojo.description != null and SearchPojo.description != ''">
            and Sa_TrackingItem.description like concat('%', #{SearchPojo.description}, '%')
        </if>
        <if test="SearchPojo.milestone != null and SearchPojo.milestone != ''">
            and Sa_TrackingItem.milestone like concat('%', #{SearchPojo.milestone}, '%')
        </if>
        <if test="SearchPojo.outputresult != null and SearchPojo.outputresult != ''">
            and Sa_TrackingItem.outputresult like concat('%', #{SearchPojo.outputresult}, '%')
        </if>
        <if test="SearchPojo.mainresponsibleperson != null and SearchPojo.mainresponsibleperson != ''">
            and Sa_TrackingItem.mainresponsibleperson like concat('%', #{SearchPojo.mainresponsibleperson}, '%')
        </if>
        <if test="SearchPojo.subresponsibleperson != null and SearchPojo.subresponsibleperson != ''">
            and Sa_TrackingItem.subresponsibleperson like concat('%', #{SearchPojo.subresponsibleperson}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Sa_TrackingItem.remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Sa_TrackingItem.custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Sa_TrackingItem.custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Sa_TrackingItem.custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Sa_TrackingItem.custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Sa_TrackingItem.custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Sa_TrackingItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.phasename != null and SearchPojo.phasename != ''">
                or Sa_TrackingItem.PhaseName like concat('%', #{SearchPojo.phasename}, '%')
            </if>
            <if test="SearchPojo.taskname != null and SearchPojo.taskname != ''">
                or Sa_TrackingItem.TaskName like concat('%', #{SearchPojo.taskname}, '%')
            </if>
            <if test="SearchPojo.description != null and SearchPojo.description != ''">
                or Sa_TrackingItem.Description like concat('%', #{SearchPojo.description}, '%')
            </if>
            <if test="SearchPojo.milestone != null and SearchPojo.milestone != ''">
                or Sa_TrackingItem.Milestone like concat('%', #{SearchPojo.milestone}, '%')
            </if>
            <if test="SearchPojo.outputresult != null and SearchPojo.outputresult != ''">
                or Sa_TrackingItem.OutputResult like concat('%', #{SearchPojo.outputresult}, '%')
            </if>
            <if test="SearchPojo.mainresponsibleperson != null and SearchPojo.mainresponsibleperson != ''">
                or Sa_TrackingItem.MainResponsiblePerson like concat('%', #{SearchPojo.mainresponsibleperson}, '%')
            </if>
            <if test="SearchPojo.subresponsibleperson != null and SearchPojo.subresponsibleperson != ''">
                or Sa_TrackingItem.SubResponsiblePerson like concat('%', #{SearchPojo.subresponsibleperson}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Sa_TrackingItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Sa_TrackingItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Sa_TrackingItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Sa_TrackingItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Sa_TrackingItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Sa_TrackingItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>


    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_TrackingItem(id, Pid, PhaseName, TaskName, Description, PlanStart, PlanEnd, Milestone, ActualStart, CompletionDate, OutputResult, MainResponsiblePerson, SubResponsiblePerson, Closed, DisannulMark, RowNum, Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{pid}, #{phasename}, #{taskname}, #{description}, #{planstart}, #{planend}, #{milestone}, #{actualstart}, #{completiondate}, #{outputresult}, #{mainresponsibleperson}, #{subresponsibleperson}, #{closed}, #{disannulmark}, #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_TrackingItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="phasename != null">
                PhaseName = #{phasename},
            </if>
            <if test="taskname != null">
                TaskName = #{taskname},
            </if>
            <if test="description != null">
                Description = #{description},
            </if>

            PlanStart = #{planstart},


            PlanEnd = #{planend},

            <if test="milestone != null">
                Milestone = #{milestone},
            </if>

            ActualStart = #{actualstart},


            CompletionDate = #{completiondate},

            <if test="outputresult != null">
                OutputResult = #{outputresult},
            </if>
            <if test="mainresponsibleperson != null">
                MainResponsiblePerson = #{mainresponsibleperson},
            </if>
            <if test="subresponsibleperson != null">
                SubResponsiblePerson = #{subresponsibleperson},
            </if>
            <if test="closed != null">
                Closed = #{closed},
            </if>
            <if test="disannulmark != null">
                DisannulMark = #{disannulmark},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_TrackingItem
        where id = #{key}
    </delete>
</mapper>

