<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaUserloginMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaUserloginPojo">
        select id,
        Userid,
        UserPassword,
        Listerid,
        Lister,
        CreateDate,
        ModifyDate
        from Sa_UserLogin
        where Sa_UserLogin.id = #{key}
    </select>
    <sql id="selectSaUserloginVo">
        select id,
               Userid,
               UserPassword,
               Listerid,
               Lister,
               CreateDate,
               ModifyDate
        from Sa_UserLogin
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaUserloginPojo">
        <include refid="selectSaUserloginVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_UserLogin.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.userid != null">
            and Sa_UserLogin.Userid like concat('%', #{SearchPojo.userid}, '%')
        </if>
        <if test="SearchPojo.userpassword != null">
            and Sa_UserLogin.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_UserLogin.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_UserLogin.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.userid != null">
                or Sa_UserLogin.Userid like concat('%', #{SearchPojo.userid}, '%')
            </if>
            <if test="SearchPojo.userpassword != null">
                or Sa_UserLogin.UserPassword like concat('%', #{SearchPojo.userpassword}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_UserLogin.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_UserLogin.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_UserLogin(id, Userid, UserPassword, Listerid, Lister, CreateDate, ModifyDate)
        values (#{id}, #{userid}, #{userpassword}, #{listerid}, #{lister}, #{createdate}, #{modifydate})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_UserLogin
        <set>
            <if test="userid != null">
                Userid =#{userid},
            </if>
            <if test="userpassword != null">
                UserPassword =#{userpassword},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_UserLogin
        where id = #{key}
    </delete>
</mapper>

