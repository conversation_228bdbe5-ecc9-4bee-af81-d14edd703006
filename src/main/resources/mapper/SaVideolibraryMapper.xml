<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaVideolibraryMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaVideolibraryPojo">
        select id,
        VideoTitle,
        VideoPlayUrl,
        VideoCoverUrl,
        DirName,
        FileName,
        Goodsid,
        VideoDuration,
        VideoPlayTimes,
        VideoGoodNum,
        VideoNgNum,
        VideoLevel,
        VideoDesc,
        FileSize,
        FisrtLevelGroupid,
        SecLevelGroupid,
        SecretKey,
        UploadTime,
        TextTutorial,
        VideoTag,
        Description,
        BackColorArgb,
        ForeColorArgb,
        PublicMark,
        EnabledMark,
        RowNum,
        Remark,
        CreateBy,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON>er,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON>,
        <PERSON><PERSON><PERSON><PERSON>,
        <PERSON><PERSON>sDate,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Custom6,
        Custom7,
        Custom8,
        Custom9,
        Custom10,
        Tenantid,
        TenantName,
        Revision
        from Sa_VideoLibrary
        where Sa_VideoLibrary.id = #{key}
    </select>
    <sql id="selectSaVideolibraryVo">
        select id,
               VideoTitle,
               VideoPlayUrl,
               VideoCoverUrl,
               DirName,
               FileName,
               Goodsid,
               VideoDuration,
               VideoPlayTimes,
               VideoGoodNum,
               VideoNgNum,
               VideoLevel,
               VideoDesc,
               FileSize,
               FisrtLevelGroupid,
               SecLevelGroupid,
               SecretKey,
               UploadTime,
               TextTutorial,
               VideoTag,
               Description,
               BackColorArgb,
               ForeColorArgb,
               PublicMark,
               EnabledMark,
               RowNum,
               Remark,
               CreateBy,
               CreateByid,
               CreateDate,
               Lister,
               Listerid,
               ModifyDate,
               Assessor,
               Assessorid,
               AssessDate,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Custom6,
               Custom7,
               Custom8,
               Custom9,
               Custom10,
               Tenantid,
               TenantName,
               Revision
        from Sa_VideoLibrary
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaVideolibraryPojo">
        <include refid="selectSaVideolibraryVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_VideoLibrary.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.videotitle != null">
            and Sa_VideoLibrary.VideoTitle like concat('%', #{SearchPojo.videotitle}, '%')
        </if>
        <if test="SearchPojo.videoplayurl != null">
            and Sa_VideoLibrary.VideoPlayUrl like concat('%', #{SearchPojo.videoplayurl}, '%')
        </if>
        <if test="SearchPojo.videocoverurl != null">
            and Sa_VideoLibrary.VideoCoverUrl like concat('%', #{SearchPojo.videocoverurl}, '%')
        </if>
        <if test="SearchPojo.videodesc != null">
            and Sa_VideoLibrary.VideoDesc like concat('%', #{SearchPojo.videodesc}, '%')
        </if>
        <if test="SearchPojo.fisrtlevelgroupid != null">
            and Sa_VideoLibrary.FisrtLevelGroupid like concat('%', #{SearchPojo.fisrtlevelgroupid}, '%')
        </if>
        <if test="SearchPojo.seclevelgroupid != null">
            and Sa_VideoLibrary.SecLevelGroupid like concat('%', #{SearchPojo.seclevelgroupid}, '%')
        </if>
        <if test="SearchPojo.secretkey != null">
            and Sa_VideoLibrary.SecretKey like concat('%', #{SearchPojo.secretkey}, '%')
        </if>
        <if test="SearchPojo.texttutorial != null">
            and Sa_VideoLibrary.TextTutorial like concat('%', #{SearchPojo.texttutorial}, '%')
        </if>
        <if test="SearchPojo.videotag != null">
            and Sa_VideoLibrary.VideoTag like concat('%', #{SearchPojo.videotag}, '%')
        </if>
        <if test="SearchPojo.backcolorargb != null">
            and Sa_VideoLibrary.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
        </if>
        <if test="SearchPojo.forecolorargb != null">
            and Sa_VideoLibrary.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_VideoLibrary.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_VideoLibrary.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_VideoLibrary.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_VideoLibrary.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_VideoLibrary.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.assessor != null">
            and Sa_VideoLibrary.Assessor like concat('%', #{SearchPojo.assessor}, '%')
        </if>
        <if test="SearchPojo.assessorid != null">
            and Sa_VideoLibrary.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_VideoLibrary.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_VideoLibrary.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_VideoLibrary.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_VideoLibrary.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_VideoLibrary.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_VideoLibrary.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_VideoLibrary.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_VideoLibrary.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_VideoLibrary.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_VideoLibrary.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
        <if test="SearchPojo.tenantname != null">
            and Sa_VideoLibrary.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.videotitle != null">
                or Sa_VideoLibrary.VideoTitle like concat('%', #{SearchPojo.videotitle}, '%')
            </if>
            <if test="SearchPojo.videoplayurl != null">
                or Sa_VideoLibrary.VideoPlayUrl like concat('%', #{SearchPojo.videoplayurl}, '%')
            </if>
            <if test="SearchPojo.videocoverurl != null">
                or Sa_VideoLibrary.VideoCoverUrl like concat('%', #{SearchPojo.videocoverurl}, '%')
            </if>
            <if test="SearchPojo.videodesc != null">
                or Sa_VideoLibrary.VideoDesc like concat('%', #{SearchPojo.videodesc}, '%')
            </if>
            <if test="SearchPojo.fisrtlevelgroupid != null">
                or Sa_VideoLibrary.FisrtLevelGroupid like concat('%', #{SearchPojo.fisrtlevelgroupid}, '%')
            </if>
            <if test="SearchPojo.seclevelgroupid != null">
                or Sa_VideoLibrary.SecLevelGroupid like concat('%', #{SearchPojo.seclevelgroupid}, '%')
            </if>
            <if test="SearchPojo.secretkey != null">
                or Sa_VideoLibrary.SecretKey like concat('%', #{SearchPojo.secretkey}, '%')
            </if>
            <if test="SearchPojo.texttutorial != null">
                or Sa_VideoLibrary.TextTutorial like concat('%', #{SearchPojo.texttutorial}, '%')
            </if>
            <if test="SearchPojo.videotag != null">
                or Sa_VideoLibrary.VideoTag like concat('%', #{SearchPojo.videotag}, '%')
            </if>
            <if test="SearchPojo.backcolorargb != null">
                or Sa_VideoLibrary.BackColorArgb like concat('%', #{SearchPojo.backcolorargb}, '%')
            </if>
            <if test="SearchPojo.forecolorargb != null">
                or Sa_VideoLibrary.ForeColorArgb like concat('%', #{SearchPojo.forecolorargb}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_VideoLibrary.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_VideoLibrary.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_VideoLibrary.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_VideoLibrary.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_VideoLibrary.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.assessor != null">
                or Sa_VideoLibrary.Assessor like concat('%', #{SearchPojo.assessor}, '%')
            </if>
            <if test="SearchPojo.assessorid != null">
                or Sa_VideoLibrary.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_VideoLibrary.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_VideoLibrary.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_VideoLibrary.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_VideoLibrary.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_VideoLibrary.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_VideoLibrary.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_VideoLibrary.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_VideoLibrary.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_VideoLibrary.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_VideoLibrary.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
            <if test="SearchPojo.tenantname != null">
                or Sa_VideoLibrary.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_VideoLibrary(id, VideoTitle, VideoPlayUrl, VideoCoverUrl,Goodsid,DirName,FileName, VideoDuration,
        VideoPlayTimes,
        VideoGoodNum,
        VideoNgNum,
        VideoLevel,
        VideoDesc, FileSize, FisrtLevelGroupid, SecLevelGroupid, SecretKey, UploadTime,
        TextTutorial, VideoTag, Description, BackColorArgb, ForeColorArgb, PublicMark,
        EnabledMark,
        RowNum, Remark, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate,
        Assessor, Assessorid, AssessDate, Custom1, Custom2, Custom3, Custom4, Custom5,
        Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{videotitle}, #{videoplayurl}, #{videocoverurl},
        #{goodsid},#{dirname},#{filename},#{videoduration}, #{videoplaytimes},
        #{videogoodnum}, #{videongnum}, #{videolevel},
        #{videodesc}, #{filesize}, #{fisrtlevelgroupid}, #{seclevelgroupid}, #{secretkey}, #{uploadtime},
        #{texttutorial}, #{videotag}, #{description}, #{backcolorargb}, #{forecolorargb}, #{publicmark},
        #{enabledmark},
        #{rownum}, #{remark}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate},
        #{assessor}, #{assessorid}, #{assessdate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5},
        #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_VideoLibrary
        <set>
            <if test="videotitle != null">
                VideoTitle =#{videotitle},
            </if>
            <if test="videoplayurl != null">
                VideoPlayUrl =#{videoplayurl},
            </if>
            <if test="videocoverurl != null">
                VideoCoverUrl =#{videocoverurl},
            </if>
            <if test="goodsid != null">
                Goodsid =#{goodsid},
            </if>
            <if test="dirname != null">
                DirName =#{dirname},
            </if>
            <if test="filename != null">
                FileName =#{filename},
            </if>
            <if test="videoduration != null">
                VideoDuration =#{videoduration},
            </if>
            <if test="videoplaytimes != null">
                VideoPlayTimes =#{videoplaytimes},
            </if>
            <if test="videodesc != null">
                VideoDesc =#{videodesc},
            </if>
            <if test="filesize != null">
                FileSize =#{filesize},
            </if>
            <if test="fisrtlevelgroupid != null">
                FisrtLevelGroupid =#{fisrtlevelgroupid},
            </if>
            <if test="seclevelgroupid != null">
                SecLevelGroupid =#{seclevelgroupid},
            </if>
            <if test="secretkey != null">
                SecretKey =#{secretkey},
            </if>
            <if test="uploadtime != null">
                UploadTime =#{uploadtime},
            </if>
            <if test="texttutorial != null">
                TextTutorial =#{texttutorial},
            </if>
            <if test="videotag != null">
                VideoTag =#{videotag},
            </if>
            <if test="description != null">
                Description =#{description},
            </if>
            <if test="backcolorargb != null">
                BackColorArgb =#{backcolorargb},
            </if>
            <if test="forecolorargb != null">
                ForeColorArgb =#{forecolorargb},
            </if>
            <if test="publicmark != null">
                PublicMark =#{publicmark},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="assessor != null">
                Assessor =#{assessor},
            </if>
            <if test="assessorid != null">
                Assessorid =#{assessorid},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null">
                TenantName =#{tenantname},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_VideoLibrary
        where id = #{key}
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_VideoLibrary
        SET Assessor = #{assessor},
        Assessorid = #{assessorid},
        AssessDate = #{assessdate},
        Revision=Revision + 1
        where id = #{id}
    </update>
</mapper>

