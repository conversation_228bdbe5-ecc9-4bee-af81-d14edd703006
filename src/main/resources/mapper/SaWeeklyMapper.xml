<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaWeeklyMapper">

    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaWeeklyPojo">
        <include refid="selectSaWeeklyVo"/>
        where Sa_Weekly.id = #{key} 
    </select>
    <sql id="selectSaWeeklyVo">
         select
id, RefNo, Bill<PERSON><PERSON>le, BillT<PERSON>, BillDate, BillCode, Deptid, DeptName, Reporter, Reporterid, StateText, StateDate, CompJson, RecordJson, PlanJson, IssuesJson, RowNum, Remark, Assessorid, <PERSON>ses<PERSON>, Asses<PERSON><PERSON>ate, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>di<PERSON><PERSON>ate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, <PERSON><PERSON><PERSON>, <PERSON>ant<PERSON><PERSON>, Revision        from Sa_Weekly
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam" resultType="inks.service.sa.pms.domain.pojo.SaWeeklyPojo">
        <include refid="selectSaWeeklyVo"/>
         where 1 = 1 
         <if test="filterstr != null ">
            ${filterstr}
        </if>
         <if test="DateRange != null ">
            <if test="DateRange.DateColumn ==null or DateRange.DateColumn == ''">
                 and Sa_Weekly.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
             </if>
             <if test="DateRange.DateColumn !=null and DateRange.DateColumn != ''">
                  and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
              </if>
         </if>
        <if test="SearchPojo != null ">
         <if test="SearchType==0">           
           <include refid="and"></include>            
         </if>
         <if test="SearchType==1">     
           <include refid="or"></include>        
         </if>
        </if> 
        order by ${orderBy} 
        <if test="OrderType==0">asc</if>
        <if test="OrderType==1">desc</if>
    </select>
     <sql id="and">
<if test="SearchPojo.refno != null ">
   and Sa_Weekly.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   and Sa_Weekly.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   and Sa_Weekly.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billcode != null ">
   and Sa_Weekly.BillCode like concat('%', #{SearchPojo.billcode}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   and Sa_Weekly.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   and Sa_Weekly.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.reporter != null ">
   and Sa_Weekly.Reporter like concat('%', #{SearchPojo.reporter}, '%')
</if>
<if test="SearchPojo.reporterid != null ">
   and Sa_Weekly.Reporterid like concat('%', #{SearchPojo.reporterid}, '%')
</if>
<if test="SearchPojo.statetext != null ">
   and Sa_Weekly.StateText like concat('%', #{SearchPojo.statetext}, '%')
</if>
<if test="SearchPojo.compjson != null ">
   and Sa_Weekly.CompJson like concat('%', #{SearchPojo.compjson}, '%')
</if>
<if test="SearchPojo.recordjson != null ">
   and Sa_Weekly.RecordJson like concat('%', #{SearchPojo.recordjson}, '%')
</if>
<if test="SearchPojo.planjson != null ">
   and Sa_Weekly.PlanJson like concat('%', #{SearchPojo.planjson}, '%')
</if>
<if test="SearchPojo.issuesjson != null ">
   and Sa_Weekly.IssuesJson like concat('%', #{SearchPojo.issuesjson}, '%')
</if>
<if test="SearchPojo.remark != null ">
   and Sa_Weekly.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   and Sa_Weekly.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   and Sa_Weekly.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.createby != null ">
   and Sa_Weekly.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   and Sa_Weekly.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   and Sa_Weekly.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   and Sa_Weekly.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   and Sa_Weekly.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   and Sa_Weekly.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   and Sa_Weekly.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   and Sa_Weekly.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   and Sa_Weekly.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   and Sa_Weekly.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   and Sa_Weekly.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   and Sa_Weekly.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   and Sa_Weekly.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   and Sa_Weekly.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   and Sa_Weekly.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
     </sql>   
     <sql id="or">
  <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
<if test="SearchPojo.refno != null ">
   or Sa_Weekly.RefNo like concat('%', #{SearchPojo.refno}, '%')
</if>
<if test="SearchPojo.billtitle != null ">
   or Sa_Weekly.BillTitle like concat('%', #{SearchPojo.billtitle}, '%')
</if>
<if test="SearchPojo.billtype != null ">
   or Sa_Weekly.BillType like concat('%', #{SearchPojo.billtype}, '%')
</if>
<if test="SearchPojo.billcode != null ">
   or Sa_Weekly.BillCode like concat('%', #{SearchPojo.billcode}, '%')
</if>
<if test="SearchPojo.deptid != null ">
   or Sa_Weekly.Deptid like concat('%', #{SearchPojo.deptid}, '%')
</if>
<if test="SearchPojo.deptname != null ">
   or Sa_Weekly.DeptName like concat('%', #{SearchPojo.deptname}, '%')
</if>
<if test="SearchPojo.reporter != null ">
   or Sa_Weekly.Reporter like concat('%', #{SearchPojo.reporter}, '%')
</if>
<if test="SearchPojo.reporterid != null ">
   or Sa_Weekly.Reporterid like concat('%', #{SearchPojo.reporterid}, '%')
</if>
<if test="SearchPojo.statetext != null ">
   or Sa_Weekly.StateText like concat('%', #{SearchPojo.statetext}, '%')
</if>
<if test="SearchPojo.compjson != null ">
   or Sa_Weekly.CompJson like concat('%', #{SearchPojo.compjson}, '%')
</if>
<if test="SearchPojo.recordjson != null ">
   or Sa_Weekly.RecordJson like concat('%', #{SearchPojo.recordjson}, '%')
</if>
<if test="SearchPojo.planjson != null ">
   or Sa_Weekly.PlanJson like concat('%', #{SearchPojo.planjson}, '%')
</if>
<if test="SearchPojo.issuesjson != null ">
   or Sa_Weekly.IssuesJson like concat('%', #{SearchPojo.issuesjson}, '%')
</if>
<if test="SearchPojo.remark != null ">
   or Sa_Weekly.Remark like concat('%', #{SearchPojo.remark}, '%')
</if>
<if test="SearchPojo.assessorid != null ">
   or Sa_Weekly.Assessorid like concat('%', #{SearchPojo.assessorid}, '%')
</if>
<if test="SearchPojo.assessor != null ">
   or Sa_Weekly.Assessor like concat('%', #{SearchPojo.assessor}, '%')
</if>
<if test="SearchPojo.createby != null ">
   or Sa_Weekly.CreateBy like concat('%', #{SearchPojo.createby}, '%')
</if>
<if test="SearchPojo.createbyid != null ">
   or Sa_Weekly.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
</if>
<if test="SearchPojo.lister != null ">
   or Sa_Weekly.Lister like concat('%', #{SearchPojo.lister}, '%')
</if>
<if test="SearchPojo.listerid != null ">
   or Sa_Weekly.Listerid like concat('%', #{SearchPojo.listerid}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
   or Sa_Weekly.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
</if>
<if test="SearchPojo.custom2 != null ">
   or Sa_Weekly.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
</if>
<if test="SearchPojo.custom3 != null ">
   or Sa_Weekly.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
</if>
<if test="SearchPojo.custom4 != null ">
   or Sa_Weekly.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
</if>
<if test="SearchPojo.custom5 != null ">
   or Sa_Weekly.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
</if>
<if test="SearchPojo.custom6 != null ">
   or Sa_Weekly.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
</if>
<if test="SearchPojo.custom7 != null ">
   or Sa_Weekly.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
</if>
<if test="SearchPojo.custom8 != null ">
   or Sa_Weekly.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
</if>
<if test="SearchPojo.custom9 != null ">
   or Sa_Weekly.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
</if>
<if test="SearchPojo.custom10 != null ">
   or Sa_Weekly.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
</if>
<if test="SearchPojo.tenantname != null ">
   or Sa_Weekly.TenantName like concat('%', #{SearchPojo.tenantname}, '%')
</if>
</trim>
     </sql>
    <!--新增所有列-->
    <insert id="insert" >
        insert into Sa_Weekly(id, RefNo, BillTitle, BillType, BillDate, BillCode, Deptid, DeptName, Reporter, Reporterid, StateText, StateDate, CompJson, RecordJson, PlanJson, IssuesJson, RowNum, Remark, Assessorid, Assessor, AssessDate, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, TenantName, Revision)
        values (#{id}, #{refno}, #{billtitle}, #{billtype}, #{billdate}, #{billcode}, #{deptid}, #{deptname}, #{reporter}, #{reporterid}, #{statetext}, #{statedate}, #{compjson}, #{recordjson}, #{planjson}, #{issuesjson}, #{rownum}, #{remark}, #{assessorid}, #{assessor}, #{assessdate}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{tenantname}, #{revision})
    </insert> 

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Weekly
        <set>
            <if test="refno != null ">
                RefNo =#{refno},
            </if>
            <if test="billtitle != null ">
                BillTitle =#{billtitle},
            </if>
            <if test="billtype != null ">
                BillType =#{billtype},
            </if>
            <if test="billdate != null">
                BillDate =#{billdate},
            </if>
            <if test="billcode != null ">
                BillCode =#{billcode},
            </if>
            <if test="deptid != null ">
                Deptid =#{deptid},
            </if>
            <if test="deptname != null ">
                DeptName =#{deptname},
            </if>
            <if test="reporter != null ">
                Reporter =#{reporter},
            </if>
            <if test="reporterid != null ">
                Reporterid =#{reporterid},
            </if>
            <if test="statetext != null ">
                StateText =#{statetext},
            </if>
            <if test="statedate != null">
                StateDate =#{statedate},
            </if>
            <if test="compjson != null ">
                CompJson =#{compjson},
            </if>
            <if test="recordjson != null ">
                RecordJson =#{recordjson},
            </if>
            <if test="planjson != null ">
                PlanJson =#{planjson},
            </if>
            <if test="issuesjson != null ">
                IssuesJson =#{issuesjson},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="remark != null ">
                Remark =#{remark},
            </if>
            <if test="assessorid != null ">
                Assessorid =#{assessorid},
            </if>
            <if test="assessor != null ">
                Assessor =#{assessor},
            </if>
            <if test="assessdate != null">
                AssessDate =#{assessdate},
            </if>
            <if test="createby != null ">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null ">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null ">
                Lister =#{lister},
            </if>
            <if test="listerid != null ">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null ">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null ">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null ">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null ">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null ">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null ">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null ">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null ">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null ">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null ">
                Custom10 =#{custom10},
            </if>
            <if test="tenantname != null ">
                TenantName =#{tenantname},
            </if>
                Revision=Revision+1
        </set>
        where id = #{id} 
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Weekly where id = #{key} 
    </delete>
    <!--通过主键审核数据-->
    <update id="approval">
        update Sa_Weekly SET
            Assessor = #{assessor},
            Assessorid = #{assessorid},
            AssessDate = #{assessdate},
            Revision=Revision+1
        where id = #{id}
    </update>
</mapper>

