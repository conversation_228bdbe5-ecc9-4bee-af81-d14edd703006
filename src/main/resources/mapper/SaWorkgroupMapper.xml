<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaWorkgroupMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaWorkgroupPojo">
        <include refid="selectSaWorkgroupVo"/>
        where Sa_Workgroup.id = #{key}
    </select>
    <sql id="selectSaWorkgroupVo">
         select
id, WgGroupid, GroupUid, GroupName, Abbreviate, GroupClass, Linkman, Telephone, GroupFax, GroupAdd, Remark, InvalidDate, GroupType, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Mobile, Country, Province, GroupZip, Seller, EnabledMark, Operator, Operatorid, Collaboratorids, Collaborators, SerCode, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Sa_Workgroup
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaWorkgroupPojo">
        <include refid="selectSaWorkgroupVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_Workgroup.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.wggroupid != null">
            and Sa_Workgroup.WgGroupid like concat('%', #{SearchPojo.wggroupid}, '%')
        </if>
        <if test="SearchPojo.groupuid != null">
            and Sa_Workgroup.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
        </if>
        <if test="SearchPojo.groupname != null">
            and Sa_Workgroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
        </if>
        <if test="SearchPojo.abbreviate != null">
            and Sa_Workgroup.Abbreviate like concat('%', #{SearchPojo.abbreviate}, '%')
        </if>
        <if test="SearchPojo.groupclass != null">
            and Sa_Workgroup.GroupClass like concat('%', #{SearchPojo.groupclass}, '%')
        </if>
        <if test="SearchPojo.linkman != null">
            and Sa_Workgroup.Linkman like concat('%', #{SearchPojo.linkman}, '%')
        </if>
        <if test="SearchPojo.telephone != null">
            and Sa_Workgroup.Telephone like concat('%', #{SearchPojo.telephone}, '%')
        </if>
        <if test="SearchPojo.groupfax != null">
            and Sa_Workgroup.GroupFax like concat('%', #{SearchPojo.groupfax}, '%')
        </if>
        <if test="SearchPojo.groupadd != null">
            and Sa_Workgroup.GroupAdd like concat('%', #{SearchPojo.groupadd}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_Workgroup.Remark like concat('%', #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.grouptype != null">
            and Sa_Workgroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_Workgroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_Workgroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_Workgroup.Lister like concat('%', #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_Workgroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.mobile != null">
            and Sa_Workgroup.Mobile like concat('%', #{SearchPojo.mobile}, '%')
        </if>
        <if test="SearchPojo.country != null">
            and Sa_Workgroup.Country like concat('%', #{SearchPojo.country}, '%')
        </if>
        <if test="SearchPojo.province != null">
            and Sa_Workgroup.Province like concat('%', #{SearchPojo.province}, '%')
        </if>
        <if test="SearchPojo.groupzip != null">
            and Sa_Workgroup.GroupZip like concat('%', #{SearchPojo.groupzip}, '%')
        </if>
        <if test="SearchPojo.seller != null">
            and Sa_Workgroup.Seller like concat('%', #{SearchPojo.seller}, '%')
        </if>
<if test="SearchPojo.operator != null ">
   and Sa_Workgroup.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   and Sa_Workgroup.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.collaboratorids != null ">
   and Sa_Workgroup.Collaboratorids like concat('%', #{SearchPojo.collaboratorids}, '%')
</if>
<if test="SearchPojo.collaborators != null ">
   and Sa_Workgroup.Collaborators like concat('%', #{SearchPojo.collaborators}, '%')
</if>
<if test="SearchPojo.custom1 != null ">
            and Sa_Workgroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_Workgroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_Workgroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_Workgroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_Workgroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_Workgroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_Workgroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_Workgroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_Workgroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_Workgroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.wggroupid != null">
                or Sa_Workgroup.WgGroupid like concat('%', #{SearchPojo.wggroupid}, '%')
            </if>
            <if test="SearchPojo.groupuid != null">
                or Sa_Workgroup.GroupUid like concat('%', #{SearchPojo.groupuid}, '%')
            </if>
            <if test="SearchPojo.groupname != null">
                or Sa_Workgroup.GroupName like concat('%', #{SearchPojo.groupname}, '%')
            </if>
            <if test="SearchPojo.abbreviate != null">
                or Sa_Workgroup.Abbreviate like concat('%', #{SearchPojo.abbreviate}, '%')
            </if>
            <if test="SearchPojo.groupclass != null">
                or Sa_Workgroup.GroupClass like concat('%', #{SearchPojo.groupclass}, '%')
            </if>
            <if test="SearchPojo.linkman != null">
                or Sa_Workgroup.Linkman like concat('%', #{SearchPojo.linkman}, '%')
            </if>
            <if test="SearchPojo.telephone != null">
                or Sa_Workgroup.Telephone like concat('%', #{SearchPojo.telephone}, '%')
            </if>
            <if test="SearchPojo.groupfax != null">
                or Sa_Workgroup.GroupFax like concat('%', #{SearchPojo.groupfax}, '%')
            </if>
            <if test="SearchPojo.groupadd != null">
                or Sa_Workgroup.GroupAdd like concat('%', #{SearchPojo.groupadd}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_Workgroup.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.grouptype != null">
                or Sa_Workgroup.GroupType like concat('%', #{SearchPojo.grouptype}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_Workgroup.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_Workgroup.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_Workgroup.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_Workgroup.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.mobile != null">
                or Sa_Workgroup.Mobile like concat('%', #{SearchPojo.mobile}, '%')
            </if>
            <if test="SearchPojo.country != null">
                or Sa_Workgroup.Country like concat('%', #{SearchPojo.country}, '%')
            </if>
            <if test="SearchPojo.province != null">
                or Sa_Workgroup.Province like concat('%', #{SearchPojo.province}, '%')
            </if>
            <if test="SearchPojo.groupzip != null">
                or Sa_Workgroup.GroupZip like concat('%', #{SearchPojo.groupzip}, '%')
            </if>
            <if test="SearchPojo.seller != null">
   or Sa_Workgroup.Seller like concat('%', #{SearchPojo.seller}, '%')
</if>
<if test="SearchPojo.operator != null ">
   or Sa_Workgroup.Operator like concat('%', #{SearchPojo.operator}, '%')
</if>
<if test="SearchPojo.operatorid != null ">
   or Sa_Workgroup.Operatorid like concat('%', #{SearchPojo.operatorid}, '%')
</if>
<if test="SearchPojo.collaboratorids != null ">
   or Sa_Workgroup.Collaboratorids like concat('%', #{SearchPojo.collaboratorids}, '%')
</if>
<if test="SearchPojo.collaborators != null ">
   or Sa_Workgroup.Collaborators like concat('%', #{SearchPojo.collaborators}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_Workgroup.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_Workgroup.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_Workgroup.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_Workgroup.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_Workgroup.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_Workgroup.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_Workgroup.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_Workgroup.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_Workgroup.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_Workgroup.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_Workgroup(id, WgGroupid, GroupUid, GroupName, Abbreviate, GroupClass, Linkman, Telephone, GroupFax, GroupAdd, Remark, InvalidDate, GroupType, RowNum, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Mobile, Country, Province, GroupZip, Seller, EnabledMark, Operator, Operatorid, Collaboratorids, Collaborators, SerCode, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{wggroupid}, #{groupuid}, #{groupname}, #{abbreviate}, #{groupclass}, #{linkman}, #{telephone}, #{groupfax}, #{groupadd}, #{remark}, #{invaliddate}, #{grouptype}, #{rownum}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{mobile}, #{country}, #{province}, #{groupzip}, #{seller}, #{enabledmark}, #{operator}, #{operatorid}, #{collaboratorids}, #{collaborators}, #{sercode}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_Workgroup
        <set>
            <if test="wggroupid != null">
                WgGroupid =#{wggroupid},
            </if>
            <if test="groupuid != null">
                GroupUid =#{groupuid},
            </if>
            <if test="groupname != null">
                GroupName =#{groupname},
            </if>
            <if test="abbreviate != null">
                Abbreviate =#{abbreviate},
            </if>
            <if test="groupclass != null">
                GroupClass =#{groupclass},
            </if>
            <if test="linkman != null">
                Linkman =#{linkman},
            </if>
            <if test="telephone != null">
                Telephone =#{telephone},
            </if>
            <if test="groupfax != null">
                GroupFax =#{groupfax},
            </if>
            <if test="groupadd != null">
                GroupAdd =#{groupadd},
            </if>
            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="invaliddate != null">
                InvalidDate =#{invaliddate},
            </if>
            <if test="grouptype != null">
                GroupType =#{grouptype},
            </if>
            <if test="rownum != null">
                RowNum =#{rownum},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="mobile != null">
                Mobile =#{mobile},
            </if>
            <if test="country != null">
                Country =#{country},
            </if>
            <if test="province != null">
                Province =#{province},
            </if>
            <if test="groupzip != null">
                GroupZip =#{groupzip},
            </if>
            <if test="seller != null">
                Seller =#{seller},
            </if>
            <if test="enabledmark != null">
                EnabledMark =#{enabledmark},
            </if>
            <if test="operator != null ">
                Operator =#{operator},
            </if>
            <if test="operatorid != null ">
                Operatorid =#{operatorid},
            </if>
            <if test="collaboratorids != null ">
                Collaboratorids =#{collaboratorids},
            </if>
            <if test="collaborators != null ">
                Collaborators =#{collaborators},
            </if>
            <if test="sercode != null ">
                SerCode =#{sercode},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            Revision=Revision+1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete from Sa_Workgroup where id = #{key}
    </delete>

    <select id="countBySerCode" resultType="int">
        select count(1) from Sa_Workgroup where SerCode = #{sercode}
    </select>

    <select id="findBySerCode" resultType="inks.service.sa.pms.domain.pojo.SaWorkgroupPojo">
        select * from Sa_Workgroup where SerCode = #{sercode}
    </select>

    <select id="getIdBySerCode" resultType="java.lang.String">
        select id from Sa_Workgroup where SerCode = #{sercode}
    </select>
</mapper>

