<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaWorklogMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaWorklogPojo">
        <include refid="selectbillVo"/>
        where Sa_WorkLog.id = #{key}
    </select>
    <!--查询指定行返回列-->
    <sql id="selectbillVo">
         select
id, WorkDate, WorkType, Weather, WorkToday, WorkTomorrow, Title, SendEmailNum, ToEmail, OtherEmails, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision        from Sa_WorkLog
    </sql>
    <sql id="selectdetailVo">
        select Sa_WorkLogItem.id,
               Sa_WorkLogItem.Pid,
               Sa_WorkLogItem.ItemType,
               Sa_WorkLogItem.Projectid,
               Sa_WorkLogItem.WorkTime,
               Sa_WorkLogItem.ItemDesc,
               Sa_WorkLogItem.ModuleCode,
               Sa_WorkLogItem.WorkCompRate,
               Sa_WorkLogItem.RowNum,
               Sa_WorkLogItem.Remark,
               Sa_WorkLogItem.Custom1,
               Sa_WorkLogItem.Custom2,
               Sa_WorkLogItem.Custom3,
               Sa_WorkLogItem.Custom4,
               Sa_WorkLogItem.Custom5,
               Sa_WorkLogItem.Revision,
               Sa_WorkLog.WorkDate,
               Sa_WorkLog.Weather,
               Sa_WorkLog.WorkType,
               Sa_WorkLog.WorkToday,
               Sa_WorkLog.WorkTomorrow,
               Sa_WorkLog.Title,
               Sa_WorkLog.SendEmailNum,
               Sa_WorkLog.ToEmail,
               Sa_WorkLog.OtherEmails,
               Sa_WorkLog.Summary,
               Sa_WorkLog.CreateBy,
               Sa_WorkLog.CreateByid,
               Sa_WorkLog.CreateDate,
               Sa_WorkLog.Lister,
               Sa_WorkLog.Listerid,
               Sa_WorkLog.ModifyDate
        from Sa_WorkLog
                 Right Join Sa_WorkLogItem on Sa_WorkLog.id = Sa_WorkLogItem.Pid
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaWorklogitemdetailPojo">
        <include refid="selectdetailVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_WorkLog.CreateDate BETWEEN #{dateRange.startDate} and #{dateRange.endDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.id != null">
            and Sa_WorkLog.id like concat('%', #{SearchPojo.id}, '%')
        </if>
        <if test="SearchPojo.weather != null">
            and Sa_WorkLog.weather like concat('%',
            #{SearchPojo.weather}, '%')
        </if>
        <if test="SearchPojo.worktoday != null">
            and Sa_WorkLog.worktoday like concat('%',
            #{SearchPojo.worktoday}, '%')
        </if>
        <if test="SearchPojo.worktomorrow != null">
            and Sa_WorkLog.worktomorrow like concat('%',
            #{SearchPojo.worktomorrow}, '%')
        </if>
        <if test="SearchPojo.title != null">
            and Sa_WorkLog.title like concat('%',
            #{SearchPojo.title}, '%')
        </if>
        <if test="SearchPojo.toemail != null">
            and Sa_WorkLog.toemail like concat('%',
            #{SearchPojo.toemail}, '%')
        </if>
        <if test="SearchPojo.otheremails != null">
            and Sa_WorkLog.otheremails like concat('%',
            #{SearchPojo.otheremails}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_WorkLog.createby like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_WorkLog.createbyid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_WorkLog.lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_WorkLog.listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_WorkLog.custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_WorkLog.custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_WorkLog.custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_WorkLog.custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_WorkLog.custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_WorkLog.custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_WorkLog.custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_WorkLog.custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_WorkLog.custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_WorkLog.custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.id != null">
                or Sa_WorkLog.id like concat('%', #{SearchPojo.id}, '%')
            </if>
            <if test="SearchPojo.weather != null">
                or Sa_WorkLog.Weather like concat('%', #{SearchPojo.weather}, '%')
            </if>
            <if test="SearchPojo.worktoday != null">
                or Sa_WorkLog.WorkToday like concat('%', #{SearchPojo.worktoday}, '%')
            </if>
            <if test="SearchPojo.worktomorrow != null">
                or Sa_WorkLog.WorkTomorrow like concat('%', #{SearchPojo.worktomorrow}, '%')
            </if>
            <if test="SearchPojo.title != null">
                or Sa_WorkLog.Title like concat('%', #{SearchPojo.title}, '%')
            </if>
            <if test="SearchPojo.toemail != null">
                or Sa_WorkLog.ToEmail like concat('%', #{SearchPojo.toemail}, '%')
            </if>
            <if test="SearchPojo.otheremails != null">
                or Sa_WorkLog.OtherEmails like concat('%', #{SearchPojo.otheremails}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_WorkLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_WorkLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_WorkLog.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_WorkLog.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_WorkLog.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_WorkLog.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_WorkLog.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_WorkLog.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_WorkLog.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_WorkLog.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_WorkLog.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_WorkLog.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_WorkLog.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_WorkLog.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--查询指定行数据-->
    <select id="getPageTh" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaWorklogPojo">
        <include refid="selectbillVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_WorkLog.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="thand">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="thor">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="thand">
        <if test="SearchPojo.id != null">
            and Sa_WorkLog.id like concat('%', #{SearchPojo.id}, '%')
        </if>
        <if test="SearchPojo.weather != null">
            and Sa_WorkLog.Weather like concat('%',
            #{SearchPojo.weather}, '%')
        </if>
        <if test="SearchPojo.worktoday != null">
            and Sa_WorkLog.WorkToday like concat('%',
            #{SearchPojo.worktoday}, '%')
        </if>
        <if test="SearchPojo.worktomorrow != null">
            and Sa_WorkLog.WorkTomorrow like concat('%',
            #{SearchPojo.worktomorrow}, '%')
        </if>
        <if test="SearchPojo.title != null">
            and Sa_WorkLog.Title like concat('%',
            #{SearchPojo.title}, '%')
        </if>
        <if test="SearchPojo.toemail != null">
            and Sa_WorkLog.ToEmail like concat('%',
            #{SearchPojo.toemail}, '%')
        </if>
        <if test="SearchPojo.otheremails != null">
            and Sa_WorkLog.OtherEmails like concat('%',
            #{SearchPojo.otheremails}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_WorkLog.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.createbyid != null">
            and Sa_WorkLog.CreateByid like concat('%',
            #{SearchPojo.createbyid}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_WorkLog.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
        <if test="SearchPojo.listerid != null">
            and Sa_WorkLog.Listerid like concat('%',
            #{SearchPojo.listerid}, '%')
        </if>
        <if test="SearchPojo.custom1 != null">
            and Sa_WorkLog.Custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null">
            and Sa_WorkLog.Custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null">
            and Sa_WorkLog.Custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null">
            and Sa_WorkLog.Custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null">
            and Sa_WorkLog.Custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
        <if test="SearchPojo.custom6 != null">
            and Sa_WorkLog.Custom6 like concat('%',
            #{SearchPojo.custom6}, '%')
        </if>
        <if test="SearchPojo.custom7 != null">
            and Sa_WorkLog.Custom7 like concat('%',
            #{SearchPojo.custom7}, '%')
        </if>
        <if test="SearchPojo.custom8 != null">
            and Sa_WorkLog.Custom8 like concat('%',
            #{SearchPojo.custom8}, '%')
        </if>
        <if test="SearchPojo.custom9 != null">
            and Sa_WorkLog.Custom9 like concat('%',
            #{SearchPojo.custom9}, '%')
        </if>
        <if test="SearchPojo.custom10 != null">
            and Sa_WorkLog.Custom10 like concat('%',
            #{SearchPojo.custom10}, '%')
        </if>
    </sql>
    <sql id="thor">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.id != null">
                or Sa_WorkLog.id like concat('%', #{SearchPojo.id}, '%')
            </if>
            <if test="SearchPojo.weather != null">
                or Sa_WorkLog.Weather like concat('%', #{SearchPojo.weather}, '%')
            </if>
            <if test="SearchPojo.worktoday != null">
                or Sa_WorkLog.WorkToday like concat('%', #{SearchPojo.worktoday}, '%')
            </if>
            <if test="SearchPojo.worktomorrow != null">
                or Sa_WorkLog.WorkTomorrow like concat('%', #{SearchPojo.worktomorrow}, '%')
            </if>
            <if test="SearchPojo.title != null">
                or Sa_WorkLog.Title like concat('%', #{SearchPojo.title}, '%')
            </if>
            <if test="SearchPojo.toemail != null">
                or Sa_WorkLog.ToEmail like concat('%', #{SearchPojo.toemail}, '%')
            </if>
            <if test="SearchPojo.otheremails != null">
                or Sa_WorkLog.OtherEmails like concat('%', #{SearchPojo.otheremails}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_WorkLog.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.createbyid != null">
                or Sa_WorkLog.CreateByid like concat('%', #{SearchPojo.createbyid}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_WorkLog.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
            <if test="SearchPojo.listerid != null">
                or Sa_WorkLog.Listerid like concat('%', #{SearchPojo.listerid}, '%')
            </if>
            <if test="SearchPojo.custom1 != null">
                or Sa_WorkLog.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null">
                or Sa_WorkLog.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null">
                or Sa_WorkLog.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null">
                or Sa_WorkLog.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null">
                or Sa_WorkLog.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
            <if test="SearchPojo.custom6 != null">
                or Sa_WorkLog.Custom6 like concat('%', #{SearchPojo.custom6}, '%')
            </if>
            <if test="SearchPojo.custom7 != null">
                or Sa_WorkLog.Custom7 like concat('%', #{SearchPojo.custom7}, '%')
            </if>
            <if test="SearchPojo.custom8 != null">
                or Sa_WorkLog.Custom8 like concat('%', #{SearchPojo.custom8}, '%')
            </if>
            <if test="SearchPojo.custom9 != null">
                or Sa_WorkLog.Custom9 like concat('%', #{SearchPojo.custom9}, '%')
            </if>
            <if test="SearchPojo.custom10 != null">
                or Sa_WorkLog.Custom10 like concat('%', #{SearchPojo.custom10}, '%')
            </if>
        </trim>
    </sql>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_WorkLog(id, WorkDate, WorkType, Weather, WorkToday, WorkTomorrow, Title, SendEmailNum, ToEmail, OtherEmails, Summary, CreateBy, CreateByid, CreateDate, Lister, Listerid, ModifyDate, Custom1, Custom2, Custom3, Custom4, Custom5, Custom6, Custom7, Custom8, Custom9, Custom10, Tenantid, Revision)
        values (#{id}, #{workdate}, #{worktype}, #{weather}, #{worktoday}, #{worktomorrow}, #{title}, #{sendemailnum}, #{toemail}, #{otheremails}, #{summary}, #{createby}, #{createbyid}, #{createdate}, #{lister}, #{listerid}, #{modifydate}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{custom6}, #{custom7}, #{custom8}, #{custom9}, #{custom10}, #{tenantid}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_WorkLog
        <set>
            <if test="id != null">
                id =#{id},
            </if>
            <if test="workdate != null">
                WorkDate =#{workdate},
            </if>
            <if test="worktype != null">
                WorkType =#{worktype},
            </if>
            <if test="weather != null">
                Weather =#{weather},
            </if>
            <if test="worktoday != null">
                WorkToday =#{worktoday},
            </if>
            <if test="worktomorrow != null">
                WorkTomorrow =#{worktomorrow},
            </if>
            <if test="title != null">
                Title =#{title},
            </if>
            <if test="sendemailnum != null">
                SendEmailNum =#{sendemailnum},
            </if>
            <if test="toemail != null">
                ToEmail =#{toemail},
            </if>
            <if test="otheremails != null">
                OtherEmails =#{otheremails},
            </if>
            <if test="summary != null ">
                Summary =#{summary},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createbyid != null">
                CreateByid =#{createbyid},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="listerid != null">
                Listerid =#{listerid},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="custom1 != null">
                Custom1 =#{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 =#{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 =#{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 =#{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 =#{custom5},
            </if>
            <if test="custom6 != null">
                Custom6 =#{custom6},
            </if>
            <if test="custom7 != null">
                Custom7 =#{custom7},
            </if>
            <if test="custom8 != null">
                Custom8 =#{custom8},
            </if>
            <if test="custom9 != null">
                Custom9 =#{custom9},
            </if>
            <if test="custom10 != null">
                Custom10 =#{custom10},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_WorkLog
        where id = #{key}
    </delete>
    <!--查询DelListIds-->
    <select id="getDelItemIds" resultType="java.lang.String"
            parameterType="inks.service.sa.pms.domain.pojo.SaWorklogPojo">
        select id
        from Sa_WorkLogItem
        where Pid = #{id}
        <if test="item != null and item.size() > 0">
            and id not in
            <foreach collection="item" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="getDelPlanIds" resultType="java.lang.String"
            parameterType="inks.service.sa.pms.domain.pojo.SaWorklogPojo">
        select id
        from Sa_WorkLogPlan
        where Pid = #{id}
        <if test="plan != null and plan.size() > 0">
            and id not in
            <foreach collection="plan" open="(" close=")" separator="," item="item">
                <if test="item.id != null">
                    #{item.id}
                </if>
                <if test="item.id == null">
                    ''
                </if>
            </foreach>
        </if>
    </select>

    <select id="checkWorklogTitle" resultType="int">
        select count(1)
        from Sa_WorkLog
        where Title = #{title}
        and SendEmailNum > 0
    </select>
</mapper>

