<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="inks.service.sa.pms.mapper.SaWorklogitemMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaWorklogitemPojo">
        select id,
        Pid,
        ItemType,
        Projectid,
        WorkTime,
        ItemDesc,
        ModuleCode,
        WorkCompRate,
        RowNum,
        Remark,
        Custom1,
        Custom2,
        Custom3,
        Custom4,
        Custom5,
        Revision
        from Sa_WorkLogItem
        where Sa_WorkLogItem.id = #{key}
    </select>
    <sql id="selectSaWorklogitemVo">
        select id,
               Pid,
               ItemType,
               Projectid,
               WorkTime,
               ItemDesc,
               ModuleCode,
               WorkCompRate,
               RowNum,
               Remark,
               Custom1,
               Custom2,
               Custom3,
               Custom4,
               Custom5,
               Revision
        from Sa_WorkLogItem
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaWorklogitemPojo">
        <include refid="selectSaWorklogitemVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_WorkLogItem.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.startDate} and #{DateRange.endDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
            and Sa_WorkLogItem.pid like concat('%', #{SearchPojo.pid}, '%')
        </if>
        <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
            and Sa_WorkLogItem.itemtype like concat('%',
            #{SearchPojo.itemtype}, '%')
        </if>
        <if test="SearchPojo.projectid != null and SearchPojo.projectid != ''">
            and Sa_WorkLogItem.projectid like concat('%',
            #{SearchPojo.projectid}, '%')
        </if>
        <if test="SearchPojo.itemdesc != null and SearchPojo.itemdesc != ''">
            and Sa_WorkLogItem.itemdesc like concat('%',
            #{SearchPojo.itemdesc}, '%')
        </if>
        <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
            and Sa_WorkLogItem.modulecode like concat('%',
            #{SearchPojo.modulecode}, '%')
        </if>
        <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
            and Sa_WorkLogItem.remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
            and Sa_WorkLogItem.custom1 like concat('%',
            #{SearchPojo.custom1}, '%')
        </if>
        <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
            and Sa_WorkLogItem.custom2 like concat('%',
            #{SearchPojo.custom2}, '%')
        </if>
        <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
            and Sa_WorkLogItem.custom3 like concat('%',
            #{SearchPojo.custom3}, '%')
        </if>
        <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
            and Sa_WorkLogItem.custom4 like concat('%',
            #{SearchPojo.custom4}, '%')
        </if>
        <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
            and Sa_WorkLogItem.custom5 like concat('%',
            #{SearchPojo.custom5}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.pid != null and SearchPojo.pid != ''">
                or Sa_WorkLogItem.Pid like concat('%', #{SearchPojo.pid}, '%')
            </if>
            <if test="SearchPojo.itemtype != null and SearchPojo.itemtype != ''">
                or Sa_WorkLogItem.ItemType like concat('%', #{SearchPojo.itemtype}, '%')
            </if>
            <if test="SearchPojo.projectid != null and SearchPojo.projectid != ''">
                or Sa_WorkLogItem.Projectid like concat('%', #{SearchPojo.projectid}, '%')
            </if>
            <if test="SearchPojo.itemdesc != null and SearchPojo.itemdesc != ''">
                or Sa_WorkLogItem.ItemDesc like concat('%', #{SearchPojo.itemdesc}, '%')
            </if>
            <if test="SearchPojo.modulecode != null and SearchPojo.modulecode != ''">
                or Sa_WorkLogItem.ModuleCode like concat('%', #{SearchPojo.modulecode}, '%')
            </if>
            <if test="SearchPojo.remark != null and SearchPojo.remark != ''">
                or Sa_WorkLogItem.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.custom1 != null and SearchPojo.custom1 != ''">
                or Sa_WorkLogItem.Custom1 like concat('%', #{SearchPojo.custom1}, '%')
            </if>
            <if test="SearchPojo.custom2 != null and SearchPojo.custom2 != ''">
                or Sa_WorkLogItem.Custom2 like concat('%', #{SearchPojo.custom2}, '%')
            </if>
            <if test="SearchPojo.custom3 != null and SearchPojo.custom3 != ''">
                or Sa_WorkLogItem.Custom3 like concat('%', #{SearchPojo.custom3}, '%')
            </if>
            <if test="SearchPojo.custom4 != null and SearchPojo.custom4 != ''">
                or Sa_WorkLogItem.Custom4 like concat('%', #{SearchPojo.custom4}, '%')
            </if>
            <if test="SearchPojo.custom5 != null and SearchPojo.custom5 != ''">
                or Sa_WorkLogItem.Custom5 like concat('%', #{SearchPojo.custom5}, '%')
            </if>
        </trim>
    </sql>

    <!--查询List-->
    <select id="getList" resultType="inks.service.sa.pms.domain.pojo.SaWorklogitemPojo">
        select Sa_WorkLogItem.id,
        Sa_WorkLogItem.Pid,
        Sa_WorkLogItem.ItemType,
        Sa_WorkLogItem.Projectid,
        Sa_WorkLogItem.WorkTime,
        Sa_WorkLogItem.ItemDesc,
        Sa_WorkLogItem.ModuleCode,
        Sa_WorkLogItem.WorkCompRate,
        Sa_WorkLogItem.RowNum,
        Sa_WorkLogItem.Remark,
        Sa_WorkLogItem.Custom1,
        Sa_WorkLogItem.Custom2,
        Sa_WorkLogItem.Custom3,
        Sa_WorkLogItem.Custom4,
        Sa_WorkLogItem.Custom5,
        Sa_WorkLogItem.Revision,
        Sa_Project.ProjName as projname
        from Sa_WorkLogItem
        Left join Sa_Project on Sa_WorkLogItem.Projectid = Sa_Project.id
        where Sa_WorkLogItem.Pid = #{Pid}
        order by RowNum
    </select>

    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_WorkLogItem(id, Pid, ItemType, Projectid, WorkTime, ItemDesc, ModuleCode, WorkCompRate, RowNum,
        Remark, Custom1, Custom2, Custom3, Custom4, Custom5, Revision)
        values (#{id}, #{pid}, #{itemtype}, #{projectid}, #{worktime}, #{itemdesc}, #{modulecode}, #{workcomprate},
        #{rownum}, #{remark}, #{custom1}, #{custom2}, #{custom3}, #{custom4}, #{custom5}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_WorkLogItem
        <set>
            <if test="pid != null">
                Pid = #{pid},
            </if>
            <if test="itemtype != null">
                ItemType = #{itemtype},
            </if>
            <if test="projectid != null">
                Projectid = #{projectid},
            </if>
            <if test="worktime != null">
                WorkTime = #{worktime},
            </if>
            <if test="itemdesc != null">
                ItemDesc = #{itemdesc},
            </if>
            <if test="modulecode != null">
                ModuleCode = #{modulecode},
            </if>
            <if test="workcomprate != null">
                WorkCompRate = #{workcomprate},
            </if>
            <if test="rownum != null">
                RowNum = #{rownum},
            </if>
            <if test="remark != null">
                Remark = #{remark},
            </if>
            <if test="custom1 != null">
                Custom1 = #{custom1},
            </if>
            <if test="custom2 != null">
                Custom2 = #{custom2},
            </if>
            <if test="custom3 != null">
                Custom3 = #{custom3},
            </if>
            <if test="custom4 != null">
                Custom4 = #{custom4},
            </if>
            <if test="custom5 != null">
                Custom5 = #{custom5},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_WorkLogItem
        where id = #{key}
    </delete>
</mapper>

