<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="inks.service.sa.pms.mapper.pmsSaUserMapper">
    <!--查询单个-->
    <select id="getEntity" resultType="inks.service.sa.pms.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        where Sa_User.id = #{key}
    </select>
    <sql id="selectSaUserVo">
        select Sa_User.id,
               Sa_User.WxOpenid,
               Sa_User.UserName,
               Sa_User.RealName,
               Sa_User.Password,
               Sa_User.Phone,
               Sa_User.Email,
               Sa_User.EmailAuthCode,
               Sa_User.DingUserid,
               Sa_User.GitLabToken,
               Sa_User.GitLabUserName,
               Sa_User.Sex,
               Sa_User.Avatar,
               Sa_User.DirName,
               Sa_User.FileName,
               Sa_User.UserState,
               Sa_User.RoleType,
               Sa_User.AdminMark,
               Sa_User.Remark,
               Sa_User.CreateBy,
               Sa_User.CreateDate,
               Sa_User.Lister,
               Sa_User.ModifyDate,
               Sa_User.Revision
        from Sa_User
    </sql>

    <!--查询指定行数据-->
    <select id="getPageList" parameterType="inks.common.core.domain.QueryParam"
            resultType="inks.service.sa.pms.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        where 1 = 1
        <if test="filterstr != null">
            ${filterstr}
        </if>
        <if test="DateRange != null">
            <if test="DateRange.DateColumn == null or DateRange.DateColumn == ''">
                and Sa_User.CreateDate BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
            <if test="DateRange.DateColumn != null and DateRange.DateColumn != ''">
                and ${DateRange.DateColumn} BETWEEN #{DateRange.StartDate} and #{DateRange.EndDate}
            </if>
        </if>
        <if test="SearchPojo != null">
            <if test="SearchType == 0">
                <include refid="and">
                </include>
            </if>
            <if test="SearchType == 1">
                <include refid="or">
                </include>
            </if>
        </if>
        order by ${orderBy}
        <if test="OrderType == 0">
            asc
        </if>
        <if test="OrderType == 1">
            desc
        </if>
    </select>
    <sql id="and">
        <if test="SearchPojo.username != null">
            and Sa_User.UserName like concat('%', #{SearchPojo.username}, '%')
        </if>
        <if test="SearchPojo.realname != null">
            and Sa_User.RealName like concat('%',
            #{SearchPojo.realname}, '%')
        </if>
        <if test="SearchPojo.phone != null">
            and Sa_User.Phone like concat('%',
            #{SearchPojo.phone}, '%')
        </if>
        <if test="SearchPojo.email != null">
            and Sa_User.Email like concat('%',
            #{SearchPojo.email}, '%')
        </if>
        <if test="SearchPojo.avatar != null">
            and Sa_User.Avatar like concat('%',
            #{SearchPojo.avatar}, '%')
        </if>
        <if test="SearchPojo.remark != null">
            and Sa_User.Remark like concat('%',
            #{SearchPojo.remark}, '%')
        </if>
        <if test="SearchPojo.createby != null">
            and Sa_User.CreateBy like concat('%',
            #{SearchPojo.createby}, '%')
        </if>
        <if test="SearchPojo.lister != null">
            and Sa_User.Lister like concat('%',
            #{SearchPojo.lister}, '%')
        </if>
    </sql>
    <sql id="or">
        <trim prefix=" AND (" suffix=")" prefixOverrides="AND |OR ">
            <if test="SearchPojo.username != null">
                or Sa_User.UserName like concat('%', #{SearchPojo.username}, '%')
            </if>
            <if test="SearchPojo.realname != null">
                or Sa_User.RealName like concat('%', #{SearchPojo.realname}, '%')
            </if>
            <if test="SearchPojo.phone != null">
                or Sa_User.Phone like concat('%', #{SearchPojo.phone}, '%')
            </if>
            <if test="SearchPojo.email != null">
                or Sa_User.Email like concat('%', #{SearchPojo.email}, '%')
            </if>
            <if test="SearchPojo.avatar != null">
                or Sa_User.Avatar like concat('%', #{SearchPojo.avatar}, '%')
            </if>
            <if test="SearchPojo.remark != null">
                or Sa_User.Remark like concat('%', #{SearchPojo.remark}, '%')
            </if>
            <if test="SearchPojo.createby != null">
                or Sa_User.CreateBy like concat('%', #{SearchPojo.createby}, '%')
            </if>
            <if test="SearchPojo.lister != null">
                or Sa_User.Lister like concat('%', #{SearchPojo.lister}, '%')
            </if>
        </trim>
    </sql>
    <!--新增所有列-->
    <insert id="insert">
        insert into Sa_User(id, WxOpenid, UserName, RealName, Password, Phone, Email, EmailAuthCode, DingUserid, GitLabToken, GitLabUserName, Sex, Avatar, DirName, FileName, RoleType, AdminMark, UserState, Remark, CreateBy, CreateDate, Lister, ModifyDate, Revision)
        values (#{id}, #{wxopenid}, #{username}, #{realname}, #{password}, #{phone}, #{email}, #{emailauthcode}, #{dinguserid}, #{gitlabtoken}, #{gitlabusername}, #{sex}, #{avatar}, #{dirname}, #{filename}, #{roletype}, #{adminmark}, #{userstate}, #{remark}, #{createby}, #{createdate}, #{lister}, #{modifydate}, #{revision})
    </insert>

    <!--通过主键修改数据-->
    <update id="update">
        update Sa_User
        <set>
            <if test="wxopenid != null">
                WxOpenid = #{wxopenid},
            </if>
            <if test="username != null">
                UserName =#{username},
            </if>
            <if test="realname != null">
                RealName =#{realname},
            </if>
            <if test="password != null">
                Password =#{password},
            </if>
            <if test="phone != null">
                Phone =#{phone},
            </if>
            <if test="email != null">
                Email =#{email},
            </if>
            <if test="emailauthcode != null">
                EmailAuthCode =#{emailauthcode},
            </if>
            <if test="dinguserid != null">
                DingUserid =#{dinguserid},
            </if>
            <if test="gitlabtoken != null ">
                GitLabToken =#{gitlabtoken},
            </if>
            <if test="gitlabusername != null ">
                GitLabUserName =#{gitlabusername},
            </if>
            <if test="sex != null">
                Sex =#{sex},
            </if>
            <if test="avatar != null">
                Avatar =#{avatar},
            </if>
            <if test="userstate != null">
                UserState =#{userstate},
            </if>
            <if test="roletype != null">
                RoleType =#{roletype},
            </if>

            <if test="adminmark != null">
                AdminMark =#{adminmark},
            </if>

            <if test="remark != null">
                Remark =#{remark},
            </if>
            <if test="createby != null">
                CreateBy =#{createby},
            </if>
            <if test="createdate != null">
                CreateDate =#{createdate},
            </if>
            <if test="lister != null">
                Lister =#{lister},
            </if>
            <if test="modifydate != null">
                ModifyDate =#{modifydate},
            </if>
            <if test="dirname != null">
                DirName =#{dirname},
            </if>
            <if test="filename != null">
                FileName =#{filename},
            </if>
            Revision=Revision + 1
        </set>
        where id = #{id}
    </update>

    <!--通过主键删除-->
    <delete id="delete">
        delete
        from Sa_User
        where id = #{key}
    </delete>

    <select id="getEntityByOpenid" resultType="inks.service.sa.pms.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE Sa_User.WxOpenid = #{openid}
    </select>

    <select id="getUserInfo" resultType="inks.service.sa.pms.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE Sa_User.id = #{id}
    </select>

    <select id="getEntityByUNameAndPass" resultType="inks.service.sa.pms.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE Sa_User.Username = #{username}
        and Password = #{password}
        ORDER BY CreateDate DESC limit 1
    </select>

    <select id="checkPasswordByUserid" resultType="inks.service.sa.pms.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE Sa_User.id = #{userid}
        and Password = #{password}
        ORDER BY CreateDate DESC
        limit 1
    </select>

    <select id="getAdminMarkByUserid" resultType="int">
        select AdminMark
        from Sa_User
        where id = #{userid}
    </select>

    <select id="checkUseridUsed" resultType="java.lang.String">
        (SELECT 'Sa_Engineer' as usedName From Sa_Engineer where Userid = #{userid} LIMIT 1)
        UNION ALL
        (SELECT 'Sa_ScmUser' as usedName From Sa_ScmUser where Userid = #{userid} LIMIT 1)
        UNION ALL
        (SELECT 'Sa_TodoUser' as usedName From Sa_TodoUser where Userid = #{userid} LIMIT 1)
        UNION ALL
        (SELECT 'Sa_UserGroup' as usedName From Sa_UserGroup where Userid = #{userid} LIMIT 1)
    </select>

    <select id="getDingUserid" resultType="java.lang.String">
        select DingUserid
        from Sa_User
        where id = #{userid}
    </select>

    <select id="getEntityByRealName" resultType="inks.service.sa.pms.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
        WHERE Sa_User.RealName = #{realname}
    </select>

    <update id="updateAdminPassword">
        update Sa_User
        set Password = #{encryptPassword}
        where UserName = 'admin'
    </update>

    <select id="getAllList" resultType="inks.service.sa.pms.domain.pojo.SaUserPojo">
        <include refid="selectSaUserVo"/>
    </select>
</mapper>