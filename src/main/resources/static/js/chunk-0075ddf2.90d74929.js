(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0075ddf2"],{"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,i,o){return t/=o/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var o=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function a(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,i){var s=n(),l=t-s,r=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=r;var t=Math.easeInOutQuad(c,s,l,e);a(t),c<e?o(d):i&&"function"===typeof i&&i()};d()}},"2b8cb":function(t,e,i){},3340:function(t,e,i){"use strict";i.r(e);var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.FormVisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):i("div",{ref:"index",staticClass:"index"},[i("div",{staticClass:"page-container"},[i("el-row",{attrs:{type:"flex"}},[i("el-col",{staticStyle:{padding:"4px 0px"},style:"height:"+t.tableMaxHeight,attrs:{span:16}},[i("div",[i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px",float:"right"},attrs:{label:"我的",size:"mini",border:""},on:{change:t.changeMyself},model:{value:t.myself,callback:function(e){t.myself=e},expression:"myself"}}),i("h3",{staticStyle:{margin:"10px","line-height":"32px",color:"#606266","text-align":"center"}},[t._v(" 工作日志 ")])],1),i("div",{style:"height:calc("+t.tableMaxHeight+" - 80px)"},[i("ul",{staticClass:"workLog_ul"},t._l(t.lst,(function(e,o){return i("li",{key:o,staticClass:"workLog_li",on:{click:function(i){return t.showWork(e)}}},[i("div",{staticClass:"brand"},[t._v(t._s(e.lister.slice(0,1)||"NA"))]),i("div",{staticClass:"li_center"},[i("div",[t._v(" "+t._s(e.lister)+"的"+t._s(e.worktype?"工作周报":"工作日志")+" "),e.sendemailnum?i("i",{staticClass:"el-icon-s-promotion",staticStyle:{color:"#409eff"}}):t._e()]),i("div",{staticClass:"worktodayText",attrs:{title:e.worktodayText}},[t._v(" "+t._s(e.worktodayText)+" ")]),i("div",{staticClass:"date"},[t._v(t._s(t._f("dateFormat")(e.workdate)))])]),i("i",{staticClass:"li_right el-icon-arrow-right"})])})),0),i("div",{staticStyle:{"margin-top":"10px"}},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)])]),i("el-col",{style:"display: flex;flex-direction: column;padding: 0px 10px 0 10px;border-left: 1px solid #dcdfe6;",attrs:{span:8}},[i("div",{staticClass:"formdataInfo"},[i("el-button",{staticStyle:{"margin-bottom":"-12px","margin-top":"12px"},attrs:{type:"primary",size:"small",icon:"el-icon-edit"},on:{click:function(e){return t.showform(0)}}},[t._v("写日志")])],1),i("el-divider",{attrs:{"content-position":"left"}}),i("div",{staticClass:"workcontent"},[t.selRow.id?i("div",{staticStyle:{height:"81%"}},[i("div",{staticClass:"header"},[i("div",[i("div",{staticStyle:{display:"inline-block"}},[t._v(t._s(t.selRow.title))]),t.selRow.sendemailnum?i("div",{staticClass:"isSend"},[i("i",{staticClass:"el-icon-s-promotion"}),i("span",[t._v("已发送")])]):t._e()]),i("div",{staticClass:"right"},[i("span",{staticClass:"date"},[t._v(t._s(t._f("dateFormat")(t.selRow.workdate)))]),i("span",{staticClass:"detail",on:{click:function(e){return t.showform(t.selRow.id)}}},[t._v("详细内容")])])]),i("div",{staticStyle:{"overflow-y":"auto",height:"calc(100% )"}},[i("h3",[t._v(t._s(t.selRow.worktype?"上周工作记录":"今日工作记录"))]),i("div",{domProps:{innerHTML:t._s(t.selRow.worktoday.replace(/\n/gm,"<br>"))}}),i("h3",[t._v(t._s(t.selRow.worktype?"本周计划":"明日工作计划"))]),i("div",{domProps:{innerHTML:t._s(t.selRow.worktomorrow.replace(/\n/gm,"<br>"))}})])]):i("div",{staticClass:"noData"},[t._v("暂无日志信息")])])],1)],1)],1)])])},a=[],n=(i("e9c4"),i("ac1f"),i("5319"),i("b775")),s=i("333d"),l=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),i("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[i("el-button",{attrs:{size:"small"}},[t._v("操作"),i("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.idx},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")]),i("el-dropdown-item",{attrs:{icon:"el-icon-folder",disabled:!t.idx},nativeOn:{click:function(e){t.emailVisible=!0}}},[t._v("发送邮件")])],1)],1),i("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),i("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("div",{staticClass:"flex j-end p-a",staticStyle:{top:"55px",right:"20px"}},[i("div",{staticClass:"refNo flex j-end"},[i("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[i("div",{staticStyle:{"margin-right":"10px",width:"100px"}},[t.formdata.sendemailnum?i("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"finish1"}}):t._e()],1)])])]),i("div",{ref:"form_main_info",staticClass:"form form-head p-r",staticStyle:{height:"96%"}},[i("el-form",{ref:"formdata",staticStyle:{height:"99%"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"日志标题"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入日志标题"},model:{value:t.formdata.title,callback:function(e){t.$set(t.formdata,"title",e)},expression:"formdata.title"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"日期"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",clearable:"",placeholder:"时间"},model:{value:t.formdata.workdate,callback:function(e){t.$set(t.formdata,"workdate",e)},expression:"formdata.workdate"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"天气"}},[i("el-select",{attrs:{placeholder:"请选择"},model:{value:t.formdata.weather,callback:function(e){t.$set(t.formdata,"weather",e)},expression:"formdata.weather"}},[i("el-option",{attrs:{label:"晴",value:"晴"}}),i("el-option",{attrs:{label:"多云",value:"多云"}}),i("el-option",{attrs:{label:"阴",value:"阴"}}),i("el-option",{attrs:{label:"雨",value:"雨"}}),i("el-option",{attrs:{label:"雪",value:"雪"}})],1)],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:10}},[i("el-form-item",{staticStyle:{"margin-bottom":"12px"},attrs:{label:"备注","label-position":"right","label-width":"100px"}},[i("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",clearable:""},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1),i("el-form-item",{staticStyle:{"margin-bottom":"12px"},attrs:{label:"类型"}},[i("el-radio-group",{on:{change:t.changeType},model:{value:t.formdata.worktype,callback:function(e){t.$set(t.formdata,"worktype",e)},expression:"formdata.worktype"}},[i("el-radio",{attrs:{label:0}},[t._v("日志")]),i("el-radio",{attrs:{label:1}},[t._v("周报")])],1)],1)],1),i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px",height:"calc(100% - 160px)"}},[i("el-divider",{attrs:{"content-position":"left"}},[t._v(" "+t._s(t.formdata.worktype?"上周工作记录":"今日工作记录"))]),i("EditItem",{ref:"elitem",style:{width:"99%",height:"48%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData,downloadWorkLog:t.downloadWorkLog,downloadGitLab:t.downloadGitLab}}),i("el-divider",{attrs:{"content-position":"left"}},[t._v(" "+t._s(t.formdata.worktype?"本周计划":"明日计划"))]),i("Tomorrow",{ref:"tomorrow",staticStyle:{width:"99%",height:"48%","margin-top":"10px"},attrs:{lstitem:t.formdata.plan,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData,downloadWorkLog:t.downloadWorkLog}})],1)],1)],1)])]),t.emailVisible?i("el-dialog",{attrs:{width:"500px",title:"邮件发送","append-to-body":!0,visible:t.emailVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.emailVisible=e}}},[i("el-form",{ref:"emailform",attrs:{model:t.emailform,"label-width":"80px"}},[i("el-form-item",{attrs:{label:"收件人"}},[i("el-popover",{ref:"dictionaryRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}}},[i("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"sa_workLog.addressee"},on:{singleSel:function(e){t.emailform.toemail=e.dictvalue,t.$refs.dictionaryRef.doClose()},closedic:function(e){return t.$refs.dictionaryRef.doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"收件人",clearable:""},model:{value:t.emailform.toemail,callback:function(e){t.$set(t.emailform,"toemail",e)},expression:"emailform.toemail"}})],1)],1)],1),i("el-form-item",{attrs:{label:"抄送"}},[i("el-popover",{ref:"dictionaryotherRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selotherRef.bindData()}}},[i("selDictionaries",{ref:"selotherRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"sa_workLog.addressee"},on:{singleSel:function(e){t.emailform.otheremails+=e.dictvalue+";",t.$refs.dictionaryotherRef.doClose()},closedic:function(e){return t.$refs.dictionaryotherRef.doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"抄送人",clearable:""},model:{value:t.emailform.otheremails,callback:function(e){t.$set(t.emailform,"otheremails",e)},expression:"emailform.otheremails"}})],1)],1)],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.EemailSave()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.emailVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),i("el-dialog",{attrs:{title:"Todo",visible:t.TodoVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0,width:"60vw"},on:{"update:visible":function(e){t.TodoVisible=e}}},[i("div",[i("selTodo",{ref:"selTodo",attrs:{multi:1,selecturl:"/S06M09B1/getOnlinePageList?type=a"}})],1),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.chooseWorkLog()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.TodoVisible=!1}}},[t._v("取 消")])],1)])],1)},r=[];i("99af"),i("b64b"),i("d3b7"),i("25f0"),i("4d90");const c={add(t){return new Promise((e,i)=>{var o=JSON.stringify(t);n["a"].post("/S06M10B1/create",o).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var o=JSON.stringify(t);n["a"].post("/S06M10B1/update",o).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){return new Promise((e,i)=>{n["a"].get("/S06M10B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})}};var d=c,m=i("1975"),u=i("b893"),f=i("5c73"),p=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selPwProcess",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"400px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"55"}}):i("el-table-column",{attrs:{label:"",width:"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"标题",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtitle))])]}}])}),i("el-table-column",{attrs:{label:"类型",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.billtype))])]}}])}),i("el-table-column",{attrs:{label:"计划完成",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormat")(e.row.endplan)))])]}}])})],1)],1),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},h=[],g=i("ade3"),w={components:{Pagination:s["a"]},props:["multi","selecturl"],data:function(){return Object(g["a"])(Object(g["a"])({title:"Todo",listLoading:!0,lst:[],searchstr:" ",strfilter:"",radio:"",selrows:"",total:0},"searchstr",""),"queryParams",{PageNum:1,PageSize:10,OrderType:1,SearchType:1})},watch:{},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0;var e="/S06M09B1/getOnlinePageList";this.selecturl&&(e=this.selecturl),n["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={engineercode:t,engineername:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},v=w,b=(i("d038"),i("2877")),y=Object(b["a"])(v,p,h,!1,null,"32ec1206",null),x=y.exports,k=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c",staticStyle:{height:"100%"}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("el-row",[i("el-button-group",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(e){return t.getAdd(1)}}},[t._v(" 添 加")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini",icon:"el-icon-top"},nativeOn:{click:function(e){return t.getMoveUp()}}},[t._v(" 上 移")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini",icon:"el-icon-bottom"},nativeOn:{click:function(e){return t.getMoveDown()}}},[t._v(" 下 移")]),i("el-button",{attrs:{disabled:0==t.multipleSelection.length,type:"danger",size:"mini",icon:"el-icon-delete"},nativeOn:{click:function(e){return t.delItem()}}},[t._v("删 除")])],1)],1),i("div",[i("el-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary",size:"mini",icon:"el-icon-download"},on:{click:function(e){return t.$emit("downloadGitLab")}}},[t._v("GitLab导入")]),i("el-button",{staticStyle:{"margin-right":"10px"},attrs:{type:"primary",size:"mini",icon:"el-icon-download"},on:{click:function(e){return t.$emit("downloadWorkLog")}}},[t._v("导入")])],1)],1),i("div",{staticClass:"table-container f-1 table-position"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{data:t.lst,"element-loading-text":"Loading",height:t.tableHeight,border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[i("el-table-column",{attrs:{type:"selection",width:"50px",align:"center",fixed:"left"}}),i("el-table-column",{attrs:{align:"center",label:"ID",width:"50px"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e){return[!e.displaymark?t._e():i("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(o){return["plandate"==e.itemcode?i("div",[o.row.isEdit?i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",size:"default"},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}}):i("span",[t._v(t._s(t._f("dateFormat")(o.row[e.itemcode])))])],1):"itemtype"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",size:"default"},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}},t._l(t.itemtypeList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.value,value:e.value}},[i("span",{staticStyle:{float:"left"}},[t._v(t._s(e.value))]),i("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px","margin-left":"10px"}},[t._v(t._s(e.desc))])])})),1)],1):i("span",[t._v(t._s(o.row[e.itemcode]))])]):"projectid"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("autoComplete",{attrs:{size:"default",value:o.row.projname,baseurl:"/S06M01S1/getBillList?own=true",params:{name:"projname",other:""}},on:{setRow:function(e){return t.setRow(e,o.row)},autoClear:function(e){return t.autoClear(o.row)}}})],1):i("span",[t._v(t._s(o.row.projname))])]):"itemdesc"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("el-input",{attrs:{size:"default",placeholder:e.itemname,type:"textarea",autosize:{minRows:2,maxRows:4}},on:{focus:function(t){return t.currentTarget.select()}},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(o.row[e.itemcode]))])]):"worktime"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:.5,max:8,step:.5,precision:1,label:"请输入行号"},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(o.row[e.itemcode]))])]):"workcomprate"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("el-slider",{attrs:{step:10,"show-stops":""},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(o.row[e.itemcode]))])]):i("div",[o.row.isEdit?i("div",[i("el-input",{attrs:{size:"default",placeholder:e.itemname},on:{focus:function(t){return t.currentTarget.select()}},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(o.row[e.itemcode]))])])]}}],null,!0)})]}))],2)],1)])},S=[],_=(i("a434"),i("0643"),i("4e3e"),i("159b"),{formcode:"S06M10B1Item",item:[{itemcode:"itemtype",itemname:"类型",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"projectid",itemname:"项目",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"modulecode",itemname:"功能编码",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"itemdesc",itemname:"任务日志",minwidth:"150",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"worktime",itemname:"工时(H)",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"workcomprate",itemname:"完成比例",minwidth:"60",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"}]}),$={formcode:"S06M10B1Plan",item:[{itemcode:"itemtype",itemname:"类型",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"projectid",itemname:"项目",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"modulecode",itemname:"功能编码",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"itemdesc",itemname:"任务日志",minwidth:"150",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"worktimeexpect",itemname:"预估工时(H)",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"workcomprate",itemname:"完成比例",minwidth:"60",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"}]},C={id:"",pid:"",itemtype:"开发",projectid:"",worktime:.5,itemdesc:"",modulecode:"",workcomprate:0,rownum:"",remark:"",custom1:"",custom2:"",custom3:"",custom4:"",custom5:""},T={id:"",pid:"",itemtype:"开发",projectid:"",worktimeexpect:.5,itemdesc:"",modulecode:"",workcomprate:0,rownum:"",remark:"",custom1:"",custom2:"",custom3:"",custom4:"",custom5:""},P=i("5b24"),j={name:"Elitem",components:{autoComplete:P["a"]},props:["formdata","lstitem","idx"],data:function(){return{listLoading:!1,lst:[],multi:0,tableHeight:200,multipleSelection:[],isEditOk:!0,selVisible:!1,tableForm:_,itemtypeList:[{value:"开发",desc:"合同开发"},{value:"测试",desc:"测试软件"},{value:"手册",desc:"文档类工作"},{value:"维护",desc:"对接客户"},{value:"研发",desc:"自主研发"}]}},watch:{lstitem:function(t,e){this.lst=this.lstitem},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].isEdit=!1}},created:function(){this.lst=[]},mounted:function(){this.catchHight()},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},methods:{deleteRows:function(t,e){var i=this,o=this.multipleSelection;o&&o.forEach((function(t,e){i.lst.forEach((function(e,o){t.goodsid===e.goodsid&&t.rownum===e.rownum&&i.lst.splice(o,1)}))})),this.$refs.multipleTable.clearSelection()},getAdd:function(t){var e=Object.assign({},C);0!=this.idx&&(e.pid=this.idx),this.lst.push(e)},handleSelectionChange:function(t){this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32,console.log("this.tableHeight",t.tableHeight))}))},setRow:function(t,e){console.log(t),e.projectid=t.id,e.projname=t.projname},autoClear:function(t){t.projectid="",t.projname=""},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},O=j,D=(i("60ca"),Object(b["a"])(O,k,S,!1,null,"260a666f",null)),L=D.exports,I=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c",staticStyle:{height:"100%"}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("el-row",[i("el-button-group",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(e){return t.getAdd(1)}}},[t._v(" 添 加")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini",icon:"el-icon-top"},nativeOn:{click:function(e){return t.getMoveUp()}}},[t._v(" 上 移")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini",icon:"el-icon-bottom"},nativeOn:{click:function(e){return t.getMoveDown()}}},[t._v(" 下 移")]),i("el-button",{attrs:{disabled:0==t.multipleSelection.length,type:"danger",size:"mini",icon:"el-icon-delete"},nativeOn:{click:function(e){return t.delItem()}}},[t._v("删 除")])],1)],1)],1),i("div",{staticClass:"table-container f-1 table-position"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{data:t.lst,"element-loading-text":"Loading",height:t.tableHeight,border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[i("el-table-column",{attrs:{type:"selection",width:"50px",align:"center",fixed:"left"}}),i("el-table-column",{attrs:{align:"center",label:"ID",width:"50px"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e){return[!e.displaymark?t._e():i("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(o){return["plandate"==e.itemcode?i("div",[o.row.isEdit?i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",size:"default"},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}}):i("span",[t._v(t._s(t._f("dateFormat")(o.row[e.itemcode])))])],1):"itemtype"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",size:"default"},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}},t._l(t.itemtypeList,(function(e){return i("el-option",{key:e.value,attrs:{label:e.value,value:e.value}},[i("span",{staticStyle:{float:"left"}},[t._v(t._s(e.value))]),i("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px","margin-left":"10px"}},[t._v(t._s(e.desc))])])})),1)],1):i("span",[t._v(t._s(o.row[e.itemcode]))])]):"projectid"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("autoComplete",{attrs:{size:"default",value:o.row.projname,baseurl:"/S06M01S1/getBillList?own=true",params:{name:"projname",other:""}},on:{setRow:function(e){return t.setRow(e,o.row)},autoClear:function(e){return t.autoClear(o.row)}}})],1):i("span",[t._v(t._s(o.row.projname))])]):"itemdesc"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("el-input",{attrs:{size:"default",placeholder:e.itemname,type:"textarea",autosize:{minRows:2,maxRows:4}},on:{focus:function(t){return t.currentTarget.select()}},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(o.row[e.itemcode]))])]):"worktimeexpect"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("el-input-number",{staticStyle:{width:"100%"},attrs:{min:.5,max:8,step:.5,precision:1,label:"请输入行号"},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(o.row[e.itemcode]))])]):"workcomprate"==e.itemcode?i("div",[o.row.isEdit?i("div",[i("el-slider",{attrs:{step:10,"show-stops":""},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(o.row[e.itemcode]))])]):i("div",[o.row.isEdit?i("div",[i("el-input",{attrs:{size:"default",placeholder:e.itemname},on:{focus:function(t){return t.currentTarget.select()}},model:{value:o.row[e.itemcode],callback:function(i){t.$set(o.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(o.row[e.itemcode]))])])]}}],null,!0)})]}))],2)],1)])},R=[],z={name:"Elitem",components:{autoComplete:P["a"]},props:["formdata","lstitem","idx"],data:function(){return{listLoading:!1,lst:[],multi:0,tableHeight:200,multipleSelection:[],isEditOk:!0,selVisible:!1,tableForm:$,itemtypeList:[{value:"开发",desc:"合同开发"},{value:"测试",desc:"测试软件"},{value:"手册",desc:"文档类工作"},{value:"维护",desc:"对接客户"},{value:"研发",desc:"自主研发"}]}},watch:{lstitem:function(t,e){this.lst=this.lstitem},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].isEdit=!1}},created:function(){this.lst=[]},mounted:function(){this.catchHight()},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},methods:{deleteRows:function(t,e){var i=this,o=this.multipleSelection;o&&o.forEach((function(t,e){i.lst.forEach((function(e,o){t.goodsid===e.goodsid&&t.rownum===e.rownum&&i.lst.splice(o,1)}))})),this.$refs.multipleTable.clearSelection()},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-100)}))},getAdd:function(t){var e=Object.assign({},T);0!=this.idx&&(e.pid=this.idx),this.lst.push(e)},handleSelectionChange:function(t){this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},setRow:function(t,e){e.projectid=t.id,e.projname=t.projname},autoClear:function(t){t.projectid="",t.projname=""},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},E=z,B=(i("f60b"),Object(b["a"])(E,I,R,!1,null,"17e1e9c6",null)),M=B.exports,F=(i("b0b8"),{name:"Formedit",components:{MyEditor:m["a"],selDictionaries:f["a"],selTodo:x,EditItem:L,Tomorrow:M},props:["idx"],data:function(){return{title:"工作日志",formdata:{title:"工作日志-"+JSON.parse(window.localStorage.getItem("getInfo")).realname+"-"+this.dateFormats(),workdate:new Date,weather:"晴",worktoday:"",worktomorrow:"",remark:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[],plan:[],worktype:0},formRules:{title:[{required:!0,trigger:"blur",message:"标题不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px",emailVisible:!1,emailform:{toemail:"<EMAIL>",otheremails:""},userList:[],TodoVisible:!1}},computed:{formcontainHeight:function(){return window.innerHeight-113+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData(),this.getUserList()},methods:{bindData:function(){var t=this;0!=this.idx&&n["a"].get("/S06M10B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},getUserList:function(){var t=this,e={PageNum:1,PageSize:20,OrderType:0,SearchType:1};n["a"].post("/S06M09B1User/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.userList=e.data.data.list,t.$forceUpdate())}))},changeType:function(t){this.formdata.title=t?"工作周报-"+JSON.parse(window.localStorage.getItem("getInfo")).realname+"-"+this.dateFormats():"工作日志-"+JSON.parse(window.localStorage.getItem("getInfo")).realname+"-"+this.dateFormats()},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){for(var t=this,e=this.$refs.elitem.lst,i=this.$refs.tomorrow.lst,o="<ol>",a="<ol>",n=0;n<e.length;n++){var s=e[n];s.itemdesc&&(o+='<li style="line-height: 2;">'+s.itemdesc.replace(/\n/gm,"<br>")+"("+(s.worktime?s.worktime:.5)+"H)"+(100==s.workcomprate?"——完成":"")+"</li>")}for(n=0;n<i.length;n++){s=i[n];s.itemdesc&&(a+='<li style="line-height: 2;">'+s.itemdesc.replace(/\n/gm,"<br>")+(100==s.workcomprate?"——完成":"")+"</li>")}o+="</ol>",a+="</ol>",this.formdata.worktoday=o,this.formdata.worktomorrow=a,console.log(this.formdata),this.formdata.item=this.$refs.elitem.lst,this.formdata.plan=this.$refs.tomorrow.lst;var l=Object.assign({},this.formdata);0==this.idx?d.add(l).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")})):d.update(l).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")}))},EemailSave:function(){var t=this,e={key:this.formdata.id,toemail:this.emailform.toemail,otheremails:this.emailform.otheremails};n["a"].post("/S06M10B1/sendWorkEmail",JSON.stringify(e)).then((function(e){200==e.data.code?(console.log("res",e),t.$message.success("邮件发送成功")):t.$message.warning(e.data.msg||"邮件发送失败")})).catch((function(t){console.log(t||"请求错误")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("closeForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},downloadWorkLog:function(){var t=this;this.idx?this.$confirm("该日志已保存, 是否重新导入任务?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.initBindData()})).catch((function(){})):this.initBindData()},initBindData:function(){var t=this,e="/S06M02B1/getDemandCalendar";e+="?appointeengineerid='".concat(this.$store.state.user.userinfo.engineer.id,"'");var i={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};0==this.formdata.worktype?i.DateRange={StartDate:Object(u["b"])(new Date),EndDate:Object(u["a"])(new Date((new Date).getTime()+864e5))}:i.DateRange={StartDate:Object(u["b"])(Object(u["d"])()[0]),EndDate:Object(u["a"])(Object(u["h"])()[1])},this.$request.post(e,JSON.stringify(i)).then((function(e){if(console.log("222",e),200==e.data.code){var i=e.data.data;t.$refs.elitem.lst=[],t.$refs.tomorrow.lst=[];for(var o=0;o<i.length;o++){var a=i[o];if(0==t.formdata.worktype)if(Object(u["b"])(a.deaddate)==Object(u["b"])(new Date)){var n=Object.assign({},C);n.projectid=a.projectid,n.projname=a.projname,n.itemdesc=a.billtitle,n.worktime=a.worktime,n.workcomprate="已完成"==a.statustype?100:0,0!=t.idx&&(n.pid=t.idx),t.$refs.elitem.lst.push(n)}else{n=Object.assign({},T);n.projectid=a.projectid,n.projname=a.projname,n.itemdesc=a.billtitle,n.worktime=a.worktime,0!=t.idx&&(n.pid=t.idx),t.$refs.tomorrow.lst.push(n)}else{var s=new Date(Object(u["b"])(a.deaddate)).getTime(),l=new Date(Object(u["b"])(Object(u["h"])()[0])).getTime();if(s>l){n=Object.assign({},T);n.projectid=a.projectid,n.projname=a.projname,n.itemdesc=a.billtitle,n.worktime=a.worktime,0!=t.idx&&(n.pid=t.idx),t.$refs.tomorrow.lst.push(n)}else{n=Object.assign({},C);n.projectid=a.projectid,n.projname=a.projname,n.itemdesc=a.billtitle,n.worktime=a.worktime,n.workcomprate="已完成"==a.statustype?100:0,0!=t.idx&&(n.pid=t.idx),t.$refs.elitem.lst.push(n)}}}}else t.$message.warning(e.data.msg||"获取需求任务失败")}))},chooseWorkLog:function(){var t=this.$refs.selTodo.$refs.selPwProcess.selection;if(0!=t.length){for(var e="<ol>",i=0;i<t.length;i++){var o=t[i];e+='<li style="line-height: 2;">'+o.billtitle+"</li>"}e+="</ol>",this.formdata.worktomorrow+=e,this.TodoVisible=!1}else this.$message.warning("请选择Todo内容")},downloadGitLab:function(){var t=this;this.idx?this.$confirm("该日志已保存, 是否确认导入?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.initGitLab()})).catch((function(){})):this.initGitLab()},initGitLab:function(){var t=this,e="/GitLab/syncDemandFromGitLabByLogin";this.$request.get(e).then((function(e){if(200==e.data.code){var i=e.data.data;if(i)for(var o=JSON.parse(i.split("GitLab API:")[1]),a=0;a<o.length;a++){var n=o[a],s=Object.assign({},C);s.itemdesc=n.commit_title,0!=t.idx&&(s.pid=t.idx),t.$refs.elitem.lst.push(s)}}else t.$message.warning(e.data.msg||"导入失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},changeHtml:function(t,e){this.formdata[e]=t},dateFormats:function(){var t=new Date,e=t.getFullYear(),i=(t.getMonth()+1).toString().padStart(2,"0"),o=t.getDate().toString().padStart(2,"0");return"".concat(e).concat(i).concat(o)}}}),N=F,q=(i("ed90"),Object(b["a"])(N,l,r,!1,null,"183816e8",null)),A=q.exports,V={name:"S06M10B1",components:{Pagination:s["a"],formedit:A},data:function(){return{idx:0,lst:[],selRow:{},FormVisible:!1,total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},strfilter:"",myself:!0}},computed:{tableMaxHeight:function(){return window.innerHeight-100+"px"}},watch:{},created:function(){this.bindData()},methods:{bindData:function(){var t=this;if(this.myself)var e="/S06M10B1/getPageTh?type=a";else e="/S06M10B1/getPageTh";n["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list;for(var i=0;i<t.lst.length;i++){var o=t.lst[i];o.worktodayText=o.worktoday.replace(/<[^<>]+>/g,"")}t.total=e.data.data.total}})).catch((function(t){console.log(t.response)}))},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeMyself:function(t){this.myself=t,this.bindData()},showform:function(t){this.idx=t,this.FormVisible=!0},removeHTMLTag:function(t){return t=t.replace(/<[^<>]+>/g,""),t=t.replace(/[ | ]\n/g,"\n"),t=t.replace(/ /gi,""),t},escape2Html:function(t){var e={lt:"<",gt:">",nbsp:" ",amp:"&",quot:'"'};return t.replace(/&(lt|gt|nbsp|amp|quot);/gi,(function(t,i){return e[i]}))},showWork:function(t){this.selRow=t,console.log(this.selRow,"this.selRow"),this.$forceUpdate()},closeForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(t){this.idx=t},search:function(t){""!=t?this.queryParams.SearchPojo={worktoday:t,worktomorrow:t,title:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},H=V,U=(i("d9fc8"),Object(b["a"])(H,o,a,!1,null,"5803d8a0",null));e["default"]=U.exports},"4cc9":function(t,e,i){},"53ae":function(t,e,i){},"5c73":function(t,e,i){"use strict";var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,o){return i("li",{key:o,class:t.radio==o?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=o}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,o){return i("p",{key:o,class:t.ActiveIndex==o?"isActive":"",on:{click:function(e){t.ActiveIndex=o}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},a=[],n=(i("a434"),i("e9c4"),i("b775")),s=i("333d"),l=i("b0b8"),r={components:{Pagination:s["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],n["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){l.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:l.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,l.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=l.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,n["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=r,d=(i("af2b"),i("2877")),m=Object(d["a"])(c,o,a,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"60ca":function(t,e,i){"use strict";i("e663")},"7de4":function(t,e,i){},a770:function(t,e,i){},af2b:function(t,e,i){"use strict";i("7de4")},d038:function(t,e,i){"use strict";i("2b8cb")},d9fc8:function(t,e,i){"use strict";i("4cc9")},e663:function(t,e,i){},ed90:function(t,e,i){"use strict";i("53ae")},f60b:function(t,e,i){"use strict";i("a770")}}]);