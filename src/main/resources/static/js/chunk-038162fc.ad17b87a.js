(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-038162fc"],{"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,i,a){return t/=a/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,i){var s=n(),r=t-s,l=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=l;var t=Math.easeInOutQuad(c,s,r,e);o(t),c<e?a(d):i&&"function"===typeof i&&i()};d()}},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"15b6":function(t,e,i){},"34d5":function(t,e,i){},"42c5":function(t,e,i){"use strict";i("f674")},"5cc6":function(t,e,i){var a=i("74e8");a("Uint8",(function(t){return function(e,i,a){return t(this,e,i,a)}}))},7471:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.FormVisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[i("listheader",{attrs:{tableForm:t.tableForm},on:{btnadd:function(e){return t.showform(0)},btnsearch:t.search,bindData:t.bindData,AdvancedSearch:t.AdvancedSearch,btnExport:t.btnExport}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:t.showHelp?20:24}},[i("tableList",{ref:"tableList",on:{changeidx:t.changeidx,showform:t.showform,sendTableForm:t.sendTableForm}})],1),i("el-col",{attrs:{span:t.showHelp?4:0}},[i("helpmodel",{ref:"helpmodel",attrs:{code:"S06M19S1"}})],1)],1)],1)],1)])},o=[],n=(i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),i("div",{staticClass:"iShowBtn"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),i("el-collapse-transition",[i("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据编码"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入单据编码",size:"small"},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"单据标题"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入单据标题",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"客户"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入客户",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"联系人"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入联系人",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1),i("el-col",{attrs:{span:4}},[i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.AdvancedSearch()}}},[t._v(" 搜索 ")])],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"服务需求"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入服务需求",size:"small"},model:{value:t.formdata.sercontent,callback:function(e){t.$set(t.formdata,"sercontent",e)},expression:"formdata.sercontent"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"服务过程"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入服务过程",size:"small"},model:{value:t.formdata.serprocess,callback:function(e){t.$set(t.formdata,"serprocess",e)},expression:"formdata.serprocess"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"未尽事宜"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入未尽事宜",size:"small"},model:{value:t.formdata.serloss,callback:function(e){t.$set(t.formdata,"serloss",e)},expression:"formdata.serloss"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"简述"}},[i("el-input",{attrs:{clearable:"",placeholder:"请输入简述",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1)],1)],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.setColumsVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)}),s=[],r=i("8daf"),l={name:"Listheader",props:["tableForm"],components:{Setcolums:r["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},thorList:!0,setColumsVisible:!1,code:"S06M19S1List"}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList)},btnExport:function(){this.$emit("btnExport")}}},c=l,d=(i("42c5"),i("2877")),m=Object(d["a"])(c,n,s,!1,null,"290d3211",null),u=m.exports,f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!t.formdata.assessor},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),i("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),i("el-dropdown",{staticStyle:{"margin-left":"10px"},attrs:{trigger:"click",placement:"bottom"}},[i("el-button",{attrs:{size:"small"}},[t._v("操作"),i("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id||!!t.formdata.assessor},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),i("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[i("el-button",{attrs:{size:"small"}},[t._v("过程"),i("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"})],1),i("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),i("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[i("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"min-height":"110px"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",{attrs:{gutter:10}},[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("name")}}},[i("el-form-item",{attrs:{label:"名称",prop:"name"}},[i("el-input",{attrs:{placeholder:"请输入名称",clearable:"",size:"small"},model:{value:t.formdata.name,callback:function(e){t.$set(t.formdata,"name",e)},expression:"formdata.name"}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("code")}}},[i("el-form-item",{attrs:{label:"编码",prop:"code"}},[i("el-input",{attrs:{placeholder:"请输入编码",clearable:"",size:"small"},model:{value:t.formdata.code,callback:function(e){t.$set(t.formdata,"code",e)},expression:"formdata.code"}})],1)],1)])],1)],1)],1),i("div",{staticClass:"form-body form f-1"},[i("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.itemjson,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData}})],1),i("el-divider"),i("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[i("el-row",{staticStyle:{"margin-top":"10px","margin-right":"20px"}},[i("el-col",{attrs:{span:18}},[i("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[i("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:"经办人"}},[i("el-input",{attrs:{placeholder:"请输入经办人姓名",clearable:"",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建人"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"制表"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"修改日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),i("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return i("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button-group",{staticStyle:{float:"left"}},[i("el-button",{attrs:{type:"print"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="print",t.submitRemoteReport()}}},[t._v("云打印")]),i("el-button",{attrs:{type:"preview"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="preview",t.submitRemoteReport()}}},[t._v("云预览")])],1),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("打印")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),i("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[i("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},p=[],h=(i("e9c4"),i("b64b"),i("d3b7"),i("3ca3"),i("ddb0"),i("2b3d"),i("bf19"),i("9861"),i("b775"));const b={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);h["a"].post("/S06M19S1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);h["a"].post("/S06M19S1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){return new Promise((e,i)=>{h["a"].get("/S06M19S1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})}};var w=b,g=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("el-row",[i("el-button-group",[i("el-button",{attrs:{type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getTrackTemp()}}},[i("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveUp()}}},[i("i",{staticClass:"el-icon-top"}),t._v(" 上 移")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveDown()}}},[i("i",{staticClass:"el-icon-bottom"}),t._v(" 下 移")]),i("el-button",{attrs:{disabled:!t.selected,type:"danger",size:"mini"},nativeOn:{click:function(e){return t.delItem()}}},[i("i",{staticClass:"el-icon-delete"}),t._v(" 删 除")])],1)],1)],1),i("div",{staticClass:"table-container f-1 table-position"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{data:t.lst,"element-loading-text":"Loading",height:t.tableHeight,border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center",fixed:""}}),i("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e,a){return[!e.displaymark?t._e():i("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable,index:a+2},scopedSlots:t._u([{key:"default",fn:function(a){return[i("div",{style:1==a.row.disannulmark?"text-decoration: line-through;":""},["planstart"==e.itemcode||"planend"==e.itemcode||"completiondate"==e.itemcode||"actualstart"==e.itemcode?i("div",[a.row.isEdit?i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:a.row[e.itemcode],callback:function(i){t.$set(a.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}}):i("span",[t._v(t._s(t._f("dateFormat")(a.row[e.itemcode])))])],1):i("div",[a.row.isEdit?i("div",[i("el-input",{attrs:{size:"small",placeholder:e.itemname},model:{value:a.row[e.itemcode],callback:function(i){t.$set(a.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}})],1):i("span",[t._v(t._s(a.row[e.itemcode]))])])])]}}],null,!0)})]}))],2)],1)])},v=[],y=(i("a434"),i("0643"),i("4e3e"),i("159b"),{formcode:"S06M19S1List",item:[{itemcode:"code",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center"},{itemcode:"name",itemname:"名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"modifydate",itemname:"修改日期",minwidth:"100",displaymark:1,overflow:1,datasheet:"modifydate"},{itemcode:"remark",itemname:"备注",minwidth:"80",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"60",displaymark:1,overflow:1}]}),x={formcode:"S06M19S1Item",item:[{itemcode:"phasename",itemname:"阶段名",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"taskname",itemname:"任务名",minwidth:"80",displaymark:1,overflow:1},{itemcode:"description",itemname:"描述",minwidth:"100",displaymark:1,overflow:1},{itemcode:"planstart",itemname:"计划开始时间",minwidth:"70",displaymark:1,overflow:1},{itemcode:"planend",itemname:"计划结束时间",minwidth:"80",displaymark:1,overflow:1},{itemcode:"milestone",itemname:"里程碑",minwidth:"80",displaymark:1,overflow:1},{itemcode:"actualstart",itemname:"实际开始实际",minwidth:"100",displaymark:1,overflow:1},{itemcode:"completiondate",itemname:"完工时间",minwidth:"100",displaymark:1,overflow:1},{itemcode:"outputresult",itemname:"产出成果",minwidth:"80",displaymark:1,overflow:1},{itemcode:"mainresponsibleperson",itemname:"主负责人",minwidth:"100",displaymark:1,overflow:1},{itemcode:"subresponsibleperson",itemname:"次负责人",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1}]},k=(i("da92"),{name:"Elitem",components:{},props:["formdata","lstitem","idx"],data:function(){return{formLabelWidth:"100px",listLoading:!1,lst:[],multi:0,selected:!1,tableHeight:300,multipleSelection:[],isEditOk:!0,selVisible:!1,tableForm:x}},watch:{lstitem:function(t,e){this.lst=this.lstitem?JSON.parse(this.lstitem):[]},"formdata.assessor":function(t,e){this.formdata.assessor?this.isEditOk=!1:this.isEditOk=!0},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,i=t.length,a=t.substring(e,i);return a>0?t:t.substring(0,e)},deleteRows:function(t,e){var i=this,a=this.multipleSelection;a&&a.forEach((function(t,e){i.lst.forEach((function(e,a){t.phasename===e.phasename&&t.rownum===e.rownum&&i.lst.splice(a,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},getTrackTemp:function(){var t={rownum:0,phasename:"",taskname:"",description:"",planstart:new Date,planend:new Date,actualstart:new Date,completiondate:new Date,milestone:"",outputresult:"",mainresponsibleperson:"",subresponsibleperson:"",remark:""};0!=this.idx&&(t.pid=this.idx),this.lst.push(t)},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1,this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0))},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}}),S=k,$=(i("7e40"),Object(d["a"])(S,g,v,!1,null,"13f066e2",null)),_=$.exports,F={name:"Formedit",components:{elitem:_},props:["idx"],data:function(){return{title:"跟踪模板",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,name:"",code:"",itemjson:"",remark:""},formRules:{name:[{required:!0,trigger:"blur",message:"模板名称为必填项"}],code:[{required:!0,trigger:"blur",message:"模板编码为必填项"}]},formLabelWidth:"100px",multi:0,selVisible:!1,ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,printType:"print"}},computed:{formcontainHeight:function(){return window.innerHeight-124+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},mounted:function(){},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&h["a"].get("/S06M19S1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){for(var t=this,e=[],i=0;i<this.$refs.elitem.lst.length;i++){var a=Object.assign({},this.$refs.elitem.lst[i]);this.$delete(a,"isEdit"),e.push(a)}this.formdata.itemjson=JSON.stringify(e||""),0==this.idx?w.add(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")})):w.update(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){w.delete(t).then((function(){e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(t){e.$message.warning(t||"删除失败")}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},printButton:function(){var t=this;h["a"].get("/SaReports/getListByModuleCode?code=S06M19S1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?h["a"].get("/S06M19S1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var i=[];i.push(e.data);var a=window.URL.createObjectURL(new Blob(i,{type:"application/pdf"}));t.pdfUrl=a,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;if(""!=this.reportModel){var e="/S06M19S1/printWebBill?key="+this.idx+"&ptid="+this.reportModel;"preview"==this.printType&&(e="/S06M19S1/printWebBill?key="+this.idx+"&ptid="+this.reportModel+"&cmd=1"),h["a"].get(e).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")}))}else this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.formdata.printcount+=1,this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},fastKey:function(){var t=this;document.onkeydown=function(e){var i=e.keyCode;83==i&&e.ctrlKey?(e.preventDefault(),t.formdata.assessor?t.$message.warning("单据已审核，保存失败！"):t.submitForm("formdata")):27==i&&(e.preventDefault(),t.closeForm())}},changeidx:function(t){this.dialogIdx=t}}},C=F,D=(i("c5e3"),Object(d["a"])(C,f,p,!1,null,"686124af",null)),z=D.exports,P=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[i("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e,a){return[!e.displaymark?t._e():i("el-table-column",{key:a,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return["code"==e.itemcode?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showform(a.row.id)}}},[t._v(t._s(a.row.code?a.row.code:"编码"))]):"serdate"==e.itemcode||"modifydate"==e.itemcode?i("span",[t._v(t._s(t._f("dateFormat")(a.row[e.itemcode])))]):i("span",[t._v(t._s(a.row[e.itemcode]))])]}}],null,!0)})]}))],2),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},T=[],O=(i("d81d"),i("a573"),i("333d")),L=i("48da"),M={components:{Pagination:O["a"]},data:function(){return{lst:[],searchstr:"",total:0,listLoading:!1,idx:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){},methods:{bindData:function(){var t=this;h["a"].post("/S06M19S1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getcolumn:function(){this.tableForm=y},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={name:t,code:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(t,this.tableForm),this.bindData()},showform:function(t){this.$emit("showform",t)},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],i=[],a=0;a<t.tableForm.item.length;a++){var o=t.tableForm.item[a];o.displaymark&&(e.push(o.itemname),i.push(o.itemcode))}var n=t.lst,s=t.formatJson(i,n);Object(L["a"])(e,s,"跟踪模板")}.bind(null,i)).catch(i.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}}},R=M,E=(i("781e"),Object(d["a"])(R,P,T,!1,null,"7eed998e",null)),V=E.exports,N=i("0521"),q={name:"S06M19S1",components:{listheader:u,formedit:z,tableList:V,helpmodel:N["a"]},data:function(){return{lst:[],FormVisible:!1,idx:0,thorList:!1,tableForm:{},showHelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.bindData(),t.$refs.tableList.getcolumn()}))},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.btnExport()}))},search:function(t){this.$refs.tableList.search(t)},AdvancedSearch:function(t){this.$refs.tableList.AdvancedSearch(t)},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(t){this.idx=t}}},H=q,I=(i("be18"),Object(d["a"])(H,a,o,!1,null,null,null));e["default"]=I.exports},"781e":function(t,e,i){"use strict";i("9250")},"7e40":function(t,e,i){"use strict";i("34d5")},"841c":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),n=i("1d80"),s=i("129f"),r=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=n(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var n=o(t),l=String(this),c=n.lastIndex;s(c,0)||(n.lastIndex=0);var d=r(n,l);return s(n.lastIndex,c)||(n.lastIndex=c),null===d?-1:d.index}]}))},9250:function(t,e,i){},be18:function(t,e,i){"use strict";i("15b6")},c5e3:function(t,e,i){"use strict";i("f0f6")},f0f6:function(t,e,i){},f674:function(t,e,i){}}]);