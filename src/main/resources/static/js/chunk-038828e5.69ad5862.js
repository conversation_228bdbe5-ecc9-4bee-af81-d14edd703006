(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-038828e5"],{"00b3":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnadd:function(t){return e.showform(0)},btnsearch:e.search,bindData:e.bindData,AdvancedSearch:e.AdvancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"sort-change":e.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),e._l(e.tableForm.item,(function(t,n){return[!t.displaymark?e._e():a("el-table-column",{key:n,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(n){return["engineercode"==t.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showform(n.row.id)}}},[e._v(e._s(n.row.engineercode?n.row.engineercode:"编码"))]):"createdate"==t.itemcode||"modifydate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormats")(n.row[t.itemcode])))]):"enabledmark"==t.itemcode?a("div",[n.row.enabledmark?a("el-tag",{attrs:{size:"small"}},[e._v("启用")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("停 用")])],1):a("span",[e._v(e._s(n.row[t.itemcode]))])]}}],null,!0)})]})),2==e.$store.state.user.userinfo.isadmin||null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype?a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handlePiPerMission(t.row.id,t.row)}}},[e._v("删除")])]}}],null,!1,828007154)}):e._e()],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.GetList}})],1)],1)],1)],1),e.FormVisible?a("el-dialog",{attrs:{width:"800px",title:"工程师","append-to-body":!0,visible:e.FormVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.FormVisible=t},close:e.bindData}},[a("formedit",{ref:"formedit",attrs:{idx:e.idx},on:{bindData:e.bindData,changeidx:e.changeidx,closeDialog:function(t){e.FormVisible=!1}}})],1):e._e()],1)},i=[],r=(a("e9c4"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1),2==e.$store.state.user.userinfo.isadmin||null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype?a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:e.btnadd}},[e._v(" 添加 ")]):e._e()],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:e.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(t){e.iShow=!e.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:e.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"工号"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入工号",size:"small"},model:{value:e.formdata.engineercode,callback:function(t){e.$set(e.formdata,"engineercode",t)},expression:"formdata.engineercode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"姓名"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入姓名",size:"small"},model:{value:e.formdata.engineername,callback:function(t){e.$set(e.formdata,"engineername",t)},expression:"formdata.engineername"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"关联用户"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入关联用户",size:"small"},model:{value:e.formdata.username,callback:function(t){e.$set(e.formdata,"username",t)},expression:"formdata.username"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"邮箱"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入邮箱",size:"small"},model:{value:e.formdata.email,callback:function(t){e.$set(e.formdata,"email",t)},expression:"formdata.email"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.AdvancedSearch()}}},[e._v(" 搜索 ")])],1)],1)],1)],1)])],1)}),o=[],s={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M02S2List"}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},l=s,c=(a("efb4"),a("2877")),d=Object(c["a"])(l,r,o,!1,null,"2fc7dbfc",null),m=d.exports,u=a("333d"),f=a("b775"),p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":"100px",rules:e.formRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("engineercode")}}},[a("el-form-item",{attrs:{label:"工号",prop:"engineercode"}},[a("el-input",{attrs:{placeholder:"请输入工号",clearable:""},model:{value:e.formdata.engineercode,callback:function(t){e.$set(e.formdata,"engineercode",t)},expression:"formdata.engineercode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("engineername")}}},[a("el-form-item",{attrs:{label:"姓名",prop:"engineername"}},[a("el-input",{attrs:{placeholder:"请输入姓名",clearable:""},model:{value:e.formdata.engineername,callback:function(t){e.$set(e.formdata,"engineername",t)},expression:"formdata.engineername"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("engineertype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"engineertype"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择类型"},model:{value:e.formdata.engineertype,callback:function(t){e.$set(e.formdata,"engineertype",t)},expression:"formdata.engineertype"}},[a("el-option",{attrs:{label:"管理者",value:"管理者"}}),a("el-option",{attrs:{label:"开发",value:"开发"}}),a("el-option",{attrs:{label:"运维",value:"运维"}}),a("el-option",{attrs:{label:"测试",value:"测试"}})],1)],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("userid")}}},[a("el-form-item",{attrs:{label:"关联用户",prop:"userid"}},[a("autoComplete",{attrs:{size:"default",value:e.formdata.username,baseurl:"/PmsSaUser/getPageList",params:{name:"username",other:"realname"}},on:{setRow:e.setRow,autoClear:e.autoClear}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!0,type:"number",min:0,size:"default"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:15}},[a("div",{on:{click:function(t){return e.cleValidate("email")}}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{placeholder:"请输入邮箱",clearable:""},model:{value:e.formdata.email,callback:function(t){e.$set(e.formdata,"email",t)},expression:"formdata.email"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",[a("el-form-item",{attrs:{label:"状态"}},[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}})],1)],1)])],1)],1)],1),a("el-form",{staticClass:"footFormContent"},[a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入摘要",clearable:""},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1)],1),a("el-row",{directives:[{name:"show",rawName:"v-show",value:2==e.$store.state.user.userinfo.isadmin||null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype,expression:"\n          $store.state.user.userinfo.isadmin == 2 ||\n          ($store.state.user.userinfo.engineer == null\n            ? false\n            : $store.state.user.userinfo.engineer.engineertype == '管理者')\n        "}]},[a("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px"},attrs:{span:24}},[a("div",[e.formdata.id?a("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.deleteBtn()}}},[e._v("删除")]):e._e()],1),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("formdata")}}},[e._v("保存")]),a("el-button",{on:{click:e.closeDialog}},[e._v("取消")])],1)])],1)],1)])])},h=[];a("b64b");const g={add(e){return new Promise((t,a)=>{var n=JSON.stringify(e);f["a"].post("/S06M02S2/create",n).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var n=JSON.stringify(e);f["a"].post("/S06M02S2/update",n).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{f["a"].get("/S06M02S2/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var b=g,w=a("5b24"),y={name:"addDialog",props:["idx"],components:{autoComplete:w["a"]},data:function(){return{title:"工程项目",currentId:this.idx,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,engineercode:"",engineertype:"开发",engineername:"",remark:"",enabledmark:1,userid:"",username:"",email:""},formRules:{projname:[{required:!0,trigger:"blur",message:"名称不能为空"}],projcode:[{required:!0,trigger:"blur",message:"项目编码不能为空"}],projtype:[{required:!0,trigger:"blur",message:"项目类型不能为空"}]}}},watch:{idx:function(e,t){this.currentId=e,this.binddata()}},created:function(){this.binddata()},methods:{binddata:function(){var e=this;0!=this.currentId&&f["a"].get("/S06M02S2/getEntity?key=".concat(this.currentId)).then((function(t){200==t.data.code?e.formdata=t.data.data:e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeDialog()}})})).catch((function(t){e.$message.error(t||"请求错误")}))},submitForm:function(e){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return!1;t.saveForm()}))},saveForm:function(){var e=this;if(0==this.idx){var t=Object.assign({},this.formdata);t.status=[{statusname:"待处理",statustype:"开始"},{statusname:"进行中",statustype:"进行中"},{statusname:"已结束",statustype:"已完成"}],b.add(t).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("bindData"),e.closeDialog())})).catch((function(t){e.$message.warning(t||"保存失败")}))}else b.update(this.formdata).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("changeIdx",t.data.id),e.$emit("bindData"))})).catch((function(t){e.$message.warning(t||"保存失败")}))},deleteBtn:function(){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.delete(e.formdata.id).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("bindData"),e.closeDialog()})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},setRow:function(e){this.formdata.userid=e.id,this.formdata.username=e.username,this.formdata.email=e.email},autoClear:function(){this.formdata.userid="",this.formdata.username="",this.formdata.email=""},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}},v=y,x=(a("4ff4"),Object(c["a"])(v,p,h,!1,null,"46fac05e",null)),S=x.exports,k={formcode:"S06M02S2List",item:[{itemcode:"engineercode",itemname:"工号",minwidth:"80",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center",datasheet:"Sa_Engineer.engineercode"},{itemcode:"engineername",itemname:"姓名",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_Engineer.engineername"},{itemcode:"engineertype",itemname:"类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Engineer.engineertype"},{itemcode:"username",itemname:"关联用户",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Engineer.username"},{itemcode:"email",itemname:"邮箱",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Engineer.email"},{itemcode:"enabledmark",itemname:"状态",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Engineer.enabledmark"},{itemcode:"remark",itemname:"备注",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Engineer.remark"}]},$={name:"S06M02S2",components:{Pagination:u["a"],listheader:m,formedit:S},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:10,OrderType:0,SearchType:1,OrderBy:"Sa_Engineer.rownum"},tableForm:k}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){var e=this;this.listLoading=!0,f["a"].post("/S06M02S2/getPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},changeSort:function(e){"descending"==e.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(e,this.tableForm),this.bindData()},handlePiPerMission:function(e,t){var a=this;this.$confirm("是否确定注销工程师【"+t.engineername+"】，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.idx=e,f["a"].get("/S06M02S2/delete?key=".concat(e)).then((function(e){200==e.data.code&&(a.$message.success("删除成功"),a.bindData())})).catch((function(e){a.$message.warning("删除失败")}))})).catch((function(){}))},GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={engineercode:e,engineername:e,engineertype:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(e){this.idx=e,this.FormVisible=!0},closeForm:function(){this.bindData(),this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(e){this.idx=e}}},_=$,P=(a("c006"),Object(c["a"])(_,n,i,!1,null,"e43805d4",null));t["default"]=P.exports},"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,a,n){return e/=n/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function i(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,a){var o=r(),s=e-o,l=20,c=0;t="undefined"===typeof t?500:t;var d=function(){c+=l;var e=Math.easeInOutQuad(c,o,s,t);i(e),c<t?n(d):a&&"function"===typeof a&&a()};d()}},"447a":function(e,t,a){},"4ff4":function(e,t,a){"use strict";a("6425")},6425:function(e,t,a){},7005:function(e,t,a){},c006:function(e,t,a){"use strict";a("7005")},efb4:function(e,t,a){"use strict";a("447a")}}]);