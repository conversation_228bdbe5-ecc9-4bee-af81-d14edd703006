(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0891993a"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function s(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(t,e,a){var n=o(),l=t-n,r=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=r;var t=Math.easeInOutQuad(c,n,l,e);s(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},3387:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm},on:{btnadd:function(e){return t.showform(0)},btnsearch:t.search,bindData:t.bindData,AdvancedSearch:t.AdvancedSearch,btnExport:t.btnExport}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:t.showHelp?20:24}},[a("tableList",{ref:"tableList",on:{changeidx:t.changeidx,showform:t.showform,sendTableForm:t.sendTableForm}})],1),a("el-col",{attrs:{span:t.showHelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"S06M07B1"}})],1)],1)],1)],1)])},s=[],o=(a("ac1f"),a("841c"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单据编码",size:"small"},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据标题"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单据标题",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"客户"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入客户",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"联系人"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入联系人",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.AdvancedSearch()}}},[t._v(" 搜索 ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"服务需求"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入服务需求",size:"small"},model:{value:t.formdata.sercontent,callback:function(e){t.$set(t.formdata,"sercontent",e)},expression:"formdata.sercontent"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"服务过程"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入服务过程",size:"small"},model:{value:t.formdata.serprocess,callback:function(e){t.$set(t.formdata,"serprocess",e)},expression:"formdata.serprocess"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"未尽事宜"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入未尽事宜",size:"small"},model:{value:t.formdata.serloss,callback:function(e){t.$set(t.formdata,"serloss",e)},expression:"formdata.serloss"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"简述"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入简述",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1)],1)],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.setColumsVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)}),n=[],l=a("8daf"),r={name:"Listheader",props:["tableForm"],components:{Setcolums:l["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},thorList:!0,setColumsVisible:!1,code:"D11M04B1List"}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList)},btnExport:function(){this.$emit("btnExport")}}},c=r,d=(a("9bc5"),a("2877")),m=Object(d["a"])(c,o,n,!1,null,"77d6b019",null),u=m.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!t.formdata.assessor},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[t.formdata.assessor?a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.DeApproval()}}},[t._v(" 反审核")]):a("el-button",{attrs:{size:"small",type:"primary",disabled:!t.formdata.id},on:{click:function(e){return t.approval()}}},[t._v(" 审 核")])],1),a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),a("el-dropdown",{staticStyle:{"margin-left":"10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id||!!t.formdata.assessor},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("过程"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"})],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:""},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),a("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[a("div",{staticStyle:{"margin-right":"10px",width:"100px"}},[t.formdata.finishcount>0&&t.formdata.finishcount+t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"finish1"}}):t.formdata.disannulcount>0&&t.formdata.disannulcount==t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"revoke"}}):t._e()],1),a("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"min-height":"110px"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[a("el-form-item",{attrs:{label:"单据类型",prop:"billtype"}},[a("el-input",{attrs:{placeholder:"请输入单据类型",clearable:"",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据主题"}},[a("el-input",{attrs:{placeholder:"请输入单据主题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"项目"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",size:"small"},on:{change:t.changeProject},model:{value:t.formdata.projectid,callback:function(e){t.$set(t.formdata,"projectid",e)},expression:"formdata.projectid"}},t._l(t.projectData,(function(t){return a("el-option",{key:t.value,attrs:{label:t.projname,value:t.id}})})),1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[a("el-popover",{ref:"dictionaryRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}}},[a("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_JobOrder.groupname"},on:{singleSel:function(e){t.formdata.groupname=e.dictvalue,t.$refs["dictionaryRef"].doClose(),t.$forceUpdate()},closedic:function(e){return t.$refs["dictionaryRef"].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择客户",clearable:"",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("linkman")}}},[a("el-form-item",{attrs:{label:"联系人"}},[a("el-input",{attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"联系电话"}},[a("el-input",{attrs:{placeholder:"请输入联系电话",clearable:"",size:"small"},model:{value:t.formdata.telephone,callback:function(e){t.$set(t.formdata,"telephone",e)},expression:"formdata.telephone"}})],1)],1),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("licenseplate")}}},[a("el-form-item",{attrs:{label:"车牌号",prop:"licenseplate"}},[a("el-popover",{ref:"licenRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selLicenRef.bindData()}}},[a("selDictionaries",{ref:"selLicenRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_JobOrder.licenseplate"},on:{singleSel:function(e){t.formdata.licenseplate=e.dictvalue,t.$refs["licenRef"].doClose(),t.$forceUpdate()},closedic:function(e){return t.$refs["licenRef"].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择车牌号",clearable:"",size:"small"},model:{value:t.formdata.licenseplate,callback:function(e){t.$set(t.formdata,"licenseplate",e)},expression:"formdata.licenseplate"}})],1)],1)],1)],1)])],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("serclass")}}},[a("el-form-item",{attrs:{label:"服务方式"}},[a("el-input",{attrs:{placeholder:"请输入服务方式",clearable:"",size:"small"},model:{value:t.formdata.serclass,callback:function(e){t.$set(t.formdata,"serclass",e)},expression:"formdata.serclass"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("seradd")}}},[a("el-form-item",{attrs:{label:"服务地址"}},[a("el-input",{attrs:{placeholder:"请输入服务地址",clearable:"",size:"small"},model:{value:t.formdata.seradd,callback:function(e){t.$set(t.formdata,"seradd",e)},expression:"formdata.seradd"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("serdate")}}},[a("el-form-item",{attrs:{label:"服务日期"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.serdate,callback:function(e){t.$set(t.formdata,"serdate",e)},expression:"formdata.serdate"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("mileage")}}},[a("el-form-item",{attrs:{label:"公里数"}},[a("el-input",{attrs:{placeholder:"请输入公里数",clearable:"",size:"small"},model:{value:t.formdata.mileage,callback:function(e){t.$set(t.formdata,"mileage",e)},expression:"formdata.mileage"}})],1)],1)])],1)],1)],1),a("div",{ref:"tabsHeight",staticClass:"form-body form"},[a("div",{staticClass:"outer-div"},[a("header",{staticClass:"header"},[a("ul",{staticClass:"tab-tilte"},[a("li",{class:{active:0==t.activeTabs},on:{click:function(e){t.activeTabs=0}}},[t._v(" 服务内容 ")]),a("li",{class:{active:1==t.activeTabs},on:{click:function(e){t.activeTabs=1}}},[t._v(" 物品清单 ")])])]),a("div",{staticClass:"tab-content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:0==t.activeTabs,expression:"activeTabs == 0"}],staticStyle:{"min-height":"300px"}},[a("el-form",{staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"80px"}},[a("div",{staticClass:"flex",attrs:{id:"fieldset"}},[a("fieldset",{staticClass:"fieldsetStyle",staticStyle:{width:"49%","margin-bottom":"10px"}},[a("legend",[t._v("服务需求")]),a("MyEditor",{ref:"MyEditor",attrs:{height:215,html:t.formdata.sercontent,excludeKeys:[],toolbarKeys:["undo","redo","headerSelect","bold","italic","fontSize","fontFamily","color","bgColor","|","justifyLeft","justifyCenter","justifyRight","justifyJustify","|","bulletedList","numberedList","indent","delIndent","insertTable","|","clearStyle","fullScreen"]},on:{changeHtml:function(e){t.formdata.sercontent=e}}})],1),a("fieldset",{staticClass:"fieldsetStyle",staticStyle:{width:"49%","margin-bottom":"10px"}},[a("legend",[t._v("服务过程")]),a("MyEditor",{ref:"MyEditor",attrs:{height:215,html:t.formdata.serprocess,excludeKeys:[],toolbarKeys:["undo","redo","headerSelect","bold","italic","fontSize","fontFamily","color","bgColor","|","justifyLeft","justifyCenter","justifyRight","justifyJustify","|","bulletedList","numberedList","indent","delIndent","insertTable","|","clearStyle","fullScreen"]},on:{changeHtml:function(e){t.formdata.serprocess=e}}})],1)])])],1),a("div",{directives:[{name:"show",rawName:"v-show",value:1==t.activeTabs,expression:"activeTabs == 1"}],staticStyle:{"min-height":"300px"}},[a("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.matjson?JSON.parse(t.formdata.matjson):[],formdata:t.formdata,idx:t.idx,tabsHeight:t.tabsHeight}})],1)])])]),a("el-divider"),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("serloss")}}},[a("el-form-item",{attrs:{label:"未尽事宜"}},[a("el-input",{attrs:{placeholder:"请输入未尽事宜",clearable:"",size:"small"},model:{value:t.formdata.serloss,callback:function(e){t.$set(t.formdata,"serloss",e)},expression:"formdata.serloss"}})],1)],1)]),a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("confidential")}}},[a("el-form-item",{attrs:{label:"机密信息"}},[a("el-input",{attrs:{placeholder:"请输入机密信息",clearable:"",size:"small"},model:{value:t.formdata.confidential,callback:function(e){t.$set(t.formdata,"confidential",e)},expression:"formdata.confidential"}})],1)],1)])],1),a("el-row",{staticStyle:{"margin-top":"10px","margin-right":"20px"}},[a("el-col",{attrs:{span:18}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"经办人"}},[a("el-input",{attrs:{placeholder:"请输入经办人姓名",clearable:"",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.assessor))])])],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.formdata.assessor,expression:"formdata.assessor"}],attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核日期"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.assessdate)))])])],1)],1)],1)],1)]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button-group",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"print"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="print",t.submitRemoteReport()}}},[t._v("云打印")]),a("el-button",{attrs:{type:"preview"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="preview",t.submitRemoteReport()}}},[t._v("云预览")])],1),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("打印")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},p=[],h=a("c7eb"),b=a("1da1"),v=(a("e9c4"),a("b64b"),a("d3b7"),a("3ca3"),a("0643"),a("4e3e"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("b775"));const g={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);v["a"].post("/S06M07B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);v["a"].post("/S06M07B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{v["a"].get("/S06M07B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var w=g,y=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",disabled:!!t.formdata.assessor},nativeOn:{click:function(e){return t.getselPwProcess(1)}}},[a("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveUp()}}},[a("i",{staticClass:"el-icon-top"}),t._v(" 上 移")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveDown()}}},[a("i",{staticClass:"el-icon-bottom"}),t._v(" 下 移")]),a("el-button",{attrs:{disabled:!t.selected,type:"danger",size:"mini"},nativeOn:{click:function(e){return t.delItem()}}},[a("i",{staticClass:"el-icon-delete"}),t._v(" 删 除")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{data:t.lst,"element-loading-text":"Loading",height:t.tableHeight,border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[a("el-table-column",{attrs:{type:"selection",width:"50px",align:"center",fixed:"left"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"货品名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"货品名称"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:e.row.itemname,callback:function(a){t.$set(e.row,"itemname",a)},expression:"scope.row.itemname"}}):a("span",[t._v(t._s(e.row.itemname))])]}}])}),a("el-table-column",{attrs:{label:"规格",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"规格"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:e.row.itemspec,callback:function(a){t.$set(e.row,"itemspec",a)},expression:"scope.row.itemspec"}}):a("span",[t._v(t._s(e.row.itemspec))])]}}])}),a("el-table-column",{attrs:{label:"单位",align:"center","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"单位"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:e.row.goodsunit,callback:function(a){t.$set(e.row,"goodsunit",a)},expression:"scope.row.goodsunit"}}):a("span",[t._v(t._s(e.row.itemunit))])]}}])}),a("el-table-column",{attrs:{label:"数量",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"数量"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:e.row.quantity,callback:function(a){t.$set(e.row,"quantity",a)},expression:"scope.row.quantity"}}):a("span",[t._v(t._s(e.row.quantity))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center","min-width":"140","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.isEdit?a("el-input",{attrs:{size:"small",placeholder:"请输入内容"},on:{focus:function(t){return t.currentTarget.select()}},model:{value:e.row.remark,callback:function(a){t.$set(e.row,"remark",a)},expression:"scope.row.remark"}}):a("span",[t._v(t._s(e.row.remark))])]}}])})],1)],1),t.PwProcessFormVisible?a("el-dialog",{attrs:{title:"货品信息","append-to-body":!0,visible:t.PwProcessFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.PwProcessFormVisible=e}}},[a("selPwProcess",{ref:"selPwProcess",attrs:{multi:t.multi}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPwProcess()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.PwProcessFormVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},x=[],k=a("ade3"),S=(a("a434"),a("da92"),{name:"Elitem",components:{},props:["formdata","lstitem","idx","tabsHeight"],data:function(){return{formLabelWidth:"100px",listLoading:!1,PwProcessFormVisible:!1,lst:[],multi:0,selected:!1,tableHeight:300,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,selVisible:!1}},watch:{lstitem:function(t,e){this.lst=this.lstitem,this.lst.forEach((function(t){t.isEdit=!1}))},"formdata.assessor":function(t,e){this.formdata.assessor?this.isEditOk=!1:this.isEditOk=!0},lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{selPwProcess:function(){this.PwProcessFormVisible=!1;var t=this.$refs.selPwProcess.$refs.selectgoods.selection;console.log(t);for(var e=0;e<t.length;e++){var a=Object(k["a"])({rownum:0,goodsid:t[e].id,goodsuid:t[e].goodsuid,goodsname:t[e].goodsname,goodsunit:t[e].goodsunit,goodsspec:t[e].goodsspec,amount:0,itemcode:t[e].goodsuid,itemname:t[e].goodsname,itemspec:t[e].goodsspec,itemunit:t[e].goodsunit,price:0,quantity:0,remark:""},"rownum",0);0!=this.idx&&(a.pid=this.idx),this.lst.push(a)}},numFormat:function(t){t+="";var e=t.lastIndexOf(".")?t.lastIndexOf("."):0,a=t.length,i=t.substring(e,a);return i>0?t:t.substring(0,e)},deleteRows:function(t,e){var a=this,i=this.multipleSelection;i&&i.forEach((function(t,e){a.lst.forEach((function(e,i){t.goodsid===e.goodsid&&t.rownum===e.rownum&&a.lst.splice(i,1)}))})),this.$refs.multipleTable.clearSelection(),this.selected=!1},getselPwProcess:function(t){var e=Object(k["a"])({rownum:0,goodsid:"",goodsuid:"",goodsname:"",goodsunit:"",goodsspec:"",amount:0,itemcode:"",itemname:"",itemspec:"",itemunit:"",price:0,quantity:0,remark:""},"rownum",0);0!=this.idx&&(e.pid=this.idx),this.lst.push(e)},catchHight:function(){var t=this;this.$nextTick((function(){t.tabsHeight&&(t.tableHeight=t.tabsHeight.height-62)}))},handleSelectionChange:function(t){t.length>0?this.selected=!0:this.selected=!1,this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},SelCust:function(t){var e=this.$refs.SelCust.selrows;console.log("selctResult",e,this.$refs.SelCust),this.lst[t].groupname=e.groupname,this.lst[t].groupid=e.id,this.selVisible=!1},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}}),_=S,$=(a("3d13"),Object(d["a"])(_,y,x,!1,null,"4db3987e",null)),C=$.exports,P=a("1975"),z=a("5c73"),F={name:"Formedit",components:{MyEditor:P["a"],elitem:C,selDictionaries:z["a"]},props:["idx"],data:function(){return{title:"服务派工",formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,assessor:"",billdate:new Date,billtitle:"",billtype:"服务派工",confidential:"",disannulmark:0,finishmark:0,gengroupid:"",groupcode:"",groupname:"",groupid:"",itemcode:"",itemcount:0,itemname:"",linkman:"",matjson:"",operator:"",operatorid:"",printcount:0,projectid:"",refno:"",remark:"",revision:0,score1:0,score2:0,score3:0,seradd:"",serclass:"",sercontent:"",serdate:"",serloss:"",serprocess:"",licenseplate:"",mileage:0,telephone:""},formRules:{billtype:[{required:!0,trigger:"blur",message:"单据类型为必填项"}],groupid:[{required:!0,trigger:"blur",message:"客户为必填项"}]},formLabelWidth:"100px",multi:0,selVisible:!1,ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,tabsHeight:280,activeTabs:"0",projectData:[],selDicVisible:!1,printType:"print"}},computed:{formcontainHeight:function(){return window.innerHeight-124+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData(),this.getProject()},mounted:function(){this.tabsHeight=this.$refs.tabsHeight.getBoundingClientRect()},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&v["a"].get("/S06M07B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getProject:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};v["a"].post("/S06M01S1/getPageTh",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.projectData=e.data.data.list)}))},changeProject:function(t){var e=this;this.projectData.forEach((function(a){a.id==t&&(e.formdata.itemname=a.projname,e.formdata.itemcode=a.projcode,e.formdata.projectid=a.id)}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;console.log(this.formdata),this.formdata.matjson=JSON.stringify(this.$refs.elitem.lst),0==this.idx?w.add(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")})):w.update(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning(e||"保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){w.delete(t).then((function(){e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(t){e.$message.warning(t||"删除失败")}))})).catch((function(){}))},approval:function(){var t=this;this.formdata.id?this.approvalRequest(this.formdata.id):this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;w.add(t.formdata).then((function(e){t.formdata=e.data,t.$emit("changeidx",t.formdata.id),t.approvalRequest(t.formdata.id)})).catch((function(e){t.$message.warning(e||"保存失败")}))}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},DeApproval:function(){var t=this;v["a"].get("/S06M07B1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"反审核成功"),t.formdata=e.data.data):t.$message.warning(e.data.msg||"反审核失败")}))},approvalRequest:function(t){var e=this;return Object(b["a"])(Object(h["a"])().mark((function t(){return Object(h["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,v["a"].get("/S06M07B1/approval?key="+e.formdata.id).then((function(t){200==t.data.code?(e.$message.success(t.data.msg||"审核成功"),e.formdata=t.data.data):e.$message.warning(t.data.msg||"审核失败")}));case 2:case"end":return t.stop()}}),t)})))()},printButton:function(){var t=this;v["a"].get("/SaReports/getListByModuleCode?code=S06M07B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?v["a"].get("/S06M07B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;if(""!=this.reportModel){var e="/S06M07B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel;"preview"==this.printType&&(e="/S06M07B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel+"&cmd=1"),v["a"].get(e).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")}))}else this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.formdata.printcount+=1,this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},fastKey:function(){var t=this;document.onkeydown=function(e){var a=e.keyCode;83==a&&e.ctrlKey?(e.preventDefault(),t.formdata.assessor?t.$message.warning("单据已审核，保存失败！"):t.submitForm("formdata")):27==a&&(e.preventDefault(),t.closeForm())}},changeidx:function(t){this.dialogIdx=t}}},D=F,O=(a("c71a"),Object(d["a"])(D,f,p,!1,null,"4e3751c3",null)),T=O.exports,M=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"summary-method":t.getSummaries,"show-summary":"","element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["refno"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showform(i.row.id)}}},[t._v(t._s(i.row.refno?i.row.refno:"编码"))]):"serdate"==e.itemcode||"modifydate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)],1)},V=[],I=(a("d81d"),a("a573"),a("333d")),j=a("48da"),R={formcode:"S06M07B1List",item:[{itemcode:"refno",itemname:"单据编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Pms_JobOrder.refno"},{itemcode:"billtype",itemname:"单据类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Pms_JobOrder.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Pms_JobOrder.billtitle"},{itemcode:"serdate",itemname:"服务日期",minwidth:"80",displaymark:1,overflow:1},{itemcode:"itemcode",itemname:"产品编码",minwidth:"80",displaymark:1,overflow:1,datasheet:"Pms_JobOrder.itemcode"},{itemcode:"itemname",itemname:"产品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Pms_JobOrder.itemname"},{itemcode:"groupname",itemname:"客户",minwidth:"100",displaymark:1,overflow:1,datasheet:"Pms_JobOrder.groupname"},{itemcode:"linkman",itemname:"联系人",minwidth:"100",displaymark:1,overflow:1,datasheet:"Pms_JobOrder.linkman"},{itemcode:"telephone",itemname:"电话",minwidth:"80",displaymark:1,overflow:1},{itemcode:"seradd",itemname:"地址",minwidth:"80",displaymark:1,overflow:1},{itemcode:"serclass",itemname:"服务方式",minwidth:"80",displaymark:1,overflow:1},{itemcode:"licenseplate",itemname:"车牌号",minwidth:"80",displaymark:1,overflow:1},{itemcode:"mileage",itemname:"公里数",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"80",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"60",displaymark:1,overflow:1,datasheet:"Pms_JobOrder.lister"}]},L=a("b893"),B={components:{Pagination:I["a"]},data:function(){return{lst:[],searchstr:"",total:0,listLoading:!1,idx:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:R,customList:[],groupInfo:{}}},props:["online"],computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){},updated:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.doLayout()}))},methods:{bindData:function(){var t=this;this.listLoading=!0,v["a"].post("/S06M07B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){this.tableForm=R},getSummaries:function(t){return Object(L["e"])(t,["mileage"])},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.binData()},search:function(t){""!=t?this.queryParams.SearchPojo={refno:t,billtitle:t,itemcode:t,itemname:t,linkman:t,groupname:t,licenseplate:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(t,this.tableForm),this.binData()},showform:function(t){this.$emit("showform",t)},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],a=[],i=0;i<t.tableForm.item.length;i++){var s=t.tableForm.item[i];s.displaymark&&(e.push(s.itemname),a.push(s.itemcode))}var o=t.lst,n=t.formatJson(a,o);Object(j["a"])(e,n,"服务派工")}.bind(null,a)).catch(a.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))}}},E=B,N=(a("e1553"),Object(d["a"])(E,M,V,!1,null,"f56cbbb0",null)),A=N.exports,q=a("0521"),H={name:"S06M07B1",components:{listheader:u,formedit:T,tableList:A,helpmodel:q["a"]},data:function(){return{title:"服务派工",lst:[],FormVisible:!1,idx:0,thorList:!1,tableForm:{},showHelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.bindData(),t.$refs.tableList.getColumn()}))},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.btnExport()}))},search:function(t){this.$refs.tableList.search(t)},AdvancedSearch:function(t){this.$refs.tableList.AdvancedSearch(t)},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(t){this.idx=t}}},J=H,U=(a("7598"),Object(d["a"])(J,i,s,!1,null,null,null));e["default"]=U.exports},"3d13":function(t,e,a){"use strict";a("ad23")},"50d5":function(t,e,a){},"5c73":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,i){return a("li",{key:i,class:t.radio==i?"active":"",on:{click:function(a){t.getCurrentRow(e),t.radio=i}}},[a("span",[t._v(t._s(e.dictvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,i){return a("p",{key:i,class:t.ActiveIndex==i?"isActive":"",on:{click:function(e){t.ActiveIndex=i}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},s=[],o=(a("a434"),a("e9c4"),a("b775")),n=a("333d"),l=a("b0b8"),r={components:{Pagination:n["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],o["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var a=0;a<t.formdata.item.length;a++)t.lst.push(t.formdata.item[a])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.addItem(a)})).catch((function(t){console.log(t)}))},addItem:function(t){l.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:l.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.lst[t.ActiveIndex].dictvalue=a,l.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=l.getFullChars(a)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,o["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=r,d=(a("af2b"),a("2877")),m=Object(d["a"])(c,i,s,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"5cc6":function(t,e,a){var i=a("74e8");i("Uint8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))},7598:function(t,e,a){"use strict";a("8bfb")},"7de4":function(t,e,a){},"841c":function(t,e,a){"use strict";var i=a("d784"),s=a("825a"),o=a("1d80"),n=a("129f"),l=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=o(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var o=s(t),r=String(this),c=o.lastIndex;n(c,0)||(o.lastIndex=0);var d=l(o,r);return n(o.lastIndex,c)||(o.lastIndex=c),null===d?-1:d.index}]}))},"8bfb":function(t,e,a){},9993:function(t,e,a){},"9bc5":function(t,e,a){"use strict";a("ea0d")},ad23:function(t,e,a){},af2b:function(t,e,a){"use strict";a("7de4")},c71a:function(t,e,a){"use strict";a("9993")},e1553:function(t,e,a){"use strict";a("50d5")},ea0d:function(t,e,a){}}]);