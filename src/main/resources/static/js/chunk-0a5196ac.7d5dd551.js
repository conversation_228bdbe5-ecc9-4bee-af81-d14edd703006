(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0a5196ac"],{"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,i,a){return t/=a/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,i){var o=s(),l=t-o,r=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=r;var t=Math.easeInOutQuad(c,o,l,e);n(t),c<e?a(d):i&&"function"===typeof i&&i()};d()}},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"152b":function(t,e,i){"use strict";i("8997")},"1cd7":function(t,e,i){"use strict";i("92b5")},"5c73":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,a){return i("li",{key:a,class:t.radio==a?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=a}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,a){return i("p",{key:a,class:t.ActiveIndex==a?"isActive":"",on:{click:function(e){t.ActiveIndex=a}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},n=[],s=(i("a434"),i("e9c4"),i("b775")),o=i("333d"),l=i("b0b8"),r={components:{Pagination:o["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],s["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){l.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:l.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,l.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=l.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,s["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=r,d=(i("af2b"),i("2877")),m=Object(d["a"])(c,a,n,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"7a46":function(t,e,i){},"7de4":function(t,e,i){},8277:function(t,e,i){"use strict";i("7a46")},"841c":function(t,e,i){"use strict";var a=i("d784"),n=i("825a"),s=i("1d80"),o=i("129f"),l=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=s(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var s=n(t),r=String(this),c=s.lastIndex;o(c,0)||(s.lastIndex=0);var d=l(s,r);return o(s.lastIndex,c)||(s.lastIndex=c),null===d?-1:d.index}]}))},8997:function(t,e,i){},"92b5":function(t,e,i){},af2b:function(t,e,i){"use strict";i("7de4")},eb32:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.FormVisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[i("listHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,btnExport:t.btnExport,btnHelp:t.btnHelp}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:t.showhelp?20:24}},[i("tableList",{ref:"tableList",on:{changeIdx:t.changeIdx,showForm:t.showForm,sendTableForm:t.sendTableForm}})],1),i("el-col",{attrs:{span:t.showhelp?4:0}},[i("HelpModel",{ref:"helpmodel",attrs:{code:"S06M29S1"}})],1)],1)],1)],1)])},n=[],s=(i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container flex j-s"},[i("div",{staticStyle:{display:"flex"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),i("div",{staticClass:"iShowBtn"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),o=[],l={name:"Listheader",props:["tableForm"],components:{},data:function(){return{strfilter:"",iShow:!1,formdata:{},setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(t){this.$emit("advancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")}}},r=l,c=(i("152b"),i("2877")),d=Object(c["a"])(r,s,o,!1,null,"2f906d1b",null),m=d.exports,u=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),i("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[i("el-button",{attrs:{size:"small"}},[t._v("操作"),i("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id||!!t.formdata.assessor},nativeOn:{click:function(e){return t.rowdel(t.idx)}}},[t._v("删 除")])],1)],1),i("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),i("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:4}},[i("div",{on:{click:function(e){return t.cleValidate("stagename")}}},[i("el-form-item",{attrs:{label:"阶段名称",prop:"stagename"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入阶段名称",clearable:"",size:"small"},model:{value:t.formdata.stagename,callback:function(e){t.$set(t.formdata,"stagename",e)},expression:"formdata.stagename"}})],1)],1)]),i("el-col",{attrs:{span:4}},[i("div",{on:{click:function(e){return t.cleValidate("stagetype")}}},[i("el-form-item",{attrs:{label:"阶段类型",prop:"stagetype"}},[i("el-popover",{ref:"dictionaryRef",attrs:{placement:"bottom",trigger:"click",width:"200px"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}}},[i("selDictionaries",{ref:"selDictionariesRef",attrs:{multi:0,billcode:"Sa_ActivityStage.stagetype"},on:{singleSel:function(e){t.formdata.stagetype=e.dictvalue,t.$refs["dictionaryRef"].doClose(),t.$forceUpdate(),t.cleValidate("stagetype")},closedic:function(e){t.$refs["dictionaryRef"].doClose(),t.cleValidate("stagetype")}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"请选择阶段类型",clearable:"",size:"small"},model:{value:t.formdata.stagetype,callback:function(e){t.$set(t.formdata,"stagetype",e)},expression:"formdata.stagetype"}})],1)],1)],1)],1)]),i("el-col",{attrs:{span:4}},[i("div",{on:{click:function(e){return t.cleValidate("stagedesc")}}},[i("el-form-item",{attrs:{label:"描述",prop:"stagedesc"}},[i("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入描述",clearable:"",size:"small"},model:{value:t.formdata.stagedesc,callback:function(e){t.$set(t.formdata,"stagedesc",e)},expression:"formdata.stagedesc"}})],1)],1)]),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{attrs:{controls:!0,type:"number",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1),i("el-row")],1),i("el-divider")],1),i("div",{staticClass:"form-body form f-1"},[i("elitem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{idx:t.idx,lstitem:t.formdata.item,formdata:t.formdata},on:{bindData:t.bindData}})],1),i("el-form",{attrs:{"label-width":t.formLabelWidth}},[i("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[i("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建人"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"制表"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"修改日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},f=[],h=(i("b64b"),i("b775"));const p={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);h["a"].post("/S06M29S1/create",a).then(t=>{console.log(a,t),200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);h["a"].post("/S06M29S1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){return new Promise((e,i)=>{h["a"].get("/S06M29S1/delete?key="+t).then(t=>{console.log("删除："+t),200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})}};var b=p,g=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("el-row",[i("el-button-group",[i("el-button",{attrs:{type:"primary",size:"mini"},nativeOn:{click:function(e){return t.addItem(0)}}},[i("i",{staticClass:"el-icon-circle-plus-outline"}),t._v(" 添 加")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveUp()}}},[i("i",{staticClass:"el-icon-top"}),t._v(" 上 移")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini"},nativeOn:{click:function(e){return t.getMoveDown()}}},[i("i",{staticClass:"el-icon-bottom"}),t._v(" 下 移")]),i("el-button",{attrs:{disabled:0==t.multipleSelection.length,type:"danger",size:"mini"},nativeOn:{click:function(e){return t.delItem()}}},[i("i",{staticClass:"el-icon-delete"}),t._v(" 删 除")])],1)],1),i("div",{staticStyle:{"margin-right":"10px",position:"relative"}},[i("el-button",{staticStyle:{"font-weight":"bold"},attrs:{size:"mini",icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.$emit("bindData")}}}),i("el-button",{staticStyle:{float:"right"},attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)],1),i("div",{staticClass:"table-container f-1 table-position"},[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{data:t.lst,"element-loading-text":"Loading",height:t.tableHeight,border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[i("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),i("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e){return[!e.displaymark?t._e():i("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return["itemname"==e.itemcode||"remark"==e.itemcode?i("div",[a.row.isEdit?i("el-input",{attrs:{size:"small",placeholder:e.itemname},model:{value:a.row[e.itemcode],callback:function(i){t.$set(a.row,e.itemcode,i)},expression:"scope.row[i.itemcode]"}}):i("span",[t._v(t._s(a.row[e.itemcode]))])],1):"operate"==e.itemcode?i("div",[i("el-button",{attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(e){return t.openItemFormdata(a.row,a.$index)}}},[t._v("编辑")]),i("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(e){return t.delItemBtn(a.row,a.$index)}}},[t._v("删除")])],1):i("span",[t._v(t._s(a.row[e.itemcode]))])]}}],null,!0)})]}))],2)],1),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"5vh"},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:"S06M29S1Item",tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindData")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),t.itemFormVissable?i("el-dialog",{attrs:{title:"新增","append-to-body":!0,visible:t.itemFormVissable,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.itemFormVissable=e}}},[i("itemDialog",{ref:"itemDialog",attrs:{data:t.stagejsonData}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.submitItemForm()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.itemFormVissable=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},v=[],w=i("2909"),y=i("c7eb"),x=i("1da1"),S=(i("99af"),i("a434"),i("b0c0"),i("e9c4"),i("d3b7"),i("0643"),i("4e3e"),i("159b"),{formcode:"S06M29S1List",item:[{itemcode:"stagename",itemname:"阶段名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_ActivityStage.stagename"},{itemcode:"stagetype",itemname:"阶段类型",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_ActivityStage.stagetype"},{itemcode:"stagedesc",itemname:"描述",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_ActivityStage.stagedesc"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_ActivityStage.lister"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_ActivityStage.modifydate"}]}),k={formcode:"S06M29S1Item",item:[{itemcode:"itemname",itemname:"阶段名称",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"stagejson",itemname:"阶段配置",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1},{itemcode:"operate",itemname:"操作",minwidth:"100",displaymark:1,overflow:1}]},$=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-form",{attrs:{"label-width":"100px"}},t._l(this.list,(function(e,a){return i("div",{key:a},[i("el-row",[i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"子字段名称",prop:"apprname"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入子字段名称",size:"small"},model:{value:e.name,callback:function(i){t.$set(e,"name",i)},expression:"item.name"}})],1)],1),i("el-col",{attrs:{span:10}},[i("el-form-item",{attrs:{label:"","label-width":"60px"}},[i("div",{staticClass:"flex"},[i("el-checkbox",{attrs:{label:"必填","true-label":1,"false-label":0,size:"mini"},model:{value:e.mustmark,callback:function(i){t.$set(e,"mustmark",i)},expression:"item.mustmark"}}),i("el-checkbox",{attrs:{label:"允许上传附件","true-label":1,"false-label":0,size:"mini"},model:{value:e.filemark,callback:function(i){t.$set(e,"filemark",i)},expression:"item.filemark"}})],1)])],1),i("el-col",{attrs:{span:4}},[a==t.list.length-1?i("el-button",{attrs:{type:"text",icon:"el-icon-plus"},on:{click:function(e){return t.addItem()}}},[t._v("新增")]):t._e(),i("el-button",{staticStyle:{color:"#f56c6c"},attrs:{type:"text",icon:"el-icon-delete"},on:{click:function(i){return t.deleteItem(e,a)}}},[t._v("删除")])],1)],1)],1)})),0)],1)},_=[],F={props:["data"],data:function(){return{list:[]}},methods:{bindData:function(){this.data.length?this.list=Object(w["a"])(this.data):this.list=[{name:"",mustmark:0,filemark:0}]},addItem:function(){var t={name:"",mustmark:0,filemark:0};this.list.push(t)},deleteItem:function(t,e){this.list.splice(e,1)}}},C=F,I=Object(c["a"])(C,$,_,!1,null,null,null),D=I.exports,O={name:"Elitem",components:{itemDialog:D},props:["formdata","lstitem","idx"],data:function(){return{title:"阶段",listLoading:!1,lst:[],multi:0,tableHeight:0,index:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:k,setColumsVisible:!1,itemFormVissable:!1,stagejsonData:[],selStageIndex:0}},watch:{lstitem:function(t,e){this.lst=this.lstitem},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){this.lst=[]},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},mounted:function(){this.catchHight()},methods:{bindData:function(){},getColumn:function(){var t=this;return Object(x["a"])(Object(y["a"])().mark((function e(){var i;return Object(y["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:i=k,t.$getColumn(k.formcode,i).then((function(e){t.tableForm=Object.assign({},e.colList),t.$forceUpdate()}));case 2:case"end":return e.stop()}}),e)})))()},openDialog:function(){this.setColumsVisible=!0},addItem:function(t,e){var i=[{itemname:"",rownum:0,stagejson:"",remark:"",isEdit:!1}];this.lst=[].concat(Object(w["a"])(this.lst),i)},openItemFormdata:function(t,e){var i=this,a=t.stagejson?JSON.parse(t.stagejson):[];this.stagejsonData=Object(w["a"])(a),this.itemFormVissable=!0,this.selStageIndex=e,this.$nextTick((function(){i.$refs.itemDialog.bindData()}))},submitItemForm:function(){var t=this.$refs.itemDialog.list.length?this.$refs.itemDialog.list:"";this.lst[this.selStageIndex].stagejson=JSON.stringify(t),this.itemFormVissable=!1},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},handleSelectionChange:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].rownum=e;this.multipleSelection=t},delItemBtn:function(t,e){var i=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){i.lst.splice(e,1)})).catch((function(){}))},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var i=this,a=this.multipleSelection;a&&a.forEach((function(t,e){i.lst.forEach((function(e,a){t.name==e.name&&t.defval==e.defval&&t.itemval==e.itemval&&t.rownum==e.rownum&&i.lst.splice(a,1)}))})),this.$refs.multipleTable.clearSelection()},saveRow:function(t){console.log("saveRow");for(var e=0;e<this.lst.length;e++)this.$set(this.lst[e],"isEdit",!1);t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},V=O,T=Object(c["a"])(V,g,v,!1,null,"75139d4f",null),z=T.exports,P=i("5c73"),E={name:"Formedit",components:{elitem:z,selDictionaries:P["a"]},props:["idx"],data:function(){return{title:"阶段",formdata:{stagename:"",stagetype:"",stagedesc:"",rownum:1,summary:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},formRules:{stagename:[{required:!0,trigger:"blur",message:"阶段名称为必填项"}]},formLabelWidth:"100px",multi:0}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&h["a"].get("/S06M29S1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.formsave()}))},formsave:function(){for(var t=this,e=0;e<this.$refs.elitem.lst.length;e++){var i=this.$refs.elitem.lst[e];this.$delete(i,"isEdit")}this.formdata.item=this.$refs.elitem.lst,0==this.idx?b.add(this.formdata).then((function(e){200==e.code&&(t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data)})).catch((function(e){t.$message.warning(e||"保存失败")})):b.update(this.formdata).then((function(e){200==e.code&&(t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data)})).catch((function(e){t.$message.warning(e||"保存失败")}))},closeForm:function(){this.$emit("closeForm")},rowdel:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),b.delete(t).then((function(){e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},A=E,L=(i("1cd7"),Object(c["a"])(A,u,f,!1,null,"858feaf6",null)),j=L.exports,M=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading","summary-method":t.getSummaries,"show-summary":"",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[i("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e,a){return[!e.displaymark?t._e():i("el-table-column",{key:a,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(a){return["stagename"==e.itemcode?i("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showForm(a.row.id)}}},[t._v(t._s(a.row[e.itemcode]?a.row[e.itemcode]:"单据编号"))]):"modifydate"==e.itemcode?i("span",[t._v(t._s(t._f("dateFormat")(a.row[e.itemcode])))]):i("span",[t._v(t._s(a.row[e.itemcode]))])]}}],null,!0)})]}))],2),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),i("div",{staticStyle:{"margin-right":"40px"}},[i("Scene",{ref:"scene",attrs:{code:t.tableForm.formcode},on:{bindData:t.bindData}})],1)],1)],1)},B=[],N=i("b893"),q={components:{},data:function(){return{lst:[],searchstr:"",total:0,listLoading:!1,idx:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1,OrderBy:"Sa_ActivityStage.rownum"},tableForm:S}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},updated:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.doLayout()}))},methods:{bindData:function(){var t=this;this.listLoading=!0,this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),h["a"].post("/S06M29S1/getPageTh",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;return Object(x["a"])(Object(y["a"])().mark((function e(){return Object(y["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$getColumn(t.tableForm.formcode,S).then((function(e){t.tableForm=Object.assign({},e.colList),t.$emit("sendTableForm",t.tableForm)}));case 1:case"end":return e.stop()}}),e)})))()},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={groupcode:t,groupname:t,grouptype:t,describe:t,summary:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.$advancedSearch(this,t)},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData()},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"阶段")},getSummaries:function(t){return Object(N["e"])(t,["amount"])}}},R=q,H=(i("8277"),Object(c["a"])(R,M,B,!1,null,"13ff5d3f",null)),U=H.exports,J={name:"S06M29S1",components:{listHeader:m,FormEdit:j,tableList:U},data:function(){return{title:"阶段",FormVisible:!1,idx:0,tableForm:{},showhelp:!1}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){this.$refs.tableList.bindData()},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.btnExport()}))},search:function(t){this.$refs.tableList.search(t)},advancedSearch:function(t){this.$refs.tableList.advancedSearch(t)},showForm:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeIdx:function(t){this.idx=t}}},Y=J,W=Object(c["a"])(Y,a,n,!1,null,"0129d2d0",null);e["default"]=W.exports}}]);