(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-12cbd131"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return l})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function s(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(t,e,a){var l=o(),n=t-l,r=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=r;var t=Math.easeInOutQuad(c,l,n,e);s(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"21c8":function(t,e,a){"use strict";a("5d39")},"335d5":function(t,e,a){},"5c73":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,i){return a("li",{key:i,class:t.radio==i?"active":"",on:{click:function(a){t.getCurrentRow(e),t.radio=i}}},[a("span",[t._v(t._s(e.dictvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,i){return a("p",{key:i,class:t.ActiveIndex==i?"isActive":"",on:{click:function(e){t.ActiveIndex=i}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},s=[],o=(a("a434"),a("e9c4"),a("b775")),l=a("333d"),n=a("b0b8"),r={components:{Pagination:l["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],o["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var a=0;a<t.formdata.item.length;a++)t.lst.push(t.formdata.item[a])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.addItem(a)})).catch((function(t){console.log(t)}))},addItem:function(t){n.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:n.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.lst[t.ActiveIndex].dictvalue=a,n.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=n.getFullChars(a)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,o["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=r,d=(a("af2b"),a("2877")),f=Object(d["a"])(c,i,s,!1,null,"d2ba3d7a",null);e["a"]=f.exports},"5d39":function(t,e,a){},"7de4":function(t,e,a){},"7e4f":function(t,e,a){"use strict";a("335d5")},9406:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"dashboard-container",staticStyle:{margin:"10px 20px"}},[a("div",{staticClass:"dashboard-header flex j-s",staticStyle:{"flex-wrap":"wrap"}},[a("div",{staticClass:"flex f-d-c",staticStyle:{flex:"1"}},[a("el-card",{staticClass:"box-card",staticStyle:{height:"188px"}},[a("div",{staticClass:"flex a-c j-s",staticStyle:{margin:"-12px 10px",width:"100%"},attrs:{slot:"header"},slot:"header"},[a("div",{staticClass:"flex a-c"},[a("i",{staticClass:"el-icon-s-marketing",style:{"font-size":"24px",color:"#fa7753"}}),a("span",{staticStyle:{"margin-left":"10px","font-weight":"bold","font-size":"18px",cursor:"default"}},[t._v("需求统计")])]),a("div",[a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"今日完成",size:"mini"},on:{change:t.getCountOnlineByBillType},model:{value:t.isFinish,callback:function(e){t.isFinish=e},expression:"isFinish"}}),a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"我的",size:"mini"},on:{change:t.getCountOnlineByBillType},model:{value:t.isOwn,callback:function(e){t.isOwn=e},expression:"isOwn"}})],1)]),a("div",{staticStyle:{display:"flex","flex-wrap":"wrap",width:"100%","margin-top":"-15px"}},t._l(t.needAllList,(function(e,i){return a("div",{key:i,staticClass:"needAllItem",style:{"background-color":t.colorArr[i]}},[a("div",{staticClass:"fontWeight"},[a("i",{class:e.icon}),a("span",[t._v(" "+t._s(e.billtype))])]),a("div",{staticStyle:{float:"right"}},[t._v(t._s(e.count))])])})),0)])],1),a("div",{staticStyle:{position:"relative","min-width":"40%"}},[a("el-card",{staticClass:"box-card",staticStyle:{height:"188px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{staticClass:"dashboard-text"},[a("div",{staticClass:"dashboard-Avatar"},[a("img",{attrs:{src:null!=t.userinfo.Avatar?t.userinfo.Avatar:t.defaultImg,alt:"",onerror:t.defaultImg}})]),a("span",[t._v(t._s(t.userinfo.realname)+","+t._s(t.week)+","+t._s(t.hoursTip))])]),a("weather",{staticStyle:{position:"absolute",right:"-60px",bottom:"10px"}})],1)])],1)]),a("div",{staticClass:"dashboard-body flex j-s",staticStyle:{"flex-wrap":"wrap"}},[a("el-card",{staticStyle:{margin:"10px",width:"48%",background:"#a665a7"}},[a("div",{staticClass:"flex a-c",attrs:{slot:"header"},slot:"header"},[a("div",[a("i",{staticClass:"el-icon-s-home",style:{color:"#fff","font-size":"24px"}})]),a("span",{staticStyle:{"margin-left":"10px","font-weight":"bold","font-size":"18px",color:"#fff",cursor:"default"},on:{click:function(e){return t.navTo("/S06/M09B1")}}},[t._v("任务")])]),t._l(t.todoList,(function(e,i){return a("div",{key:i,staticClass:"todoList-li flex j-s"},[a("div",{staticStyle:{cursor:"default"},on:{click:function(a){return a.stopPropagation(),t.openTodoList(e)}}},[a("span",[t._v(t._s(e.billtitle))]),a("el-tag",{staticStyle:{"margin-left":"10px"},attrs:{type:"需求"==e.billtype?"warning":"primary",size:"mini"}},[t._v(t._s(e.billtype))]),a("span",{attrs:{title:"紧急"}},[e.urgentmark?a("i",{staticClass:"el-icon-warning",staticStyle:{"margin-left":"10px",color:"#f56c6c"}}):t._e()])],1),a("div",{staticStyle:{cursor:"default"}},[a("span",{staticStyle:{"font-size":"14px","margin-right":"10px"}},[t._v(t._s(t._f("dateFormat")(e.billdate)))]),a("span",{staticClass:"todoList-icon",attrs:{title:"重要"}},[a("i",{class:e.importantmark?"el-icon-star-on":"el-icon-star-off",style:{color:e.importantmark?"#a665a7":"#000"}})]),a("span",[a("el-checkbox",{attrs:{"true-label":1,"false-label":0},on:{change:function(a){return t.changeStatus(a,e)}},model:{value:e.finishmark,callback:function(a){t.$set(e,"finishmark",a)},expression:"b.finishmark"}})],1)])])})),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>10,expression:"total > 10"}],staticStyle:{background:"transparent"},attrs:{layout:"prev, pager, next",small:"",total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getTodoPage}})],2),a("el-card",{staticStyle:{margin:"10px",width:"48%",background:"#a665a7"}},[a("div",{staticClass:"flex a-c",attrs:{slot:"header"},slot:"header"},[a("div",[a("i",{staticClass:"el-icon-s-home",style:{color:"#fff","font-size":"24px"}})]),a("span",{staticStyle:{"margin-left":"10px","font-weight":"bold","font-size":"18px",color:"#fff",cursor:"default"},on:{click:function(e){return t.navTo("/S06/M02B4")}}},[t._v("需求")])]),a("div",{staticClass:"flex",staticStyle:{"flex-wrap":"wrap"}},t._l(t.needList,(function(e,i){return a("div",{key:i,staticClass:"todoList-li flex j-s a-c",on:{click:function(a){return t.openFormedit(e)}}},[a("div",{staticStyle:{cursor:"default"}},[a("span",[t._v(t._s(e.billtitle))]),a("el-tag",{staticStyle:{"margin-left":"10px"},attrs:{type:"缺陷"==e.billtype?"warning":"primary",size:"mini"}},[t._v(t._s(e.billtype))]),a("span",{class:"label-"+t.getStatusName(e.demandstatus)["statuscolor"],staticStyle:{background:"transparent","font-size":"12px","margin-left":"8px"}},[t._v(" "+t._s(t.getStatusName(e.demandstatus)["statusname"])+" ")]),a("div",{staticStyle:{"font-size":"14px","margin-top":"2px"}},t._l(JSON.parse(e.labeljson),(function(e,i){return a("span",{class:"label-color label-"+e.color},[t._v(" "+t._s(e.name)+" ")])})),0)],1),a("div",{staticStyle:{cursor:"default"}},[a("span",{staticClass:"timestatusSty",style:t.getitemStatus(e.timestatus)},[t._v(" "+t._s(e.timestatus))]),0==e.level?a("span",{staticClass:"levelTag taggray"},[t._v("较低")]):1==e.level?a("span",{staticClass:"levelTag tagblue"},[t._v("普通")]):2==e.level?a("span",{staticClass:"levelTag tagorange"},[t._v("紧急")]):3==e.level?a("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")]):t._e()])])})),0),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total1>10,expression:"total1 > 10"}],staticStyle:{background:"transparent"},attrs:{layout:"prev, pager, next",small:"",total:t.total1,page:t.queryParams1.PageNum,limit:t.queryParams1.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams1,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams1,"PageSize",e)},pagination:t.getNeedPage}})],1)],1),t.formeditVisible?a("el-dialog",{key:t.formeditKey,attrs:{width:"56vw",title:"任务","append-to-body":!0,visible:t.formeditVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"6vh"},on:{"update:visible":function(e){t.formeditVisible=e},close:t.getNeedList}},[a("div",{staticClass:"flex",staticStyle:{"justify-content":"space-between"},attrs:{slot:"title"},slot:"title"},[a("div",{staticClass:"flex"},[a("span",[t._v("任务 -")]),a("el-popover",{ref:"timestatusRef",attrs:{placement:"bottom-start",trigger:"click","popper-class":""}},[a("div",[t.selForm.timestatus&&"暂停"!=t.selForm.timestatus?"开始"==t.selForm.timestatus?a("div",[a("el-button",{staticStyle:{color:"#fa8c15"},attrs:{type:"text",icon:"el-icon-video-pause"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"暂停")}}},[t._v(" 暂停 ")])],1):t._e():a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-video-play"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"开始")}}},[t._v(" 开始 ")])],1),"开始"==t.selForm.timestatus?a("div",[a("el-button",{staticStyle:{color:"#14a10f"},attrs:{type:"text",icon:"el-icon-finished"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"完成")}}},[t._v(" 完成 ")])],1):t._e()]),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("span",{staticStyle:{"text-decoration":"underline",cursor:"pointer","margin-left":"4px"}},[t._v(t._s(t.selForm.timestatus?t.selForm.timestatus:"未开始"))])])])],1),a("div",{staticClass:"flex a-c",staticStyle:{"margin-right":"20px",cursor:"pointer"}},[a("el-dropdown",{staticStyle:{margin:"0px 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.$refs.formedit.bindData()}}},[t._v("刷 新")]),a("el-dropdown-item",{attrs:{disabled:2!=t.$store.state.user.userinfo.isadmin&&(t.$store.state.user.userinfo.engineer.id!=t.selForm.appointeeid&&t.$store.state.user.userinfo.userid!=t.selForm.createbyid),icon:"el-icon-delete"},nativeOn:{click:function(e){return t.deleteForm()}}},[t._v("删 除 ")])],1)],1)],1)]),a("FormEdit",{ref:"formedit",attrs:{idx:t.selForm.id,userList:t.userList,statusList:t.statusList},on:{closeDialog:function(e){t.formeditVisible=!1},bindData:function(e){return t.getNeedList(t.project)}}})],1):t._e(),t.todoListVisible?a("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.todoListVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.todoListVisible=e}}},[a("S06M09B1",{ref:"S06M09B1",attrs:{idx:t.dialogIdx,isDialog:!0},on:{changeIdx:function(e){t.dialogIdx=e},closeDialog:function(e){t.todoListVisible=!1,t.getTodoList()}}})],1):t._e()],1)},s=[],o=a("c7eb"),l=a("1da1"),n=a("5530"),r=(a("99af"),a("7db0"),a("c740"),a("d81d"),a("e9c4"),a("d3b7"),a("0643"),a("fffc"),a("a573"),a("2f62")),c=function(){var t=this,e=t.$createElement;t._self._c;return t._m(0)},d=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"weater"}},[a("iframe",{attrs:{width:"400",scrolling:"no",height:"100",frameborder:"0",allowtransparency:"true",src:"https://i.tianqi.com?c=code&id=35&icon=1&site=34"}})])}],f=(a("b775"),a("bc3a"),{name:"Weather",data:function(){return{}},created:function(){this.bindData()},methods:{bindData:function(){}}}),u=f,m=a("2877"),p=Object(m["a"])(u,c,d,!1,null,null,null),g=p.exports,h=(a("25f0"),a("4d90"),function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"";if(null==t)return"";var a=new Date(t),i=a.getFullYear(),s=(a.getMonth()+1).toString().padStart(2,"0"),o=a.getDate().toString().padStart(2,"0");if("yyyy-mm-dd"===e.toLowerCase())return"".concat(i,"-").concat(s,"-").concat(o);var l=a.getHours().toString().padStart(2,"0"),n=a.getMinutes().toString().padStart(2,"0"),r=a.getSeconds().toString().padStart(2,"0");return"".concat(i,"-").concat(s,"-").concat(o," ").concat(l,":").concat(n,":").concat(r)}),b=h,v=a("a116"),y=a("de74"),S={name:"Dashboard",components:{weather:g,FormEdit:v["a"],S06M09B1:y["a"]},data:function(){return{hoursTip:"",week:"",date:"",config:{header:[],data:[]},queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:0},queryParams1:{PageNum:1,PageSize:10,OrderType:1,SearchType:0},total:0,total1:0,todoList:[],needList:[],selForm:{},formeditVisible:!1,projectAllList:[],projectAllListCopy:[],projectVal:"",formeditKey:0,userList:[],statusList:[],labelList:[],needAllList:[],colorArr:["#fa7753","#65a6ff","#9cd159","#36c6d3","#9049f3"],dialogIdx:0,todoListVisible:!1,isFinish:!0,isOwn:!0}},computed:Object(n["a"])({indexHeight:function(){return window.innerHeight-130+"px"},defaultImg:function(){return'this.src="https://oss.aliyuncs.com/aliyun_id_photo_bucket/default_handsome.jpg"'}},Object(r["b"])(["userinfo","tenantinfo"])),created:function(){this.getInfoList(),this.getTime(),this.getCountOnlineByBillType()},mounted:function(){this.getTodoList(),this.getNeedList(),this.getprojectAllList()},methods:{getCountOnlineByBillType:function(){var t=this;return Object(l["a"])(Object(o["a"])().mark((function e(){var a,i,s,l;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.needAllList=[{billtype:"任务",count:0,icon:"el-icon-plus"},{billtype:"需求",count:0,icon:"el-icon-s-opportunity"},{billtype:"测试",count:0,icon:"el-icon-s-check"},{billtype:"缺陷",count:0,icon:"el-icon-s-release"},{billtype:"实施",count:0,icon:"el-icon-s-promotion"}],t.isFinish){e.next=14;break}return e.prev=2,e.next=5,t.$request.post("/S06M02B2/getCountOnlineByBillType?own=".concat(t.isOwn?1:0));case 5:a=e.sent,200==a.data.code&&Array.isArray(a.data.data)&&(i=a.data.data,t.needAllList=t.needAllList.map((function(t){var e,a=i.find((function(e){return e.billtype===t.billtype}));return{billtype:t.billtype,icon:t.icon,count:null!==(e=null===a||void 0===a?void 0:a.count)&&void 0!==e?e:t.count}}))),e.next=12;break;case 9:e.prev=9,e.t0=e["catch"](2),console.log(e.t0);case 12:e.next=24;break;case 14:return e.prev=14,e.next=17,t.$request.post("/S06M02B2/getCountFinishByBillType?own=".concat(t.isOwn?1:0));case 17:s=e.sent,200==s.data.code&&Array.isArray(s.data.data)&&(l=s.data.data,t.needAllList=t.needAllList.map((function(t){var e,a=l.find((function(e){return e.billtype===t.billtype}));return{billtype:t.billtype,icon:t.icon,count:null!==(e=null===a||void 0===a?void 0:a.count)&&void 0!==e?e:t.count}}))),e.next=24;break;case 21:e.prev=21,e.t1=e["catch"](14),console.log(e.t1);case 24:case"end":return e.stop()}}),e,null,[[2,9],[14,21]])})))()},getInfoList:function(){var t=this;return Object(l["a"])(Object(o["a"])().mark((function e(){var a,i;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1,OrderBy:"rownum"},i="/S06M02S2/getOnlinePageList",t.$request.post(i,JSON.stringify(a)).then((function(e){200==e.data.code&&(t.userList=[].concat(e.data.data.list))})),t.$request.post("/S06M02S3/getPageList",JSON.stringify(a)).then((function(e){200==e.data.code&&(t.statusList=[].concat(e.data.data.list))})),t.$request.post("/S06M02S6/getPageList",JSON.stringify(a)).then((function(e){200==e.data.code&&(t.labelList=[].concat(e.data.data.list))}));case 5:case"end":return e.stop()}}),e)})))()},getTodoList:function(){var t=this,e="/S06M09B1/getOnlinePageList?own=1";this.$request.post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.todoList=e.data.data.list,t.total=e.data.data.total)}))},getNeedList:function(){var t=this,e="/S06M02B2/getOnlinePageList?own=1";this.$request.post(e,JSON.stringify(this.queryParams1)).then((function(e){200==e.data.code&&(t.needList=e.data.data.list,t.total1=e.data.data.total)}))},getTodoPage:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.getTodoList()},getNeedPage:function(t){this.queryParams1.PageNum=t.page,this.queryParams1.PageSize=t.limit,this.getNeedList()},getStatusName:function(t){var e={statusname:"",statuscolor:"grey"};if(t){var a=this.statusList.findIndex((function(e){return e.id==t}));-1!=a&&(e.statusname=this.statusList[a].statusname,e.statuscolor=this.statusList[a].statuscolor)}return e},openFormedit:function(t){var e=this;this.selForm=Object.assign({},t),this.formeditVisible=!0,this.$nextTick((function(){e.$refs.formedit.projectAllList=e.projectAllList,e.$refs.formedit.projectAllListCopy=e.projectAllListCopy}))},openTodoList:function(t){var e=this;this.dialogIdx=t.id,this.$nextTick((function(){e.todoListVisible=!0}))},getprojectAllList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};this.$request.post("/S06M01S1/getBillList?own=true",JSON.stringify(e)).then((function(e){200==e.data.code?(t.projectAllList=e.data.data.list,t.projectAllListCopy=[].concat(t.projectAllList)):t.$message.warning(e.data.msg||"查询项目信息失败")}))},changeTimeStatus:function(t,e){var a=this;"完成"==e?this.$confirm("是否确定完成该任务，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.updateFun(t,e)})).catch((function(){})):this.updateFun(t,e)},updateFun:function(t,e){var a=this,i="/S06M02S7/start";"暂停"==e?i="/S06M02S7/pause":"完成"==e&&(i="/S06M02S7/complete"),this.$request.get(i+"?key="+t.id).then((function(t){200==t.data.code?(a.$message.success(t.data.msg||"操作成功，该任务已"+e),a.$set(a.selForm,"timestatus",e),a.bindData(),a.formeditKey+=1):a.$message.warning(t.data.msg||"操作失败")}))},changeStatus:function(t,e){var a=this;1==t?this.$confirm("是否确定完成该Todo，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.changeStatusFun(e,"完成")})).catch((function(){e.finishmark=0})):this.changeStatusFun(e,"撤回")},changeStatusFun:function(t,e){var a=this,i="";"开始"==e?i="/S06M09B1/start":"完成"==e?i="/S06M09B1/finish":"撤回"==e&&(i="/S06M09B1/recall"),this.$request.get(i+"?key="+t.id).then((function(t){200==t.data.code?(a.$message.success(t.data.msg||"操作成功，该Todo已"+e),a.getTodoList()):a.$message.warning(t.data.msg||"操作失败")}))},navTo:function(t){this.$router.push(t)},getTime:function(){this.date=new Date,this.date=b(this.date,"yyyy-mm-dd"),this.time=new Date,this.time=b(this.time).substring(11);var t=["星期天","星期一","星期二","星期三","星期四","星期五","星期六"][(new Date).getDay()];this.week=t;var e=new Date,a=this;e.getHours()>=0&&e.getHours()<12?a.hoursTip="上午好":e.getHours()>=12&&e.getHours()<18?a.hoursTip="下午好":a.hoursTip="晚上好"},getitemStatus:function(t){var e={color:"#000",padding:"2px 6px","border-radius":"4px",display:"inline-block","font-size":"12px","line-height":"12px","margin-right":"10px"};return"开始"==t?(e.color="#409EFF",e.border="1px solid"):"暂停"==t?(e.color="#fa8c15",e.border="1px solid"):"完成"==t&&(e.color="#14a10f",e.border="1px solid"),e}}},x=S,w=(a("7e4f"),Object(m["a"])(x,i,s,!1,null,"0ba80cfb",null));e["default"]=w.exports},a116:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"wrap"},[i("div",{staticClass:"left"},[i("div",{staticClass:"left-item"},[i("el-input",{attrs:{readonly:"已完成"==t.status.statustype,placeholder:"请输入任务标题",clearable:""},on:{change:function(e){return t.changeMsg("任务标题")}},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1),i("div",{staticClass:"left-item"},[i("el-input",{attrs:{placeholder:"请输入任务描述",clearable:"",type:"textarea",autosize:{minRows:4,maxRows:6}},on:{change:function(e){return t.changeMsg("任务描述")}},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1),i("div",{staticClass:"left-item"},[t._m(0),i("el-popover",{ref:"billTypeRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-selectTag"}},[i("div",{ref:"selectTag",staticClass:"levelTag-content"},t._l(t.billtypeArr,(function(e,a){return i("div",{key:a,staticClass:"levelTag-content-item",on:{click:function(a){return t.changeBilltype(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.billtype==e?1:0}}),i("span",[t._v(t._s(e))])])})),0),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.billtype?t.formdata.billtype:"待选择"))])])])],1),i("div",{staticClass:"left-item"},[t._m(1),i("div",[i("el-popover",{ref:"statusRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[t.formdata.demandstatus?t._e():i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeStatus({id:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.demandstatus?0:1,color:"#1b9aee"}}),i("span",[t._v("待处理")])]),t._l(t.statusList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeStatus(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.demandstatus==e.id?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.statusname))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.demandstatus?i("div",[i("span",[t._v(t._s(this.getStatusName(t.formdata.demandstatus)))])]):i("div",[t._v("待处理")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(2),i("div",[i("el-popover",{ref:"appointeeRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeAppointee({id:"",engineername:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.appointee?0:1,color:"#1b9aee"}}),i("span",[t._v("待认领")])]),t._l(t.userList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeAppointee(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.appointeeid==e.id?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.engineername))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.appointeeid?i("span",{staticClass:"appointeeTag"},[t._v(" "+t._s(t.formdata.appointee)+" "),i("i",{staticClass:"CloseBtnSty el-icon-close",on:{click:function(e){return e.stopPropagation(),t.changeAppointee({id:"",engineername:""})}}})]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("待认领")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(3),i("div",[i("el-popover",{ref:"collaboratorRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeCollaborator({id:"",engineername:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.collaboratorids?0:1,color:"#1b9aee"}}),i("span",[t._v("待添加")])]),t._l(t.userList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeCollaborator(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.collaboratorids&&t.formdata.collaboratorids.includes(e.id)?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.engineername))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.collaboratorids?i("div",[i("span",{staticClass:"collaboratorsTag"},[t._v(t._s(t.formdata.collaborators)+" "),i("i",{staticClass:"CloseBtnSty el-icon-close",on:{click:function(e){return e.stopPropagation(),t.changeCollaborator({id:"",engineername:""})}}})])]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("待添加")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(4),i("el-popover",{ref:"labeljsonRef",attrs:{placement:"right-end",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"250px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.groupVal,expression:"groupVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索客户"},domProps:{value:t.groupVal},on:{input:[function(e){e.target.composing||(t.groupVal=e.target.value)},function(e){return t.getGroupAllList(t.groupVal)}]}})]),t.groupAllList.length?i("div",{staticStyle:{height:"220px","overflow-y":"auto"}},t._l(t.groupAllList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeGroup(e)}}},[i("span",{class:"label-color"},[t._v(t._s(e.groupname))]),i("div",[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.groupid==e.id?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无客户","image-size":90}})],1)])]),i("div",{staticClass:"flex a-c",attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.groupname?t.formdata.groupname:"暂无客户"))]),t.formdata.groupname?i("div",{staticClass:"CloseBtnSty",on:{click:function(e){return e.stopPropagation(),t.changeGroup({id:""})}}},[i("i",{staticClass:"el-icon-close"})]):t._e()])])],1),i("div",{staticClass:"left-item"},[t._m(5),i("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择开始时间","default-time":"08:30:00",clearable:!0,editable:!1},on:{clear:function(e){t.formdata.startdate=""},input:function(e){t.formdata.startdate=e},change:function(e){return t.changeDate("开始时间")}},model:{value:t.formdata.startdate,callback:function(e){t.$set(t.formdata,"startdate",e)},expression:"formdata.startdate"}})],1),i("div",{staticClass:"left-item"},[t._m(6),i("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择截止时间","default-time":"17:00:00",clearable:!0,editable:!1},on:{clear:function(e){t.formdata.deaddate=""},input:function(e){t.formdata.deaddate=e},change:function(e){return t.changeDate("截止时间")}},model:{value:t.formdata.deaddate,callback:function(e){t.$set(t.formdata,"deaddate",e)},expression:"formdata.deaddate"}})],1),i("div",{staticClass:"left-item"},[t._m(7),i("el-popover",{ref:"labeljsonRef",attrs:{placement:"right-end",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"250px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.projectVal,expression:"projectVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索项目"},domProps:{value:t.projectVal},on:{input:[function(e){e.target.composing||(t.projectVal=e.target.value)},t.searchProject]}})]),t.projectAllListCopy.length?i("div",{staticStyle:{height:"220px","overflow-y":"auto"}},t._l(t.projectAllListCopy,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeProject(e)}}},[i("span",{class:"label-color"},[t._v(t._s(e.projname))]),i("div",[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.projectid==e.id?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无项目","image-size":90}})],1)])]),i("div",{staticClass:"flex a-c",attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.projname?t.formdata.projname:"暂无项目"))]),t.formdata.projname?i("div",{staticClass:"CloseBtnSty",on:{click:function(e){return e.stopPropagation(),t.changeProject({id:""})}}},[i("i",{staticClass:"el-icon-close"})]):t._e()])])],1),i("div",{staticClass:"left-item"},[t._m(8),i("div",[i("el-popover",{ref:"selectTagRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-selectTag"}},[i("div",{ref:"selectTag",staticClass:"levelTag-content"},[i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(0)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:0==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag taggray"},[t._v("较低")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(1)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:1==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagblue"},[t._v("普通")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(2)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:2==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagorange"},[t._v("紧急")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(3)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:3==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")])])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[0==t.formdata.level?i("div",{staticClass:"levelTag taggray"},[t._v(" 较低 ")]):1==t.formdata.level?i("div",{staticClass:"levelTag tagblue"},[t._v(" 普通 ")]):2==t.formdata.level?i("div",{staticClass:"levelTag tagorange"},[t._v(" 紧急 ")]):3==t.formdata.level?i("div",{staticClass:"levelTag tagred"},[t._v(" 非常紧急 ")]):t._e()])])],1)]),i("div",{staticClass:"left-item"},[t._m(9),i("div",[i("el-popover",{ref:"labeljsonRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"220px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.showSetLbel,expression:"showSetLbel"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.labelVal,expression:"labelVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索标签"},domProps:{value:t.labelVal},on:{input:[function(e){e.target.composing||(t.labelVal=e.target.value)},t.searchLabel]}}),i("i",{staticClass:"el-icon-circle-plus-outline",on:{click:function(e){t.showSetLbel=!1,t.labeloperaType=1,t.labelname=""}}})]),t.labelList.length?i("div",{staticStyle:{height:"180px","overflow-y":"auto"}},t._l(t.labelList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeLabeljson(e)}}},[i("span",{class:"label-color label-"+e.labelcolor},[t._v(t._s(e.labelname))]),i("div",[i("i",{staticClass:"el-icon-edit",staticStyle:{color:"#15ad31"},on:{click:function(a){return a.stopPropagation(),t.editLabel(e)}}}),i("i",{staticClass:"el-icon-check",style:{opacity:-1!=t.formdata.labeljson.findIndex((function(t){return t.labelid==e.id}))?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无标签","image-size":80}})],1)]),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.showSetLbel,expression:"!showSetLbel"}]},[i("div",{staticClass:"createLabel flex j-s a-c"},[i("i",{staticClass:"el-icon-arrow-left",on:{click:function(e){t.showSetLbel=!0}}}),i("strong",[t._v(" 创建标签")]),i("i",{staticClass:"el-icon-close",on:{click:function(e){t.showSetLbel=!0,t.$refs["labeljsonRef"].doClose()}}})]),i("div",{},[i("el-input",{staticStyle:{padding:"10px","margin-top":"12px"},attrs:{placeholder:"标签名称"},model:{value:t.labelname,callback:function(e){t.labelname=e},expression:"labelname"}}),i("div",{staticClass:"flex",staticStyle:{padding:"10px","margin-top":"12px"}},t._l(t.discColor,(function(e,a){return i("div",{key:a,class:"disc-color disc-"+e,on:{click:function(a){t.labelcolor=e}}},[t.labelcolor==e?i("i",{staticClass:"el-icon-check"}):t._e()])})),0),i("el-button",{staticStyle:{width:"calc(100% - 20px)",margin:"12px 10px"},attrs:{type:"primary"},on:{click:t.createLabel}},[t._v("创建")])],1)])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.labeljson.length?i("div",{staticStyle:{display:"inline-block",cursor:"pointer"}},[i("i",{staticClass:"el-icon-circle-plus-outline"})]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("添加标签")])])])],1),t.formdata.labeljson.length?i("div",{staticClass:"popper-labeljson labstyle"},t._l(t.formdata.labeljson,(function(e,a){return i("span",{key:a,class:"label-color label-"+e.color},[t._v(" "+t._s(e.name)+" "),i("i",{staticClass:"el-icon-close",on:{click:function(i){return t.closeLabeljson(e,a)}}})])})),0):t._e()]),i("div",{staticClass:"left-item"},[t._m(10),i("el-input-number",{attrs:{min:0,placeholder:"请输入工时",controls:!0,step:.5},on:{change:t.changeWorkTime},model:{value:t.formdata.worktime,callback:function(e){t.$set(t.formdata,"worktime",e)},expression:"formdata.worktime"}})],1),i("div",{staticClass:"left-item"},[t._m(11),i("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},on:{change:t.changeWeekMark},model:{value:t.formdata.weekmark,callback:function(e){t.$set(t.formdata,"weekmark",e)},expression:"formdata.weekmark"}})],1)]),i("div",{staticClass:"right"},[i("div",{staticClass:"right-header"},[i("p",[t._v("参与者·"+t._s(t.Participants.length))]),i("div",t._l(t.Participants,(function(e,a){return i("span",{key:a,staticClass:"right-header-tag"},[t._v(t._s(e.engineername))])})),0)]),i("div",{staticClass:"right-content myscrollbar"},[i("div",{staticClass:"tigs"},[i("div",{staticClass:"tigs-item",class:0==t.islogtype?"tigs-active":"",on:{click:function(e){t.islogtype=0,t.getDemandLog()}}},[t._v(" 所有动态 ")]),i("div",{staticClass:"tigs-item",class:1==t.islogtype?"tigs-active":"",on:{click:function(e){t.islogtype=1,t.getDemandLog()}}},[t._v(" 仅评论 ")])]),i("div",{staticClass:"logs"},[t.logTotal>10?i("div",{staticClass:"logs-more",on:{click:function(e){return t.getDemandLog(!0)}}},[t._v(" 默认显示最新10条动态，点击查看所有... ")]):t._e(),t._l(t.logList,(function(e,s){return i("div",{key:s,staticClass:"logs-item"},["评论"==e.type?i("div",{staticClass:"type-content"},[i("p",{staticClass:"flex j-s"},[i("span",{staticStyle:{color:"#333"}},[i("i",{staticClass:"el-icon-user"}),t._v(" "+t._s(e.createby))]),i("span",[t._v(t._s(t._f("dateFormats")(e.createdate)))])]),i("p",{staticStyle:{color:"#505050","text-indent":"16px"},domProps:{innerHTML:t._s(e.content.replace(/\n/gm,"<br>"))}}),e.attachment.name?i("div",{staticClass:"fileUploadsty"},[i("div",{staticClass:"fileUploadShow"},[i("img",{attrs:{src:e.attachment.type?a("b484")("./"+e.attachment.type+".png"):a("692f"),alt:""}})]),i("div",{staticClass:"fileUploadInfo"},[i("p",{staticClass:"ellipsis"},[t._v("名称："+t._s(e.attachment.name))]),i("p",[t._v("大小："+t._s(e.attachment.size+"KB"))]),i("span",{staticClass:"downFile",on:{click:function(a){return t.downFileName(e.attachment)}}},[t._v(" 下载附件 ")]),i("span",{staticClass:"downFile",on:{click:function(a){return t.getFileName(e.attachment)}}},[t._v(" 预览附件 ")])])]):t._e()]):i("div",[i("p",{staticClass:"flex j-s",staticStyle:{color:"#8c8c8c","font-size":"12px"}},[i("span",{staticStyle:{flex:"1"}},[i("i",{staticClass:"el-icon-edit-outline"}),i("span",{staticStyle:{"margin-left":"6px"},domProps:{innerHTML:t._s(e.content.replace(/\n/gm,"<br>"))}})]),i("span",[t._v(t._s(t._f("dateFormats")(e.createdate)))])])])])}))],2)]),i("div",{staticClass:"right-footer"},[i("textarea",{directives:[{name:"model",rawName:"v-model",value:t.textareaVal,expression:"textareaVal"}],staticClass:"textareaSty",attrs:{cols:"30",rows:"3",placeholder:"请输入评论"},domProps:{value:t.textareaVal},on:{input:function(e){e.target.composing||(t.textareaVal=e.target.value)}}}),i("div",{staticClass:"filesty"},t._l(t.fileList,(function(e,a){return i("span",{key:a,staticStyle:{cursor:"default"}},[t._v(" "+t._s(e.name)+" "),i("i",{staticClass:"el-icon-close",staticStyle:{cursor:"pointer"},on:{click:function(i){return t.deleteFile(e,a)}}})])})),0),i("div",[i("i",{staticClass:"el-icon-paperclip paperclipFile",on:{click:t.openFileUpload}}),i("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:t.addLog}},[t._v("回复")])],1)])]),t.fileUploadVisible?i("el-dialog",{attrs:{title:"添加附件",visible:t.fileUploadVisible,width:"500px","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.fileUploadVisible=e}}},[i("FileUpload",{ref:"fileUpload",on:{closeDialog:function(e){t.fileUploadVisible=!1}}}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.fileUploadBtn}},[t._v("确定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.fileUploadVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.isViewPdf20?i("el-dialog",{attrs:{title:"附件预览",visible:t.isViewPdf20,top:"2vh",width:"80%","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[t.isImg?i("div",{staticStyle:{width:"100%","text-align":"center"}},[i("img",{staticStyle:{height:"auto",width:"auto"},attrs:{src:t.blobUrl,alt:""}})]):i("iframe",{staticStyle:{height:"80vh",width:"100%"},attrs:{id:"iframeId",src:t.blobUrl,frameborder:"0",scrolling:"auto"}})]):t._e()],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-info"}),t._v(" 类型")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-folder-checked"}),t._v(" 状态")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user"}),t._v(" 执行者")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user-solid"}),t._v(" 协作者")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user-solid"}),t._v(" 客户")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-date"}),t._v(" 开始时间")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-date"}),t._v(" 截止时间")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-coin"}),t._v(" 项目")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-s-flag"}),t._v(" 优先级")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-price-tag"}),t._v(" 标签")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-time"}),t._v(" 工时")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-share"}),t._v(" 周报")])}],o=a("c7eb"),l=a("1da1"),n=(a("99af"),a("c740"),a("caad"),a("a434"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("2532"),a("3ca3"),a("4d90"),a("5319"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("08dd")),r=a("6e25"),c={props:["idx","statusList","userList","billtypeArr"],components:{FileUpload:n["a"],picker:r["default"]},data:function(){return{projectList:{item:[]},Participants:[],status:{},formdata:{labeljson:[],remark:"",billtitle:""},islogtype:0,logTotal:0,logList:[],labelList:[],labelListCopy:[],textareaVal:"",labelVal:"",labelname:"",labelId:"",showSetLbel:!0,labelcolor:"blue",labeloperaType:1,fileUploadVisible:!1,fileList:[],projectVal:"",projectAllList:[],projectAllListCopy:[],groupVal:"",groupAllList:[],blobUrl:"",isViewPdf20:!1,isImg:!1,workItemList:[],discColor:["blue","green","zi","orange","red"]}},mounted:function(){this.bindData(),this.getLabelList(),this.getGroupAllList()},methods:{bindData:function(){var t=this;0!=this.idx&&this.$request.get("/S06M02B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formdata.weekmark=t.formdata.weekmark?1:0,t.formdata.labeljson=t.formdata.labeljson?JSON.parse(t.formdata.labeljson):[],t.fileList=[],t.$forceUpdate(),t.getDemandLog(),t.bindRightContent()):t.$message.warning(e.data.msg||"获取任务信息失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getDemandLog:function(t){var e=this,a={PageNum:1,PageSize:10,OrderType:1,SearchType:1,SearchPojo:{demandid:this.formdata.id}};this.islogtype&&(a.scenedata=[{field:"Sa_DemandLog.type",fieldtype:1,math:"equal",value:"评论"}]),t&&(a.PageSize=100),this.$request.post("/S06M02S1/getPageList",JSON.stringify(a)).then((function(t){if(200==t.data.code){e.logList=t.data.data.list;for(var a=0;a<e.logList.length;a++){var i=e.logList[a];i.attachment=JSON.parse(i.attachment)}e.logTotal=t.data.data.total,console.log(e.logList)}else e.$message.warning(t.data.msg||"获取任务日志失败")}))},openFileUpload:function(){this.fileList.length>=1?this.$message.warning("只能添加一个附件"):this.fileUploadVisible=!0},fileUploadBtn:function(){var t=this,e=new FormData;e.append("file",this.$refs.fileUpload.file),this.$request.post("/File/upload?dirname="+this.$refs.fileUpload.uploadFileName,e).then((function(e){if(200==e.data.code){var a={name:t.$refs.fileUpload.uploadFileName,type:t.$refs.fileUpload.uploadFileType,size:t.$refs.fileUpload.uploadFileSize,fileurl:"/"+e.data.data.dirname+"/"+e.data.data.filename};t.fileList.push(a),t.fileUploadVisible=!1}else t.$message.warning(e.data.msg||"上传失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},deleteFile:function(t,e){var a=this;this.$request.post("/File/deleteFile?bucketName=inkspms&objectName="+t.fileurl).then((function(t){200==t.data.code?a.fileList.splice(e,1):a.$message.warning(t.data.msg||"删除附件失败")})).catch((function(t){a.$message.error(t||"请求错误")}))},downFileName:function(t){this.axios.get("http://dev.inksyun.com:9080/inkspms"+t.fileurl,{responseType:"blob"}).then((function(e){var a=window.URL.createObjectURL(e.data),i=document.createElement("a");i.href=a,i.download=t.name,i.click(),i.remove()}))},getFileName:function(t){var e=this;console.log(t),this.axios.get("http://dev.inksyun.com:9080/inkspms"+t.fileurl,{responseType:"blob"}).then((function(a){var i="application/pdf";if(e.isImg=!1,"pdf"==t.type.toLowerCase())i="application/pdf";else if("txt"===t.type.toLowerCase())i="text/plain";else{if(["jpg","jpeg","gif","bmp","png"].includes(t.name.toLowerCase()))return e.isImg=!0,e.blobUrl=window.URL.createObjectURL(a.data),e.isViewPdf20=!0,void e.$forceUpdate();e.$utils.message("info","此类型文件不支持预览,请下载查看！")}var s=[];s.push(a.data),e.blobUrl=window.URL.createObjectURL(new Blob(s,{type:i})),e.isViewPdf20=!0}))},addLog:function(){var t=this,e={type:"评论",content:this.textareaVal,demandid:this.formdata.id};this.fileList.length&&(e.attachment=JSON.stringify(this.fileList[0])),this.$request.post("/S06M02S1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.textareaVal="",t.fileList=[],t.getDemandLog()):t.$message.warning(e.data.msg||"评论失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},changeMsg:function(t){var e=this,a={id:this.formdata.id};"任务描述"==t?a.remark=this.formdata.remark:a.billtitle=this.formdata.billtitle,this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(a){200==a.data.code?(e.formdata=a.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(a.data.msg||"修改"+t+"失败"),e.bindData())}))},changeBilltype:function(t){var e=this;if(!t||this.formdata.billtype!=t){this.$refs["billTypeRef"].doClose();var a={billtype:t,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改优先级失败"),e.bindData())}))}},changeStatus:function(t){var e=this;if(this.formdata.demandstatus!=t.id){this.$refs["statusRef"].doClose();var a={demandstatus:t.id,id:this.formdata.id};2==t.statusattr?this.$prompt("","完成说明",{confirmButtonText:"提交",cancelButtonText:"关闭",closeOnClickModal:!1,inputValue:this.formdata.finishdes,inputType:"textarea",inputPlaceholder:"请输入"}).then((function(t){var i=t.value;a.finishdes=i,a.finishdate=new Date,e.updateStatus(a)})).catch((function(){e.$emit("bindData")})):this.updateStatus(a)}},updateStatus:function(t){var e=this;this.$request.post("/S06M02B1/update",JSON.stringify(t)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindData()):(e.$message.warning(t.data.msg||"修改状态失败"),e.bindData())}))},changeAppointee:function(t){var e=this;if(this.formdata.appointeeid!=t.id){this.$refs["appointeeRef"].doClose();var a={appointeeid:t.id,appointee:t.engineername,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindRightContent()):(e.$message.warning(t.data.msg||"修改执行者失败"),e.bindData())}))}},changeCollaborator:function(t){var e=this,a={collaboratorids:"",collaborators:"",id:this.formdata.id};if(""==t.id)this.$refs["collaboratorRef"].doClose();else if(this.formdata.collaboratorids){var i=this.formdata.collaboratorids.split(","),s=this.formdata.collaborators.split(",");if(console.log(i,"collaboratoridArr"),i.includes(t.id)){var o=i.findIndex((function(e){return e==t.id}));if(console.log(o),-1!=o){i.splice(o,1),s.splice(o,1),console.log(s,"collaboratorArr2");for(var l=/,$/gi,n=0;n<i.length;n++){var r=i[n];a.collaboratorids+=r+","}a.collaboratorids=a.collaboratorids.replace(l,"");for(n=0;n<s.length;n++){r=s[n];a.collaborators+=r+","}a.collaborators=a.collaborators.replace(l,"")}}else a.collaboratorids=this.formdata.collaboratorids+","+t.id,a.collaborators=this.formdata.collaborators+","+t.engineername}else a.collaboratorids=t.id,a.collaborators=t.engineername;this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindRightContent()):(e.$message.warning(t.data.msg||"修改执行者失败"),e.bindData())}))},changeGroup:function(t){var e=this,a={groupid:t.id||"",groupname:t.groupname||"",groupuid:t.groupuid||"",id:this.formdata.id};this.formdata.groupid!=t.id&&this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改客户信息失败"),e.bindData())}))},getGroupAllList:function(t){var e=this,a={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};t&&(a.SearchPojo={groupname:t}),this.$request.post("/S06M14B1/getPageList",JSON.stringify(a)).then((function(t){200==t.data.code?e.groupAllList=t.data.data.list:e.$message.warning(t.data.msg||"查询客户信息失败")}))},changelevel:function(t){var e=this;if(this.formdata.level!=t){this.$refs["selectTagRef"].doClose();var a={level:t,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改优先级失败"),e.bindData())}))}},changeDate:function(t){var e=this,a={id:this.formdata.id};"截止时间"==t?this.formdata.deaddate?a.deaddate=this.dateFormat(this.formdata.deaddate):a.deaddate=new Date(0):this.formdata.startdate?a.startdate=this.dateFormat(this.formdata.startdate):a.startdate=new Date(0),this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.$forceUpdate(),e.getDemandLog()):(e.$message.warning(t.data.msg||"修改时间失败"),e.bindData())}))},dateFormat:function(t){if(console.log(t),t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),o=e.getHours().toString().padStart(2,"0"),l=e.getMinutes().toString().padStart(2,"0");e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(s," ").concat(o,":").concat(l)}},changeWeekMark:function(t){var e=this,a={weekmark:t||0,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改工时失败"),e.bindData())}))},changeWorkTime:function(t){var e=this,a={worktime:t||0,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改工时失败"),e.bindData())}))},changeLabeljson:function(t){var e=this,a=this.formdata.labeljson.findIndex((function(e){return e.labelid==t.id}));if(console.log(a),-1==a){var i={name:t.labelname,color:t.labelcolor,labelid:t.id};this.formdata.labeljson.push(i)}-1!=a&&this.formdata.labeljson.splice(a,1);var s={labeljson:this.formdata.labeljson,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(s)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改标签失败"),e.bindData())}))},closeLabeljson:function(t){var e=this,a=this.formdata.labeljson.findIndex((function(e){return e.labelid==t.labelid}));-1!=a&&this.formdata.labeljson.splice(a,1);var i={labeljson:this.formdata.labeljson,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(i)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改时间失败"),e.bindData())}))},createLabel:function(){var t=this,e={labelname:this.labelname,labelcolor:this.labelcolor},a="标签创建";if(this.labeloperaType)var i="/S06M02S6/create";else{i="/S06M02S6/update";e.id=this.labelId,a="标签修改"}this.$request.post(i,JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(a+"成功"),t.showSetLbel=!0,t.labelcolor="blue",t.$nextTick((function(){t.getLabelList()}))):t.$message.warning(e.data.msg||a+"失败")}))},editLabel:function(t){console.log(t),this.labelname=t.labelname,this.labelcolor=t.labelcolor,this.labelId=t.id,this.showSetLbel=!1,this.labeloperaType=0},searchLabel:function(){if(this.labelVal){this.labelList=[];for(var t=0;t<this.labelListCopy.length;t++){var e=this.labelListCopy[t];e.labelname.includes(this.labelVal)&&this.labelList.push(e)}}else this.labelList=[].concat(this.labelListCopy)},changeProject:function(t){var e=this;if(""==t.id)var a={projectid:"",itemname:"",itemcode:"",status:null,id:this.formdata.id};else{if(this.formdata.projectid==t.id)return;a={projectid:t.id,itemname:t.projname,itemcode:t.projcode,status:t.status[0].id,id:this.formdata.id}}this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改项目失败"),e.bindData())}))},searchProject:function(){if(this.projectVal){this.projectAllListCopy=[];for(var t=0;t<this.projectAllList.length;t++){var e=this.projectAllList[t];e.projname.includes(this.projectVal)&&this.projectAllListCopy.push(e)}}else this.projectAllListCopy=[].concat(this.projectAllList)},bindRightContent:function(){var t=this;return Object(l["a"])(Object(o["a"])().mark((function e(){var a,i,s,l;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.Participants=[],t.formdata.appointee&&(a={engineername:t.formdata.appointee},t.Participants.push(a)),t.formdata.collaborators)for(i=t.formdata.collaborators.split(","),s=0;s<i.length;s++)l=i[s],a={engineername:l},t.Participants.push(a);case 3:case"end":return e.stop()}}),e)})))()},getLabelList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1};this.$request.post("/S06M02S6/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.labelList=[].concat(e.data.data.list),t.labelListCopy=[].concat(e.data.data.list))}))},getStatusName:function(t){var e="";if(t){var a=this.statusList.findIndex((function(e){return e.id==t}));-1!=a&&(e=this.statusList[a].statusname)}return e}}},d=c,f=(a("c9bd"),a("d36e"),a("2877")),u=Object(f["a"])(d,i,s,!1,null,"403d2814",null);e["a"]=u.exports},a18e:function(t,e,a){},af2b:function(t,e,a){"use strict";a("7de4")},c9bd:function(t,e,a){"use strict";a("f88f")},d36e:function(t,e,a){"use strict";a("a18e")},de74:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!t.formdata.assessor},on:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.idx},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini",readonly:!!t.formdata.id},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:""},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"billtype"}},[a("el-popover",{ref:"billtypeRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selbilltypeRef.bindData()}}},[a("selDictionaries",{ref:"selbilltypeRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Pms_ProPoint.billtype"},on:{singleSel:function(e){t.formdata.billtype=e.dictvalue,t.$refs.billtypeRef.doClose()},closedic:function(e){return t.$refs.billtypeRef.doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"类型",clearable:"",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[a("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"接受人员"}},[a("autoComplete",{attrs:{size:"small",value:t.formdata.accepter,baseurl:"/S06M09B1User/getPageList",params:{name:"name"}},on:{setRow:function(e){return t.setRow(e)},autoClear:function(e){return t.autoClear()}}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"计划开始"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.formdata.startplan,callback:function(e){t.$set(t.formdata,"startplan",e)},expression:"formdata.startplan"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"计划完成"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.formdata.endplan,callback:function(e){t.$set(t.formdata,"endplan",e)},expression:"formdata.endplan"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"公共"}},[a("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"重要"}},[a("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.importantmark,callback:function(e){t.$set(t.formdata,"importantmark",e)},expression:"formdata.importantmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"紧急的"}},[a("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.urgentmark,callback:function(e){t.$set(t.formdata,"urgentmark",e)},expression:"formdata.urgentmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"完成"}},[a("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.finishmark,callback:function(e){t.$set(t.formdata,"finishmark",e)},expression:"formdata.finishmark"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"40px"}},[a("el-col",{attrs:{span:24}},[a("MyEditor",{ref:"MyEditor",attrs:{height:400,html:t.formdata.tdcontent,excludeKeys:["group-image","insertImage","uploadImage","insertLink","group-video","insertVideo","uploadVideo","fullScreen"]},on:{changeHtml:function(e){return t.changeHtml(e)}}})],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},s=[],o=(a("b0c0"),a("b64b"),a("b775"));const l={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);o["a"].post("/S06M09B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);o["a"].post("/S06M09B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{o["a"].get("/S06M09B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var n=l,r=a("1975"),c=a("5b24"),d=a("5c73"),f=a("b0b8"),u={name:"Formedit",components:{MyEditor:r["a"],autoComplete:c["a"],selDictionaries:d["a"]},props:["idx","isDialog"],data:function(){return{title:"TODO列表",formdata:{type:"",content:"",title:"",billtype:"",remark:"",accepterid:"",accepter:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,status:1},formRules:{type:[{required:!0,trigger:"blur",message:"类型不能为空"}],title:[{required:!0,trigger:"blur",message:"标题不能为空"}],content:[{required:!0,trigger:"blur",message:"公告内容不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px",formheight:"500px"}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&o["a"].get("/S06M09B1/getEntity?key=".concat(this.idx)).then((function(e){console.log("==================",e),200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?n.add(this.formdata).then((function(e){console.log("新建====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")})):n.update(this.formdata).then((function(e){console.log("保存====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),n.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},writeCode:function(t){f.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.type=f.getFullChars(t)},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},changeHtml:function(t){this.formdata.tdcontent=t},setRow:function(t){this.formdata.accepter=t.name,this.formdata.accepterid=t.id,this.cleValidate("accepter")},autoClear:function(){this.formdata.accepterid="",this.formdata.accepter=""}}},m=u,p=(a("21c8"),a("2877")),g=Object(p["a"])(m,i,s,!1,null,"383f1cf1",null);e["a"]=g.exports},f88f:function(t,e,a){}}]);