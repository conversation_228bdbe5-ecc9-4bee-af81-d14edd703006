(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-13e6fe74"],{"06d7":function(e,t,a){"use strict";a("0bc3")},"0bc3":function(e,t,a){},"14fc":function(e,t,a){},"160c":function(e,t,a){"use strict";a("14fc")},8270:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"wrap"},[a("div",{staticClass:"searchHeader"},[a("cardHeaderWork",{ref:"cardHeaderWork",on:{getSearchVal:e.bindData}})],1),a("ul",{staticClass:"list-ul"},[a("li",{staticClass:"list-header"},[a("h4",[e._v("待办任务 · "+e._s(e.list.length))])]),a("div",{staticStyle:{"max-height":"78vh",overflow:"auto"}},e._l(e.list,(function(t,s){return a("li",{key:s,staticClass:"list-li"},[a("div",{staticClass:"list-li-item",staticStyle:{width:"100%"}},[a("h4",{staticStyle:{"font-size":"18px"}},[e._v(e._s(t.billtitle))])]),a("div",{staticClass:"left",staticStyle:{flex:"1"}},[a("div",{staticClass:"list-li-item flex"},[a("span",[e._v("任务描述：")]),a("p",{domProps:{innerHTML:e._s(t.remark)}})]),t.appointeeid?a("div",{staticClass:"list-li-item"},[a("span",[e._v("执行者：")]),a("div",{staticClass:"appointeenameSty"},[e._v(e._s(t.appointee))])]):e._e(),t.deaddate?a("div",{staticClass:"list-li-item"},[a("span",[e._v("截止时间：")]),a("span",{staticClass:"dateSpan",class:(new Date).getTime()>new Date(t.deaddate).getTime()?"dateRed":new Date(t.deaddate).getTime()-(new Date).getTime()<864e5?"dateOrange":"dateBlue"},[e._v(e._s(e.isdeaddate(t.deaddate))+" 截止")])]):e._e(),t.labeljson&&JSON.parse(t.labeljson).length?a("div",{staticClass:"list-li-item"},[a("span",[e._v("标签:")]),e._l(t.labeljson?JSON.parse(t.labeljson):[],(function(t,s){return a("span",{key:s,class:"label-color label-"+t.color},[e._v(" "+e._s(t.name)+" ")])}))],2):e._e()]),a("div",{staticClass:"right",staticStyle:{"text-align":"left","margin-left":"20px"}},[a("div",{staticClass:"list-li-item"},[a("span",[e._v("所属项目：")]),e._v(e._s(t.itemname)+" ")]),a("div",{staticClass:"list-li-item"},[a("span",[e._v("创建者：")]),e._v(e._s(t.createby)+" ")]),a("div",{staticClass:"list-li-item"},[a("span",[e._v("优先级：")]),0==t.level?a("div",{staticClass:"levelTag taggray"},[e._v("较低")]):1==t.level?a("div",{staticClass:"levelTag tagblue"},[e._v(" 普通 ")]):2==t.level?a("div",{staticClass:"levelTag tagorange"},[e._v(" 紧急 ")]):3==t.level?a("div",{staticClass:"levelTag tagred"},[e._v(" 非常紧急 ")]):e._e()])])])})),0),a("li",{staticClass:"list-foot"})]),a("div")])},l=[],i=(a("99af"),a("caad"),a("4e82"),a("e9c4"),a("d3b7"),a("25f0"),a("2532"),a("4d90"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"searchwrap"},[a("div",{staticClass:"searchForm"},[null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype?a("div",{staticClass:"searchForm-item"},[a("span",{staticClass:"title"},[e._v("用户")]),a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择用户"},on:{change:function(t){return e.changeVal("user")}},model:{value:e.searchForm.user,callback:function(t){e.$set(e.searchForm,"user",t)},expression:"searchForm.user"}},[a("el-option",{attrs:{label:"所有",value:"all"}}),e._l(e.peoples,(function(e){return a("el-option",{key:e.id,attrs:{label:e.engineername,value:e.id}})}))],2)],1):e._e(),null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype?a("div",{staticClass:"searchForm-item"},[a("span",{staticClass:"title"},[e._v("操作")]),a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择操作"},on:{change:function(t){return e.changeVal("operate")}},model:{value:e.searchForm.operate,callback:function(t){e.$set(e.searchForm,"operate",t)},expression:"searchForm.operate"}},[a("el-option",{attrs:{label:"所有",value:"all"}}),a("el-option",{attrs:{label:"执行者",value:"appointeengineerid"}}),a("el-option",{attrs:{label:"创建者",value:"createengineerid"}})],1)],1):e._e(),a("div",{staticClass:"searchForm-item"},[a("span",{staticClass:"title"},[e._v("状态")]),a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择操作"},on:{change:function(t){return e.changeVal("status")}},model:{value:e.searchForm.status,callback:function(t){e.$set(e.searchForm,"status",t)},expression:"searchForm.status"}},[a("el-option",{attrs:{label:"所有",value:"all"}}),a("el-option",{attrs:{label:"待处理",value:"待处理"}}),a("el-option",{attrs:{label:"进行中",value:"进行中"}}),a("el-option",{attrs:{label:"已完成未结转",value:"已完成未结转"}}),a("el-option",{attrs:{label:"已完成已结转",value:"已完成已结转"}})],1)],1),a("div",{staticClass:"searchForm-item"},[a("span",{staticClass:"title"},[e._v("排序")]),a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择操作"},on:{change:function(t){return e.changeVal("sort")}},model:{value:e.searchForm.sort,callback:function(t){e.$set(e.searchForm,"sort",t)},expression:"searchForm.sort"}},[a("el-option",{attrs:{label:"截止时间",value:"deaddate"}}),a("el-option",{attrs:{label:"更新时间",value:"statusmodifydate"}}),a("el-option",{attrs:{label:"创建时间",value:"createdate"}}),a("el-option",{attrs:{label:"优先级",value:"level"}})],1)],1),a("div",{staticClass:"searchForm-item"},[a("span",{staticClass:"title"},[e._v("方式")]),a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择操作"},on:{change:function(t){return e.changeVal("sortway")}},model:{value:e.searchForm.sortway,callback:function(t){e.$set(e.searchForm,"sortway",t)},expression:"searchForm.sortway"}},[a("el-option",{attrs:{label:"从大到小",value:1}}),a("el-option",{attrs:{label:"从小到大",value:0}})],1)],1),null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype?a("div",{staticClass:"searchForm-item"},[a("span",{staticClass:"title"},[e._v("时间")]),a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"unlink-panels":"",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:function(t){return e.changeVal("dateRange")}},model:{value:e.searchForm.dateRange,callback:function(t){e.$set(e.searchForm,"dateRange",t)},expression:"searchForm.dateRange"}})],1):e._e()])])}),r=[],n=a("b893"),o={data:function(){return{searchForm:{user:this.$store.state.user.userinfo.engineer.id,operate:"appointeengineerid",status:"待处理",sort:"deaddate",sortway:1,dateRange:[Object(n["b"])(new Date),Object(n["a"])(new Date)]},peoples:[]}},mounted:function(){this.initEngineer()},methods:{initEngineer:function(){var e=this;if(null!=this.$store.state.user.userinfo.engineer&&"管理者"==this.$store.state.user.userinfo.engineer.engineertype){var t={PageNum:1,PageSize:100,OrderType:1,SearchType:1};this.peoples=[],this.$request.post("/S06M02S2/getPageList",JSON.stringify(t)).then((function(t){200==t.data.code?e.peoples=t.data.data.list:e.peoples.push(e.$store.state.user.userinfo.engineer)}))}},changeVal:function(e){console.log("files",e),this.$emit("getSearchVal")}}},c=o,d=(a("06d7"),a("2877")),u=Object(d["a"])(c,i,r,!1,null,"2c0e0196",null),p=u.exports,g={components:{cardHeaderWork:p},data:function(){return{list:[],queryParams:{PageNum:1,PageSize:1e3,OrderType:1,SearchType:1},total:0}},mounted:function(){this.bindData()},methods:{bindData:function(){var e=this,t="/S06M02B1/getTodoPageList",a=this.$refs.cardHeaderWork.searchForm;"all"!=a.status&&(t.includes("?")?t+="&statustype="+a.status:t+="?statustype="+a.status),"all"!=a.operate?t.includes("?")?t+="&"+a.operate+"='"+a.user+"'":t+="?"+a.operate+"='"+a.user+"'":"all"!=a.user&&(t.includes("?")?t+="&engineerids='"+a.user+"'":t+="?engineerids='"+a.user+"'"),this.queryParams.OrderBy=a.sort,this.queryParams.OrderType=a.sortway,a.dateRange&&(this.queryParams.DateRange={StartDate:a.dateRange[0],EndDate:a.dateRange[1]}),this.$request.post(t,JSON.stringify(this.queryParams)).then((function(t){200==t.data.code?(e.list=t.data.data.list,e.total=t.data.data.total):e.$message.warning(t.data.msg||"获取待办任务失败")})).catch((function(t){e.$message.error(t||"请求错误")}))},isdeaddate:function(e){if(!e)return!1;var t=new Date(e).getTime(),a=new Date(e),s=(a.getFullYear(),(a.getMonth()+1).toString().padStart(2,"0")),l=a.getDate().toString().padStart(2,"0"),i=a.getHours().toString().padStart(2,"0"),r=a.getMinutes().toString().padStart(2,"0");return(new Date).getTime()>t||(new Date).getTime(),"".concat(s,"月").concat(l,"日").concat(i,":").concat(r)}}},h=g,v=(a("160c"),Object(d["a"])(h,s,l,!1,null,"f514bf0c",null));t["default"]=v.exports}}]);