(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-16eb8f96"],{"04e2":function(t,e,i){t.exports=i.p+"static/img/videoplayer.5992bc98.jpg"},"1c27":function(t,e,i){},"2d46":function(t,e,i){t.exports=i.p+"static/img/wizard_main_bg.98203fb9.png"},"4a9a":function(t,e,i){},"608d":function(t,e,i){},9522:function(t,e,i){"use strict";i.r(e);var o=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{"background-color":"#f5f6f7","min-height":"100vh","padding-bottom":"10px"}},[i("header",{style:{"background-image":"url("+t.Image+")"}},[i("div",{staticClass:"flex a-c j-s-a pageStyle"},[i("div",{staticClass:"title"},[i("h2",[i("span",[t._v("欢迎使用")]),t._v(" "+t._s(t.productInfo.name)+" ")]),i("p",[t._v(t._s(t.productInfo.content))])]),i("svg-icon",{staticStyle:{width:"420px",height:"320px"},attrs:{"icon-class":"datasheet"}})],1),i("div",{staticClass:"card pageStyle"},t._l(t.productInfo.cardlst,(function(e,o){return i("div",{key:o,staticClass:"card-item",style:{"background-color":e.bgcolor}},[i("div",{staticClass:"left"},[i("h3",[t._v(t._s(e.title))]),i("p",{attrs:{title:e.content}},[t._v(t._s(e.content))]),i("router-link",{staticClass:"linkTo",attrs:{to:e.link}},[t._v("点击查看 "),i("i",{staticClass:"el-icon-arrow-right"})])],1),i("div",{staticClass:"right"},[i("svg-icon",{staticStyle:{width:"120px",height:"120px"},attrs:{"icon-class":"datasheet"}})],1)])})),0)]),i("section",{staticClass:"pageStyle section"},[i("leftComponent",{staticClass:"left",attrs:{lst:t.productInfo.videolst,moreurl:t.productInfo.videourl}}),i("rightComponent",{staticClass:"right",attrs:{lst:t.productInfo.newList,moreurl:t.productInfo.newsurl}})],1),t._m(0)])},n=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("footer",{staticClass:"pageStyle"},[i("div",{staticClass:"footer"},[i("p",[t._v("联系我们")]),i("ul",[i("li",[t._v("公司：嘉兴应凯科技有限公司")]),i("li",[t._v("邮箱：<EMAIL>")]),i("li",[t._v("电话：18606858808")]),i("li",[t._v("网址：http://www.inkstech.com/")]),i("li",[t._v("地址：浙江省嘉善县惠民街道台升大道3号3号楼306室")])])])])}],a=(i("b64b"),{id:1,name:"OMS订单管理系统",content:" 订单管理系统是指组织用于管理企业日常业务的一套软件工具，其中包括销售、采购、仓库、生产、品质、财务等等",image:"wizard_main_bg.png",svgicon:"datasheet",videourl:"",newsurl:"",cardlst:[{title:"快速入门",content:"教你如何快速上手oms订单系统",link:"",svgicon:"datasheet",bgcolor:"#e7f2ff"}],videolst:[{productid:"",title:"1.1 连接数据库并添加数据集",videourl:"",image:""}],newList:[{productid:"",title:"企业标准化软件、个性化软件开发"}]}),s=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"pageStyle"},[t._m(0),i("div",{staticClass:"section"},t._l(t.lst,(function(e,o){return i("div",{key:o,staticClass:"section-item",on:{click:function(i){return t.openVideo(e)}}},[i("div",{staticClass:"imgsty",style:{"background-image":"url("+(e.image?e.image:t.Image)+")","background-size":"100% 100%"}}),i("div",{staticClass:"title"},[t._v(t._s(e.title))])])})),0),t.videoVisible?i("el-dialog",{attrs:{width:"81vw",top:"4px",title:"","append-to-body":!0,visible:t.videoVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.videoVisible=e}}},[i("div",[i("video-player",{ref:"videoPlayer",staticClass:"player-video",attrs:{playsinline:!1,options:t.playOptions},on:{ready:t.onPlayerReady,play:function(e){return t.onPlayerPlay(e)},pause:function(e){return t.onPlayerPause(e)},ended:function(e){return t.onPlayerEnd(e)},waiting:function(e){return t.onPlayerWaiting(e)},playing:function(e){return t.onPlayerPlaying(e)},loadeddata:function(e){return t.onPlayerLoadeddata(e)},timeupdate:function(e){return t.onPlayerTimeupdate(e)},statechanged:function(e){return t.playerStateChanged(e)}}})],1)]):t._e()],1)},r=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"header"},[i("h3",[t._v("教学视频")])])}],l=(i("a9e3"),{props:{moreurl:{type:String,default:""},lst:{type:Array}},data:function(){return{Image:i("04e2"),videoVisible:!1,videoTitle:"",playOptions:{height:"400px",width:"100%",playbackRates:[1,1.5,2],autoplay:!0,muted:!1,loop:!1,preload:"auto",language:"zh-CN",aspectRatio:"16:9",fluid:!0,poster:"./gcy-logo-200.png",notSupportedMessage:"此视频暂无法播放，请稍后再试",controlBar:{currentTimeDisplay:!0,progressControl:!0,playbackRateMenuButton:!0,timeDivider:!0,durationDisplay:!0,remainingTimeDisplay:!0,fullscreenToggle:!0},sources:[{type:"video/mp4",src:""}]}}},methods:{openVideo:function(t){this.videoTitle=t.title,this.videoVisible=!0,this.playOptions.sources[0].src=t.videourl,console.log(t,this.playOptions)},onPlayerReady:function(){console.log("准备好了")},onPlayerPlay:function(t){console.log("播放了");var e=0;Number(Math.floor(this.playedTime))===Number(Math.floor(t.duration()))?(this.playedTime=0,e=0):Number(Math.floor(t.currentTime()))!==Number(Math.floor(this.playedTime))&&(e=this.playedTime,t.currentTime(e))},onPlayerPause:function(t){console.log("暂停中"),this.playedTime=t.currentTime()},onPlayerEnd:function(t){console.log("播放结束了")},onPlayerWaiting:function(t){console.log("播放停止中")},onPlayerPlaying:function(t){console.log("开始播放了")},onPlayerLoadeddata:function(t){console.log("开始下载数据")},onPlayerTimeupdate:function(t){console.log(t);var e=t.currentTime();e-this.currentTime>1&&t.currentTime(this.currentTime>this.maxTime?this.currentTime:this.maxTime),this.currentTime=t.currentTime(),this.maxTime=this.currentTime>this.maxTime?this.currentTime:this.maxTime},playerStateChanged:function(t){console.log(t)},pause:function(){this.$refs.videoPlayer.player.pause()}}}),c=l,u=(i("d438"),i("2877")),d=Object(u["a"])(c,s,r,!1,null,"41c0de98",null),p=d.exports,f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"pageStyle"},[t._m(0),i("div",{staticClass:"section"},[i("ul",t._l(t.lst,(function(e,o){return i("li",{key:o,attrs:{title:e.title},on:{click:function(i){return t.routerTo(e.url)}}},[t._v(" "+t._s(e.title)+" ")])})),0)])])},m=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"header"},[i("h3",[t._v("最新动态")])])}],g={props:{moreurl:{type:String,default:""},lst:{type:Array}},data:function(){return{}},methods:{routerTo:function(t){console.log(t),t&&this.$route.push(t)}}},h=g,y=(i("b937"),Object(u["a"])(h,f,m,!1,null,"5aae229a",null)),v=y.exports,_={name:"",components:{leftComponent:p,rightComponent:v},data:function(){return{Image:i("2d46"),productInfo:a,cardlst:[{title:"快速入门",content:"教你如何快速上手oms订单系统",link:"",svgicon:"datasheet",bgcolor:"#e7f2ff"},{title:"在线文档",content:"涵盖oms订单系统的插件安装、用户手册、使用教程、常见问题的解决方案、以及二次开发等",link:"",svgicon:"datasheet",bgcolor:"#fff5e7"},{title:"企业版",content:"提供企业级应用场景，增强包提供高等级原厂服务支持最佳实践建议",link:"",svgicon:"datasheet",bgcolor:"#f6e7ff"}],formKey:""}},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;this.formKey=this.$route.query.key||this.$route.params.key,console.log(this.formKey),this.$request.get("/S06M13B1/getEntityByGoodsCode?goodscode="+this.formKey).then((function(e){console.log(e),200==e.data.code?t.productInfo=JSON.parse(e.data.data.descjson):t.$alert(e.data.msg||"获取产品信息失败",{type:"warning",callback:function(e){t.$router.go(-1)}})})).catch((function(e){t.$message.error(e||"请求错误")}))}}},b=_,C=(i("f82d"),Object(u["a"])(b,o,n,!1,null,"02a3103a",null));e["default"]=C.exports},b937:function(t,e,i){"use strict";i("1c27")},d438:function(t,e,i){"use strict";i("4a9a")},f82d:function(t,e,i){"use strict";i("608d")}}]);