(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-17b87352"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(t,e,a){var r=s(),n=t-r,l=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=l;var t=Math.easeInOutQuad(c,r,n,e);o(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"35f6":function(t,e,a){},"3c6a":function(t,e,a){"use strict";a("c790")},8121:function(t,e,a){"use strict";a("f92f")},"841c":function(t,e,a){"use strict";var i=a("d784"),o=a("825a"),s=a("1d80"),r=a("129f"),n=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=s(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var s=o(t),l=String(this),c=s.lastIndex;r(c,0)||(s.lastIndex=0);var d=n(s,l);return r(s.lastIndex,c)||(s.lastIndex=c),null===d?-1:d.index}]}))},9811:function(t,e,a){"use strict";a("bdae")},bdae:function(t,e,a){},c6ca:function(t,e,a){"use strict";a("35f6")},c790:function(t,e,a){},e413:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx,partGroup:t.partGroup}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnadd:function(e){return t.showform(0)},btnsearch:t.search,bindData:t.bindData,AdvancedSearch:t.AdvancedSearch,btnExport:t.btnExport}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.treeVisble,expression:"treeVisble"}],attrs:{span:4}},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[a("div",{staticClass:"groupTitle"},[a("span",[t._v("分组")]),a("i",{staticClass:"el-icon-s-tools",style:{color:t.treeEditable?"#1e80ff":""},on:{click:function(e){return t.treeEdit()}}})]),a("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto",width:"100%"},attrs:{data:t.partGroup,"node-key":"id","default-expand-all":"","expand-on-click-node":!1},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{on:{click:function(){return t.handleNodeClick(i)}}},[t._v(t._s(i.label)+" ")]),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==i.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return t.editTreeNode(i)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return t.addTreeChild(i)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==i.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return t.delTreeNode(i)}}})],1)])}}])})],1)]),a("el-col",{attrs:{span:t.treeVisble?t.showHelp?16:20:t.showHelp?20:24}},[a("tableList",{ref:"tableList",on:{changeidx:t.changeidx,showform:t.showform,sendTableForm:t.sendTableForm}})],1),a("el-col",{attrs:{span:t.showHelp?4:0}},[a("HelpModel",{ref:"helpmodel",attrs:{code:"S06M16B1"}})],1)],1)],1)],1),t.gropuvisible?a("el-dialog",{attrs:{title:"分类","append-to-body":!0,visible:t.gropuvisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuvisible=e}}},[a("Group",{ref:"group",attrs:{idx:t.idx,pid:t.pid},on:{bindData:t.bindTreeData,closeDialog:function(e){t.gropuvisible=!1}}})],1):t._e()],1)},o=[],s=a("2909"),r=(a("99af"),a("d81d"),a("e9c4"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("a573"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单据编码",size:"small"},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据标题"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单据标题",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"客户"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入客户",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"联系人"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入联系人",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.AdvancedSearch()}}},[t._v(" 搜索 ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"服务需求"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入服务需求",size:"small"},model:{value:t.formdata.sercontent,callback:function(e){t.$set(t.formdata,"sercontent",e)},expression:"formdata.sercontent"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"服务过程"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入服务过程",size:"small"},model:{value:t.formdata.serprocess,callback:function(e){t.$set(t.formdata,"serprocess",e)},expression:"formdata.serprocess"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"未尽事宜"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入未尽事宜",size:"small"},model:{value:t.formdata.serloss,callback:function(e){t.$set(t.formdata,"serloss",e)},expression:"formdata.serloss"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"简述"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入简述",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1)],1)],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.setColumsVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)}),n=[],l=a("8daf"),c={name:"Listheader",props:["tableForm"],components:{Setcolums:l["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},thorList:!0,setColumsVisible:!1,code:"S06M16B1ist"}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList)},btnExport:function(){this.$emit("btnExport")}}},d=c,m=(a("c6ca"),a("2877")),u=Object(m["a"])(d,r,n,!1,null,"5d45779c",null),f=u.exports,p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{approval:t.approval,submitForm:t.submitForm,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton()},clickMethods:t.clickMethods}})],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"min-height":"110px"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("name")}}},[a("el-form-item",{attrs:{label:"模板名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入模板名称",clearable:"",size:"small"},model:{value:t.formdata.name,callback:function(e){t.$set(t.formdata,"name",e)},expression:"formdata.name"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("revion")}}},[a("el-form-item",{attrs:{label:"版本",prop:"revion"}},[a("el-input",{attrs:{placeholder:"请输入版本",clearable:"",size:"small"},model:{value:t.formdata.revion,callback:function(e){t.$set(t.formdata,"revion",e)},expression:"formdata.revion"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("rownum")}}},[a("el-form-item",{attrs:{label:"行号",prop:"rownum"}},[a("el-input-number",{attrs:{min:0,label:"请输入行号",size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)])],1),a("el-row",{staticStyle:{"margin-top":"15px"}},[a("el-col",{attrs:{span:24}},[a("el-tabs",{staticStyle:{"min-height":"380px"},attrs:{"tab-position":"left"}},[a("el-tab-pane",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"模板内容"}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入模板内容",size:"small",type:"textarea",autosize:{minRows:18,maxRows:24}},model:{value:t.formdata.velocity,callback:function(e){t.$set(t.formdata,"velocity",e)},expression:"formdata.velocity"}})],1)])],1)],1)],1)],1)],1),a("el-divider"),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"10px","margin-right":"20px"}},[a("el-col",{attrs:{span:22}},[a("el-form-item",{attrs:{label:"摘要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"S06M16B1Edit",commonurl:"/S06M16B1/printBill",weburl:"/S06M16B1/printWebBill",modelurl:"/SaReports/getListByModuleCode"}})],1)},h=[],b=a("b775");const g={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);b["a"].post("/S06M16B1/create",i).then(t=>{console.log(t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);b["a"].post("/S06M16B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){b["a"].get("/S06M16B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,b["a"].get("/S06M16B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||!t.formdata.assessor?"审核成功":"反审核成功"),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||!t.formdata.assessor?"审核失败":"反审核失败"))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})}};var v=g,w=a("1975"),x=["id","name","velocity","revion","rownum","gengroupid","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],y={params:x},S=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],k=[],$={name:"Formedit",components:{MyEditor:w["a"]},props:["idx"],data:function(){return{title:"生成器模板",operateBar:S,processBar:k,formdata:{name:"",velocity:"",revion:"",rownum:0},formRules:{name:[{required:!0,trigger:"blur",message:"模板名称为必填项"}]},defaultProps:{children:"children",label:"label",value:"id",checkStrictly:!0},formLabelWidth:"100px",multi:0,selVisible:!1,formstate:0,submitting:0}},computed:{formcontainHeight:function(){return window.innerHeight-124+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;this.formstate=0,this.listLoading=!0,0!=this.idx&&b["a"].get("/S06M16B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},handleChangeGroupid:function(t){console.log(t),this.formdata.gengroupid=t[t.length-1],this.$forceUpdate()},clickMethods:function(t){this[t.meth](t.param)},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this,e={};e=this.$getParam(y,e,this.formdata),this.submitting=1,0==this.idx?v.add(e).then((function(e){t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})):v.update(e).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})),setTimeout((function(){t.submitting=0}),5e3)},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){v.delete(e)})).catch((function(){}))},approval:function(){v.approval(this)},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},changeidx:function(t){this.dialogIdx=t}}},_=$,F=(a("8121"),Object(m["a"])(_,p,h,!1,null,"32f601f0",null)),P=F.exports,C=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["name"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showform(i.row.id)}}},[t._v(t._s(i.row.name?i.row.name:"模板名称"))]):"data"==e.itemcode||"modifydate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormats")(i.row[e.itemcode])))]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)},D=[],T=(a("4e3e"),a("159b"),a("333d")),E={formcode:"S06M16B1List",item:[{itemcode:"name",itemname:"模板名称",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Sa_GenVelocity.name"},{itemcode:"revion",itemname:"版本",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_GenVelocity.revion"},{itemcode:"remark",itemname:"备注",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_GenVelocity.remark"},{itemcode:"lister",itemname:"制表",minwidth:"60",displaymark:1,overflow:1,datasheet:"Sa_GenVelocity.lister"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"60",displaymark:1,overflow:1,datasheet:"Sa_GenVelocity.modifydate"}]},z={components:{Pagination:T["a"]},props:["partGroup"],data:function(){return{lst:[],searchstr:"",total:0,listLoading:!1,idx:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:E,showsettingvisible:!1,Databases:[],formdata:{gengroupid:""},defaultProps:{children:"children",label:"label",value:"id",checkStrictly:!0}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},created:function(){this.getDatabases()},methods:{bindData:function(){var t=this;b["a"].post("/S06M16B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getDatabases:function(){var t=this;this.Databases=[],b["a"].get("/S06M16B1/databases").then((function(e){if(200==e.data.code){var a=e.data.data;a.forEach((function(e){var a={name:e};t.Databases.push(a)}))}})).catch((function(t){}))},getcolumn:function(){this.tableForm=E},handlePreview:function(t){this.showsettingvisible=!0},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={name:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(t,this.tableForm),this.bindData()},showform:function(t){this.$emit("showform",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"模板")},groupsearch:function(t){""!=t?this.queryParams.SearchPojo={gengroupid:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}}},B=z,N=(a("3c6a"),Object(m["a"])(B,C,D,!1,null,"79fd081a",null)),L=N.exports,V={name:"S06M16B1",components:{ListHeader:f,FormEdit:P,tableList:L},data:function(){return{title:"生成器模板",lst:[],FormVisible:!1,idx:0,tableForm:{},showHelp:!1,treeTitle:"分组",gropuvisible:!1,treeVisble:!1,partGroup:[],treeEditable:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.bindData(),t.$refs.tableList.getcolumn()}))},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.btnExport()}))},bindTreeData:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};b["a"].post("/S06M16B2/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){var a=[{id:"0",label:"分组"}],i=e.data.data.list.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),o=[].concat(Object(s["a"])(i),a);t.partGroup=t.transData(o,"id","pid","children"),console.log(t.partGroup)}else t.$message.warning(e.data.msg||"获取分组信息失败")}))},search:function(t){this.$refs.tableList.search(t)},AdvancedSearch:function(t){this.$refs.tableList.AdvancedSearch(t)},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(t){this.idx=t},handleNodeClick:function(t){if("root"==t.id)this.$refs.tableList.search("");else{var e=t.id;this.$refs.tableList.groupsearch(e)}},treeEdit:function(){this.treeEditable=!this.treeEditable},editTreeNode:function(t){this.pid=t.pid,this.showGroupform(t.id)},addTreeChild:function(t){this.pid=t.id,this.showGroupform(0)},delTreeNode:function(t){var e=this;console.log(t),t.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),b["a"].get("/S06M16B2/delete?key=".concat(t.id)).then((function(t){e.$message.success("删除成功！"),e.bindTreeData()})).catch((function(t){e.$message.warning(t||"删除失败")}))})).catch((function(){}))},showGroupform:function(t){this.idx=t,this.gropuvisible=!0},transData:function(t,e,a,i){for(var o=[],s={},r=e,n=a,l=i,c=0,d=0,m=t.length;c<m;c++)s[t[c][r]]=t[c];for(;d<m;d++){var u=t[d],f=s[u[n]];f?(!f[l]&&(f[l]=[]),f[l].push(u)):o.push(u)}return o}}},M=V,q=(a("9811"),Object(m["a"])(M,i,o,!1,null,null,null));e["default"]=q.exports},f92f:function(t,e,a){}}]);