(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1c4e3622"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),Math.easeInOutQuad=function(e,t,a,i){return e/=i/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function r(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(e,t,a){var n=o(),s=e-n,l=20,c=0;t="undefined"===typeof t?500:t;var m=function(){c+=l;var e=Math.easeInOutQuad(c,n,s,t);r(e),c<t?i(m):a&&"function"===typeof a&&a()};m()}},"3ec1":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",e._g({ref:"formedit",attrs:{idx:e.idx}},{compForm:e.compForm,closeForm:e.closeForm,changeidx:e.changeidx,bindData:e.bindData}))],1):a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnadd:function(t){return e.showform(0)},btnsearch:e.search,bindData:e.bindData,AdvancedSearch:e.AdvancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"sort-change":e.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["name"==t.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showform(i.row.id)}}},[e._v(e._s(i.row.name?i.row.name:"名称"))]):"createdate"==t.itemcode||"modifydate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormats")(i.row[t.itemcode])))]):"sex"==t.itemcode?a("span",[e._v(e._s(i.row[t.itemcode]?"男":"女"))]):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.GetList}})],1)],1)],1)],1)])},r=[],o=(a("e9c4"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:e.btnadd}},[e._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:e.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(t){e.iShow=!e.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:e.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"编号"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入编号",size:"small"},model:{value:e.formdata.pwcode,callback:function(t){e.$set(e.formdata,"pwcode",t)},expression:"formdata.pwcode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入名称",size:"small"},model:{value:e.formdata.pwname,callback:function(t){e.$set(e.formdata,"pwname",t)},expression:"formdata.pwname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"作业规格"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入作业规格",size:"small"},model:{value:e.formdata.pwspec,callback:function(t){e.$set(e.formdata,"pwspec",t)},expression:"formdata.pwspec"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"计件单位"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入计件单位",size:"small"},model:{value:e.formdata.workunit,callback:function(t){e.$set(e.formdata,"workunit",t)},expression:"formdata.workunit"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.AdvancedSearch()}}},[e._v(" 搜索 ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"制表"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入制表",size:"small"},model:{value:e.formdata.lister,callback:function(t){e.$set(e.formdata,"lister",t)},expression:"formdata.lister"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"备注"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入备注",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1)],1)],1)])],1)}),n=[],s={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M09B1USERList"}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},l=s,c=(a("e38b"),a("2877")),m=Object(c["a"])(l,o,n,!1,null,"0f656720",null),d=m.exports,f=a("333d"),u=a("b775"),p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!e.formdata.assessor},on:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[e._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!e.idx},nativeOn:{click:function(t){return e.deleteForm(e.idx)}}},[e._v("删 除")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.closeForm(t)}}},[e._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:e.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,rules:e.formRules}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("name")}}},[a("el-form-item",{attrs:{label:"名称",prop:"name"}},[a("autoComplete",{attrs:{size:"small",value:e.formdata.name,baseurl:"/S06M02S2/getPageList",params:{name:"engineername",other:"engineertype"}},on:{setRow:function(t){return e.setRow(t)},autoClear:function(t){return e.autoClear()}}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("phone")}}},[a("el-form-item",{attrs:{label:"手机号",prop:"phone"}},[a("el-input",{attrs:{placeholder:"请输入手机号",clearable:"",size:"small"},model:{value:e.formdata.phone,callback:function(t){e.$set(e.formdata,"phone",t)},expression:"formdata.phone"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("email")}}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{placeholder:"请输入邮箱",clearable:"",size:"small"},model:{value:e.formdata.email,callback:function(t){e.$set(e.formdata,"email",t)},expression:"formdata.email"}})],1)],1)]),a("el-col",{attrs:{span:2}},[a("div",{on:{click:function(t){return e.cleValidate("backcolorargb")}}},[a("el-form-item",{attrs:{label:"背景色",prop:"backcolorargb"}},[a("el-color-picker",{attrs:{predefine:e.predefineColors,size:"small"},model:{value:e.formdata.backcolorargb,callback:function(t){e.$set(e.formdata,"backcolorargb",t)},expression:"formdata.backcolorargb"}})],1)],1)]),a("el-col",{attrs:{span:3}},[a("div",{on:{click:function(t){return e.cleValidate("sex")}}},[a("el-form-item",{attrs:{label:"性别",prop:"sex"}},[a("el-radio-group",{model:{value:e.formdata.sex,callback:function(t){e.$set(e.formdata,"sex",t)},expression:"formdata.sex"}},[a("el-radio",{attrs:{label:1}},[e._v("男")]),a("el-radio",{attrs:{label:0}},[e._v("女")])],1)],1)],1)]),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"状态"}},[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.userstate,callback:function(t){e.$set(e.formdata,"userstate",t)},expression:"formdata.userstate"}})],1)],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)])])},h=[];a("b0c0"),a("b64b");const b={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);u["a"].post("/S06M09B1User/create",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);u["a"].post("/S06M09B1User/update",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{u["a"].get("/S06M09B1User/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var w=b,g=a("5b24"),v=a("b0b8"),x={name:"Formedit",components:{autoComplete:g["a"]},props:["idx"],data:function(){return{title:"todo用户",formdata:{sex:1,remark:"",name:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,backcolorargb:"#409eff",userstate:1},formRules:{name:[{required:!0,trigger:"blur",message:"名称不能为空"}]},predefineColors:["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#DCDFE6","#303133","#000000","#FFFFFF"],multi:0,selVisible:!1,formLabelWidth:"100px",formheight:"500px"}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(e,t){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;this.listLoading=!0,0!=this.idx&&u["a"].get("/S06M09B1User/getEntity?key=".concat(this.idx)).then((function(t){200==t.data.code?e.formdata=t.data.data:e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeForm()}}),e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error("请求错误")}))},submitForm:function(e){var t=this;this.$refs.formdata.validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;0==this.idx?w.add(this.formdata).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("changeidx",t.data.id),e.$emit("bindData"),e.bindData(),e.$emit("compForm"))})).catch((function(t){e.$message.warning("保存失败")})):w.update(this.formdata).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("bindData"),e.bindData(),e.$emit("compForm"))})).catch((function(t){e.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(e){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),w.delete(e).then((function(e){200==e.code&&t.$message.success("删除成功"),t.$emit("compForm")})).catch((function(){t.$message.warning("删除失败")}))})).catch((function(){}))},writeCode:function(e){v.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.engineercode=v.getFullChars(e)},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},setRow:function(e){this.formdata.name=e.engineername,this.cleValidate("name")},autoClear:function(){this.formdata.name=""}}},y=x,k=(a("f89f"),Object(c["a"])(y,p,h,!1,null,"f6a3645c",null)),S=k.exports,_={formcode:"S06M09B1USERList",item:[{itemcode:"name",itemname:"名称",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center"},{itemcode:"phone",itemname:"手机号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"email",itemname:"邮箱",minwidth:"80",displaymark:1,overflow:1},{itemcode:"sex",itemname:"性别",minwidth:"80",displaymark:1,overflow:1},{itemcode:"createby",itemname:"创建者",minwidth:"70",displaymark:1,overflow:1,datasheet:"Pms_ProPoint.createby"},{itemcode:"remark",itemname:"备注",minwidth:"70",displaymark:1,overflow:1,datasheet:"Pms_ProPoint.remark"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"Pms_ProPoint.lister"},{itemcode:"createdate",itemname:"创建日期",minwidth:"70",displaymark:1,sortable:1,overflow:1,datasheet:"Pms_ProPoint.createdate"}]},F={name:"SYSM01B1",components:{Pagination:f["a"],listheader:d,formedit:S},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},tableForm:_}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.bindData()},methods:{bindData:function(){var e=this;this.listLoading=!0,u["a"].post("/S06M09B1User/getPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},changeSort:function(e){"descending"==e.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(e,this.tableForm),this.bindData()},GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={engineercode:e,engineername:e,engineertype:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(e){this.idx=e,this.FormVisible=!0},closeForm:function(){this.bindData(),this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(e){this.idx=e}}},P=F,$=(a("5617"),Object(c["a"])(P,i,r,!1,null,"7fd90986",null));t["default"]=$.exports},5617:function(e,t,a){"use strict";a("6b35")},"6b35":function(e,t,a){},e134:function(e,t,a){},e38b:function(e,t,a){"use strict";a("e134")},f1e7:function(e,t,a){},f89f:function(e,t,a){"use strict";a("f1e7")}}]);