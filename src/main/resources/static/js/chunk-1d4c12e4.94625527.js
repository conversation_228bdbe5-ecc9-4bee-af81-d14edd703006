(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1d4c12e4"],{"00d2":function(e,t,a){"use strict";a("9b11")},"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return s})),Math.easeInOutQuad=function(e,t,a,i){return e/=i/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(e,t,a){var s=n(),r=e-s,l=20,c=0;t="undefined"===typeof t?500:t;var d=function(){c+=l;var e=Math.easeInOutQuad(c,s,r,t);o(e),c<t?i(d):a&&"function"===typeof a&&a()};d()}},"61e1":function(e,t,a){},"6e18":function(e,t,a){"use strict";a("fa4c")},"9b11":function(e,t,a){},dbdb:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",e._g({ref:"formedit",attrs:{idx:e.idx}},{compForm:e.compForm,closeForm:e.closeForm,changeidx:e.changeidx,bindData:e.bindData}))],1):e._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!e.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnadd:function(t){return e.showform(0)},btnsearch:e.search,bindData:e.bindData,AdvancedSearch:e.AdvancedSearch,allDelete:e.allDelete,Bindcolumn:e.getcolumn,btnHelp:e.btnHelp}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:e.showHelp?20:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["modifydate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormats")(i.row[t.itemcode])))]):"authcode"==t.itemcode?a("div",[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showform(i.row.id)}}},[e._v(" "+e._s(i.row[t.itemcode])+" ")])],1):"enabledmark"==t.itemcode?a("div",[i.row.enabledmark?a("el-tag",{attrs:{size:"small"}},[e._v("正 常")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("停 用")])],1):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.GetList}})],1),a("el-col",{attrs:{span:e.showHelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"SYSM01B9"}})],1)],1)],1)],1)])},o=[],n=a("c7eb"),s=a("b85c"),r=a("1da1"),l=(a("e9c4"),a("d3b7"),a("3ca3"),a("ddb0"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:e.btnadd}},[e._v(" 添加 ")]),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"danger",icon:"el-icon-delete",plain:"",size:"mini"},on:{click:function(t){return e.$emit("allDelete")}}},[e._v(" 批量删除 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:function(t){return e.$emit("bindData")}}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:e.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(t){e.iShow=!e.iShow}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(t){return e.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(t){return e.$emit("btnHelp")}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:e.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"授权Code"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入授权Code",size:"small"},model:{value:e.formdata.authcode,callback:function(t){e.$set(e.formdata,"authcode",t)},expression:"formdata.authcode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"授权作用"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入授权作用",size:"small"},model:{value:e.formdata.authdesc,callback:function(t){e.$set(e.formdata,"authdesc",t)},expression:"formdata.authdesc"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"登录名"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入登录名",size:"small"},model:{value:e.formdata.username,callback:function(t){e.$set(e.formdata,"username",t)},expression:"formdata.username"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.AdvancedSearch()}}},[e._v(" 搜索 ")])],1)],1)],1)],1)]),e.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:e.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.setColumsVisible=t}}},[a("Setcolums",{ref:"setcolums",attrs:{code:e.code,tableForm:e.tableForm},on:{bindData:function(t){return e.$emit("Bindcolumn")},closeDialog:function(t){e.setColumsVisible=!1}}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(t){return e.submitUpdate()}}},[e._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.setColumsVisible=!1}}},[e._v("取 消")])],1)],1):e._e()],1)}),c=[],d=a("8daf"),m={name:"Listheader",props:["tableForm"],components:{Setcolums:d["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},setColumsVisible:!1,code:"SYSM01B9List"}},methods:{openDialog:function(){this.setColumsVisible=!0},AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){console.log(this.strfilter),this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},defaultRole:function(){this.$emit("defaultRole")}}},u=m,f=(a("00d2"),a("2877")),h=Object(f["a"])(u,l,c,!1,null,"708ebc2a",null),p=h.exports,b=a("333d"),g=a("b775"),w=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-button",{attrs:{disabled:!e.idx,size:"small"},nativeOn:{click:function(t){return e.deleteForm(e.idx)}}},[e._v(" 删 除")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.closeForm(t)}}},[e._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:e.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":"100px","auto-complete":"on",rules:e.formRules}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("authdesc")}}},[a("el-form-item",{attrs:{label:"授权作用",prop:"authdesc"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入授权作用",size:"small"},model:{value:e.formdata.authdesc,callback:function(t){e.$set(e.formdata,"authdesc",t)},expression:"formdata.authdesc"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("authcode")}}},[a("el-form-item",{attrs:{label:"授权Code",prop:"authcode"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入授权Code",size:"small",readonly:!!e.formdata.id},model:{value:e.formdata.authcode,callback:function(t){e.$set(e.formdata,"authcode",t)},expression:"formdata.authcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("username")}}},[a("el-form-item",{attrs:{label:"登录名",prop:"username"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入登录名",size:"small"},model:{value:e.formdata.username,callback:function(t){e.$set(e.formdata,"username",t)},expression:"formdata.username"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("userpassword")}}},[a("el-form-item",{attrs:{label:"密码"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入密码",size:"small"},model:{value:e.formdata.userpassword,callback:function(t){e.$set(e.formdata,"userpassword",t)},expression:"formdata.userpassword"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"","label-width":"60px"}},[a("el-checkbox",{attrs:{label:"有效性","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}})],1)],1)],1),a("el-divider"),a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:23}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormat")(e.formdata.modifydate)))])])],1)],1)],1)],1)])])])},v=[];a("b64b");const y={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);g["a"].post("/system/SYSM01B9/create",i).then(e=>{console.log(i,e),200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);g["a"].post("/system/SYSM01B9/update",i).then(e=>{console.log(e,i),200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},addItem(e){return new Promise((t,a)=>{var i=JSON.stringify(e);g["a"].post("/system/SYSM03B2/create",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{g["a"].get("/system/SYSM01B9/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var x=y,S={name:"Formedit",components:{},props:["idx"],data:function(){return{title:"登录授权码",formdata:{authdesc:"",enabledmark:1,id:"",password:"",remark:"",revision:0,rownum:0,username:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{modulecode:[{required:!0,trigger:"blur",message:"模块编码为必填项"}],apprtype:[{required:!0,trigger:"blur",message:"审批类型为必填项"}],apprname:[{required:!0,trigger:"blur",message:"审批名称为必填项"}]},formLabelWidth:"100px",multi:0}},computed:{formcontainHeight:function(){return window.innerHeight-123+"px"}},watch:{idx:function(e,t){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;0!=this.idx&&(this.listLoading=!0,g["a"].get("/system/SYSM01B9/getEntity?key=".concat(this.idx)).then((function(t){200==t.data.code?e.formdata=t.data.data:e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeForm()}}),e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error("请求错误")})))},submitForm:function(){var e=this;this.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var e=this;0==this.idx?(x.add(this.formdata).then((function(t){e.$message.success("保存成功"),e.$emit("changeidx",t.data.id),e.$emit("bindData"),e.formdata=t.data})).catch((function(t){e.$message.warning(t||"保存失败")})),console.log("完成窗口")):x.update(this.formdata).then((function(t){e.$message.success("保存成功"),e.$emit("bindData"),e.formdata=t.data})).catch((function(t){e.$message.warning(t||"保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(e){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){x.delete(e).then((function(e){t.$message.success(e.msg||"删除成功"),t.$emit("compForm")})).catch((function(e){t.$message.warning(e||"删除失败")}))})).catch((function(){}))},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}},k=S,_=(a("f881"),Object(f["a"])(k,w,v,!1,null,"252a1362",null)),$=_.exports,C=a("0521"),F={formcode:"SYSM01B9List",item:[{itemcode:"authcode",itemname:"授权Code",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"PiAuthCode.authcode"},{itemcode:"authdesc",itemname:"授权作用",minwidth:"80",displaymark:1,overflow:1,datasheet:"PiAuthCode.authdesc"},{itemcode:"username",itemname:"登录名",minwidth:"100",displaymark:1,overflow:1,datasheet:"PiAuthCode.username"},{itemcode:"enabledmark",itemname:"有效性",minwidth:"60",displaymark:1,overflow:1,datasheet:"PiAuthCode.enabledmark"},{itemcode:"lister",itemname:"制表",minwidth:"60",displaymark:1,overflow:1,datasheet:"PiAuthCode.lister"},{itemcode:"remark",itemname:"备注",minwidth:"80",displaymark:1,overflow:1,datasheet:"PiAuthCode.remark"}]},P={components:{listheader:p,formedit:$,Pagination:b["a"],helpmodel:C["a"]},data:function(){return{title:"登录授权码",idx:0,listLoading:!1,lst:[],FormVisible:!1,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},multi:0,tableForm:F,showHelp:!1,selectList:[]}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},mounted:function(){this.bindData(),this.getcolumn()},methods:{GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},bindData:function(){var e=this;this.listLoading=!0,g["a"].post("/system/SYSM01B9/getPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},getcolumn:function(){var e=this;g["a"].get("/system/SYSM07B9/getBillEntityByCode?code=SYSM01B9List").then((function(t){if(200==t.data.code){if(null==t.data.data)return void(e.tableForm=F);e.tableForm=t.data.data}})).catch((function(t){e.$message.error("请求出错")}))},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},handleSelectionChange:function(e){this.selectList=e},allDelete:function(){var e=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deleteRows()})).catch((function(){})):this.$message.warning("请先选择货品")},deleteRows:function(e,t){var a=this;return Object(r["a"])(Object(n["a"])().mark((function e(){var t,i,o,r,l,c;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t=a,i=a.selectList,!i){e.next=22;break}o=[],r=Object(s["a"])(i),e.prev=5,c=Object(n["a"])().mark((function e(){var t,a;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t=l.value,a=new Promise((function(e,a){g["a"].get("/system/SYSM01B9/delete?key"+t.id).then((function(t){200==t.code?0==t.data?a("删除失败"):e("删除成功"):a("删除失败")})).catch((function(e){a("删除失败")}))})),o.push(a);case 3:case"end":return e.stop()}}),e)})),r.s();case 8:if((l=r.n()).done){e.next=12;break}return e.delegateYield(c(),"t0",10);case 10:e.next=8;break;case 12:e.next=17;break;case 14:e.prev=14,e.t1=e["catch"](5),r.e(e.t1);case 17:return e.prev=17,r.f(),e.finish(17);case 20:return e.next=22,Promise.all(o).then((function(e){t.$message.success("删除成功"),a.selectList=[]})).catch((function(e){t.$message.warning(e)})).finally((function(){t.bindData()}));case 22:a.$refs.tableList.clearSelection();case 23:case"end":return e.stop()}}),e,null,[[5,14,17,20]])})))()},search:function(e){""!=e?this.queryParams.SearchPojo={rolename:e,rolecode:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(e){this.idx=e,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(e){this.idx=e}}},z=P,D=(a("6e18"),Object(f["a"])(z,i,o,!1,null,"840d8216",null));t["default"]=D.exports},f881:function(e,t,a){"use strict";a("61e1")},fa4c:function(e,t,a){}}]);