(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-257e8263"],{"01a9":function(e,t,a){},"0f9f":function(e,t,a){"use strict";a("383b")},"1468c":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnadd:function(t){return e.showform(0)},bindData:function(t){return e.$refs.tableList.bindData(e.projectRow)},btnsearch:function(t){return e.$refs.tableList.search(t)},AdvancedSearch:function(t){return e.$refs.tableList.AdvancedSearch(t)},btnExport:function(t){return e.$refs.tableList.btnExport()},btnHelp:e.btnHelp}}),a("el-row",[a("el-col",{attrs:{span:e.showHelp?20:24}},[a("TableList",{ref:"tableList",attrs:{projectRow:e.projectRow},on:{changeidx:e.changeidx,showform:e.showform,sendTableForm:e.sendTableForm}})],1),a("el-col",{attrs:{span:e.showHelp?4:0}},[a("HelpModel",{ref:"helpmodel",attrs:{code:"S06M25B1"}})],1)],1)],1),e.formvisible?a("el-dialog",{attrs:{title:"开发活动计划","append-to-body":!0,visible:e.formvisible,width:"700px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.formvisible=t}}},[a("formedit",{ref:"formedit",attrs:{idx:e.idx,projectRow:e.projectRow},on:{bindData:function(t){return e.$refs.tableList.bindData(e.projectRow)},closeDialog:function(t){e.formvisible=!1}}})],1):e._e()],1)},n=[],r=a("ade3"),o=a("b775"),s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":"100px",rules:e.formRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("name")}}},[a("el-form-item",{attrs:{label:"活动名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"活动名称",clearable:""},model:{value:e.formdata.name,callback:function(t){e.$set(e.formdata,"name",t)},expression:"formdata.name"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("principal")}}},[a("el-form-item",{attrs:{label:"负责人",prop:"principal"}},[a("autoComplete",{attrs:{size:"default",value:e.formdata.principal,baseurl:"/S06M02S2/getPageList",params:{name:"engineername",other:"engineertype"}},on:{setRow:function(t){return e.setRow(t)},autoClear:function(t){return e.autoClear()}}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("deadline")}}},[a("el-form-item",{attrs:{label:"截止时间",prop:"deadline"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss",type:"datetime",clearable:"",placeholder:"选择日期"},model:{value:e.formdata.deadline,callback:function(t){e.$set(e.formdata,"deadline",t)},expression:"formdata.deadline"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("onlinemode")}}},[a("el-form-item",{attrs:{label:"上线方式",prop:"onlinemode"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"上线方式"},model:{value:e.formdata.onlinemode,callback:function(t){e.$set(e.formdata,"onlinemode",t)},expression:"formdata.onlinemode"}},[a("el-option",{attrs:{label:"线上",value:"线上"}}),a("el-option",{attrs:{label:"上门",value:"上门"}})],1)],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("description")}}},[a("el-form-item",{attrs:{label:"完成率"}},[a("el-slider",{attrs:{step:10,"show-stops":""},model:{value:e.formdata.completionrate,callback:function(t){e.$set(e.formdata,"completionrate",t)},expression:"formdata.completionrate"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("rownum")}}},[a("el-form-item",{attrs:{label:"排序",prop:"rownum"}},[a("el-input-number",{attrs:{min:0,label:"请输入排序"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)]),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"","label-width":"20px"}},[a("el-checkbox",{attrs:{label:"标记中止","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.closed,callback:function(t){e.$set(e.formdata,"closed",t)},expression:"formdata.closed"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent"},[a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",clearable:""},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1)],1),a("el-row",[a("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px"},attrs:{span:24}},[a("div",[e.formdata.id?a("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.handleDelete()}}},[e._v("删除")]):e._e()],1),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("formdata")}}},[e._v("保存")]),a("el-button",{on:{click:e.closeDialog}},[e._v("取消")])],1)])],1)],1)])])},l=[];a("b64b");const c={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);o["a"].post("/S06M25B1/create",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);o["a"].post("/S06M25B1/update",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{o["a"].get("/S06M25B1/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var d=c,m=a("5b24"),u={name:"addDialog",props:["idx","projectRow"],components:{autoComplete:m["a"]},data:function(){return{title:"开发活动计划",currentId:this.idx,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,name:"",principalid:"",principal:"",deadline:new Date,completionrate:0,onlinemode:"线上",closed:0,rownum:0,id:""},formRules:{name:[{required:!0,trigger:"blur",message:"名称不能为空"}],principal:[{required:!0,trigger:"blur",message:"负责人不能为空"}]}}},watch:{idx:function(e,t){this.currentId=e,this.bindData()}},mounted:function(){},methods:{bindData:function(){var e=this;this.formdata.principalid=this.projectRow.id,this.formdata.principal=this.projectRow.principal,0!=this.currentId&&o["a"].get("/S06M25B1/getEntity?key=".concat(this.currentId)).then((function(t){200==t.data.code?e.formdata=t.data.data:e.$message.warning(t.data.msg||"获取开发活动计划信息失败")})).catch((function(t){e.$message.error(t||"请求错误")}))},submitForm:function(e){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return!1;t.saveForm()}))},saveForm:function(){var e=this,t=Object.assign({},this.formdata);0==this.idx?d.add(t).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.formdata=t.data.data,e.$emit("bindData"))})).catch((function(t){e.$message.warning(t||"保存失败")})):d.update(t).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("changeIdx",t.data.id),e.formdata=t.data.data,e.$emit("bindData"))})).catch((function(t){e.$message.warning(t||"保存失败"),e.closeDialog()}))},handleDelete:function(){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.delete(e.formdata.id).then((function(t){200==t.code&&(e.$message.success("删除成功"),e.$emit("bindData"),e.closeDialog())})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},setRow:function(e){this.formdata.principalid=e.id,this.formdata.principal=e.engineername,this.cleValidate("principal")},autoClear:function(){this.formdata.principalid="",this.formdata.principal=""}}},f=u,p=(a("0f9f"),a("2877")),h=Object(p["a"])(f,s,l,!1,null,"369e6a4c",null),b=h.exports,w=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",attrs:{data:e.lst,"summary-method":e.getSummaries,"show-summary":"","element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.$index+1))])]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["name"==t.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showform(i.row.id)}}},[e._v(e._s(i.row.name?i.row.name:"名称"))]):"createdate"==t.itemcode||"deadline"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormat")(i.row[t.itemcode])))]):"completionrate"==t.itemcode?a("div",[e._v(" "+e._s(i.row[t.itemcode]+"%")+" ")]):"closed"==t.itemcode?a("div",[i.row[t.itemcode]?a("el-tag",{attrs:{size:"small",type:"warning"}},[e._v("中止")]):a("el-tag",{attrs:{size:"small"}},[e._v("正常")])],1):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.GetList}})],1)],1)},g=[],v=a("c7eb"),y=a("1da1"),x=(a("d81d"),a("e9c4"),a("d3b7"),a("0643"),a("a573"),a("48da")),S={formcode:"S06M25B1List",item:[{itemcode:"name",itemname:"活动名称",minwidth:"100",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center",datasheet:"Sa_ImplementPlan.name"},{itemcode:"principal",itemname:"负责人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_ImplementPlan.principal"},{itemcode:"deadline",itemname:"截止时间",minwidth:"100",displaymark:1,overflow:0,datasheet:"Sa_ImplementPlan.deadline"},{itemcode:"onlinemode",itemname:"上线方式",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_ImplementPlan.onlinemode"},{itemcode:"completionrate",itemname:"完成率",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_ImplementPlan.completionrate"},{itemcode:"closed",itemname:"标记中止",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_ImplementPlan.closed"},{itemcode:"rownum",itemname:"排序",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_ImplementPlan.rownum"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_ImplementPlan.lister"},{itemcode:"createdate",itemname:"创建日期",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_ImplementPlan.createdate"}]},$=a("b893"),k={components:{},props:["projectRow"],data:function(){return{lst:[],total:0,listLoading:!1,idx:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:S,selectList:[]}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},updated:function(){var e=this;this.$nextTick((function(){e.$refs.tableList.doLayout()}))},methods:{bindData:function(e){var t=this;this.listLoading=!0;var a="/S06M25B1/getPageList";this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),o["a"].post(a,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"获取计划信息失败"),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getcolumn:function(){var e=this;return Object(y["a"])(Object(v["a"])().mark((function t(){return Object(v["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.$emit("sendTableForm",e.tableForm);case 1:case"end":return t.stop()}}),t)})))()},getSummaries:function(e){return Object($["e"])(e,["amount"])},GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},handleSelectionChange:function(e){this.selectList=e},search:function(e){""!=e?this.queryParams.SearchPojo={description:e,version:e,releasename:e,remark:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData(this.projectRow)},AdvancedSearch:function(e){this.queryParams.scenedata=e,""==e[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(e){"descending"==e.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(e,this.tableForm),this.bindData()},showform:function(e){this.$emit("showform",e)},btnExport:function(){var e=this;Promise.resolve().then(function(){for(var t=[],a=[],i=0;i<e.tableForm.item.length;i++){var n=e.tableForm.item[i];n.displaymark&&(t.push(n.itemname),a.push(n.itemcode))}var r=e.lst,o=e.formatJson(a,r);Object(x["a"])(t,o,"版本发布")}.bind(null,a)).catch(a.oe)},formatJson:function(e,t){return t.map((function(t){return e.map((function(e){return t[e]}))}))}}},P=k,_=(a("1b1a"),Object(p["a"])(P,w,g,!1,null,"f7544526",null)),D=_.exports,j=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:e.btnadd}},[e._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:function(t){return e.$emit("btnExport")}}})],1)])])},L=[],F={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M12B1List"}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},q=F,O=(a("ccc2"),Object(p["a"])(q,j,L,!1,null,"0cc9199b",null)),I=O.exports,C={name:"S06M25B1",components:Object(r["a"])({formedit:b,TableList:D,listheader:I},"formedit",b),data:function(){return{lst:[],formvisible:!1,idx:0,total:0,projectData:[],showHelp:!1,tableForm:{},projectRow:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-160}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){this.$refs.tableList.bindData()},sendTableForm:function(e){this.tableForm=e},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},showform:function(e){var t=this;this.idx=e,this.formvisible=!0,setTimeout((function(){t.$refs.formedit.bindData()}),10),this.$forceUpdate()},closeForm:function(){this.formvisible=!1},compForm:function(){this.formvisible=!1},changeidx:function(e){this.idx=e}}},T=C,H=(a("1864"),Object(p["a"])(T,i,n,!1,null,"c9f94ac4",null));t["default"]=H.exports},1864:function(e,t,a){"use strict";a("01a9")},"1b1a":function(e,t,a){"use strict";a("7c77")},"383b":function(e,t,a){},"5cc6":function(e,t,a){var i=a("74e8");i("Uint8",(function(e){return function(t,a,i){return e(this,t,a,i)}}))},"7c77":function(e,t,a){},ccc2:function(e,t,a){"use strict";a("df88")},df88:function(e,t,a){}}]);