(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2cfd0749"],{"0515":function(t,e,n){},10:function(t,e){},11:function(t,e){},12:function(t,e){},1446:function(t,e,n){},"283f":function(t,e,n){"use strict";n("0515")},3:function(t,e){},4:function(t,e){},5:function(t,e){},6:function(t,e){},7:function(t,e){},8:function(t,e){},9:function(t,e){},"9ed6":function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"login-container flex a-c j-end",staticStyle:{width:"100%"}},[n("div",{staticClass:"p-r",staticStyle:{width:"100%",height:"100%",display:"flex","justify-content":"center","align-items":"center"}},[n("div",{staticClass:"logoBox"}),n("div",{staticClass:"login",staticStyle:{"z-index":"99",position:"relative"}},[t.formVisable?n("el-form",{ref:"loginForm",staticClass:"login-form",attrs:{model:t.loginForm,rules:t.loginRules,"auto-complete":"on","label-position":"left"}},[n("div",{staticClass:"title-container"},[n("h3",{staticClass:"title"},[t._v(t._s(t.apptitle))])]),n("div",{directives:[{name:"show",rawName:"v-show",value:t.isErcode,expression:"isErcode"}]},[n("div",{staticStyle:{"font-size":"20px","text-align":"center",padding:"0 0 20px 0"}},[n("span",{staticStyle:{color:"#6285f7"}},[t._v("账号密码")]),t._v("登录 ")]),n("div",{staticClass:"ercode_tab swicth-ercode",on:{click:function(e){return t.openErcode()}}},[n("svg",{attrs:{width:"52",height:"52","xmlns:xlink":"http://www.w3.org/1999/xlink",fill:"currentColor"}},[n("defs",[n("path",{attrs:{id:"id-3938311804-a",d:"M0 0h48a4 4 0 0 1 4 4v48L0 0z"}})]),n("g",{attrs:{fill:"none","fill-rule":"evenodd"}},[n("mask",{attrs:{id:"id-3938311804-b",fill:"#fff"}},[n("use",{attrs:{"xlink:href":"#id-3938311804-a"}})]),n("use",{attrs:{fill:"#0084FF","xlink:href":"#id-3938311804-a"}}),n("image",{attrs:{width:"52",height:"52",mask:"url(#id-3938311804-b)","xlink:href":"data:image/png;base64,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"}})])])]),n("el-form-item",{staticStyle:{"margin-bottom":"30px"},attrs:{prop:"UserName"}},[n("span",{staticClass:"svg-container"},[n("svg-icon",{attrs:{"icon-class":"user"}})],1),n("el-input",{ref:"UserName",attrs:{placeholder:"手机和邮箱",name:"UserName",type:"text",tabindex:"1","auto-complete":"off"},model:{value:t.loginForm.UserName,callback:function(e){t.$set(t.loginForm,"UserName",e)},expression:"loginForm.UserName"}})],1),n("el-form-item",{staticStyle:{"margin-bottom":"30px"},attrs:{prop:"Password"}},[n("span",{staticClass:"svg-container"},[n("svg-icon",{attrs:{"icon-class":"password"}})],1),n("el-input",{key:t.passwordType,ref:"Password",attrs:{type:t.passwordType,placeholder:"密码",name:"Password",tabindex:"2","auto-complete":"off"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.validateCaptcha(e)}},model:{value:t.loginForm.Password,callback:function(e){t.$set(t.loginForm,"Password",e)},expression:"loginForm.Password"}}),n("span",{staticClass:"show-pwd",on:{click:t.showPwd}},[n("svg-icon",{attrs:{"icon-class":"password"===t.passwordType?"eye":"eye-open"}})],1)],1),n("div",{staticClass:"passwordOpera"},[n("el-checkbox",{attrs:{"true-label":1,"false-label":0},model:{value:t.loginForm.lockPwd,callback:function(e){t.$set(t.loginForm,"lockPwd",e)},expression:"loginForm.lockPwd"}},[t._v("记住密码")])],1),n("br"),n("el-button",{staticStyle:{width:"100%","margin-bottom":"10px"},attrs:{loading:t.loading,type:"primary"},nativeOn:{click:function(e){return e.preventDefault(),t.validateCaptcha(e)}}},[t._v("登录")])],1),n("div",{directives:[{name:"show",rawName:"v-show",value:!t.isErcode,expression:"!isErcode"}]},[n("div",{staticStyle:{"font-size":"20px","text-align":"center",padding:"0 0 20px 0"}},[n("span",{staticStyle:{color:"#6285f7"}},[t._v("微信扫码")]),t._v("一键登录 ")]),n("div",{staticClass:"ercode_tab switch-input",on:{click:function(e){return t.changeErcode()}}},[n("svg",{attrs:{width:"52",height:"52","xmlns:xlink":"http://www.w3.org/1999/xlink",fill:"currentColor"}},[n("defs",[n("path",{attrs:{id:"id-14580708-a",d:"M0 0h48a4 4 0 0 1 4 4v48L0 0z"}})]),n("g",{attrs:{fill:"none","fill-rule":"evenodd"}},[n("mask",{attrs:{id:"id-14580708-b",fill:"#fff"}},[n("use",{attrs:{"xlink:href":"#id-14580708-a"}})]),n("use",{attrs:{fill:"#0084FF","xlink:href":"#id-14580708-a"}}),n("path",{attrs:{fill:"#FFF",d:"M22.125 4h13.75A4.125 4.125 0 0 1 40 8.125v27.75A4.125 4.125 0 0 1 35.875 40h-13.75A4.125 4.125 0 0 1 18 35.875V8.125A4.125 4.125 0 0 1 22.125 4zm6.938 34.222c1.139 0 2.062-.945 2.062-2.11 0-1.167-.923-2.112-2.063-2.112-1.139 0-2.062.945-2.062 2.111 0 1.166.923 2.111 2.063 2.111zM21 8.333v24h16v-24H21z",mask:"url(#id-14580708-b)"}}),n("g",{attrs:{mask:"url(#id-14580708-b)"}},[n("path",{attrs:{fill:"#FFF",d:"M46.996 15.482L39 19.064l-7.996-3.582A1.6 1.6 0 0 1 32.6 14h12.8a1.6 1.6 0 0 1 1.596 1.482zM47 16.646V24.4a1.6 1.6 0 0 1-1.6 1.6H32.6a1.6 1.6 0 0 1-1.6-1.6v-7.754l8 3.584 8-3.584z"}}),n("path",{attrs:{fill:"#0084FF",d:"M31 15.483v1.17l8 3.577 8-3.577v-1.17l-8 3.581z","fill-rule":"nonzero"}})])])])]),n("div",{staticClass:"ercode",staticStyle:{width:"200px",margin:"0 auto",position:"relative"}},[n("el-image",{staticStyle:{width:"200px",height:"200px",border:"1px solid #ccc","border-radius":"10px"},attrs:{src:t.qrcodeurl}},[n("div",{staticClass:"image-slot",attrs:{slot:"error"},slot:"error"},[n("i",{staticClass:"el-icon-picture-outline",staticStyle:{"font-size":"50px","font-weight":"200"}}),n("span",{staticStyle:{color:"#666","padding-top":"10px"}},[t._v("二维码加载中......")])])]),t.iscodeError?n("div",{staticClass:"qrcodeMark"}):t._e()],1),t.iscodeError?n("div",{staticClass:"codeError",on:{click:function(e){return t.openErcode()}}},[n("i",{staticClass:"el-icon-refresh-left"}),t._v(" 二维码失效，请重新生成 ")]):n("div",{staticClass:"ercode-foot"},[n("div",[t._v("打开 "),n("span",[t._v("微信App")])]),n("div",{staticStyle:{"font-size":"12px","margin-top":"10px"}},[t._v(" 在「首页」右上角打开扫一扫 ")])])]),n("el-divider",[t._v("登录方式")]),n("div",{staticClass:"loginType"},[n("div",{staticStyle:{cursor:"pointer"},on:{click:t.openErcode}},[n("svg-icon",{staticStyle:{color:"#8a8a8a"},attrs:{"icon-class":"wechat-fill"}}),n("span",{staticStyle:{"padding-left":"5px"}},[t._v("微信扫码")])],1),n("div",{staticStyle:{cursor:"pointer"},on:{click:t.changeErcode}},[n("svg-icon",{attrs:{"icon-class":"phone-iphone"}}),n("span",{staticStyle:{"padding-left":"5px"}},[t._v("账号密码")])],1)])],1):t._e(),n("div",{staticStyle:{color:"#666","font-size":"14px","text-align":"center",position:"absolute",bottom:"18px",width:"100%"}},[t._v(" Copyright@嘉兴应凯科技有限公司 版权所有 ")])],1)]),n("div",{staticClass:"contactUs"},[n("a",{attrs:{href:"http://www.inkstech.com",target:"_blank"}},[t._v(" 联系我们")]),n("a",{attrs:{href:"#"},on:{click:function(e){return t.Collection(e)}}},[t._v(" 收藏")]),n("a",{attrs:{href:"#"},on:{click:t.routerTo}},[t._v(" 首页")])])])},A=[],a=n("c7eb"),l=n("1da1"),o=(n("99af"),n("e9c4"),n("a9e3"),n("d3b7"),n("25f0"),n("4d90"),n("498a"),n("b775")),r=n("3452"),s=n.n(r),c=(n("4360"),n("5f87")),g=(n("d044"),[{name:"需求",path:"/S06",pid:null,meta:{icon:"D01M01B1",title:"需求"},children:[{name:"需求申请",path:"1",meta:{icon:"",title:"需求管理"},pid:null,children:[{children:null,name:"需求提报",path:"/S06/M02B3",meta:{icon:"",title:"需求提报"},pid:null},{children:null,name:"需求平台",path:"/S06/M02B4",meta:{icon:"",title:"需求平台"},pid:null},{children:null,name:"需求工作平台",path:"/S06/M02R1",meta:{icon:"",title:"需求工作平台"},pid:null}]},{name:"需求处理",path:"2",meta:{icon:"",title:"需求处理"},pid:null,children:[{children:null,name:"服务派工",path:"/S06/M07B1",meta:{icon:"",title:"服务派工"},pid:null},{children:null,name:"客户信息",path:"/S06/M14B1",meta:{icon:"",title:"客户信息"},pid:null},{children:null,name:"反馈单",path:"/S06/M28B1",meta:{icon:"",title:"反馈单"},pid:null}]},{name:"基础设置",path:"/S06M24B1",meta:{icon:"",title:"基础设置"},pid:null,children:[{children:null,name:"工程项目",path:"/S06/M01S1",meta:{icon:"",title:"工程项目"},pid:null},{children:null,name:"工程师",path:"/S06/M02S2",meta:{icon:"",title:"工程师"},pid:null},{children:null,name:"需求状态",path:"/S06/M02S3",meta:{icon:"",title:"需求状态"},pid:null},{children:null,name:"需求标签",path:"/S06/M02S6",meta:{icon:"",title:"需求标签"},pid:null},{children:null,name:"活动阶段",path:"/S06/M29S1",meta:{icon:"",title:"活动阶段"},pid:null}]},{name:"报表",path:"",meta:{icon:"",title:"报表"},pid:null,children:[{children:null,name:"开发活动计划",path:"/S06/M24B1",meta:{icon:"",title:"开发活动计划"},pid:null},{children:null,name:"实施活动计划",path:"/S06/M25B1",meta:{icon:"",title:"实施活动计划"},pid:null},{children:null,name:"活动中心",path:"/S06/M29B1",meta:{icon:"",title:"活动中心"},pid:null}]}]},{name:"任务",path:"4",pid:null,meta:{icon:"D05M01B1",title:"任务"},children:[{name:"TODO报表",path:"",meta:{icon:"",title:"TODO报表"},pid:null,children:[{children:null,name:"TODO列表",path:"/S06/M09B1",meta:{icon:"",title:"TODO列表"},pid:null},{children:null,name:"TODO类型",path:"/S06/M09R1",meta:{icon:"",title:"TODO类型"},pid:null},{children:null,name:"TODO时间",path:"/S06/M09R2",meta:{icon:"",title:"TODO时间"},pid:null},{children:null,name:"TODO大屏",path:"/S06/MBIR1",meta:{icon:"",title:"TODO大屏"},pid:null}]},{name:"工作日志",path:"",meta:{icon:"",title:"工作日志"},pid:null,children:[{children:null,name:"工作日志",path:"/S06/M10B1",meta:{icon:"",title:"工作日志"},pid:null},{children:null,name:"会议管理",path:"/S06/M15B1",meta:{icon:"",title:"会议管理"},pid:null}]},{name:"基础设置",path:"",meta:{icon:"",title:"基础设置"},pid:null,children:[{children:null,name:"用户设置",path:"/S06/M09B1USER",meta:{icon:"",title:"用户设置"},pid:null},{children:null,name:"类型设置",path:"/S06/M09B1TYPE",meta:{icon:"",title:"类型设置"},pid:null},{children:null,name:"跟踪模板",path:"/S06/M19S1",meta:{icon:"",title:"跟踪模板"},pid:null}]},{name:"报表",path:"",meta:{icon:"",title:"报表"},pid:null,children:[{children:null,name:"多任务跟踪表",path:"/S06/M19B1",meta:{icon:"",title:"多任务跟踪表"},pid:null},{children:null,name:"缺陷报告",path:"/S06/M20B1",meta:{icon:"",title:"缺陷报告"},pid:null},{children:null,name:"MQTT日志",path:"/S06/M30B1",meta:{icon:"",title:"MQTT日志"},pid:null}]}]},{name:"研发",path:"yanfa",pid:null,meta:{icon:"bug",title:"研发"},children:[{name:"研发中心",path:"1",meta:{icon:"",title:"研发中心"},pid:null,children:[{children:null,name:"功能中心",path:"/S06/M38B1",meta:{icon:"",title:"功能中心"},pid:null},{children:null,name:"SQL变更",path:"/S06/M41B1",meta:{icon:"",title:"SQL变更"},pid:null},{children:null,name:"版本发布",path:"/S06/M12B1",meta:{icon:"",title:"版本发布"},pid:null},{children:null,name:"创意池",path:"/S06/M37B1",meta:{icon:"",title:"创意池"},pid:null}]}]},{name:"产品",path:"chanpin",pid:null,meta:{icon:"D04M04R01",title:"产品"},children:[{name:"产品中心",path:"1",meta:{icon:"",title:"产品中心"},pid:null,children:[{children:null,name:"解决方案",path:"/S06/M22B1",meta:{icon:"",title:"解决方案"},pid:null},{children:null,name:"产品中心",path:"/S06/M13B1",meta:{icon:"",title:"产品中心"},pid:null},{children:null,name:"产品介绍",path:"/S06/M08B1",meta:{icon:"",title:"产品介绍"},pid:null},{children:null,name:"扩展功能",path:"/S06/M88B1",meta:{icon:"",title:"扩展功能"},pid:null}]}]},{name:"文库",path:"/S05",pid:null,meta:{icon:"D03M01B1",title:"文库"},children:[{name:"视频设置",path:"1",meta:{icon:"",title:"视频设置"},pid:null,children:[{children:null,name:"视频中心",path:"/S06/M05B1",meta:{icon:"",title:"视频中心"},pid:null},{children:null,name:"文件中心",path:"/Sa/File",meta:{icon:"",title:"文件中心"},pid:null},{children:null,name:"知识库",path:"/S06/M04B1",meta:{icon:"",title:"知识库"},pid:null}]},{name:"文档中心",path:"/S06/M95B3",meta:{icon:"",title:"文档中心"},pid:null,children:[{children:null,name:"文档仓库",path:"/S06/M95B3",meta:{icon:"",title:"文档仓库"},pid:null},{children:null,name:"文档用户",path:"/S06/M95B4",meta:{icon:"",title:"文档用户"},pid:null},{children:null,name:"文档中心",path:"/S06/M11S1",meta:{icon:"",title:"文档中心"},pid:null}]},{name:"LNK简书",path:"S06/M11B1",meta:{icon:"",title:"LNK简书"},pid:null,children:[{children:null,name:"简书",path:"/S06/M11R1",meta:{icon:"",title:"简书"},pid:null},{children:null,name:"LNK简书",path:"/S06/M11B1",meta:{icon:"",title:"LNK简书"},pid:null},{children:null,name:"项目设置",path:"/S06/M11B2",meta:{icon:"",title:"项目设置"},pid:null}]}]},{name:"基础",path:"/S04",pid:null,meta:{icon:"D05M01B1",title:"基础"},children:[{name:"基础设置",path:"1",meta:{icon:"D10M02B1",title:"基础设置"},pid:null,children:[{children:null,name:"广告通知",path:"/S06/M06B2",meta:{icon:"",title:"广告通知"},pid:null}]},{name:"代码生成",path:"2",meta:{icon:"D10M02B1",title:"代码生成"},pid:null,children:[{children:null,name:"代码生成器模板",path:"/S06/M16B1",meta:{icon:"",title:"代码生成器模板"},pid:null},{children:null,name:"代码分组",path:"/S06/M16B2",meta:{icon:"",title:"代码分组"},pid:null},{children:null,name:"代码生成器",path:"/S06/M16B3",meta:{icon:"",title:"代码生成器"},pid:null}]}]},{name:"系统",path:"9",pid:null,meta:{icon:"user",title:"系统"},children:[{name:"系统配置",path:"",meta:{icon:"",title:"系统配置"},pid:null,children:[{children:null,name:"系统参数",path:"/Sa/Config",meta:{icon:"",title:"系统参数"},pid:null},{children:null,name:"Web菜单",path:"/Sa/MenuWeb",meta:{icon:"",title:"Web菜单"},pid:null},{children:null,name:"App菜单",path:"/Sa/MenuApp",meta:{icon:"",title:"App菜单"},pid:null}]},{name:"数据参数",path:"",meta:{icon:"",title:"数据参数"},pid:null,children:[{children:null,name:"报表中心",path:"/Sa/Reports",meta:{icon:"",title:"报表中心"},pid:null},{children:null,name:"数据字典",path:"/Sa/Dict",meta:{icon:"",title:"数据字典"},pid:null},{children:null,name:"报表库",path:"/S06/M27B1",meta:{icon:"",title:"报表库"},pid:null}]}]},{name:"外链",path:"blank",pid:null,meta:{icon:"el-icon-share",title:"外链"},children:[{name:"相关外链",path:"",meta:{icon:"",title:"相关外链"},pid:null,children:[{children:null,name:"智汇云桥",path:"https://kid.inksyun.com",isBlank:!0,meta:{icon:"",title:"智汇云桥"},pid:null},{children:null,name:"应凯在线学习平台",path:"http://edu.inksyun.com",isBlank:!0,meta:{icon:"",title:"应凯在线学习平台"},pid:null}]}]}]),d=[{name:"需求",path:"/S06",pid:null,meta:{icon:"D01M01B1",title:"需求"},children:[{name:"需求设置",path:"1",meta:{icon:"",title:"需求设置"},pid:null,children:[{children:null,name:"需求单",path:"/S06/M02B1user",meta:{icon:"",title:"需求单"},pid:null}]}]}],C={name:"Login",components:{},data:function(){var t=function(t,e,n){0==e.trim().length?n(new Error("请输入正确的用户名")):n()},e=function(t,e,n){e.length<6?n(new Error("密码不能少于 6 位")):n()};return{formVisable:!0,dialogWidth:"800px",loginForm:{UserName:"",Password:"",Code:"",lockPwd:0},loginRules:{UserName:[{required:!0,trigger:"blur",validator:t}],Password:[{required:!0,trigger:"blur",validator:e}],Code:[{required:!0,trigger:"blur",message:"验证码为必填项"}]},isErcode:!0,loading:!1,passwordType:"password",redirect:void 0,API:this.$store.state.app.config.baseURL,apptitle:this.$store.state.app.config.title,iscodeError:!1,time_set:null,scankey:"",codeUrl:"",uuid:"",qrcodeurl:"",sceneStr:"",timer:null}},watch:{$route:{handler:function(t){this.redirect=t.query&&t.query.redirect},immediate:!0}},created:function(){"notInDingTalk"!=this.$store.state.app.platform?this.showding=!0:this.showding=!1},mounted:function(){this.getCookie()},destroyed:function(){window.clearInterval(this.time_set)},methods:{routerTo:function(){this.$router.push("/home"),sessionStorage.setItem("navIndex",0)},showPwd:function(){"password"===this.passwordType?this.passwordType="":this.passwordType="password"},validateCaptcha:function(){var t=this;this.$refs.loginForm.validate((function(e){if(!e)return!1;t.$SetTrackingLog("login_submit",t.loginForm.UserName);var n={username:t.loginForm.UserName,password:t.loginForm.Password};o["a"].post("/PmsSaUser/login?type=1",JSON.stringify(n)).then((function(e){if(console.log(e),200==e.data.code){1==t.loginForm.lockPwd?t.setCookie(t.loginForm.UserName,t.loginForm.Password,30):t.clearCookie(),t.$notify({title:"登陆成功",type:"success",message:"欢迎登录"});var n=e.data.data.loginuser;n.openid=e.data.data.openid,n.registrkey=e.data.data.registrkey,localStorage.setItem("getInfo",JSON.stringify(n)),t.$store.state.user.userinfo=e.data.data.loginuser,Object(c["c"])(e.data.data.access_token),t.$store.state.user.token=Object(c["a"])(),t.remove(),t.$SetTrackingLog("login_success","","/PmsSaUser/login")}else t.$notify.error({title:"登陆失败",message:e.data.msg}),t.$SetTrackingLog("login_fail",e.data.msg,"/PmsSaUser/login")}))}))},remove:function(){var t=this,e=[];this.$request.get("/SaMenuWeb/getMenuWebListByLoginUser").then((function(n){200==n.data.code?0!=n.data.data.length?(e=n.data.data,localStorage.setItem("navjson",JSON.stringify(e)),t.$store.dispatch("app/setnavdata",e),t.$router.push({path:"/dashboard"})):o["a"].get("/SaUser/getUserInfo").then((function(n){200==n.data.code&&(e=n.data.data.adminmark?g:d,localStorage.setItem("navjson",JSON.stringify(e)),t.$store.dispatch("app/setnavdata",e),t.$router.push({path:"/dashboard"}))})):t.$message.error(n.data.msg||"获取菜单失败")})).catch((function(e){console.log(e),t.$message.error(e||"请求失败")}))},dateFormat:function(){var t=new Date,e=t.getFullYear(),n=(t.getMonth()+1).toString().padStart(2,"0"),i=t.getDate().toString().padStart(2,"0"),A=t.getHours().toString().padStart(2,"0"),a=t.getMinutes().toString().padStart(2,"0"),l=t.getSeconds().toString().padStart(2,"0");return"".concat(e,"-").concat(n,"-").concat(i," ").concat(A,":").concat(a,":").concat(l)},setCookie:function(t,e,n){var i=s.a.AES.encrypt(e,"inks").toString(),A=new Date;A.setTime(A.getTime()+864e5*n),window.document.cookie="userName="+t+";path=/;expires="+A.toGMTString(),window.document.cookie="userPwd="+i+";path=/;expires="+A.toGMTString(),window.document.cookie="lockPwd=1;path=/;expires="+A.toGMTString()},getCookie:function(){if(document.cookie.length>0)for(var t=document.cookie.split("; "),e=0;e<t.length;e++){var n=t[e].split("=");if("userName"==n[0])this.loginForm.UserName=n[1];else if("userPwd"==n[0]){var i=n[1],A=s.a.AES.decrypt(i,"inks");this.loginForm.Password=A.toString(s.a.enc.Utf8)}else"lockPwd"==n[0]&&(this.loginForm.lockPwd=Number(n[1]))}},clearCookie:function(){this.setCookie("","",-1)},openErcode:function(){var t=this;this.qrcodeurl="";var e=this;e.$nextTick((function(){e.isErcode=!1})),this.getWxQrcode().then((function(){clearInterval(t.time_set),t.time_set=setInterval((function(){t.isLoginSuccess(t.sceneStr)}),1e3)}))},getWxQrcode:function(){var t=this;return Object(l["a"])(Object(a["a"])().mark((function e(){return Object(a["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.iscodeError=!1,o["a"].post("".concat(t.$store.state.app.config.oamapi,"/wx/qrcode/").concat(t.$store.state.app.config.wxappid,"/qrcodes?expireSeconds=")+60).then((function(e){t.qrcodeurl=e.data.data.url,t.sceneStr=e.data.data.sceneStr}));case 2:case"end":return e.stop()}}),e)})))()},isLoginSuccess:function(t){var e=this;t&&o["a"].post("".concat(this.$store.state.app.config.oamapi,"/wx/qrcode/").concat(this.$store.state.app.config.wxappid,"/qrcodesResult?sceneStr=")+this.sceneStr).then((function(t){200==t.data.code?0!==t.data.data.result&&e.getLogin(t.data.data.openidToken):500==t.data.code&&(e.iscodeError=!0,window.clearInterval(that.time_set))}))},getLogin:function(t){var e=this;o["a"].post("/PmsSaUser/loginByOpenidToken?openidToken="+t).then((function(n){if(200===n.data.code){window.clearInterval(e.time_set),e.$notify({title:"登陆成功",type:"success",message:"欢迎登录"});var i=n.data.data.loginuser;i.openid=t,e.sendMqttMsg({getInfo:i,type:"扫码登录成功"}),localStorage.setItem("getInfo",JSON.stringify(i)),e.$store.state.user.userinfo=n.data.data.loginuser,Object(c["c"])(n.data.data.access_token),e.$store.state.user.token=Object(c["a"])(),e.remove(),e.$SetTrackingLog("login_wx",i.realname,"/PmsSaUser/loginByOpenidToken")}else 500==n.data.code&&(e.$message.warning("请先绑定微信，再扫码登录！"),window.clearInterval(e.time_set),e.sendMqttMsg({getInfo:{logintime:new Date,realname:"",ipaddr:""},type:"扫码登录失败"}))}))},changeErcode:function(){var t=this;t.$nextTick((function(){t.isErcode=!0})),window.clearInterval(t.time_set)},Collection:function(){try{window.external.addFavorite(location.href,document.title)}catch(t){try{window.sidebar.addPanel(document.title,location.href,"")}catch(t){alert("加入收藏失败，请按Ctrl+D手动添加。")}}}}},B=C,E=(n("283f"),n("e546"),n("2877")),Q=Object(E["a"])(B,i,A,!1,null,"36f01c9f",null);e["default"]=Q.exports},e546:function(t,e,n){"use strict";n("1446")}}]);