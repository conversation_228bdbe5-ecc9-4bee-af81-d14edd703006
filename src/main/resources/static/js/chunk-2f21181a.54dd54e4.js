(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2f21181a"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),Math.easeInOutQuad=function(e,t,a,i){return e/=i/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function n(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(e,t,a){var o=r(),s=e-o,l=20,c=0;t="undefined"===typeof t?500:t;var d=function(){c+=l;var e=Math.easeInOutQuad(c,o,s,t);n(e),c<t?i(d):a&&"function"===typeof a&&a()};d()}},"36a3":function(e,t,a){"use strict";a("acf7")},"8f71":function(e,t,a){},aa58:function(e,t,a){"use strict";a("c243")},acf7:function(e,t,a){},c243:function(e,t,a){},ece9:function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnadd:function(t){return e.showform(0)},btnsearch:e.search,bindData:e.bindData,AdvancedSearch:e.AdvancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"sort-change":e.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["engineercode"==t.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showform(i.row.id)}}},[e._v(e._s(i.row.engineercode?i.row.engineercode:"编码"))]):"createdate"==t.itemcode||"modifydate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormats")(i.row[t.itemcode])))]):"enabledmark"==t.itemcode?a("div",[i.row.enabledmark?a("el-tag",{attrs:{size:"small"}},[e._v("启用")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("停 用")])],1):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]})),2==e.$store.state.user.userinfo.isadmin||null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype?a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handlePiPerMission(t.row.id,t.row)}}},[e._v("删除")])]}}],null,!1,828007154)}):e._e()],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.GetList}})],1)],1)],1)],1),e.FormVisible?a("el-dialog",{attrs:{width:"800px",title:"文档用户","append-to-body":!0,visible:e.FormVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.FormVisible=t},close:e.bindData}},[a("formedit",{ref:"formedit",attrs:{idx:e.idx},on:{bindData:e.bindData,closeDialog:function(t){e.FormVisible=!1}}})],1):e._e()],1)},n=[],r=(a("e9c4"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1),2==e.$store.state.user.userinfo.isadmin||null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype?a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:e.btnadd}},[e._v(" 添加 ")]):e._e()],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:e.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(t){e.iShow=!e.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:e.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"编号"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入编号",size:"small"},model:{value:e.formdata.doccode,callback:function(t){e.$set(e.formdata,"doccode",t)},expression:"formdata.doccode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"姓名"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入姓名",size:"small"},model:{value:e.formdata.docname,callback:function(t){e.$set(e.formdata,"docname",t)},expression:"formdata.docname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"关联用户"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入关联用户",size:"small"},model:{value:e.formdata.username,callback:function(t){e.$set(e.formdata,"username",t)},expression:"formdata.username"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"邮箱"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入邮箱",size:"small"},model:{value:e.formdata.email,callback:function(t){e.$set(e.formdata,"email",t)},expression:"formdata.email"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.AdvancedSearch()}}},[e._v(" 搜索 ")])],1)],1)],1)],1)])],1)}),o=[],s={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M95B4List"}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},l=s,c=(a("36a3"),a("2877")),d=Object(c["a"])(l,r,o,!1,null,"413ae2d4",null),m=d.exports,u=a("333d"),f=a("b775"),p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":"100px",rules:e.formRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("doccode")}}},[a("el-form-item",{attrs:{label:"编码",prop:"doccode"}},[a("el-input",{attrs:{placeholder:"请输入编码",clearable:""},model:{value:e.formdata.doccode,callback:function(t){e.$set(e.formdata,"doccode",t)},expression:"formdata.doccode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("docname")}}},[a("el-form-item",{attrs:{label:"姓名",prop:"docname"}},[a("el-input",{attrs:{placeholder:"请输入姓名",clearable:""},model:{value:e.formdata.docname,callback:function(t){e.$set(e.formdata,"docname",t)},expression:"formdata.docname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("doctype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"doctype"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择类型"},model:{value:e.formdata.doctype,callback:function(t){e.$set(e.formdata,"doctype",t)},expression:"formdata.doctype"}},[a("el-option",{attrs:{label:"管理者",value:"管理者"}}),a("el-option",{attrs:{label:"用户",value:"用户"}})],1)],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("userid")}}},[a("el-form-item",{attrs:{label:"关联用户",prop:"userid"}},[a("autoComplete",{attrs:{size:"default",value:e.formdata.username,baseurl:"/PmsSaUser/getPageList",params:{name:"username",other:"realname"}},on:{setRow:e.setRow,autoClear:e.autoClear}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:15}},[a("div",{on:{click:function(t){return e.cleValidate("email")}}},[a("el-form-item",{attrs:{label:"邮箱",prop:"email"}},[a("el-input",{attrs:{placeholder:"请输入邮箱",clearable:""},model:{value:e.formdata.email,callback:function(t){e.$set(e.formdata,"email",t)},expression:"formdata.email"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",[a("el-form-item",{attrs:{label:"状态"}},[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:e.formdata.enabledmark,callback:function(t){e.$set(e.formdata,"enabledmark",t)},expression:"formdata.enabledmark"}})],1)],1)])],1)],1)],1),a("el-form",{staticClass:"footFormContent"},[a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入摘要",clearable:""},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1)],1),a("el-row",{directives:[{name:"show",rawName:"v-show",value:2==e.$store.state.user.userinfo.isadmin||null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype,expression:"\n          $store.state.user.userinfo.isadmin == 2 ||\n          ($store.state.user.userinfo.engineer == null\n            ? false\n            : $store.state.user.userinfo.engineer.engineertype == '管理者')\n        "}]},[a("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"flex-end","margin-top":"20px"},attrs:{span:24}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("formdata")}}},[e._v("保存")]),a("el-button",{on:{click:e.closeDialog}},[e._v("取消")])],1)],1)],1)])])},h=[];a("b64b");const b={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);f["a"].post("/S06M95B4/create",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);f["a"].post("/S06M95B4/update",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{f["a"].get("/S06M95B4/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var g=b,w=a("5b24"),y={name:"addDialog",props:["idx"],components:{autoComplete:w["a"]},data:function(){return{title:"文档用户",currentId:this.idx,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,doccode:"",doctype:"用户",docname:"",remark:"",enabledmark:1,userid:"",username:"",email:""},formRules:{doccode:[{required:!0,trigger:"blur",message:"编码不能为空"}],docname:[{required:!0,trigger:"blur",message:"姓名不能为空"}]}}},watch:{idx:function(e,t){this.currentId=e,this.binddata()}},created:function(){this.binddata()},methods:{binddata:function(){var e=this;0!=this.currentId&&f["a"].get("/S06M95B4/getEntity?key=".concat(this.currentId)).then((function(t){200==t.data.code?e.formdata=t.data.data:e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeDialog()}})})).catch((function(t){e.$message.error(t||"请求错误")}))},submitForm:function(e){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return!1;t.saveForm()}))},saveForm:function(){var e=this;if(0==this.idx){var t=Object.assign({},this.formdata);g.add(t).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("bindData"))})).catch((function(t){e.$message.warning(t||"保存失败")}))}else g.update(this.formdata).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("changeIdx",t.data.id),e.$emit("bindData"))})).catch((function(t){e.$message.warning(t||"保存失败")}))},setRow:function(e){this.formdata.userid=e.id,this.formdata.username=e.username,this.formdata.email=e.email},autoClear:function(){this.formdata.userid="",this.formdata.username="",this.formdata.email=""},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}},v=y,x=(a("aa58"),Object(c["a"])(v,p,h,!1,null,"2907336e",null)),k=x.exports,S={formcode:"S06M95B4List",item:[{itemcode:"doccode",itemname:"编码",minwidth:"80",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center",datasheet:"Sa_Engineer.doccode"},{itemcode:"docname",itemname:"姓名",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_Engineer.docname"},{itemcode:"doctype",itemname:"类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Engineer.doctype"},{itemcode:"username",itemname:"关联用户",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Engineer.username"},{itemcode:"email",itemname:"邮箱",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Engineer.email"},{itemcode:"enabledmark",itemname:"状态",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Engineer.enabledmark"},{itemcode:"remark",itemname:"备注",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Engineer.remark"}]},$={name:"S06M95B4",components:{Pagination:u["a"],listheader:m,formedit:k},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},tableForm:S}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){var e=this;this.listLoading=!0,f["a"].post("/S06M95B4/getPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},changeSort:function(e){"descending"==e.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(e,this.tableForm),this.bindData()},handlePiPerMission:function(e,t){var a=this;this.$confirm("是否确定注销文档用户【"+t.engineername+"】，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.idx=e,f["a"].get("/S06M95B4/delete?key=".concat(e)).then((function(e){200==e.data.code&&(a.$message.success("删除成功"),a.bindData())})).catch((function(e){a.$message.warning("删除失败")}))})).catch((function(){}))},GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={engineercode:e,engineername:e,engineertype:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(e){this.idx=e,this.FormVisible=!0},closeForm:function(){this.bindData(),this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(e){this.idx=e}}},P=$,_=(a("f398"),Object(c["a"])(P,i,n,!1,null,"138c322b",null));t["default"]=_.exports},f398:function(e,t,a){"use strict";a("8f71")}}]);