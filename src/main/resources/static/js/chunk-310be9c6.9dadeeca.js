(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-310be9c6"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,a){var s=o(),r=t-s,l=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=l;var t=Math.easeInOutQuad(c,s,r,e);n(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"0c17":function(t,e,a){"use strict";a("3b5f")},1095:function(t,e,a){"use strict";a("1c2a")},"11d6b":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("listheader",{ref:"ListHeader",attrs:{tableForm:t.tableForm},on:{btnadd:function(e){return t.showform(0)},btnsearch:t.search,bindData:t.bindData,AdvancedSearch:t.AdvancedSearch,changeBalance:t.changeBalance,changeOwn:t.changeOwn,changeShowType:t.changeShowType,quickTickBtn:t.quickTickBtn}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:"表格"==t.showType,expression:"showType == '表格'"}],attrs:{span:24}},[a("TableList",{ref:"tableList",attrs:{online:t.online,own:t.own},on:{changeIdx:t.changeIdx,showform:t.showform,sendTableForm:t.sendTableForm}})],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:"类型"==t.showType,expression:"showType == '类型'"}],attrs:{span:24}},[a("TodoType",{ref:"todoType",on:{changeList:function(e){return t.$refs.ListHeader.changeList(e)}}})],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:"时间"==t.showType,expression:"showType == '时间'"}],attrs:{span:24}},[a("TodoTime",{ref:"todoTime",on:{changeList:function(e){return t.$refs.ListHeader.changeList(e)}}})],1)],1)],1)],1)])},n=[],o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"mini"}},[t._v(" "+t._s(t.showType)+" "),a("i",{staticClass:"el-icon-arrow-down"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{class:"表格"==t.showType?"isChosed":"",attrs:{icon:"el-icon-s-grid"},nativeOn:{click:function(e){return t.changeShowType("表格")}}},[t._v("表格")]),a("el-dropdown-item",{class:"类型"==t.showType?"isChosed":"",attrs:{icon:"el-icon-coin"},nativeOn:{click:function(e){return t.changeShowType("类型")}}},[t._v("类型")]),a("el-dropdown-item",{class:"时间"==t.showType?"isChosed":"",attrs:{icon:"el-icon-time"},nativeOn:{click:function(e){return t.changeShowType("时间")}}},[t._v("时间")])],1)],1),"表格"!=t.showType?a("quickTick",{ref:"quickTick",attrs:{droupList:t.droupList},on:{quickTickBtn:function(e){return t.$emit("quickTickBtn",e)}}}):t._e(),"表格"==t.showType?a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"我的",size:"mini",border:""},on:{change:t.changeOwn},model:{value:t.isown,callback:function(e){t.isown=e},expression:"isown"}}):t._e(),"表格"==t.showType?a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}):t._e(),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}})],1)])])},s=[],r=(a("99af"),a("e82a")),l={name:"Listheader",props:["tableForm"],components:{quickTick:r["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M09B1List",balance:!0,isown:!0,showType:"表格",droupList:[]}},methods:{changeShowType:function(t){this.showType=t,this.$emit("changeShowType",t)},AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)},changeOwn:function(t){this.isown=t,this.$emit("changeOwn",t)},changeList:function(t){this.droupList=[].concat(t)}}},c=l,d=(a("a122"),a("2877")),m=Object(d["a"])(c,o,s,!1,null,"33ffef81",null),f=m.exports,u=a("b775"),h=a("de74"),p=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)],1)},b=[],g=a("c7eb"),v=a("1da1"),y=(a("e9c4"),a("a9e3"),a("b64b"),a("d3b7"),a("c7cd"),a("0643"),a("4e3e"),a("159b"),{formcode:"S06M09B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center"},{itemcode:"billtitle",itemname:"标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Pms_ProPoint.billtitle"},{itemcode:"billtype",itemname:"类型",minwidth:"100",displaymark:1,overflow:1,datasheet:"Pms_ProPoint.billtype"},{itemcode:"billdate",itemname:"日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Pms_ProPoint.billdate"},{itemcode:"accepter",itemname:"接受人员",minwidth:"80",displaymark:1,overflow:1},{itemcode:"endplan",itemname:"计划完成",minwidth:"70",displaymark:1,sortable:1,overflow:1,datasheet:"Pms_ProPoint.endplan"},{itemcode:"status",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"60",displaymark:1,overflow:1,datasheet:"Pms_ProPoint.lister"},{itemcode:"createby",itemname:"创建者",minwidth:"60",displaymark:1,overflow:1,datasheet:"Pms_ProPoint.createby"},{itemcode:"operate",itemname:"操作",minwidth:"150",fixed:"right",sortable:0,displaymark:1,overflow:1}]}),w={components:{},props:["own","online"],data:function(){var t=this;return{lst:[],total:0,listLoading:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y,customList:[],selectList:[],totalfields:[],exportitle:"TODO列表",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var a=e.startRowIndex;t.rowScroll=a}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var a=e.selectedRowKeys;if(t.selectList=[],0!=a.length)for(var i=0;i<a.length;i++)t.selectList.push({id:a[i]})},selectedAllChange:function(e){var a=e.isSelected;e.selectedRowKeys;t.selectList=a?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(t){t.row,t.column,t.rowIndx;return{mouseup:function(t){}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var a=this.tableForm.item[e];a.displaymark&&(t+=Number(a.minwidth))}}return t}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1));var e="/S06M09B1/getPageList";this.online&&(e="/S06M09B1/getOnlinePageList"),e+="?own="+(this.own?1:0),this.$request.post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"查询失败"),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;return Object(v["a"])(Object(g["a"])().mark((function e(){return Object(g["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm);case 2:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,a=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,i){var n={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(a,i){var n=a.row,o=(a.column,a.rowIndex,"");return"endplan"==t.itemcode||"billdate"==t.itemcode?e.$options.filters.dateFormat(n[t.itemcode]):"refno"==t.itemcode?(o=i("el-button",{attrs:{type:"text",size:"small",title:n[t.itemcode]},on:{click:function(){return e.showform(n.id)}}},[n[t.itemcode]?n[t.itemcode]:"编码"]),o):"status"==t.itemcode?(1==n.finishmark?o=i("span",{class:"textborder-green"},["已完成"]):1==n.closed?o=i("span",{class:"textborder-red"},["已关闭"]):0==n.finishmark&&""!=n.accepterid?o=i("span",{class:"textborder-blue"},["开发中"]):0==n.finishmark&&""==n.accepterid&&(o=i("span",{class:"textborder-gary"},["新建"])),o):"operate"==t.itemcode?(o=i("div",[i("el-button",{attrs:{type:"text",icon:"el-icon-video-play"},directives:[{name:"show",value:!n.startactual}],on:{click:function(){return e.changeStatus(n,"开始")}}},["开始"]),i("el-button",{attrs:{type:"text",icon:"el-icon-finished"},directives:[{name:"show",value:!!n.startactual&&0==n.finishmark}],style:"color: #14a10f",on:{click:function(){return e.changeStatus(n,"完成")}}},["完成"]),i("el-button",{attrs:{type:"text",icon:"el-icon-back"},directives:[{name:"show",value:!!n.endactual&&1==n.finishmark}],style:"color: #fa8c15",on:{click:function(){return e.changeStatus(n,"撤回")}}},["撤回"])]),o):n[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),a.push(n)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,a){t.row,t.column;var i=t.rowIndex;return i+e.rowScroll+1}}),this.customData=a,this.keynum+=1},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},handleSelectionChange:function(t){this.selectList=t},search:function(t){""!=t?this.queryParams.SearchPojo={description:t,version:t,releasename:t,remark:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData(this.projectRow)},AdvancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){for(var e in row)if(""!=row[e]){t={prop:e};"desc"==row[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData();break}},showform:function(t){this.$emit("showform",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},changeStatus:function(t,e){var a=this,i="";"开始"==e?i="/S06M09B1/start":"完成"==e?i="/S06M09B1/finish":"撤回"==e&&(i="/S06M09B1/recall"),this.$request.get(i+"?key="+t.id).then((function(t){200==t.data.code?(a.$message.success(t.data.msg||"操作成功，该Todo已"+e),a.bindData()):a.$message.warning(t.data.msg||"操作失败")}))}}},k=w,x=(a("1095"),Object(d["a"])(k,p,b,!1,null,"5c60b67c",null)),S=x.exports,C=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"todoContainer"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("div",{staticClass:"todoContai"},[a("div",{staticClass:"todoHead"},[a("div",{staticClass:"headorder"},[t._v("序号")]),t._l(t.userList,(function(e){return[a("div",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"t.show"}],key:e.id,staticClass:"headItem",style:{background:e.backcolorargb}},[t._v(" "+t._s(e.name)+" "+t._s(e.taskNum?"（"+e.taskNum+"）":"")+" ")])]}))],2),t._l(t.typeList,(function(e,i){return a("div",{key:i,staticClass:"todoBody",style:{height:240*e.typeheight+"px"}},[a("div",{staticClass:"left",style:{background:e.typecolor}},[t._v(" "+t._s(e.typename)+" ")]),t._l(t.userList,(function(n,o){return a("div",{key:o,staticStyle:{height:"100%"}},[n.show?a("div",{staticClass:"bodyType"},[a("div",{staticClass:"right"},[a("draggable",{staticClass:"itemBox",staticStyle:{height:"100%"},attrs:{group:"items",tag:"div","data-row":e.typename,"data-col":n.id,"data-name":n.name},on:{end:function(a){return t.dragChange(a,e.typename,n.id,n.name)}}},[t._l(t.list,(function(o,s){return[o.billtype==e.typename&&o.accepterid==n.id?a("div",{key:s,staticClass:"items",style:{borderLeft:"5px solid "+t.getBorderColor(o.endplan,o.finishmark)},on:{mouseover:function(e){return t.mouseOver(o,i)},mousedown:function(e){return t.sendMsg(o,o.id)}}},[a("div",{staticClass:"iconStyle"},[a("div",{staticClass:"iconStyle_left"},[a("span",{attrs:{title:o.billtitle}},[t._v(t._s(o.billtitle))])]),a("div",{staticClass:"iconStyle_right"},[a("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"bottom-start"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{staticClass:"dropdownPop",attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(e){return t.editItem(o,s)}}},[t._v("编辑")]),a("el-dropdown-item",{attrs:{icon:"el-icon-finished"},nativeOn:{click:function(e){return t.finish(o,s)}}},[t._v("完成")]),a("el-dropdown-item",{attrs:{icon:"el-icon-circle-close"},nativeOn:{click:function(e){return t.showDown(o)}}},[t._v("关闭 ")])],1)],1)],1)]),a("div",{staticClass:"dateStyle"},[a("div",{staticClass:"dateStyle_date"},[a("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t._f("dateFormat")(o.endplan))+" ")]),a("div",{staticClass:"dateStyle_public"},[a("span",{attrs:{title:"今天"},on:{click:function(e){return t.changeToday(o)}}},[new Date(o.endplan).getTime()>new Date(t.startDate).getTime()&&new Date(o.endplan).getTime()<new Date(t.endDate).getTime()?a("i",{staticClass:"el-icon-s-platform"}):a("i",{staticClass:"el-icon-monitor"})]),a("span",{attrs:{title:"公共"},on:{click:function(e){return t.changeClick(o,"publicmark")}}},[a("i",{class:o.publicmark?"el-icon-message-solid":"el-icon-bell"})]),a("span",{attrs:{title:"重要 "},on:{click:function(e){return t.changeClick(o,"importantmark")}}},[a("i",{class:o.importantmark?"el-icon-star-on":"el-icon-star-off"})]),a("span",{attrs:{title:"紧急"},on:{click:function(e){return t.changeClick(o,"urgentmark")}}},[a("i",{class:o.urgentmark?"el-icon-warning":"el-icon-warning-outline"})])])])]):t._e()]}))],2)],1)]):t._e()])}))],2)}))],2),a("div",{staticClass:"todoPublic"},[a("div",{staticClass:"todoHead todoPubHead",staticStyle:{"margin-left":"0px"}},[a("div",{staticClass:"todoHead_item"},[t._v("公海")]),a("div",{staticClass:"todoHead_icon"},[a("div",{staticStyle:{"margin-right":"30px"},on:{click:function(e){return t.getprocess()}}},[a("el-badge",{staticClass:"item",attrs:{value:t.parentList.length}},[a("i",{staticClass:"el-icon-edit"})])],1)])]),a("div",{staticClass:"todoPublicBody"},[a("draggable",{staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px"},attrs:{group:"items",tag:"div","data-row":"公海","data-col":"root","data-name":"公海"},on:{end:function(e){return t.dragChange(e,"公海","root","公海")}}},[t._l(t.list,(function(e,i){return["公海"==e.billtype&&"root"==e.accepterid?a("div",{key:i,staticClass:"items",style:{borderLeft:"5px solid "+t.getBorderColor(e.endplan,e.finishmark)},on:{mouseover:function(a){return t.mouseOver(e,0)},mousedown:function(a){return t.sendMsg(e,e.id)}}},[a("div",{staticClass:"iconStyle"},[a("div",{staticClass:"iconStyle_left"},[a("span",{attrs:{title:e.billtitle}},[t._v(t._s(e.billtitle))])]),a("div",{staticClass:"iconStyle_right"},[a("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"bottom-start"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{staticClass:"dropdownPop",attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(a){return t.editItem(e,i)}}},[t._v("编辑")]),a("el-dropdown-item",{attrs:{icon:"el-icon-delete"},nativeOn:{click:function(a){return t.deleteItem(e)}}},[t._v(" 删除 ")])],1)],1)],1)]),a("div",{staticClass:"dateStyle"},[a("div",{staticClass:"dateStyle_date"},[a("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t._f("dateFormat")(e.endplan))+" ")]),a("div",{staticClass:"dateStyle_public"},[a("span",{attrs:{title:"今天"},on:{click:function(a){return t.changeToday(e)}}},[new Date(e.endplan).getTime()>new Date(t.startDate).getTime()&&new Date(e.endplan).getTime()<new Date(t.endDate).getTime()?a("i",{staticClass:"el-icon-s-platform"}):a("i",{staticClass:"el-icon-monitor"})]),a("span",{attrs:{title:"公共"},on:{click:function(a){return t.changeClick(e,"publicmark")}}},[a("i",{class:e.publicmark?"el-icon-message-solid":"el-icon-bell"})]),a("span",{attrs:{title:"重要"},on:{click:function(a){return t.changeClick(e,"importantmark")}}},[a("i",{class:e.importantmark?"el-icon-star-on":"el-icon-star-off"})]),a("span",{attrs:{title:"紧急"},on:{click:function(a){return t.changeClick(e,"urgentmark")}}},[a("i",{class:e.urgentmark?"el-icon-warning":"el-icon-warning-outline"})])])])]):t._e()]}))],2)],1),a("div",{staticClass:"todoPublicFoot"},[a("el-popover",{ref:"addpopover",attrs:{placement:"top",trigger:"click",width:"400",title:"新增"}},[a("div",{staticStyle:{padding:"15px"}},[a("el-form",{ref:"todoform",attrs:{model:t.todoform,"label-width":"70px",rules:t.todoformRules}},[a("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[a("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入标题"},model:{value:t.todoform.billtitle,callback:function(e){t.$set(t.todoform,"billtitle",e)},expression:"todoform.billtitle"}})],1)],1),a("div",{on:{click:function(e){return t.cleValidate("endplan")}}},[a("el-form-item",{attrs:{label:"计划完成",prop:"endplan"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",clearable:"",placeholder:"时间"},model:{value:t.todoform.endplan,callback:function(e){t.$set(t.todoform,"endplan",e)},expression:"todoform.endplan"}})],1)],1),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.todosave()}}},[t._v("确 定")])],1)])],1),a("div",{staticClass:"addPlus",attrs:{slot:"reference"},on:{click:function(e){return t.showform(0)}},slot:"reference"},[a("i",{staticClass:"el-icon-plus"})])])],1)])])])],1),t.finishVisible?a("el-dialog",{attrs:{width:"400px",title:"完成描述","append-to-body":!0,visible:t.finishVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.finishVisible=e}}},[a("div",{staticStyle:{padding:"15px"}},[a("el-form",{ref:"finishform",attrs:{model:t.finishform,"label-width":"70px",rules:t.finishformRules}},[a("el-form-item",{attrs:{label:"完工工时",prop:"finishhours"}},[a("el-input-number",{attrs:{min:0,step:.1,size:"small","controls-position":"right"},model:{value:t.finishform.finishhours,callback:function(e){t.$set(t.finishform,"finishhours",e)},expression:"finishform.finishhours"}}),a("span",{staticStyle:{"margin-left":"10px","font-weight":"bold"}},[t._v("H")])],1),a("div",{on:{click:function(e){return t.finishValidate("endremark")}}},[a("el-form-item",{attrs:{label:"完工描述"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",rows:3,placeholder:"请输入完成描述"},model:{value:t.finishform.endremark,callback:function(e){t.$set(t.finishform,"endremark",e)},expression:"finishform.endremark"}})],1)],1),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.finishUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.finishVisible=!1}}},[t._v("取 消")])],1)],1)],1)]):t._e(),t.formeditVisible?a("el-dialog",{attrs:{width:"800px",title:"Todo","append-to-body":!0,visible:t.formeditVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.formeditVisible=e}}},[a("formedit",{ref:"formedit",attrs:{idx:t.idx}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.formeditSave()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.formeditVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},_=[],$=(a("c740"),a("b0c0"),a("25f0"),a("3ca3"),a("4d90"),a("ddb0"),a("b76a")),D=a.n($),T=a("7e12"),P=a("b893"),O={components:{draggable:D.a,formedit:T["a"]},data:function(){return{list:[],userList:[],typeList:[],idx:0,formType:0,currentId:0,todoform:{billtitle:"",endplan:new Date},todoformRules:{billtitle:[{required:!0,trigger:"blur",message:"标题为必填项"}]},finishVisible:!1,finishform:{id:"",endremark:"",finishhours:0},finishformRules:{endremark:[{required:!0,trigger:"blur",message:"完成描述为必填项"}]},showHelp:!1,queryParams:{PageNum:1,PageSize:1e4,OrderType:0,SearchType:1},formeditVisible:!1,startDate:Object(P["b"])(new Date),endDate:Object(P["a"])(new Date),myself:0,parentList:[]}},created:function(){},methods:{bindData:function(){var t=this;u["a"].post("/S06M09B1/getOnlinePageList?type=1",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.list=e.data.data.list,t.getUserList(),t.getparentList(),t.$forceUpdate()),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getUserList:function(){var t=this,e={PageNum:1,PageSize:200,OrderType:0,SearchType:1},a="/S06M09B1User/getPageList";u["a"].post(a,JSON.stringify(e)).then((function(e){if(200==e.data.code){t.userList=e.data.data.list;for(var a=0;a<t.userList.length;a++){t.userList[a];t.userList[a].show=!0,t.userList[a].taskNum=0;for(var i=0;i<t.list.length;i++)t.userList[a].id==t.list[i].accepterid&&(t.userList[a].taskNum+=1)}t.$forceUpdate(),t.$emit("changeList",t.userList)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getparentList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1,scenedata:[{field:"Sa_Demand.todoid",fieldtype:0,math:"equal",value:""}]};u["a"].post("/S06M02B1/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.parentList=e.data.data.list)})).catch((function(t){}))},quickTickBtn:function(t){var e=this.userList.findIndex((function(e){return e.id==t.id}));-1!=e&&(this.$set(this.userList[e],"show",t.show),this.$emit("changeList",this.userList)),this.$forceUpdate()},getDicist:function(){var t=this;u["a"].get("/SaDict/getBillEntityByDictCode?key=sa_todo.billtype").then((function(e){if(200==e.data.code){var a=["#7EC5FF","#409eff","#C280FF","#95C804","#FFB45C"],i=e.data.data.item;i.forEach((function(t,e){t.backTypeColor=a[e]})),t.typeList=i}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getType:function(){var t=this,e={PageNum:1,PageSize:100,OrderType:0,SearchType:1};u["a"].post("/S06M09B1Type/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.typeList=e.data.data.list),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},dragChange:function(t){var e=this,a=t.to.dataset.row,i=t.to.dataset.col,n=t.to.dataset.name;this.list.forEach((function(t){t.id===e.currentId&&(t.billtype=a,t.accepterid=i,t.accepter=n,e.favemove(t))})),this.$forceUpdate()},sendMsg:function(t,e){this.currentId=e},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},showform:function(t){this.todoform.billtype="公海",this.todoform.accepterid="root"},todosave:function(){var t=this;u["a"].post("/S06M09B1/create",JSON.stringify(this.todoform)).then((function(e){200==e.data.code&&(t.todoform.billtitle="",t.list.push(e.data.data),t.$message.success("新增成功"),t.$refs.addpopover.doClose())})).catch((function(t){}))},btnadd:function(){var t=this;this.formType=0,setTimeout((function(){t.formeditVisible=!0}),100)},editItem:function(t,e){var a=this;this.idx=t.id,this.formType=1,this.formeditVisible=!0,setTimeout((function(){a.$refs.formedit.bindData()}),100)},formeditSave:function(){var t=this,e="/S06M09B1/create";this.formType&&(e="/S06M09B1/update");var a=Object.assign({},this.$refs.formedit.formdata);a.finishhours=this.finishform.finishhours,a.endremark=this.finishform.endremark,u["a"].post(e,JSON.stringify(a)).then((function(e){200==e.data.code&&(t.$message.success("新增成功"),t.formeditVisible=!1,t.bindData())})).catch((function(t){}))},favemove:function(t){var e=this,a={id:t.id,accepterid:t.accepterid,accepter:t.accepter,billtype:t.billtype};u["a"].post("/S06M09B1/update",JSON.stringify(a)).then((function(t){200==t.data.code&&(e.bindData(),e.$forceUpdate())})).catch((function(t){}))},cleValidate:function(t){this.$refs.todoform.clearValidate(t)},mouseOver:function(t,e){},finishValidate:function(t){this.$refs.finishform.clearValidate(t)},finish:function(t,e){this.finishform.id=t.id,this.finishform.endremark="",this.finishform.finishmark=1,this.finishVisible=!0},finishUpdate:function(){var t=this;u["a"].post("/S06M09B1/update",JSON.stringify(this.finishform)).then((function(e){t.finishVisible=!1,200==e.data.code&&(t.$message.success(e.data.data.billtitle+"完成"),t.bindData(),t.finishVisible=!1)})).catch((function(t){}))},changeClick:function(t,e){t[e]=t[e]?0:1,u["a"].post("/S06M09B1/update",JSON.stringify(t)).then((function(e){200==e.data.code&&(t=e.data.data)})).catch((function(t){}))},search:function(t){console.log("res",t),""!=t?this.queryParams.SearchPojo={billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},showDown:function(t){var e=this;this.$confirm("是否确认关闭?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.reqClosed(t)}))},reqClosed:function(t){var e=this,a={id:t.id,closed:1};u["a"].post("/S06M09B1/update",JSON.stringify(a)).then((function(t){200==t.data.code&&(e.$message.success(t.data.data.billtitle+"已关闭"),e.bindData())})).catch((function(t){}))},deleteItem:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){u["a"].get("/S06M09B1/delete?key="+t.id).then((function(t){200==t.data.code?(e.$message.success("删除成功"),e.bindData()):e.$message.warning(t.data.msg||"删除失败")}))}))},getBorderColor:function(t,e){if(!t)return"#9e9e9e";var a="",i=new Date(t).setHours(23,59,59,0),n=(new Date).getTime();return a=i-n<0?e?"#ffeb3b":"#f44336":i-n<864e5?e?"#8bc34a":"#2196f3":i-n>864e5&&i-n<1728e5?"#ff9800":"#9e9e9e",a},changeToday:function(t){var e=this,a=new Date;if(new Date(t.endplan).getTime()>new Date(this.startDate).getTime()&&new Date(t.endplan).getTime()<new Date(this.endDate).getTime())var i={id:t.id,endplan:new Date(a.setDate(a.getDate()+1))};else i={id:t.id,endplan:a};u["a"].post("/S06M09B1/update",JSON.stringify(i)).then((function(t){200==t.data.code&&e.bindData()})).catch((function(t){}))},getprocess:function(){var t=this;return Object(v["a"])(Object(g["a"])().mark((function e(){var a,i,n;return Object(g["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(t,a=[],i=0;i<t.parentList.length;i++)n=new Promise((function(e,a){var n=t.parentList[i],o={parentid:n.id,billtype:"公海",accepterid:"root",billtitle:n.billtitle,publicmark:1};u["a"].post("/S06M09B1/create",JSON.stringify(o)).then((function(t){200==t.data.code?e(t.data):a(t.data.msg||"新增错误")}))})),a.push(n);return e.next=5,Promise.all(a).then((function(e){t.$message.success("导入成功"),t.bindData()}));case 5:case"end":return e.stop()}}),e)})))()}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),i=e.getDate().toString().padStart(2,"0");return"".concat(a,"-").concat(i)}}}},L=O,B=(a("f44c"),Object(d["a"])(L,C,_,!1,null,"bd47afce",null)),F=B.exports,N=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"todoContainer"},[a("el-row",[a("el-col",{attrs:{span:t.showHelp?20:24}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("div",{staticClass:"todoContai"},[a("div",{staticClass:"todoHead"},[a("div",{staticClass:"headorder"},[t._v("序号")]),t._l(t.userList,(function(e){return[a("div",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"t.show"}],key:e.id,staticClass:"headItem",style:{background:e.backcolorargb}},[t._v(" "+t._s(e.name)+" "+t._s(e.taskNum?"（"+e.taskNum+"）":"")+" ")])]}))],2),t._l(t.typeList,(function(e,i){return a("div",{key:i,staticClass:"todoBody"},[a("div",{staticClass:"left",style:{background:e.backTypeColor}},[t._v(" "+t._s(e.billtitle)+" ")]),t._l(t.userList,(function(i,n){return a("div",{key:n},[i.show?a("div",{staticClass:"bodyType"},[a("div",{staticClass:"right"},[a("draggable",{staticClass:"itemBox",staticStyle:{height:"100%"},attrs:{group:"items",tag:"div","data-row":e.billtitle,"data-col":i.id,"data-name":i.name},on:{end:function(a){return t.dragChange(a,e.billtitle,i.id,i.name)}}},[t._l(t.list,(function(n,o){return[n.accepterid==i.id&&new Date(n.endplan).getTime()>=new Date(e.startDate).getTime()&&new Date(n.endplan).getTime()<=new Date(e.endDate).getTime()?a("div",{key:o,staticClass:"items",style:{borderLeft:"5px solid "+t.getBorderColor(n.endplan,n.finishmark)},on:{mousedown:function(e){return t.sendMsg(n,n.id)}}},[a("div",{staticClass:"iconStyle"},[a("div",{staticClass:"iconStyle_left"},[t._v(" "+t._s(n.billtitle)+" ")]),a("div",{staticClass:"iconStyle_right"},[a("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"right-start"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(e){return t.editItem(n,o)}}},[t._v("编辑")]),a("el-dropdown-item",{attrs:{icon:"el-icon-finished"},nativeOn:{click:function(e){return t.finish(n,o)}}},[t._v("完成")]),a("el-dropdown-item",{attrs:{icon:"el-icon-circle-close"},nativeOn:{click:function(e){return t.showDown(n)}}},[t._v("关闭 ")])],1)],1)],1)]),a("div",{staticClass:"dateStyle"},[a("div",{staticClass:"dateStyle_date"},[a("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t._f("dateFormat")(n.endplan))+" ")]),a("div",{staticClass:"dateStyle_public"},[a("span",{attrs:{title:"公共"},on:{click:function(e){return t.changeClick(n,"publicmark")}}},[a("i",{class:n.publicmark?"el-icon-message-solid":"el-icon-bell"})]),a("span",{attrs:{title:"重要"},on:{click:function(e){return t.changeClick(n,"importantmark")}}},[a("i",{class:n.importantmark?"el-icon-star-on":"el-icon-star-off"})]),a("span",{attrs:{title:"紧急"},on:{click:function(e){return t.changeClick(n,"urgentmark")}}},[a("i",{class:n.urgentmark?"el-icon-warning":"el-icon-warning-outline"})])])])]):t._e()]}))],2)],1)]):t._e()])}))],2)}))],2),a("div",{staticClass:"todoPublic"},[a("div",{staticClass:"todoHead",staticStyle:{"margin-left":"0px"}},[a("div",{staticClass:"headItem",style:{background:"#8bc34a"}},[t._v(" 公海 ")])]),a("div",{staticClass:"todoPublicBody"},[a("draggable",{staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px"},attrs:{group:"items",tag:"div","data-row":"公海","data-col":"root","data-name":"公海"},on:{end:function(e){return t.dragChange(e,"公海","root","公海")}}},[t._l(t.list,(function(e,i){return["公海"==e.billtype&&"root"==e.accepterid?a("div",{key:i,staticClass:"items",style:{borderLeft:"5px solid "+t.getBorderColor(e.endplan,e.finishmark)},on:{mouseover:function(a){return t.mouseOver(e,0)},mousedown:function(a){return t.sendMsg(e,e.id)}}},[a("div",{staticClass:"iconStyle"},[a("div",{staticClass:"iconStyle_left"},[t._v(t._s(e.billtitle))]),a("div",{staticClass:"iconStyle_right"},[a("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"bottom-start"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{staticClass:"dropdownPop",attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(a){return t.editItem(e,i)}}},[t._v("编辑")]),a("el-dropdown-item",{attrs:{icon:"el-icon-delete"},nativeOn:{click:function(a){return t.deleteItem(e)}}},[t._v(" 删除 ")])],1)],1)],1)]),a("div",{staticClass:"dateStyle"},[a("div",{staticClass:"dateStyle_date"},[a("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t._f("dateFormat")(e.endplan))+" ")]),a("div",{staticClass:"dateStyle_public"},[a("span",{attrs:{title:"公共"},on:{click:function(a){return t.changeClick(e,"publicmark")}}},[a("i",{class:e.publicmark?"el-icon-message-solid":"el-icon-bell"})]),a("span",{attrs:{title:"紧急"},on:{click:function(a){return t.changeClick(e,"importantmark")}}},[a("i",{class:e.importantmark?"el-icon-star-on":"el-icon-star-off"})]),a("span",{attrs:{title:"重要"},on:{click:function(a){return t.changeClick(e,"urgentmark")}}},[a("i",{class:e.urgentmark?"el-icon-warning":"el-icon-warning-outline"})])])])]):t._e()]}))],2)],1)])])])],1),t.finishVisible?a("el-dialog",{attrs:{width:"400px",title:"完成描述","append-to-body":!0,visible:t.finishVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.finishVisible=e}}},[a("div",{staticStyle:{padding:"15px"}},[a("el-form",{ref:"finishform",attrs:{model:t.finishform,"label-width":"60px",rules:t.finishformRules}},[a("el-form-item",{attrs:{label:"完工工时",prop:"finishhours"}},[a("el-input-number",{attrs:{min:0,step:.1,size:"small","controls-position":"right"},model:{value:t.finishform.finishhours,callback:function(e){t.$set(t.finishform,"finishhours",e)},expression:"finishform.finishhours"}}),a("span",{staticStyle:{"margin-left":"10px","font-weight":"bold"}},[t._v("H")])],1),a("div",{on:{click:function(e){return t.finishValidate("endremark")}}},[a("el-form-item",{attrs:{label:"描述",prop:"endremark"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",rows:3,placeholder:"请输入完成描述"},model:{value:t.finishform.endremark,callback:function(e){t.$set(t.finishform,"endremark",e)},expression:"finishform.endremark"}})],1)],1),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.finishUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.finishVisible=!1}}},[t._v("取 消")])],1)],1)],1)]):t._e(),t.formeditVisible?a("el-dialog",{attrs:{width:"800px",title:"Todo","append-to-body":!0,visible:t.formeditVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.formeditVisible=e}}},[a("formedit",{ref:"formedit",attrs:{idx:t.idx}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.formeditSave()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.formeditVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},I=[],V=a("da36"),q={components:{draggable:D.a,formedit:V["a"]},data:function(){return{list:[],userList:[],idx:0,formType:0,typeList:[{id:1,billtitle:"逾期",backTypeColor:"#D9001B",startDate:Object(P["b"])(this.getDay(-1e4)),endDate:Object(P["a"])(this.getDay(-1))},{id:2,billtitle:"今天",backTypeColor:"#7EC5FF",startDate:Object(P["b"])(new Date),endDate:Object(P["a"])(new Date)},{id:3,billtitle:"明天",backTypeColor:"#409EFF",startDate:Object(P["b"])(new Date(this.getDay(1))),endDate:Object(P["a"])(new Date(this.getDay(1)))},{id:4,billtitle:"后期",backTypeColor:"#95C804",startDate:Object(P["b"])(new Date(this.getDay(2))),endDate:Object(P["a"])(new Date(this.getDay(1e4)))}],currentId:0,todoform:{},todoformRules:{billtitle:[{required:!0,trigger:"blur",message:"标题为必填项"}]},finishVisible:!1,finishform:{id:"",endremark:"",finishhours:0},finishformRules:{endremark:[{required:!0,trigger:"blur",message:"完成描述为必填项"}]},showHelp:!1,queryParams:{PageNum:1,PageSize:1e3,OrderType:0,SearchType:1},formeditVisible:!1,dicList:[]}},created:function(){},methods:{bindData:function(){var t=this;u["a"].post("/S06M09B1/getOnlinePageList?type=1",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.list=e.data.data.list,t.getUserList(),t.$forceUpdate()),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getUserList:function(){var t=this,e={PageNum:1,PageSize:20,OrderType:0,SearchType:1};u["a"].post("/S06M09B1User/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){t.userList=e.data.data.list;for(var a=0;a<t.userList.length;a++){t.userList[a].show=!0,t.userList[a].taskNum=0;for(var i=0;i<t.list.length;i++)t.userList[a].id==t.list[i].accepterid&&(t.userList[a].taskNum+=1)}t.$forceUpdate(),t.$emit("changeList",t.userList)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},quickTickBtn:function(t){var e=this.userList.findIndex((function(e){return e.id==t.id}));-1!=e&&(this.$set(this.userList[e],"show",t.show),this.$emit("changeList",this.userList)),this.$forceUpdate()},getDicist:function(){var t=this;u["a"].get("/SaDict/getBillEntityByDictCode?key=sa_todo.billtype").then((function(e){if(200==e.data.code){var a=e.data.data.item;t.dicList=a}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},changeClick:function(t,e){t[e]=t[e]?0:1,u["a"].post("/S06M09B1/update",JSON.stringify(t)).then((function(e){200==e.data.code&&(t=e.data.data)})).catch((function(t){}))},dragChange:function(t){var e=this,a=t.to.dataset.row,i=t.to.dataset.col,n=t.to.dataset.name;this.list.forEach((function(t){t.id===e.currentId&&("逾期"==a?t.endplan=Object(P["a"])(e.getDay(-1)):"今天"==a?t.endplan=new Date:"明天"==a?t.endplan=Object(P["b"])(e.getDay(1)):"后期"==a&&(t.endplan=Object(P["b"])(e.getDay(2))),t.accepterid=i,t.accepter=n,e.favemove(t))})),this.$forceUpdate()},sendMsg:function(t,e){this.currentId=e},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},showform:function(t){this.todoform.billtype="公海",this.todoform.accepterid="root"},todosave:function(){var t=this;u["a"].post("/S06M09B1/create",JSON.stringify(this.todoform)).then((function(e){200==e.data.code&&(t.todoform.billtitle="",t.list.push(e.data.data),t.$message.success("新增成功"),t.$refs.addpopover.doClose())})).catch((function(t){}))},btnadd:function(){var t=this;this.formType=0,setTimeout((function(){t.formeditVisible=!0}),100)},editItem:function(t,e){var a=this;this.idx=t.id,this.formType=1,this.formeditVisible=!0,setTimeout((function(){a.$refs.formedit.bindData()}),100)},formeditSave:function(){var t=this,e="/S06M09B1/create";this.formType&&(e="/S06M09B1/update");var a=Object.assign({},this.$refs.formedit.formdata);a.finishhours=this.finishform.finishhours,a.endremark=this.finishform.endremark,u["a"].post(e,JSON.stringify(a)).then((function(e){200==e.data.code&&(t.$message.success("新增成功"),t.formeditVisible=!1,t.bindData())})).catch((function(t){}))},favemove:function(t){var e={id:t.id,accepterid:t.accepterid,accepter:t.accepter,endplan:t.endplan,billtype:this.dicList[0].dictvalue};u["a"].post("/S06M09B1/update",JSON.stringify(e)).then((function(t){t.data.code})).catch((function(t){}))},cleValidate:function(t){this.$refs.todoform.clearValidate(t)},finishValidate:function(t){this.$refs.finishform.clearValidate(t)},finish:function(t,e){this.finishform.id=t.id,this.finishform.endremark="",this.finishform.finishmark=1,this.finishVisible=!0},finishUpdate:function(){var t=this;u["a"].post("/S06M09B1/update",JSON.stringify(this.finishform)).then((function(e){t.finishVisible=!1,200==e.data.code&&(t.$message.success(e.data.data.billtitle+"完成"),t.bindData(),t.finishVisible=!1)})).catch((function(t){}))},mouseOver:function(t,e){},showDown:function(t){var e=this;this.$confirm("是否确认关闭?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.reqClosed(t)}))},reqClosed:function(t){var e=this,a={id:t.id,closed:1};u["a"].post("/S06M09B1/update",JSON.stringify(a)).then((function(t){200==t.data.code&&(e.$message.success(t.data.data.billtitle+"已关闭"),e.bindData())})).catch((function(t){}))},deleteItem:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){u["a"].get("/S06M09B1/delete?key="+t.id).then((function(t){200==t.data.code?(e.$message.success("删除成功"),e.bindData()):e.$message.warning(t.data.msg||"删除失败")}))}))},getDay:function(t){var e=new Date,a=e.getTime()+864e5*t;e.setTime(a);var i=e.getFullYear(),n=e.getMonth(),o=e.getDate();return n=this.doHandleMonth(n+1),o=this.doHandleMonth(o),i+"-"+n+"-"+o},doHandleMonth:function(t){var e=t;return 1===t.toString().length&&(e="0"+t),e},getBorderColor:function(t,e){if(!t)return"#9e9e9e";var a="",i=new Date(t).setHours(23,59,59,0),n=(new Date).getTime();return a=i-n<0?e?"#ffeb3b":"#f44336":i-n<864e5?e?"#8bc34a":"#2196f3":i-n>864e5&&i-n<1728e5?"#ff9800":"#9e9e9e",a},search:function(t){""!=t?this.queryParams.SearchPojo={billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),i=e.getDate().toString().padStart(2,"0");return"".concat(a,"-").concat(i)}}}},M=q,z=(a("dd3b"),Object(d["a"])(M,N,I,!1,null,"d22f9b7e",null)),R=z.exports,j={name:"S06M09B1",components:{listheader:f,formedit:h["a"],TableList:S,TodoType:F,TodoTime:R},data:function(){return{lst:[],FormVisible:!1,idx:0,searchstr:"",tableForm:{},online:1,own:!0,showType:"表格"}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){"表格"==this.showType?(this.$refs.tableList.bindData(),this.$refs.tableList.getColumn()):"类型"==this.showType?(this.$refs.todoType.getType(),this.$refs.todoType.bindData()):"时间"==this.showType&&(this.$refs.todoTime.getDicist(),this.$refs.todoTime.bindData())},changeShowType:function(t){var e=this;this.showType=t,this.$nextTick((function(){e.bindData()}))},sendTableForm:function(t){this.tableForm=t},quickTickBtn:function(t){("类型"==this.showType||"时间"==this.showType)&&this.$refs.todoType.quickTickBtn(t)},search:function(t){""!=t?this.queryParams.SearchPojo={billtitle:t,billtype:t,refno:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.bindData(),this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeIdx:function(t){this.idx=t},changeBalance:function(t){var e=this;this.online=t,this.$nextTick((function(){e.bindData()}))},changeOwn:function(t){var e=this;this.own=t,this.$nextTick((function(){e.bindData()}))}}},H=j,E=(a("bb0b"),Object(d["a"])(H,i,n,!1,null,"716078e6",null));e["default"]=E.exports},"1c2a":function(t,e,a){},"21c8":function(t,e,a){"use strict";a("5d39")},"270a":function(t,e,a){},"3b5f":function(t,e,a){},"3bc6":function(t,e,a){"use strict";a("270a")},"3f61":function(t,e,a){},"501a":function(t,e,a){},"5c73":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,i){return a("li",{key:i,class:t.radio==i?"active":"",on:{click:function(a){t.getCurrentRow(e),t.radio=i}}},[a("span",[t._v(t._s(e.dictvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,i){return a("p",{key:i,class:t.ActiveIndex==i?"isActive":"",on:{click:function(e){t.ActiveIndex=i}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},n=[],o=(a("a434"),a("e9c4"),a("b775")),s=a("333d"),r=a("b0b8"),l={components:{Pagination:s["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],o["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var a=0;a<t.formdata.item.length;a++)t.lst.push(t.formdata.item[a])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.addItem(a)})).catch((function(t){console.log(t)}))},addItem:function(t){r.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:r.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.lst[t.ActiveIndex].dictvalue=a,r.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=r.getFullChars(a)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,o["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=l,d=(a("af2b"),a("2877")),m=Object(d["a"])(c,i,n,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"5d39":function(t,e,a){},"7de4":function(t,e,a){},"7e12":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"billtype"}},[a("el-input",{attrs:{placeholder:"类型",size:"small",readonly:""},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[a("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划完成"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.formdata.endplan,callback:function(e){t.$set(t.formdata,"endplan",e)},expression:"formdata.endplan"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"客户名"}},[a("el-popover",{ref:"groupnamePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}}},[a("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.groupname"},on:{singleSel:function(e){t.formdata.groupname=e.dictvalue,t.$refs.groupnamePopverRef.doClose()},closedic:function(e){return t.$refs.groupnamePopverRef.doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"客户名",clearable:"",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"产品名"}},[a("el-popover",{ref:"productnamePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selProductnameRef.bindData()}}},[a("selDictionaries",{ref:"selProductnameRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.productname"},on:{singleSel:function(e){t.formdata.productname=e.dictvalue,t.$refs.productnamePopverRef.doClose()},closedic:function(e){return t.$refs.productnamePopverRef.doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"产品名",clearable:"",size:"small"},model:{value:t.formdata.productname,callback:function(e){t.$set(t.formdata,"productname",e)},expression:"formdata.productname"}})],1)],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"来源"}},[a("el-popover",{ref:"sourcePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.sourceRef.bindData()}}},[a("selDictionaries",{ref:"sourceRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.source"},on:{singleSel:function(e){t.formdata.source=e.dictvalue,t.$refs.sourcePopverRef.doClose()},closedic:function(e){return t.$refs.sourcePopverRef.doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"来源",clearable:"",size:"small"},model:{value:t.formdata.source,callback:function(e){t.$set(t.formdata,"source",e)},expression:"formdata.source"}})],1)],1)],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"公共","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"重要","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.importantmark,callback:function(e){t.$set(t.formdata,"importantmark",e)},expression:"formdata.importantmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"紧急","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.urgentmark,callback:function(e){t.$set(t.formdata,"urgentmark",e)},expression:"formdata.urgentmark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)},n=[],o=(a("b64b"),a("b775")),s=a("5c73"),r={name:"Formedit",props:["idx"],components:{selDictionaries:s["a"]},data:function(){return{title:"TODO列表",formdata:{billtype:"公海",accepterid:"root",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,productname:"",groupname:"",endplan:new Date},formRules:{type:[{required:!0,trigger:"blur",message:"类型不能为空"}],title:[{required:!0,trigger:"blur",message:"标题不能为空"}],content:[{required:!0,trigger:"blur",message:"公告内容不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&o["a"].get("/S06M09B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?CRUD.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")})):CRUD.update(this.formdata).then((function(e){console.log("保存====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),CRUD.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},l=r,c=(a("3bc6"),a("2877")),d=Object(c["a"])(l,i,n,!1,null,"2f124ae9",null);e["a"]=d.exports},"8a34":function(t,e,a){},a122:function(t,e,a){"use strict";a("b635")},af2b:function(t,e,a){"use strict";a("7de4")},b635:function(t,e,a){},bb0b:function(t,e,a){"use strict";a("3f61")},da36:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"billtype"}},[a("el-input",{attrs:{placeholder:"类型",size:"small",readonly:""},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[a("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"计划完成"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.formdata.endplan,callback:function(e){t.$set(t.formdata,"endplan",e)},expression:"formdata.endplan"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"客户名"}},[a("el-popover",{ref:"groupnamePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}}},[a("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.groupname"},on:{singleSel:function(e){t.formdata.groupname=e.dictvalue,t.$refs.groupnamePopverRef.doClose()},closedic:function(e){return t.$refs.groupnamePopverRef.doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"客户名",clearable:"",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"产品名"}},[a("el-popover",{ref:"productnamePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selProductnameRef.bindData()}}},[a("selDictionaries",{ref:"selProductnameRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.productname"},on:{singleSel:function(e){t.formdata.productname=e.dictvalue,t.$refs.productnamePopverRef.doClose()},closedic:function(e){return t.$refs.productnamePopverRef.doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"产品名",clearable:"",size:"small"},model:{value:t.formdata.productname,callback:function(e){t.$set(t.formdata,"productname",e)},expression:"formdata.productname"}})],1)],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"来源"}},[a("el-popover",{ref:"sourcePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.sourceRef.bindData()}}},[a("selDictionaries",{ref:"sourceRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.source"},on:{singleSel:function(e){t.formdata.source=e.dictvalue,t.$refs.sourcePopverRef.doClose()},closedic:function(e){return t.$refs.sourcePopverRef.doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"来源",clearable:"",size:"small"},model:{value:t.formdata.source,callback:function(e){t.$set(t.formdata,"source",e)},expression:"formdata.source"}})],1)],1)],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"公共","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"重要","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.importantmark,callback:function(e){t.$set(t.formdata,"importantmark",e)},expression:"formdata.importantmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"紧急","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.urgentmark,callback:function(e){t.$set(t.formdata,"urgentmark",e)},expression:"formdata.urgentmark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)},n=[],o=(a("b64b"),a("b775")),s={name:"Formedit",props:["idx"],data:function(){return{title:"TODO列表",formdata:{billtype:"公海",accepterid:"root",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,productname:"",groupname:"",endplan:new Date},formRules:{type:[{required:!0,trigger:"blur",message:"类型不能为空"}],title:[{required:!0,trigger:"blur",message:"标题不能为空"}],content:[{required:!0,trigger:"blur",message:"公告内容不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&o["a"].get("/S06M09B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?CRUD.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")})):CRUD.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),CRUD.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},r=s,l=(a("0c17"),a("2877")),c=Object(l["a"])(r,i,n,!1,null,"007443e8",null);e["a"]=c.exports},dd3b:function(t,e,a){"use strict";a("501a")},de74:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!t.formdata.assessor},on:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.idx},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini",readonly:!!t.formdata.id},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:""},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"billtype"}},[a("el-popover",{ref:"billtypeRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selbilltypeRef.bindData()}}},[a("selDictionaries",{ref:"selbilltypeRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Pms_ProPoint.billtype"},on:{singleSel:function(e){t.formdata.billtype=e.dictvalue,t.$refs.billtypeRef.doClose()},closedic:function(e){return t.$refs.billtypeRef.doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"类型",clearable:"",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[a("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"接受人员"}},[a("autoComplete",{attrs:{size:"small",value:t.formdata.accepter,baseurl:"/S06M09B1User/getPageList",params:{name:"name"}},on:{setRow:function(e){return t.setRow(e)},autoClear:function(e){return t.autoClear()}}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"计划开始"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.formdata.startplan,callback:function(e){t.$set(t.formdata,"startplan",e)},expression:"formdata.startplan"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"计划完成"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.formdata.endplan,callback:function(e){t.$set(t.formdata,"endplan",e)},expression:"formdata.endplan"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"公共"}},[a("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"重要"}},[a("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.importantmark,callback:function(e){t.$set(t.formdata,"importantmark",e)},expression:"formdata.importantmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"紧急的"}},[a("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.urgentmark,callback:function(e){t.$set(t.formdata,"urgentmark",e)},expression:"formdata.urgentmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:"完成"}},[a("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.finishmark,callback:function(e){t.$set(t.formdata,"finishmark",e)},expression:"formdata.finishmark"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"40px"}},[a("el-col",{attrs:{span:24}},[a("MyEditor",{ref:"MyEditor",attrs:{height:400,html:t.formdata.tdcontent,excludeKeys:["group-image","insertImage","uploadImage","insertLink","group-video","insertVideo","uploadVideo","fullScreen"]},on:{changeHtml:function(e){return t.changeHtml(e)}}})],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])},n=[],o=(a("b0c0"),a("b64b"),a("b775"));const s={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);o["a"].post("/S06M09B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);o["a"].post("/S06M09B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{o["a"].get("/S06M09B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var r=s,l=a("1975"),c=a("5b24"),d=a("5c73"),m=a("b0b8"),f={name:"Formedit",components:{MyEditor:l["a"],autoComplete:c["a"],selDictionaries:d["a"]},props:["idx","isDialog"],data:function(){return{title:"TODO列表",formdata:{type:"",content:"",title:"",billtype:"",remark:"",accepterid:"",accepter:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,status:1},formRules:{type:[{required:!0,trigger:"blur",message:"类型不能为空"}],title:[{required:!0,trigger:"blur",message:"标题不能为空"}],content:[{required:!0,trigger:"blur",message:"公告内容不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px",formheight:"500px"}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&o["a"].get("/S06M09B1/getEntity?key=".concat(this.idx)).then((function(e){console.log("==================",e),200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?r.add(this.formdata).then((function(e){console.log("新建====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")})):r.update(this.formdata).then((function(e){console.log("保存====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),r.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},writeCode:function(t){m.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.type=m.getFullChars(t)},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},changeHtml:function(t){this.formdata.tdcontent=t},setRow:function(t){this.formdata.accepter=t.name,this.formdata.accepterid=t.id,this.cleValidate("accepter")},autoClear:function(){this.formdata.accepterid="",this.formdata.accepter=""}}},u=f,h=(a("21c8"),a("2877")),p=Object(h["a"])(u,i,n,!1,null,"383f1cf1",null);e["a"]=p.exports},f44c:function(t,e,a){"use strict";a("8a34")}}]);