(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3254a4ec"],{"60a6":function(t,e,i){},a097:function(t,e,i){"use strict";i("bc9f")},a83c:function(t,e,i){"use strict";i("f3e3")},b698:function(t,e,i){"use strict";i("60a6")},bc9f:function(t,e,i){},befd:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnadd:function(e){return t.showform(0)},bindData:function(e){return t.$refs.tableList.bindData()},btnsearch:t.search,advancedSearch:t.advancedSearch,btnExport:function(e){return t.$refs.tableList.btnExport()},btnHelp:t.btnHelp,changeModelUrl:t.changeModelUrl,changeBalance:t.changeBalance,bindColumn:function(e){return t.$refs.tableList.getColumn()}}}),i("el-row",[i("el-col",{attrs:{span:t.showHelp?20:24}},[i("TableList",{ref:"tableList",on:{changeIdx:t.changeIdx,showform:t.showform,sendTableForm:t.sendTableForm}})],1)],1)],1)])},o=[],r=(i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton()},clickMethods:t.clickMethods}})],1)]),i("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("div",{ref:"form_main_info",staticClass:"form form-head p-r",staticStyle:{height:"96%"}},[i("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[i("div",{staticClass:"refNo flex j-end"},[i("div",{staticClass:"refNo-item"},[i("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),i("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),i("div",[i("span",[t._v("NO：")]),i("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:""},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),i("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[i("div",{staticStyle:{"margin-right":"10px",width:"100px"}},[t.formdata.finishcount>0&&t.formdata.finishcount>=t.formdata.itemcount?i("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"finish1"}}):t._e()],1),i("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[i("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),i("el-form",{ref:"formdata",staticStyle:{height:"99%"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("div",{on:{click:function(e){return t.cleValidate("ideatitle")}}},[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创意标题",prop:"ideatitle"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"输入创意标题",size:"small"},model:{value:t.formdata.ideatitle,callback:function(e){t.$set(t.formdata,"ideatitle",e)},expression:"formdata.ideatitle"}})],1)],1)],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创意类型"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入创意类型",size:"small"},model:{value:t.formdata.ideatype,callback:function(e){t.$set(t.formdata,"ideatype",e)},expression:"formdata.ideatype"}})],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:"类型"}},[t.formdata.publicmark?i("el-tag",{attrs:{effect:"plain",size:"small"}},[t._v(" 公共 ")]):i("el-tag",{attrs:{effect:"plain",size:"small",type:"info"}},[t._v(" 私有 ")])],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:"状态"}},[t.formdata.finishmark?i("el-tag",{attrs:{effect:"plain",size:"small",type:"info"}},[t._v(" 关闭 ")]):i("el-tag",{attrs:{effect:"plain",size:"small"}},[t._v(" 开启 ")])],1)],1)],1),i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("div",{staticClass:"flex j-s",staticStyle:{"flex-wrap":"nowrap"}},[i("EditItem",{ref:"elitem",staticClass:"form-body-left",style:{width:"600px",height:"100%"},attrs:{lstitem:t.formdata.ideajson,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData,getWordCloud:t.getWordCloud}}),i("div",{staticClass:"form-body-right"},[i("div",{staticClass:"modestyle"},[i("i",{staticClass:"el-icon-sort",on:{click:function(e){t.rotationRange=[0,0],t.getWordCloud(t.$refs.elitem.lst)}}},[t._v("水平")]),i("i",{staticClass:"el-icon-bottom-right",on:{click:function(e){t.rotationType=!t.rotationType,t.rotationRange=t.rotationType?[-60,-10]:[10,60],t.getWordCloud(t.$refs.elitem.lst)}}},[t._v("倾斜")]),i("i",{staticClass:"el-icon-refresh-right",on:{click:function(e){return t.getWordCloud(t.$refs.elitem.lst)}}},[t._v(" 刷新")])]),i("div",{ref:"word-cloud",staticClass:"flex wordCloudBox",attrs:{id:"wordCloud"}})])],1),i("el-divider"),i("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建人"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"制表"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"修改日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"审核"}},[i("span",{staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.assessor))])])],1),i("el-col",{directives:[{name:"show",rawName:"v-show",value:t.formdata.assessor,expression:"formdata.assessor"}],attrs:{span:4}},[i("el-form-item",{attrs:{label:"审核日期"}},[i("span",{staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.assessdate)))])])],1)],1)],1)],1)],1)],1)])]),i("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"S06M37B1Edit",weburl:"/S06M37B1/printWebBill"}})],1)}),s=[],n=i("2909"),l=(i("99af"),i("a15b"),i("e9c4"),i("b64b"),i("d3b7"),i("25f0"),i("4d90"),i("b775"));const c={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);l["a"].post("/S06M37B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);l["a"].post("/S06M37B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){return new Promise((e,i)=>{l["a"].get("/S06M37B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})}};var d=c,m=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c",staticStyle:{height:"100%"}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("el-row",[i("el-button-group",[i("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(e){return t.getAdd(0,"add")}}},[t._v(" 添 加")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini",icon:"el-icon-top"},nativeOn:{click:function(e){return t.getMoveUp()}}},[t._v(" 上 移")]),i("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini",icon:"el-icon-bottom"},nativeOn:{click:function(e){return t.getMoveDown()}}},[t._v(" 下 移")]),i("el-button",{attrs:{disabled:0==t.multipleSelection.length,type:"danger",size:"mini",icon:"el-icon-delete"},nativeOn:{click:function(e){return t.delItem()}}},[t._v("删 除")])],1)],1),i("div",{staticStyle:{"margin-right":"10px",position:"relative"}},[i("el-button",{staticStyle:{"font-weight":"bold"},attrs:{size:"mini",icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.$emit("bindData")}}})],1)],1),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption}})],1),t.ideaFromVisible?i("el-dialog",{attrs:{width:"500px",title:"标签设置","append-to-body":!0,visible:t.ideaFromVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.ideaFromVisible=e}}},[i("el-form",{ref:"ideaform",staticClass:"custInfo",attrs:{model:t.ideaform,"label-width":"60px"}},[i("el-row",[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"内容",prop:"name"}},[i("el-input",{attrs:{placeholder:"请输入标签内容",clearable:""},model:{value:t.ideaform.name,callback:function(e){t.$set(t.ideaform,"name",e)},expression:"ideaform.name"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"权重"}},[i("el-slider",{attrs:{"show-input":""},model:{value:t.ideaform.value,callback:function(e){t.$set(t.ideaform,"value",e)},expression:"ideaform.value"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"排序"}},[i("el-input-number",{attrs:{controls:!0,type:"number",min:0,size:"default"},model:{value:t.ideaform.rownum,callback:function(e){t.$set(t.ideaform,"rownum",e)},expression:"ideaform.rownum"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"颜色"}},[i("el-color-picker",{attrs:{predefine:t.predefineColors,size:"small"},model:{value:t.ideaform.color,callback:function(e){t.$set(t.ideaform,"color",e)},expression:"ideaform.color"}})],1)],1)],1),i("el-row",{staticStyle:{"margin-top":"0px"}},[i("el-col",{attrs:{span:24}},[i("el-form-item",{attrs:{label:"备注"}},[i("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",clearable:""},model:{value:t.ideaform.remark,callback:function(e){t.$set(t.ideaform,"remark",e)},expression:"ideaform.remark"}})],1)],1)],1)],1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitIdeaFrom()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.ideaFromVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},f=[],h=(i("c740"),i("a434"),i("b0c0"),i("a9e3"),i("c7cd"),i("0643"),i("4e3e"),i("159b"),{formcode:"S06M37B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Sa_IdeaPool.refno"},{itemcode:"ideatitle",itemname:"创意标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_IdeaPool.ideatitle"},{itemcode:"ideatype",itemname:"创意类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_IdeaPool.ideatype"},{itemcode:"billdate",itemname:"日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_IdeaPool.billdate"},{itemcode:"itemcount",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_IdeaPool.itemcount"},{itemcode:"publicmark",itemname:"公共",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_IdeaPool.publicmark"},{itemcode:"finishmark",itemname:"状态",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_IdeaPool.finishmark"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_IdeaPool.lister"},{itemcode:"remark",itemname:"备注",minwidth:"100",defwidth:"",displaymark:1,overflow:1,aligntype:"center",datasheet:"Sa_IdeaPool.remark"}]}),u={formcode:"S06M37B1Item",item:[{itemcode:"name",itemname:"内容",minwidth:"150",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"value",itemname:"权重",minwidth:"80",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"color",itemname:"颜色",minwidth:"150",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"remark",itemname:"备注",minwidth:"100",defwidth:"",displaymark:1,overflow:1,aligntype:"center"}]},p={name:"",value:20,color:"",remark:"",rownum:0},b={name:"Elitem",components:{},props:["formdata","lstitem","formstate","idx"],data:function(){var t=this;return{listLoading:!1,lst:[],tableHeight:200,isEditOk:!0,ideaform:{},ideaformType:"add",ideaFromVisible:!1,predefineColors:["#E6A23C","#F56C6C","#67C23A","#13C2C2","#2FC25B","#409EFF","#009DFF","#8496FA","#909399","#000000"],multipleSelection:[],keynum:0,tableForm:u,customList:[],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){this.lst=this.lstitem?JSON.parse(this.lstitem):[]},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i)}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){this.initTable(this.tableForm),this.$forceUpdate()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,r=(i.column,i.rowIndex),s="";return"name"==t.itemcode?(s=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.getAdd(o,r)}}},[o[t.itemcode]?o[t.itemcode]:"内容"]),s):"color"==t.itemcode?(s=a("div",{attrs:{title:o[t.itemcode]},style:"display: flex;align-items: center;justify-content: center;"},[a("div",{style:"margin-right:10px;width:15px;height:15px;background:"+o[t.itemcode]}),o[t.itemcode]]),s):(s=a("span",[o[t.itemcode]]),s)}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.columsData=i,this.keynum+=1},getAdd:function(t,e){this.ideaformType=e,this.ideaform={},this.ideaform=t?Object.assign({},t):Object.assign({},p),this.ideaFromVisible=!0},submitIdeaFrom:function(){var t=this;this.ideaform.name?("add"!=this.ideaformType?this.lst[this.ideaformType]=Object.assign({},this.ideaform):this.lst.push(this.ideaform),this.ideaFromVisible=!1,this.keynum+=1,this.$nextTick((function(){t.$emit("getWordCloud",t.lst)}))):this.$message.warning("内容为必填项")},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},deleteRows:function(t,e){var i=this,a=this.multipleSelection;a&&a.forEach((function(t,e){i.lst.forEach((function(e,a){t.name===e.name&&t.rownum===e.rownum&&i.lst.splice(a,1)}))})),this.$refs.multipleTable.clearSelection()},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=3*(t.$refs.elitem.getBoundingClientRect().height-32))}))},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},g=b,v=(i("d859"),i("2877")),w=Object(v["a"])(g,m,f,!1,null,"e86c2eba",null),y=w.exports,x=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!0,ieval:1,label:'this.formdata.publicmark ? "转私有" : "转公共"',icon:"el-icon-share",disabled:"this.formstate!=1",methods:"toPublicMark",param:"",children:[]},{show:1,divided:!1,ieval:1,label:'!this.formdata.finishmark ? "关闭" : "开启"',icon:"el-icon-s-order",disabled:"this.formstate!=1",methods:"toFinishMark",param:"",children:[]}],k=[],S=(i("87a1"),{name:"Formedit",components:{EditItem:y},props:["idx"],data:function(){return{title:"创意池",operateBar:x,processBar:k,formdata:{id:"",refno:"",ideatitle:"",billdate:new Date,createdate:new Date,ideatype:"",ideajson:"",rownum:"",remark:"",itemcount:0,publicmark:0,finishmark:0,operator:"",operatorid:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{ideatitle:[{required:!0,trigger:"blur",message:"创意标题不能为空"}]},formLabelWidth:"100px",formstate:0,submitting:0,printType:"print",operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",rotationType:!1,rotationRange:[10,60],sizeRange:[20,65]}},computed:{formcontainHeight:function(){return window.innerHeight-113+"px"}},watch:{idx:function(t,e){this.bindData()}},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;this.formstate=0,0!=this.idx&&this.$request.get("/S06M37B1/getEntity?key=".concat(this.idx)).then((function(e){if(200==e.data.code){if(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1,t.formdata.ideajson){var i=JSON.parse(t.formdata.ideajson);t.$nextTick((function(){t.$refs.elitem.lst=i,t.getWordCloud(i)}))}}else t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){for(var t=this,e=Object(n["a"])(this.$refs.elitem.lst),i=0;i<e.length;i++){var a=e[i];this.$delete(a,"rowKeys")}this.formdata.ideajson=e.length?JSON.stringify(e):"",this.formdata.itemcount=e.length?e.length:0;var o=Object.assign({},this.formdata);this.submitting=1,0==this.idx?d.add(o).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData(),t.submitting=0,t.formstate=t.formdata.assessor?2:1)})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):d.update(o).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),t.submitting=0,t.formstate=t.formdata.assessor?2:1)})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){d.delete(e.formdata.id).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)},getWordCloud:function(t){var e=document.getElementById("wordCloud"),i=this.$echarts.init(e),a={backgroundColor:"#fff",series:[{name:"",type:"wordCloud",sizeRange:this.sizeRange,rotationRange:this.rotationRange,textPadding:0,gridSize:8,width:"100%",height:"100%",left:"center",top:"center",drawOutOfBound:!1,layoutAnimation:!0,autoSize:{enable:!0,minSize:4},textStyle:{normal:{color:function(t){return t.data.color?t.data.color:"rgb("+[Math.round(160*Math.random()),Math.round(160*Math.random()),Math.round(160*Math.random())].join(",")+")"}},fontFamily:"sans-serif",fontWeight:"bold"},emphasis:{focus:"self",textStyle:{textShadowBlur:10,textShadowColor:"#333"}},data:t}]};i.setOption(a),window.onresize=function(){i.resize()}},toPublicMark:function(){var t=this,e=this.formdata.publicmark?"是否确认将该创意转入私有中?":"是否确认将该创意转入公共中?";this.$confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.formdata.publicmark=t.formdata.publicmark?0:1,t.saveForm()})).catch((function(){}))},toFinishMark:function(){var t=this,e=this.formdata.finishmark?"是否确认开启创意?":"是否确认关闭创意?";this.$confirm(e,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.formdata.finishmark=t.formdata.finishmark?0:1,t.saveForm()})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},dateFormats:function(){var t=new Date,e=t.getFullYear(),i=(t.getMonth()+1).toString().padStart(2,"0"),a=t.getDate().toString().padStart(2,"0");return"".concat(e).concat(i).concat(a)}}}),$=S,C=(i("a83c"),Object(v["a"])($,r,s,!1,null,"32e91c4e",null)),F=C.exports,_=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)],1)},O=[],D=i("c7eb"),P=i("1da1"),z={components:{},props:[],data:function(){var t=this;return{lst:[],total:0,listLoading:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:h,customList:[],selectList:[],totalfields:[],exportitle:"创意池",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(t){t.row,t.column,t.rowIndx;return{mouseup:function(t){}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1));var e="/S06M37B1/getPageList";this.$request.post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"查询失败"),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;return Object(P["a"])(Object(D["a"])().mark((function e(){return Object(D["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm);case 2:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,r=(i.column,i.rowIndex,"");return"billdate"==t.itemcode||"createdate"==t.itemcode?e.$options.filters.dateFormat(o[t.itemcode]):"refno"==t.itemcode?(r=a("el-button",{attrs:{type:"text",size:"small",title:o[t.itemcode]},on:{click:function(){return e.showform(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"编码"]),r):"finishmark"==t.itemcode?(r=a("div",[a("el-tag",{directives:[{name:"show",value:!o[t.itemcode]}],attrs:{effect:"plain",size:"small"}},["开启"]),a("el-tag",{directives:[{name:"show",value:o[t.itemcode]}],attrs:{effect:"plain",size:"small",type:"info"}},["关闭"])]),r):"publicmark"==t.itemcode?(r=a("div",[a("el-tag",{directives:[{name:"show",value:!o[t.itemcode]}],attrs:{effect:"plain",size:"small",type:"info"}},["私有"]),a("el-tag",{directives:[{name:"show",value:o[t.itemcode]}],attrs:{effect:"plain",size:"small"}},["公共"])]),r):o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},handleSelectionChange:function(t){this.selectList=t},search:function(t){""!=t?this.queryParams.SearchPojo={description:t,version:t,releasename:t,remark:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData(this.projectRow)},AdvancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){for(var e in row)if(""!=row[e]){t={prop:e};"desc"==row[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData();break}},showform:function(t){this.$emit("showform",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)}}},T=z,B=(i("b698"),Object(v["a"])(T,_,O,!1,null,"f19b5cd0",null)),I=B.exports,N=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:"filter-container flex j-s"},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),i("div",{staticClass:"iShowBtn"},[i("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:function(e){return t.$emit("btnExport")}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,baseparam:"/SaDgFormat",tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)},M=[],H={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},thorList:!0,balance:!1,setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},advancedSearch:function(){this.$emit("advancedSearch",this.formdata),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},L=H,E=(i("a097"),Object(v["a"])(L,N,M,!1,null,"6b617a8f",null)),j=E.exports,R={name:"S06M37B1",components:{FormEdit:F,TableList:I,ListHeader:j},data:function(){return{formvisible:!1,idx:0,total:0,showHelp:!1,tableForm:{},thorList:!0,online:0}},computed:{tableMaxHeight:function(){return window.innerHeight-160}},watch:{},mounted:function(){this.bindData(),this.$refs.tableList.getColumn()},methods:{bindData:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.bindData()}))},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},search:function(t){this.$refs.tableList.search(t)},advancedSearch:function(t){this.$refs.tableList.advancedSearch(t)},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},changeBalance:function(t){this.online=t,this.bindData()},showform:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t}}},q=R,V=Object(v["a"])(q,a,o,!1,null,null,null);e["default"]=V.exports},c100:function(t,e,i){},d859:function(t,e,i){"use strict";i("c100")},f3e3:function(t,e,i){}}]);