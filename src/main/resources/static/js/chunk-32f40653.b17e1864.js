(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-32f40653"],{2633:function(t,i,e){t.exports=e.p+"static/img/banner1.3788b4eb.png"},"3ace":function(t,i,e){"use strict";e.r(i);var n=function(){var t=this,i=t.$createElement,e=t._self._c||i;return e("div",{staticClass:"pageStyle"},[e("div",{staticClass:"container-fuild",staticStyle:{"min-height":"500px"},attrs:{id:"swiper"}},[e("div",{staticClass:"swiper-container banner-swiper"},[e("div",{staticClass:"swiper-wrapper"},t._l(t.swiperList,(function(i,n){return e("div",{key:n,staticClass:"swiper-slide"},[e("img",{staticClass:"swiper-lazy",attrs:{"data-src":i.img,alt:"轮播图"}}),e("div",{staticClass:"swiper-lazy-preloader"}),e("div",{staticClass:"swiper-slide-title"},[e("h1",[t._v(t._s(i.title))]),e("p",[t._v(t._s(i.content))])])])})),0),e("div",{staticClass:"swiper-pagination"}),e("div",{staticClass:"swiper-button-prev"}),e("div",{staticClass:"swiper-button-next"})])])])},a=[],s=e("41d6"),r=(e("455b"),{data:function(){return{lst:[],swiperList:[{img:e("2633"),path:"",title:"商务协调团队",content:"协助银行、信托交易所、证券交易客户方案选型，梳理客户需求等工作"},{img:e("cefb"),path:"",title:"售后服务团队",content:"受理用户的系统建设，以现场+远程的方式进行服务，负责跟进客户系统使用情况"},{img:e("2633"),path:"",title:"培训讲师团队",content:"以现场、远程等方式完成系统操作培训，收集用户使用反馈"},{img:e("cefb"),path:"",title:"系统开发团队",content:"对客户需求进行持续响应，保证系统满足用户业务动态发展的需要"}]}},computed:{tableMaxHeight:function(){var t=window.innerHeight-408;return t<500&&(t=500),t+"px"}},mounted:function(){this.bindData()},methods:{bindData:function(){new s["a"](".banner-swiper",{loop:!0,effect:"fade",autoplay:{delay:3e3,stopOnLastSlide:!1,disableOnInteraction:!1},pagination:{el:".swiper-pagination",clickable:!0},navigation:{nextEl:".swiper-button-next",prevEl:".swiper-button-prev"},lazy:{loadPrevNext:!0},observer:!0,observeParents:!0})}}}),c=r,p=(e("60a4"),e("2877")),l=Object(p["a"])(c,n,a,!1,null,"17236544",null);i["default"]=l.exports},"438f":function(t,i,e){},"60a4":function(t,i,e){"use strict";e("438f")},cefb:function(t,i,e){t.exports=e.p+"static/img/banner2.4d894794.jpg"}}]);