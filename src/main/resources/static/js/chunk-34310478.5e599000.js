(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-34310478"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"14cc":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"content"},[a("div",{staticClass:"content-left"},[a("div",{staticClass:"projectTitle flex j-s a-c",staticStyle:{padding:"0 20px"}},[a("h3",{staticClass:"projectname"},[t._v("项目列表")]),a("div",[a("i",{staticClass:"el-icon-edit-outline",staticStyle:{"margin-right":"10px"},attrs:{title:"GitLab同步到需求"},on:{click:t.downloadGitLab}}),a("i",{staticClass:"el-icon-refresh-right",attrs:{title:"刷新"},on:{click:t.bindData}})])]),a("MenuList",{ref:"menuList",style:{height:t.tableMaxHeights+"px","overflow-y":"auto"},attrs:{subMenu:t.projectData},on:{clickMenu:t.clickMenu}})],1),a("div",{staticClass:"content-right"},[a("div",{staticClass:"content-right-header"}),a("cardList",{ref:"cardList",attrs:{projectAllList:t.projectAllList},on:{clickMenu:t.clickMenu}})],1)])])},s=[],r=(a("99af"),a("e9c4"),a("ac1f"),a("841c"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"card-list",style:{height:t.tableMaxHeight}},[a("div",{staticClass:"search",staticStyle:{height:"30px","line-height":"30px"}},[a("cardHeader",{ref:"cardHeader",attrs:{peoples:t.projectList.item},on:{getSearchVal:t.getInitWork}})],1),a("div",{staticStyle:{display:"flex",margin:"15px",height:"calc(96% - 20px)","margin-top":"0"}},[t._l(t.statusList,(function(e,i){return[a("div",{key:i,staticClass:"card-list-item"},[a("div",{staticClass:"card-list-item-header"},[a("p",[t._v(" "+t._s(e.statusname)+" "),a("span",{staticClass:"tag",class:"开始"==e.statustype?"tagBlue":"进行中"==e.statustype?"tagRed":"tagGray"},[t._v(t._s(e.statustype))])]),a("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"bottom-start"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{staticClass:"dropdownPop",attrs:{slot:"dropdown"},slot:"dropdown"},[e.finishmark&&"已完成"==e.statustype?a("el-dropdown-item",{attrs:{icon:"el-icon-view"},nativeOn:{click:function(e){t.isFinishLimit=!t.isFinishLimit,t.getInitWork()}}},[t._v(t._s(t.isFinishLimit?"查看最新":"查看更多"))]):t._e(),a("el-dropdown-item",{attrs:{disabled:null==t.$store.state.user.userinfo.engineer||"管理者"!=t.$store.state.user.userinfo.engineer.engineertype,icon:"el-icon-edit"},nativeOn:{click:function(a){return t.editItem(e,i)}}},[t._v("编辑任务列")]),a("el-dropdown-item",{attrs:{disabled:null==t.$store.state.user.userinfo.engineer||"管理者"!=t.$store.state.user.userinfo.engineer.engineertype,icon:"el-icon-delete"},nativeOn:{click:function(a){return t.deleteItem(e)}}},[t._v(" 删除任务列 ")])],1)],1)],1),"开始"==e.statustype?a("div",{staticClass:"card-list-item-add",on:{click:function(a){return t.openWork(e)}}},[a("i",{staticClass:"el-icon-plus"})]):t._e(),a("draggable",{staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px","overflow-y":"auto","margin-bottom":"8px"},attrs:{group:"aItemBox",tag:"div","data-index":i,"data-name":e.statusname,dragClass:"dragClass",ghostClass:"ghostClass",animation:"500",touchStartThreshold:"50px"},on:{end:function(a){return t.endaItem(a,e)}},model:{value:e.children,callback:function(a){t.$set(e,"children",a)},expression:"item.children"}},[a("transition-group",{staticStyle:{height:"100%",display:"block"},attrs:{"data-index":i,"data-name":e.statusname}},[t._l(e.children,(function(i,s){return[a("div",{key:s,staticClass:"itemBox-li",style:{"border-color":t.setitemBoxli(i.level)},on:{mousedown:function(a){return t.handleSelect(i,e)},click:function(e){return t.openFormedit()}}},[a("div",{staticClass:"aItemTitle",style:{color:"已完成"==e.statustype?"#cccc":"#333"},attrs:{title:i.billtitle}},[t._v(" "+t._s(i.billtitle)+" ")]),i.appointeeid?a("div",{staticClass:"appointeeImg"},[t._v(" "+t._s(i.appointee?i.appointee.slice(0,1).toUpperCase():"NA")+" ")]):t._e(),a("div",{staticClass:"createbySty",style:{color:"已完成"==e.statustype?"#cccc":"#9e9e9e"}},[a("i",{staticClass:"el-icon-user-solid"}),t._v(" "+t._s(i.createby)+" ")]),t.isdeaddate(i.deaddate)&&"已完成"!=e.statustype?a("div",{staticClass:"deaddateSty"},[i.startdate?a("span",{staticClass:"dateBlue",staticStyle:{"margin-bottom":"4px"}},[t._v(" "+t._s(t.startdata(i.startdate))+" 开始 ")]):t._e(),a("span",{class:(new Date).getTime()>new Date(i.deaddate).getTime()?"dateRed":new Date(i.deaddate).getTime()-(new Date).getTime()<864e5?"dateOrange":"dateBlue"},[t._v(t._s(t.isdeaddate(i.deaddate))+" 截止")])]):t._e()])]}))],2)],1)],1)]})),a("div",{directives:[{name:"show",rawName:"v-show",value:null!=t.$store.state.user.userinfo.engineer&&"管理者"==t.$store.state.user.userinfo.engineer.engineertype,expression:"\n          $store.state.user.userinfo.engineer == null\n            ? false\n            : $store.state.user.userinfo.engineer.engineertype == '管理者'\n        "}],staticClass:"addnewStatus",on:{click:t.addStatus}},[a("i",{staticClass:"el-icon-plus"}),t._v(" 新建任务列表 ")])],2)]),t.statusVisible?a("el-dialog",{attrs:{width:"400px",title:"任务列","append-to-body":!0,visible:t.statusVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.statusVisible=e},close:t.bindData}},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"任务列名称",prop:"statusname"}},[a("el-input",{attrs:{placeholder:"请输入任务列名称",clearable:""},model:{value:t.formdata.statusname,callback:function(e){t.$set(t.formdata,"statusname",e)},expression:"formdata.statusname"}})],1),a("el-form-item",{attrs:{label:"任务列类型",prop:"statustype"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择任务列类型"},model:{value:t.formdata.statustype,callback:function(e){t.$set(t.formdata,"statustype",e)},expression:"formdata.statustype"}},[a("el-option",{attrs:{label:"开始",value:"开始"}}),a("el-option",{attrs:{label:"进行中",value:"进行中"}}),a("el-option",{attrs:{label:"已完成",value:"已完成"}})],1)],1),a("el-form-item",{attrs:{label:"任务列排序",prop:"rownum"}},[a("el-input-number",{staticClass:"inputNumberContent",staticStyle:{width:"160px"},attrs:{controls:!0,type:"number",min:0},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}}),a("el-checkbox",{staticStyle:{"margin-left":"10px"},attrs:{label:"结转","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.finishmark,callback:function(e){t.$set(t.formdata,"finishmark",e)},expression:"formdata.finishmark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("提 交")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.statusVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.workVisible?a("el-dialog",{attrs:{width:"480px",title:"新建任务","append-to-body":!0,visible:t.workVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.workVisible=e},close:t.bindData}},[a("el-form",{ref:"workdata",attrs:{model:t.workdata,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"任务标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入任务标题",clearable:""},model:{value:t.workdata.billtitle,callback:function(e){t.$set(t.workdata,"billtitle",e)},expression:"workdata.billtitle"}})],1),a("el-form-item",{attrs:{label:"任务描述",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入任务描述",clearable:"",size:"small",type:"textarea",autosize:{minRows:3,maxRows:6}},model:{value:t.workdata.remark,callback:function(e){t.$set(t.workdata,"remark",e)},expression:"workdata.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitWorkUpdate()}}},[t._v("提 交")]),a("el-button",{on:{click:function(e){t.workVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.formeditVisible?a("el-dialog",{attrs:{width:"56vw",title:"任务","append-to-body":!0,visible:t.formeditVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"6vh"},on:{"update:visible":function(e){t.formeditVisible=e},close:t.getInitWork}},[a("div",{staticClass:"flex",staticStyle:{"justify-content":"space-between"},attrs:{slot:"title"},slot:"title"},[a("div",[t._v("任务")]),a("div",{staticClass:"flex a-c",staticStyle:{"margin-right":"20px",cursor:"pointer"}},[a("el-dropdown",{staticStyle:{margin:"0px 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.$refs.formedit.bindData()}}},[t._v("刷 新")]),a("el-dropdown-item",{attrs:{disabled:2!=t.$store.state.user.userinfo.isadmin&&t.workform.createbyid!=t.$store.state.user.userinfo.userid,icon:"el-icon-delete"},nativeOn:{click:function(e){return t.deleteForm()}}},[t._v("删 除")])],1)],1)],1)]),a("formedit",{ref:"formedit",attrs:{idx:t.workform.id,projectList:t.projectList,status:t.formdata,projectAllList:t.projectAllList},on:{closeDialog:function(e){t.formeditVisible=!1},bindData:function(e){return t.bindData(t.project)},showWorkItem:t.showWorkItem}})],1):t._e()],1)}),n=[],o=a("c7eb"),c=a("1da1"),l=(a("c740"),a("4e82"),a("d3b7"),a("25f0"),a("4d90"),a("b76a")),d=a.n(l),u=a("8c99"),m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"searchForm"},[a("div",{staticClass:"searchForm-item"},[a("el-popover",{ref:"peopleRef",attrs:{placement:"bottom-start",trigger:"click","popper-class":"popper-searchForm"}},[a("div",{staticClass:"searchForm-content"},[a("div",{staticClass:"searchForm-content-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v("创建者")]),a("el-select",{attrs:{clearable:"",placeholder:"请选择创建者"},on:{change:function(e){return t.changeVal("searchForm","create")}},model:{value:t.searchForm.create,callback:function(e){t.$set(t.searchForm,"create",e)},expression:"searchForm.create"}},t._l(t.peoples,(function(t){return a("el-option",{key:t.engineerid,attrs:{label:t.engineername,value:t.engineername}})})),1)],1),a("div",{staticClass:"searchForm-content-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v("执行者")]),a("el-select",{attrs:{clearable:"",placeholder:"请选择执行者"},on:{change:function(e){return t.changeVal("searchForm","appointee")}},model:{value:t.searchForm.appointee,callback:function(e){t.$set(t.searchForm,"appointee",e)},expression:"searchForm.appointee"}},t._l(t.peoples,(function(t){return a("el-option",{key:t.engineerid,attrs:{label:t.engineername,value:t.engineerid}})})),1)],1),a("div",{staticStyle:{width:"100%",height:"1px",background:"#f0f0f0","margin-bottom":"10px"}}),a("div",{staticClass:"searchForm-content-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v("排序(降)")]),a("el-select",{attrs:{placeholder:"请选择排序方式"},on:{change:function(e){return t.changeVal("searchForm","sort")}},model:{value:t.searchForm.sort,callback:function(e){t.$set(t.searchForm,"sort",e)},expression:"searchForm.sort"}},[a("el-option",{attrs:{label:"更新时间",value:"statusmodifydate"}}),a("el-option",{attrs:{label:"创建时间",value:"createdate"}}),a("el-option",{attrs:{label:"截止时间",value:"deaddate"}})],1)],1)]),a("div",{staticClass:"searchForm-footer"},[a("div",{staticClass:"refresh",on:{click:function(e){t.searchForm.create="",t.searchForm.appointee="",t.searchForm.sort="statusmodifydate",t.$emit("getSearchVal")}}},[a("i",{staticClass:"el-icon-refresh-right"}),t._v(" 重置 ")])]),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("span",{staticStyle:{cursor:"pointer"}},[t._v("筛选 "),a("i",{staticClass:"el-icon-arrow-down"})])])])],1)])])},p=[],f={props:{peoples:{type:Array,default:[]}},data:function(){return{searchForm:{create:"",appointee:"",sort:"statusmodifydate"},searchOption:[]}},methods:{initSearch:function(){this.searchForm={create:"",appointee:"",sort:"statusmodifydate"}},changeVal:function(t,e){console.log(t,this[t][e]),this.$emit("getSearchVal")}}},h=f,g=(a("bf2b"),a("7714"),a("2877")),b=Object(g["a"])(h,m,p,!1,null,"3c7fbaf9",null),v=b.exports,w={components:{draggable:d.a,formedit:u["a"],cardHeader:v},props:["projectAllList"],data:function(){return{lst:[],statusList:[],project:{},projectList:{item:[]},idx:0,workVisible:!1,formeditVisible:!1,statusVisible:!1,formdata:{statustype:"开始",statusname:"",id:"",rownum:0,finishmark:0},workdata:{billtype:"新增",billtitle:"",remark:""},isFinishLimit:!1,workform:{id:0},workList:[]}},computed:{tableMaxHeight:function(){return window.innerHeight-110+"px"}},mounted:function(){},methods:{bindData:function(t){var e=this;return Object(c["a"])(Object(o["a"])().mark((function a(){return Object(o["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return e.project=Object.assign({},t),a.next=3,e.$request.get("/S06M01S1/getBillEntity?key="+t.id).then((function(t){200==t.data.code?(e.projectList=Object.assign({},t.data.data),e.statusList=t.data.data.status,e.getInitWork(),e.$refs.cardHeader.initSearch(),e.$forceUpdate()):e.$message.warning(t.data.msg||"获取任务列信息失败")}));case 3:case"end":return a.stop()}}),a)})))()},getInitWork:function(){var t=this;return Object(c["a"])(Object(o["a"])().mark((function e(){var a,i,s,r,n;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.statusList.length){e.next=3;break}return t.$message.warning("获取任务列信息失败，请刷新重试"),e.abrupt("return");case 3:for(a=0;a<t.statusList.length;a++)t.statusList[a].children=[];return i={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1,OrderBy:"statusmodifydate",scenedata:[{field:"projectid",fieldtype:1,math:"equal",value:t.project.id}]},s=t.$refs.cardHeader.searchForm,s.create&&(r={field:"e2.EngineerName",fieldtype:1,math:"equal",value:s.create},i.scenedata.push(r)),s.appointee&&(r={field:"appointee",fieldtype:1,math:"equal",value:s.appointee},i.scenedata.push(r)),s.sort&&(i.OrderBy=s.sort),n="/S06M02B1/getPageList?own=true",t.isFinishLimit&&(n+="&finishlimit=200"),e.next=13,t.$request.post(n,JSON.stringify(i)).then((function(e){if(200==e.data.code){t.workList=e.data.data.list;for(var a=0;a<t.workList.length;a++){var i=t.workList[a],s=t.statusList.findIndex((function(t){return t.id==i.status}));-1!=s&&t.statusList[s].children.push(i)}t.$forceUpdate()}}));case 13:case"end":return e.stop()}}),e)})))()},addStatus:function(){this.formdata={statustype:"开始",statusname:"",id:"",rownum:this.projectList.status.length+1,finishmark:0},this.statusVisible=!0},editItem:function(t,e){this.formdata=Object.assign({},t),this.statusVisible=!0},submitUpdate:function(){var t=this;if(this.formdata.statusname){if(this.formdata.id)var e=Object.assign({},this.formdata),a="/S06M01S1/updateStatus",i="任务列保存";else e={pid:this.project.id,statusname:this.formdata.statusname,statustype:this.formdata.statustype,rownum:this.formdata.rownum,finishmark:this.formdata.finishmark},a="/S06M01S1/createStatus",i="任务列创建";this.$request.post(a,JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(i+"成功"),t.statusVisible=!1,t.$emit("clickMenu",t.project)):t.$message.warning(e.data.msg||i+"失败")}))}else this.$message.warning("任务列名称为必填项")},deleteItem:function(t){var e=this;this.$confirm("此操作将永久删除该任务列, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.$request.get("/S06M01S1/deleteStatus?key="+t.id).then((function(t){200==t.data.code?(e.$message.success("删除成功"),e.$emit("clickMenu",e.project)):e.$message.warning(t.data.msg||"删除失败")}))})).catch((function(){e.$message.error(error||"删除错误")}))},openWork:function(t){this.workVisible=!0,this.workdata={billtype:"新增",billtitle:"",billdate:new Date,remark:"",level:1,status:t.id,projectid:this.project.id,itemcode:this.project.code,itemname:this.projectList.projname},console.log("projectList",this.projectList,this.workdata)},submitWorkUpdate:function(){var t=this,e=Object.assign({},this.workdata);this.$request.post("/S06M02B1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success("任务创建成功"),t.workVisible=!1,t.bindData(t.project)):t.$message.warning(e.data.msg||"任务创建失败")}))},handleSelect:function(t,e){this.workform=Object.assign({},t),this.formdata=Object.assign({},e)},statrItem:function(t,e,a){console.log("statrItem",t,e,a)},endaItem:function(t,e){var a=this;console.log("endaItem",t,t.to.dataset.id,e);var i=t.to.dataset.index,s={id:this.workform.id,status:this.statusList[i].id};this.statusList[i].id!=e.id&&s.id&&this.$request.post("/S06M02B1/update",JSON.stringify(s)).then((function(t){200==t.data.code?a.getInitWork():a.$message.warning(t.data.msg||"修改失败")}))},deleteForm:function(){var t=this;this.$confirm("是否确定删除任务【"+this.workform.billtitle+"】，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$request.get("/S06M02B1/delete?key=".concat(t.workform.id)).then((function(e){if(200==e.data.code){t.$message.success("删除成功");for(var a=0;a<t.statusList.length;a++)t.statusList[a].children=[];t.getInitWork(),t.formeditVisible=!1}else t.$message.warning(res.data.msg||"删除失败")})).catch((function(e){t.$message.warning(e||"删除失败")}))})).catch((function(){}))},openFormedit:function(){this.formeditVisible=!0},setitemBoxli:function(t){return 1==t?"rgb(27, 154, 238)":2==t?"rgb(250, 140, 21)":3==t?"rgb(230, 36, 18)":"rgb(140, 140, 140)"},isdeaddate:function(t){if(!t)return!1;var e=new Date(t).getTime(),a=new Date(t),i=(a.getFullYear(),(a.getMonth()+1).toString().padStart(2,"0")),s=a.getDate().toString().padStart(2,"0"),r=a.getHours().toString().padStart(2,"0"),n=a.getMinutes().toString().padStart(2,"0");return(new Date).getTime()>e||(new Date).getTime(),"".concat(i,"月").concat(s,"日").concat(r,":").concat(n)},startdata:function(t){if(!t)return!1;new Date(t).getTime();var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),i=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0");return"".concat(a,"月").concat(i,"日").concat(s,":").concat(r)},getSearchVal:function(t){var e=this;console.log(this.$refs.cardHeader.searchForm);for(var a=0;a<this.statusList.length;a++)this.statusList[a].children=[];var i={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1,scenedata:[{field:"projectid",fieldtype:1,math:"equal",value:this.project.id}]},s=this.$refs.cardHeader.searchForm;if(s.create){var r={field:"createengineername",fieldtype:1,math:"equal",value:s.create};i.scenedata.push(r)}if(s.appointee){r={field:"appointee",fieldtype:1,math:"equal",value:s.appointee};i.scenedata.push(r)}s.sort&&(i.OrderBy=s.sort),this.$request.post("/S06M02B1/getPageList?own=true",JSON.stringify(i)).then((function(t){if(200==t.data.code){e.workList=t.data.data.list;for(var a=0;a<e.workList.length;a++){var i=e.workList[a],s=e.statusList.findIndex((function(t){return t.id==i.status}));-1!=s&&e.statusList[s].children.push(i)}e.$forceUpdate()}else e.$message.warning(t.data.msg||"查询失败")}))},showWorkItem:function(t){var e=this;console.log(t),this.formeditVisible=!1,setTimeout((function(){e.workform=Object.assign({},t),e.formeditVisible=!0}),100)}}},k=w,y=(a("a367"),Object(g["a"])(k,r,n,!1,null,"db2cbdca",null)),S=y.exports,x=a("ab18"),L={name:"S06M02B1",components:{cardList:S,MenuList:x["a"]},data:function(){return{FormVisible:!1,online:1,thorList:!1,projectData:[],projectAllList:[]}},computed:{tableMaxHeights:function(){return window.innerHeight-180}},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};this.$request.post("/S06M01S1/getBillList?own=true",JSON.stringify(e)).then((function(e){if(200==e.data.code&&e.data.data.list.length){t.projectAllList=e.data.data.list;for(var a=e.data.data.list.concat(),i=[],s=0;s<a.length;s++){var r=a[s],n={id:r.id,label:r.projname+" "+r.todocount+"-"+r.overduecount,code:r.projcode,icon:t.setBgStyle(r)["icon"],type:"file",iconbg:t.setBgStyle(r)["color"],icontip:r.custom1};i.push(n)}t.projectData=i.concat(),t.projectData.length&&t.$refs.menuList.clickVal(t.projectData[0])}}))},setBgStyle:function(t){var e={color:"#00a2ff",icon:"el-icon-discount"};return"合同"==t.custom1?(e.color="#00a2ff",e.icon="el-icon-document"):"实施"==t.custom1?(e.color="#f44336",e.icon="el-icon-s-operation"):"自研"==t.custom1?(e.color="#fdac35",e.icon="el-icon-edit-outline"):"维护"==t.custom1?(e.color="#4caf50",e.icon="el-icon-connection"):(e.color="#838383",e.icon="el-icon-discount"),e},clickMenu:function(t){console.log(t,this.projectData),this.$refs.cardList.bindData(t)},pagePrint:function(){this.thorList?this.$refs.cardList.pagePrint():this.$refs.tableList.pagePrint()},searchByGroup:function(t){this.thorList?this.$refs.cardList.searchByGroup(t):this.$refs.tableList.searchByGroup(t)},search:function(t){this.thorList?this.$refs.cardList.search(t):this.$refs.tableList.search(t)},AdvancedSearch:function(t){this.thorList?this.$refs.cardList.AdvancedSearch(t):this.$refs.tableList.AdvancedSearch(t)},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(t){this.idx=t},changeBalance:function(t){this.online=t,this.bindData()},openList:function(t){var e=this;this.thorList=t,t&&(this.treeVisble=!1),setTimeout((function(){e.bindData()}),10)},downloadGitLab:function(){var t=this;this.$confirm(" 是否确认重新GitLab同步需求?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.initGitLab()})).catch((function(){}))},initGitLab:function(){var t=this,e="/GitLab/syncDemandFromGitLabByLogin";this.$request.get(e).then((function(e){if(200==e.data.code){var a=e.data.data;if(a){var i=a.split("GitLab API:")[0];t.$alert(i,"提示")}t.bindData()}else t.$message.warning(e.data.msg||"同步失败")})).catch((function(e){t.$message.error(e||"请求错误")}))}}},$=L,j=(a("1dc4"),Object(g["a"])($,i,s,!1,null,"62a97ba7",null));e["default"]=j.exports},"1dc4":function(t,e,a){"use strict";a("c5ef")},"3e8e":function(t,e,a){},7714:function(t,e,a){"use strict";a("3e8e")},"841c":function(t,e,a){"use strict";var i=a("d784"),s=a("825a"),r=a("1d80"),n=a("129f"),o=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=r(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var r=s(t),c=String(this),l=r.lastIndex;n(l,0)||(r.lastIndex=0);var d=o(r,c);return n(r.lastIndex,l)||(r.lastIndex=l),null===d?-1:d.index}]}))},"8c41":function(t,e,a){},a367:function(t,e,a){"use strict";a("8c41")},bf2b:function(t,e,a){"use strict";a("c1f7")},c1f7:function(t,e,a){},c5ef:function(t,e,a){}}]);