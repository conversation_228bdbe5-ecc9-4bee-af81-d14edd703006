(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-344f2d7a"],{"67d2":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnsearch:function(t){return e.$refs.tableList.search(t)},bindData:e.bindData,advancedSearch:function(t){return e.$refs.tableList.advancedSearch(t)},btnExport:function(t){return e.$refs.tableList.btnExport()},btnHelp:e.btnHelp,changeBalance:e.changeBalance,changeismy:e.changeismy,bindColumn:function(t){return e.$refs.tableList.getColumn()}}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:e.showHelp?20:24}},[a("TableList",{ref:"tableList",attrs:{onismy:e.onismy,online:e.online},on:{changeidx:e.changeidx,sendTableForm:e.sendTableForm}})],1),a("el-col",{attrs:{span:e.showHelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"S06M02B2"}})],1)],1)],1)],1)])},n=[],s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"filter-container flex j-s a-c"},[a("div",{staticStyle:{display:"flex"}},[a("div",{staticClass:"flex infoForm a-c"},[a("span",{staticClass:"infoForm-Title"},[e._v("时间范围")]),a("el-date-picker",{staticStyle:{width:"250px","margin-right":"10px"},attrs:{"unlink-panels":"",size:"mini","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"],type:"daterange","range-separator":"-","start-placeholder":"开始日期","end-placeholder":"结束日期","picker-options":e.pickerOptions},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1)],1),a("div",{staticClass:"iShowBtn"},[a("div",{staticStyle:{display:"inline-block"}},[a("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#13ce66"},on:{change:e.changeismy},model:{value:e.ismy,callback:function(t){e.ismy=t},expression:"ismy"}}),a("span",{staticStyle:{"margin-right":"10px","font-size":"14px"}},[e._v(e._s(e.ismy?"我的":"公海"))])],1),a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:e.changeBalance},model:{value:e.balance,callback:function(t){e.balance=t},expression:"balance"}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:e.$store.state.advancedSearch.modulecode==e.tableForm.formcode?"primary":"default"},on:{click:function(t){return e.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:e.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(t){return e.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(t){return e.$emit("btnHelp")}}})],1)]),e.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:e.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.setColumsVisible=t}}},[a("SetColums",{ref:"setcolums",attrs:{code:e.tableForm.formcode,baseparam:"/SaDgFormat",tableForm:e.tableForm},on:{bindData:function(t){return e.$emit("bindColumn")},closeDialog:function(t){e.setColumsVisible=!1}}})],1):e._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:e.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.searchVisible=t}}},[a("SearchForm",{ref:"searchForm",attrs:{code:e.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:e.advancedSearch,closedDialog:function(t){e.searchVisible=!1},bindData:e.bindData}})],1)],1)},o=[],r=a("b893"),l={name:"Listheader",components:{},props:["showTree","tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},dateRange:Object(r["c"])(),pickerOptions:Object(r["g"])(),balance:!0,ismy:!0,setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},advancedSearch:function(e){var t={dateRange:this.dateRange,formdata:e};this.$emit("advancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var e=this;this.searchVisible=!0,setTimeout((function(){e.$refs.searchForm.getInit()}),100)},btnsearch:function(){var e={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnsearch",e)},btnExport:function(){this.$emit("btnExport")},bindData:function(){this.$emit("bindData")},changeBalance:function(e){var t=0;t=e?1:0,this.$emit("changeBalance",t)},changeismy:function(e){var t=0;t=e?1:0,this.$emit("changeismy",t)}}},c=l,d=(a("ec5a"),a("2877")),m=Object(d["a"])(c,s,o,!1,null,"0036ccbc",null),h=m.exports,u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],ref:"tableList",staticStyle:{overflow:"auto"},attrs:{data:e.lst,"summary-method":e.getSummaries,"show-summary":"","element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(t.$index+1))])]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["billdate"==t.itemcode||"receivetime"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormat")(i.row[t.itemcode])))]):"startdate"==t.itemcode||"deaddate"==t.itemcode||"finishdate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormats")(i.row[t.itemcode])))]):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.getList}}),a("div",{staticStyle:{"margin-right":"40px"}},[a("Scene",{ref:"scene",attrs:{code:e.tableForm.formcode},on:{bindData:e.bindData}})],1)],1)],1)},f=[],b=a("c7eb"),p=a("1da1"),g=(a("d81d"),a("e9c4"),a("b64b"),a("d3b7"),a("0643"),a("a573"),a("b775")),y={formcode:"S06M02B2List",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Sa_Demand.refno"},{itemcode:"billdate",itemname:"单据日期",minwidth:"120",displaymark:1,sortable:1,overflow:1,datasheet:"Sa_Demand.billdate"},{itemcode:"billtype",itemname:"单据类型",minwidth:"120",displaymark:1,sortable:1,overflow:1,datasheet:"Sa_Demand.billtype"},{itemcode:"billtitle",itemname:"单据标题",minwidth:"180",displaymark:1,sortable:1,overflow:1,datasheet:"Sa_Demand.billtitle"},{itemcode:"abbreviate",itemname:"客户名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Demand.abbreviate"},{itemcode:"level",itemname:"优先级",minwidth:"70",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.state"},{itemcode:"itemname",itemname:"项目名称",minwidth:"120",displaymark:1,overflow:1,datasheet:"Sa_Demand.itemname"},{itemcode:"itemcode",itemname:"项目编码",minwidth:"120",displaymark:1,overflow:1,datasheet:"Sa_Demand.itemcode"},{itemcode:"description",itemname:"内容描述",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.description"},{itemcode:"labeljson",itemname:"标签",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.labeljson"},{itemcode:"startdate",itemname:"开始日期",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.startdate"},{itemcode:"deaddate",itemname:"截止日期",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.deaddate"},{itemcode:"finishdate",itemname:"完成日期",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.finishdate"},{itemcode:"appointee",itemname:"指派人",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.appointee"},{itemcode:"operator",itemname:"经办人",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.operator"},{itemcode:"remark",itemname:"备注",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.remark"},{itemcode:"status",itemname:"状态",minwidth:"70",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.state"},{itemcode:"lister",itemname:"制表",minwidth:"70",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.lister"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.modifydate"}]},w={components:{},props:["online","onismy"],data:function(){return{lst:[],total:0,listLoading:!1,idx:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y,selectList:[]}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},updated:function(){var e=this;this.$nextTick((function(){e.$refs.tableList.doLayout()}))},methods:{bindData:function(){var e=this;if(this.listLoading=!0,this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),this.online)if(this.onismy)var t="/S06M02B2/getOnlinePageList?own=1";else t="/S06M02B2/getOnlinePageList?own=-1";else if(this.onismy)t="/S06M02B2/getPageList?own=1";else t="/S06M02B2/getPageList?own=-1";this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object(r["c"])()[0],EndDate:Object(r["c"])()[1]}),g["a"].post(t,JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},getColumn:function(){var e=this;return Object(p["a"])(Object(b["a"])().mark((function t(){var a;return Object(b["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:a=y,e.$getColumn(e.tableForm.formcode,a).then((function(t){e.tableForm=Object.assign({},t.colList),e.$emit("sendTableForm",e.tableForm)}));case 2:case"end":return t.stop()}}),t)})))()},getSummaries:function(e){return Object(r["e"])(e,["amount"])},getList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},handleSelectionChange:function(e){this.selectList=e},search:function(e){this.$search(this,e,1)},advancedSearch:function(e){this.$advancedSearch(this,e,1)},changeSort:function(e){"descending"==e.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(e,this.tableForm),this.bindData()},showform:function(e){this.$emit("showform",e)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"需求列表")},formatJson:function(e,t){return t.map((function(t){return e.map((function(e){return t[e]}))}))}}},v=w,S=(a("94a5"),Object(d["a"])(v,u,f,!1,null,"4ad6725b",null)),x=S.exports,k=a("0521"),D={name:"S06M02B2",components:{listheader:h,helpmodel:k["a"],TableList:x},data:function(){return{idx:0,tableForm:{},showHelp:!1,online:1,onismy:1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){},mounted:function(){this.bindData(),this.getColumn()},methods:{getColumn:function(){var e=this;this.$nextTick((function(){e.$refs.tableList.getColumn()}))},bindData:function(){var e=this;this.$nextTick((function(){e.$refs.tableList.bindData()}))},sendTableForm:function(e){this.tableForm=e},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},changeidx:function(e){this.idx=e},changeBalance:function(e){this.online=e,this.bindData()},changeismy:function(e){this.onismy=e,this.bindData(),this.$forceUpdate()}}},_=D,$=(a("c85d"),Object(d["a"])(_,i,n,!1,null,"6fd7773f",null));t["default"]=$.exports},"714f":function(e,t,a){},"94a5":function(e,t,a){"use strict";a("f8ca")},bd9dd:function(e,t,a){},c85d:function(e,t,a){"use strict";a("714f")},ec5a:function(e,t,a){"use strict";a("bd9dd")},f8ca:function(e,t,a){}}]);