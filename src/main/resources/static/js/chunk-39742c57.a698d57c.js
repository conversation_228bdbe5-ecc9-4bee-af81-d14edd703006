(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-39742c57"],{"43f2":function(t,a,e){},"8d87":function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"pageStyle",style:{"min-height":t.tableMaxHeight}},[e("div",{staticClass:"main-left"},[e("mavon-editor",{ref:"md",staticClass:"markdown",style:{width:"100%",height:"100%","z-index":"99"},attrs:{value:t.formdata.markdowndata,subfield:t.prop.subfield,defaultOpen:t.prop.defaultOpen,toolbarsFlag:t.prop.toolbarsFlag,editable:t.prop.editable,scrollStyle:t.prop.scrollStyle}})],1),e("div",{staticClass:"main-right"},[e("div",{staticClass:"info-card",class:t.isfixed?"isfixed":"",style:{top:t.fixedtop+"px"}},[e("div",{staticClass:"info-title"},[t._v("基础信息")]),e("ul",{staticClass:"info-content"},[e("li",[e("span",{staticClass:"li-title"},[t._v("分类：")]),e("span",{staticClass:"li-content"},[t._v(t._s(t.formdata.type||"-"))])]),e("li",[e("span",{staticClass:"li-title"},[t._v("运维：")]),e("span",{staticClass:"li-content"},[t._v(t._s(t.formdata.author||"-"))])]),e("li",[e("span",{staticClass:"li-title"},[t._v("发布时间：")]),e("span",{staticClass:"li-content"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate||"-")))])]),e("li",[e("span",{staticClass:"li-title"},[t._v("难易度：")]),e("span",{staticClass:"li-content",staticStyle:{display:"inline-block"}},[e("el-rate",{staticStyle:{"line-height":"2.2"},attrs:{disabled:"","show-score":"","text-color":"#ff9900"},model:{value:t.formdata.rateval,callback:function(a){t.$set(t.formdata,"rateval",a)},expression:"formdata.rateval"}})],1)]),e("li",{directives:[{name:"show",rawName:"v-show",value:t.formdata.descjson,expression:"formdata.descjson"}]},[e("span",{staticClass:"li-title"},[t._v("产品链接：")]),e("span",{staticClass:"li-content linkto",on:{click:function(a){return t.prodInfo()}}},[t._v(t._s(t.formdata.goodsname))])]),e("li",{directives:[{name:"show",rawName:"v-show",value:t.formdata.appurl,expression:"formdata.appurl"}]},[e("span",{staticClass:"li-title"},[t._v("小程序：")]),e("span",{staticClass:"li-content",staticStyle:{display:"inline-block"}},[e("div",[t._v(" 打开微信扫一扫 "),e("div",{ref:"qrCode",attrs:{id:"qrcode"}})])])])])])])])},i=[],o=(e("d044"),{data:function(){return{formdata:{markdowndata:"",rateval:2.5,author:"胡玮",type:"",date:"2023-08-17",appurl:""},fixedtop:176,isfixed:!1,formKey:""}},computed:{tableMaxHeight:function(){var t=window.innerHeight-410;return t<300&&(t=300),t+"px"},prop:function(){var t={subfield:!1,defaultOpen:"preview",editable:!1,toolbarsFlag:!1,scrollStyle:!0};return t}},mounted:function(){this.bindData(),window.addEventListener("scroll",this.handleScrollStart)},beforeDestroy:function(){window.removeEventListener("scroll",this.handleScrollStart)},methods:{bindData:function(){var t=this;this.formKey=this.$route.query.key||this.$route.params.key,console.log(this.$route,"3"),this.formKey&&this.$request.get("/S06M13B1/getEntity?key=".concat(this.formKey)).then((function(a){200==a.data.code?(t.formdata=a.data.data,t.formdata.mdurl&&t.getMarkDown(t.formdata.mdurl)):t.$alert(a.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(a){t.closeForm()}})})).catch((function(a){t.$message.error(a||"请求错误")}))},getMarkDown:function(t){var a=this;this.$request.get("/File/getMinioUrl/"+t).then((function(t){a.formdata.markdowndata=t.data?t.data:"",a.$forceUpdate()}))},handleScrollStart:function(){window.scrollY<176?this.isfixed=!1:(this.isfixed=!0,this.fixedtop=.001*window.scrollY+10)},prodInfo:function(){var t=this.$router.resolve("/suggpage/goods/"+this.formKey);window.open(t.href,"_blank")}}}),l=o,n=(e("c027"),e("2877")),r=Object(n["a"])(l,s,i,!1,null,"8dbf2eee",null);a["default"]=r.exports},c027:function(t,a,e){"use strict";e("43f2")}}]);