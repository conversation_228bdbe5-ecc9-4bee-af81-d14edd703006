(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-39e4b8c6"],{"05d1":function(t,e,a){"use strict";a("20b9")},"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,a,n){return t/=n/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var n=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function i(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=i(),l=t-o,s=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=s;var t=Math.easeInOutQuad(c,o,l,e);r(t),c<e?n(d):a&&"function"===typeof a&&a()};d()}},"20b9":function(t,e,a){},"27ae":function(t,e,a){(function(a){var n,r;(function(e,a){t.exports=a(e)})("undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof a?a:this,(function(a){"use strict";a=a||{};var i,o=a.Base64,l="2.6.4",s="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",c=function(t){for(var e={},a=0,n=t.length;a<n;a++)e[t.charAt(a)]=a;return e}(s),d=String.fromCharCode,m=function(t){if(t.length<2){var e=t.charCodeAt(0);return e<128?t:e<2048?d(192|e>>>6)+d(128|63&e):d(224|e>>>12&15)+d(128|e>>>6&63)+d(128|63&e)}e=65536+1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320);return d(240|e>>>18&7)+d(128|e>>>12&63)+d(128|e>>>6&63)+d(128|63&e)},u=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,p=function(t){return t.replace(u,m)},f=function(t){var e=[0,2,1][t.length%3],a=t.charCodeAt(0)<<16|(t.length>1?t.charCodeAt(1):0)<<8|(t.length>2?t.charCodeAt(2):0),n=[s.charAt(a>>>18),s.charAt(a>>>12&63),e>=2?"=":s.charAt(a>>>6&63),e>=1?"=":s.charAt(63&a)];return n.join("")},h=a.btoa&&"function"==typeof a.btoa?function(t){return a.btoa(t)}:function(t){if(t.match(/[^\x00-\xFF]/))throw new RangeError("The string contains invalid characters.");return t.replace(/[\s\S]{1,3}/g,f)},b=function(t){return h(p(String(t)))},v=function(t){return t.replace(/[+\/]/g,(function(t){return"+"==t?"-":"_"})).replace(/=/g,"")},g=function(t,e){return e?v(b(t)):b(t)},w=function(t){return g(t,!0)};a.Uint8Array&&(i=function(t,e){for(var a="",n=0,r=t.length;n<r;n+=3){var i=t[n],o=t[n+1],l=t[n+2],c=i<<16|o<<8|l;a+=s.charAt(c>>>18)+s.charAt(c>>>12&63)+("undefined"!=typeof o?s.charAt(c>>>6&63):"=")+("undefined"!=typeof l?s.charAt(63&c):"=")}return e?v(a):a});var x,y=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,S=function(t){switch(t.length){case 4:var e=(7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3),a=e-65536;return d(55296+(a>>>10))+d(56320+(1023&a));case 3:return d((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return d((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},k=function(t){return t.replace(y,S)},_=function(t){var e=t.length,a=e%4,n=(e>0?c[t.charAt(0)]<<18:0)|(e>1?c[t.charAt(1)]<<12:0)|(e>2?c[t.charAt(2)]<<6:0)|(e>3?c[t.charAt(3)]:0),r=[d(n>>>16),d(n>>>8&255),d(255&n)];return r.length-=[0,0,2,1][a],r.join("")},F=a.atob&&"function"==typeof a.atob?function(t){return a.atob(t)}:function(t){return t.replace(/\S{1,4}/g,_)},C=function(t){return F(String(t).replace(/[^A-Za-z0-9\+\/]/g,""))},$=function(t){return k(F(t))},A=function(t){return String(t).replace(/[-_]/g,(function(t){return"-"==t?"+":"/"})).replace(/[^A-Za-z0-9\+\/]/g,"")},B=function(t){return $(A(t))};a.Uint8Array&&(x=function(t){return Uint8Array.from(C(A(t)),(function(t){return t.charCodeAt(0)}))});var P=function(){var t=a.Base64;return a.Base64=o,t};if(a.Base64={VERSION:l,atob:C,btoa:h,fromBase64:B,toBase64:g,utob:p,encode:g,encodeURI:w,btou:k,decode:B,noConflict:P,fromUint8Array:i,toUint8Array:x},"function"===typeof Object.defineProperty){var D=function(t){return{value:t,enumerable:!1,writable:!0,configurable:!0}};a.Base64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",D((function(){return B(this)}))),Object.defineProperty(String.prototype,"toBase64",D((function(t){return g(this,t)}))),Object.defineProperty(String.prototype,"toBase64URI",D((function(){return g(this,!0)})))}}return a["Meteor"]&&(Base64=a.Base64),t.exports?t.exports.Base64=a.Base64:(n=[],r=function(){return a.Base64}.apply(e,n),void 0===r||(t.exports=r)),{Base64:a.Base64}}))}).call(this,a("c8ba"))},"35ca":function(t,e,a){},"6e93":function(t,e,a){"use strict";a("35ca")},f3fc:function(t,e,a){},f8cf:function(t,e,a){"use strict";a("f3fc")},f9dc:function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm},on:{btnSearch:t.search,btnAdd:function(e){return t.showForm(0)},advancedSearch:t.advancedSearch,btnHelp:t.btnHelp,bindData:t.bindData,bindColumn:t.getColumn}}),a("div",{staticClass:"page-container"},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:t.showhelp?20:24}},[a("el-table",{staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,n){return[!e.displaymark?t._e():a("el-table-column",{key:n,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(n){return["billdate"==e.itemcode||"modifydate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormats")(n.row[e.itemcode])))]):"apprname"==e.itemcode?a("div",[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showForm(n.row.id)}}},[t._v(" "+t._s(n.row[e.itemcode])+" ")])],1):"enabledmark"==e.itemcode?a("div",[n.row.enabledmark?a("el-tag",{attrs:{size:"small"}},[t._v("正 常")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[t._v("停 用")])],1):a("span",[t._v(t._s(n.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1),a("el-col",{attrs:{span:t.showhelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"S06M36B1"}})],1)],1)],1)],1),a("el-dialog",{attrs:{title:"详细信息","append-to-body":!0,visible:t.searchVisible,width:"60vw","close-on-click-modal":!1},on:{"update:visible":function(e){t.searchVisible=e}}},[a("div",{staticStyle:{display:"flex","margin-top":"-20px"}},[a("div",{staticStyle:{width:"30%","margin-right":"40px"}},[a("span",{staticClass:"searchtitle"},[t._v("vm模板")]),a("el-input",{attrs:{type:"textarea",autosize:{minRows:18,maxRows:21}},model:{value:t.tempdata.name,callback:function(e){t.$set(t.tempdata,"name",e)},expression:"tempdata.name"}})],1),a("div",{staticStyle:{flex:"1"}},[a("span",{staticClass:"searchtitle"},[t._v("temp模板")]),a("el-input",{attrs:{type:"textarea",readonly:"",autosize:{minRows:18,maxRows:21}},model:{value:t.tempdata.content,callback:function(e){t.$set(t.tempdata,"content",e)},expression:"tempdata.content"}})],1)]),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitSave}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.searchVisible=!1}}},[t._v("取 消")])],1)])],1)},r=[],i=(a("b0c0"),a("e9c4"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:function(e){return t.$emit("bindData")}}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini"},on:{click:function(e){t.iShow=!t.iShow}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"模块编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入模块编码",size:"small"},model:{value:t.formdata.modulecode,callback:function(e){t.$set(t.formdata,"modulecode",e)},expression:"formdata.modulecode"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"报表类型"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入报表类型",size:"small"},model:{value:t.formdata.rpttype,callback:function(e){t.$set(t.formdata,"rpttype",e)},expression:"formdata.rpttype"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"报表数据"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入报表数据",size:"small"},model:{value:t.formdata.rptname,callback:function(e){t.$set(t.formdata,"rptname",e)},expression:"formdata.rptname"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"6px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.advancedSearch()}}},[t._v(" 搜索 ")])],1)],1)],1)],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,baseparam:"/SaDgFormat",tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)}),o=[],l={name:"Listheader",props:["tableForm"],components:{},data:function(){return{strfilter:"",iShow:!1,formdata:{},setColumsVisible:!1,code:"D96M13B1List"}},methods:{openDialog:function(){this.setColumsVisible=!0},advancedSearch:function(){this.iShow=!1,this.$emit("advancedSearch",this.formdata)},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){this.$emit("btnSearch",this.strfilter)}}},s=l,c=(a("05d1"),a("2877")),d=Object(c["a"])(s,i,o,!1,null,"76bab4f7",null),m=d.exports,u=a("333d"),p=a("b775"),f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{disabled:!t.idx,size:"small"},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v(" 删 除")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"on",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("modulecode")}}},[a("el-form-item",{attrs:{label:"模块编码",prop:"modulecode"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入模块编码",size:"small"},model:{value:t.formdata.modulecode,callback:function(e){t.$set(t.formdata,"modulecode",e)},expression:"formdata.modulecode"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("templateid")}}},[a("el-form-item",{attrs:{label:"模版id",prop:"templateid"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入模版id",size:"small"},model:{value:t.formdata.templateid,callback:function(e){t.$set(t.formdata,"templateid",e)},expression:"formdata.templateid"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("apprname")}}},[a("el-form-item",{attrs:{label:"审批名称",prop:"apprname"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入审批名称",size:"small"},model:{value:t.formdata.apprname,callback:function(e){t.$set(t.formdata,"apprname",e)},expression:"formdata.apprname"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("apprtype")}}},[a("el-form-item",{attrs:{label:"审批类型"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入审批类型",size:"small"},model:{value:t.formdata.apprtype,callback:function(e){t.$set(t.formdata,"apprtype",e)},expression:"formdata.apprtype"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"执行条件",prop:"resultcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入执行条件",size:"small"},model:{value:t.formdata.resultcode,callback:function(e){t.$set(t.formdata,"resultcode",e)},expression:"formdata.resultcode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"回调Bean",prop:"callbackbean"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入回调Bean",size:"small"},model:{value:t.formdata.callbackbean,callback:function(e){t.$set(t.formdata,"callbackbean",e)},expression:"formdata.callbackbean"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"","label-width":"60px"}},[a("el-checkbox",{attrs:{label:"有效性","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:15}},[a("el-form-item",{attrs:{label:"回调Url",prop:"callbackurl"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入回调Url",size:"small"},model:{value:t.formdata.callbackurl,callback:function(e){t.$set(t.formdata,"callbackurl",e)},expression:"formdata.callbackurl"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"15px"}},[a("el-col",{attrs:{span:22}},[a("el-form-item",{attrs:{label:"审批模板"}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入审批模板",size:"small",type:"textarea",autosize:{minRows:20,maxRows:24}},model:{value:t.reportBase64Data,callback:function(e){t.reportBase64Data=e},expression:"reportBase64Data"}})],1)])],1)],1),a("el-divider"),a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)])])])},h=[],b=a("c7eb"),v=a("1da1");a("b64b");const g={add(t){return new Promise((e,a)=>{var n=JSON.stringify(t);p["a"].post("/S06M36B1/create",n).then(t=>{console.log(n,t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var n=JSON.stringify(t);p["a"].post("/S06M36B1/update",n).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{p["a"].get("/S06M36B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var w=g,x=(a("27ae"),{name:"Formedit",components:{},props:["idx"],data:function(){return{title:"企业微信审批",formdata:{modulecode:"",apprtype:"",apprname:"",datatemp:"",resultcode:"",callbackbean:"",rownum:0,enabledmark:1,remark:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,tenantid:"default"},reportBase64Data:"",formRules:{modulecode:[{required:!0,trigger:"blur",message:"模块编码为必填项"}],apprtype:[{required:!0,trigger:"blur",message:"审批类型为必填项"}],apprname:[{required:!0,trigger:"blur",message:"审批名称为必填项"}]},formLabelWidth:"100px",multi:0,selVisible:!1,tenantData:[]}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;return Object(v["a"])(Object(b["a"])().mark((function e(){return Object(b["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0==t.idx){e.next=4;break}return t.listLoading=!0,e.next=4,p["a"].get("/S06M36B1/getEntity?key=".concat(t.idx)).then((function(e){200==e.data.code&&(t.formdata=e.data.data,t.reportBase64Data=e.data.data.datatemp),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 4:case"end":return e.stop()}}),e)})))()},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.formdata.datatemp=this.reportBase64Data,0==this.idx?(w.add(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data})).catch((function(e){t.$message.warning(e||"保存失败")})),console.log("完成窗口")):w.update(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data})).catch((function(e){t.$message.warning(e||"保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){w.delete(t).then((function(t){e.$message.success(t.msg||"删除成功"),e.$emit("compForm")})).catch((function(t){e.$message.warning(t||"删除失败")}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}}),y=x,S=(a("6e93"),Object(c["a"])(y,f,h,!1,null,"95240a62",null)),k=S.exports,_=a("0521"),F={formcode:"S06M36B1List",item:[{itemcode:"modulecode",itemname:"模块编码",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Uts_WxeAppr.modulecode"},{itemcode:"templateid",itemname:"模板id",minwidth:"100",displaymark:0,overflow:1,datasheet:"Uts_WxeAppr.templateid"},{itemcode:"apprname",itemname:"审批名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Uts_WxeAppr.apprname"},{itemcode:"apprtype",itemname:"审批类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Uts_WxeAppr.apprtype"},{itemcode:"callbackbean",itemname:"回调Bean",minwidth:"80",displaymark:1,overflow:1,datasheet:"Uts_WxeAppr.callbackbean"},{itemcode:"callbackurl",itemname:"回调url",minwidth:"150",displaymark:1,overflow:0,datasheet:"Uts_WxeAppr.callbackurl"},{itemcode:"resultcode",itemname:"执行条件",minwidth:"80",displaymark:1,overflow:1,datasheet:"Uts_WxeAppr.resultcode"},{itemcode:"enabledmark",itemname:"有效性",minwidth:"60",displaymark:1,overflow:1,datasheet:"Uts_WxeAppr.enabledmark"},{itemcode:"lister",itemname:"制表",minwidth:"60",displaymark:1,overflow:1,datasheet:"Uts_WxeAppr.lister"},{itemcode:"modifydate",itemname:"日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Uts_WxeAppr.modifydate"}]},C={components:{Pagination:u["a"],listheader:m,formedit:k,helpmodel:_["a"]},data:function(){return{title:"",lst:[],FormVisible:!1,idx:0,total:0,selectedList:[],queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:F,showhelp:!1,searchVisible:!1,tempdata:{content:"",name:""},selectRow:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;this.listLoading=!0,p["a"].post("/S06M36B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;p["a"].get("/SaDgFormat/getBillEntityByCode?code=S06M36B1List").then((function(e){if(200==e.data.code){if(null==e.data.data)return void(t.tableForm=F);t.tableForm=e.data.data}})).catch((function(e){t.$message.error("请求出错")}))},btnHelp:function(){this.showhelp=!this.showhelp,this.showhelp&&this.$refs.helpmodel.bindData()},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={modulecode:t,rptname:t,rpttype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showForm:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1,this.bindData()},compForm:function(){this.bindData(),this.FormVisible=!1},changeIdx:function(t){this.idx=t},openDialog:function(t){var e=this;this.selectRow=t,p["a"].get("/S06M36B1/getTemplateDetail?key="+t.templateid).then((function(t){if(200==t.data.code)if(0==t.data.data.errcode){e.searchVisible=!0;var a='{\n"creator_userid": "${approvePojo.creatoruserid}",\n"template_id": "${approvePojo.modelcode}",\n"use_template_approver": 0,\n"approver": [{\n"attr": 1,\n"userid": ["nanno"]\n}],\n#if (${approvePojo.notifyer})\n"notifyer": ["${approvePojo.notifyer}"],\n#end "notify_type": 1,\n"apply_data": {\n"contents": [{\n"control": "Text",\n"id": "item-1494250768190",\n"value": {\n"text": "${approvePojo.object.groupname}"\n}\n},\n{\n"control": "Textarea",\n"id": "Textarea-1675703462714",\n"value": {\n"text": "${approvePojo.object.refno}"\n}\n}\n,{\n"control": "Table",\n"id": "Table-1673925193003",\n"value": {\n"children": [\n#foreach($object in $approvePojo.object.item)\n{\n"list": [{\n"control": "Text",\n"id": "Text-1675743312055",\n"value": {\n"text": "${object.goodsname}"\n}\n},\n{\n"control": "Text",\n"id": "Text-1675703544721",\n"value": {\n"text": "${object.price}"\n}\n}\n]\n}\n#if($foreach.hasNext),#end\n#end\n]\n}\n}\n]\n}\n}';e.tempdata.content=JSON.stringify(t.data.data.template_content,null,"\t"),e.tempdata.name=t.data.data.template_names.length?JSON.stringify(t.data.data.template_names,null,"\t"):a}else e.$message.warning(t.data.data.errmsg||"获取信息失败");else e.$message.warning(t.data.msg||"获取MV模板信息失败")}))},submitSave:function(){var t=this,e=Object.assign({},this.selectRow);e.datatemp=this.tempdata.name,p["a"].post("/S06M36B1/update",JSON.stringify(e)).then((function(e){200==e.data.code?(t.searchVisible=!1,t.$message.success("保存成功"),t.bindData()):t.$message.warning(e.data.msg||"保存失败")}))}}},$=C,A=(a("f8cf"),Object(c["a"])($,n,r,!1,null,"68953b49",null));e["default"]=A.exports}}]);