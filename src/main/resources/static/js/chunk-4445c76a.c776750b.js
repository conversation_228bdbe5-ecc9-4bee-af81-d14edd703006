(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4445c76a"],{"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,i,a){return t/=a/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,i){var o=s(),r=t-o,l=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=l;var t=Math.easeInOutQuad(c,o,r,e);n(t),c<e?a(d):i&&"function"===typeof i&&i()};d()}},"270a":function(t,e,i){},"3bc6":function(t,e,i){"use strict";i("270a")},"5abc":function(t,e,i){},"5c73":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,a){return i("li",{key:a,class:t.radio==a?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=a}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,a){return i("p",{key:a,class:t.ActiveIndex==a?"isActive":"",on:{click:function(e){t.ActiveIndex=a}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},n=[],s=(i("a434"),i("e9c4"),i("b775")),o=i("333d"),r=i("b0b8"),l={components:{Pagination:o["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],s["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){r.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:r.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,r.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=r.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,s["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=l,d=(i("af2b"),i("2877")),u=Object(d["a"])(c,a,n,!1,null,"d2ba3d7a",null);e["a"]=u.exports},6324:function(t,e,i){},"7de4":function(t,e,i){},"7e12":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[i("el-row",[i("el-col",{attrs:{span:12}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"类型",prop:"billtype"}},[i("el-input",{attrs:{placeholder:"类型",size:"small",readonly:""},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[i("el-input",{attrs:{placeholder:"请输入标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)]),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"计划完成"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.formdata.endplan,callback:function(e){t.$set(t.formdata,"endplan",e)},expression:"formdata.endplan"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"客户名"}},[i("el-popover",{ref:"groupnamePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}}},[i("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.groupname"},on:{singleSel:function(e){t.formdata.groupname=e.dictvalue,t.$refs.groupnamePopverRef.doClose()},closedic:function(e){return t.$refs.groupnamePopverRef.doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"客户名",clearable:"",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"产品名"}},[i("el-popover",{ref:"productnamePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selProductnameRef.bindData()}}},[i("selDictionaries",{ref:"selProductnameRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.productname"},on:{singleSel:function(e){t.formdata.productname=e.dictvalue,t.$refs.productnamePopverRef.doClose()},closedic:function(e){return t.$refs.productnamePopverRef.doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"产品名",clearable:"",size:"small"},model:{value:t.formdata.productname,callback:function(e){t.$set(t.formdata,"productname",e)},expression:"formdata.productname"}})],1)],1)],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"来源"}},[i("el-popover",{ref:"sourcePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.sourceRef.bindData()}}},[i("selDictionaries",{ref:"sourceRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.source"},on:{singleSel:function(e){t.formdata.source=e.dictvalue,t.$refs.sourcePopverRef.doClose()},closedic:function(e){return t.$refs.sourcePopverRef.doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"来源",clearable:"",size:"small"},model:{value:t.formdata.source,callback:function(e){t.$set(t.formdata,"source",e)},expression:"formdata.source"}})],1)],1)],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:""}},[i("el-checkbox",{attrs:{label:"公共","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:""}},[i("el-checkbox",{attrs:{label:"重要","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.importantmark,callback:function(e){t.$set(t.formdata,"importantmark",e)},expression:"formdata.importantmark"}})],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:""}},[i("el-checkbox",{attrs:{label:"紧急","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.urgentmark,callback:function(e){t.$set(t.formdata,"urgentmark",e)},expression:"formdata.urgentmark"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建人"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"创建日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"制表"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"修改日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)},n=[],s=(i("b64b"),i("b775")),o=i("5c73"),r={name:"Formedit",props:["idx"],components:{selDictionaries:o["a"]},data:function(){return{title:"TODO列表",formdata:{billtype:"公海",accepterid:"root",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,productname:"",groupname:"",endplan:new Date},formRules:{type:[{required:!0,trigger:"blur",message:"类型不能为空"}],title:[{required:!0,trigger:"blur",message:"标题不能为空"}],content:[{required:!0,trigger:"blur",message:"公告内容不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&s["a"].get("/S06M09B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?CRUD.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")})):CRUD.update(this.formdata).then((function(e){console.log("保存====",e),200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),CRUD.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},l=r,c=(i("3bc6"),i("2877")),d=Object(c["a"])(l,a,n,!1,null,"2f124ae9",null);e["a"]=d.exports},af2b:function(t,e,i){"use strict";i("7de4")},b9c6:function(t,e,i){"use strict";i("5abc")},cc0b:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"todoContainer"},[i("listheader",{ref:"listheader",attrs:{userList:t.userList},on:{btnadd:function(e){return t.btnadd()},btnsearch:t.search,bindData:function(e){return t.bindData()},btnHelp:t.btnHelp,quickTickBtn:t.quickTickBtn}}),i("el-row",[i("el-col",{attrs:{span:t.showHelp?20:24}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("div",{staticClass:"todoContai"},[i("div",{staticClass:"todoHead"},[i("div",{staticClass:"headorder"},[t._v("序号")]),t._l(t.userList,(function(e){return[i("div",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"t.show"}],key:e.id,staticClass:"headItem",style:{background:e.backcolorargb}},[t._v(" "+t._s(e.name)+" "+t._s(e.taskNum?"（"+e.taskNum+"）":"")+" ")])]}))],2),t._l(t.typeList,(function(e,a){return i("div",{key:a,staticClass:"todoBody",style:{height:240*e.typeheight+"px"}},[i("div",{staticClass:"left",style:{background:e.typecolor}},[t._v(" "+t._s(e.typename)+" ")]),t._l(t.userList,(function(n,s){return i("div",{key:s,staticStyle:{height:"100%"}},[n.show?i("div",{staticClass:"bodyType"},[i("div",{staticClass:"right"},[i("draggable",{staticClass:"itemBox",staticStyle:{height:"100%"},attrs:{group:"items",tag:"div","data-row":e.typename,"data-col":n.id,"data-name":n.name},on:{end:function(i){return t.dragChange(i,e.typename,n.id,n.name)}}},[t._l(t.list,(function(s,o){return[s.billtype==e.typename&&s.accepterid==n.id?i("div",{key:o,staticClass:"items",style:{borderLeft:"5px solid "+t.getBorderColor(s.endplan,s.finishmark)},on:{mouseover:function(e){return t.mouseOver(s,a)},mousedown:function(e){return t.sendMsg(s,s.id)}}},[i("div",{staticClass:"iconStyle"},[i("div",{staticClass:"iconStyle_left"},[i("span",{attrs:{title:s.billtitle}},[t._v(t._s(s.billtitle))])]),i("div",{staticClass:"iconStyle_right"},[i("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"bottom-start"}},[i("i",{staticClass:"el-icon-more"}),i("el-dropdown-menu",{staticClass:"dropdownPop",attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(e){return t.editItem(s,o)}}},[t._v("编辑")]),i("el-dropdown-item",{attrs:{icon:"el-icon-finished"},nativeOn:{click:function(e){return t.finish(s,o)}}},[t._v("完成")]),i("el-dropdown-item",{attrs:{icon:"el-icon-circle-close"},nativeOn:{click:function(e){return t.showDown(s)}}},[t._v("关闭 ")])],1)],1)],1)]),i("div",{staticClass:"dateStyle"},[i("div",{staticClass:"dateStyle_date"},[i("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t._f("dateFormat")(s.endplan))+" ")]),i("div",{staticClass:"dateStyle_public"},[i("span",{attrs:{title:"今天"},on:{click:function(e){return t.changeToday(s)}}},[new Date(s.endplan).getTime()>new Date(t.startDate).getTime()&&new Date(s.endplan).getTime()<new Date(t.endDate).getTime()?i("i",{staticClass:"el-icon-s-platform"}):i("i",{staticClass:"el-icon-monitor"})]),i("span",{attrs:{title:"公共"},on:{click:function(e){return t.changeClick(s,"publicmark")}}},[i("i",{class:s.publicmark?"el-icon-message-solid":"el-icon-bell"})]),i("span",{attrs:{title:"重要 "},on:{click:function(e){return t.changeClick(s,"importantmark")}}},[i("i",{class:s.importantmark?"el-icon-star-on":"el-icon-star-off"})]),i("span",{attrs:{title:"紧急"},on:{click:function(e){return t.changeClick(s,"urgentmark")}}},[i("i",{class:s.urgentmark?"el-icon-warning":"el-icon-warning-outline"})])])])]):t._e()]}))],2)],1)]):t._e()])}))],2)}))],2),i("div",{staticClass:"todoPublic"},[i("div",{staticClass:"todoHead todoPubHead",staticStyle:{"margin-left":"0px"}},[i("div",{staticClass:"todoHead_item"},[t._v("公海")]),i("div",{staticClass:"todoHead_icon"},[i("div",{staticStyle:{"margin-right":"30px"},on:{click:function(e){return t.getprocess()}}},[i("el-badge",{staticClass:"item",attrs:{value:t.parentList.length}},[i("i",{staticClass:"el-icon-edit"})])],1)])]),i("div",{staticClass:"todoPublicBody"},[i("draggable",{staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px"},attrs:{group:"items",tag:"div","data-row":"公海","data-col":"root","data-name":"公海"},on:{end:function(e){return t.dragChange(e,"公海","root","公海")}}},[t._l(t.list,(function(e,a){return["公海"==e.billtype&&"root"==e.accepterid?i("div",{key:a,staticClass:"items",style:{borderLeft:"5px solid "+t.getBorderColor(e.endplan,e.finishmark)},on:{mouseover:function(i){return t.mouseOver(e,0)},mousedown:function(i){return t.sendMsg(e,e.id)}}},[i("div",{staticClass:"iconStyle"},[i("div",{staticClass:"iconStyle_left"},[i("span",{attrs:{title:e.billtitle}},[t._v(t._s(e.billtitle))])]),i("div",{staticClass:"iconStyle_right"},[i("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"bottom-start"}},[i("i",{staticClass:"el-icon-more"}),i("el-dropdown-menu",{staticClass:"dropdownPop",attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(i){return t.editItem(e,a)}}},[t._v("编辑")]),i("el-dropdown-item",{attrs:{icon:"el-icon-delete"},nativeOn:{click:function(i){return t.deleteItem(e)}}},[t._v(" 删除 ")])],1)],1)],1)]),i("div",{staticClass:"dateStyle"},[i("div",{staticClass:"dateStyle_date"},[i("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t._f("dateFormat")(e.endplan))+" ")]),i("div",{staticClass:"dateStyle_public"},[i("span",{attrs:{title:"今天"},on:{click:function(i){return t.changeToday(e)}}},[new Date(e.endplan).getTime()>new Date(t.startDate).getTime()&&new Date(e.endplan).getTime()<new Date(t.endDate).getTime()?i("i",{staticClass:"el-icon-s-platform"}):i("i",{staticClass:"el-icon-monitor"})]),i("span",{attrs:{title:"公共"},on:{click:function(i){return t.changeClick(e,"publicmark")}}},[i("i",{class:e.publicmark?"el-icon-message-solid":"el-icon-bell"})]),i("span",{attrs:{title:"重要"},on:{click:function(i){return t.changeClick(e,"importantmark")}}},[i("i",{class:e.importantmark?"el-icon-star-on":"el-icon-star-off"})]),i("span",{attrs:{title:"紧急"},on:{click:function(i){return t.changeClick(e,"urgentmark")}}},[i("i",{class:e.urgentmark?"el-icon-warning":"el-icon-warning-outline"})])])])]):t._e()]}))],2)],1),i("div",{staticClass:"todoPublicFoot"},[i("el-popover",{ref:"addpopover",attrs:{placement:"top",trigger:"click",width:"400",title:"新增"}},[i("div",{staticStyle:{padding:"15px"}},[i("el-form",{ref:"todoform",attrs:{model:t.todoform,"label-width":"70px",rules:t.todoformRules}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入标题"},model:{value:t.todoform.billtitle,callback:function(e){t.$set(t.todoform,"billtitle",e)},expression:"todoform.billtitle"}})],1)],1),i("div",{on:{click:function(e){return t.cleValidate("endplan")}}},[i("el-form-item",{attrs:{label:"计划完成",prop:"endplan"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",clearable:"",placeholder:"时间"},model:{value:t.todoform.endplan,callback:function(e){t.$set(t.todoform,"endplan",e)},expression:"todoform.endplan"}})],1)],1),i("div",{staticStyle:{"text-align":"right"}},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.todosave()}}},[t._v("确 定")])],1)])],1),i("div",{staticClass:"addPlus",attrs:{slot:"reference"},on:{click:function(e){return t.showform(0)}},slot:"reference"},[i("i",{staticClass:"el-icon-plus"})])])],1)])])]),i("el-col",{attrs:{span:t.showHelp?4:0}},[i("helpmodel",{ref:"helpmodel",attrs:{code:"S06M09R1"}})],1)],1),t.finishVisible?i("el-dialog",{attrs:{width:"400px",title:"完成描述","append-to-body":!0,visible:t.finishVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.finishVisible=e}}},[i("div",{staticStyle:{padding:"15px"}},[i("el-form",{ref:"finishform",attrs:{model:t.finishform,"label-width":"70px",rules:t.finishformRules}},[i("el-form-item",{attrs:{label:"完工工时",prop:"finishhours"}},[i("el-input-number",{attrs:{min:0,step:.1,size:"small","controls-position":"right"},model:{value:t.finishform.finishhours,callback:function(e){t.$set(t.finishform,"finishhours",e)},expression:"finishform.finishhours"}}),i("span",{staticStyle:{"margin-left":"10px","font-weight":"bold"}},[t._v("H")])],1),i("div",{on:{click:function(e){return t.finishValidate("endremark")}}},[i("el-form-item",{attrs:{label:"完工描述"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",rows:3,placeholder:"请输入完成描述"},model:{value:t.finishform.endremark,callback:function(e){t.$set(t.finishform,"endremark",e)},expression:"finishform.endremark"}})],1)],1),i("div",{staticStyle:{"text-align":"right"}},[i("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.finishUpdate()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.finishVisible=!1}}},[t._v("取 消")])],1)],1)],1)]):t._e(),t.formeditVisible?i("el-dialog",{attrs:{width:"800px",title:"Todo","append-to-body":!0,visible:t.formeditVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.formeditVisible=e}}},[i("formedit",{ref:"formedit",attrs:{idx:t.idx}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.formeditSave()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.formeditVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},n=[],s=i("c7eb"),o=i("1da1"),r=(i("99af"),i("c740"),i("b0c0"),i("e9c4"),i("d3b7"),i("25f0"),i("3ca3"),i("4d90"),i("0643"),i("4e3e"),i("159b"),i("ddb0"),i("b76a")),l=i.n(r),c=i("b775"),d=i("7e12"),u=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[i("div",{staticStyle:{display:"flex"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),i("div",{staticClass:"iShowBtn",staticStyle:{display:"flex"}},[i("quickTick",{ref:"quickTick",attrs:{droupList:t.droupList},on:{quickTickBtn:function(e){return t.$emit("quickTickBtn",e)}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",title:"帮助",icon:"el-icon-s-help"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)])])},f=[],m=i("e82a"),p={name:"Listheader",props:["tableForm","userList"],components:{quickTick:m["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},droupList:[],thorList:!0,content:[{color:"#ff9800",value:"明天待完成"},{color:"#2196f3",value:"今天待完成"},{color:"#8bc34a",value:"正常完成"},{color:"#f44336",value:"逾期未完成"},{color:"#ffeb3b",value:"逾期完成"}]}},mounted:function(){},methods:{btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},changeList:function(t){this.droupList=[].concat(t)}}},h=p,b=(i("b9c6"),i("2877")),g=Object(b["a"])(h,u,f,!1,null,"76f2271e",null),v=g.exports,y=i("0521"),w=i("b893"),k={components:{draggable:l.a,listheader:v,helpmodel:y["a"],formedit:d["a"]},data:function(){return{list:[],userList:[],typeList:[],idx:0,formType:0,currentId:0,todoform:{billtitle:"",endplan:new Date},todoformRules:{billtitle:[{required:!0,trigger:"blur",message:"标题为必填项"}]},finishVisible:!1,finishform:{id:"",endremark:"",finishhours:0},finishformRules:{endremark:[{required:!0,trigger:"blur",message:"完成描述为必填项"}]},showHelp:!1,queryParams:{PageNum:1,PageSize:1e4,OrderType:0,SearchType:1},formeditVisible:!1,startDate:Object(w["b"])(new Date),endDate:Object(w["a"])(new Date),myself:0,parentList:[]}},created:function(){this.bindData(),this.getType()},methods:{bindData:function(){var t=this;c["a"].post("/S06M09B1/getOnlinePageList?type=1",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.list=e.data.data.list,t.getUserList(),t.getparentList(),t.$forceUpdate()),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getUserList:function(){var t=this,e={PageNum:1,PageSize:200,OrderType:0,SearchType:1},i="/S06M09B1User/getPageList";c["a"].post(i,JSON.stringify(e)).then((function(e){if(200==e.data.code){t.userList=e.data.data.list;for(var i=0;i<t.userList.length;i++){t.userList[i];t.userList[i].show=!0,t.userList[i].taskNum=0;for(var a=0;a<t.list.length;a++)t.userList[i].id==t.list[a].accepterid&&(t.userList[i].taskNum+=1)}t.$forceUpdate(),t.$refs.listheader.changeList(t.userList)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getparentList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1,scenedata:[{field:"Sa_Demand.todoid",fieldtype:0,math:"equal",value:""}]};c["a"].post("/S06M02B1/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.parentList=e.data.data.list)})).catch((function(t){}))},quickTickBtn:function(t){var e=this.userList.findIndex((function(e){return e.id==t.id}));-1!=e&&(this.$set(this.userList[e],"show",t.show),this.$refs.listheader.changeList(this.userList)),this.$forceUpdate()},getDicist:function(){var t=this;c["a"].get("/SaDict/getBillEntityByDictCode?key=sa_todo.billtype").then((function(e){if(200==e.data.code){var i=["#7EC5FF","#409eff","#C280FF","#95C804","#FFB45C"],a=e.data.data.item;a.forEach((function(t,e){t.backTypeColor=i[e]})),t.typeList=a}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getType:function(){var t=this,e={PageNum:1,PageSize:100,OrderType:0,SearchType:1};c["a"].post("/S06M09B1Type/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.typeList=e.data.data.list),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},dragChange:function(t){var e=this,i=t.to.dataset.row,a=t.to.dataset.col,n=t.to.dataset.name;this.list.forEach((function(t){t.id===e.currentId&&(t.billtype=i,t.accepterid=a,t.accepter=n,e.favemove(t))})),this.$forceUpdate()},sendMsg:function(t,e){this.currentId=e},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},showform:function(t){this.todoform.billtype="公海",this.todoform.accepterid="root"},todosave:function(){var t=this;c["a"].post("/S06M09B1/create",JSON.stringify(this.todoform)).then((function(e){200==e.data.code&&(t.todoform.billtitle="",t.list.push(e.data.data),t.$message.success("新增成功"),t.$refs.addpopover.doClose())})).catch((function(t){}))},btnadd:function(){var t=this;this.formType=0,setTimeout((function(){t.formeditVisible=!0}),100)},editItem:function(t,e){var i=this;this.idx=t.id,this.formType=1,this.formeditVisible=!0,setTimeout((function(){i.$refs.formedit.bindData()}),100)},formeditSave:function(){var t=this,e="/S06M09B1/create";this.formType&&(e="/S06M09B1/update");var i=Object.assign({},this.$refs.formedit.formdata);i.finishhours=this.finishform.finishhours,i.endremark=this.finishform.endremark,c["a"].post(e,JSON.stringify(i)).then((function(e){200==e.data.code&&(t.$message.success("新增成功"),t.formeditVisible=!1,t.bindData())})).catch((function(t){}))},favemove:function(t){var e=this,i={id:t.id,accepterid:t.accepterid,accepter:t.accepter,billtype:t.billtype};c["a"].post("/S06M09B1/update",JSON.stringify(i)).then((function(t){200==t.data.code&&(e.bindData(),e.$forceUpdate())})).catch((function(t){}))},cleValidate:function(t){this.$refs.todoform.clearValidate(t)},mouseOver:function(t,e){},finishValidate:function(t){this.$refs.finishform.clearValidate(t)},finish:function(t,e){this.finishform.id=t.id,this.finishform.endremark="",this.finishform.finishmark=1,this.finishVisible=!0},finishUpdate:function(){var t=this;c["a"].post("/S06M09B1/update",JSON.stringify(this.finishform)).then((function(e){t.finishVisible=!1,200==e.data.code&&(t.$message.success(e.data.data.billtitle+"完成"),t.bindData(),t.finishVisible=!1)})).catch((function(t){}))},changeClick:function(t,e){t[e]=t[e]?0:1,c["a"].post("/S06M09B1/update",JSON.stringify(t)).then((function(e){200==e.data.code&&(t=e.data.data)})).catch((function(t){}))},search:function(t){console.log("res",t),""!=t?this.queryParams.SearchPojo={billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},showDown:function(t){var e=this;this.$confirm("是否确认关闭?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.reqClosed(t)}))},reqClosed:function(t){var e=this,i={id:t.id,closed:1};c["a"].post("/S06M09B1/update",JSON.stringify(i)).then((function(t){200==t.data.code&&(e.$message.success(t.data.data.billtitle+"已关闭"),e.bindData())})).catch((function(t){}))},deleteItem:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){c["a"].get("/S06M09B1/delete?key="+t.id).then((function(t){200==t.data.code?(e.$message.success("删除成功"),e.bindData()):e.$message.warning(t.data.msg||"删除失败")}))}))},getBorderColor:function(t,e){if(!t)return"#9e9e9e";var i="",a=new Date(t).setHours(23,59,59,0),n=(new Date).getTime();return i=a-n<0?e?"#ffeb3b":"#f44336":a-n<864e5?e?"#8bc34a":"#2196f3":a-n>864e5&&a-n<1728e5?"#ff9800":"#9e9e9e",i},changeToday:function(t){var e=this,i=new Date;if(new Date(t.endplan).getTime()>new Date(this.startDate).getTime()&&new Date(t.endplan).getTime()<new Date(this.endDate).getTime())var a={id:t.id,endplan:new Date(i.setDate(i.getDate()+1))};else a={id:t.id,endplan:i};c["a"].post("/S06M09B1/update",JSON.stringify(a)).then((function(t){200==t.data.code&&e.bindData()})).catch((function(t){}))},getprocess:function(){var t=this;return Object(o["a"])(Object(s["a"])().mark((function e(){var i,a,n;return Object(s["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(t,i=[],a=0;a<t.parentList.length;a++)n=new Promise((function(e,i){var n=t.parentList[a],s={parentid:n.id,billtype:"公海",accepterid:"root",billtitle:n.billtitle,publicmark:1};c["a"].post("/S06M09B1/create",JSON.stringify(s)).then((function(t){200==t.data.code?e(t.data):i(t.data.msg||"新增错误")}))})),i.push(n);return e.next=5,Promise.all(i).then((function(e){t.$message.success("导入成功"),t.bindData()}));case 5:case"end":return e.stop()}}),e)})))()}},filters:{dateFormat:function(t){if(t){var e=new Date(t),i=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),a=e.getDate().toString().padStart(2,"0");return"".concat(i,"-").concat(a)}}}},S=k,x=(i("e5a0"),Object(b["a"])(S,a,n,!1,null,"3d8e727f",null));e["default"]=x.exports},e5a0:function(t,e,i){"use strict";i("6324")}}]);