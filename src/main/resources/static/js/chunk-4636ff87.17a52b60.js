(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4636ff87","chunk-183a0062","chunk-664b1b42"],{"01ede":function(t,e,a){},"08db":function(t,e,a){"use strict";a("330d")},"093b":function(t,e,a){},"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function s(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function o(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(t,e,a){var n=o(),l=t-n,r=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=r;var t=Math.easeInOutQuad(c,n,l,e);s(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"0a25":function(t,e,a){"use strict";a.d(e,"c",(function(){return i})),a.d(e,"b",(function(){return s})),a.d(e,"a",(function(){return o}));var i={formcode:"S06M28B1Th",item:[{itemcode:"refno",itemname:"编码",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center"},{itemcode:"billtitle",itemname:"会议标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billtitle"},{itemcode:"billtype",itemname:"类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billtype"},{itemcode:"personnel",itemname:"参与者",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_Feedback.personnel"},{itemcode:"billdate",itemname:"日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billdate"},{itemcode:"projectname",itemname:"项目名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"location",itemname:"地点",minwidth:"80",displaymark:1,overflow:1},{itemcode:"finishcount",itemname:"状态",minwidth:"80",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_Feedback.lister"},{itemcode:"createdate",itemname:"创建日期",minwidth:"70",displaymark:1,sortable:1,overflow:1,datasheet:"Pms_ProPoint.createdate"}]},s={formcode:"S06M28B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center"},{itemcode:"billtitle",itemname:"会议标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billtitle"},{itemcode:"billtype",itemname:"类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billtype"},{itemcode:"billdate",itemname:"日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billdate"},{itemcode:"projectname",itemname:"项目名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"groupname",itemname:"客户名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"location",itemname:"地点",minwidth:"80",displaymark:1,overflow:1},{itemcode:"story",itemname:"反馈内容",minwidth:"120",displaymark:1,overflow:1},{itemcode:"opinion",itemname:"处理意见",minwidth:"120",displaymark:1,overflow:1},{itemcode:"finishmark",itemname:"完工",minwidth:"100",displaymark:1,overflow:1},{itemcode:"submitmark",itemname:"是否提报",minwidth:"100",defwidth:"",displaymark:1,overflow:1,aligntype:"center"}]},o={formcode:"S06M28B1Item",item:[{itemcode:"itemtype",itemname:"类型",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"story",itemname:"反馈内容",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"opinion",itemname:"处理意见",minwidth:"150",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"exponent",itemname:"指数",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"finishmark",itemname:"完工",minwidth:"50",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center"},{itemcode:"submitmark",itemname:"是否提报",minwidth:"100",defwidth:"",displaymark:1,overflow:1,aligntype:"center"}]}},"0bf6":function(t,e,a){},"0dd7":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"flex"},[a("div",{staticClass:"row-item"},[a("div",{staticClass:"cardSty",staticStyle:{height:"calc(32% - 10px)"}},[a("div",{staticClass:"cardTitle sky_blue flex j-s",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("反馈单")]),a("i",{staticClass:"el-icon-refresh-right",on:{click:function(e){return t.$refs.S06M28B1.bindData()}}})]),a("div",{staticStyle:{height:"calc(100% - 52px)"}},[a("S06M28B1",{ref:"S06M28B1"})],1)]),a("div",{staticClass:"cardSty",staticStyle:{height:"calc(32% - 10px)"}},[a("div",{staticClass:"cardTitle orange_red flex j-s",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("缺陷报告")]),a("i",{staticClass:"el-icon-refresh-right",on:{click:function(e){return t.$refs.S06M20B1.bindData()}}})]),a("div",{staticStyle:{height:"calc(100% - 52px)"}},[a("S06M20B1",{ref:"S06M20B1"})],1)]),a("div",{staticClass:"cardSty",staticStyle:{height:"calc(32% - 0px)"}},[a("div",{staticClass:"cardTitle bg-red flex j-s",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("客户提报")]),a("i",{staticClass:"el-icon-refresh-right"})])])]),a("div",{staticClass:"row-item"},[a("div",{staticClass:"cardSty"},[a("div",{staticClass:"cardTitle bg-qing flex j-s",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("需求提报")]),a("div",[a("i",{staticClass:"el-icon-refresh-right",on:{click:function(e){return t.$refs.S06M02B3.bindData()}}})])]),a("div",{staticStyle:{height:"calc(100% - 52px)"}},[a("S06M02B3",{ref:"S06M02B3",attrs:{engineerData:t.engineerData,online:t.online}})],1)])]),a("div",{staticClass:"row-item"},[a("div",{staticClass:"cardSty"},[a("div",{staticClass:"cardTitle bg-purple flex j-s",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("需求平台")]),a("i",{staticClass:"el-icon-refresh-right",on:{click:function(e){return t.$refs.S06M02B4.bindData()}}})]),a("div",{staticStyle:{height:"calc(100% - 52px)"}},[a("S06M02B4",{ref:"S06M02B4",attrs:{engineerData:t.engineerData,statusList:t.statusList,projectAllList:t.projectAllList,projectAllListCopy:t.projectAllListCopy}})],1)])]),a("div",{staticClass:"row-item"},[a("div",{staticClass:"cardSty"},[a("div",{staticClass:"cardTitle shen_green flex j-s",attrs:{slot:"header"},slot:"header"},[a("span",[t._v("今日完成")]),a("div",[a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{size:"mini"},on:{change:t.changeIsmy},model:{value:t.ismy,callback:function(e){t.ismy=e},expression:"ismy"}},[t._v("我的")]),a("i",{staticClass:"el-icon-refresh-right",on:{click:function(e){return t.$refs.S06M02B5.bindData()}}})],1)]),a("div",{staticStyle:{height:"calc(100% - 52px)"}},[a("S06M02B4",{ref:"S06M02B5",attrs:{finishmark:1,ismy:t.ismy,engineerData:t.engineerData,statusList:t.statusList,projectAllList:t.projectAllList,projectAllListCopy:t.projectAllListCopy}})],1)])])])},s=[],o=(a("99af"),a("e9c4"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"compageSty"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selPwProcess",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[a("el-table-column",{attrs:{label:"ID",width:"40",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"标题",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.showform(e.row.id)}}},[t._v(t._s(e.row.billtitle?e.row.billtitle:"标题"))])]}}])}),a("el-table-column",{attrs:{label:"客户",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.customer))])]}}])}),a("el-table-column",{attrs:{label:"项目",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.projectname))])]}}])}),a("el-table-column",{attrs:{label:"状态",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.finishcount==e.row.itemcount&&e.row.finishcount?a("el-tag",{attrs:{size:"small",type:"success"}},[t._v("完成")]):a("el-tag",{attrs:{size:"small"}},[t._v("未完成")])]}}])})],1),t.formVisible?a("el-dialog",{attrs:{"append-to-body":!0,visible:t.formVisible,width:"90vw",top:"1vh","show-close":!1,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.formVisible=e}}},[a("FormEdit",{ref:"formedit",attrs:{idx:t.idx},on:{bindData:t.bindData,closeForm:function(e){t.formVisible=!1}}})],1):t._e()],1)}),n=[],l=a("b775"),r=a("3be7"),c={props:[],components:{FormEdit:r["a"]},data:function(){return{title:"反馈单",listLoading:!0,lst:[],idx:0,total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},formVisible:!1}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,l["a"].post("/S06M28B1/getPageTh",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},showform:function(t){this.idx=t,this.formVisible=!0}}},d=c,m=(a("08db"),a("2877")),p=Object(m["a"])(d,o,n,!1,null,"4b77a885",null),f=p.exports,u=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"compageSty"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selPwProcess",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[a("el-table-column",{attrs:{label:"ID",width:"40",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),a("el-table-column",{attrs:{label:"软件",align:"center","min-width":"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.softwarename))])]}}])}),a("el-table-column",{attrs:{label:"版本",align:"center",width:"80","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.showform(e.row.id)}}},[t._v(t._s(e.row.softwareversion?e.row.softwareversion:"标题"))])]}}])}),a("el-table-column",{attrs:{label:"缺陷描述",align:"center","min-width":"120","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.description))])]}}])}),a("el-table-column",{attrs:{label:"程度",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.severity?a("el-tag",{attrs:{size:"small",type:"warning"}},[t._v("一般")]):2==e.row.severity?a("el-tag",{attrs:{size:"small",type:"danger"}},[t._v("严重")]):a("el-tag",{attrs:{size:"small"}},[t._v("轻微")])]}}])})],1),t.formVisible?a("el-dialog",{attrs:{"append-to-body":!0,visible:t.formVisible,width:"90vw",top:"1vh","show-close":!1,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.formVisible=e}}},[a("FormEdit",{ref:"formedit",attrs:{idx:t.idx},on:{bindData:t.bindData,closeForm:function(e){t.formVisible=!1}}})],1):t._e()],1)},h=[],g=a("eadf"),v={props:[],components:{FormEdit:g["a"]},data:function(){return{title:"缺陷报告",listLoading:!0,lst:[],idx:0,total:0,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},formVisible:!1}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,l["a"].post("/S06M20B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},showform:function(t){this.idx=t,this.formVisible=!0}}},b=v,y=(a("e7b5"),Object(m["a"])(b,u,h,!1,null,"39b8dd30",null)),w=y.exports,S=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"compageSty"},[t._l(t.list,(function(e,i){return a("div",{key:i,staticClass:"todoList-li flex j-s"},[a("div",{staticClass:"flex j-s",staticStyle:{cursor:"default","flex-wrap":"nowrap",width:"100%"}},[a("div",{staticStyle:{display:"inline-block",flex:"1"}},[a("div",{staticClass:"descriptionSty",attrs:{title:e.description},on:{click:function(a){return t.openEdit(e)}}},[t._v(" "+t._s(e.description)+" ")]),a("div",{staticStyle:{"font-size":"12px","margin-top":"10px"}},[0==e.level?a("span",{staticClass:"levelTag"},[t._v("较低")]):t._e(),1==e.level?a("span",{staticClass:"levelTag tagblue"},[t._v("普通")]):t._e(),2==e.level?a("span",{staticClass:"levelTag tagorange"},[t._v("紧急")]):t._e(),3==e.level?a("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")]):t._e(),e.assigneename?a("span",{staticClass:"el-icon-user"},[t._v(" "+t._s(e.assigneename))]):t._e()])])]),a("div",{staticStyle:{cursor:"default","font-weight":"bold",width:"90px","text-align":"right"}},[a("el-tooltip",{attrs:{effect:"dark",placement:"right"}},[a("div",{staticStyle:{"line-height":"18px"},attrs:{slot:"content"},slot:"content"},[t._v(" 类型："+t._s(e.type)),a("br"),t._v(" 客户："+t._s(e.groupname)),a("br"),t._v(" 执行者："+t._s(e.assigneename)),a("br"),t._v(" 协作者："+t._s(e.participantnames)),a("br"),t._v(" 截止时间："+t._s(t._f("dateFormat")(e.deadlinedate))),a("br"),t._v(" 提报时间："+t._s(e.createdate)),a("br"),t._v(" 备注："+t._s(e.remark)+" ")]),a("span",{class:t.getStatusClass(e.status),staticStyle:{display:"block"}},[t._v(t._s(e.status))])])],1)])})),t.formVisible?a("el-dialog",{attrs:{title:"需求提报","append-to-body":!0,visible:t.formVisible,width:"700px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.formVisible=e}}},[a("FormEdit",{ref:"formedit",attrs:{idx:t.idx,engineerData:t.engineerData},on:{bindData:t.bindData,closeDialog:function(e){t.formVisible=!1}}})],1):t._e()],2)},x=[],_=(a("c740"),a("ac1f"),a("5319"),a("8a0f")),k={props:["engineerData","projectRow","online"],components:{FormEdit:_["default"]},data:function(){return{title:"需求提报",list:[],idx:0,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},formVisible:!1}},methods:{bindData:function(){var t=this,e="/S06M02B3/getPageList";this.online&&(e="/S06M02B3/getOnlinePageList"),this.$request.post(e,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.list=e.data.data.list,t.total=e.data.data.total;for(var a=0;a<t.list.length;a++){var i=t.list[a];i.assessorid&&i.demandmark?t.$set(i,"status","已转需求"):i.assessorid&&!i.demandmark?t.$set(i,"status","已审待转"):i.assessorid||!i.oaflowmark||i.demandmark||t.$set(i,"status","OA审批中"),t.$set(i,"assigneename",t.getAssigneeName(i.assignee)),t.$set(i,"participantnames",t.getParticipantNames(i.participants))}}})).catch((function(t){}))},openEdit:function(t){var e=this;this.idx=t.id,this.formVisible=!0,this.$nextTick((function(){e.$refs.formedit.bindData()}))},getParticipantNames:function(t){var e="";if(t){for(var a=t.split(","),i=0;i<a.length;i++){var s=a[i],o=this.engineerData.findIndex((function(t){return t.id==s}));-1!=o&&(e+=this.engineerData[o].engineername+",")}var n=/,$/gi;e=e.replace(n,"")}return e},getStatusClass:function(t){var e="";return"已审待转"==t?e="tagorange":"已转需求"==t?e="taggreen":"OA审批中"==t?e="tagblue":"已废弃"==t&&(e="taggrey"),e},getAssigneeName:function(t){var e="";if(t){var a=this.engineerData.findIndex((function(e){return e.id==t}));-1!=a&&(e=this.engineerData[a].engineername)}return e},getlevelStyle:function(t){var e="";return 0==t?e="":1==t?e="border:1px solid #65a6ff":2==t?e="border:1px solid #fa7753":3==t&&(e="border:1px solid rgb(230, 36, 18)"),e}}},$=k,C=(a("66fca"),Object(m["a"])($,S,x,!1,null,"783eea40",null)),j=C.exports,D=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"compageSty"},[t._l(t.list,(function(e,i){return a("div",{key:i,staticClass:"todoList-li flex j-s"},[a("div",{staticClass:"flex",staticStyle:{cursor:"default",width:"100%"}},[a("div",{staticStyle:{display:"inline-block",width:"100%"}},[a("div",{staticClass:"descriptionSty",attrs:{title:e.billtitle},on:{click:function(a){return t.openFormedit(e)}}},[t._v(" "+t._s(e.billtitle)+" ")]),a("div",{staticClass:"flex j-s a-c",staticStyle:{"font-size":"14px","margin-top":"10px"}},[a("div",[a("el-tag",{staticStyle:{"margin-right":"10px"},attrs:{type:"缺陷"==e.billtype?"warning":"primary",size:"mini"}},[t._v(t._s(e.billtype))]),e.appointee?a("span",{staticClass:"el-icon-user"},[t._v(" "+t._s(e.appointee))]):t._e()],1),a("div",[a("el-tooltip",{attrs:{effect:"dark",placement:"right"}},[a("div",{staticStyle:{"line-height":"18px"},attrs:{slot:"content"},slot:"content"},[t._v(" 任务描述："+t._s(e.remark)),a("br"),t._v(" 类型："+t._s(e.billtype)),a("br"),t._v(" 状态："+t._s(t.getStatusName(e.demandstatus))),a("br"),t._v(" 执行者："+t._s(e.appointee||"-")),a("br"),t._v(" 协作者："+t._s(e.collaborators||"-")),a("br"),t._v(" 开始时间："+t._s(t._f("dateFormat")(e.startdate))),a("br"),t._v(" 截止时间："+t._s(t._f("dateFormat")(e.deaddate))),a("br")]),a("div",[a("span",{staticClass:"timestatusSty",style:t.getitemStatus(e.timestatus,e)},[t._v(" "+t._s(e.timestatus))]),0==e.level?a("span",{staticClass:"levelTag"},[t._v("较低")]):t._e(),1==e.level?a("span",{staticClass:"levelTag tagblue"},[t._v("普通")]):t._e(),2==e.level?a("span",{staticClass:"levelTag tagorange"},[t._v("紧急")]):t._e(),3==e.level?a("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")]):t._e()])])],1)])])])])})),t.formeditVisible?a("el-dialog",{key:t.formeditKey,attrs:{width:"56vw",title:"任务","append-to-body":!0,visible:t.formeditVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"6vh"},on:{"update:visible":function(e){t.formeditVisible=e},close:t.bindData}},[a("div",{staticClass:"flex",staticStyle:{"justify-content":"space-between"},attrs:{slot:"title"},slot:"title"},[a("div",{staticClass:"flex"},[a("span",[t._v("任务 -")]),a("el-popover",{ref:"timestatusRef",attrs:{placement:"bottom-start",trigger:"click","popper-class":""}},[a("div",[t.selForm.timestatus&&"暂停"!=t.selForm.timestatus?"开始"==t.selForm.timestatus?a("div",[a("el-button",{staticStyle:{color:"#fa8c15"},attrs:{type:"text",icon:"el-icon-video-pause"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"暂停")}}},[t._v(" 暂停 ")])],1):t._e():a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-video-play"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"开始")}}},[t._v(" 开始 ")])],1),"开始"==t.selForm.timestatus?a("div",[a("el-button",{staticStyle:{color:"#14a10f"},attrs:{type:"text",icon:"el-icon-finished"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"完成")}}},[t._v(" 完成 ")])],1):t._e()]),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("span",{staticStyle:{"text-decoration":"underline",cursor:"pointer","margin-left":"4px"}},[t._v(t._s(t.selForm.timestatus?t.selForm.timestatus:"未开始"))])])])],1),a("div",{staticClass:"flex a-c",staticStyle:{"margin-right":"20px",cursor:"pointer"}},[a("el-dropdown",{staticStyle:{margin:"0px 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.$refs.formedit.bindData()}}},[t._v("刷 新")]),a("el-dropdown-item",{attrs:{disabled:2!=t.$store.state.user.userinfo.isadmin&&(t.$store.state.user.userinfo.engineer.id!=t.selForm.appointeeid&&t.$store.state.user.userinfo.userid!=t.selForm.createbyid),icon:"el-icon-delete"},nativeOn:{click:function(e){return t.deleteForm()}}},[t._v("删 除 ")])],1)],1)],1)]),a("FormEdit",{ref:"formedit",attrs:{idx:t.selForm.id,userList:t.engineerData,statusList:t.statusList,billtypeArr:t.billtypeArr},on:{closeDialog:function(e){t.formeditVisible=!1},bindData:function(e){return t.bindData(t.project)}}})],1):t._e()],2)},L=[],M=a("a116"),T=a("b893"),B={props:["engineerData","statusList","projectAllListCopy","projectAllList","finishmark","ismy"],components:{FormEdit:M["a"]},data:function(){return{title:"需求提报",list:[],idx:0,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},formeditVisible:!1,selForm:{},formeditKey:0,billtypeArr:["需求","任务","测试","缺陷","实施"]}},methods:{bindData:function(){var t=this,e="/S06M02B2/getOnlinePageList?own=1";this.finishmark&&(e="/S06M02B2/getPageListFinish?own=".concat(this.ismy?1:0),this.queryParams.scenedata=[{field:"Sa_Demand.finishdate",fieldtype:2,math:"between",value:Object(T["b"])(new Date),valueb:Object(T["a"])(new Date)}]),this.$request.post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.list=e.data.data.list,t.total=e.data.data.total)})).catch((function(t){}))},openFormedit:function(t){var e=this;this.selForm=Object.assign({},t),this.formeditVisible=!0,this.$nextTick((function(){e.$refs.formedit.projectAllList=e.projectAllList,e.$refs.formedit.projectAllListCopy=e.projectAllListCopy}))},changeTimeStatus:function(t,e){var a=this;"完成"==e?this.$confirm("是否确定完成该任务，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.updateFun(t,e)})).catch((function(){})):this.updateFun(t,e)},updateFun:function(t,e){var a=this,i="/S06M02S7/start";"暂停"==e?i="/S06M02S7/pause":"完成"==e&&(i="/S06M02S7/complete"),this.$request.get(i+"?key="+t.id).then((function(t){200==t.data.code?(a.$message.success(t.data.msg||"操作成功，该任务已"+e),a.$set(a.selForm,"timestatus",e),a.bindData(),a.formeditKey+=1):a.$message.warning(t.data.msg||"操作失败")}))},getStatusName:function(t){var e="待处理";if(t){var a=this.statusList.findIndex((function(e){return e.id==t}));-1!=a&&(e=this.statusList[a].statusname)}return e},getitemStatus:function(t,e){var a={color:"#000",padding:"2px 6px","border-radius":"4px","margin-right":"10px"};return"开始"==t?(a.color="#409EFF",a.border="1px solid"):"暂停"==t?(a.color="#fa8c15",a.border="1px solid"):"完成"==t&&(a.color="#14a10f",a.border="1px solid"),a}}},F=B,O=(a("4c20"),Object(m["a"])(F,D,L,!1,null,"5870e43a",null)),P=O.exports,V={components:{S06M28B1:f,S06M20B1:w,S06M02B3:j,S06M02B4:P},data:function(){return{engineerData:[],statusList:[],projectAllList:[],projectAllListCopy:[],online:!1,ismy:!0}},created:function(){this.getInfoList()},mounted:function(){this.$refs.S06M02B3.bindData(),this.$refs.S06M02B4.bindData(),this.$refs.S06M02B5.bindData(),this.getprojectAllList()},methods:{getInfoList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1,OrderBy:"rownum"};this.$request.post("/S06M02S3/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.statusList=[].concat(e.data.data.list))})),this.engineerData=[],this.$request.post("/S06M02S2/getOnlinePageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.engineerData=[].concat(e.data.data.list))}))},getprojectAllList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};this.$request.post("/S06M01S1/getBillList?own=true",JSON.stringify(e)).then((function(e){200==e.data.code?(t.projectAllList=e.data.data.list,t.projectAllListCopy=[].concat(t.projectAllList)):t.$message.warning(e.data.msg||"查询项目信息失败")}))},changeIsmy:function(t){var e=this;this.ismy=t,this.$nextTick((function(){e.$refs.S06M02B5.bindData()}))},changeOnline:function(t){var e=this;this.online=t,this.$nextTick((function(){e.$refs.S06M02B3.bindData()}))}}},N=V,z=(a("bb4d"),Object(m["a"])(N,i,s,!1,null,"22527f14",null));e["default"]=z.exports},"1b2f":function(t,e,a){},2070:function(t,e,a){},"330d":function(t,e,a){},"3be7":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!t.formdata.assessor},on:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[t.formdata.assessor?a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.deApproval()}}},[t._v(" 反审核")]):a("el-button",{attrs:{size:"small",type:"primary",disabled:!t.formdata.id},on:{click:function(e){return t.approval()}}},[t._v(" 审 核")])],1),a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.$refs.PrintServer.printButton(0,1)}}},[t._v("打 印")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.idx},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")]),a("el-dropdown-item",{attrs:{divided:"",icon:"el-icon-edit-outline",disabled:!t.formdata.assessor},nativeOn:{click:function(e){return t.openS06M02B3()}}},[t._v("需求提报")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r",staticStyle:{height:"96%"}},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:""},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),a("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[a("div",{staticStyle:{"margin-right":"10px",width:"100px"}},[t.formdata.finishcount>0&&t.formdata.finishcount>=t.formdata.itemcount?a("svg-icon",{staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"finish1"}}):t._e()],1),a("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),a("el-form",{ref:"formdata",staticStyle:{height:"99%"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"会议标题",prop:"billtitle"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入会议标题",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)],1),a("div",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"会议类型"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入会议类型",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"日期"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",clearable:"",placeholder:"时间",size:"small"},model:{value:t.formdata.createdate,callback:function(e){t.$set(t.formdata,"createdate",e)},expression:"formdata.createdate"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"会议地点"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入会议地点",size:"small"},model:{value:t.formdata.location,callback:function(e){t.$set(t.formdata,"location",e)},expression:"formdata.location"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"项目名称",prop:"projectname"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",size:"small"},on:{change:t.changeProject},model:{value:t.formdata.projectname,callback:function(e){t.$set(t.formdata,"projectname",e)},expression:"formdata.projectname"}},t._l(t.projectData,(function(t){return a("el-option",{key:t.value,attrs:{label:t.projname,value:t.id}})})),1)],1)],1),a("el-col",{attrs:{span:4}},[a("div",{ref:"colRefs",on:{click:function(e){return t.cleValidate("customer")}}},[a("el-form-item",{attrs:{label:"客户",prop:"customer"}},[a("inksAutoComplete",{attrs:{size:"small",value:t.formdata.groupname,baseurl:"/S06M14B1/getPageList",params:{name:"groupname",other:"groupuid"}},on:{setRow:function(e){t.formdata.groupid=e.id,t.formdata.groupname=e.groupname,t.formdata.customer=e.groupname,t.formdata.groupuid=e.groupuid},autoClear:function(e){t.formdata.groupid="",t.formdata.groupname="",t.formdata.customer="",t.formdata.groupuid=""}}})],1)],1)]),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"参会人员"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,placeholder:"请选择人员",size:"small"},on:{change:t.changePersonnel},model:{value:t.personnelList,callback:function(e){t.personnelList=e},expression:"personnelList"}},t._l(t.engineerData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.engineername,value:t.id}})})),1)],1)],1)],1),a("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[a("EditItem",{ref:"elitem",style:{width:"99%",height:"100%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData}}),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.assessor))])])],1),a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.formdata.assessor,expression:"formdata.assessor"}],attrs:{span:4}},[a("el-form-item",{attrs:{label:"审核日期"}},[a("span",{staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.assessdate)))])])],1)],1)],1)],1)],1)],1)])]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"S06M28B1Edit",weburl:"/S06M28B1/printWebBill"}}),t.operationVisible?a("el-dialog",{staticClass:"operationDialog",attrs:{width:"700px",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,"show-close":!1,title:"需求提报"},on:{"update:visible":function(e){t.operationVisible=e}}}):t._e(),t.processVisible?a("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"2vh"},on:{"update:visible":function(e){t.processVisible=e}}},["S06M02B3"==t.processModel?a("S06M02B3List",{ref:"S06M02B3List",attrs:{searchVal:t.formdata.refno,isDialog:!0}}):t._e()],1):t._e()],1)},s=[],o=a("c7eb"),n=a("1da1"),l=(a("99af"),a("a15b"),a("e9c4"),a("b64b"),a("d3b7"),a("25f0"),a("3ca3"),a("4d90"),a("0643"),a("4e3e"),a("159b"),a("ddb0"),a("b775"));const r={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);l["a"].post("/S06M28B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);l["a"].post("/S06M28B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{l["a"].get("/S06M28B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var c=r,d=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{ref:"elitem",staticClass:"flex f-d-c",staticStyle:{height:"100%"}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[a("el-row",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(e){return t.getAdd(1)}}},[t._v(" 添 加")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini",icon:"el-icon-top"},nativeOn:{click:function(e){return t.getMoveUp()}}},[t._v(" 上 移")]),a("el-button",{attrs:{disabled:1!=t.multipleSelection.length,type:"primary",size:"mini",icon:"el-icon-bottom"},nativeOn:{click:function(e){return t.getMoveDown()}}},[t._v(" 下 移")]),a("el-button",{attrs:{disabled:0==t.multipleSelection.length,type:"danger",size:"mini",icon:"el-icon-delete"},nativeOn:{click:function(e){return t.delItem()}}},[t._v("删 除")])],1)],1)],1),a("div",{staticClass:"table-container f-1 table-position"},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{data:t.lst,"element-loading-text":"Loading",height:t.tableHeight,border:"",fit:"","highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange,"row-click":t.saveRow}},[a("el-table-column",{attrs:{type:"selection",width:"50px",align:"center",fixed:"left"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e){return[!e.displaymark?t._e():a("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["plandate"==e.itemcode?a("div",[i.row.isEdit?a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",placeholder:"选择日期",size:"default"},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}}):a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))])],1):"itemtype"==e.itemcode?a("div",[i.row.isEdit?a("div",[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择",size:"default"},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}},t._l(t.itemtypeList,(function(e){return a("el-option",{key:e.value,attrs:{label:e.value,value:e.value}},[a("span",{staticStyle:{float:"left"}},[t._v(t._s(e.value))]),a("span",{staticStyle:{float:"right",color:"#8492a6","font-size":"13px","margin-left":"10px"}},[t._v(t._s(e.desc))])])})),1)],1):a("span",[t._v(t._s(i.row[e.itemcode]))])]):"opinion"==e.itemcode?a("div",[i.row.isEdit?a("div",[a("el-input",{attrs:{size:"default",placeholder:e.itemname,type:"textarea",autosize:{minRows:2,maxRows:4}},on:{focus:function(t){return t.currentTarget.select()}},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}})],1):a("span",[t._v(t._s(i.row[e.itemcode]))])]):"exponent"==e.itemcode?a("div",[a("div",[a("el-rate",{on:{change:t.changeRate},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}})],1)]):"finishmark"==e.itemcode?a("div",[a("div",[a("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"small"},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}})],1)]):"submitmark"==e.itemcode?a("div",[1==i.row[e.itemcode]?a("span",[t._v("已提报")]):a("span",[t._v("未提报")])]):a("div",[i.row.isEdit?a("div",[a("el-input",{attrs:{size:"default",placeholder:e.itemname},on:{focus:function(t){return t.currentTarget.select()}},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}})],1):a("span",[t._v(t._s(i.row[e.itemcode]))])])]}}],null,!0)})]}))],2)],1)])},m=[],p=(a("a434"),a("0a25")),f=a("ade3"),u=Object(f["a"])(Object(f["a"])(Object(f["a"])(Object(f["a"])(Object(f["a"])(Object(f["a"])(Object(f["a"])({id:"",pid:"",itemtype:"开发",story:"",opinion:"",exponent:0,finishmark:0,remark:"",rownum:""},"remark",""),"custom1",""),"custom2",""),"custom3",""),"custom4",""),"custom5",""),"rownum",0),h=a("5b24"),g={name:"Elitem",components:{autoComplete:h["a"]},props:["formdata","lstitem","idx"],data:function(){return{listLoading:!1,lst:[],multi:0,tableHeight:200,multipleSelection:[],isEditOk:!0,selVisible:!1,tableForm:p["a"],itemtypeList:[{value:"开发",desc:"合同开发"},{value:"测试",desc:"测试软件"},{value:"手册",desc:"文档类工作"},{value:"维护",desc:"对接客户"},{value:"研发",desc:"自主研发"}]}},watch:{lstitem:function(t,e){this.lst=this.lstitem},lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a,t[a].isEdit=!1}},created:function(){this.lst=[]},mounted:function(){this.catchHight()},updated:function(){var t=this;this.$nextTick((function(){t.$refs.multipleTable.doLayout()}))},methods:{deleteRows:function(t,e){var a=this,i=this.multipleSelection;i&&i.forEach((function(t,e){a.lst.forEach((function(e,i){t.goodsid===e.goodsid&&t.rownum===e.rownum&&a.lst.splice(i,1)}))})),this.$refs.multipleTable.clearSelection()},getAdd:function(t){var e=Object.assign({},u);0!=this.idx&&(e.pid=this.idx),this.lst.push(e)},handleSelectionChange:function(t){this.multipleSelection=t},delItem:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){}))},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=3*(t.$refs.elitem.getBoundingClientRect().height-32))}))},changeRate:function(t){console.log(t,"rate")},saveRow:function(t){for(var e=0;e<this.lst.length;e++)this.lst[e].isEdit=!1;this.isEditOk&&(t.isEdit?this.$set(t,"isEdit",!1):this.$set(t,"isEdit",!0)),this.$forceUpdate()},getMoveUp:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(0!=e){this.lst.splice(e,1),this.lst.splice(e-1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")}else this.$message.warning("只能选择一行内容！")},getMoveDown:function(){if(1==this.multipleSelection.length){var t=this.multipleSelection[0],e=this.multipleSelection[0].rownum;if(e!=this.lst.length-1){this.lst.splice(e,1),this.lst.splice(e+1,0,t);for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")}else this.$message.warning("只能选择一行内容！")}}},v=g,b=(a("75f0"),a("2877")),y=Object(b["a"])(v,d,m,!1,null,"4c14f926",null),w=y.exports,S=a("5c73"),x=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formedit",staticStyle:{"margin-top":"-20px"}},[a("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}]},[a("el-table",{key:t.keynum,ref:"tableList",attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["type"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showform(i.row.id)}}},[t._v(t._s(i.row[e.itemcode]?i.row[e.itemcode]:"提报类型"))]):"createdate"==e.itemcode||"deadlinedate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):"approvalswitch"==e.itemcode?a("div",[a("el-switch",{attrs:{size:"small",disabled:"已废弃"==i.row.status},on:{change:function(e){return t.changeApprovalSwitch(e,i.row)}},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}})],1):"description"==e.itemcode?a("div",[a("span",{domProps:{innerHTML:t._s(i.row[e.itemcode].replace(/\n/gm,"<br>"))}})]):"status"==e.itemcode?a("div",[a("span",{class:t.getStatusClass(i.row[e.itemcode]),staticStyle:{"font-weight":"bold"}},[t._v(t._s(i.row[e.itemcode]))])]):"level"==e.itemcode?a("div",[0==i.row[e.itemcode]?a("span",{staticClass:"levelTag"},[t._v("较低")]):t._e(),1==i.row[e.itemcode]?a("span",{staticClass:"levelTag tagblue"},[t._v("普通")]):t._e(),2==i.row[e.itemcode]?a("span",{staticClass:"levelTag tagorange"},[t._v("紧急")]):t._e(),3==i.row[e.itemcode]?a("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")]):t._e()]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)],1)])},_=[],k=(a("c740"),a("ac1f"),a("5319"),a("ea61")),$=a("b893"),C={components:{FormEdit:function(){return Promise.all([a.e("chunk-commons"),a.e("chunk-664b1b42")]).then(a.bind(null,"8a0f"))}},props:["online","searchVal","isDialog"],data:function(){return{lst:[],total:0,idx:0,formvisible:!1,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:k["a"],selectList:[]}},computed:{tableMaxHeight:function(){var t=window.innerHeight-180;return t<600&&(t=600),t+"px"}},updated:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.doLayout()}))},mounted:function(){this.bindData(),this.getColumn()},methods:{bindData:function(){var t=this;if(this.isDialog){var e={citeuid:this.searchVal};this.queryParams.SearchType=0,this.queryParams.SearchPojo?this.queryParams.SearchPojo=Object.assign(this.queryParams.SearchPojo,e):this.queryParams.SearchPojo=e}this.queryParams.DateRange||(this.queryParams.DateRange={StartDate:Object($["c"])()[0],EndDate:Object($["c"])()[1]});var a="/S06M02B3/getPageList";this.online&&(a="/S06M02B3/getOnlinePageList"),l["a"].post(a,JSON.stringify(this.queryParams)).then((function(e){if(200==e.data.code){t.lst=e.data.data.list,t.total=e.data.data.total;for(var a=0;a<t.lst.length;a++){var i=t.lst[a];i.approvalswitch=!1,i.assessorid&&(i.approvalswitch=!0),i.assessorid&&i.demandmark?t.$set(i,"status","已转需求"):i.assessorid&&!i.demandmark?t.$set(i,"status","已审待转"):i.assessorid||!i.oaflowmark||i.demandmark||t.$set(i,"status","OA审批中"),t.$set(i,"assigneename",t.getAssigneeName(i.assignee)),t.$set(i,"participantnames",t.getParticipantNames(i.participants))}}})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(n["a"])(Object(o["a"])().mark((function e(){return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$emit("sendTableForm",t.tableForm);case 1:case"end":return e.stop()}}),e)})))()},changeApprovalSwitch:function(t,e){var a=this;console.log(t),t?this.$confirm("是否确定审核通过?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(i){a.approval(t,e)})).catch((function(){a.$set(e,"approvalswitch",!1),a.$nextTick((function(){a.keynum+=1}))})):this.approval(t,e)},getAssigneeName:function(t){var e="";if(t){var a=this.engineerData.findIndex((function(e){return e.id==t}));-1!=a&&(e=this.engineerData[a].engineername)}return e},getParticipantNames:function(t){var e="";if(t){for(var a=t.split(","),i=0;i<a.length;i++){var s=a[i],o=this.engineerData.findIndex((function(t){return t.id==s}));-1!=o&&(e+=this.engineerData[o].engineername+",")}var n=/,$/gi;e=e.replace(n,"")}return e},approval:function(t,e){var a=this;l["a"].get("/S06M02B3/approval?key="+e.id).then((function(e){200==e.data.code?(a.$message.success(t?"审核成功":"反审核成功"),a.bindData()):a.$message.warning(e.data.msg||t?"审核失败":"反审核失败")}))},handleSelectionChange:function(t){this.selectList=t},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},getStatusClass:function(t){var e="";return"已审待转"==t?e="tagorange":"已转需求"==t?e="taggreen":"OA审批中"==t?e="tagblue":"已废弃"==t&&(e="taggrey"),e}}},j=C,D=(a("b2f3"),Object(b["a"])(j,x,_,!1,null,"236dec92",null)),L=D.exports,M={name:"Formedit",components:{EditItem:w,selDictionaries:S["a"],S06M02B3List:L},props:["idx","engineerData"],data:function(){return{title:"反馈单",formdata:{refno:"",projectid:"",billtitle:"",billdate:new Date,createdate:new Date,projectname:"",customer:"",groupid:"",groupname:"",groupuid:"",location:"",personnel:"",item:[],lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},personnelList:[],formRules:{projectname:[{required:!0,trigger:"blur",message:"项目名称不能为空"}],billtitle:[{required:!0,trigger:"blur",message:"会议主题不能为空"}],customer:[{required:!0,trigger:"blur",message:"客户名称不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px",TodoVisible:!1,projectData:[],eleWidth:"",tabsHeight:280,activeTabs:"0",selDicVisible:!1,printType:"print",operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:""}},computed:{formcontainHeight:function(){return window.innerHeight-113+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.getProject()},mounted:function(){var t=this;window.addEventListener("resize",(function(){t.eleWidth=t.SelDict()})),this.eleWidth=this.SelDict(),this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx&&l["a"].get("/S06M28B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.personnelList=t.formdata.personnel?t.formdata.personnel.split(","):[]):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},changePersonnel:function(t){t.length?this.formdata.personnel=this.personnelList.join(","):this.formdata.personnel=""},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.formdata.item=this.$refs.elitem.lst;var e=Object.assign({},this.formdata);0==this.idx?c.add(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")})):c.update(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData())})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){c.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("closeForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},approval:function(){var t=this;this.formdata.id?this.approvalRequest(this.formdata.id):this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;c.add(t.formdata).then((function(e){t.formdata=e.data,t.$emit("changeidx",t.formdata.id),t.approvalRequest(t.formdata.id)})).catch((function(e){t.$message.warning(e||"保存失败")}))}))},deApproval:function(){var t=this;l["a"].get("/S06M28B1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"反审核成功"),t.formdata=e.data.data):t.$message.warning(e.data.msg||"反审核失败")}))},approvalRequest:function(t){var e=this;return Object(n["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,l["a"].get("/S06M28B1/approval?key="+e.formdata.id).then((function(t){200==t.data.code?(e.$message.success(t.data.msg||"审核成功"),e.formdata=t.data.data):e.$message.warning(t.data.msg||"审核失败")}));case 2:case"end":return t.stop()}}),t)})))()},getProject:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};l["a"].post("/S06M01S1/getPageTh",JSON.stringify(e)).then((function(e){console.log(e,"项目列表"),200==e.data.code&&(t.projectData=e.data.data.list)}))},changeProject:function(t){var e=this;this.projectData.forEach((function(a){a.id==t&&(e.formdata.projectname=a.projname,e.formdata.projectid=a.id)}))},processBill:function(t){this.processVisible=!0,this.processModel=t.code,this.processTitle=t.label},openS06M02B3:function(){var t=this;if(0!=this.$refs.elitem.multipleSelection.length){for(var e=0;e<this.$refs.elitem.multipleSelection.length;e++)if(1==this.$refs.elitem.multipleSelection[e].submitmark)return void this.$message.warning("第"+this.$refs.elitem.multipleSelection[e]+"行已转需求提报！");this.$confirm("是否勾选内容转需求提报?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.setDemSubmit()}))}else this.$message.warning("勾选内容不能为空")},setDemSubmit:function(){var t=this;return Object(n["a"])(Object(o["a"])().mark((function e(){var a,i,s,n;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(t,a=[],i=t.$refs.elitem.multipleSelection,s=0;s<i.length;s++)n=new Promise((function(e,a){var o=i[s],n={type:"反馈单",source:"反馈单",status:"待审核",groupname:t.formdata.customer,description:o.story,level:1,participants:t.formdata.personnel,feedbackitemid:o.id};t.$request.post("S06M02B3/create",JSON.stringify(n)).then((function(t){200==t.data.code?e(t.data):a(t.data.msg||"生成WIP表错误")}))})),a.push(n);return e.next=6,Promise.all(a).then((function(e){t.$message.success(e.msg||"生成提报成功！"),t.bindData()})).catch((function(e){t.$message.warning(e)})).finally((function(){}));case 6:case"end":return e.stop()}}),e)})))()},changeIdx:function(t){this.dialogIdx=t},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},SelDict:function(){var t=this.$refs.colRefs;return"".concat(t.offsetWidth-100)},dateFormats:function(){var t=new Date,e=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),i=t.getDate().toString().padStart(2,"0");return"".concat(e).concat(a).concat(i)}}},T=M,B=(a("e232"),Object(b["a"])(T,i,s,!1,null,"34fbc0e4",null));e["a"]=B.exports},"4c20":function(t,e,a){"use strict";a("7036")},"4dae":function(t,e,a){"use strict";a("56e0")},"56e0":function(t,e,a){},"5c73":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,i){return a("li",{key:i,class:t.radio==i?"active":"",on:{click:function(a){t.getCurrentRow(e),t.radio=i}}},[a("span",[t._v(t._s(e.dictvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,i){return a("p",{key:i,class:t.ActiveIndex==i?"isActive":"",on:{click:function(e){t.ActiveIndex=i}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},s=[],o=(a("a434"),a("e9c4"),a("b775")),n=a("333d"),l=a("b0b8"),r={components:{Pagination:n["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],o["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var a=0;a<t.formdata.item.length;a++)t.lst.push(t.formdata.item[a])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.addItem(a)})).catch((function(t){console.log(t)}))},addItem:function(t){l.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:l.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.lst[t.ActiveIndex].dictvalue=a,l.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=l.getFullChars(a)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,o["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=r,d=(a("af2b"),a("2877")),m=Object(d["a"])(c,i,s,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"66fca":function(t,e,a){"use strict";a("b443")},7036:function(t,e,a){},"716f":function(t,e,a){},"75f0":function(t,e,a){"use strict";a("01ede")},"7de4":function(t,e,a){},"8a0f":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",[a("div",{staticStyle:{position:"absolute",right:"40px","z-index":"99"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1),a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("type")}}},[a("el-form-item",{attrs:{label:"提报类型",prop:"type"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"类型"},model:{value:t.formdata.type,callback:function(e){t.$set(t.formdata,"type",e)},expression:"formdata.type"}},[a("el-option",{attrs:{label:"bug",value:"bug"}}),a("el-option",{attrs:{label:"需求",value:"需求"}}),a("el-option",{attrs:{label:"客户反馈",value:"客户反馈"}}),a("el-option",{attrs:{label:"运维反馈",value:"运维反馈"}})],1)],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("source")}}},[a("el-form-item",{attrs:{label:"需求来源",prop:"source"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.formdata.source,callback:function(e){t.$set(t.formdata,"source",e)},expression:"formdata.source"}},[a("el-option",{attrs:{label:"pms",value:"pms"}}),a("el-option",{attrs:{label:"rms",value:"rms"}}),a("el-option",{attrs:{label:"客户",value:"客户"}}),a("el-option",{attrs:{label:"内部",value:"内部"}}),a("el-option",{attrs:{label:"其他",value:"其他"}})],1)],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[a("autoComplete",{attrs:{size:"default",value:t.formdata.groupname,baseurl:"/S06M14B1/getPageList",params:{name:"groupname",other:"groupuid"}},on:{setRow:function(e){return t.setRow(e)},autoClear:function(e){return t.autoClear()}}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("status")}}},[a("el-form-item",{attrs:{label:"进展阶段",prop:"status"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.assessorid},model:{value:t.formdata.status,callback:function(e){t.$set(t.formdata,"status",e)},expression:"formdata.status"}},[a("el-option",{attrs:{label:"待审批",value:"待审批"}}),a("el-option",{attrs:{label:"已废弃",value:"已废弃"}})],1)],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("description")}}},[a("el-form-item",{attrs:{label:"提报内容",prop:"description"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入提报内容",clearable:"",autosize:{minRows:4,maxRows:10}},model:{value:t.formdata.description,callback:function(e){t.$set(t.formdata,"description",e)},expression:"formdata.description"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("deadlinedate")}}},[a("el-form-item",{attrs:{label:"截止日期",prop:"deadlinedate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss","default-time":"17:00:00",type:"datetime",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.deadlinedate,callback:function(e){t.$set(t.formdata,"deadlinedate",e)},expression:"formdata.deadlinedate"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("level")}}},[a("el-form-item",{attrs:{label:"优先级",prop:"level"}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.formdata.level,callback:function(e){t.$set(t.formdata,"level",e)},expression:"formdata.level"}},[a("el-option",{attrs:{label:"较低",value:0}}),a("el-option",{attrs:{label:"普通",value:1}}),a("el-option",{attrs:{label:"紧急",value:2}}),a("el-option",{attrs:{label:"非常紧急",value:3}})],1)],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("assigneename")}}},[a("el-form-item",{attrs:{label:"执行者",prop:"assigneename"}},[a("autoComplete",{attrs:{size:"default",value:t.formdata.assigneename,baseurl:"/S06M02S2/getPageList",params:{name:"engineername",other:"engineertype"}},on:{setRow:t.setAssignee,autoClear:t.clearAssignee}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("participants")}}},[a("el-form-item",{attrs:{label:"协作者",prop:"participants"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,clearable:"",placeholder:"请选择人员"},model:{value:t.formdata.participants,callback:function(e){t.$set(t.formdata,"participants",e)},expression:"formdata.participants"}},t._l(t.engineerData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.engineername,value:t.id}})})),1)],1)],1)])],1)],1)],1),a("el-form",{staticClass:"footFormContent"},[a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",clearable:""},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1)],1),a("el-row",[a("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px"},attrs:{span:24}},[a("div",[t.formdata.id&&!t.formdata.assessorid?a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.handleDelete()}}},[t._v("删除")]):t._e(),t.formdata.id?a("el-button",{attrs:{type:"success",disabled:"已废弃"==t.formdata.status},on:{click:function(e){return t.approval()}}},[t._v(t._s(t.formdata.assessorid?"反审核":"审核"))]):t._e(),t.formdata.id&&!t.formdata.assessorid?a("el-button",{attrs:{type:"success",disabled:"已废弃"==t.formdata.status},on:{click:function(e){return t.$refs.flowable.action()}}},[t._v("OA审批")]):t._e()],1),a("div",[t.formdata.assessorid?t._e():a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("formdata")}}},[t._v("保存")]),a("el-button",{on:{click:t.closeDialog}},[t._v("关闭")])],1)])],1)],1)]),a("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"S06M02B3Edit",examineurl:"/S06M02B3/sendapprovel"}})],1)},s=[],o=(a("c740"),a("b64b"),a("d3b7"),a("ac1f"),a("5319"),a("0643"),a("4e3e"),a("159b"),a("b775"));const n={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);o["a"].post("/S06M02B3/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);o["a"].post("/S06M02B3/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{o["a"].get("/S06M02B3/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var l=n,r=a("5b24"),c=a("acb9"),d={name:"addDialog",props:["idx","projectRow","isDialog","initData","billcode","engineerData"],components:{autoComplete:r["a"],Flowable:c["a"]},data:function(){return{title:"需求提报",currentId:this.idx,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,type:"",description:"",deadlinedate:"",assignee:"",assigneename:"",status:"待审批",source:"",level:0,groupid:"",groupname:"",participants:[],id:""},formRules:{type:[{required:!0,trigger:"blur",message:"类型不能为空"}],projectid:[{required:!0,trigger:"blur",message:"项目不能为空"}]}}},watch:{idx:function(t,e){this.currentId=t,this.bindData()}},mounted:function(){this.isDialog&&this.billSwitch(this.billcode)},methods:{bindData:function(){var t=this;0!=this.currentId&&o["a"].get("/S06M02B3/getEntity?key=".concat(this.currentId)).then((function(e){if(200==e.data.code){if(t.formdata=e.data.data,t.formdata.participants=t.formdata.participants?t.formdata.participants.split(","):[],t.formdata.assignee){var a=t.engineerData.findIndex((function(e){return e.id==t.formdata.assignee}));-1!=a&&t.$set(t.formdata,"assigneename",t.engineerData[a].engineername)}}else t.$message.warning(e.data.msg||"获取发布信息失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},submitForm:function(t){var e=this;this.$refs["formdata"].validate((function(t){if(!t)return!1;e.saveForm()}))},saveForm:function(){var t=this,e=Object.assign({},this.formdata);if(this.formdata.participants.length){e.participants="",this.formdata.participants.forEach((function(t){e.participants+=t+","}));var a=/,$/gi;e.participants=e.participants.replace(a,"")}else e.participants="";console.log("paramsdata",e),0==this.idx?l.add(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.$emit("changeIdx",e.data.id),t.$nextTick((function(){t.bindData()})))})).catch((function(e){t.$message.warning(e||"保存失败")})):l.update(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.$nextTick((function(){t.bindData()})))})).catch((function(e){t.$message.warning(e||"保存失败"),t.closeDialog()}))},handleDelete:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.delete(t.formdata.id).then((function(e){200==e.code&&(t.$message.success("删除成功"),t.$emit("bindData"),t.closeDialog())})).catch((function(){t.$message.warning("删除失败")}))})).catch((function(){}))},approval:function(){var t=this;this.formdata.assessorid||!this.formdata.oaflowmark||this.formdata.demandmark?o["a"].get("/S06M02B3/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success(t.formdata.assessorid?"反审核成功":"审核成功"),t.$emit("bindData"),t.bindData()):t.$message.warning(e.data.msg||!t.formdata.assessorid?"审核失败":"反审核失败")})):this.$message.warning("单据正在OA审批中")},closeDialog:function(){this.isDialog,this.$emit("closeDialog")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},setRow:function(t){console.log(t),this.formdata.groupid=t.id,this.formdata.groupname=t.groupname,this.formdata.groupuid=t.groupuid,this.cleValidate("groupname")},autoClear:function(){this.formdata.groupid="",this.formdata.groupname="",this.formdata.groupuid=""},setAssignee:function(t){this.formdata.assignee=t.id,this.formdata.assigneename=t.engineername},clearAssignee:function(){this.formdata.assignee="",this.formdata.assigneename=""},billSwitch:function(t){}}},m=d,p=(a("4dae"),a("2877")),f=Object(p["a"])(m,i,s,!1,null,"642677e5",null);e["default"]=f.exports},"8b05":function(t,e,a){"use strict";a("c40e")},a116:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"wrap"},[i("div",{staticClass:"left"},[i("div",{staticClass:"left-item"},[i("el-input",{attrs:{readonly:"已完成"==t.status.statustype,placeholder:"请输入任务标题",clearable:""},on:{change:function(e){return t.changeMsg("任务标题")}},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1),i("div",{staticClass:"left-item"},[i("el-input",{attrs:{placeholder:"请输入任务描述",clearable:"",type:"textarea",autosize:{minRows:4,maxRows:6}},on:{change:function(e){return t.changeMsg("任务描述")}},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1),i("div",{staticClass:"left-item"},[t._m(0),i("el-popover",{ref:"billTypeRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-selectTag"}},[i("div",{ref:"selectTag",staticClass:"levelTag-content"},t._l(t.billtypeArr,(function(e,a){return i("div",{key:a,staticClass:"levelTag-content-item",on:{click:function(a){return t.changeBilltype(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.billtype==e?1:0}}),i("span",[t._v(t._s(e))])])})),0),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.billtype?t.formdata.billtype:"待选择"))])])])],1),i("div",{staticClass:"left-item"},[t._m(1),i("div",[i("el-popover",{ref:"statusRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[t.formdata.demandstatus?t._e():i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeStatus({id:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.demandstatus?0:1,color:"#1b9aee"}}),i("span",[t._v("待处理")])]),t._l(t.statusList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeStatus(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.demandstatus==e.id?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.statusname))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.demandstatus?i("div",[i("span",[t._v(t._s(this.getStatusName(t.formdata.demandstatus)))])]):i("div",[t._v("待处理")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(2),i("div",[i("el-popover",{ref:"appointeeRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeAppointee({id:"",engineername:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.appointee?0:1,color:"#1b9aee"}}),i("span",[t._v("待认领")])]),t._l(t.userList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeAppointee(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.appointeeid==e.id?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.engineername))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.appointeeid?i("span",{staticClass:"appointeeTag"},[t._v(" "+t._s(t.formdata.appointee)+" "),i("i",{staticClass:"CloseBtnSty el-icon-close",on:{click:function(e){return e.stopPropagation(),t.changeAppointee({id:"",engineername:""})}}})]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("待认领")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(3),i("div",[i("el-popover",{ref:"collaboratorRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeCollaborator({id:"",engineername:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.collaboratorids?0:1,color:"#1b9aee"}}),i("span",[t._v("待添加")])]),t._l(t.userList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeCollaborator(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.collaboratorids&&t.formdata.collaboratorids.includes(e.id)?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.engineername))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.collaboratorids?i("div",[i("span",{staticClass:"collaboratorsTag"},[t._v(t._s(t.formdata.collaborators)+" "),i("i",{staticClass:"CloseBtnSty el-icon-close",on:{click:function(e){return e.stopPropagation(),t.changeCollaborator({id:"",engineername:""})}}})])]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("待添加")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(4),i("el-popover",{ref:"labeljsonRef",attrs:{placement:"right-end",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"250px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.groupVal,expression:"groupVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索客户"},domProps:{value:t.groupVal},on:{input:[function(e){e.target.composing||(t.groupVal=e.target.value)},function(e){return t.getGroupAllList(t.groupVal)}]}})]),t.groupAllList.length?i("div",{staticStyle:{height:"220px","overflow-y":"auto"}},t._l(t.groupAllList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeGroup(e)}}},[i("span",{class:"label-color"},[t._v(t._s(e.groupname))]),i("div",[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.groupid==e.id?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无客户","image-size":90}})],1)])]),i("div",{staticClass:"flex a-c",attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.groupname?t.formdata.groupname:"暂无客户"))]),t.formdata.groupname?i("div",{staticClass:"CloseBtnSty",on:{click:function(e){return e.stopPropagation(),t.changeGroup({id:""})}}},[i("i",{staticClass:"el-icon-close"})]):t._e()])])],1),i("div",{staticClass:"left-item"},[t._m(5),i("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择开始时间","default-time":"08:30:00",clearable:!0,editable:!1},on:{clear:function(e){t.formdata.startdate=""},input:function(e){t.formdata.startdate=e},change:function(e){return t.changeDate("开始时间")}},model:{value:t.formdata.startdate,callback:function(e){t.$set(t.formdata,"startdate",e)},expression:"formdata.startdate"}})],1),i("div",{staticClass:"left-item"},[t._m(6),i("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择截止时间","default-time":"17:00:00",clearable:!0,editable:!1},on:{clear:function(e){t.formdata.deaddate=""},input:function(e){t.formdata.deaddate=e},change:function(e){return t.changeDate("截止时间")}},model:{value:t.formdata.deaddate,callback:function(e){t.$set(t.formdata,"deaddate",e)},expression:"formdata.deaddate"}})],1),i("div",{staticClass:"left-item"},[t._m(7),i("el-popover",{ref:"labeljsonRef",attrs:{placement:"right-end",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"250px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.projectVal,expression:"projectVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索项目"},domProps:{value:t.projectVal},on:{input:[function(e){e.target.composing||(t.projectVal=e.target.value)},t.searchProject]}})]),t.projectAllListCopy.length?i("div",{staticStyle:{height:"220px","overflow-y":"auto"}},t._l(t.projectAllListCopy,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeProject(e)}}},[i("span",{class:"label-color"},[t._v(t._s(e.projname))]),i("div",[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.projectid==e.id?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无项目","image-size":90}})],1)])]),i("div",{staticClass:"flex a-c",attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.projname?t.formdata.projname:"暂无项目"))]),t.formdata.projname?i("div",{staticClass:"CloseBtnSty",on:{click:function(e){return e.stopPropagation(),t.changeProject({id:""})}}},[i("i",{staticClass:"el-icon-close"})]):t._e()])])],1),i("div",{staticClass:"left-item"},[t._m(8),i("div",[i("el-popover",{ref:"selectTagRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-selectTag"}},[i("div",{ref:"selectTag",staticClass:"levelTag-content"},[i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(0)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:0==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag taggray"},[t._v("较低")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(1)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:1==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagblue"},[t._v("普通")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(2)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:2==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagorange"},[t._v("紧急")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(3)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:3==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")])])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[0==t.formdata.level?i("div",{staticClass:"levelTag taggray"},[t._v(" 较低 ")]):1==t.formdata.level?i("div",{staticClass:"levelTag tagblue"},[t._v(" 普通 ")]):2==t.formdata.level?i("div",{staticClass:"levelTag tagorange"},[t._v(" 紧急 ")]):3==t.formdata.level?i("div",{staticClass:"levelTag tagred"},[t._v(" 非常紧急 ")]):t._e()])])],1)]),i("div",{staticClass:"left-item"},[t._m(9),i("div",[i("el-popover",{ref:"labeljsonRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"220px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.showSetLbel,expression:"showSetLbel"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.labelVal,expression:"labelVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索标签"},domProps:{value:t.labelVal},on:{input:[function(e){e.target.composing||(t.labelVal=e.target.value)},t.searchLabel]}}),i("i",{staticClass:"el-icon-circle-plus-outline",on:{click:function(e){t.showSetLbel=!1,t.labeloperaType=1,t.labelname=""}}})]),t.labelList.length?i("div",{staticStyle:{height:"180px","overflow-y":"auto"}},t._l(t.labelList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeLabeljson(e)}}},[i("span",{class:"label-color label-"+e.labelcolor},[t._v(t._s(e.labelname))]),i("div",[i("i",{staticClass:"el-icon-edit",staticStyle:{color:"#15ad31"},on:{click:function(a){return a.stopPropagation(),t.editLabel(e)}}}),i("i",{staticClass:"el-icon-check",style:{opacity:-1!=t.formdata.labeljson.findIndex((function(t){return t.labelid==e.id}))?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无标签","image-size":80}})],1)]),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.showSetLbel,expression:"!showSetLbel"}]},[i("div",{staticClass:"createLabel flex j-s a-c"},[i("i",{staticClass:"el-icon-arrow-left",on:{click:function(e){t.showSetLbel=!0}}}),i("strong",[t._v(" 创建标签")]),i("i",{staticClass:"el-icon-close",on:{click:function(e){t.showSetLbel=!0,t.$refs["labeljsonRef"].doClose()}}})]),i("div",{},[i("el-input",{staticStyle:{padding:"10px","margin-top":"12px"},attrs:{placeholder:"标签名称"},model:{value:t.labelname,callback:function(e){t.labelname=e},expression:"labelname"}}),i("div",{staticClass:"flex",staticStyle:{padding:"10px","margin-top":"12px"}},t._l(t.discColor,(function(e,a){return i("div",{key:a,class:"disc-color disc-"+e,on:{click:function(a){t.labelcolor=e}}},[t.labelcolor==e?i("i",{staticClass:"el-icon-check"}):t._e()])})),0),i("el-button",{staticStyle:{width:"calc(100% - 20px)",margin:"12px 10px"},attrs:{type:"primary"},on:{click:t.createLabel}},[t._v("创建")])],1)])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.labeljson.length?i("div",{staticStyle:{display:"inline-block",cursor:"pointer"}},[i("i",{staticClass:"el-icon-circle-plus-outline"})]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("添加标签")])])])],1),t.formdata.labeljson.length?i("div",{staticClass:"popper-labeljson labstyle"},t._l(t.formdata.labeljson,(function(e,a){return i("span",{key:a,class:"label-color label-"+e.color},[t._v(" "+t._s(e.name)+" "),i("i",{staticClass:"el-icon-close",on:{click:function(i){return t.closeLabeljson(e,a)}}})])})),0):t._e()]),i("div",{staticClass:"left-item"},[t._m(10),i("el-input-number",{attrs:{min:0,placeholder:"请输入工时",controls:!0,step:.5},on:{change:t.changeWorkTime},model:{value:t.formdata.worktime,callback:function(e){t.$set(t.formdata,"worktime",e)},expression:"formdata.worktime"}})],1),i("div",{staticClass:"left-item"},[t._m(11),i("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},on:{change:t.changeWeekMark},model:{value:t.formdata.weekmark,callback:function(e){t.$set(t.formdata,"weekmark",e)},expression:"formdata.weekmark"}})],1)]),i("div",{staticClass:"right"},[i("div",{staticClass:"right-header"},[i("p",[t._v("参与者·"+t._s(t.Participants.length))]),i("div",t._l(t.Participants,(function(e,a){return i("span",{key:a,staticClass:"right-header-tag"},[t._v(t._s(e.engineername))])})),0)]),i("div",{staticClass:"right-content myscrollbar"},[i("div",{staticClass:"tigs"},[i("div",{staticClass:"tigs-item",class:0==t.islogtype?"tigs-active":"",on:{click:function(e){t.islogtype=0,t.getDemandLog()}}},[t._v(" 所有动态 ")]),i("div",{staticClass:"tigs-item",class:1==t.islogtype?"tigs-active":"",on:{click:function(e){t.islogtype=1,t.getDemandLog()}}},[t._v(" 仅评论 ")])]),i("div",{staticClass:"logs"},[t.logTotal>10?i("div",{staticClass:"logs-more",on:{click:function(e){return t.getDemandLog(!0)}}},[t._v(" 默认显示最新10条动态，点击查看所有... ")]):t._e(),t._l(t.logList,(function(e,s){return i("div",{key:s,staticClass:"logs-item"},["评论"==e.type?i("div",{staticClass:"type-content"},[i("p",{staticClass:"flex j-s"},[i("span",{staticStyle:{color:"#333"}},[i("i",{staticClass:"el-icon-user"}),t._v(" "+t._s(e.createby))]),i("span",[t._v(t._s(t._f("dateFormats")(e.createdate)))])]),i("p",{staticStyle:{color:"#505050","text-indent":"16px"},domProps:{innerHTML:t._s(e.content.replace(/\n/gm,"<br>"))}}),e.attachment.name?i("div",{staticClass:"fileUploadsty"},[i("div",{staticClass:"fileUploadShow"},[i("img",{attrs:{src:e.attachment.type?a("b484")("./"+e.attachment.type+".png"):a("692f"),alt:""}})]),i("div",{staticClass:"fileUploadInfo"},[i("p",{staticClass:"ellipsis"},[t._v("名称："+t._s(e.attachment.name))]),i("p",[t._v("大小："+t._s(e.attachment.size+"KB"))]),i("span",{staticClass:"downFile",on:{click:function(a){return t.downFileName(e.attachment)}}},[t._v(" 下载附件 ")]),i("span",{staticClass:"downFile",on:{click:function(a){return t.getFileName(e.attachment)}}},[t._v(" 预览附件 ")])])]):t._e()]):i("div",[i("p",{staticClass:"flex j-s",staticStyle:{color:"#8c8c8c","font-size":"12px"}},[i("span",{staticStyle:{flex:"1"}},[i("i",{staticClass:"el-icon-edit-outline"}),i("span",{staticStyle:{"margin-left":"6px"},domProps:{innerHTML:t._s(e.content.replace(/\n/gm,"<br>"))}})]),i("span",[t._v(t._s(t._f("dateFormats")(e.createdate)))])])])])}))],2)]),i("div",{staticClass:"right-footer"},[i("textarea",{directives:[{name:"model",rawName:"v-model",value:t.textareaVal,expression:"textareaVal"}],staticClass:"textareaSty",attrs:{cols:"30",rows:"3",placeholder:"请输入评论"},domProps:{value:t.textareaVal},on:{input:function(e){e.target.composing||(t.textareaVal=e.target.value)}}}),i("div",{staticClass:"filesty"},t._l(t.fileList,(function(e,a){return i("span",{key:a,staticStyle:{cursor:"default"}},[t._v(" "+t._s(e.name)+" "),i("i",{staticClass:"el-icon-close",staticStyle:{cursor:"pointer"},on:{click:function(i){return t.deleteFile(e,a)}}})])})),0),i("div",[i("i",{staticClass:"el-icon-paperclip paperclipFile",on:{click:t.openFileUpload}}),i("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:t.addLog}},[t._v("回复")])],1)])]),t.fileUploadVisible?i("el-dialog",{attrs:{title:"添加附件",visible:t.fileUploadVisible,width:"500px","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.fileUploadVisible=e}}},[i("FileUpload",{ref:"fileUpload",on:{closeDialog:function(e){t.fileUploadVisible=!1}}}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.fileUploadBtn}},[t._v("确定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.fileUploadVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.isViewPdf20?i("el-dialog",{attrs:{title:"附件预览",visible:t.isViewPdf20,top:"2vh",width:"80%","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[t.isImg?i("div",{staticStyle:{width:"100%","text-align":"center"}},[i("img",{staticStyle:{height:"auto",width:"auto"},attrs:{src:t.blobUrl,alt:""}})]):i("iframe",{staticStyle:{height:"80vh",width:"100%"},attrs:{id:"iframeId",src:t.blobUrl,frameborder:"0",scrolling:"auto"}})]):t._e()],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-info"}),t._v(" 类型")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-folder-checked"}),t._v(" 状态")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user"}),t._v(" 执行者")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user-solid"}),t._v(" 协作者")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user-solid"}),t._v(" 客户")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-date"}),t._v(" 开始时间")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-date"}),t._v(" 截止时间")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-coin"}),t._v(" 项目")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-s-flag"}),t._v(" 优先级")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-price-tag"}),t._v(" 标签")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-time"}),t._v(" 工时")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-share"}),t._v(" 周报")])}],o=a("c7eb"),n=a("1da1"),l=(a("99af"),a("c740"),a("caad"),a("a434"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("2532"),a("3ca3"),a("4d90"),a("5319"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("08dd")),r=a("6e25"),c={props:["idx","statusList","userList","billtypeArr"],components:{FileUpload:l["a"],picker:r["default"]},data:function(){return{projectList:{item:[]},Participants:[],status:{},formdata:{labeljson:[],remark:"",billtitle:""},islogtype:0,logTotal:0,logList:[],labelList:[],labelListCopy:[],textareaVal:"",labelVal:"",labelname:"",labelId:"",showSetLbel:!0,labelcolor:"blue",labeloperaType:1,fileUploadVisible:!1,fileList:[],projectVal:"",projectAllList:[],projectAllListCopy:[],groupVal:"",groupAllList:[],blobUrl:"",isViewPdf20:!1,isImg:!1,workItemList:[],discColor:["blue","green","zi","orange","red"]}},mounted:function(){this.bindData(),this.getLabelList(),this.getGroupAllList()},methods:{bindData:function(){var t=this;0!=this.idx&&this.$request.get("/S06M02B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formdata.weekmark=t.formdata.weekmark?1:0,t.formdata.labeljson=t.formdata.labeljson?JSON.parse(t.formdata.labeljson):[],t.fileList=[],t.$forceUpdate(),t.getDemandLog(),t.bindRightContent()):t.$message.warning(e.data.msg||"获取任务信息失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getDemandLog:function(t){var e=this,a={PageNum:1,PageSize:10,OrderType:1,SearchType:1,SearchPojo:{demandid:this.formdata.id}};this.islogtype&&(a.scenedata=[{field:"Sa_DemandLog.type",fieldtype:1,math:"equal",value:"评论"}]),t&&(a.PageSize=100),this.$request.post("/S06M02S1/getPageList",JSON.stringify(a)).then((function(t){if(200==t.data.code){e.logList=t.data.data.list;for(var a=0;a<e.logList.length;a++){var i=e.logList[a];i.attachment=JSON.parse(i.attachment)}e.logTotal=t.data.data.total,console.log(e.logList)}else e.$message.warning(t.data.msg||"获取任务日志失败")}))},openFileUpload:function(){this.fileList.length>=1?this.$message.warning("只能添加一个附件"):this.fileUploadVisible=!0},fileUploadBtn:function(){var t=this,e=new FormData;e.append("file",this.$refs.fileUpload.file),this.$request.post("/File/upload?dirname="+this.$refs.fileUpload.uploadFileName,e).then((function(e){if(200==e.data.code){var a={name:t.$refs.fileUpload.uploadFileName,type:t.$refs.fileUpload.uploadFileType,size:t.$refs.fileUpload.uploadFileSize,fileurl:"/"+e.data.data.dirname+"/"+e.data.data.filename};t.fileList.push(a),t.fileUploadVisible=!1}else t.$message.warning(e.data.msg||"上传失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},deleteFile:function(t,e){var a=this;this.$request.post("/File/deleteFile?bucketName=inkspms&objectName="+t.fileurl).then((function(t){200==t.data.code?a.fileList.splice(e,1):a.$message.warning(t.data.msg||"删除附件失败")})).catch((function(t){a.$message.error(t||"请求错误")}))},downFileName:function(t){this.axios.get("http://dev.inksyun.com:9080/inkspms"+t.fileurl,{responseType:"blob"}).then((function(e){var a=window.URL.createObjectURL(e.data),i=document.createElement("a");i.href=a,i.download=t.name,i.click(),i.remove()}))},getFileName:function(t){var e=this;console.log(t),this.axios.get("http://dev.inksyun.com:9080/inkspms"+t.fileurl,{responseType:"blob"}).then((function(a){var i="application/pdf";if(e.isImg=!1,"pdf"==t.type.toLowerCase())i="application/pdf";else if("txt"===t.type.toLowerCase())i="text/plain";else{if(["jpg","jpeg","gif","bmp","png"].includes(t.name.toLowerCase()))return e.isImg=!0,e.blobUrl=window.URL.createObjectURL(a.data),e.isViewPdf20=!0,void e.$forceUpdate();e.$utils.message("info","此类型文件不支持预览,请下载查看！")}var s=[];s.push(a.data),e.blobUrl=window.URL.createObjectURL(new Blob(s,{type:i})),e.isViewPdf20=!0}))},addLog:function(){var t=this,e={type:"评论",content:this.textareaVal,demandid:this.formdata.id};this.fileList.length&&(e.attachment=JSON.stringify(this.fileList[0])),this.$request.post("/S06M02S1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.textareaVal="",t.fileList=[],t.getDemandLog()):t.$message.warning(e.data.msg||"评论失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},changeMsg:function(t){var e=this,a={id:this.formdata.id};"任务描述"==t?a.remark=this.formdata.remark:a.billtitle=this.formdata.billtitle,this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(a){200==a.data.code?(e.formdata=a.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(a.data.msg||"修改"+t+"失败"),e.bindData())}))},changeBilltype:function(t){var e=this;if(!t||this.formdata.billtype!=t){this.$refs["billTypeRef"].doClose();var a={billtype:t,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改优先级失败"),e.bindData())}))}},changeStatus:function(t){var e=this;if(this.formdata.demandstatus!=t.id){this.$refs["statusRef"].doClose();var a={demandstatus:t.id,id:this.formdata.id};2==t.statusattr?this.$prompt("","完成说明",{confirmButtonText:"提交",cancelButtonText:"关闭",closeOnClickModal:!1,inputValue:this.formdata.finishdes,inputType:"textarea",inputPlaceholder:"请输入"}).then((function(t){var i=t.value;a.finishdes=i,a.finishdate=new Date,e.updateStatus(a)})).catch((function(){e.$emit("bindData")})):this.updateStatus(a)}},updateStatus:function(t){var e=this;this.$request.post("/S06M02B1/update",JSON.stringify(t)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindData()):(e.$message.warning(t.data.msg||"修改状态失败"),e.bindData())}))},changeAppointee:function(t){var e=this;if(this.formdata.appointeeid!=t.id){this.$refs["appointeeRef"].doClose();var a={appointeeid:t.id,appointee:t.engineername,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindRightContent()):(e.$message.warning(t.data.msg||"修改执行者失败"),e.bindData())}))}},changeCollaborator:function(t){var e=this,a={collaboratorids:"",collaborators:"",id:this.formdata.id};if(""==t.id)this.$refs["collaboratorRef"].doClose();else if(this.formdata.collaboratorids){var i=this.formdata.collaboratorids.split(","),s=this.formdata.collaborators.split(",");if(console.log(i,"collaboratoridArr"),i.includes(t.id)){var o=i.findIndex((function(e){return e==t.id}));if(console.log(o),-1!=o){i.splice(o,1),s.splice(o,1),console.log(s,"collaboratorArr2");for(var n=/,$/gi,l=0;l<i.length;l++){var r=i[l];a.collaboratorids+=r+","}a.collaboratorids=a.collaboratorids.replace(n,"");for(l=0;l<s.length;l++){r=s[l];a.collaborators+=r+","}a.collaborators=a.collaborators.replace(n,"")}}else a.collaboratorids=this.formdata.collaboratorids+","+t.id,a.collaborators=this.formdata.collaborators+","+t.engineername}else a.collaboratorids=t.id,a.collaborators=t.engineername;this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindRightContent()):(e.$message.warning(t.data.msg||"修改执行者失败"),e.bindData())}))},changeGroup:function(t){var e=this,a={groupid:t.id||"",groupname:t.groupname||"",groupuid:t.groupuid||"",id:this.formdata.id};this.formdata.groupid!=t.id&&this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改客户信息失败"),e.bindData())}))},getGroupAllList:function(t){var e=this,a={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};t&&(a.SearchPojo={groupname:t}),this.$request.post("/S06M14B1/getPageList",JSON.stringify(a)).then((function(t){200==t.data.code?e.groupAllList=t.data.data.list:e.$message.warning(t.data.msg||"查询客户信息失败")}))},changelevel:function(t){var e=this;if(this.formdata.level!=t){this.$refs["selectTagRef"].doClose();var a={level:t,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改优先级失败"),e.bindData())}))}},changeDate:function(t){var e=this,a={id:this.formdata.id};"截止时间"==t?this.formdata.deaddate?a.deaddate=this.dateFormat(this.formdata.deaddate):a.deaddate=new Date(0):this.formdata.startdate?a.startdate=this.dateFormat(this.formdata.startdate):a.startdate=new Date(0),this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.$forceUpdate(),e.getDemandLog()):(e.$message.warning(t.data.msg||"修改时间失败"),e.bindData())}))},dateFormat:function(t){if(console.log(t),t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),o=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0");e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(s," ").concat(o,":").concat(n)}},changeWeekMark:function(t){var e=this,a={weekmark:t||0,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改工时失败"),e.bindData())}))},changeWorkTime:function(t){var e=this,a={worktime:t||0,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改工时失败"),e.bindData())}))},changeLabeljson:function(t){var e=this,a=this.formdata.labeljson.findIndex((function(e){return e.labelid==t.id}));if(console.log(a),-1==a){var i={name:t.labelname,color:t.labelcolor,labelid:t.id};this.formdata.labeljson.push(i)}-1!=a&&this.formdata.labeljson.splice(a,1);var s={labeljson:this.formdata.labeljson,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(s)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改标签失败"),e.bindData())}))},closeLabeljson:function(t){var e=this,a=this.formdata.labeljson.findIndex((function(e){return e.labelid==t.labelid}));-1!=a&&this.formdata.labeljson.splice(a,1);var i={labeljson:this.formdata.labeljson,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(i)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改时间失败"),e.bindData())}))},createLabel:function(){var t=this,e={labelname:this.labelname,labelcolor:this.labelcolor},a="标签创建";if(this.labeloperaType)var i="/S06M02S6/create";else{i="/S06M02S6/update";e.id=this.labelId,a="标签修改"}this.$request.post(i,JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(a+"成功"),t.showSetLbel=!0,t.labelcolor="blue",t.$nextTick((function(){t.getLabelList()}))):t.$message.warning(e.data.msg||a+"失败")}))},editLabel:function(t){console.log(t),this.labelname=t.labelname,this.labelcolor=t.labelcolor,this.labelId=t.id,this.showSetLbel=!1,this.labeloperaType=0},searchLabel:function(){if(this.labelVal){this.labelList=[];for(var t=0;t<this.labelListCopy.length;t++){var e=this.labelListCopy[t];e.labelname.includes(this.labelVal)&&this.labelList.push(e)}}else this.labelList=[].concat(this.labelListCopy)},changeProject:function(t){var e=this;if(""==t.id)var a={projectid:"",itemname:"",itemcode:"",status:null,id:this.formdata.id};else{if(this.formdata.projectid==t.id)return;a={projectid:t.id,itemname:t.projname,itemcode:t.projcode,status:t.status[0].id,id:this.formdata.id}}this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改项目失败"),e.bindData())}))},searchProject:function(){if(this.projectVal){this.projectAllListCopy=[];for(var t=0;t<this.projectAllList.length;t++){var e=this.projectAllList[t];e.projname.includes(this.projectVal)&&this.projectAllListCopy.push(e)}}else this.projectAllListCopy=[].concat(this.projectAllList)},bindRightContent:function(){var t=this;return Object(n["a"])(Object(o["a"])().mark((function e(){var a,i,s,n;return Object(o["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.Participants=[],t.formdata.appointee&&(a={engineername:t.formdata.appointee},t.Participants.push(a)),t.formdata.collaborators)for(i=t.formdata.collaborators.split(","),s=0;s<i.length;s++)n=i[s],a={engineername:n},t.Participants.push(a);case 3:case"end":return e.stop()}}),e)})))()},getLabelList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1};this.$request.post("/S06M02S6/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.labelList=[].concat(e.data.data.list),t.labelListCopy=[].concat(e.data.data.list))}))},getStatusName:function(t){var e="";if(t){var a=this.statusList.findIndex((function(e){return e.id==t}));-1!=a&&(e=this.statusList[a].statusname)}return e}}},d=c,m=(a("c9bd"),a("d36e"),a("2877")),p=Object(m["a"])(d,i,s,!1,null,"403d2814",null);e["a"]=p.exports},a18e:function(t,e,a){},a4bb:function(t,e,a){"use strict";a("716f")},af2b:function(t,e,a){"use strict";a("7de4")},b2f3:function(t,e,a){"use strict";a("2070")},b443:function(t,e,a){},bb4d:function(t,e,a){"use strict";a("0bf6")},c40e:function(t,e,a){},c9bd:function(t,e,a){"use strict";a("f88f")},d36e:function(t,e,a){"use strict";a("a18e")},e232:function(t,e,a){"use strict";a("1b2f")},e7b5:function(t,e,a){"use strict";a("093b")},ea61:function(t,e,a){"use strict";a.d(e,"a",(function(){return i}));var i={formcode:"S06M02B3List",item:[{itemcode:"type",itemname:"类型",minwidth:"60",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center",datasheet:"Sa_DemandSubmit.type"},{itemcode:"description",itemname:"内容描述",minwidth:"180",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.description"},{itemcode:"groupname",itemname:"客户",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.groupname"},{itemcode:"level",itemname:"优先级",minwidth:"60",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.level"},{itemcode:"source",itemname:"来源",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.source"},{itemcode:"status",itemname:"进展阶段",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.status"},{itemcode:"assigneename",itemname:"执行者",minwidth:"80",displaymark:1,overflow:0,datasheet:"Sa_DemandSubmit.assignee"},{itemcode:"participantnames",itemname:"协作者",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.participants"},{itemcode:"deadlinedate",itemname:"截止时间",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.deadlinedate"},{itemcode:"createdate",itemname:"提报时间",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.createdate"},{itemcode:"remark",itemname:"备注",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.remark"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.lister"},{itemcode:"approvalswitch",itemname:"审核",minwidth:"80",displaymark:1,overflow:1,datasheet:""},{itemcode:"assessor",itemname:"审核员",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_DemandSubmit.assessor"}]}},eadf:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate","process"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.printButton()},clickMethods:t.clickMethods}}),a("div",{staticClass:"refNo",staticStyle:{top:"57px",right:"30px","min-width":"396px"}},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.discoverydate,callback:function(e){t.$set(t.formdata,"discoverydate",e)},expression:"formdata.discoverydate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini"},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)])])],1)]),a("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[a("div",{staticClass:"form form-head p-r"},[a("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},t.formtemplate.footer.type?null:{key:"Footer",fn:function(){return[a("div",[a("EditFooter",{attrs:{formdata:t.formdata}})],1)]},proxy:!0}],null,!0)})],1)]),a("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"S06M20B1Edit",examineurl:"/S06M20B1/sendapprovel"}}),t.operationVisible?a("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}}):t._e(),t.processVisible?a("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}}):t._e(),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button-group",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"print"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="print",t.submitRemoteReport()}}},[t._v("云打印")]),a("el-button",{attrs:{type:"preview"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="preview",t.submitRemoteReport()}}},[t._v("云预览")])],1),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("打印")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},s=[],o=(a("b64b"),a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("b775"));const n={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);o["a"].post("/S06M20B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);o["a"].post("/S06M20B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){o["a"].get("/S06M20B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,o["a"].get("/S06M20B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||!t.formdata.assessor?"审核成功":"反审核成功"),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||!t.formdata.assessor?"审核失败":"反审核失败"))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,a){if(1==t||2==t)var i=1==t?"作废":"启用",s="/S06M20B1/disannul?type="+(1==t?1:0);else i=3==t?"中止":"启用",s="/S06M20B1/closed?type="+(3==t?1:0);o["a"].post(s,JSON.stringify(e)).then(t=>{200==t.data.code?(a.$message.success(t.data.msg||i+"成功"),a.$emit("bindData"),a.formdata=t.data.data):a.$message.warning(t.data.msg||i+"失败")}).catch(t=>{a.$message.error(t||"服务请求错误")})}};var l=n,r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("softwarename")}}},[a("el-form-item",{attrs:{label:"软件名称",prop:"softwarename"}},[a("el-popover",{attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}},model:{value:t.selnamevisible,callback:function(e){t.selnamevisible=e},expression:"selnamevisible"}},[a("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_DefectReport.softwarename"},on:{singleSel:function(e){t.selnamevisible=!1,t.formdata.softwarename=e.dictvalue,t.cleValidate("softwarename")},closedic:function(e){t.selnamevisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择软件名称",clearable:"",size:"small"},model:{value:t.formdata.softwarename,callback:function(e){t.$set(t.formdata,"softwarename",e)},expression:"formdata.softwarename"}})],1)],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"版本"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入版本",clearable:"",size:"small"},model:{value:t.formdata.softwareversion,callback:function(e){t.$set(t.formdata,"softwareversion",e)},expression:"formdata.softwareversion"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("defecttype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"defecttype"}},[a("el-popover",{attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.seltypeRef.bindData()}},model:{value:t.seltypevisible,callback:function(e){t.seltypevisible=e},expression:"seltypevisible"}},[a("selDictionaries",{ref:"seltypeRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_DefectReport.defecttype"},on:{singleSel:function(e){t.seltypevisible=!1,t.formdata.defecttype=e.dictvalue,t.cleValidate("defecttype")},closedic:function(e){t.seltypevisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:t.formdata.defecttype,callback:function(e){t.$set(t.formdata,"defecttype",e)},expression:"formdata.defecttype"}})],1)],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"严重程度",prop:"severity"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择严重程度",size:"small"},model:{value:t.formdata.severity,callback:function(e){t.$set(t.formdata,"severity",e)},expression:"formdata.severity"}},[a("el-option",{attrs:{label:"轻微",value:0}}),a("el-option",{attrs:{label:"一般",value:1}}),a("el-option",{attrs:{label:"严重",value:2}})],1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"优先级"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入优先级",clearable:"",size:"small"},model:{value:t.formdata.priority,callback:function(e){t.$set(t.formdata,"priority",e)},expression:"formdata.priority"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("testenvironment")}}},[a("el-form-item",{attrs:{label:"测试环境"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入测试环境",size:"small"},model:{value:t.formdata.testenvironment,callback:function(e){t.$set(t.formdata,"testenvironment",e)},expression:"formdata.testenvironment"}})],1)],1)]),a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("description")}}},[a("el-form-item",{attrs:{label:"缺陷描述"}},[a("el-input",{attrs:{placeholder:"请输入缺陷描述",size:"small",type:"textarea"},model:{value:t.formdata.description,callback:function(e){t.$set(t.formdata,"description",e)},expression:"formdata.description"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"flex-end"}},[a("h5",[t._v("步骤重现")])]),a("MyEditor",{ref:"MyEditor",attrs:{height:325,html:t.formdata.reproductionsteps,excludeKeys:["group-image","insertImage","uploadImage","insertLink","group-video","insertVideo","uploadVideo","fullScreen"]},on:{changeHtml:function(e){t.formdata.reproductionsteps=e}}})],1)],1)],1)},c=[],d=a("5c73"),m=a("1975"),p={props:{formdata:{type:Object},title:{type:String}},components:{selDictionaries:d["a"],MyEditor:m["a"]},data:function(){return{formRules:{softwarename:[{required:!0,trigger:"blur",message:"软件名称"}]},selnamevisible:!1,seltypevisible:!1}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},f=p,u=(a("8b05"),a("2877")),h=Object(u["a"])(f,r,c,!1,null,"f9077dce",null),g=h.exports,v=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{staticClass:"footFormContent",attrs:{"label-width":"100px"}},[a("el-divider"),a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:18}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"测试人员"}},[a("el-input",{attrs:{placeholder:"请输入测试人员",clearable:"",size:"small"},model:{value:t.formdata.tester,callback:function(e){t.$set(t.formdata,"tester",e)},expression:"formdata.tester"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)},b=[],y={props:{formdata:{type:Object}},data:function(){return{salesmanRefsVisible:!1}}},w=y,S=Object(u["a"])(w,v,b,!1,null,"520bd3d8",null),x=S.exports,_=a("dcb4"),k=["id","refno","softwarename","softwareversion","discoverydate","tester","description","attachment","defecttype","severity","priority","testenvironment","reproductionsteps","remark","custom1","custom2","custom3","custom4","custom5"],$={params:k},C=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],j=[],D={header:{type:0,title:"缺陷报告",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"其他发货",value:"其他发货"},{label:"其他退货",value:"其他退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:""},{col:5,code:"linkman",label:"联系人",type:"input",methods:"",param:""},{col:5,code:"telephone",label:"联系电话",type:"input",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]}},L=(new Date,{name:"Formedit",components:{FormTemp:_["a"],EditHeader:g,EditFooter:x},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"缺陷报告",operateBar:C,processBar:j,formdata:{refno:"",softwarename:"",softwareversion:"",discoverydate:new Date,tester:"",description:"",defecttype:"",severity:0,priority:"",testenvironment:"",reproductionsteps:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:D,formstate:0,submitting:0,ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,printType:"print"}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){this.formtemplate=D},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&o["a"].get("/S06M20B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){},autoClear:function(){},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this,e={};e=this.$getParam($,e,this.formdata),this.submitting=1,0==this.idx?l.add(e).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):l.update(e).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.delete(t)})).catch((function(){}))},approval:function(){l.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var a=this.$refs.elitem.multipleSelection;2==t||4==t?l.reqitembill(t,a,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){l.reqitembill(t,a,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processModel=t.code,this.processTitle=t.label},changeBillType:function(){formdata.item=[]},changeIdx:function(t){this.dialogIdx=t},billSwitch:function(t){},printButton:function(){var t=this;o["a"].get("/SaReports/getListByModuleCode?code=S06M20B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?o["a"].get("/S06M20B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;if(""!=this.reportModel){var e="/S06M20B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel;"preview"==this.printType&&(e="/S06M20B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel+"&cmd=1"),o["a"].get(e).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")}))}else this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.formdata.printcount+=1,this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},fastKey:function(){var t=this;document.onkeydown=function(e){var a=e.keyCode;83==a&&e.ctrlKey?e.preventDefault():27==a&&(e.preventDefault(),t.closeForm())}}}}),M=L,T=(a("a4bb"),Object(u["a"])(M,i,s,!1,null,"28944749",null));e["a"]=T.exports},f88f:function(t,e,a){}}]);