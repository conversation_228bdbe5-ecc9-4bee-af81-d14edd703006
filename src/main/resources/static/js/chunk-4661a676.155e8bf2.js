(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4661a676"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function n(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=s(),l=t-o,r=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=r;var t=Math.easeInOutQuad(c,o,l,e);n(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"0e7b":function(t,e,a){},"0f70":function(t,e,a){"use strict";a("0e7b")},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"26cf":function(t,e,a){},"30ea":function(t,e,a){},"46b2":function(t,e,a){},"4b65":function(t,e,a){"use strict";a("9b3b")},"5c73":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,i){return a("li",{key:i,class:t.radio==i?"active":"",on:{click:function(a){t.getCurrentRow(e),t.radio=i}}},[a("span",[t._v(t._s(e.dictvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,i){return a("p",{key:i,class:t.ActiveIndex==i?"isActive":"",on:{click:function(e){t.ActiveIndex=i}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},n=[],s=(a("a434"),a("e9c4"),a("b775")),o=a("333d"),l=a("b0b8"),r={components:{Pagination:o["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],s["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var a=0;a<t.formdata.item.length;a++)t.lst.push(t.formdata.item[a])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.addItem(a)})).catch((function(t){console.log(t)}))},addItem:function(t){l.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:l.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.lst[t.ActiveIndex].dictvalue=a,l.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=l.getFullChars(a)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,s["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=r,d=(a("af2b"),a("2877")),m=Object(d["a"])(c,i,n,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"624d":function(t,e,a){},"6c579":function(t,e,a){"use strict";a("624d")},"773c":function(t,e,a){},"7de4":function(t,e,a){},"7eb1":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnadd:function(e){return t.showform(0)},bindData:function(e){return t.$refs.tableList.bindData()},btnsearch:t.search,advancedSearch:t.advancedSearch,btnExport:function(e){return t.$refs.tableList.btnExport()},btnHelp:t.btnHelp,changeModelUrl:t.changeModelUrl,changeBalance:t.changeBalance,bindColumn:function(e){return t.$refs.tableList.getColumn()}}}),a("el-row",[a("el-col",{attrs:{span:t.showHelp?20:24}},[a("TableList",{ref:"tableList",on:{changeIdx:t.changeIdx,showform:t.showform,sendTableForm:t.sendTableForm,openDetail:t.openDetail,openApiDetail:t.openApiDetail}})],1)],1)],1),t.FormVisible?a("el-dialog",{attrs:{width:"600px",title:"新增功能","append-to-body":!0,visible:t.FormVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.FormVisible=e},close:t.bindData}},[a("FormEdit",{ref:"formedit",attrs:{idx:t.idx},on:{bindData:t.bindData,changeIdx:t.changeIdx,closeDialog:function(e){t.FormVisible=!1}}})],1):t._e(),t.drawformvisible?a("el-drawer",{attrs:{visible:t.drawformvisible,"with-header":!1,size:"80%"},on:{"update:visible":function(e){t.drawformvisible=e}}},[a("formDraw",t._g({ref:"formDraw",attrs:{idx:t.idx,drawType:t.drawType}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e()],1)},n=[],s=(a("ac1f"),a("841c"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("functionname")}}},[a("el-form-item",{attrs:{label:"功能名称",prop:"functionname"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"输入功能名称"},model:{value:t.formdata.functionname,callback:function(e){t.$set(t.formdata,"functionname",e)},expression:"formdata.functionname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("functioncode")}}},[a("el-form-item",{attrs:{label:"功能编码"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入功能编码"},model:{value:t.formdata.functioncode,callback:function(e){t.$set(t.formdata,"functioncode",e)},expression:"formdata.functioncode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("functioncode")}}},[a("el-form-item",{attrs:{label:"功能标题"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入功能标题"},model:{value:t.formdata.functiontitle,callback:function(e){t.$set(t.formdata,"functiontitle",e)},expression:"formdata.functiontitle"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("functioncode")}}},[a("el-form-item",{attrs:{label:"项目"}},[a("autoComplete",{attrs:{size:"default",value:t.formdata.projname,baseurl:"/S06M13B1/getPageList",params:{name:"goodsname",other:""}},on:{setRow:function(e){return t.setRow(e)},autoClear:function(e){return t.autoClear()}}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"功能描述","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入功能描述",clearable:""},model:{value:t.formdata.functiondesc,callback:function(e){t.$set(t.formdata,"functiondesc",e)},expression:"formdata.functiondesc"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent"},[a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",clearable:""},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1)],1),a("el-row",[a("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px"},attrs:{span:24}},[a("div",[t.formdata.id?a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.deleteBtn()}}},[t._v("删除")]):t._e()],1),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("formdata")}}},[t._v("保存")]),a("el-button",{on:{click:t.closeDialog}},[t._v("关闭")])],1)])],1)],1)])])}),o=[],l=(a("b64b"),a("b775"));const r={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);l["a"].post("/S06M38B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);l["a"].post("/S06M38B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{l["a"].get("/S06M38B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var c=r,d=a("5b24"),m={name:"addDialog",props:["idx"],components:{autoComplete:d["a"]},data:function(){return{title:"",currentId:this.idx,formdata:{id:"",functioncode:"",functionname:"",functiontitle:"",functiondesc:"",projectid:"",projname:"",billdate:new Date,createdate:new Date,remark:"",rownum:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{functionname:[{required:!0,trigger:"blur",message:"名称不能为空"}]}}},watch:{idx:function(t,e){this.currentId=t,this.binddata()}},created:function(){this.binddata()},methods:{binddata:function(){var t=this;0!=this.currentId&&l["a"].get("/S06M38B1/getEntity?key=".concat(this.currentId)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeDialog()}})})).catch((function(e){t.$message.error(e||"请求错误")}))},submitForm:function(t){var e=this;this.$refs["formdata"].validate((function(t){if(!t)return!1;e.saveForm()}))},saveForm:function(){var t=this;if(0==this.idx){var e=Object.assign({},this.formdata);c.add(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.formdata=e.data.data,t.$emit("bindData"),t.closeDialog())})).catch((function(e){t.$message.warning(e||"保存失败")}))}else c.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"))})).catch((function(e){t.$message.warning(e||"保存失败")}))},deleteBtn:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){c.delete(t.formdata.id).then((function(e){200==e.code&&t.$message.success("删除成功"),t.$emit("bindData"),t.closeDialog()})).catch((function(){t.$message.warning("删除失败")}))})).catch((function(){}))},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},setRow:function(t){this.formdata.projectid=t.id,this.formdata.projname=t.goodsname},autoClear:function(){this.formdata.projectid="",this.formdata.projname=""}}},u=m,f=(a("c50f"),a("2877")),p=Object(f["a"])(u,s,o,!1,null,"b5e20f6e",null),h=p.exports,b=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)],1)},w=[],v=a("c7eb"),g=a("1da1"),y=(a("e9c4"),a("a9e3"),a("d3b7"),a("c7cd"),a("0643"),a("4e3e"),a("159b"),{formcode:"S06M38B1List",item:[{itemcode:"functioncode",itemname:"功能编码",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Sa_Function.functioncode"},{itemcode:"functionname",itemname:"功能名称",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_Function.functionname"},{itemcode:"functiontitle",itemname:"功能标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Function.functiontitle"},{itemcode:"functiondesc",itemname:"功能描述",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Function.functiondesc"},{itemcode:"projname",itemname:"项目",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Function.projname"},{itemcode:"rownum",itemname:"行号",minwidth:"60",displaymark:1,overflow:1,datasheet:"Sa_Function.rownum"},{itemcode:"lister",itemname:"制表",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Function.lister"},{itemcode:"remark",itemname:"备注",minwidth:"100",defwidth:"",displaymark:1,overflow:1,aligntype:"center",datasheet:"Sa_Function.remark"},{itemcode:"operate",itemname:"操作",minwidth:"150",displaymark:1,overflow:1}]}),x={components:{},props:[],data:function(){var t=this;return{lst:[],total:0,listLoading:!1,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:y,customList:[],selectList:[],totalfields:[],exportitle:"创意池",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var a=e.startRowIndex;t.rowScroll=a}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var a=e.selectedRowKeys;if(t.selectList=[],0!=a.length)for(var i=0;i<a.length;i++)t.selectList.push({id:a[i]})},selectedAllChange:function(e){var a=e.isSelected;e.selectedRowKeys;t.selectList=a?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(t){t.row,t.column,t.rowIndx;return{mouseup:function(t){}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var a=this.tableForm.item[e];a.displaymark&&(t+=Number(a.minwidth))}}return t}},methods:{bindData:function(){var t=this;this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1));var e="/S06M38B1/getPageList";this.$request.post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"查询失败"),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getColumn:function(){var t=this;return Object(g["a"])(Object(v["a"])().mark((function e(){return Object(v["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm);case 2:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,a=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,i){var n={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(a,i){var n=a.row,s=(a.column,a.rowIndex,"");return"billdate"==t.itemcode||"createdate"==t.itemcode?e.$options.filters.dateFormat(n[t.itemcode]):"functioncode"==t.itemcode?(s=i("el-button",{attrs:{type:"text",size:"small",title:n[t.itemcode]},on:{click:function(){return e.showform(n.id)}}},[n[t.itemcode]?n[t.itemcode]:"编码"]),s):"operate"==t.itemcode?(s=i("div",[i("el-button",{attrs:{type:"text"},on:{click:function(){return e.openDetail(n)}}},["数据表设计"]),i("el-button",{attrs:{type:"text"},on:{click:function(){return e.openApiDetail(n)}}},["接口信息"])]),s):n[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),a.push(n)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,a){t.row,t.column;var i=t.rowIndex;return i+e.rowScroll+1}}),this.customData=a,this.keynum+=1},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},handleSelectionChange:function(t){this.selectList=t},search:function(t){""!=t?this.queryParams.SearchPojo={functioncode:t,functionname:t,functiontitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData(this.projectRow)},AdvancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){for(var e in row)if(""!=row[e]){t={prop:e};"desc"==row[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(t,this.tableForm),this.bindData();break}},showform:function(t){this.$emit("showform",t)},openDetail:function(t){this.$emit("openDetail",t)},openApiDetail:function(t){this.$emit("openApiDetail",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)}}},S=x,k=(a("6c579"),Object(f["a"])(S,b,w,!1,null,"19e71d9f",null)),I=k.exports,$=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-search",title:"高级筛选",type:t.$store.state.advancedSearch.modulecode==t.tableForm.formcode?"primary":"default"},on:{click:function(e){return t.openSearchForm()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:function(e){return t.$emit("btnExport")}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,baseparam:"/SaDgFormat",tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[a("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode,baseparam:"/SaScene"},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)},T=[],R={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},thorList:!0,balance:!1,setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},advancedSearch:function(){this.$emit("advancedSearch",this.formdata),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},_=R,D=(a("0f70"),Object(f["a"])(_,$,T,!1,null,"109ae4d0",null)),C=D.exports,z=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"6px",right:"30px"}},[a("div",{staticClass:"flex"},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:[""],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{closeForm:t.closeForm,clickMethods:t.clickMethods}})],1)])]),a("div",{staticStyle:{padding:"15px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r xsContent"},[a("div",{staticStyle:{"font-size":"18px",color:"rgb(107, 119, 140)"}},[t._v("功能中心")]),a("div",{staticClass:"xsInfo"},[a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("功能编码")]),a("div",[t._v(t._s(t.formdata.functioncode||"-"))])]),a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("功能名称")]),a("div",[t._v(t._s(t.formdata.functionname||"-"))])]),a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("功能标题")]),a("div",[t._v(t._s(t.formdata.functiontitle||"-"))])]),a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("创建时间")]),a("div",[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])]),a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("制表")]),a("div",[t._v(t._s(t.formdata.lister||"-"))])])]),"table"==t.drawType?a("div",{staticClass:"xsBody"},[a("EditItem",{ref:"elitem",style:{width:"99%",height:"100%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData}})],1):a("div",{staticClass:"xsBody"},[a("ApiItem",{ref:"apiItem",style:{width:"99%",height:"100%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,idx:t.idx},on:{bindData:t.bindData}})],1)])])])])},B=[],O=[{show:1,divided:!1,label:"新增表格设计",icon:"el-icon-plus",disabled:"this.formstate!=1",methods:"addTabs",param:"",children:[]}],V=[],F=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"btngroup"},[a("div",{staticClass:"btngroupItem el-icon-plus",on:{click:t.addTabs}},[t._v("新增设计")]),a("div",{staticClass:"btngroupItem el-icon-edit",on:{click:t.editTabs}},[t._v("编辑设计")]),a("div",{staticClass:"btngroupItem el-icon-download",on:{click:t.downloadTabs}},[t._v(" 导入设计 ")]),a("div",{staticClass:"btngroupItem el-icon-refresh-right",on:{click:t.bindData}},[t._v(" 刷新 ")])]),t.list.length?a("div",[a("el-tabs",{staticStyle:{height:"calc(100vh - 200px)"},attrs:{type:"border-card",closable:""},on:{"tab-remove":t.removeTab},model:{value:t.tabsValue,callback:function(e){t.tabsValue=e},expression:"tabsValue"}},t._l(t.list,(function(e,i){return a("el-tab-pane",{key:i,attrs:{label:e.comment,name:e.id}},[a("div",[a("div",{staticClass:"tabsContentHead"},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[a("div",{staticClass:"formItemSty"},[a("el-form-item",{staticStyle:{"margin-bottom":"6px"},attrs:{label:"表名"}},[t._v(" "+t._s(e.tablename)+" ")]),a("el-form-item",{staticStyle:{"margin-bottom":"6px"},attrs:{label:"表名注释"}},[t._v(" "+t._s(e.comment)+" ")]),a("el-form-item",{staticStyle:{"margin-bottom":"6px"},attrs:{label:"版本号"}},[t._v(" "+t._s(e.versionnumber)+" ")]),a("el-form-item",{staticStyle:{"margin-bottom":"6px"},attrs:{label:"备注"}},[t._v(" "+t._s(e.remark)+" ")])],1)])],1),a("div",[a("div",[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(a){return t.addItemBtn(e)}}},[t._v(" 新增")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-edit"},nativeOn:{click:function(a){return t.editItemBtn(e)}}},[t._v(" 编辑")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},nativeOn:{click:function(a){return t.delItemBtn(e)}}},[t._v(" 删除")])],1)],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.item,height:"calc(100vh - 350px)"},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{prop:"fieldname",label:"字段名"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",{style:{color:e.row.indexmark?"#108ee9":"#606266"}},[t._v(t._s(e.row.fieldname))])]}}],null,!0)}),a("el-table-column",{attrs:{prop:"fieldtype",label:"字段类型"}}),a("el-table-column",{attrs:{prop:"comment",label:"字段描述"}}),a("el-table-column",{attrs:{prop:"defvalue",label:"默认值"}}),a("el-table-column",{attrs:{prop:"primarymark",label:"主键",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.primarymark?a("el-tag",{attrs:{type:"success",size:"small"}},[t._v("是")]):t._e()]}}],null,!0)}),a("el-table-column",{attrs:{prop:"notnullmark",label:"非null",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.notnullmark?a("el-tag",{attrs:{type:"success",size:"small"}},[t._v("空")]):t._e()]}}],null,!0)}),a("el-table-column",{attrs:{prop:"calcmark",label:"是否计算",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[1==e.row.calcmark?a("el-tag",{attrs:{type:"success",size:"small"}},[t._v("是")]):t._e()]}}],null,!0)}),a("el-table-column",{attrs:{prop:"calcdesc",label:"计算描述"}}),a("el-table-column",{attrs:{prop:"calcformula",label:"计算公式"}}),a("el-table-column",{attrs:{prop:"characterset",label:"字符集"}}),a("el-table-column",{attrs:{prop:"collation",label:"排序规则"}}),a("el-table-column",{attrs:{prop:"remark",label:"备注"}})],1)],1)])])})),1)],1):a("el-empty",{attrs:{description:"暂无数据，点击按钮新增"}}),a("el-dialog",{attrs:{"close-on-click-modal":!1,title:"表格设计",visible:t.addTabsVissable,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.addTabsVissable=e}}},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"表名"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入表名"},model:{value:t.selTabsRow.tablename,callback:function(e){t.$set(t.selTabsRow,"tablename",e)},expression:"selTabsRow.tablename"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"表名注释"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入表名注释"},model:{value:t.selTabsRow.comment,callback:function(e){t.$set(t.selTabsRow,"comment",e)},expression:"selTabsRow.comment"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"字符集"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入字符集"},model:{value:t.selTabsRow.characterset,callback:function(e){t.$set(t.selTabsRow,"characterset",e)},expression:"selTabsRow.characterset"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"排序规则"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入排序规则"},model:{value:t.selTabsRow.collation,callback:function(e){t.$set(t.selTabsRow,"collation",e)},expression:"selTabsRow.collation"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"版本号"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入版本号"},model:{value:t.selTabsRow.versionnumber,callback:function(e){t.$set(t.selTabsRow,"versionnumber",e)},expression:"selTabsRow.versionnumber"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,size:"default"},model:{value:t.selTabsRow.rownum,callback:function(e){t.$set(t.selTabsRow,"rownum",e)},expression:"selTabsRow.rownum"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入备注"},model:{value:t.selTabsRow.remark,callback:function(e){t.$set(t.selTabsRow,"remark",e)},expression:"selTabsRow.remark"}})],1)],1),a("div",{staticStyle:{"margin-top":"15px"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"default"},nativeOn:{click:function(e){return t.btnAddTagsSumbit()}}},[t._v("确 认")]),a("el-button",{attrs:{size:"default"},on:{click:function(e){t.addTabsVissable=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{attrs:{"close-on-click-modal":!1,title:"子表设计",visible:t.addItemVissable,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.addItemVissable=e}}},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"字段名"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入字段名"},model:{value:t.selItemRow.fieldname,callback:function(e){t.$set(t.selItemRow,"fieldname",e)},expression:"selItemRow.fieldname"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"字段类型"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入字段类型"},model:{value:t.selItemRow.fieldtype,callback:function(e){t.$set(t.selItemRow,"fieldtype",e)},expression:"selItemRow.fieldtype"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"字段描述"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入字段描述"},model:{value:t.selItemRow.comment,callback:function(e){t.$set(t.selItemRow,"comment",e)},expression:"selItemRow.comment"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"默认值"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入默认值"},model:{value:t.selItemRow.defvalue,callback:function(e){t.$set(t.selItemRow,"defvalue",e)},expression:"selItemRow.defvalue"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:""}},[a("div",{staticClass:"flex"},[a("el-checkbox",{attrs:{label:"主键","true-label":1,"false-label":0,size:"mini"},model:{value:t.selItemRow.primarymark,callback:function(e){t.$set(t.selItemRow,"primarymark",e)},expression:"selItemRow.primarymark"}}),a("el-checkbox",{attrs:{label:"非null","true-label":1,"false-label":0,size:"mini"},model:{value:t.selItemRow.notnullmark,callback:function(e){t.$set(t.selItemRow,"notnullmark",e)},expression:"selItemRow.notnullmark"}}),a("el-checkbox",{attrs:{label:"计算mark","true-label":1,"false-label":0,size:"mini"},model:{value:t.selItemRow.calcmark,callback:function(e){t.$set(t.selItemRow,"calcmark",e)},expression:"selItemRow.calcmark"}})],1)]),t.selItemRow.calcmark?a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"计算公式"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入计算公式"},model:{value:t.selItemRow.calcformula,callback:function(e){t.$set(t.selItemRow,"calcformula",e)},expression:"selItemRow.calcformula"}})],1):t._e(),t.selItemRow.calcmark?a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"计算描述"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入计算描述"},model:{value:t.selItemRow.calcdesc,callback:function(e){t.$set(t.selItemRow,"calcdesc",e)},expression:"selItemRow.calcdesc"}})],1):t._e(),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"字符集"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入字符集"},model:{value:t.selItemRow.characterset,callback:function(e){t.$set(t.selItemRow,"characterset",e)},expression:"selItemRow.characterset"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"排序规则"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入排序规则"},model:{value:t.selItemRow.collation,callback:function(e){t.$set(t.selItemRow,"collation",e)},expression:"selItemRow.collation"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,size:"default"},model:{value:t.selItemRow.rownum,callback:function(e){t.$set(t.selItemRow,"rownum",e)},expression:"selItemRow.rownum"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入备注"},model:{value:t.selItemRow.remark,callback:function(e){t.$set(t.selItemRow,"remark",e)},expression:"selItemRow.remark"}})],1)],1),a("div",{staticStyle:{"margin-top":"15px"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"default"},nativeOn:{click:function(e){return t.btnAddItemSumbit()}}},[t._v("确 认")]),a("el-button",{attrs:{size:"default"},on:{click:function(e){t.addItemVissable=!1}}},[t._v("取 消")])],1)],1)],1)},P=[],L=a("b85c"),M=(a("99af"),a("c740"),a("3ca3"),a("ddb0"),{name:"formadd",props:["idx","formdata"],components:{},data:function(){return{tabsValue:"",list:[],addTabsVissable:!1,selTabsRow:{},selTabsType:"add",addItemVissable:!1,selItemRow:{},selItemType:"add",selectList:[]}},mounted:function(){},methods:{bindData:function(){var t=this,e={PageNum:1,PageSize:20,OrderType:0,SearchType:1,SearchPojo:{fnid:this.formdata.id},OrderBy:"rownum"};this.$request.post("/S06M39B1/getBillList",JSON.stringify(e)).then((function(e){if(200==e.data.code&&(t.list=[].concat(e.data.data.list),t.list.length)){if(""!=t.tabsValue){var a=t.list.findIndex((function(e){return e.id==t.tabsValue}));if(-1!=a)return void(t.tabsValue=t.list[a].id)}t.tabsValue=t.list[0].id}}))},btnAddTagsSumbit:function(){var t=this,e="add"==this.selTabsType?"/S06M39B1/create":"/S06M39B1/update";this.$request.post(e,JSON.stringify(this.selTabsRow)).then((function(e){200==e.data.code?(t.bindData(),t.addTabsVissable=!1):t.$message.warning(e.data.msg||"操作失败")}))},addTabs:function(){this.selTabsRow={fnid:this.formdata.id,characterset:"utf8mb3",collation:"utf8mb3_bin",versionnumber:this.$options.filters.dateFormat(new Date),rownum:this.list.length?this.list.length:0},this.selTabsType="add",this.addTabsVissable=!0},editTabs:function(){var t=this;console.log(this.tabsValue);var e=this.list.findIndex((function(e){return e.id==t.tabsValue}));-1!=e&&(this.selTabsRow=Object.assign({},this.list[e]),this.selTabsType="edit",this.addTabsVissable=!0)},removeTab:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.deleteRows(t)}))},deleteRows:function(t){var e=this;this.$request.get("/S06M39B1/delete?key="+t).then((function(t){200==t.data.code?(e.bindData(),e.$message.success("操作成功")):e.$message.warning(t.data.msg||"操作失败")}))},handleSelectionChange:function(t){console.log(t),this.selectList=t},addItemBtn:function(t){this.selItemRow={pid:t.id,primarymark:0,notnullmark:0,calcmark:0,characterset:"utf8mb3",collation:"utf8mb3_bin",rownum:t.item.length},this.selItemType="add",this.addItemVissable=!0},editItemBtn:function(t){1==this.selectList.length?(this.selItemRow=Object.assign({},this.selectList[0]),this.selItemType="edit",this.addItemVissable=!0,this.$forceUpdate()):this.$message.warning("请选择一条数据")},btnAddItemSumbit:function(){var t=this,e="add"==this.selItemType?"/S06M39B1/createItem":"/S06M39B1/updateItem";this.$request.post(e,JSON.stringify(this.selItemRow)).then((function(e){200==e.data.code?(t.bindData(),t.addItemVissable=!1):t.$message.warning(e.data.msg||"操作失败")}))},delItemBtn:function(){var t=this;this.selectList.length?this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteItemRows()})):this.$message.warning("请选择要删除的数据")},deleteItemRows:function(){var t=this;return Object(g["a"])(Object(v["a"])().mark((function e(){var a,i,n,s,o;return Object(v["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=t,i=[],n=Object(L["a"])(t.selectList),e.prev=3,o=Object(v["a"])().mark((function e(){var a,n;return Object(v["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=s.value,n=new Promise((function(e,i){t.$request.get("/S06M39B1/deleteItem?key="+a.id).then((function(t){200==t.data.code?e("删除成功"):i("删除失败")}))})),i.push(n);case 3:case"end":return e.stop()}}),e)})),n.s();case 6:if((s=n.n()).done){e.next=10;break}return e.delegateYield(o(),"t0",8);case 8:e.next=6;break;case 10:e.next=15;break;case 12:e.prev=12,e.t1=e["catch"](3),n.e(e.t1);case 15:return e.prev=15,n.f(),e.finish(15);case 18:return e.next=20,Promise.all(i).then((function(t){a.$message.success("删除成功"),a.bindData()})).catch((function(t){a.$message.warning("删除失败"),a.bindData()}));case 20:case"end":return e.stop()}}),e,null,[[3,12,15,18]])})))()},downloadTabs:function(){var t=this;this.$prompt("","建表SQL",{confirmButtonText:"提交",cancelButtonText:"关闭",closeOnClickModal:!1,inputType:"textarea",inputPlaceholder:"请输入SQL语句",inputPattern:/\S+/,inputErrorMessage:"内容不能为空",customClass:"customTextarea"}).then((function(e){var a=e.value;t.downloadItemFun(a)})).catch((function(){}))},downloadItemFun:function(t){var e=this;this.$request.post("/S06M39B1/saveTable?fnid="+this.formdata.id,t).then((function(t){200==t.data.code?(e.$message.success("数据表导入成功"),e.bindData()):e.$message.warning(t.data.msg||"数据表导入失败")})).catch((function(t){e.$message.error(t||"请求错误")}))}}}),E=M,j=(a("99bf"),a("eeed"),Object(f["a"])(E,F,P,!1,null,"1d499e02",null)),q=j.exports,A=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",[a("div",{staticClass:"flex j-s"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-circle-plus-outline"},nativeOn:{click:function(e){return t.addItemBtn()}}},[t._v(" 新增")]),a("el-button",{attrs:{type:"success",size:"mini",icon:"el-icon-upload2"},nativeOn:{click:function(e){return t.exportItemBtn()}}},[t._v(" swagger导入")]),a("el-button",{attrs:{type:"danger",size:"mini",icon:"el-icon-delete"},nativeOn:{click:function(e){return t.delItemBtn()}}},[t._v(" 删除")])],1),a("el-button",{attrs:{icon:"el-icon-refresh-right",size:"mini"},on:{click:t.bindData}},[t._v(" 刷新 ")])],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:t.list,height:"calc(100vh - 250px)"},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"55"}}),a("el-table-column",{attrs:{prop:"apiname",label:"接口名称"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(a){return t.editItemBtn(e.row)}}},[t._v(" "+t._s(e.row.apiname)+" ")])]}}])}),a("el-table-column",{attrs:{prop:"apidescription",label:"接口描述"}}),a("el-table-column",{attrs:{prop:"apiurl",label:"接口路径"}}),a("el-table-column",{attrs:{prop:"httpmethod",label:"请求方式",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return["get"==e.row.httpmethod.toLowerCase()?a("el-tag",{attrs:{type:"primary",size:"small"}},[t._v("GET")]):"post"==e.row.httpmethod.toLowerCase()?a("el-tag",{attrs:{type:"success",size:"small"}},[t._v("POST")]):"put"==e.row.httpmethod.toLowerCase()?a("el-tag",{attrs:{type:"warning",size:"small"}},[t._v("PUT")]):"delete"==e.row.httpmethod.toLowerCase()?a("el-tag",{attrs:{type:"error",size:"small"}},[t._v("DELETE")]):t._e()]}}])}),a("el-table-column",{attrs:{prop:"requestparams",label:"请求参数",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"responseparams",label:"响应参数",align:"center","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"responseexample",label:"响应示例","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{prop:"statuscode",label:"响应状态码"}}),a("el-table-column",{attrs:{prop:"curl",label:"Curl"}}),a("el-table-column",{attrs:{prop:"rownum",label:"行号"}}),a("el-table-column",{attrs:{prop:"remark",label:"备注"}})],1)],1),a("el-dialog",{attrs:{"close-on-click-modal":!1,title:"接口信息",visible:t.addItemVissable,width:"600px","append-to-body":""},on:{"update:visible":function(e){t.addItemVissable=e}}},[a("el-form",{attrs:{size:"small","label-width":"100px"}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"接口名称"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入接口名称"},model:{value:t.selItemRow.apiname,callback:function(e){t.$set(t.selItemRow,"apiname",e)},expression:"selItemRow.apiname"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"接口描述"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入接口描述"},model:{value:t.selItemRow.apidescription,callback:function(e){t.$set(t.selItemRow,"apidescription",e)},expression:"selItemRow.apidescription"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"接口路径"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入接口路径"},model:{value:t.selItemRow.apiurl,callback:function(e){t.$set(t.selItemRow,"apiurl",e)},expression:"selItemRow.apiurl"}},[a("el-select",{staticStyle:{width:"100px"},attrs:{slot:"prepend",placeholder:"请选择"},slot:"prepend",model:{value:t.selItemRow.httpmethod,callback:function(e){t.$set(t.selItemRow,"httpmethod",e)},expression:"selItemRow.httpmethod"}},[a("el-option",{attrs:{label:"GET",value:"get"}}),a("el-option",{attrs:{label:"POST",value:"post"}}),a("el-option",{attrs:{label:"PUT",value:"put"}}),a("el-option",{attrs:{label:"DELETE",value:"delete"}})],1)],1)],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"请求参数"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入请求参数"},model:{value:t.selItemRow.requestparams,callback:function(e){t.$set(t.selItemRow,"requestparams",e)},expression:"selItemRow.requestparams"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"响应参数"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入响应参数"},model:{value:t.selItemRow.responseparams,callback:function(e){t.$set(t.selItemRow,"responseparams",e)},expression:"selItemRow.responseparams"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"响应示例"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入响应示例"},model:{value:t.selItemRow.responseexample,callback:function(e){t.$set(t.selItemRow,"responseexample",e)},expression:"selItemRow.responseexample"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"响应状态码"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入响应状态码"},model:{value:t.selItemRow.statuscode,callback:function(e){t.$set(t.selItemRow,"statuscode",e)},expression:"selItemRow.statuscode"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"Curl"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入Curl"},model:{value:t.selItemRow.curl,callback:function(e){t.$set(t.selItemRow,"curl",e)},expression:"selItemRow.curl"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"行号"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,size:"default"},model:{value:t.selItemRow.rownum,callback:function(e){t.$set(t.selItemRow,"rownum",e)},expression:"selItemRow.rownum"}})],1),a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"备注"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{size:"default",placeholder:"请输入备注"},model:{value:t.selItemRow.remark,callback:function(e){t.$set(t.selItemRow,"remark",e)},expression:"selItemRow.remark"}})],1)],1),a("div",{staticStyle:{"margin-top":"15px"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"default"},nativeOn:{click:function(e){return t.btnAddItemSumbit()}}},[t._v("确 认")]),a("el-button",{attrs:{size:"default"},on:{click:function(e){t.addItemVissable=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{attrs:{"close-on-click-modal":!1,title:"swagger导入",visible:t.swaggerExportVissable,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.swaggerExportVissable=e}}},[a("el-form",{attrs:{"label-width":"0"}},[a("el-form-item",{staticStyle:{"margin-bottom":"10px"},attrs:{label:""}},[a("el-popover",{ref:"dictionaryRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictRef.bindData()}}},[a("selDict",{ref:"selDictRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_ApiInfo.swaggerUrl"},on:{singleSel:function(e){t.$refs["dictionaryRef"].doClose(),t.swaggerUrl=e.dictvalue,t.cleValidate("defecttype")},closedic:function(e){return t.$refs["dictionaryRef"].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择swagger地址",clearable:""},model:{value:t.swaggerUrl,callback:function(e){t.swaggerUrl=e},expression:"swaggerUrl"}})],1)],1)],1)],1),a("div",{staticStyle:{"margin-top":"15px"},attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"default"},nativeOn:{click:function(e){return t.swaggerExportSumbit()}}},[t._v("确 认")]),a("el-button",{attrs:{size:"default"},on:{click:function(e){t.swaggerExportVissable=!1}}},[t._v("取 消")])],1)],1)],1)},N=[],H=a("5c73"),U={name:"formadd",props:["idx","formdata"],components:{selDict:H["a"]},data:function(){return{tabsValue:"",list:[],selTabsRow:{},selTabsType:"add",addItemVissable:!1,selItemRow:{},selItemType:"add",selectList:[],swaggerExportVissable:!1,swaggerUrl:""}},mounted:function(){},methods:{bindData:function(){var t=this,e={PageNum:1,PageSize:20,OrderType:0,SearchType:1,SearchPojo:{fnid:this.formdata.id},OrderBy:"rownum"};this.$request.post("/S06M40B1/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.list=[].concat(e.data.data.list))}))},handleSelectionChange:function(t){this.selectList=t},addItemBtn:function(){this.selItemRow={fnid:this.formdata.id,httpmethod:"get",rownum:this.list.length},this.selItemType="add",this.addItemVissable=!0},editItemBtn:function(t){this.selItemRow=Object.assign({},t),this.selItemType="edit",this.addItemVissable=!0,this.$forceUpdate()},btnAddItemSumbit:function(){var t=this,e="add"==this.selItemType?"/S06M40B1/create":"/S06M40B1/update";this.$request.post(e,JSON.stringify(this.selItemRow)).then((function(e){200==e.data.code?(t.bindData(),t.addItemVissable=!1):t.$message.warning(e.data.msg||"操作失败")}))},exportItemBtn:function(){console.log(this.formdata,"formdata"),this.swaggerExportVissable=!0},swaggerExportSumbit:function(){var t=this,e="/S06M40B1/importSwagger?fnid="+this.formdata.id;e+="&fncode="+this.formdata.functioncode,e+="&swaggerUrl="+this.swaggerUrl,this.$request.get(e).then((function(e){200==e.data.code?(t.bindData(),t.swaggerExportVissable=!1):t.$message.warning(e.data.msg||"操作失败")}))},delItemBtn:function(){var t=this;this.selectList.length?this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteItemRows()})):this.$message.warning("请选择要删除的数据")},deleteItemRows:function(){var t=this;return Object(g["a"])(Object(v["a"])().mark((function e(){var a,i,n,s,o;return Object(v["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=t,i=[],n=Object(L["a"])(t.selectList),e.prev=3,o=Object(v["a"])().mark((function e(){var a,n;return Object(v["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=s.value,n=new Promise((function(e,i){t.$request.get("/S06M40B1/delete?key="+a.id).then((function(t){200==t.data.code?e("删除成功"):i("删除失败")}))})),i.push(n);case 3:case"end":return e.stop()}}),e)})),n.s();case 6:if((s=n.n()).done){e.next=10;break}return e.delegateYield(o(),"t0",8);case 8:e.next=6;break;case 10:e.next=15;break;case 12:e.prev=12,e.t1=e["catch"](3),n.e(e.t1);case 15:return e.prev=15,n.f(),e.finish(15);case 18:return e.next=20,Promise.all(i).then((function(t){a.$message.success("删除成功"),a.bindData()})).catch((function(t){a.$message.warning("删除失败"),a.bindData()}));case 20:case"end":return e.stop()}}),e,null,[[3,12,15,18]])})))()}}},J=U,Y=(a("4b65"),Object(f["a"])(J,A,N,!1,null,"8586778a",null)),G=Y.exports,K={props:["idx","drawType"],components:{EditItem:q,ApiItem:G},data:function(){return{operateBar:O,processBar:V,formdata:{},activeTabs:1,activeData:[],formstate:0,submitting:0}},computed:{formcontainHeight:function(){var t=window.innerHeight-30;return t<600&&(t=600),t+"px"}},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;this.$request.get("/S06M38B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,"table"==t.drawType?t.$nextTick((function(){t.$refs.elitem.bindData()})):"api"==t.drawType&&t.$nextTick((function(){t.$refs.apiItem.bindData()}))):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeDialog()}})})).catch((function(e){t.$message.error(e||"请求错误")}))},closeForm:function(){this.$emit("closeForm")},changeIdx:function(t){this.dialogIdx=t},clickMethods:function(t){this[t.meth](t.param)}}},Q=K,W=(a("c0c7"),Object(f["a"])(Q,z,B,!1,null,"4ba6adde",null)),X=W.exports,Z={name:"S06M38B1",components:{FormEdit:h,TableList:I,ListHeader:C,formDraw:X},data:function(){return{FormVisible:!1,idx:0,total:0,showHelp:!1,tableForm:{},thorList:!0,online:0,drawformvisible:!1,drawType:"table"}},computed:{tableMaxHeight:function(){return window.innerHeight-160}},watch:{},mounted:function(){this.bindData(),this.$refs.tableList.getColumn()},methods:{bindData:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.bindData()}))},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},openDetail:function(t){this.idx=t.id,this.drawType="table",this.drawformvisible=!0},openApiDetail:function(t){this.idx=t.id,this.drawType="api",this.drawformvisible=!0},search:function(t){this.$refs.tableList.search(t)},advancedSearch:function(t){this.$refs.tableList.advancedSearch(t)},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},changeBalance:function(t){this.online=t,this.bindData()},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1,this.drawformvisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1,this.drawformvisible=!1},changeIdx:function(t){this.idx=t}}},tt=Z,et=Object(f["a"])(tt,i,n,!1,null,null,null);e["default"]=et.exports},"841c":function(t,e,a){"use strict";var i=a("d784"),n=a("825a"),s=a("1d80"),o=a("129f"),l=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=s(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var s=n(t),r=String(this),c=s.lastIndex;o(c,0)||(s.lastIndex=0);var d=l(s,r);return o(s.lastIndex,c)||(s.lastIndex=c),null===d?-1:d.index}]}))},"99bf":function(t,e,a){"use strict";a("26cf")},"9b3b":function(t,e,a){},af2b:function(t,e,a){"use strict";a("7de4")},c0c7:function(t,e,a){"use strict";a("46b2")},c50f:function(t,e,a){"use strict";a("30ea")},eeed:function(t,e,a){"use strict";a("773c")}}]);