(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4e5b4476"],{"0720":function(t,e,a){},"18b3":function(t,e,a){},"20fc":function(t,e,a){"use strict";a("590c")},"213f":function(t,e,a){},"27ae":function(t,e,a){(function(a){var i,r;(function(e,a){t.exports=a(e)})("undefined"!==typeof self?self:"undefined"!==typeof window?window:"undefined"!==typeof a?a:this,(function(a){"use strict";a=a||{};var o,n=a.Base64,s="2.6.4",l="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",d=function(t){for(var e={},a=0,i=t.length;a<i;a++)e[t.charAt(a)]=a;return e}(l),c=String.fromCharCode,m=function(t){if(t.length<2){var e=t.charCodeAt(0);return e<128?t:e<2048?c(192|e>>>6)+c(128|63&e):c(224|e>>>12&15)+c(128|e>>>6&63)+c(128|63&e)}e=65536+1024*(t.charCodeAt(0)-55296)+(t.charCodeAt(1)-56320);return c(240|e>>>18&7)+c(128|e>>>12&63)+c(128|e>>>6&63)+c(128|63&e)},u=/[\uD800-\uDBFF][\uDC00-\uDFFFF]|[^\x00-\x7F]/g,p=function(t){return t.replace(u,m)},f=function(t){var e=[0,2,1][t.length%3],a=t.charCodeAt(0)<<16|(t.length>1?t.charCodeAt(1):0)<<8|(t.length>2?t.charCodeAt(2):0),i=[l.charAt(a>>>18),l.charAt(a>>>12&63),e>=2?"=":l.charAt(a>>>6&63),e>=1?"=":l.charAt(63&a)];return i.join("")},h=a.btoa&&"function"==typeof a.btoa?function(t){return a.btoa(t)}:function(t){if(t.match(/[^\x00-\xFF]/))throw new RangeError("The string contains invalid characters.");return t.replace(/[\s\S]{1,3}/g,f)},g=function(t){return h(p(String(t)))},b=function(t){return t.replace(/[+\/]/g,(function(t){return"+"==t?"-":"_"})).replace(/=/g,"")},v=function(t,e){return e?b(g(t)):g(t)},w=function(t){return v(t,!0)};a.Uint8Array&&(o=function(t,e){for(var a="",i=0,r=t.length;i<r;i+=3){var o=t[i],n=t[i+1],s=t[i+2],d=o<<16|n<<8|s;a+=l.charAt(d>>>18)+l.charAt(d>>>12&63)+("undefined"!=typeof n?l.charAt(d>>>6&63):"=")+("undefined"!=typeof s?l.charAt(63&d):"=")}return e?b(a):a});var y,x=/[\xC0-\xDF][\x80-\xBF]|[\xE0-\xEF][\x80-\xBF]{2}|[\xF0-\xF7][\x80-\xBF]{3}/g,S=function(t){switch(t.length){case 4:var e=(7&t.charCodeAt(0))<<18|(63&t.charCodeAt(1))<<12|(63&t.charCodeAt(2))<<6|63&t.charCodeAt(3),a=e-65536;return c(55296+(a>>>10))+c(56320+(1023&a));case 3:return c((15&t.charCodeAt(0))<<12|(63&t.charCodeAt(1))<<6|63&t.charCodeAt(2));default:return c((31&t.charCodeAt(0))<<6|63&t.charCodeAt(1))}},k=function(t){return t.replace(x,S)},_=function(t){var e=t.length,a=e%4,i=(e>0?d[t.charAt(0)]<<18:0)|(e>1?d[t.charAt(1)]<<12:0)|(e>2?d[t.charAt(2)]<<6:0)|(e>3?d[t.charAt(3)]:0),r=[c(i>>>16),c(i>>>8&255),c(255&i)];return r.length-=[0,0,2,1][a],r.join("")},C=a.atob&&"function"==typeof a.atob?function(t){return a.atob(t)}:function(t){return t.replace(/\S{1,4}/g,_)},F=function(t){return C(String(t).replace(/[^A-Za-z0-9\+\/]/g,""))},$=function(t){return k(C(t))},D=function(t){return String(t).replace(/[-_]/g,(function(t){return"-"==t?"+":"/"})).replace(/[^A-Za-z0-9\+\/]/g,"")},j=function(t){return $(D(t))};a.Uint8Array&&(y=function(t){return Uint8Array.from(F(D(t)),(function(t){return t.charCodeAt(0)}))});var O=function(){var t=a.Base64;return a.Base64=n,t};if(a.Base64={VERSION:s,atob:F,btoa:h,fromBase64:j,toBase64:v,utob:p,encode:v,encodeURI:w,btou:k,decode:j,noConflict:O,fromUint8Array:o,toUint8Array:y},"function"===typeof Object.defineProperty){var V=function(t){return{value:t,enumerable:!1,writable:!0,configurable:!0}};a.Base64.extendString=function(){Object.defineProperty(String.prototype,"fromBase64",V((function(){return j(this)}))),Object.defineProperty(String.prototype,"toBase64",V((function(t){return v(this,t)}))),Object.defineProperty(String.prototype,"toBase64URI",V((function(){return v(this,!0)})))}}return a["Meteor"]&&(Base64=a.Base64),t.exports?t.exports.Base64=a.Base64:(i=[],r=function(){return a.Base64}.apply(e,i),void 0===r||(t.exports=r)),{Base64:a.Base64}}))}).call(this,a("c8ba"))},"4d5b":function(t,e,a){},"590c":function(t,e,a){},"6cb1":function(t,e,a){"use strict";a("c695")},"79b7":function(t,e,a){"use strict";a("4d5b")},"8c05":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx,projectid:t.projectid}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("div",{staticClass:"showcontent",style:{height:t.tableMaxHeight}},[a("div",{staticClass:"left"},[t._m(0),a("div",{staticClass:"btngroup"},[a("el-button-group",[a("el-button",{attrs:{type:"default",size:"small"},nativeOn:{click:function(e){return t.folderTools(e)}}},[t._v("文件分组")]),a("el-button",{attrs:{type:"default",size:"small"},nativeOn:{click:function(e){return t.showform(0)}}},[t._v("新建文件")]),a("el-button",{attrs:{type:"default",size:"small"},nativeOn:{click:function(e){return t.showform(t.fileRow.id)}}},[t._v("编辑文件")])],1)],1),a("MenuList",{ref:"menuList",attrs:{subMenu:t.groupData},on:{clickMenu:t.clickMenu}})],1),a("div",{staticClass:"right"},[a("TableList",{ref:"tableList"})],1)])])]),t.gropToolsVisible?a("el-dialog",{attrs:{title:"文件夹管理","append-to-body":!0,visible:t.gropToolsVisible,width:"800px",top:"5vh","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropToolsVisible=e},close:t.handleCloseDialog}},[a("groupTool",{ref:"groupTool",attrs:{projectid:t.projectid,projectname:t.projectname},on:{closeDialog:function(e){t.gropToolsVisible=!1}}})],1):t._e()],1)},r=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"projectTitle flex j-s a-c",staticStyle:{padding:"0 20px"}},[a("h3",{staticClass:"projectname"},[t._v("文档中心")])])}],o=a("2909"),n=(a("99af"),a("d81d"),a("e9c4"),a("d3b7"),a("0643"),a("a573"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"container"},[a("div",{staticClass:"table_contai flex"},[t._l(t.list,(function(e,i){return a("div",{key:i,staticClass:"contai_list"},[a("div",{staticClass:"contai_list_top",style:{background:e.coverimage?e.coverimage:"#30314f"}},[a("div",{staticClass:"list_title"},[t._v(t._s(e.proname))]),a("div",{staticClass:"list_img"},[a("span",[t._v(t._s(e.proname))])]),a("div",{staticClass:"list_button",on:{click:function(a){return t.setEnter(e)}}},[t._v("立即进入")])]),a("div",{staticClass:"contai_list_bottom"})])})),a("div",{staticClass:"contai_list contai_listadd",on:{click:function(e){return t.getProject()}}},[t._m(0)])],2),t.proVissible?a("el-dialog",{attrs:{width:"800px",title:"新增项目","append-to-body":!0,visible:t.proVissible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.proVissible=e}}},[a("formadd",{ref:"formadd",attrs:{idx:t.idx},on:{saveForm:t.saveForm}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.projecSave()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.proVissible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)}),s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"contai_listadd_icon"},[a("i",{staticClass:"el-icon-plus"})])}],l=a("b775"),d=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("proname")}}},[a("el-form-item",{attrs:{label:"项目名称",prop:"proname"}},[a("el-input",{attrs:{placeholder:"请输入项目名称",clearable:"",size:"small"},model:{value:t.formdata.proname,callback:function(e){t.$set(t.formdata,"proname",e)},expression:"formdata.proname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("projcode")}}},[a("el-form-item",{attrs:{label:"项目编码",prop:"projcode"}},[a("el-input",{attrs:{placeholder:"请输入项目编码",clearable:"",size:"small"},model:{value:t.formdata.projcode,callback:function(e){t.$set(t.formdata,"projcode",e)},expression:"formdata.projcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("projtype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"projtype"}},[a("el-input",{attrs:{placeholder:"类型",size:"small"},model:{value:t.formdata.projtype,callback:function(e){t.$set(t.formdata,"projtype",e)},expression:"formdata.projtype"}})],1)],1)]),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"公共","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1),a("el-col",{attrs:{span:2}},[a("el-form-item",{attrs:{label:""}},[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("coverimage")}}},[a("el-form-item",{attrs:{label:"背景颜色",prop:"coverimage"}},[a("el-input",{attrs:{placeholder:"请输入背景颜色",clearable:"",size:"small"},model:{value:t.formdata.coverimage,callback:function(e){t.$set(t.formdata,"coverimage",e)},expression:"formdata.coverimage"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("rownum")}}},[a("el-form-item",{attrs:{label:"行号",prop:"rownum"}},[a("el-input-number",{attrs:{min:0,label:"请输入行号",size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)},c=[],m={name:"Formedit",props:["idx"],data:function(){return{title:"项目",formdata:{proname:"",projcode:"",projtype:"",coverimage:"",enabledmark:1,publicmark:0,rownum:0},formRules:{proname:[{required:!0,trigger:"blur",message:"名称不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&l["a"].get("/S06M09B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},submitForm:function(){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return console.log("error submit!!"),!1;t.$emit("saveForm",t.formdata)}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},u=m,p=(a("20fc"),a("2877")),f=Object(p["a"])(u,d,c,!1,null,"aaeff9e0",null),h=f.exports;const g={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);l["a"].post("/S06M11S1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);l["a"].post("/S06M11S1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{l["a"].get("/S06M11S1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},addPro(t){return new Promise((e,a)=>{var i=JSON.stringify(t);l["a"].post("/S06M11S1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var b=g,v={components:{formadd:h},data:function(){return{list:[],idx:0,queryParams:{PageNum:1,PageSize:1e3,OrderType:0,SearchType:1,OrderBy:"rownum"},proVissible:!1}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;l["a"].post("/S06M11B2/getPageListByUser",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.list=e.data.data.list),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getProject:function(){this.proVissible=!0},projecSave:function(){this.$refs.formadd.submitForm()},saveForm:function(t){var e=this;b.addPro(t).then((function(t){e.$message.success(t.msg||"保存成功"),e.bindData(),e.proVissible=!1})).catch((function(t){e.$message.warning(t||"保存失败")}))},setEnter:function(t){this.$emit("bthGroup",t)}}},w=v,y=(a("d9f10"),Object(p["a"])(w,n,s,!1,null,"382d772a",null)),x=y.exports,S=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small",disabled:!!t.formdata.assessor},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[t.formdata.assessor?a("el-button",{attrs:{size:"small",type:"primary"},on:{click:function(e){return t.DeApproval()}}},[t._v(" 反审核")]):a("el-button",{attrs:{size:"small",type:"primary",disabled:!t.formdata.id},on:{click:function(e){return t.approval()}}},[t._v(" 审 核")])],1),a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id||!!t.formdata.assessor},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")]),a("el-dropdown-item",{attrs:{icon:"el-icon-upload"},nativeOn:{click:function(e){return t.$refs.upload.click()}}},[t._v(" 导入MarkDown ")]),a("el-dropdown-item",{attrs:{icon:"el-icon-download",disabled:!t.formdata.id},nativeOn:{click:function(e){return t.getExport()}}},[t._v(" 导出MarkDown ")]),a("el-dropdown-item",{attrs:{icon:"el-icon-share",disabled:!t.formdata.id},nativeOn:{click:function(e){return t.getShare()}}},[t._v(" 分享 ")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:!!t.idx},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),a("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[a("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,"auto-complete":"off",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("mdgroupid")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"mdgroupid"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.groupnameOptions,props:t.defaultProps,"show-all-levels":!1,placeholder:"请选择分组名称",size:"small"},on:{change:t.handleChangeGroupid},model:{value:t.formdata.mdgroupid,callback:function(e){t.$set(t.formdata,"mdgroupid",e)},expression:"formdata.mdgroupid"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[a("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入标题",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("frontphoto")}}},[a("el-form-item",{attrs:{label:"封面图",prop:"frontphoto"}},[t.formdata.frontphoto||t.pictureurl?a("div",{staticClass:"cardphotoImg"},[a("el-popover",{ref:"addpopover",attrs:{placement:"right",trigger:"click"}},[a("img",{staticStyle:{"max-width":"1200px","max-height":"800px"},attrs:{src:t.formdata.frontphoto?t.baseURL+t.formdata.frontphoto:t.pictureurl,alt:""}}),a("img",{attrs:{slot:"reference",src:t.formdata.frontphoto?t.baseURL+t.formdata.frontphoto:t.pictureurl,alt:""},slot:"reference"})]),a("div",{staticClass:"cardphotoImg_icon",on:{click:function(e){return t.deletepic()}}},[a("i",{staticClass:"el-icon-delete",staticStyle:{color:"#a1a1a1"}})])],1):a("div",{staticClass:"cardphotoImg cardphotoImg_text",on:{click:function(e){return t.$refs.uploadimage.click()}}},[t._v(" + ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"uploadimage",attrs:{type:"file"},on:{change:t.getPicFile}})])])],1)])],1),a("el-divider",{staticStyle:{"margin-top":"40px"}}),a("el-row",{staticStyle:{margin:"15px 0 10px 0"}},[a("el-col",{attrs:{span:24}},[a("div",{style:{height:"calc("+t.formcontainHeight+" - 320px)"}},[a("mavon-editor",{ref:"md",staticClass:"markdown",style:{width:"100%",height:"100%","z-index":"99"},attrs:{value:t.formdata.markdowndata,subfield:t.prop.subfield,defaultOpen:t.prop.defaultOpen,toolbarsFlag:t.prop.toolbarsFlag,editable:t.prop.editable,scrollStyle:t.prop.scrollStyle,toolbars:t.prop.toolbars},on:{imgAdd:t.imgAdd,imgDel:t.imgDel}})],1)])],1)],1)],1),a("el-divider"),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":"100px"}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"新建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})]),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"upload",attrs:{type:"file"},on:{change:t.getFile}})]),t.shareVisible?a("el-dialog",{attrs:{width:"500px",title:"分享","append-to-body":!0,visible:t.shareVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.shareVisible=e}}},[a("div",{staticStyle:{padding:"15px"}},[a("el-form",{ref:"shareform",attrs:{model:t.shareform,"label-width":"70px",rules:t.shareformRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"截至日期"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.shareform.deadtime,callback:function(e){t.$set(t.shareform,"deadtime",e)},expression:"shareform.deadtime"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("password")}}},[a("el-form-item",{attrs:{label:"分享密码",prop:"password"}},[a("el-input",{attrs:{placeholder:"请输入分享密码",clearable:"",size:"small",maxlength:"4",minlength:"4"},model:{value:t.shareform.password,callback:function(e){t.$set(t.shareform,"password",e)},expression:"shareform.password"}})],1)],1)])],1),a("div",{staticStyle:{"text-align":"right"}},[a("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.getUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.shareVisible=!1}}},[t._v("取 消")])],1)],1)],1)]):t._e()],1)},k=[],_=a("ade3"),C=(a("b64b"),a("3ca3"),a("4e3e"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("6ca8")),F=a.n(C),$=(a("27ae"),{name:"Formedit",props:["idx","projectid"],components:{},data:function(){return Object(_["a"])(Object(_["a"])(Object(_["a"])({title:"文档",formdata:{enabledmark:1,remark:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,mdgroupid:"",markdowndata:"",assessor:""},formRules:{billtitle:[{required:!0,trigger:"blur",message:"标题为必填项"}],mdgroupid:[{required:!0,trigger:"blur",message:"分组为必填项"}]},formLabelWidth:"100px",selVisible:!1,multi:0,groupnameOptions:[],defaultProps:{children:"children",label:"groupname",value:"id"},ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,txtTextView:{content:""},baseURL:this.$store.state.app.config.baseURL,shareVisible:!1,shareform:{deadtime:"",password:""},shareformRules:{}},"baseURL",this.$store.state.app.config.baseURL+"File/getImage/"),"pictureurl",""),"picdata","")},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"},prop:function(){var t={subfield:!0,editable:!0,toolbarsFlag:!0,scrollStyle:!0,toolbars:{bold:!0,italic:!0,header:!0,underline:!0,strikethrough:!0,mark:!0,superscript:!0,subscript:!0,quote:!0,ol:!0,ul:!0,link:!0,imagelink:!0,code:!0,table:!0,fullscreen:!0,readmodel:!0,htmlcode:!0,help:!0,undo:!0,redo:!0,trash:!0,save:!1,navigation:!0,alignleft:!0,aligncenter:!0,alignright:!0,subfield:!0,preview:!0}};return t}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData(),this.BindDataByuidgroupname()},mounted:function(){},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&l["a"].get("/S06M11S1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.formdata=e.data.data,t.readFile(t.formdata.mdurl)),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.getPicture()}))},getPicture:function(){var t=this;this.picdata?l["a"].post("/File/uploadPic",this.picdata).then((function(e){200==e.data.code?(t.formdata.frontphoto=e.data.data.dirname+"/"+e.data.data.filename,t.saveForm()):t.$message.warning("图片上传出错")})).catch((function(t){reject()})):this.saveForm()},saveForm:function(){var t=this;this.formdata.content=this.$refs.md.d_value,0==this.idx?b.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.readFile(t.formdata.mdurl))})).catch((function(e){t.$message.warning("保存失败")})):b.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.formdata=e.data,t.readFile(t.formdata.mdurl))})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.delete(t).then((function(t){200==t.code&&(e.$message.success("删除成功"),e.$emit("compForm"))})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},printButton:function(){var t=this;l["a"].get("/system/SYSM07B3/getListByModuleCode?code=S06M04B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?l["a"].get("/S06M04B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},imgAdd:function(t,e){var a=this;F()(e).then((function(e){l["a"].post("/File/uploadPic",e.formData).then((function(e){if(200==e.data.code){var i=a.baseURL+"File/getImage/"+e.data.data.dirname+"/"+e.data.data.filename;a.$refs.md.$img2Url(t,i)}else a.$message.warning("上传失败")}))}))},imgDel:function(){},readFile:function(t){var e=this;l["a"].get("/File/getMinioUrl/"+t).then((function(t){e.formdata.markdowndata=t.data,e.$forceUpdate()}))},approval:function(){var t=this;l["a"].get("/S06M11S1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success("审核成功"),t.formdata=e.data.data,t.$emit("bindData")):t.$message.warning(e.data.msg||"审核失败")}))},DeApproval:function(){var t=this;l["a"].get("/S06M11S1/approval?key="+this.formdata.id).then((function(e){200==e.data.code?(t.$message.success("反审核成功"),t.formdata=e.data.data,t.$emit("bindData")):t.$message.warning(e.data.msg||"反审核失败")}))},handleChangeGroupid:function(t){this.formdata.mdgroupid=t[t.length-1]},BindDataByuidgroupname:function(){var t=this,e={PageNum:1,PageSize:100,OrderType:0,SearchType:1,OrderBy:"rownum",scenedata:[{field:"Sa_MdGroupDoc.mdprojectid",fieldtype:"0",math:"equal",value:this.projectid}]};l["a"].post("S06M11S3/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.groupnameOptions=t.changeFormat(e.data.data.list))})).catch((function(t){}))},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.id]=t})),t.forEach((function(t){var i=a[t.parentid];i?(i.children||(i.children=[])).push(t):e.push(t)})),e},getExport:function(){var t=new Blob([this.formdata.markdowndata],{type:"text/plain"}),e=document.createElement("a");e.download=this.formdata.billtitle+".md",e.style.display="none",e.href=URL.createObjectURL(t),document.body.appendChild(e),e.click(),document.body.removeChild(e)},getFile:function(){var t=this,e=this.$refs.upload,a=e.files[0],i=new FileReader;i.readAsText(a),i.onload=function(e){t.formdata.markdowndata=e.currentTarget.result,t.$forceUpdate()}},exportPDF:function(){var t=this.$refs.md.$el.innerHTML;console.log("htmlContent",t)},getShare:function(){this.shareVisible=!0,this.shareform={deadtime:this.GetMonthStr(1),password:""}},GetMonthStr:function(t){var e=new Date;e.setMonth(e.getMonth()+t);var a=e.getFullYear(),i=e.getMonth()+1,r=e.getDate();return a+"-"+i+"-"+r},getUpdate:function(){var t=this,e={lnkmarkdownid:this.formdata.id,deadtime:this.shareform.deadtime,password:this.shareform.password,passwordmark:this.shareform.password?1:0};l["a"].post("/S06M11B5/create",JSON.stringify(e)).then((function(e){t.shareVisible=!1,200==e.data.code?t.$router.push({path:"/lnkshare",query:{id:e.data.data.id}}):t.$message.warning("创建错误")})).catch((function(t){}))},getPicFile:function(){var t=this,e=this.$refs.uploadimage,a=e.files[0];F()(a).then((function(e){t.pictureurl=e.base64,t.picdata=e.formData}))},deletepic:function(){this.formdata.frontphoto="",this.pictureurl="",this.picdata=""}}}),D=$,j=(a("a247"),Object(p["a"])(D,S,k,!1,null,"ba0e7c9a",null)),O=j.exports,V=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入名称",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入编码",size:"small"},model:{value:t.formdata.groupcode,callback:function(e){t.$set(t.formdata,"groupcode",e)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)])},M=[],T=(a("25f0"),a("4d90"),a("b0b8")),z={name:"Formedit",filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),r=e.getDate().toString().padStart(2,"0"),o=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(r," ").concat(o,":").concat(n,":").concat(s)}},props:["idx","pid","projectid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",modulecode:"S06M11B1",groupcode:"",groupname:"",rownum:0,remark:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx?l["a"].get("/S06M11S3/getEntity?key=".concat(this.idx)).then((function(e){console.log("==================",e),200==e.data.code&&(t.formdata=e.data.data)})):this.formdata.parentid=this.idx},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.pid&&(this.formdata.parentid=this.pid,this.formdata.mdprojectid=this.projectid),0==this.idx?l["a"].post("/S06M11S3/create",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")})):l["a"].post("/S06M11S3/update",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")}))},closeForm:function(){this.$emit("closeDialog")},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){T.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=T.getFullChars(t)}}},B=z,N=(a("b127"),Object(p["a"])(B,V,M,!1,null,"559b2990",null)),A=N.exports,R=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{height:"100%"}},[t.markdowndata?a("div",{staticStyle:{height:"100%"}},[a("mavon-editor",{ref:"md",staticClass:"markdown",style:{width:"100%",height:"100%","z-index":"99"},attrs:{value:t.markdowndata,subfield:t.prop.subfield,defaultOpen:t.prop.defaultOpen,toolbarsFlag:t.prop.toolbarsFlag,editable:t.prop.editable,scrollStyle:t.prop.scrollStyle,navigation:t.prop.navigation,codeStyle:t.prop.codeStyle}})],1):a("div",[a("el-empty",{attrs:{description:"暂无文件"}})],1)])},P=[],L={data:function(){return{markdowndata:""}},computed:{prop:function(){var t={subfield:!1,defaultOpen:"preview",editable:!1,toolbarsFlag:!1,scrollStyle:!0,navigation:!0,codeStyle:"monokai"};return t}},methods:{bindData:function(t){var e=this;t.mdurl?this.$request.get("/File/getMinioUrl/"+t.mdurl).then((function(t){e.markdowndata=t.data,e.$forceUpdate()})):this.markdowndata=""}}},U=L,E=Object(p["a"])(U,R,P,!1,null,"40e93bf2",null),I=E.exports,J=a("ab18"),q=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"page-container"},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[t._m(0),a("el-tree",{staticStyle:{"font-size":"14px",height:"70vh",overflow:"auto",width:"100%"},attrs:{data:t.groupData,"node-key":"id","default-expand-all":""},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.node,r=e.data;return a("span",{staticClass:"custom-tree-node"},[a("span",[t._v(t._s(i.label)+" ")]),a("span",[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==r.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return t.editTreeNode(r)}}})],1),a("span",[a("el-button",{staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return t.addTreeChild(r)}}})],1),a("span",[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==r.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return t.delTreeNode(r)}}})],1)])}}])})],1)]),t.gropuFormVisible?a("el-dialog",{attrs:{title:"文件夹编辑","append-to-body":!0,visible:t.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[a("group",{ref:"group",attrs:{idx:t.idx,pid:t.pid,projectid:t.projectid},on:{bindData:t.bindTreeData,closeDialog:function(e){t.gropuFormVisible=!1}}})],1):t._e()],1)},G=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"groupTitle"},[a("span",[t._v("目录")])])}],H={name:"GroupTool",components:{group:A},props:{projectid:{type:String},projectname:{type:String}},data:function(){return{groupData:[],gropuFormVisible:!1,pid:0,idx:0}},methods:{bindTreeData:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1,OrderBy:"rownum",scenedata:[{field:"Sa_MdGroupDoc.mdprojectid",fieldtype:"0",math:"equal",value:this.projectid}]};l["a"].post("/S06M11S3/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){var a=e.data.data.list.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname,prefix:t.prefix}})),i=[{id:"0",pid:-1,label:"文件分组",prefix:!1}],r=[].concat(Object(o["a"])(a),i);t.groupData=t.transData(r,"id","pid","children")}}))},showGroupform:function(t){this.idx=t,this.gropuFormVisible=!0},editTreeNode:function(t){this.pid=t.pid,this.showGroupform(t.id)},addTreeChild:function(t){this.pid=t.id,this.showGroupform(0)},delTreeNode:function(t){var e=this;t.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),l["a"].get("/S06M11S3/delete?key=".concat(t.id)).then((function(){console.log("执行关闭保存"),e.$message.success({message:"删除成功！"}),e.bindTreeData()})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},transData:function(t,e,a,i){for(var r=[],o={},n=e,s=a,l=i,d=0,c=0,m=t.length;d<m;d++)o[t[d][n]]=t[d];for(;c<m;c++){var u=t[c],p=o[u[s]];p?(!p[l]&&(p[l]=[]),p[l].push(u)):r.push(u)}return r}}},W=H,Y=(a("79b7"),Object(p["a"])(W,q,G,!1,null,"93b79a48",null)),Z=Y.exports,Q={name:"S06M11S1",components:{formedit:O,project:x,group:A,groupTool:Z,TableList:I,MenuList:J["a"]},data:function(){return{lst:[],FormVisible:!1,proVisible:!0,groupData:[],projectid:"",projectname:"",gropToolsVisible:!1,fileRow:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-110+"px"}},watch:{},created:function(){this.bindTreeData(),this.bindData()},methods:{bindData:function(t){var e=this,a={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1,OrderBy:"rownum"};l["a"].post("/S06M11S1/getMdByProjectIdOrGroupId?projectid="+this.projectid+"&groupid="+t,JSON.stringify(a)).then((function(t){var a=e.groupData.concat();if(200==t.data.code){if(t.data.data.list.length){for(var i=t.data.data.list.concat(),r=[],n=0;n<i.length;n++){var s=i[n],l={id:s.id,pid:s.mdgroupid,label:s.billtitle,icon:"",type:"file",mdurl:s.mdurl};r.push(l)}a=[].concat(Object(o["a"])(a),r)}e.clickMenu(0)}else e.$message.warning(t.data.msg||"获取信息失败");e.groupData=e.transData(a,"id","pid","children")})).catch((function(t){console.log(er||"请求错误")}))},bindTreeData:function(){var t=this,e={PageNum:1,PageSize:100,OrderType:0,SearchType:1,OrderBy:"rownum",scenedata:[{field:"Sa_MdGroupDoc.mdprojectid",fieldtype:"0",math:"equal",value:this.projectid}]};l["a"].post("/S06M11S3/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){var a=e.data.data.list.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname,icon:t.groupicon,type:"folder"}}));t.groupData=a,t.bindData("")}}))},clickMenu:function(t){this.fileRow=t,this.$refs.tableList.bindData(t)},folderTools:function(){var t=this;this.gropToolsVisible=!0,this.$nextTick((function(){t.$refs.groupTool.bindTreeData()}))},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1,this.bindData()},compForm:function(){this.bindData(),this.FormVisible=!1,this.bindTreeData()},transData:function(t,e,a,i){for(var r=[],o={},n=e,s=a,l=i,d=0,c=0,m=t.length;d<m;d++)o[t[d][n]]=t[d];for(;c<m;c++){var u=t[c],p=o[u[s]];p?(!p[l]&&(p[l]=[]),p[l].push(u)):r.push(u)}return r},handleCloseDialog:function(){this.bindTreeData()},changeidx:function(t){this.idx=t},bthGroup:function(t){this.projectid=t.id,this.projectname=t.proname,this.proVisible=!1,this.bindTreeData()},btnQuit:function(){this.proVisible=!0}}},K=Q,X=(a("6cb1"),Object(p["a"])(K,i,r,!1,null,"50718e35",null));e["default"]=X.exports},a247:function(t,e,a){"use strict";a("18b3")},b127:function(t,e,a){"use strict";a("0720")},c695:function(t,e,a){},d9f10:function(t,e,a){"use strict";a("213f")}}]);