(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-55b4b2c4"],{"0702":function(t,e,a){},"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return n})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(t,e,a){var n=s(),r=t-n,l=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=l;var t=Math.easeInOutQuad(c,n,r,e);o(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"2f2b":function(t,e,a){},"4f6f":function(t,e,a){"use strict";a("2f2b")},"5c73":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?a("ul",{},t._l(t.lst,(function(e,i){return a("li",{key:i,class:t.radio==i?"active":"",on:{click:function(a){t.getCurrentRow(e),t.radio=i}}},[a("span",[t._v(t._s(e.dictvalue))])])})),0):a("div",{staticClass:"noData"},[t._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),a("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},t._l(t.lst,(function(e,i){return a("p",{key:i,class:t.ActiveIndex==i?"isActive":"",on:{click:function(e){t.ActiveIndex=i}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],s=(a("a434"),a("e9c4"),a("b775")),n=a("333d"),r=a("b0b8"),l={components:{Pagination:n["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var a=0;a<t.length;a++)t[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],s["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var a=0;a<t.formdata.item.length;a++)t.lst.push(t.formdata.item[a])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.addItem(a)})).catch((function(t){console.log(t)}))},addItem:function(t){r.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:r.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var a=e.value;t.lst[t.ActiveIndex].dictvalue=a,r.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=r.getFullChars(a)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,s["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=l,d=(a("af2b"),a("2877")),m=Object(d["a"])(c,i,o,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"7de4":function(t,e,a){},"841c":function(t,e,a){"use strict";var i=a("d784"),o=a("825a"),s=a("1d80"),n=a("129f"),r=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=s(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var s=o(t),l=String(this),c=s.lastIndex;n(c,0)||(s.lastIndex=0);var d=r(s,l);return n(s.lastIndex,c)||(s.lastIndex=c),null===d?-1:d.index}]}))},a6d7:function(t,e,a){},af2b:function(t,e,a){"use strict";a("7de4")},b122:function(t,e,a){},ca6f:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,btnExport:t.btnExport,changeBalance:t.changeBalance,bindColumn:function(e){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()}}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("TableList",{ref:"tableList",attrs:{online:t.online},on:{changeIdx:t.changeIdx,sendTableForm:t.sendTableForm,showForm:t.showForm}})],1)],1)],1)],1)])},o=[],s=(a("ac1f"),a("841c"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container flex a-c j-s"},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1)],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),a("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[a("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),n=[],r={name:"ListHeader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},balance:!1,setColumsVisible:!1,searchVisible:!1}},methods:{advancedSearch:function(t){var e={formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)}}},l=r,c=(a("e05d"),a("2877")),d=Object(c["a"])(l,s,n,!1,null,"0858043a",null),m=d.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate","process"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.printButton()},clickMethods:t.clickMethods}}),a("div",{staticClass:"refNo",staticStyle:{top:"57px",right:"30px","min-width":"396px"}},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.discoverydate,callback:function(e){t.$set(t.formdata,"discoverydate",e)},expression:"formdata.discoverydate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini"},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)])])],1)]),a("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods,getGroupName:t.setGroupRow,autoClear:t.autoClear},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[a("div",{staticClass:"form form-head p-r"},[a("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata},on:{getGroupName:t.setGroupRow,autoClear:t.autoClear}})],1)]},proxy:!0},t.formtemplate.footer.type?null:{key:"Footer",fn:function(){return[a("div",[a("EditFooter",{attrs:{formdata:t.formdata}})],1)]},proxy:!0}],null,!0)})],1)]),a("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"S06M20B1Edit",examineurl:"/S06M20B1/sendapprovel"}}),t.operationVisible?a("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}}):t._e(),t.processVisible?a("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}}):t._e(),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button-group",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"print"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="print",t.submitRemoteReport()}}},[t._v("云打印")]),a("el-button",{attrs:{type:"preview"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="preview",t.submitRemoteReport()}}},[t._v("云预览")])],1),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("打印")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},u=[],p=(a("b64b"),a("d3b7"),a("3ca3"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("b775"));const h={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);p["a"].post("/S06M30B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);p["a"].post("/S06M30B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){p["a"].get("/S06M30B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,p["a"].get("/S06M30B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||!t.formdata.assessor?"审核成功":"反审核成功"),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||!t.formdata.assessor?"审核失败":"反审核失败"))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,a){if(1==t||2==t)var i=1==t?"作废":"启用",o="/S06M30B1/disannul?type="+(1==t?1:0);else i=3==t?"中止":"启用",o="/S06M30B1/closed?type="+(3==t?1:0);p["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(a.$message.success(t.data.msg||i+"成功"),a.$emit("bindData"),a.formdata=t.data.data):a.$message.warning(t.data.msg||i+"失败")}).catch(t=>{a.$message.error(t||"服务请求错误")})}};var b=h,v=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("softwarename")}}},[a("el-form-item",{attrs:{label:"软件名称",prop:"softwarename"}},[a("el-popover",{attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}},model:{value:t.selnamevisible,callback:function(e){t.selnamevisible=e},expression:"selnamevisible"}},[a("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_DefectReport.softwarename"},on:{singleSel:function(e){t.selnamevisible=!1,t.formdata.softwarename=e.dictvalue,t.cleValidate("softwarename")},closedic:function(e){t.selnamevisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择软件名称",clearable:"",size:"small"},model:{value:t.formdata.softwarename,callback:function(e){t.$set(t.formdata,"softwarename",e)},expression:"formdata.softwarename"}})],1)],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"版本"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入版本",clearable:"",size:"small"},model:{value:t.formdata.softwareversion,callback:function(e){t.$set(t.formdata,"softwareversion",e)},expression:"formdata.softwareversion"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("defecttype")}}},[a("el-form-item",{attrs:{label:"类型",prop:"defecttype"}},[a("el-popover",{attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.seltypeRef.bindData()}},model:{value:t.seltypevisible,callback:function(e){t.seltypevisible=e},expression:"seltypevisible"}},[a("selDictionaries",{ref:"seltypeRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_DefectReport.defecttype"},on:{singleSel:function(e){t.seltypevisible=!1,t.formdata.defecttype=e.dictvalue,t.cleValidate("defecttype")},closedic:function(e){t.seltypevisible=!1}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:t.formdata.defecttype,callback:function(e){t.$set(t.formdata,"defecttype",e)},expression:"formdata.defecttype"}})],1)],1)],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"严重程度",prop:"severity"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择严重程度",size:"small"},model:{value:t.formdata.severity,callback:function(e){t.$set(t.formdata,"severity",e)},expression:"formdata.severity"}},[a("el-option",{attrs:{label:"轻微",value:0}}),a("el-option",{attrs:{label:"一般",value:1}}),a("el-option",{attrs:{label:"严重",value:2}})],1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"优先级"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入优先级",clearable:"",size:"small"},model:{value:t.formdata.priority,callback:function(e){t.$set(t.formdata,"priority",e)},expression:"formdata.priority"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("testenvironment")}}},[a("el-form-item",{attrs:{label:"测试环境"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px !important"},attrs:{placeholder:"请输入测试环境",size:"small"},model:{value:t.formdata.testenvironment,callback:function(e){t.$set(t.formdata,"testenvironment",e)},expression:"formdata.testenvironment"}})],1)],1)]),a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("description")}}},[a("el-form-item",{attrs:{label:"缺陷描述"}},[a("el-input",{attrs:{placeholder:"请输入缺陷描述",size:"small",type:"textarea"},model:{value:t.formdata.description,callback:function(e){t.$set(t.formdata,"description",e)},expression:"formdata.description"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"flex-end"}},[a("h5",[t._v("步骤重现")])]),a("MyEditor",{ref:"MyEditor",attrs:{height:325,html:t.formdata.reproductionsteps,excludeKeys:["group-image","insertImage","uploadImage","insertLink","group-video","insertVideo","uploadVideo","fullScreen"]},on:{changeHtml:function(e){t.formdata.reproductionsteps=e}}})],1)],1)],1)},g=[],y=a("5c73"),w=a("1975"),x={props:{formdata:{type:Object},title:{type:String}},components:{selDictionaries:y["a"],MyEditor:w["a"]},data:function(){return{formRules:{softwarename:[{required:!0,trigger:"blur",message:"软件名称"}]},selnamevisible:!1,seltypevisible:!1}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},S=x,k=(a("4f6f"),Object(c["a"])(S,v,g,!1,null,"0e3cec78",null)),$=k.exports,_=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-form",{staticClass:"footFormContent",attrs:{"label-width":"100px"}},[a("el-divider"),a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:18}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"测试人员"}},[a("el-input",{attrs:{placeholder:"请输入测试人员",clearable:"",size:"small"},model:{value:t.formdata.tester,callback:function(e){t.$set(t.formdata,"tester",e)},expression:"formdata.tester"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)},C=[],D={props:{formdata:{type:Object}},data:function(){return{salesmanRefsVisible:!1}}},F=D,B=Object(c["a"])(F,_,C,!1,null,"60abba59",null),M=B.exports,I=a("dcb4"),T=["id","refno","softwarename","softwareversion","discoverydate","tester","description","attachment","defecttype","severity","priority","testenvironment","reproductionsteps","remark","custom1","custom2","custom3","custom4","custom5"],V={params:T},R=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],O=[],P={header:{type:0,title:"缺陷报告",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"其他发货",value:"其他发货"},{label:"其他退货",value:"其他退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:""},{col:5,code:"linkman",label:"联系人",type:"input",methods:"",param:""},{col:5,code:"telephone",label:"联系电话",type:"input",methods:"",param:""}]}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]}},z=(new Date,{name:"Formedit",components:{FormTemp:I["a"],EditHeader:$,EditFooter:M},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"缺陷报告",operateBar:R,processBar:O,formdata:{refno:"",softwarename:"",softwareversion:"",discoverydate:new Date,tester:"",description:"",defecttype:"",severity:0,priority:"",testenvironment:"",reproductionsteps:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:P,formstate:0,submitting:0,ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,printType:"print"}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){this.formtemplate=P},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&p["a"].get("/S06M20B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setGroupRow:function(t){},autoClear:function(){},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this,e={};e=this.$getParam(V,e,this.formdata),this.submitting=1,0==this.idx?b.add(e).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):b.update(e).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.delete(t)})).catch((function(){}))},approval:function(){b.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var a=this.$refs.elitem.multipleSelection;2==t||4==t?b.reqitembill(t,a,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.reqitembill(t,a,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processModel=t.code,this.processTitle=t.label},changeBillType:function(){formdata.item=[]},changeIdx:function(t){this.dialogIdx=t},billSwitch:function(t){},printButton:function(){var t=this;p["a"].get("/SaReports/getListByModuleCode?code=S06M20B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?p["a"].get("/S06M20B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;if(""!=this.reportModel){var e="/S06M20B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel;"preview"==this.printType&&(e="/S06M20B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel+"&cmd=1"),p["a"].get(e).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")}))}else this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.formdata.printcount+=1,this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},fastKey:function(){var t=this;document.onkeydown=function(e){var a=e.keyCode;83==a&&e.ctrlKey?e.preventDefault():27==a&&(e.preventDefault(),t.closeForm())}}}}),E=z,N=(a("d280"),Object(c["a"])(E,f,u,!1,null,"4569050a",null)),L=N.exports,A=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("div",{staticClass:"flex a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?a("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[a("span",[t._v("计数="+t._s(t.cellNum))]),a("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),a("div",{staticStyle:{"margin-right":"40px"}})])],1)},q=[],H=a("c7eb"),j=a("1da1"),U=(a("e9c4"),a("a9e3"),a("c7cd"),a("0643"),a("4e3e"),a("159b"),{formcode:"S06M30B1List",item:[{itemcode:"devsn",itemname:"设备码SN",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Sa_MqttLog.refno"},{itemcode:"software",itemname:"软件名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_MqttLog.software"},{itemcode:"msgtype",itemname:"日志类型",minwidth:"100",displaymark:1,overflow:1},{itemcode:"msgcontent",itemname:"日志内容",minwidth:"100",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1},{itemcode:"modifydate",itemname:"修改日期",minwidth:"100",displaymark:1,overflow:1}]}),J={components:{},props:["online"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:U,customList:[],selectList:[],totalfields:[],exportitle:"MQTT日志",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var a=e.startRowIndex;t.rowScroll=a}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var a=e.selectedRowKeys;if(t.selectList=[],0!=a.length)for(var i=0;i<a.length;i++)t.selectList.push({id:a[i]})},selectedAllChange:function(e){var a=e.isSelected;e.selectedRowKeys;t.selectList=a?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var a=this.tableForm.item[e];a.displaymark&&(t+=Number(a.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;if(this.online)var e="/S06M30B1/getOnlinePageList";else e="/S06M30B1/getPageList";p["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(response.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(j["a"])(Object(H["a"])().mark((function e(){return Object(H["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm);case 2:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,a=[];this.columnHidden=[],t["item"].forEach((function(t,i){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(a,i){var o=a.row;a.column,a.rowIndex;return"billdate"==t.itemcode||"plandate"==t.itemcode||"discoverydate"==t.itemcode||"modifydate"==t.itemcode?e.$options.filters.dateFormat(o[t.itemcode]):o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),a.push(o)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,a){t.row,t.column;var i=t.rowIndex;return i+e.rowScroll+1}}),this.customData=a,this.keynum+=1},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,a=[];this.$countCellData(this,a,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){var e=t.strfilter;""!=e?this.queryParams.SearchPojo={devsn:e,software:e,msgtype:e,msgcontent:e,remark:e}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var a={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(a,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)}}},K=J,W=(a("f842"),Object(c["a"])(K,A,q,!1,null,"70dadcaa",null)),G=W.exports,Y={name:"S06M30B1",components:{ListHeader:m,FormEdit:L,TableList:G},data:function(){return{formvisible:!1,idx:0,online:0,tableForm:{}}},watch:{},mounted:function(){this.bindData(),this.$refs.tableList.getColumn()},methods:{bindData:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.bindData()}))},sendTableForm:function(t){this.tableForm=t},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.btnExport()}))},search:function(t){this.$refs.tableList.search(t)},advancedSearch:function(t){this.$refs.tableList.advancedSearch(t)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},changeBalance:function(t){this.online=t,this.bindData()}}},Q=Y,X=(a("e774"),Object(c["a"])(Q,i,o,!1,null,"0f61ca7a",null));e["default"]=X.exports},d280:function(t,e,a){"use strict";a("dc5e")},dc5e:function(t,e,a){},e05d:function(t,e,a){"use strict";a("0702")},e774:function(t,e,a){"use strict";a("b122")},f842:function(t,e,a){"use strict";a("a6d7")}}]);