(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5b413d72"],{"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},2419:function(t,e,a){"use strict";a("e8c8")},"52f7":function(t,e,a){},"5de3":function(t,e,a){"use strict";a("997a")},7953:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.formvisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[a("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnadd:function(e){return t.showform(0)},btnsearch:function(e){return t.$refs.tableList.bindData()},bindData:t.bindData,AdvancedSearch:t.AdvancedSearch,btnExport:t.btnExport,openImport:t.openImport,allDelete:function(e){return t.$refs.tableList.allDelete()}}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:t.showHelp?20:24}},[a("TableList",{ref:"tableList",on:{changeidx:t.changeidx,showform:t.showform,sendTableForm:t.sendTableForm}})],1),a("el-col",{attrs:{span:t.showHelp?4:0}},[a("HelpModel",{ref:"helpmodel",attrs:{code:"S06M16B2"}})],1)],1)],1)],1)])},o=[],s=(a("ac1f"),a("841c"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1)],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:t.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(e){t.iShow=!t.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:t.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单据编码",size:"small"},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"单据标题"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入单据标题",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"客户"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入客户",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"联系人"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入联系人",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman",e)},expression:"formdata.linkman"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.AdvancedSearch()}}},[t._v(" 搜索 ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"服务需求"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入服务需求",size:"small"},model:{value:t.formdata.sercontent,callback:function(e){t.$set(t.formdata,"sercontent",e)},expression:"formdata.sercontent"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"服务过程"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入服务过程",size:"small"},model:{value:t.formdata.serprocess,callback:function(e){t.$set(t.formdata,"serprocess",e)},expression:"formdata.serprocess"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"未尽事宜"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入未尽事宜",size:"small"},model:{value:t.formdata.serloss,callback:function(e){t.$set(t.formdata,"serloss",e)},expression:"formdata.serloss"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"简述"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入简述",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1)],1)],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.setColumsVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)}),r=[],l=a("8daf"),n={name:"Listheader",props:["tableForm"],components:{Setcolums:l["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},thorList:!0,setColumsVisible:!1,code:"S06M16B2List"}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList)},btnExport:function(){this.$emit("btnExport")}}},c=n,d=(a("9883"),a("2877")),m=Object(d["a"])(c,s,r,!1,null,"e1e7c218",null),u=m.exports,f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton()},clickMethods:t.clickMethods}})],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"min-height":"110px"},attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("name")}}},[a("el-form-item",{attrs:{label:"模板名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入模板名称",clearable:"",size:"small"},model:{value:t.formdata.name,callback:function(e){t.$set(t.formdata,"name",e)},expression:"formdata.name"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("revion")}}},[a("el-form-item",{attrs:{label:"版本",prop:"revion"}},[a("el-input",{attrs:{placeholder:"请输入版本",clearable:"",size:"small"},model:{value:t.formdata.revion,callback:function(e){t.$set(t.formdata,"revion",e)},expression:"formdata.revion"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("rownum")}}},[a("el-form-item",{attrs:{label:"行号",prop:"rownum"}},[a("el-input-number",{attrs:{min:0,label:"请输入行号",size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)])],1),a("el-row",{staticStyle:{"margin-top":"15px"}},[a("el-col",{attrs:{span:24}},[a("el-tabs",{staticStyle:{"min-height":"380px"},attrs:{"tab-position":"left"}},[a("el-tab-pane",{staticStyle:{"margin-bottom":"10px"},attrs:{label:"模板内容"}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入模板内容",size:"small",type:"textarea",autosize:{minRows:18,maxRows:24}},model:{value:t.formdata.velocity,callback:function(e){t.$set(t.formdata,"velocity",e)},expression:"formdata.velocity"}})],1)])],1)],1)],1)],1)],1),a("el-divider"),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"10px","margin-right":"20px"}}),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"S06M16B1Edit",commonurl:"/S06M16B1/printBill",weburl:"/S06M16B1/printWebBill",modelurl:"/SaReports/getListByModuleCode"}})],1)},p=[],h=a("b775");const b={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/S06M16B1/create",i).then(t=>{console.log(t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);h["a"].post("/S06M16B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){h["a"].get("/S06M16B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})}};var v=b,g=a("1975"),w=["id","name","velocity","revion","rownum","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],x={params:w},y=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],k=[],_={name:"Formedit",components:{MyEditor:g["a"]},props:["idx"],data:function(){return{title:"生成器模板",operateBar:y,processBar:k,formdata:{name:"",velocity:"",revion:"",rownum:0},formRules:{name:[{required:!0,trigger:"blur",message:"模板名称为必填项"}]},formLabelWidth:"100px",multi:0,selVisible:!1,formstate:0,submitting:0}},computed:{formcontainHeight:function(){return window.innerHeight-124+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;this.formstate=0,this.listLoading=!0,0!=this.idx&&h["a"].get("/S06M16B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},clickMethods:function(t){this[t.meth](t.param)},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this,e={};e=this.$getParam(x,e,this.formdata),this.submitting=1,0==this.idx?v.add(e).then((function(e){t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})):v.update(e).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败")})),setTimeout((function(){t.submitting=0}),5e3)},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){v.delete(e)})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},changeidx:function(t){this.dialogIdx=t}}},S=_,$=(a("e757"),Object(d["a"])(S,f,p,!1,null,"5f0116ef",null)),C=$.exports,F=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"pageStyle",style:{"min-height":t.tableMaxHeight+"px"}},[a("div",{staticClass:"left"},[t._m(0),a("div",{staticClass:"left-content"},[t.lst.length?a("ul",t._l(t.lst,(function(e,i){return a("li",{key:i,staticClass:"flex a-c j-s",class:i==t.lstActive?"active":"",on:{click:function(a){t.lstActive=i,t.selrow=e}}},[a("div",[a("p",{staticClass:"title"},[t._v(t._s(e.label))]),a("p",{staticClass:"description"},[t._v(" "+t._s(e.children?e.children.length:0)+"个文件 ")])]),a("div",[a("el-dropdown",{attrs:{trigger:"click",placement:"bottom"}},[a("span",{staticClass:"el-dropdown-link"},[a("i",{staticClass:"el-icon-more"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{nativeOn:{click:function(a){return t.openGroup(e)}}},[t._v("编辑")]),a("el-dropdown-item",{staticStyle:{color:"#f56c6c"},nativeOn:{click:function(a){return t.deleteGroup(e)}}},[t._v("删除")])],1)],1)],1)])})),0):a("el-empty",{attrs:{"image-size":100}})],1),a("div",{staticClass:"addbtn"},[a("el-button",{staticClass:"filter-item",staticStyle:{width:"90%"},attrs:{icon:"el-icon-plus",type:"primary",size:"small"},on:{click:function(e){return t.openGroup(0)}}},[t._v(" 新建 ")])],1)]),a("div",{staticClass:"right"},[a("div",{staticClass:"right-header"},[a("h3",[t._v(t._s(t.selrow.label||"内容"))]),a("el-button",{attrs:{size:"mini"},on:{click:function(e){t.idx=0,t.pid=t.selrow.id,t.gropuFormVisible=!0}}},[t._v(" 新增")])],1),a("div",{staticClass:"right-contnet"},[t.selrow.children&&t.selrow.children.length?a("div",[a("el-tree",{attrs:{data:t.selrow.children,"node-key":"id","expand-on-click-node":!1,"default-expand-all":!1},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.data;return[a("div",{staticClass:"treenode"},[a("div",{staticClass:"title"},[a("p",[a("i",{class:1===i.dirmark?"el-icon-folder":0===i.dirmark?"el-icon-document":"el-icon-document-delete"}),t._v(" "+t._s(i.label)+" "),a("span",{directives:[{name:"show",rawName:"v-show",value:!!i.dirmark,expression:"!!data.dirmark"}]},[t._v(" "+t._s(i.children?i.children.length:0)+"个文件")]),a("span",{directives:[{name:"show",rawName:"v-show",value:!!i.velocityname,expression:"!!data.velocityname"}]},[t._v("关联模板【"+t._s(i.velocityname)+"】")]),a("span",[t._v(t._s(i.remark))])])]),a("div",{staticClass:"operate"},[a("el-dropdown",{attrs:{trigger:"click",placement:"bottom"}},[a("span",{staticClass:"el-dropdown-link"},[a("i",{staticClass:"el-icon-more"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(e){return t.openGroup(i)}}},[t._v("编辑")]),a("el-dropdown-item",{directives:[{name:"show",rawName:"v-show",value:!!i.dirmark,expression:"!!data.dirmark"}],attrs:{icon:"el-icon-plus"},nativeOn:{click:function(e){t.idx=0,t.pid=i.id,t.gropuFormVisible=!0}}},[t._v("新增子项")]),a("el-dropdown-item",{directives:[{name:"show",rawName:"v-show",value:!i.dirmark,expression:"!data.dirmark"}],attrs:{icon:"el-icon-paperclip"},nativeOn:{click:function(e){return t.contactModel(i)}}},[t._v("关联模板")]),a("el-dropdown-item",{staticStyle:{color:"#f56c6c"},attrs:{icon:"el-icon-delete"},nativeOn:{click:function(e){return t.deleteGroup(i)}}},[t._v("删除")])],1)],1)],1)])]}}],null,!1,1675166291)})],1):a("el-empty",[t.selrow.label?a("el-button",{attrs:{type:"default"}},[t._v("刷新")]):t._e(),t.selrow.label?a("el-button",{attrs:{type:"primary"},on:{click:function(e){t.idx=0,t.pid=t.selrow.id,t.gropuFormVisible=!0}}},[t._v("新建")]):t._e()],1)],1)]),t.gropuFormVisible?a("el-dialog",{attrs:{title:"分组","append-to-body":!0,visible:t.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[a("Group",{ref:"group",attrs:{idx:t.idx,pid:t.pid},on:{bindData:t.bindData,closeDialog:function(e){t.gropuFormVisible=!1}}})],1):t._e(),t.contactModelVisible?a("el-dialog",{attrs:{title:"关联模板","append-to-body":!0,visible:t.contactModelVisible,width:"500px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.contactModelVisible=e}}},[a("div",{staticClass:"flex a-c"},[a("span",[t._v("模板：")]),a("autoComplete",{staticStyle:{flex:"1"},attrs:{size:"default",value:t.selrow.velocityid,baseurl:"/S06M16B1/getPageList",params:{name:"name",other:"revion"}},on:{setRow:t.setRow,autoClear:t.autoClear}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.contactModelVisible=!1}}},[t._v("取 消")])],1)]):t._e()],1)},D=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"left-header"},[a("h3",[t._v("分组")])])}],z=(a("d81d"),a("b0c0"),a("e9c4"),a("d3b7"),a("0643"),a("a573"),a("5b24")),M=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入名称",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入编码",size:"small"},model:{value:t.formdata.groupcode,callback:function(e){t.$set(t.formdata,"groupcode",e)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"文件类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择文件类型",size:"mini"},model:{value:t.formdata.dirmark,callback:function(e){t.$set(t.formdata,"dirmark",e)},expression:"formdata.dirmark"}},[a("el-option",{attrs:{label:"文件",value:0}}),a("el-option",{attrs:{label:"文件夹",value:1}}),a("el-option",{attrs:{label:"忽略",value:null}})],1)],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.modifydate,expression:"formdata.modifydate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)])},B=[],V=(a("b64b"),a("b0b8")),L={name:"Formedit",props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,deptid:"",groupcode:"",groupname:"",id:"",remark:"",rownum:0,dirmark:0},formRules:{grpkey:[{required:!0,trigger:"blur",message:"编码为必填项"}],grpname:[{required:!0,trigger:"blur",message:"名称为必填项"}]},formLabelWidth:"100px"}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx&&h["a"].get("/S06M16B2/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code&&(t.formdata=e.data.data)}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?h["a"].post("/S06M16B2/create",JSON.stringify(this.formdata)).then((function(e){t.$emit("closeDialog"),t.$emit("bindData")})):h["a"].post("/S06M16B2/update",JSON.stringify(this.formdata)).then((function(e){t.$emit("closeDialog"),t.$emit("bindData")}))},closeForm:function(){this.$emit("closeForm")},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){V.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=V.getFullChars(t)}}},O=L,E=(a("5de3"),Object(d["a"])(O,M,B,!1,null,"1257341b",null)),N=E.exports,H={components:{Group:N,autoComplete:z["a"]},data:function(){return{lst:[],selrow:{label:"",velocityid:"",velocityname:"",children:[]},gropuFormVisible:!1,pid:0,idx:"",lstActive:-1,rowActive:-1,contactModelVisible:!1,moduleVal:""}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},methods:{bindData:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};h["a"].post("/S06M16B2/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){var a=e.data.data.list.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname,remark:t.remark,dirmark:t.dirmark,velocityname:t.velocityname}}));t.lst=t.transData(a,"id","pid","children"),-1!=t.lstActive&&(t.selrow=t.lst[t.lstActive]),console.log(t.lst,t.lst.length,"sasa")}})).catch((function(e){t.$message.error(e||"请求错误")}))},openGroup:function(t){0==t?(this.idx=0,this.pid=0):(this.pid=t.pid,this.idx=t.id),this.gropuFormVisible=!0},deleteGroup:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){h["a"].get("/S06M16B2/delete?key=".concat(t.id)).then((function(){console.log("执行关闭保存"),e.$message.success({message:"删除成功！"}),e.bindData()})).catch((function(){e.$message.warning("删除失败")}))}))},contactModel:function(t){console.log(t),this.contactModelVisible=!0},setRow:function(t){console.log(t),this.selrow.velocityid=t.id,this.selrow.velocityname=t.name},autoClear:function(){this.selrow.velocityid="",this.selrow.velocityname=""},submitUpdate:function(){var t=this;h["a"].post("/S06M16B2/update",JSON.stringify(this.selrow)).then((function(e){200==e.data.code?(t.contactModelVisible=!1,t.$message.success(e.data.msg||"关联模板成功")):t.$message.warning(e.data.msg||"关联模板失败")}))},showform:function(t){this.$emit("showform",t)},transData:function(t,e,a,i){for(var o=[],s={},r=e,l=a,n=i,c=0,d=0,m=t.length;c<m;c++)s[t[c][r]]=t[c];for(;d<m;d++){var u=t[d],f=s[u[l]];f?(!f[n]&&(f[n]=[]),f[n].push(u)):o.push(u)}return o}}},T=H,I=(a("2419"),Object(d["a"])(T,F,D,!1,null,"fb4144de",null)),A=I.exports,R={name:"S06M16B2",components:{ListHeader:u,FormEdit:C,TableList:A},data:function(){return{title:"生成代码",lst:[],formvisible:!1,idx:0,thorList:!1,tableForm:{},showHelp:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.bindData()}))},sendTableForm:function(t){this.tableForm=t},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},btnExport:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.btnExport()}))},openImport:function(){var t=this;this.$nextTick((function(){t.$refs.tableList.openImport()}))},search:function(t){this.$refs.tableList.search(t)},AdvancedSearch:function(t){this.$refs.tableList.AdvancedSearch(t)},showform:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeidx:function(t){this.idx=t}}},j=R,G=(a("d523"),Object(d["a"])(j,i,o,!1,null,null,null));e["default"]=G.exports},"841c":function(t,e,a){"use strict";var i=a("d784"),o=a("825a"),s=a("1d80"),r=a("129f"),l=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=s(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var s=o(t),n=String(this),c=s.lastIndex;r(c,0)||(s.lastIndex=0);var d=l(s,n);return r(s.lastIndex,c)||(s.lastIndex=c),null===d?-1:d.index}]}))},9883:function(t,e,a){"use strict";a("a1e9")},"997a":function(t,e,a){},a1e9:function(t,e,a){},a954:function(t,e,a){},d523:function(t,e,a){"use strict";a("a954")},e757:function(t,e,a){"use strict";a("52f7")},e8c8:function(t,e,a){}}]);