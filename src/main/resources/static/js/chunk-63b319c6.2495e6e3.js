(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-63b319c6"],{"09f4":function(e,t,a){"use strict";a.d(t,"a",(function(){return n})),Math.easeInOutQuad=function(e,t,a,i){return e/=i/2,e<1?a/2*e*e+t:(e--,-a/2*(e*(e-2)-1)+t)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(e){window.setTimeout(e,1e3/60)}}();function o(e){document.documentElement.scrollTop=e,document.body.parentNode.scrollTop=e,document.body.scrollTop=e}function r(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function n(e,t,a){var n=r(),s=e-n,l=20,c=0;t="undefined"===typeof t?500:t;var d=function(){c+=l;var e=Math.easeInOutQuad(c,n,s,t);o(e),c<t?i(d):a&&"function"===typeof a&&a()};d()}},"0c9a":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",e._g({ref:"formedit",attrs:{idx:e.idx}},{compForm:e.compForm,closeForm:e.closeForm,changeidx:e.changeidx,bindData:e.bindData}))],1):a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnadd:function(t){return e.showform(0)},btnsearch:e.search,bindData:e.bindData,AdvancedSearch:e.AdvancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"sort-change":e.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["typename"==t.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(t){return e.showform(i.row.id)}}},[e._v(e._s(i.row.typename?i.row.typename:"名称"))]):"createdate"==t.itemcode||"modifydate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormats")(i.row[t.itemcode])))]):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.GetList}})],1)],1)],1)],1)])},o=[],r=(a("e9c4"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:e.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:e.btnadd}},[e._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}}),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{icon:e.iShow?"el-icon-arrow-up":"el-icon-arrow-down",size:"mini",title:"高级搜索"},on:{click:function(t){e.iShow=!e.iShow}}})],1)]),a("el-collapse-transition",[a("div",{directives:[{name:"show",rawName:"v-show",value:e.iShow,expression:"iShow"}],staticClass:"searchDetail"},[a("el-form",{ref:"formdata",attrs:{model:e.formdata,"label-width":"100px","auto-complete":"on"}},[a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"编码"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入编码",size:"small"},model:{value:e.formdata.typecode,callback:function(t){e.$set(e.formdata,"typecode",t)},expression:"formdata.typecode"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"名称"}},[a("el-input",{attrs:{clearable:"",placeholder:"请输入名称",size:"small"},model:{value:e.formdata.typename,callback:function(t){e.$set(e.formdata,"typename",t)},expression:"formdata.typename"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px","margin-top":"5px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.AdvancedSearch()}}},[e._v(" 搜索 ")])],1)],1)],1)],1)])],1)}),n=[],s={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M09B1TYPEList"}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},l=s,c=(a("1a5c"),a("2877")),d=Object(c["a"])(l,r,n,!1,null,"5cc5e0da",null),m=d.exports,p=a("333d"),f=a("b775"),u=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},on:{click:function(t){return e.submitForm("formdata")}}},[e._v(" 保 存")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[e._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!e.idx},nativeOn:{click:function(t){return e.deleteForm(e.idx)}}},[e._v("删 除")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(t){return e.closeForm(t)}}},[e._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:e.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":e.formLabelWidth,rules:e.formRules}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("typename")}}},[a("el-form-item",{attrs:{label:"名称",prop:"typename"}},[a("el-input",{attrs:{placeholder:"名称",clearable:"",size:"small"},on:{input:e.writeCode},model:{value:e.formdata.typename,callback:function(t){e.$set(e.formdata,"typename",t)},expression:"formdata.typename"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("typecode")}}},[a("el-form-item",{attrs:{label:"编码",prop:"typecode"}},[a("el-input",{attrs:{placeholder:"请输入编码",clearable:"",size:"small"},model:{value:e.formdata.typecode,callback:function(t){e.$set(e.formdata,"typecode",t)},expression:"formdata.typecode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(t){return e.cleValidate("typeicon")}}},[a("el-form-item",{attrs:{label:"图标",prop:"typeicon"}},[a("el-input",{attrs:{placeholder:"请输入图标",clearable:"",size:"small"},model:{value:e.formdata.typeicon,callback:function(t){e.$set(e.formdata,"typeicon",t)},expression:"formdata.typeicon"}})],1)],1)]),a("el-col",{attrs:{span:3}},[a("el-form-item",{attrs:{label:"高度"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:e.formdata.typeheight,callback:function(t){e.$set(e.formdata,"typeheight",t)},expression:"formdata.typeheight"}})],1)],1),a("el-col",{attrs:{span:2}},[a("div",{on:{click:function(t){return e.cleValidate("typecolor")}}},[a("el-form-item",{attrs:{label:"背景色",prop:"typecolor"}},[a("el-color-picker",{attrs:{predefine:e.predefineColors,size:"small"},model:{value:e.formdata.typecolor,callback:function(t){e.$set(e.formdata,"typecolor",t)},expression:"formdata.typecolor"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"行号"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{min:0,size:"small"},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":e.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormats")(e.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[e._v(e._s(e._f("dateFormats")(e.formdata.modifydate)))])])],1)],1)],1)],1)])])},h=[];a("b64b");const y={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);f["a"].post("/S06M09B1Type/create",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);f["a"].post("/S06M09B1Type/update",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{f["a"].get("/S06M09B1Type/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var b=y,w=a("b0b8"),g={name:"Formedit",components:{},props:["idx"],data:function(){return{title:"类型设置",formdata:{typename:"",typecode:"",typeicon:"",remark:"",rownum:0,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,typecolor:"#409eff",typeheight:1},formRules:{name:[{required:!0,trigger:"blur",message:"名称不能为空"}]},predefineColors:["#409EFF","#67C23A","#E6A23C","#F56C6C","#909399","#DCDFE6","#303133","#000000","#FFFFFF"],multi:0,selVisible:!1,formLabelWidth:"100px"}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(e,t){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var e=this;this.listLoading=!0,0!=this.idx&&f["a"].get("/S06M09B1Type/getEntity?key=".concat(this.idx)).then((function(t){console.log("==================",t),200==t.data.code?e.formdata=t.data.data:e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeForm()}}),e.listLoading=!1})).catch((function(t){e.listLoading=!1,e.$message.error("请求错误")}))},submitForm:function(e){var t=this;this.$refs.formdata.validate((function(e){if(!e)return console.log("error submit!!"),!1;t.saveForm()}))},saveForm:function(){var e=this;0==this.idx?b.add(this.formdata).then((function(t){console.log("新建====",t),200==t.code&&(e.$message.success("保存成功"),e.$emit("changeidx",t.data.id),e.$emit("bindData"),e.bindData(),e.$emit("compForm"))})).catch((function(t){e.$message.warning("保存失败")})):b.update(this.formdata).then((function(t){console.log("保存====",t),200==t.code&&(e.$message.success("保存成功"),e.$emit("bindData"),e.bindData(),e.$emit("compForm"))})).catch((function(t){e.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(e){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),b.delete(e).then((function(e){200==e.code&&t.$message.success("删除成功"),t.$emit("compForm")})).catch((function(){t.$message.warning("删除失败")}))})).catch((function(){}))},writeCode:function(e){w.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.typecode=w.getFullChars(e)},handleFocus:function(){this.selVisible=!0},handleBlur:function(){this.selVisible=!1},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}},v=g,x=(a("e7b2"),Object(c["a"])(v,u,h,!1,null,"30a3bd20",null)),S=x.exports,_={formcode:"S06M09B1TYPEList",item:[{itemcode:"typename",itemname:"名称",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"Sa_TodoType.typename"},{itemcode:"typecode",itemname:"编码",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_TodoType.typecode"},{itemcode:"typecolor",itemname:"颜色",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_TodoType.typecolor"},{itemcode:"typeicon",itemname:"图标",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_TodoType.typeicon"},{itemcode:"typeheight",itemname:"高度",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_TodoType.typeheight"},{itemcode:"remark",itemname:"备注",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_TodoType.remark"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_TodoType.lister"},{itemcode:"createdate",itemname:"创建日期",minwidth:"70",displaymark:1,sortable:1,overflow:1,datasheet:"Sa_TodoType.createdate"}]},k={name:"SYSM01B1",components:{Pagination:p["a"],listheader:m,formedit:S},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},tableForm:_}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.searchstr="",this.bindData()},methods:{bindData:function(){var e=this;this.listLoading=!0,f["a"].post("/S06M09B1Type/getPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},changeSort:function(e){"descending"==e.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(e,this.tableForm),this.bindData()},GetList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={engineercode:e,engineername:e,engineertype:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(e){this.idx=e,this.FormVisible=!0},closeForm:function(){this.bindData(),this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(e){this.idx=e}}},F=k,T=(a("6d8e"),Object(c["a"])(F,i,o,!1,null,"2dcada7b",null));t["default"]=T.exports},"1a5c":function(e,t,a){"use strict";a("8395")},6671:function(e,t,a){},"6d8e":function(e,t,a){"use strict";a("6671")},8395:function(e,t,a){},d68e:function(e,t,a){},e7b2:function(e,t,a){"use strict";a("d68e")}}]);