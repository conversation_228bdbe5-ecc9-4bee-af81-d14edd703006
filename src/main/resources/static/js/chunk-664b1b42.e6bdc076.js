(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-664b1b42"],{"4dae":function(t,a,e){"use strict";e("56e0")},"56e0":function(t,a,e){},"8a0f":function(t,a,e){"use strict";e.r(a);var i=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[e("div",{staticStyle:{padding:"20px"}},[e("div",[e("div",{staticStyle:{position:"absolute",right:"40px","z-index":"99"}},[e("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1),e("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[e("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[e("el-row",[e("el-col",{attrs:{span:12}},[e("div",{on:{click:function(a){return t.cleValidate("type")}}},[e("el-form-item",{attrs:{label:"提报类型",prop:"type"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"类型"},model:{value:t.formdata.type,callback:function(a){t.$set(t.formdata,"type",a)},expression:"formdata.type"}},[e("el-option",{attrs:{label:"bug",value:"bug"}}),e("el-option",{attrs:{label:"需求",value:"需求"}}),e("el-option",{attrs:{label:"客户反馈",value:"客户反馈"}}),e("el-option",{attrs:{label:"运维反馈",value:"运维反馈"}})],1)],1)],1)]),e("el-col",{attrs:{span:12}},[e("div",{on:{click:function(a){return t.cleValidate("source")}}},[e("el-form-item",{attrs:{label:"需求来源",prop:"source"}},[e("el-select",{staticStyle:{width:"100%"},model:{value:t.formdata.source,callback:function(a){t.$set(t.formdata,"source",a)},expression:"formdata.source"}},[e("el-option",{attrs:{label:"pms",value:"pms"}}),e("el-option",{attrs:{label:"rms",value:"rms"}}),e("el-option",{attrs:{label:"客户",value:"客户"}}),e("el-option",{attrs:{label:"内部",value:"内部"}}),e("el-option",{attrs:{label:"其他",value:"其他"}})],1)],1)],1)])],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("div",{on:{click:function(a){return t.cleValidate("groupname")}}},[e("el-form-item",{attrs:{label:"客户",prop:"groupname"}},[e("autoComplete",{attrs:{size:"default",value:t.formdata.groupname,baseurl:"/S06M14B1/getPageList",params:{name:"groupname",other:"groupuid"}},on:{setRow:function(a){return t.setRow(a)},autoClear:function(a){return t.autoClear()}}})],1)],1)]),e("el-col",{attrs:{span:12}},[e("div",{on:{click:function(a){return t.cleValidate("status")}}},[e("el-form-item",{attrs:{label:"进展阶段",prop:"status"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!!t.formdata.assessorid},model:{value:t.formdata.status,callback:function(a){t.$set(t.formdata,"status",a)},expression:"formdata.status"}},[e("el-option",{attrs:{label:"待审批",value:"待审批"}}),e("el-option",{attrs:{label:"已废弃",value:"已废弃"}})],1)],1)],1)])],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("div",{on:{click:function(a){return t.cleValidate("description")}}},[e("el-form-item",{attrs:{label:"提报内容",prop:"description"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入提报内容",clearable:"",autosize:{minRows:4,maxRows:10}},model:{value:t.formdata.description,callback:function(a){t.$set(t.formdata,"description",a)},expression:"formdata.description"}})],1)],1)])],1),e("el-row",[e("el-col",{attrs:{span:12}},[e("div",{on:{click:function(a){return t.cleValidate("deadlinedate")}}},[e("el-form-item",{attrs:{label:"截止日期",prop:"deadlinedate"}},[e("el-date-picker",{staticStyle:{width:"100%"},attrs:{"value-format":"yyyy-MM-dd HH:mm:ss","default-time":"17:00:00",type:"datetime",clearable:"",placeholder:"选择日期"},model:{value:t.formdata.deadlinedate,callback:function(a){t.$set(t.formdata,"deadlinedate",a)},expression:"formdata.deadlinedate"}})],1)],1)]),e("el-col",{attrs:{span:12}},[e("div",{on:{click:function(a){return t.cleValidate("level")}}},[e("el-form-item",{attrs:{label:"优先级",prop:"level"}},[e("el-select",{staticStyle:{width:"100%"},model:{value:t.formdata.level,callback:function(a){t.$set(t.formdata,"level",a)},expression:"formdata.level"}},[e("el-option",{attrs:{label:"较低",value:0}}),e("el-option",{attrs:{label:"普通",value:1}}),e("el-option",{attrs:{label:"紧急",value:2}}),e("el-option",{attrs:{label:"非常紧急",value:3}})],1)],1)],1)])],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("div",{on:{click:function(a){return t.cleValidate("assigneename")}}},[e("el-form-item",{attrs:{label:"执行者",prop:"assigneename"}},[e("autoComplete",{attrs:{size:"default",value:t.formdata.assigneename,baseurl:"/S06M02S2/getPageList",params:{name:"engineername",other:"engineertype"}},on:{setRow:t.setAssignee,autoClear:t.clearAssignee}})],1)],1)])],1),e("el-row",[e("el-col",{attrs:{span:24}},[e("div",{on:{click:function(a){return t.cleValidate("participants")}}},[e("el-form-item",{attrs:{label:"协作者",prop:"participants"}},[e("el-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,clearable:"",placeholder:"请选择人员"},model:{value:t.formdata.participants,callback:function(a){t.$set(t.formdata,"participants",a)},expression:"formdata.participants"}},t._l(t.engineerData,(function(t){return e("el-option",{key:t.id,attrs:{label:t.engineername,value:t.id}})})),1)],1)],1)])],1)],1)],1),e("el-form",{staticClass:"footFormContent"},[e("el-row",{staticStyle:{"margin-top":"0px"}},[e("el-col",{attrs:{span:24}},[e("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[e("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",clearable:""},model:{value:t.formdata.remark,callback:function(a){t.$set(t.formdata,"remark",a)},expression:"formdata.remark"}})],1)],1)],1)],1),e("el-row",[e("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px"},attrs:{span:24}},[e("div",[t.formdata.id&&!t.formdata.assessorid?e("el-button",{attrs:{type:"danger"},on:{click:function(a){return t.handleDelete()}}},[t._v("删除")]):t._e(),t.formdata.id?e("el-button",{attrs:{type:"success",disabled:"已废弃"==t.formdata.status},on:{click:function(a){return t.approval()}}},[t._v(t._s(t.formdata.assessorid?"反审核":"审核"))]):t._e(),t.formdata.id&&!t.formdata.assessorid?e("el-button",{attrs:{type:"success",disabled:"已废弃"==t.formdata.status},on:{click:function(a){return t.$refs.flowable.action()}}},[t._v("OA审批")]):t._e()],1),e("div",[t.formdata.assessorid?t._e():e("el-button",{attrs:{type:"primary"},on:{click:function(a){return t.submitForm("formdata")}}},[t._v("保存")]),e("el-button",{on:{click:t.closeDialog}},[t._v("关闭")])],1)])],1)],1)]),e("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"S06M02B3Edit",examineurl:"/S06M02B3/sendapprovel"}})],1)},o=[],s=(e("c740"),e("b64b"),e("d3b7"),e("ac1f"),e("5319"),e("0643"),e("4e3e"),e("159b"),e("b775"));const r={add(t){return new Promise((a,e)=>{var i=JSON.stringify(t);s["a"].post("/S06M02B3/create",i).then(t=>{200==t.data.code?a(t.data):e(t.data.msg)}).catch(t=>{e(t)})})},update(t){return new Promise((a,e)=>{var i=JSON.stringify(t);s["a"].post("/S06M02B3/update",i).then(t=>{200==t.data.code?a(t.data):e(t.data.msg)}).catch(t=>{e(t)})})},delete(t){return new Promise((a,e)=>{s["a"].get("/S06M02B3/delete?key="+t).then(t=>{200==t.data.code?a(t.data):e(t.data.msg)}).catch(t=>{e(t)})})}};var n=r,l=e("5b24"),d=e("acb9"),c={name:"addDialog",props:["idx","projectRow","isDialog","initData","billcode","engineerData"],components:{autoComplete:l["a"],Flowable:d["a"]},data:function(){return{title:"需求提报",currentId:this.idx,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,type:"",description:"",deadlinedate:"",assignee:"",assigneename:"",status:"待审批",source:"",level:0,groupid:"",groupname:"",participants:[],id:""},formRules:{type:[{required:!0,trigger:"blur",message:"类型不能为空"}],projectid:[{required:!0,trigger:"blur",message:"项目不能为空"}]}}},watch:{idx:function(t,a){this.currentId=t,this.bindData()}},mounted:function(){this.isDialog&&this.billSwitch(this.billcode)},methods:{bindData:function(){var t=this;0!=this.currentId&&s["a"].get("/S06M02B3/getEntity?key=".concat(this.currentId)).then((function(a){if(200==a.data.code){if(t.formdata=a.data.data,t.formdata.participants=t.formdata.participants?t.formdata.participants.split(","):[],t.formdata.assignee){var e=t.engineerData.findIndex((function(a){return a.id==t.formdata.assignee}));-1!=e&&t.$set(t.formdata,"assigneename",t.engineerData[e].engineername)}}else t.$message.warning(a.data.msg||"获取发布信息失败")})).catch((function(a){t.$message.error(a||"请求错误")}))},submitForm:function(t){var a=this;this.$refs["formdata"].validate((function(t){if(!t)return!1;a.saveForm()}))},saveForm:function(){var t=this,a=Object.assign({},this.formdata);if(this.formdata.participants.length){a.participants="",this.formdata.participants.forEach((function(t){a.participants+=t+","}));var e=/,$/gi;a.participants=a.participants.replace(e,"")}else a.participants="";console.log("paramsdata",a),0==this.idx?n.add(a).then((function(a){200==a.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.$emit("changeIdx",a.data.id),t.$nextTick((function(){t.bindData()})))})).catch((function(a){t.$message.warning(a||"保存失败")})):n.update(a).then((function(a){200==a.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",a.data.id),t.$emit("bindData"),t.$nextTick((function(){t.bindData()})))})).catch((function(a){t.$message.warning(a||"保存失败"),t.closeDialog()}))},handleDelete:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){n.delete(t.formdata.id).then((function(a){200==a.code&&(t.$message.success("删除成功"),t.$emit("bindData"),t.closeDialog())})).catch((function(){t.$message.warning("删除失败")}))})).catch((function(){}))},approval:function(){var t=this;this.formdata.assessorid||!this.formdata.oaflowmark||this.formdata.demandmark?s["a"].get("/S06M02B3/approval?key="+this.formdata.id).then((function(a){200==a.data.code?(t.$message.success(t.formdata.assessorid?"反审核成功":"审核成功"),t.$emit("bindData"),t.bindData()):t.$message.warning(a.data.msg||!t.formdata.assessorid?"审核失败":"反审核失败")})):this.$message.warning("单据正在OA审批中")},closeDialog:function(){this.isDialog,this.$emit("closeDialog")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},setRow:function(t){console.log(t),this.formdata.groupid=t.id,this.formdata.groupname=t.groupname,this.formdata.groupuid=t.groupuid,this.cleValidate("groupname")},autoClear:function(){this.formdata.groupid="",this.formdata.groupname="",this.formdata.groupuid=""},setAssignee:function(t){this.formdata.assignee=t.id,this.formdata.assigneename=t.engineername},clearAssignee:function(){this.formdata.assignee="",this.formdata.assigneename=""},billSwitch:function(t){}}},m=c,u=(e("4dae"),e("2877")),p=Object(u["a"])(m,i,o,!1,null,"642677e5",null);a["default"]=p.exports}}]);