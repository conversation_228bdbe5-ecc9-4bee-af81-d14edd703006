(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-67287e5a"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return o})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function r(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function s(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function o(t,e,a){var o=s(),n=t-o,l=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=l;var t=Math.easeInOutQuad(c,o,n,e);r(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"0d45":function(t,e,a){"use strict";a("ee9d")},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},3035:function(t,e,a){"use strict";a("f00a")},"456c":function(t,e,a){},5974:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selPwProcess",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"400px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}):a("el-table-column",{attrs:{label:"",width:"40",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"项目编码",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.projcode))])]}}])}),a("el-table-column",{attrs:{label:"名称",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.projname))])]}}])}),a("el-table-column",{attrs:{label:"类型",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.projtype))])]}}])}),a("el-table-column",{attrs:{label:"创建日期",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.createdate)))])]}}])}),a("el-table-column",{attrs:{label:"修改日期",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(t._f("dateFormat")(e.row.modifydate)))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},r=[],s=a("ade3"),o=(a("99af"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),a("b775")),n=a("333d"),l={components:{Pagination:n["a"]},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),r=e.getDate().toString().padStart(2,"0");return"".concat(a,"/").concat(i,"/").concat(r)}},props:["multi"],data:function(){return Object(s["a"])(Object(s["a"])({title:"工程项目",listLoading:!0,lst:[],searchstr:" ",strfilter:"",radio:"",selrows:"",total:0},"searchstr",""),"queryParams",{PageNum:1,PageSize:10,OrderType:1,SearchType:1})},watch:{},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,this.lst=[],o["a"].post("/S06M01S1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={projcode:t,projtype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},c=l,d=(a("b76b"),a("2877")),u=Object(d["a"])(c,i,r,!1,null,"29818629",null);e["a"]=u.exports},"5cc6":function(t,e,a){var i=a("74e8");i("Uint8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))},"5e9f":function(t,e,a){"use strict";a("a12a")},6198:function(t,e,a){"use strict";a("a08e")},"841c":function(t,e,a){"use strict";var i=a("d784"),r=a("825a"),s=a("1d80"),o=a("129f"),n=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=s(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var s=r(t),l=String(this),c=s.lastIndex;o(c,0)||(s.lastIndex=0);var d=n(s,l);return o(s.lastIndex,c)||(s.lastIndex=c),null===d?-1:d.index}]}))},a08e:function(t,e,a){},a12a:function(t,e,a){},b76b:function(t,e,a){"use strict";a("456c")},d919:function(t,e,a){"use strict";a("ffba")},ee9d:function(t,e,a){},f00a:function(t,e,a){},ffba:function(t,e,a){},ffe8:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),a("div",{directives:[{name:"show",rawName:"v-show",value:!t.FormVisible,expression:"!FormVisible"}],ref:"index",staticClass:"index"},[a("listheader",{on:{btnadd:function(e){return t.showform(0)},btnsearch:t.search,bindData:t.bindData,openList:t.openList,AdvancedSearch:t.AdvancedSearch,changeBalance:t.changeBalance}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}]},[a("tabList",{ref:"tableList",attrs:{online:t.online},on:{showform:t.showform,changeidx:t.changeidx}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}]},[a("cardList",{ref:"cardList",on:{changeidx:t.changeidx,showform:t.showform}})],1)])],1)],1)],1)])},r=[],s=(a("ac1f"),a("841c"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:function(e){return t.$emit("btnadd")}}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[t.isList?t._e():a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:t.isList?"el-icon-s-operation":"el-icon-s-grid"},on:{click:function(e){return t.openList()}}})],1)])])}),o=[],n={name:"Listheader",data:function(){return{strfilter:"",iShow:!1,formdata:{},balance:!0,isList:!1,code:"D06M02B1List"}},created:function(){},methods:{AdvancedSearch:function(){this.iShow=!1;var t={formdata:this.formdata};this.$emit("AdvancedSearch",t)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){var t={strfilter:this.strfilter};this.$emit("btnsearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},openList:function(){this.isList=!this.isList,this.$emit("openList",this.isList)},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)}}},l=n,c=(a("0d45"),a("2877")),d=Object(c["a"])(l,s,o,!1,null,"e34b2154",null),u=d.exports,m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,closeForm:t.closeForm,printButton:function(e){return t.$refs.PrintServer.printButton()},clickMethods:t.clickMethods}})],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{staticClass:"form form-head p-r"},[a("div",{staticClass:"flex j-end p-a",staticStyle:{top:"5px",right:"0"}},[a("div",{staticClass:"refNo flex j-end"},[a("div",{staticClass:"refNo-item"},[a("span",{staticStyle:{"margin-right":"10px"}},[t._v(" 日期： "),a("el-date-picker",{staticStyle:{width:"150px"},attrs:{type:"date",placeholder:"选择日期",size:"mini"},model:{value:t.formdata.billdate,callback:function(e){t.$set(t.formdata,"billdate",e)},expression:"formdata.billdate"}})],1),a("div",[a("span",[t._v("NO：")]),a("el-input",{staticStyle:{width:"150px"},attrs:{placeholder:"请输入单号",size:"mini",readonly:!!t.idx},model:{value:t.formdata.refno,callback:function(e){t.$set(t.formdata,"refno",e)},expression:"formdata.refno"}})],1)]),a("div",{staticStyle:{display:"flex","justify-content":"flex-end"}},[a("div",{staticStyle:{"margin-right":"10px",width:"90px"}},[a("svg-icon",{directives:[{name:"show",rawName:"v-show",value:!!t.formdata.assessor,expression:"!!formdata.assessor"}],staticStyle:{width:"80px",height:"80px"},attrs:{"icon-class":"approve"}})],1)])])]),a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules,"auto-complete":"on"}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[a("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"类型"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择类型",size:"small"},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}},[a("el-option",{attrs:{label:"新增",value:"新增"}}),a("el-option",{attrs:{label:"优化",value:"优化"}}),a("el-option",{attrs:{label:"纠错",value:"纠错"}})],1)],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"项目编码"}},[a("el-popover",{attrs:{placement:"bottom-start",trigger:"click"},on:{show:function(e){return t.$refs.selectGoods.bindData()}},model:{value:t.selVisible,callback:function(e){t.selVisible=e},expression:"selVisible"}},[a("selectGoods",{ref:"selectGoods",staticStyle:{width:"720px",height:"50vh"},attrs:{multi:0},on:{singleSel:t.selectGoods}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请输入项目编码",clearable:"",size:"small"},model:{value:t.formdata.itemcode,callback:function(e){t.$set(t.formdata,"itemcode",e)},expression:"formdata.itemcode"}},[a("i",{staticClass:"el-icon-circle-plus-outline",staticStyle:{"font-size":"15px",color:"rgb(64, 158, 255)"},attrs:{slot:"suffix"},slot:"suffix"})])],1)],1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"项目名称"}},[a("el-input",{attrs:{placeholder:"请输入项目名称",clearable:"",size:"small"},model:{value:t.formdata.itemname,callback:function(e){t.$set(t.formdata,"itemname",e)},expression:"formdata.itemname"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"8px"}},[a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("description")}}},[a("el-form-item",{attrs:{label:"需求描述",prop:"description"}},[a("el-input",{attrs:{placeholder:"请输入需求描述",clearable:"",size:"small",type:"textarea"},model:{value:t.formdata.description,callback:function(e){t.$set(t.formdata,"description",e)},expression:"formdata.description"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"地址"}},[a("el-input",{attrs:{placeholder:"请输入地址",clearable:"",size:"small"},model:{value:t.formdata.location,callback:function(e){t.$set(t.formdata,"location",e)},expression:"formdata.location"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-top":"8px"}},[a("el-col",{attrs:{span:24}},[a("div",{staticClass:"cardphoto"},[a("div",{staticClass:"cardphoto_left"},[t._v("现场照片")]),t._l(3,(function(e,i){return a("div",{key:i},[t.formdata["pictureurl"+e]||t.picture["pictureurl"+e]?a("div",{staticClass:"cardphotoImg"},[a("img",{attrs:{src:t.formdata["pictureurl"+e]?t.baseURL+t.formdata["pictureurl"+e]:t.picture["pictureurl"+e],alt:""}}),a("div",{staticClass:"cardphotoImg_icon",on:{click:function(a){return t.deletepic(e)}}},[a("i",{staticClass:"el-icon-delete",staticStyle:{color:"#a1a1a1"}})])]):t._e()])})),t.formdata.pictureurl3||t.picture.pictureurl3?t._e():a("div",{staticClass:"cardphotoImg cardphotoImg_text",on:{click:function(e){return t.$refs.uploadimage.click()}}},[t._v(" + ")])],2),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"uploadimage",attrs:{type:"file"},on:{change:t.getFile}})])])],1)],1)],1),a("div",{staticClass:"form-body form",staticStyle:{position:"relative"}}),a("el-divider"),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:18}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"经办人"}},[a("el-input",{attrs:{placeholder:"请输入经办人姓名",clearable:"",size:"small"},model:{value:t.formdata.operator,callback:function(e){t.$set(t.formdata,"operator",e)},expression:"formdata.operator"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"S06M02B1Edit",commonurl:"/S06M02B1/printBill",weburl:"/S06M02B1/printWebBill",modelurl:"/SaReports/getListByModuleCode"}})],1)},f=[],p=a("c7eb"),h=a("1da1"),b=(a("b64b"),a("d3b7"),a("5319"),a("b775"));const g={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);b["a"].post("/S06M02B1/create",i).then(t=>{console.log(t),200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);b["a"].post("/S06M02B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){b["a"].get("/S06M02B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})}};var v=g,w=a("1975"),y=a("5974"),x=["id","refno","billdate","billtype","billtitle","groupid","todoid","projectid","itemcode","itemname","description","operator","operatorid","longitude","latitude","location","phone","closer","closerid","pictureurl1","pictureurl2","pictureurl3","pictureurl4","pictureurl5","finpictureurl1","finpictureurl2","finpictureurl3","finpictureurl4","finpictureurl5","finishdes","remark","disannulmark","finishmark","custom1","custom2","custom3","custom4","custom5","custom6","custom7","custom8","custom9","custom10"],S={params:x},_=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]}],k=[],P=a("6ca8"),$=a.n(P),D={name:"Formedit",components:{MyEditor:w["a"],selectGoods:y["a"]},props:["idx"],data:function(){return{title:"需求单",operateBar:_,processBar:k,formdata:{refno:"",groupid:JSON.parse(window.localStorage.getItem("getInfo")).groupids?JSON.parse(window.localStorage.getItem("getInfo")).groupids.split(",")[0].replace(/'/g,""):"",billdate:new Date,billtitle:"",billtype:"新增",itemcode:"",itemname:"",operator:"",operatorid:"",pictureurl1:"",pictureurl2:"",pictureurl3:"",remark:""},formRules:{billtitle:[{required:!0,trigger:"blur",message:"标题为必填"}]},multi:0,formLabelWidth:"100px",selVisible:!1,workVisible:!1,formstate:0,submitting:0,picture:{pictureurl1:"",pictureurl2:"",pictureurl3:"",formData1:"",formData2:"",formData3:""},baseURL:this.$store.state.app.config.baseURL+"File/getImage/"}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},destroyed:function(){document.onkeydown=function(){},this.$setWs.wsClose()},methods:{bindData:function(){var t=this;this.formstate=0,0!=this.idx&&b["a"].get("/S06M02B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},clickMethods:function(t){this[t.meth](t.param)},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.getPicture()}))},getPicture:function(){var t=this;return Object(h["a"])(Object(p["a"])().mark((function e(){var a,i,r;return Object(p["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a=1;case 1:if(!(a<4)){e.next=9;break}return e.next=4,t.commitRefresh(t.picture["formData"+a]);case 4:i=e.sent,i&&(t.formdata["pictureurl"+a]=i);case 6:a++,e.next=1;break;case 9:r={pictureurl1:"",pictureurl2:"",pictureurl3:"",formData1:"",formData2:"",formData3:""},t.picture=r,t.saveForm();case 12:case"end":return e.stop()}}),e)})))()},commitRefresh:function(t){return new Promise((function(e,a){b["a"].post("/File/uploadPic",t).then((function(t){if(200==t.data.code)var a=t.data.data.dirname+"/"+t.data.data.filename;else a="";e(a)})).catch((function(t){a()}))}))},saveForm:function(){var t=this,e={};e=this.$getParam(S,e,this.formdata),this.submitting=1,0==this.idx?v.add(e).then((function(e){200==e.code&&(t.$message.success(e.msg||"保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1)})).catch((function(e){t.$message.warning(e||"保存失败")})):v.update(e).then((function(e){200==e.code&&(t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1)})).catch((function(e){t.$message.warning(e||"保存失败")})),setTimeout((function(){t.submitting=0}),5e3)},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){v.delete(e)})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},selectGoods:function(t){this.formdata.projectid=t.id,this.formdata.itemcode=t.projcode,this.formdata.itemname=t.projname,this.selVisible=!1,this.$refs.formdata.clearValidate("projectid")},getFile:function(){var t=this,e=this.$refs.uploadimage,a=e.files[0];$()(a).then((function(e){t.picture.pictureurl1||t.formdata.pictureurl1?t.picture.pictureurl2||t.formdata.pictureurl2?(t.picture.pictureurl3=e.base64,t.picture.formData3=e.formData):(t.picture.pictureurl2=e.base64,t.picture.formData2=e.formData):(t.picture.pictureurl1=e.base64,t.picture.formData1=e.formData)}))},deletepic:function(t){1==t?(this.formdata.pictureurl1=this.formdata.pictureurl2,this.formdata.pictureurl2=this.formdata.pictureurl3,this.formdata.pictureurl3="",this.picture.pictureurl1=this.picture.pictureurl2,this.picture.pictureurl2=this.picture.pictureurl3,this.picture.pictureurl3="",this.picture.formData1=this.picture.formData2,this.picture.formData2=this.picture.formData3,this.picture.formData3=""):2==t?(this.formdata.pictureurl2=this.formdata.pictureurl3,this.formdata.pictureurl3="",this.picture.pictureurl2=this.picture.pictureurl3,this.picture.pictureurl3="",this.picture.formData2=this.picture.formData3,this.picture.formData3=""):3==t&&(this.formdata.pictureurl3="",this.picture.pictureurl3="",this.picture.formData3="")}}},L=D,F=(a("6198"),Object(c["a"])(L,m,f,!1,null,"c20342e6",null)),C=F.exports,q=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"card-list",style:{height:t.tableMaxHeight}},[0!=t.lst.length?a("div",{staticStyle:{display:"flex","flex-wrap":"wrap",margin:"15px"}},[t._l(t.lst,(function(e,i){return[a("div",{key:i,staticClass:"listItem",class:{boderBlue:1==e.abnolevel,boderOrange:2==e.abnolevel,boderRed:3==e.abnolevel},attrs:{title:e.abnodepict}},[a("div",{staticStyle:{display:"flex","justify-content":"space-between","align-items":"center"}},[a("span",{staticClass:"filename",on:{click:function(a){return t.$emit("showform",e.id)}}},[a("i",{staticClass:"el-icon-edit"}),t._v(" "+t._s(e.billtitle))]),a("div",[a("span",[a("i",{staticClass:"el-icon-date"}),t._v(" "+t._s(t._f("dateFormat")(e.billdate)))]),a("span",{staticStyle:{"margin-left":"10px"},on:{click:function(a){return a.stopPropagation(),t.$emit("delItem",e.id,e)}}},[a("i",{staticClass:"el-icon-delete del-icon"})])])]),a("div",{staticClass:"ellipsis billtitle",staticStyle:{margin:"4px 0"}},[t._v(" "+t._s(e.billtitle)+" ")]),t._m(0,!0)])]}))],2):a("div",{staticClass:"noData"},[t._v("暂无数据")])])])},N=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between"}},[a("div",{staticClass:"itemdesc ellipsis"}),a("div")])}],j=(a("e9c4"),{data:function(){return{lst:[],nosubmit:0,queryParams:{PageNum:1,PageSize:1e3,OrderType:1,SearchType:1}}},computed:{tableMaxHeight:function(){return window.innerHeight-130+"px"}},methods:{bindData:function(){var t=this,e="/S06M02B1/getGroupPageList";b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list)})).catch((function(t){}))},search:function(t){var e=t.strfilter;""!=e?this.queryParams.SearchPojo={refno:e,billtype:e,billtitle:e,applicantdept:e,suppliers:e,itemname:e,itemcode:e,machuid:e,workuid:e,custpo:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t.formdata,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},getcolor:function(t){return 1==t?"#409eff":2==t?"#FFA500":"#FF0000"}}}),B=j,O=(a("5e9f"),Object(c["a"])(B,q,N,!1,null,"5072758e",null)),z=O.exports,T=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticClass:"table-container",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["refno"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showform(i.row.id)}}},[t._v(t._s(i.row.refno?i.row.refno:"编码"))]):"status"==e.itemcode?a("div",[1==i.row.finishmark?a("div",[a("span",{staticClass:"textborder-green"},[t._v("完成")])]):a("div")]):"modifydate"==e.itemcode||"billdate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},M=[],I=a("333d"),V={formcode:"S06M02B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:1,aligntype:"center",datasheet:"Sa_Demand.refno"},{itemcode:"billtype",itemname:"类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Demand.billtype"},{itemcode:"billtitle",itemname:"标题",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Demand.billtitle"},{itemcode:"billdate",itemname:"日期",minwidth:"100",displaymark:1,sortable:1,overflow:1,datasheet:"Sa_Demand.billdate"},{itemcode:"description",itemname:"内容描述",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.description"},{itemcode:"remark",itemname:"备注",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.remark"},{itemcode:"status",itemname:"状态",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.state"},{itemcode:"lister",itemname:"制表",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.lister"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.modifydate"}]},E=(a("48da"),{components:{Pagination:I["a"]},props:["online"],data:function(){return{lst:[],total:0,listLoading:!1,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:V}},computed:{tableMaxHeight:function(){return window.innerHeight-130+"px"}},methods:{bindData:function(){var t=this;if(this.online)var e="/S06M02B1/getOnlineGroupPageList";else e="/S06M02B1/getGroupPageList";b["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list)})).catch((function(t){}))},search:function(t){var e=t.strfilter;""!=e?this.queryParams.SearchPojo={refno:e,billtype:e,billtitle:e,applicantdept:e,suppliers:e,itemname:e,itemcode:e,machuid:e,workuid:e,custpo:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t.formdata,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(t,this.tableForm),this.bindData()},showform:function(t){this.$emit("showform",t)}}}),G=E,R=(a("d919"),Object(c["a"])(G,T,M,!1,null,"e1d8f732",null)),A=R.exports,H={name:"SYSM01B1",components:{listheader:u,formedit:C,tabList:A,cardList:z},data:function(){return{FormVisible:!1,online:1,thorList:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.bindData(),this.searchstr=""},methods:{bindData:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.cardList.bindData()})):this.$nextTick((function(){t.$refs.tableList.bindData()}))},search:function(t){this.thorList?this.$refs.cardList.search(t):this.$refs.tableList.search(t)},AdvancedSearch:function(t){this.thorList?this.$refs.cardList.AdvancedSearch(t):this.$refs.tableList.AdvancedSearch(t)},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(t){this.idx=t},changeBalance:function(t){this.online=t,this.bindData()},openList:function(t){var e=this;this.thorList=t,t&&(this.treeVisble=!1),setTimeout((function(){e.bindData()}),10)}}},J=H,U=(a("3035"),Object(c["a"])(J,i,r,!1,null,"2ee705d8",null));e["default"]=U.exports}}]);