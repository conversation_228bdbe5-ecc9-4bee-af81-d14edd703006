(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-672b5b3c"],{"328c":function(t,e,a){"use strict";a("9000")},4652:function(t,e,a){},"6c4b":function(t,e,a){},"8c99":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"wrap"},[i("div",{staticClass:"left"},[i("div",{staticClass:"left-item"},[i("el-input",{attrs:{readonly:"已完成"==t.status.statustype,placeholder:"请输入任务标题",clearable:""},on:{change:function(e){return t.changeMsg("任务标题")}},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1),i("div",{staticClass:"left-item"},[i("el-input",{attrs:{placeholder:"请输入任务描述",clearable:"",type:"textarea",autosize:{minRows:4,maxRows:6}},on:{change:function(e){return t.changeMsg("任务描述")}},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1),i("div",{staticClass:"left-item"},[t._m(0),i("div",{staticClass:"tag",class:"开始"==t.status.statustype?"tagBlue":"进行中"==t.status.statustype?"tagRed":"tagGray"},[t._v(" "+t._s(t.status.statustype)+" ")])]),i("div",{staticClass:"left-item"},[t._m(1),i("p",[t._v(t._s(t.formdata.createby))])]),i("div",{staticClass:"left-item"},[t._m(2),i("div",[i("el-popover",{ref:"appointeeRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeAppointee({engineerid:"",engineername:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.appointee?0:1,color:"#1b9aee"}}),i("span",[t._v("待认领")])]),t._l(t.projectList.item,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeAppointee(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.appointeeid==e.engineerid?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.engineername))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.appointeeid?i("span",{staticClass:"appointeeTag"},[t._v(t._s(t.formdata.appointee)+" ")]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("待认领")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(3),i("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择开始时间","default-time":"08:30:00",clearable:!0,editable:!1},on:{clear:function(e){t.formdata.startdate=""},input:function(e){t.formdata.startdate=e},change:function(e){return t.changeDate("开始时间")}},model:{value:t.formdata.startdate,callback:function(e){t.$set(t.formdata,"startdate",e)},expression:"formdata.startdate"}})],1),i("div",{staticClass:"left-item"},[t._m(4),i("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择截止时间","default-time":"17:00:00",clearable:!0,editable:!1},on:{clear:function(e){t.formdata.deaddate=""},input:function(e){t.formdata.deaddate=e},change:function(e){return t.changeDate("截止时间")}},model:{value:t.formdata.deaddate,callback:function(e){t.$set(t.formdata,"deaddate",e)},expression:"formdata.deaddate"}})],1),i("div",{staticClass:"left-item"},[t._m(5),i("el-popover",{ref:"labeljsonRef",attrs:{placement:"right-end",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"250px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.projectVal,expression:"projectVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索项目"},domProps:{value:t.projectVal},on:{input:[function(e){e.target.composing||(t.projectVal=e.target.value)},t.searchProject]}})]),t.projectAllListCopy.length?i("div",{staticStyle:{height:"220px","overflow-y":"auto"}},t._l(t.projectAllListCopy,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeProject(e)}}},[i("span",{class:"label-color"},[t._v(t._s(e.projname))]),i("div",[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.projectid==e.id?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无项目","image-size":90}})],1)])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.projectList.projname))])])])],1),i("div",{staticClass:"left-item"},[t._m(6),i("div",[i("el-popover",{ref:"selectTagRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-selectTag"}},[i("div",{ref:"selectTag",staticClass:"levelTag-content"},[i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(0)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:0==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag taggray"},[t._v("较低")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(1)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:1==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagblue"},[t._v("普通")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(2)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:2==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagorange"},[t._v("紧急")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(3)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:3==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")])])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[0==t.formdata.level?i("div",{staticClass:"levelTag taggray"},[t._v(" 较低 ")]):1==t.formdata.level?i("div",{staticClass:"levelTag tagblue"},[t._v(" 普通 ")]):2==t.formdata.level?i("div",{staticClass:"levelTag tagorange"},[t._v(" 紧急 ")]):3==t.formdata.level?i("div",{staticClass:"levelTag tagred"},[t._v(" 非常紧急 ")]):t._e()])])],1)]),i("div",{staticClass:"left-item"},[t._m(7),i("div",[i("el-popover",{ref:"labeljsonRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"220px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.showSetLbel,expression:"showSetLbel"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.labelVal,expression:"labelVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索标签"},domProps:{value:t.labelVal},on:{input:[function(e){e.target.composing||(t.labelVal=e.target.value)},t.searchLabel]}}),i("i",{staticClass:"el-icon-circle-plus-outline",on:{click:function(e){t.showSetLbel=!1,t.labeloperaType=1}}})]),t.labelList.length?i("div",{staticStyle:{height:"180px","overflow-y":"auto"}},t._l(t.labelList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeLabeljson(e)}}},[i("span",{class:"label-color label-"+e.labelcolor},[t._v(t._s(e.labelname))]),i("div",[i("i",{staticClass:"el-icon-edit",staticStyle:{color:"#15ad31"},on:{click:function(a){return a.stopPropagation(),t.editLabel(e)}}}),i("i",{staticClass:"el-icon-check",style:{opacity:-1!=t.formdata.labeljson.findIndex((function(t){return t.labelid==e.id}))?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无标签","image-size":100}})],1)]),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.showSetLbel,expression:"!showSetLbel"}]},[i("div",{staticClass:"createLabel flex j-s a-c"},[i("i",{staticClass:"el-icon-arrow-left",on:{click:function(e){t.showSetLbel=!0}}}),i("strong",[t._v("创建标签")]),i("i",{staticClass:"el-icon-close",on:{click:function(e){t.showSetLbel=!0,t.$refs["labeljsonRef"].doClose()}}})]),i("div",{},[i("el-input",{staticStyle:{padding:"10px","margin-top":"12px"},attrs:{placeholder:"标签名称"},model:{value:t.labelname,callback:function(e){t.labelname=e},expression:"labelname"}}),i("div",{staticClass:"flex",staticStyle:{padding:"10px","margin-top":"12px"}},[i("div",{staticClass:"disc-color disc-blue",on:{click:function(e){t.labelcolor="blue"}}},["blue"==t.labelcolor?i("i",{staticClass:"el-icon-check"}):t._e()]),i("div",{staticClass:"disc-color disc-green",on:{click:function(e){t.labelcolor="green"}}},["green"==t.labelcolor?i("i",{staticClass:"el-icon-check"}):t._e()]),i("div",{staticClass:"disc-color disc-zi",on:{click:function(e){t.labelcolor="zi"}}},["zi"==t.labelcolor?i("i",{staticClass:"el-icon-check"}):t._e()]),i("div",{staticClass:"disc-color disc-orange",on:{click:function(e){t.labelcolor="orange"}}},["orange"==t.labelcolor?i("i",{staticClass:"el-icon-check"}):t._e()]),i("div",{staticClass:"disc-color disc-red",on:{click:function(e){t.labelcolor="red"}}},["red"==t.labelcolor?i("i",{staticClass:"el-icon-check"}):t._e()])]),i("el-button",{staticStyle:{width:"calc(100% - 20px)",margin:"12px 10px"},attrs:{type:"primary"},on:{click:t.createLabel}},[t._v("创建")])],1)])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.labeljson.length?i("div",{staticStyle:{display:"inline-block",cursor:"pointer"}},[i("i",{staticClass:"el-icon-circle-plus-outline"})]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("添加标签")])])])],1),t.formdata.labeljson.length?i("div",{staticClass:"popper-labeljson labstyle"},t._l(t.formdata.labeljson,(function(e,a){return i("span",{key:a,class:"label-color label-"+e.color},[t._v(" "+t._s(e.name)+" "),i("i",{staticClass:"el-icon-close",on:{click:function(i){return t.closeLabeljson(e,a)}}})])})),0):t._e()]),i("div",{staticClass:"left-item"},[t._m(8),i("el-input-number",{attrs:{min:0,placeholder:"请输入工时",controls:!0,step:.5},on:{change:t.changeWorkTime},model:{value:t.formdata.worktime,callback:function(e){t.$set(t.formdata,"worktime",e)},expression:"formdata.worktime"}})],1),i("div",{staticClass:"left-item",staticStyle:{"align-items":"flex-start"}},[t._m(9),i("div",[i("el-popover",{ref:"workItemRef",attrs:{placement:"right-end",trigger:"click","popper-class":"popper-workItem"}},[i("addworkItem",{ref:"addworkItem",attrs:{projectList:t.projectList,formdata:t.formdata},on:{closePopver:function(e){return t.$refs["workItemRef"].doClose()},bindWorkItem:t.bindWorkItem}}),i("div",{staticClass:"left-item-workitem",attrs:{slot:"reference"},slot:"reference"},[i("span",{staticClass:"workitem-add"},[i("i",{staticClass:"el-icon-plus"}),t._v(" 添加子任务")])])],1),i("div",{staticClass:"workItemListSty"},t._l(t.workItemList,(function(e,a){return i("div",{key:a,staticClass:"workItemListSty-item",on:{click:function(a){return t.$emit("showWorkItem",e)}}},[i("span",{staticClass:"billtitle"},[t._v(t._s(a+1)+"、"+t._s(e.billtitle))]),i("span",{staticClass:"appointeeTag",staticStyle:{"margin-left":"15px"}},[t._v(t._s(e.appointee||"NA"))])])})),0)],1)]),i("div",{staticClass:"left-item"},[t._m(10),i("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},on:{change:t.changeWeekMark},model:{value:t.formdata.weekmark,callback:function(e){t.$set(t.formdata,"weekmark",e)},expression:"formdata.weekmark"}})],1)]),i("div",{staticClass:"right"},[i("div",{staticClass:"right-header"},[i("p",[t._v("参与者·"+t._s(t.projectList.item.length))]),i("div",t._l(t.projectList.item,(function(e,a){return i("span",{key:a,staticClass:"right-header-tag"},[t._v(t._s(e.engineername))])})),0)]),i("div",{staticClass:"right-content myscrollbar"},[i("div",{staticClass:"tigs"},[i("div",{staticClass:"tigs-item",class:0==t.islogtype?"tigs-active":"",on:{click:function(e){t.islogtype=0,t.getDemandLog()}}},[t._v(" 所有动态 ")]),i("div",{staticClass:"tigs-item",class:1==t.islogtype?"tigs-active":"",on:{click:function(e){t.islogtype=1,t.getDemandLog()}}},[t._v(" 仅评论 ")])]),i("div",{staticClass:"logs"},[t.logTotal>10?i("div",{staticClass:"logs-more",on:{click:function(e){return t.getDemandLog(!0)}}},[t._v(" 默认显示最新10条动态，点击查看所有... ")]):t._e(),t._l(t.logList,(function(e,s){return i("div",{key:s,staticClass:"logs-item"},["评论"==e.type?i("div",{staticClass:"type-content"},[i("p",{staticClass:"flex j-s"},[i("span",{staticStyle:{color:"#333"}},[i("i",{staticClass:"el-icon-user"}),t._v(" "+t._s(e.createby))]),i("span",[t._v(t._s(t._f("dateFormats")(e.createdate)))])]),i("p",{staticStyle:{color:"#505050","text-indent":"16px"},domProps:{innerHTML:t._s(e.content.replace(/\n/gm,"<br>"))}}),e.attachment.name?i("div",{staticClass:"fileUploadsty"},[i("div",{staticClass:"fileUploadShow"},[i("img",{attrs:{src:e.attachment.type?a("b484")("./"+e.attachment.type+".png"):a("692f"),alt:""}})]),i("div",{staticClass:"fileUploadInfo"},[i("p",{staticClass:"ellipsis"},[t._v("名称："+t._s(e.attachment.name))]),i("p",[t._v("大小："+t._s(e.attachment.size+"KB"))]),i("span",{staticClass:"downFile",on:{click:function(a){return t.downFileName(e.attachment)}}},[t._v(" 下载附件 ")]),i("span",{staticClass:"downFile",on:{click:function(a){return t.getFileName(e.attachment)}}},[t._v(" 预览附件 ")])])]):t._e()]):i("div",[i("p",{staticClass:"flex j-s",staticStyle:{color:"#8c8c8c","font-size":"12px"}},[i("span",{staticStyle:{flex:"1"}},[i("i",{staticClass:"el-icon-edit-outline"}),i("span",{staticStyle:{"margin-left":"6px"},domProps:{innerHTML:t._s(e.content.replace(/\n/gm,"<br>"))}})]),i("span",[t._v(t._s(t._f("dateFormats")(e.createdate)))])])])])}))],2)]),i("div",{staticClass:"right-footer"},[i("textarea",{directives:[{name:"model",rawName:"v-model",value:t.textareaVal,expression:"textareaVal"}],staticClass:"textareaSty",attrs:{cols:"30",rows:"3",placeholder:"请输入评论"},domProps:{value:t.textareaVal},on:{input:function(e){e.target.composing||(t.textareaVal=e.target.value)}}}),i("div",{staticClass:"filesty"},t._l(t.fileList,(function(e,a){return i("span",{key:a,staticStyle:{cursor:"default"}},[t._v(" "+t._s(e.name)+" "),i("i",{staticClass:"el-icon-close",staticStyle:{cursor:"pointer"},on:{click:function(i){return t.deleteFile(e,a)}}})])})),0),i("div",[i("i",{staticClass:"el-icon-paperclip paperclipFile",on:{click:t.openFileUpload}}),i("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:t.addLog}},[t._v("回复")])],1)])]),t.fileUploadVisible?i("el-dialog",{attrs:{title:"添加附件",visible:t.fileUploadVisible,width:"500px","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.fileUploadVisible=e}}},[i("FileUpload",{ref:"fileUpload",on:{closeDialog:function(e){t.fileUploadVisible=!1}}}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.fileUploadBtn}},[t._v("确定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.fileUploadVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.isViewPdf20?i("el-dialog",{attrs:{title:"附件预览",visible:t.isViewPdf20,top:"2vh",width:"80%","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[t.isImg?i("div",{staticStyle:{width:"100%","text-align":"center"}},[i("img",{staticStyle:{height:"auto",width:"auto"},attrs:{src:t.blobUrl,alt:""}})]):i("iframe",{staticStyle:{height:"80vh",width:"100%"},attrs:{id:"iframeId",src:t.blobUrl,frameborder:"0",scrolling:"auto"}})]):t._e()],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-folder-checked"}),t._v("状态")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user-solid"}),t._v("创建者")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user"}),t._v("执行者")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-date"}),t._v("开始时间")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-date"}),t._v("截止时间")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-coin"}),t._v("项目")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-s-flag"}),t._v("优先级")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-price-tag"}),t._v("标签")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-time"}),t._v("工时")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-edit-outline"}),t._v("子任务")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-share"}),t._v("周报")])}],l=(a("99af"),a("c740"),a("caad"),a("a434"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("25f0"),a("2532"),a("3ca3"),a("4d90"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("08dd")),o=a("6e25"),n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"warp"},[a("div",{staticClass:"title"},[t._v("添加子任务")]),a("el-form",{ref:"workdata",attrs:{model:t.workdata,"label-width":"60px"}},[a("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入任务标题",clearable:""},model:{value:t.workdata.billtitle,callback:function(e){t.$set(t.workdata,"billtitle",e)},expression:"workdata.billtitle"}})],1),a("el-form-item",{attrs:{label:"描述",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入任务描述",clearable:"",size:"small",type:"textarea",autosize:{minRows:3,maxRows:6}},model:{value:t.workdata.remark,callback:function(e){t.$set(t.workdata,"remark",e)},expression:"workdata.remark"}})],1),a("el-form-item",{attrs:{label:"执行者",prop:"appointeename"}},[a("el-select",{attrs:{clearable:"",placeholder:"请选择执行者"},on:{change:function(e){return t.setRow(e)},clear:function(e){return t.autoClear()}},model:{value:t.workdata.appointeename,callback:function(e){t.$set(t.workdata,"appointeename",e)},expression:"workdata.appointeename"}},t._l(t.projectList.item,(function(t){return a("el-option",{key:t.id,attrs:{label:t.engineername,value:t.id}})})),1)],1),a("el-form-item",{attrs:{label:"截止",prop:"remark"}},[a("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择截止时间","default-time":"17:00:00",clearable:!0,editable:!1},on:{clear:function(e){t.workdata.deaddate=""},input:function(e){t.workdata.deaddate=e}},model:{value:t.workdata.deaddate,callback:function(e){t.$set(t.workdata,"deaddate",e)},expression:"workdata.deaddate"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitWorkUpdate()}}},[t._v("提 交")]),a("el-button",{attrs:{type:"default",size:"small"},on:{click:function(e){return t.bindData()}}},[t._v("重 置")])],1)],1)],1)},r=[],c=a("5b24"),d={components:{autoComplete:c["a"],picker:o["default"]},props:["projectList","formdata"],data:function(){return{workdata:{billtype:"新增",billtitle:"",remark:"",appointeename:"",appointee:"",deaddate:""}}},methods:{bindData:function(){this.workdata={billtype:"新增",billtitle:"",remark:"",appointeename:"",appointee:"",deaddate:""}},submitWorkUpdate:function(){var t=this;if(this.workdata.billtitle){var e=Object.assign({},this.workdata);e.parentid=this.formdata.id,e.level=1,e.status=this.projectList.status[0].id,e.projectid=this.projectList.id,e.itemcode=this.projectList.projcode,e.itemname=this.projectList.projname,console.log(e,"paramobj"),this.$request.post("/S06M02B1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success("任务创建成功"),t.$emit("closePopver"),t.$emit("bindWorkItem")):t.$message.warning(e.data.msg||"任务创建失败")})).catch((function(e){t.$message.error(e||"请求失败")}))}else this.$message.warning("子任务标题不能为空")},setRow:function(t){var e=this.projectList.item.findIndex((function(e){return e.id==t}));-1!=e&&(this.workdata.appointee=this.projectList.item[e].engineerid,this.workdata.appointeename=this.projectList.item[e].engineername)},autoClear:function(){this.workdata.appointeename="",this.workdata.appointee=""}}},p=d,m=(a("c906"),a("2877")),f=Object(m["a"])(p,n,r,!1,null,"50c52f65",null),u=f.exports,g={props:["idx","projectList","status","projectAllList"],components:{FileUpload:l["a"],addworkItem:u,picker:o["default"]},data:function(){return{formdata:{labeljson:[],remark:"",billtitle:""},islogtype:0,logTotal:0,logList:[],labelList:[],labelListCopy:[],textareaVal:"",labelVal:"",labelname:"",labelId:"",showSetLbel:!0,labelcolor:"blue",labeloperaType:1,fileUploadVisible:!1,fileList:[],projectVal:"",projectAllListCopy:[],blobUrl:"",isViewPdf20:!1,isImg:!1,workItemList:[]}},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx&&this.$request.get("/S06M02B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formdata.weekmark=t.formdata.weekmark?1:0,t.formdata.labeljson=t.formdata.labeljson?JSON.parse(t.formdata.labeljson):[],t.labelList=[].concat(t.projectList.label),t.labelListCopy=[].concat(t.projectList.label),t.projectAllListCopy=[].concat(t.projectAllList),console.log("需求任务信息",t.formdata),console.log("项目信息",t.projectList),console.log("标签信息",t.labelList),console.log("projectAllListCopy",t.projectAllList),t.fileList=[],t.$forceUpdate(),t.getDemandLog(),t.bindWorkItem()):t.$message.warning(e.data.msg||"获取任务信息失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getDemandLog:function(t){var e=this,a={PageNum:1,PageSize:10,OrderType:1,SearchType:1,SearchPojo:{demandid:this.formdata.id}};this.islogtype&&(a.scenedata=[{field:"Sa_DemandLog.type",fieldtype:1,math:"equal",value:"评论"}]),t&&(a.PageSize=100),this.$request.post("/S06M02S1/getPageList",JSON.stringify(a)).then((function(t){if(200==t.data.code){e.logList=t.data.data.list;for(var a=0;a<e.logList.length;a++){var i=e.logList[a];i.attachment=JSON.parse(i.attachment)}e.logTotal=t.data.data.total,console.log(e.logList)}else e.$message.warning(t.data.msg||"获取任务日志失败")}))},openFileUpload:function(){this.fileList.length>=1?this.$message.warning("只能添加一个附件"):this.fileUploadVisible=!0},fileUploadBtn:function(){var t=this,e=new FormData;e.append("file",this.$refs.fileUpload.file),this.$request.post("/File/upload?dirname="+this.$refs.fileUpload.uploadFileName,e).then((function(e){if(200==e.data.code){var a={name:t.$refs.fileUpload.uploadFileName,type:t.$refs.fileUpload.uploadFileType,size:t.$refs.fileUpload.uploadFileSize,fileurl:"/"+e.data.data.dirname+"/"+e.data.data.filename};t.fileList.push(a),t.fileUploadVisible=!1}else t.$message.warning(e.data.msg||"上传失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},deleteFile:function(t,e){var a=this;this.$request.post("/File/deleteFile?bucketName=inkspms&objectName="+t.fileurl).then((function(t){200==t.data.code?a.fileList.splice(e,1):a.$message.warning(t.data.msg||"删除附件失败")})).catch((function(t){a.$message.error(t||"请求错误")}))},downFileName:function(t){this.axios.get("http://dev.inksyun.com:9080/inkspms"+t.fileurl,{responseType:"blob"}).then((function(e){var a=window.URL.createObjectURL(e.data),i=document.createElement("a");i.href=a,i.download=t.name,i.click(),i.remove()}))},getFileName:function(t){var e=this;console.log(t),this.axios.get("http://dev.inksyun.com:9080/inkspms"+t.fileurl,{responseType:"blob"}).then((function(a){var i="application/pdf";if(e.isImg=!1,"pdf"==t.type.toLowerCase())i="application/pdf";else if("txt"===t.type.toLowerCase())i="text/plain";else{if(["jpg","jpeg","gif","bmp","png"].includes(t.name.toLowerCase()))return e.isImg=!0,e.blobUrl=window.URL.createObjectURL(a.data),e.isViewPdf20=!0,void e.$forceUpdate();e.$utils.message("info","此类型文件不支持预览,请下载查看！")}var s=[];s.push(a.data),e.blobUrl=window.URL.createObjectURL(new Blob(s,{type:i})),e.isViewPdf20=!0}))},addLog:function(){var t=this,e={type:"评论",content:this.textareaVal,demandid:this.formdata.id};this.fileList.length&&(e.attachment=JSON.stringify(this.fileList[0])),this.$request.post("/S06M02S1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.textareaVal="",t.fileList=[],t.getDemandLog()):t.$message.warning(e.data.msg||"评论失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},changeMsg:function(t){var e=this,a={id:this.formdata.id};"任务描述"==t?a.remark=this.formdata.remark:a.billtitle=this.formdata.billtitle,this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(a){200==a.data.code?(e.formdata=a.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(a.data.msg||"修改"+t+"失败"),e.bindData())}))},changeAppointee:function(t){var e=this;this.$refs["appointeeRef"].doClose();var a={appointeeid:t.engineerid,appointee:t.engineername,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改执行者失败"),e.bindData())}))},changelevel:function(t){var e=this;this.$refs["selectTagRef"].doClose();var a={level:t,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改优先级失败"),e.bindData())}))},changeDate:function(t){var e=this,a={id:this.formdata.id};"截止时间"==t?this.formdata.deaddate?a.deaddate=this.dateFormat(this.formdata.deaddate):a.deaddate=new Date(0):this.formdata.startdate?a.startdate=this.dateFormat(this.formdata.startdate):a.startdate=new Date(0),this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(console.log(t.data),e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.$forceUpdate(),e.getDemandLog()):(e.$message.warning(t.data.msg||"修改时间失败"),e.bindData())}))},dateFormat:function(t){if(console.log(t),t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),l=e.getHours().toString().padStart(2,"0"),o=e.getMinutes().toString().padStart(2,"0");e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(s," ").concat(l,":").concat(o)}},changeWeekMark:function(t){var e=this,a={weekmark:t||0,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改工时失败"),e.bindData())}))},changeWorkTime:function(t){var e=this,a={worktime:t||0,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改工时失败"),e.bindData())}))},changeLabeljson:function(t){var e=this;console.log(t);var a=this.formdata.labeljson.findIndex((function(e){return e.labelid==t.id}));if(console.log(a),-1==a){var i={name:t.labelname,color:t.labelcolor,labelid:t.id};this.formdata.labeljson.push(i)}-1!=a&&this.formdata.labeljson.splice(a,1);var s={labeljson:this.formdata.labeljson,id:this.formdata.id};console.log(s),this.$request.post("/S06M02B1/update",JSON.stringify(s)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改标签失败"),e.bindData())}))},closeLabeljson:function(t){var e=this,a=this.formdata.labeljson.findIndex((function(e){return e.labelid==t.labelid}));-1!=a&&this.formdata.labeljson.splice(a,1);var i={labeljson:this.formdata.labeljson,id:this.formdata.id};console.log(i),this.$request.post("/S06M02B1/update",JSON.stringify(i)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改时间失败"),e.bindData())}))},createLabel:function(){var t=this,e={labelname:this.labelname,labelcolor:this.labelcolor,pid:this.projectList.id},a="标签创建";if(this.labeloperaType)var i="/S06M01S1/createLabel";else{i="/S06M01S1/updateLabel";e.id=this.labelId,a="标签修改"}this.$request.post(i,JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(a+"成功"),t.showSetLbel=!0,t.labelcolor="blue",t.$emit("bindData"),t.$nextTick((function(){t.bindData()}))):t.$message.warning(e.data.msg||a+"失败")}))},editLabel:function(t){console.log(t),this.labelname=t.labelname,this.labelcolor=t.labelcolor,this.labelId=t.id,this.showSetLbel=!1,this.labeloperaType=0},searchLabel:function(){if(this.labelVal){this.labelList=[];for(var t=0;t<this.labelListCopy.length;t++){var e=this.labelListCopy[t];e.labelname.includes(this.labelVal)&&this.labelList.push(e)}}else this.labelList=[].concat(this.labelListCopy)},changeProject:function(t){var e=this,a={projectid:t.id,itemname:t.projname,itemcode:t.projcode,status:t.status[0].id,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改项目失败"),e.bindData())}))},searchProject:function(){if(this.projectVal){this.projectAllListCopy=[];for(var t=0;t<this.projectAllList.length;t++){var e=this.projectAllList[t];e.projname.includes(this.projectVal)&&this.projectAllListCopy.push(e)}}else this.projectAllListCopy=[].concat(this.projectAllList)},bindWorkItem:function(){var t=this;console.log("获取子任务信息woekItemList");var e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1,OrderBy:"statusmodifydate",scenedata:[{field:"projectid",fieldtype:1,math:"equal",value:this.projectList.id},{field:"parentid",fieldtype:1,math:"equal",value:this.formdata.id}]},a="/S06M02B1/getPageList?own=true";this.$request.post(a,JSON.stringify(e)).then((function(e){200==e.data.code&&(t.workItemList=e.data.data.list,console.log(t.workItemList," this.workItemList"))}))}}},h=g,b=(a("328c"),a("d661"),Object(m["a"])(h,i,s,!1,null,"ebc2d66a",null));e["a"]=b.exports},9e3:function(t,e,a){},c906:function(t,e,a){"use strict";a("6c4b")},d661:function(t,e,a){"use strict";a("4652")}}]);