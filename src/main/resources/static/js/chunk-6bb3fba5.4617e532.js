(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6bb3fba5"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return r})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function r(t,e,a){var r=n(),s=t-r,l=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=l;var t=Math.easeInOutQuad(c,r,s,e);o(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},2401:function(t,e,a){"use strict";a("4d19")},"278e":function(t,e,a){"use strict";a("48a0")},"298a":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),0==t.FormVisible?a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm,showTree:t.treeVisble},on:{btnadd:function(e){return t.showform(0)},btnshowGroup:function(e){t.treeVisble=!t.treeVisble},btnsearch:t.search,bindData:t.bindData,AdvancedSearch:t.AdvancedSearch,allDelete:t.allDelete,btnExport:t.btnExport,btnHelp:t.btnHelp}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.treeVisble,expression:"treeVisble"}],attrs:{span:4}},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[a("div",{staticClass:"groupTitle"},[a("span",[t._v("分组")]),a("i",{staticClass:"el-icon-s-tools",style:{color:t.treeEditable?"#1e80ff":""},on:{click:function(e){return t.treeEdit()}}})]),a("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto",width:"100%"},attrs:{data:t.groupData,"node-key":"id","default-expand-all":""},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.node,o=e.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{on:{click:function(){return t.handleNodeClick(o)}}},[o.groupicon&&o.groupicon.includes("el-icon")?a("i",{class:o.groupicon}):o.groupicon?a("svg-icon",{style:{"font-size":"14px"},attrs:{"icon-class":o.groupicon}}):t._e(),t._v(" "+t._s(i.label))],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==o.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return t.editTreeNode(o)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"==o.id,expression:"data.id == '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return t.addTreeChild(o)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==o.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return t.delTreeNode(o)}}})],1)])}}],null,!1,1003400848)})],1)]),a("el-col",{attrs:{span:t.treeVisble?t.showHelp?16:20:t.showHelp?20:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableData",staticClass:"table-container",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},height:t.tableMaxHeight},on:{"selection-change":t.handleSelectionChange,"sort-change":t.changeSort}},[a("el-table-column",{attrs:{type:"selection",width:"50",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}],null,!1,3056706777)}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["goodsname"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showform(i.row.id)}}},[t._v(" "+t._s(i.row.goodsname?i.row.goodsname:"产品名称"))]):"publicmark"==e.itemcode?a("div",[1==i.row.publicmark?a("div",[a("span",{staticClass:"textborder-blue"},[t._v("公布")])]):a("div",[a("span",{staticClass:"textborder-grey"},[t._v("未公布")])])]):"createdate"==e.itemcode||"modifydate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):"operate"==e.itemcode?a("div",[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.setPageInfo(i.row)}}},[t._v(" 产品界面 ")])],1):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)],1),a("el-col",{attrs:{span:t.showHelp?4:0}},[a("helpmodel",{ref:"helpmodel",attrs:{code:"S06M13B1"}})],1)],1)],1)],1):t._e(),t.gropuFormVisible?a("el-dialog",{attrs:{title:"产品分组","append-to-body":!0,visible:t.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[a("group",t._g({ref:"group",attrs:{idx:t.idx,pid:t.pid},on:{closeDialog:function(e){t.gropuFormVisible=!1}}},{compForm:t.compForm,bindData:t.bindTreeData}))],1):t._e(),t.pageInfoVisible?a("el-dialog",{attrs:{title:"产品界面信息","append-to-body":!0,visible:t.pageInfoVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.pageInfoVisible=e}}},[a("div",{staticClass:"box-card"},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入界面信息",size:"small",type:"textarea",autosize:{minRows:20,maxRows:24}},model:{value:t.descjson,callback:function(e){t.descjson=e},expression:"descjson"}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{staticStyle:{float:"left"},attrs:{type:"default",size:"small"},on:{click:t.exportInfo}},[t._v("导入模板")]),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitPageInfo()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.pageInfoVisible=!1}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],n=a("c7eb"),r=a("b85c"),s=a("1da1"),l=a("2909"),c=(a("99af"),a("d81d"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("3ca3"),a("841c"),a("0643"),a("a573"),a("ddb0"),a("b775")),d=a("333d"),m=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.$refs.PrintServer.printButton()}}},[t._v("打 印")]),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")]),a("el-dropdown-item",{attrs:{divided:"",icon:"el-icon-upload"},nativeOn:{click:function(e){return t.$refs.upload.click()}}},[t._v(" 导入MarkDown ")]),a("el-dropdown-item",{attrs:{icon:"el-icon-download",disabled:!t.formdata.id},nativeOn:{click:function(e){return t.getExport()}}},[t._v(" 导出MarkDown ")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v("产品中心")]),a("el-row",[a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("gengroupid")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"gengroupid"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.groupnameOptions,props:t.defaultProps,"show-all-levels":!1,placeholder:"请选择分组名称",size:"small"},on:{change:t.handleChangeGroupid},model:{value:t.formdata.gengroupid,callback:function(e){t.$set(t.formdata,"gengroupid",e)},expression:"formdata.gengroupid"}})],1)],1)]),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"难易度"}},[a("el-rate",{staticStyle:{"line-height":"2.2"},attrs:{"show-score":"","text-color":"#ff9900"},model:{value:t.formdata.rateval,callback:function(e){t.$set(t.formdata,"rateval",e)},expression:"formdata.rateval"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("el-col",{attrs:{span:15}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("div",{on:{click:function(e){return t.cleValidate("goodscode")}}},[a("el-form-item",{attrs:{label:"产品编码",prop:"goodscode"}},[a("el-input",{attrs:{placeholder:"请输入产品编码",clearable:"",size:"small"},model:{value:t.formdata.goodscode,callback:function(e){t.$set(t.formdata,"goodscode","string"===typeof e?e.trim():e)},expression:"formdata.goodscode"}})],1)],1)]),a("el-col",{attrs:{span:8}},[a("div",{on:{click:function(e){return t.cleValidate("goodsname")}}},[a("el-form-item",{attrs:{label:"产品名称",prop:"goodsname"}},[a("el-input",{attrs:{placeholder:"请输入产品名称",clearable:"",size:"small"},model:{value:t.formdata.goodsname,callback:function(e){t.$set(t.formdata,"goodsname","string"===typeof e?e.trim():e)},expression:"formdata.goodsname"}})],1)],1)]),a("el-col",{attrs:{span:8}},[a("div",{on:{click:function(e){return t.cleValidate("author")}}},[a("el-form-item",{attrs:{label:"运维人员",prop:"author"}},[a("el-input",{attrs:{placeholder:"请输入运维人员",clearable:"",size:"small"},model:{value:t.formdata.author,callback:function(e){t.$set(t.formdata,"author","string"===typeof e?e.trim():e)},expression:"formdata.author"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("author")}}},[a("el-form-item",{attrs:{label:"App二维码",prop:"author"}},[a("el-input",{attrs:{placeholder:"请输入App二维码内容",clearable:"",size:"small"},model:{value:t.formdata.appurl,callback:function(e){t.$set(t.formdata,"appurl","string"===typeof e?e.trim():e)},expression:"formdata.appurl"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("desciption")}}},[a("el-form-item",{attrs:{label:"产品简介",prop:"desciption"}},[a("el-input",{staticStyle:{width:"80%","margin-top":"6px","margin-right":"20px"},attrs:{placeholder:"请输入产品简介",clearable:"",size:"small",type:"textarea",rows:2},model:{value:t.formdata.desciption,callback:function(e){t.$set(t.formdata,"desciption","string"===typeof e?e.trim():e)},expression:"formdata.desciption"}}),a("el-checkbox",{attrs:{label:"公布","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1)])],1)],1),a("el-col",{attrs:{span:5}},[a("div",{on:{click:function(e){return t.cleValidate("goodsphote")}}},[a("el-form-item",{attrs:{label:"封面图",prop:"goodsphote"}},[t.formdata.goodsphote?a("div",{staticClass:"cardphotoImg"},[a("el-popover",{ref:"addpopover",attrs:{placement:"right",trigger:"click"}},[a("img",{staticStyle:{"max-width":"1200px","max-height":"800px"},attrs:{src:t.formdata.goodsphote,alt:""}}),a("img",{attrs:{slot:"reference",src:t.formdata.goodsphote,alt:""},slot:"reference"})]),a("div",{staticClass:"cardphotoImg_icon",on:{click:function(e){return t.deletepic()}}},[a("i",{staticClass:"el-icon-delete",staticStyle:{color:"#a1a1a1"}})])],1):a("div",{staticClass:"cardphotoImg cardphotoImg_text",on:{click:function(e){return t.$refs.uploadimage.click()}}},[t._v(" + ")]),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"uploadimage",attrs:{type:"file"},on:{change:t.getPicFile}})])])],1)])],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("文件信息")]),a("el-row",{staticStyle:{margin:"15px 0 10px 0"}},[a("el-col",{attrs:{span:24}},[a("div",{style:{height:"calc("+t.formcontainHeight+" - 420px)"}},[a("mavon-editor",{ref:"md",staticClass:"markdown",style:{width:"100%",height:"100%","z-index":"99"},attrs:{value:t.formdata.markdowndata,subfield:t.prop.subfield,defaultOpen:t.prop.defaultOpen,toolbarsFlag:t.prop.toolbarsFlag,editable:t.prop.editable,scrollStyle:t.prop.scrollStyle,toolbars:t.prop.toolbars},on:{imgAdd:t.imgAdd,imgDel:t.imgDel}})],1)])],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"新建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.modifydate,expression:"formdata.modifydate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("PrintServer",{ref:"PrintServer",attrs:{formdata:t.formdata,printcode:"S06M13B1Edit",commonurl:"/S06M13B1/printBill",weburl:"/S06M13B1/printWebBill",modelurl:"/SaReports/getListByModuleCode"}}),a("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[a("input",{ref:"upload",attrs:{type:"file"},on:{change:t.getFile}})])],1)},u=[];a("2b3d"),a("bf19"),a("9861");const p={add(t){return console.log("add",t),new Promise((e,a)=>{var i=JSON.stringify(t);c["a"].post("/S06M13B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);c["a"].post("/S06M13B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{c["a"].get("/S06M13B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},checkGoodsUid(t){return new Promise((e,a)=>{var i=JSON.stringify(t);c["a"].post("/S06M13B1/getEntityBynsp",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},importEntity(t){return new Promise((e,a)=>{var i=JSON.stringify(t);c["a"].post("/S06M13B1/importEntity",i).then(t=>{200==t.data.code?e(t.data):a(t.data.message)}).catch(t=>{a(t)})})}};var f=p,h=a("6ca8"),g=a.n(h),b={name:"Formedit",components:{},props:["idx"],data:function(){return{formdata:{gengroupid:"",goodsname:"",author:"",desciption:"",goodsphote:"",markdowndata:"",mdurl:"",mdlooktimes:"",appurl:"",rateval:3,publicmark:0,remark:"",content:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{goodscode:[{required:!0,trigger:"blur",message:"货品编码不能为空"}],goodsname:[{required:!0,trigger:"blur",message:"产品名称不能为空"}]},formLabelWidth:"100px",groupnameOptions:[],defaultProps:{children:"children",label:"groupname",value:"id"},baseURL:this.$store.state.app.config.baseURL+"File/getImage/",pictureurl:"",picdata:""}},computed:{formcontainHeight:function(){return window.innerHeight-113+"px"},prop:function(){var t={subfield:!0,editable:!0,toolbarsFlag:!0,scrollStyle:!0,toolbars:{bold:!0,italic:!0,header:!0,underline:!0,strikethrough:!0,mark:!0,superscript:!0,subscript:!0,quote:!0,ol:!0,ul:!0,link:!0,imagelink:!0,code:!0,table:!0,fullscreen:!0,readmodel:!0,htmlcode:!0,help:!0,undo:!0,redo:!0,trash:!0,save:!1,navigation:!0,alignleft:!0,aligncenter:!0,alignright:!0,subfield:!0,preview:!0}};return t}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData(),this.BindDataByuidgroupname()},methods:{bindData:function(){var t=this;0!=this.idx&&c["a"].get("/S06M13B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formdata.mdurl&&t.readFile(t.formdata.mdurl)):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error(e||"请求错误")}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.formdata.content=this.$refs.md.d_value?this.$refs.md.d_value:"",0==this.idx?f.add(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.readFile(t.formdata.mdurl)})).catch((function(e){t.$message.warning(e||"保存失败")})):f.update(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),e.data.mdurl||t.readFile(e.data.mdurl)})).catch((function(e){t.$message.warning(e||"保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){f.delete(t).then((function(){e.$emit("compForm")})).catch((function(t){e.$message.warning(t||"删除失败")}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},readFile:function(t){var e=this;t&&c["a"].get("/File/getMinioUrl/"+t).then((function(t){e.formdata.markdowndata=t.data,e.$forceUpdate()}))},handleChangeGroupid:function(t){console.log(t),this.formdata.gengroupid=t[t.length-1]},BindDataByuidgroupname:function(){var t=this;c["a"].get("/SaBillGroup/getListByModuleCode?Code=S06M13B1").then((function(e){200==e.data.code&&(t.groupnameOptions=e.data.data)})).catch((function(t){}))},getPicFile:function(){var t=this,e=this.$refs.uploadimage,a=e.files[0];g()(a).then((function(e){c["a"].post("/File/uploadPic",e.formData).then((function(e){console.log(e),t.formdata.goodsphote=e.data.data.fileurl}))}))},deletepic:function(){this.formdata.frontphoto="",this.pictureurl="",this.picdata=""},imgAdd:function(t,e){var a=this;g()(e).then((function(e){c["a"].post("/File/uploadPic",e.formData).then((function(e){if(200==e.data.code){var i=a.baseURL+"File/getImage/"+e.data.data.dirname+"/"+e.data.data.filename;a.$refs.md.$img2Url(t,i)}else a.$message.warning("上传失败")}))}))},imgDel:function(){},getFile:function(){var t=this,e=this.$refs.upload,a=e.files[0],i=new FileReader;i.readAsText(a),i.onload=function(e){t.formdata.markdowndata=e.currentTarget.result,t.$forceUpdate()}},getExport:function(){var t=new Blob([this.formdata.markdowndata],{type:"text/plain"}),e=document.createElement("a");e.download=this.formdata.billtitle+".md",e.style.display="none",e.href=URL.createObjectURL(t),document.body.appendChild(e),e.click(),document.body.removeChild(e)}}},v=b,w=(a("f392"),a("2877")),x=Object(w["a"])(v,m,u,!1,null,"1c585bcc",null),y=x.exports,k={formcode:"S06M13B1List",item:[{itemcode:"goodscode",itemname:"产品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center",datasheet:"goodscode"},{itemcode:"goodsname",itemname:"产品名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"goodsname"},{itemcode:"author",itemname:"运维人员",minwidth:"80",displaymark:1,overflow:1,datasheet:"author"},{itemcode:"desciption",itemname:"产品描述",minwidth:"100",displaymark:1,overflow:1,datasheet:"desciption"},{itemcode:"publicmark",itemname:"状态",minwidth:"100",displaymark:1,overflow:1,datasheet:"lister"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"lister"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"100",displaymark:1,overflow:1,datasheet:"modifydate"},{itemcode:"operate",itemname:"操作",minwidth:"100",displaymark:1,overflow:1}]},S=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("div",{staticClass:"p-r",staticStyle:{display:"inline-block",height:"28px","margin-right":"5px",cursor:"pointer",color:"#383838"},on:{click:t.btnshowGroup}},[a("i",{staticClass:"p-r",class:[t.showTree?"el-icon-s-fold":"el-icon-s-unfold"],staticStyle:{"font-size":"20px",top:"4px"}})]),a("el-input",{staticClass:"filter-item",staticStyle:{width:"280px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-help",title:"帮助"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("Setcolums",{ref:"setcolums",attrs:{code:t.code,tableForm:t.tableForm},on:{bindData:t.bindData,closeDialog:function(e){t.setColumsVisible=!1}}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitUpdate()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.setColumsVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},$=[],_=a("8daf"),F={name:"Listheader",components:{Setcolums:_["a"]},props:["showTree","tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M13B1List",setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},AdvancedSearch:function(t){this.iShow=!1,this.$emit("AdvancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnadd:function(){this.$emit("btnadd")},btnshowGroup:function(){this.$emit("btnshowGroup")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},btnImport:function(){this.$emit("btnImport")},modelExport:function(){this.$emit("modelExport")},bindData:function(){this.$emit("bindData")},allDelete:function(){this.$emit("allDelete")},btnExport:function(){this.$emit("btnExport")}}},C=F,D=(a("4deb"),Object(w["a"])(C,S,$,!1,null,"47b56298",null)),P=D.exports,z=a("48da"),V=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入名称",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入编码",size:"small"},model:{value:t.formdata.groupcode,callback:function(e){t.$set(t.formdata,"groupcode",e)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"图标"}},[a("div",{staticClass:"flex a-c"},[a("el-input",{staticStyle:{width:"88%","margin-right":"10px"},attrs:{placeholder:"请输入图标",size:"small"},model:{value:t.formdata.groupicon,callback:function(e){t.$set(t.formdata,"groupicon",e)},expression:"formdata.groupicon"}}),t.formdata.groupicon&&t.formdata.groupicon.includes("el-icon")?a("i",{class:t.formdata.groupicon}):t.formdata.groupicon?a("svg-icon",{style:{"font-size":"14px"},attrs:{"icon-class":t.formdata.groupicon}}):t._e()],1)])],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)])},I=[],O=(a("25f0"),a("4d90"),a("b0b8")),N={name:"Formedit",filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),n=e.getHours().toString().padStart(2,"0"),r=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(n,":").concat(r,":").concat(s)}},props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",modulecode:"S06M13B1",groupcode:"",groupname:"",rownum:0,remark:"",groupicon:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx?c["a"].get("/SaBillGroup/getEntity?key=".concat(this.idx)).then((function(e){console.log("==================",e),200==e.data.code&&(t.formdata=e.data.data)})):this.formdata.parentid=this.idx},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?c["a"].post("/SaBillGroup/create",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")})):c["a"].post("/SaBillGroup/update",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")}))},closeForm:function(){this.$emit("closeDialog")},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){O.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=O.getFullChars(t)}}},B=N,T=(a("2401"),Object(w["a"])(B,V,I,!1,null,"fc2dcaba",null)),E=T.exports,q=a("0521"),L={name:"",content:"",image:"wizard_main_bg.png",svgicon:"datasheet",videourl:"",newsurl:"",cardlst:[{title:"快速入门",content:"教你如何快速上手",link:"",svgicon:"datasheet",bgcolor:"#e7f2ff"},{title:"在线文档",content:"涵盖系统的插件安装、用户手册、使用教程、常见问题的解决方案、以及二次开发等",link:"",svgicon:"datasheet",bgcolor:"#fff5e7"},{title:"企业版",content:"提供企业级应用场景，增强包提供高等级原厂服务支持最佳实践建议",link:"",svgicon:"datasheet",bgcolor:"#f6e7ff"}],videolst:[{title:"",videourl:"",image:""}],newList:[{title:"",url:""}]},j={name:"S06M13B1",components:{Pagination:d["a"],listheader:P,formedit:y,group:E,helpmodel:q["a"]},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:k,showHelp:!1,gropuFormVisible:!1,treeVisble:!0,groupData:[],treeEditable:!1,pid:0,selectList:[],selectgoods:{},pageInfoVisible:!1,pageInfo:{},descjson:""}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.bindTreeData()},mounted:function(){this.bindData(),this.getcolumn()},methods:{bindData:function(){var t=this;this.listLoading=!0,this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),c["a"].post("/S06M13B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getcolumn:function(){this.tableForm=k},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},bindTreeData:function(){var t=this;c["a"].get("SaBillGroup/getListByModuleCode?Code=S06M13B1").then((function(e){if(200==e.data.code){var a=e.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname,prefix:t.prefix,groupicon:t.groupicon}})),i=[{id:"0",pid:-1,label:"产品分组",prefix:!1,groupicon:"el-icon-menu"}],o=[].concat(Object(l["a"])(a),i);t.groupData=t.transData(o,"id","pid","children")}}))},setPageInfo:function(t){console.log(t),this.descjson=t.descjson?t.descjson:"",this.pageInfo=Object.assign({},t),this.pageInfo.mdurl&&this.readFile(this.pageInfo.mdurl),this.pageInfoVisible=!0},submitPageInfo:function(){var t=this;this.pageInfo.descjson=this.descjson,this.pageInfo.content=this.pageInfo.markdowndata,console.log(this.pageInfo),this.$request.post("/S06M13B1/update",JSON.stringify(this.pageInfo)).then((function(e){200==e.data.code?(t.$message.success("保存成功"),t.pageInfoVisible=!1,t.bindData()):t.$message.warning(e.data.msg||"保存失败")}))},exportInfo:function(){L.name=this.pageInfo.goodsname,L.content=this.pageInfo.desciption,this.descjson=JSON.stringify(L,null,"\t")},readFile:function(t){var e=this;t&&c["a"].get("/File/getMinioUrl/"+t).then((function(t){console.log(t,"asd"),e.pageInfo.markdowndata=t.data,e.$forceUpdate()}))},handleSelectionChange:function(t){this.selectList=t},allDelete:function(){var t=this;0!=this.selectList.length?this.$confirm("此操作将永久删除记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteRows()})).catch((function(){})):this.$message.warning("请先选择货品")},deleteRows:function(t,e){var a=this;return Object(s["a"])(Object(n["a"])().mark((function t(){var e,i,o,s,l,c;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e=a,i=a.selectList,!i){t.next=22;break}o=[],s=Object(r["a"])(i),t.prev=5,c=Object(n["a"])().mark((function t(){var e,a;return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e=l.value,a=new Promise((function(t,a){f.delete(e.id).then((function(i){console.log("删除",i),200==i.code?0==i.data?a("删除失败,"+e.goodsname+"在系统中已使用"):t("删除成功"):a("删除失败")})).catch((function(t){a("删除失败")}))})),o.push(a);case 3:case"end":return t.stop()}}),t)})),s.s();case 8:if((l=s.n()).done){t.next=12;break}return t.delegateYield(c(),"t0",10);case 10:t.next=8;break;case 12:t.next=17;break;case 14:t.prev=14,t.t1=t["catch"](5),s.e(t.t1);case 17:return t.prev=17,s.f(),t.finish(17);case 20:return t.next=22,Promise.all(o).then((function(t){e.$message.success("删除成功"),a.selectList=[],e.bindData()})).catch((function(t){e.$message.warning(t),e.bindData()}));case 22:a.$refs.tableData.clearSelection();case 23:case"end":return t.stop()}}),t,null,[[5,14,17,20]])})))()},btnExport:function(){var t=this;Promise.resolve().then(function(){for(var e=[],a=[],i=0;i<t.tableForm.item.length;i++){var o=t.tableForm.item[i];o.displaymark&&o.displaymark&&(e.push(o.itemname),a.push(o.itemcode))}var n=t.lst,r=t.formatJson(a,n);Object(z["a"])(e,r,"货品信息")}.bind(null,a)).catch(a.oe)},formatJson:function(t,e){return e.map((function(e){return t.map((function(t){return e[t]}))}))},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={gengroupid:t,prodcode:t,prodname:t,prodspec:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(t,this.tableForm),this.bindData()},showform:function(t){this.idx=t,this.FormVisible=!0},showGroupform:function(t){this.idx=t,this.gropuFormVisible=!0},closeForm:function(){this.FormVisible=!1,this.gropuFormVisible=!1,this.bindData()},compForm:function(){this.bindTreeData(),this.bindData(),this.FormVisible=!1,this.gropuFormVisible=!1},transData:function(t,e,a,i){for(var o=[],n={},r=e,s=a,l=i,c=0,d=0,m=t.length;c<m;c++)n[t[c][r]]=t[c];for(;d<m;d++){var u=t[d],p=n[u[s]];p?(!p[l]&&(p[l]=[]),p[l].push(u)):o.push(u)}return o},handleNodeClick:function(t){if(0==t.id){var e="";this.search(e)}else{var a=t.id;this.search(a)}},treeEdit:function(){this.treeEditable=!this.treeEditable},editTreeNode:function(t){this.pid=t.pid,this.showGroupform(t.id)},addTreeChild:function(t){this.pid=t.id,this.showGroupform(0)},delTreeNode:function(t){var e=this;t.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),c["a"].get("/SaBillGroup/delete?key=".concat(t.id)).then((function(){console.log("执行关闭保存"),e.$message.success({message:"删除成功！"}),e.bindTreeData()})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},changeidx:function(t){this.idx=t},submitExPort:function(){console.log(this.$refs.exportFile),this.$refs.exportFile.importf()}}},M=j,R=(a("278e"),Object(w["a"])(M,i,o,!1,null,null,null));e["default"]=R.exports},"48a0":function(t,e,a){},"4d19":function(t,e,a){},"4deb":function(t,e,a){"use strict";a("86e2")},"50b4":function(t,e,a){},"5cc6":function(t,e,a){var i=a("74e8");i("Uint8",(function(t){return function(e,a,i){return t(this,e,a,i)}}))},"841c":function(t,e,a){"use strict";var i=a("d784"),o=a("825a"),n=a("1d80"),r=a("129f"),s=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=n(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var n=o(t),l=String(this),c=n.lastIndex;r(c,0)||(n.lastIndex=0);var d=s(n,l);return r(n.lastIndex,c)||(n.lastIndex=c),null===d?-1:d.index}]}))},"86e2":function(t,e,a){},f392:function(t,e,a){"use strict";a("50b4")}}]);