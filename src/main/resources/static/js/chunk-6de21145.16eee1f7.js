(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6de21145"],{"09f4":function(t,e,i){"use strict";i.d(e,"a",(function(){return s})),Math.easeInOutQuad=function(t,e,i,a){return t/=a/2,t<1?i/2*t*t+e:(t--,-i/2*(t*(t-2)-1)+e)};var a=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function o(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function s(t,e,i){var s=n(),r=t-s,l=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=l;var t=Math.easeInOutQuad(c,s,r,e);o(t),c<e?a(d):i&&"function"===typeof i&&i()};d()}},"0b24":function(t,e,i){},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"12ea":function(t,e,i){"use strict";i("fb95")},"14b3":function(t,e,i){},2678:function(t,e,i){"use strict";i("30e3")},"2b6d":function(t,e,i){"use strict";i("14b3")},"30e3":function(t,e,i){},"311f":function(t,e,i){},"38e9":function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[t.formvisible?i("div",{ref:"formedit",staticClass:"formedit"},[i("FormEdit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeIdx:t.changeIdx,bindData:t.bindData}))],1):t._e(),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.formvisible,expression:"!formvisible"}],ref:"index",staticClass:"index"},[i("ListHeader",{attrs:{tableForm:t.tableForm},on:{btnAdd:function(e){return t.showForm(0)},btnSearch:t.search,bindData:t.bindData,advancedSearch:t.advancedSearch,changeModelUrl:t.changeModelUrl,btnExport:t.btnExport,changeBalance:t.changeBalance,bindColumn:function(e){t.thorList?t.$refs.tableTh.getColumn():t.$refs.tableList.getColumn()},changeOwn:t.changeOwn}}),i("div",{staticClass:"page-container"},[i("el-row",[i("el-col",{attrs:{span:24}},[i("TableTh",{directives:[{name:"show",rawName:"v-show",value:t.thorList,expression:"thorList"}],ref:"tableTh",attrs:{online:t.online,own:t.own},on:{changeIdx:t.changeIdx,sendTableForm:t.sendTableForm,showForm:t.showForm}}),i("TableList",{directives:[{name:"show",rawName:"v-show",value:!t.thorList,expression:"!thorList"}],ref:"tableList",attrs:{online:t.online},on:{changeIdx:t.changeIdx,sendTableForm:t.sendTableForm,showForm:t.showForm}})],1)],1)],1)],1)])},o=[],n=(i("ac1f"),i("841c"),function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container flex a-c j-s"},[i("div",{staticStyle:{display:"flex"}},[i("div",{staticClass:"flex infoForm a-c"}),i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnSearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnSearch},slot:"append"},[t._v("搜索 ")])],1),i("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-plus",plain:"",size:"mini"},on:{click:t.btnAdd}},[t._v(" 添加 ")])],1),i("div",{staticClass:"iShowBtn"},[i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"我的",size:"mini",border:""},on:{change:t.changeOwn},model:{value:t.isown,callback:function(e){t.isown=e},expression:"isown"}}),i("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"结存",size:"mini",border:""},on:{change:t.changeBalance},model:{value:t.balance,callback:function(e){t.balance=e},expression:"balance"}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){t.setColumsVisible=!0}}})],1)]),t.setColumsVisible?i("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[i("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e(),i("el-dialog",{attrs:{title:"高级筛选",width:"720px",visible:t.searchVisible,"append-to-body":!0,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.searchVisible=e}}},[i("SearchForm",{ref:"searchForm",attrs:{code:t.tableForm.formcode},on:{advancedSearch:t.advancedSearch,closedDialog:function(e){t.searchVisible=!1},bindData:t.bindData}})],1)],1)}),s=[],r=i("b893"),l={name:"ListHeader",props:["tableForm"],data:function(){return{strfilter:"",formdata:{},dateRange:Object(r["c"])(),pickerOptions:Object(r["g"])(),thorList:!0,balance:!0,setColumsVisible:!1,searchVisible:!1,isown:!0}},methods:{advancedSearch:function(t){var e={dateRange:this.dateRange,formdata:t};this.$emit("advancedSearch",e),this.searchVisible=!1},btnAdd:function(){this.$emit("btnAdd")},btnSearch:function(){var t={dateRange:this.dateRange,strfilter:this.strfilter};this.$emit("btnSearch",t)},bindData:function(){this.$emit("bindData")},btnExport:function(){this.$emit("btnExport")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList),this.$emit("bindColumn")},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},changeBalance:function(t){var e=0;e=t?1:0,this.$emit("changeBalance",e)},changeOwn:function(t){this.isown=t,this.$emit("changeOwn",t)}}},c=l,d=(i("2b6d"),i("2877")),m=Object(d["a"])(c,n,s,!1,null,"0699668a",null),u=m.exports,f=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[i("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"99"}},[i("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[i("EditGroupBtns",{ref:"EditGroupBtns",attrs:{showcontent:["save","print","operate","process"],formdata:t.formdata,operateBar:t.operateBar,processBar:t.processBar,formstate:t.formstate,submitting:t.submitting},on:{submitForm:t.submitForm,approval:t.approval,closeForm:t.closeForm,printButton:function(e){return t.printButton()},clickMethods:t.clickMethods}})],1)]),i("div",{staticStyle:{padding:"20px","box-sizing":"content-box",height:"100%",overflow:"auto"}},[i("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[i("FormTemp",{ref:"formtemp",staticStyle:{width:"100%",display:"flex","flex-direction":"column",height:"100%"},attrs:{formdata:t.formdata,formtemplate:t.formtemplate},on:{clickMethods:t.clickMethods},scopedSlots:t._u([t.formtemplate.header.type?null:{key:"Header",fn:function(){return[i("div",{staticClass:"form form-head p-r"},[i("EditHeader",{ref:"formHeader",attrs:{title:t.title,formdata:t.formdata,engineerData:t.engineerData},on:{setOperatorRow:t.setOperatorRow,autoClear:t.autoClear}})],1)]},proxy:!0},{key:"Item",fn:function(){return[i("div",{staticClass:"form-body form f-1",staticStyle:{"margin-top":"10px"}},[i("EditItem",{ref:"elitem",staticStyle:{width:"99%"},attrs:{lstitem:t.formdata.item,formdata:t.formdata,formtemplateItem:t.formtemplate.item,idx:t.idx,formstate:t.formstate},on:{bindData:t.bindData}})],1)]},proxy:!0},t.formtemplate.footer.type?null:{key:"Footer",fn:function(){return[i("div",[i("EditFooter",{attrs:{formdata:t.formdata}})],1)]},proxy:!0}],null,!0)})],1)]),i("Flowable",{ref:"flowable",attrs:{formdata:t.formdata,examinecode:"S06M19B1Edit",examineurl:"/S06M19B1/sendapprovel"}}),t.operationVisible?i("el-dialog",{staticClass:"operationDialog",attrs:{width:"90vw",visible:t.operationVisible,"close-on-click-modal":!1,"destroy-on-close":!0,"append-to-body":!0,top:"1vh","show-close":!1},on:{"update:visible":function(e){t.operationVisible=e}}}):t._e(),t.processVisible?i("el-dialog",{attrs:{title:t.processTitle,"append-to-body":!0,width:"90vw",visible:t.processVisible,"destroy-on-close":!0,"show-close":!0,top:"1vh"},on:{"update:visible":function(e){t.processVisible=e}}}):t._e(),i("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[i("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return i("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button-group",{staticStyle:{float:"left"}},[i("el-button",{attrs:{type:"print"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="print",t.submitRemoteReport()}}},[t._v("云打印")]),i("el-button",{attrs:{type:"preview"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="preview",t.submitRemoteReport()}}},[t._v("云预览")])],1),i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("打印")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),i("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[i("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})]),t.historyvisible?i("el-dialog",{attrs:{title:"历史单据信息","append-to-body":!0,visible:t.historyvisible,width:"1200px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.historyvisible=e}}},[i("HistoryBill",{ref:"Historybill",attrs:{multi:0,selecturl:"/S06M19B1/getBillList"}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.submitHistory()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.historyvisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},h=[],p=(i("a4d3"),i("e01a"),i("4de4"),i("7db0"),i("a15b"),i("d81d"),i("e9c4"),i("b64b"),i("d3b7"),i("3ca3"),i("0643"),i("2382"),i("fffc"),i("a573"),i("ddb0"),i("2b3d"),i("bf19"),i("9861"),i("b775"));const b={add(t){return new Promise((e,i)=>{var a=JSON.stringify(t);p["a"].post("/S06M19B1/create",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},update(t){return new Promise((e,i)=>{var a=JSON.stringify(t);p["a"].post("/S06M19B1/update",a).then(t=>{200==t.data.code?e(t.data):i(t.data.msg)}).catch(t=>{i(t)})})},delete(t){p["a"].get("/S06M19B1/delete?key="+t.formdata.id).then(e=>{200==e.data.code?0==e.data.data?t.$message.warning("删除失败,"+e.data.msg+"中已使用"):(t.$message.success(e.data.msg||"删除成功"),t.$emit("compForm")):t.$message.warning(e.data.msg||"删除失败")}).catch(e=>{t.$message.error(er||"服务请求错误")})},approval(t){t.submitting=1,p["a"].get("/S06M19B1/approval?key="+t.formdata.id).then(e=>{200==e.data.code?(t.$message.success(e.data.msg||!t.formdata.assessor?"审核成功":"反审核成功"),t.formdata=e.data.data,t.$emit("bindData"),t.submitting=0,t.formstate=t.formdata.assessor?2:1,t.stockwarningVisible=!1):(t.submitting=0,t.$message.warning(e.data.msg||!t.formdata.assessor?"审核失败":"反审核失败"))}).catch(e=>{t.submitting=0,t.$message.error(e||"服务请求错误")})},reqitembill(t,e,i){if(1==t||2==t)var a=1==t?"作废":"启用",o="/S06M19B1/disannul?type="+(1==t?1:0);else a=3==t?"中止":"启用",o="/S06M19B1/closed?type="+(3==t?1:0);p["a"].post(o,JSON.stringify(e)).then(t=>{200==t.data.code?(i.$message.success(t.data.msg||a+"成功"),i.$emit("bindData"),i.formdata=t.data.data):i.$message.warning(t.data.msg||a+"失败")}).catch(t=>{i.$message.error(t||"服务请求错误")})}};var g=b,v=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[i("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("type")}}},[i("el-form-item",{attrs:{label:"类型",prop:"type"}},[i("el-popover",{attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}},model:{value:t.selDictionariesRefVisible,callback:function(e){t.selDictionariesRefVisible=e},expression:"selDictionariesRefVisible"}},[i("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_Tracking.type"},on:{singleSel:function(e){t.selDictionariesRefVisible=!1,t.formdata.type=e.dictvalue,t.cleValidate("type")},closedic:function(e){t.selDictionariesRefVisible=!1}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"请选择类型",clearable:"",size:"small"},model:{value:t.formdata.type,callback:function(e){t.$set(t.formdata,"type",e)},expression:"formdata.type"}})],1)],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("subject")}}},[i("el-form-item",{attrs:{label:"主题",prop:"subject"}},[i("el-input",{attrs:{placeholder:"请输入主题",clearable:"",size:"small"},model:{value:t.formdata.subject,callback:function(e){t.$set(t.formdata,"subject","string"===typeof e?e.trim():e)},expression:"formdata.subject"}})],1)],1)]),i("div",{staticClass:"proPlanStyle"},[i("el-progress",{attrs:{type:"circle",percentage:t.schedule(),"stroke-width":15,format:t.prformat}}),i("p",{staticStyle:{"text-align":"center"}},[t._v("排程百分比")])],1),i("div",{staticClass:"proFinStyle"},[i("el-progress",{attrs:{type:"circle",percentage:t.findule(),"stroke-width":15,format:t.finformat}}),i("p",{staticStyle:{"text-align":"center"}},[t._v("完工百分比")])],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("customer")}}},[i("el-form-item",{attrs:{label:"客户",prop:"customer"}},[i("el-popover",{attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selcustomer.bindData()}},model:{value:t.selCustVisible,callback:function(e){t.selCustVisible=e},expression:"selCustVisible"}},[i("selDictionaries",{ref:"selcustomer",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_Tracking.customer"},on:{singleSel:function(e){t.selCustVisible=!1,t.formdata.customer=e.dictvalue,t.cleValidate("customer")},closedic:function(e){t.selCustVisible=!1}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"请选择客户",clearable:"",size:"small"},model:{value:t.formdata.customer,callback:function(e){t.$set(t.formdata,"customer",e)},expression:"formdata.customer"}})],1)],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("project")}}},[i("el-form-item",{attrs:{label:"工程项目",prop:"project"}},[i("el-popover",{attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selproject.bindData()}},model:{value:t.selProjectVisible,callback:function(e){t.selProjectVisible=e},expression:"selProjectVisible"}},[i("selDictionaries",{ref:"selproject",staticStyle:{width:"200px"},attrs:{multi:0,billcode:"Sa_Tracking.project"},on:{singleSel:function(e){t.selProjectVisible=!1,t.formdata.project=e.dictvalue,t.cleValidate("project")},closedic:function(e){t.selProjectVisible=!1}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"请选择工程项目",clearable:"",size:"small"},model:{value:t.formdata.project,callback:function(e){t.$set(t.formdata,"project",e)},expression:"formdata.project"}})],1)],1)],1)],1)]),i("el-col",{attrs:{span:5}},[i("el-form-item",{attrs:{label:""}},[i("el-checkbox",{attrs:{label:"公共","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("operator")}}},[i("el-form-item",{attrs:{label:"经办人",prop:"operator"}},[i("autoComplete",{attrs:{size:"small",value:t.formdata.operator,baseurl:"/S06M02S2/getPageList",params:{name:"engineername",other:"engineertype"}},on:{setRow:function(e){return t.$emit("setOperatorRow",e)},autoClear:function(e){return t.$emit("autoClear")}}})],1)],1)]),i("el-col",{attrs:{span:5}},[i("div",{on:{click:function(e){return t.cleValidate("assistantid")}}},[i("el-form-item",{attrs:{label:"协助人员",prop:"assistantid"}},[i("el-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,clearable:"",placeholder:"请选择人员"},model:{value:t.formdata.assistantid,callback:function(e){t.$set(t.formdata,"assistantid",e)},expression:"formdata.assistantid"}},t._l(t.engineerData,(function(t){return i("el-option",{key:t.id,attrs:{label:t.engineername,value:t.id}})})),1)],1)],1)])],1)],1)},w=[],y=i("5c73"),x=i("5b24"),k={props:{formdata:{type:Object},title:{type:String},engineerData:{type:Array}},components:{selDictionaries:y["a"],autoComplete:x["a"]},data:function(){return{formRules:{type:[{required:!0,trigger:"blur",message:"类型"}]},selDictionariesRefVisible:!1,selCustVisible:!1,selProjectVisible:!1}},methods:{cleValidate:function(t){this.$refs.formdata.clearValidate(t)},schedule:function(){if(this.formdata.planfinishcount){var t=this.formdata.planfinishcount/this.formdata.itemcount*100;return t}return 0},prformat:function(){if(this.formdata.planfinishcount){var t=this.formdata.planfinishcount+"/"+this.formdata.itemcount;return t}return"0"},findule:function(){if(this.formdata.billfinishcount){var t=this.formdata.billfinishcount/this.formdata.itemcount*100;return t}return 0},finformat:function(){if(this.formdata.billfinishcount){var t=this.formdata.billfinishcount+"/"+this.formdata.itemcount;return t}return"0"}}},S=k,$=(i("2678"),Object(d["a"])(S,v,w,!1,null,"bdcd123c",null)),_=$.exports,C=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{ref:"elitem",staticClass:"flex f-d-c form",staticStyle:{height:"100%"}},[i("ItemGroupBtns",{ref:"itemGroupBtns",attrs:{showcontent:["add","moveup","movedown","delete","copyrow"],formdata:t.formdata,tableForm:t.tableForm,formstate:t.formstate,lst:t.lst,multipleSelection:t.multipleSelection},on:{bindData:function(e){return t.$emit("bindData")},copyRow:function(e){return t.copyRow()},getAdd:t.getAdd,getColumn:t.getColumn,clearSelection:function(e){t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[]}}}),i("div",{staticClass:"table-container f-1 table-position"},[i("ve-table",{key:t.keynum,ref:"multipleTable",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"rowKeys","max-height":t.tableHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.columsData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,editOption:t.editOption,"footer-data":t.footerData,"fixed-footer":!0,"checkbox-option":t.checkboxOption,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"cell-autofill-option":t.cellAutofillOption,"clipboard-option":t.clipboardOption}})],1)],1)},D=[],T=(i("99af"),i("c740"),i("caad"),i("a9e3"),i("25f0"),i("2532"),i("4d90"),i("5319"),i("c7cd"),i("4e3e"),i("159b"),{rownum:0,phasename:"",taskname:"",description:"",planstart:"",planend:"",actualstart:"",completiondate:"",milestone:"",outputresult:"",mainresponsibleperson:"",subresponsibleperson:"",remark:""}),O={formcode:"S06M19B1Th",item:[{itemcode:"type",itemname:"类型",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Sa_Tracking.type"},{itemcode:"subject",itemname:"主题",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",datasheet:"Sa_Tracking.billtitle"},{itemcode:"customer",itemname:"客户名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Tracking.customer"},{itemcode:"project",itemname:"工程项目",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Tracking.project"},{itemcode:"operator",itemname:"经办人",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Tracking.operator"},{itemcode:"assistantname",itemname:"协助人员",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Tracking.assistantname"},{itemcode:"itemcount",itemname:"行数",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Tracking.itemcount"},{itemcode:"planfinishcount",itemname:"已排程数",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Tracking.planfinishcount"},{itemcode:"billfinishcount",itemname:"已完工数",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Tracking.billfinishcount"},{itemcode:"billplandate",itemname:"计划日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Sa_Tracking.billplandate"},{itemcode:"billfinishdate",itemname:"最大完工日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Sa_Tracking.billfinishdate"},{itemcode:"summary",itemname:"摘要",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Sa_Tracking.groupuid"},{itemcode:"lister",itemname:"制表人",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Sa_Tracking.lister"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"80",sortable:1,displaymark:1,overflow:1,datasheet:"Sa_Tracking.modifydate"}]},P={formcode:"S06M19B1List",item:[{itemcode:"phasename",itemname:"阶段名",minwidth:"100",defwidth:"",displaymark:1,overflow:1,fixed:0,sortable:0,aligntype:"center",datasheet:"Sa_TrackingItem.phasename"},{itemcode:"taskname",itemname:"任务名",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_TrackingItem.taskname"},{itemcode:"description",itemname:"描述",minwidth:"100",displaymark:1,overflow:1},{itemcode:"planstart",itemname:"计划开始时间",minwidth:"100",displaymark:1,overflow:1},{itemcode:"planend",itemname:"计划结束时间",minwidth:"100",displaymark:1,overflow:1},{itemcode:"milestone",itemname:"里程碑",minwidth:"80",displaymark:1,overflow:1},{itemcode:"actualstart",itemname:"实际开始实际",minwidth:"100",displaymark:1,overflow:1},{itemcode:"completiondate",itemname:"完工时间",minwidth:"100",displaymark:1,overflow:1},{itemcode:"outputresult",itemname:"产出成果",minwidth:"80",displaymark:1,overflow:1},{itemcode:"mainresponsibleperson",itemname:"主负责人",minwidth:"80",displaymark:1,overflow:1},{itemcode:"subresponsibleperson",itemname:"次负责人",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1}]},F={formcode:"S06M19B1Item",item:[{itemcode:"phasename",itemname:"阶段名",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center",editmark:1},{itemcode:"taskname",itemname:"任务名",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"description",itemname:"描述",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"planstart",itemname:"计划开始时间",minwidth:"100",displaymark:1,overflow:1},{itemcode:"planend",itemname:"计划结束时间",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"milestone",itemname:"里程碑",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"actualstart",itemname:"实际开始实际",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"completiondate",itemname:"完工时间",minwidth:"100",displaymark:1,overflow:1,editmark:1},{itemcode:"outputresult",itemname:"产出成果",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"mainresponsibleperson",itemname:"主负责人",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"subresponsibleperson",itemname:"次负责人",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},I={name:"EditItem",components:{},props:["formdata","lstitem","idx","formstate","formtemplateItem"],data:function(){var t=this;return{lst:[],keynum:0,tableHeight:0,nowitemtaxrate:0,multipleSelection:[],isEditOk:!0,tableForm:F,customList:[],editmarkfiles:[],countfiles:[],columsData:[],columnHidden:[],footerData:[],copyText:"",checkboxOption:{selectedRowKeys:[],selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.checkboxOption.selectedRowKeys=i,0!=i.length){t.multipleSelection=[];for(var a=0;a<i.length;a++){var o=t.lst.findIndex((function(t){return t.rowKeys==i[a]}));-1!=o&&t.multipleSelection.push(t.lst[o])}}else t.multipleSelection=[]},selectedAllChange:function(e){var i=e.isSelected,a=e.selectedRowKeys;i?(t.checkboxOption.selectedRowKeys=a,t.multipleSelection=JSON.parse(JSON.stringify(t.lst))):(t.multipleSelection=[],t.checkboxOption.selectedRowKeys=[])}},editOption:{cellValueChange:function(e){var i=e.row,a=e.column;t.editmarkfiles.includes(a.field)&&t.countfiles.includes(a.field)&&t.changeInput("",i,a.field)}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},cellAutofillOption:{directionX:!1,directionY:!0,beforeAutofill:function(e){var i=e.sourceSelectionData,a=Object.keys(i[0])[1];if(!t.editmarkfiles.includes(a))return!1},afterAutofill:function(e){e.sourceSelectionData;for(var i=e.targetSelectionData,a=0;a<i.length;a++){var o=i[a],n=t.lst.findIndex((function(t){return t.rowKeys==o.rowKeys}));-1!=n&&t.countfiles.includes(Object.keys(o)[1])&&t.changeInput("",t.lst[n],Object.keys(o)[1])}}},clipboardOption:{copy:!0,paste:!0,cut:!1,delete:!0,afterCopy:function(e){var i=e.data;t.copyText=i},afterPaste:function(e){var i=e.data,a=e.selectionRangeIndexes,o=(e.selectionRangeKeys,a.startRowIndex);t.pasteFun(i,o)}}}},computed:{tableMinWidth:function(){var t="100%";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lstitem:function(t,e){0!=this.lstitem.length&&(this.nowitemtaxrate=this.lstitem[0].itemtaxrate),this.lst=this.lstitem},formstate:function(t,e){2!=this.formstate?this.isEditOk=!0:this.isEditOk=!1,this.initTable(this.tableForm)},lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i,t[i].rowKeys||(t[i].rowKeys=(new Date).getTime()+"-"+i);this.getSummary()}},created:function(){this.lst=[]},mounted:function(){this.catchHight(),this.getColumn()},methods:{getColumn:function(){this.initTable(this.tableForm),this.editmarkfiles=[];for(var t=0;t<this.tableForm.item.length;t++){var e=this.tableForm.item[t];e.editmark&&this.editmarkfiles.push(e.itemcode)}this.$forceUpdate()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t.item.forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"asc",edit:!!e.isEditOk&&!!t.editmark,resize:!0,renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex,"");if("planstart"==t.itemcode||"planend"==t.itemcode||"actualstart"==t.itemcode||"completiondate"==t.itemcode){n=a("div",{style:"display: flex;justify-content: space-between;align-items: center;padding:0 4px"},[a("div",{style:"flex:1"},[a("span",{class:o.disannulmark?"textlinethrough":""},[e.monthFilter(o[t.itemcode])])]),a("div",[a("div",{style:"opacity: 0;width:0;height:0"},[a("el-date-picker",{style:"width:0;height:0",attrs:{size:"small",id:t.itemcode+o.rownum,value:e.getNewDate(o[t.itemcode]),type:"date",format:"MM-dd","value-format":"yyyy-MM-dd HH:mm:ss",placeholder:"选择日期"},on:{input:function(e){o[t.itemcode]=e}}})]),a("i",{directives:[{name:"show",value:!e.formdata.assessor}],class:"writePacksn el-icon-date",on:{click:function(){document.getElementById(t.itemcode+o.rownum).focus()}}})])]);return n}return n=a("span",{class:o.disannulmark?"textlinethrough":""},[o[t.itemcode]]),n}};a=e.customList.findIndex((function(e){return t.itemcode==e.attrkey}));-1!=a&&(o.edit=!!e.isEditOk),t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",operationColumn:!0,renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.columsData=i,this.keynum+=1},getSummary:function(){this.$getSummary(this,["phasename"])},pasteFun:function(t,e){var i=[];for(var a in t[0])i.push(a);for(var o=0;o<t.length;o++)for(var n=t[o],s=0;s<i.length;s++){var r=i[s];this.lst[e+o][r]=n[r].replace(/^\s*|\s*$/g,""),this.countfiles.includes(r)&&this.changeInput("",this.lst[e+o],r)}},catchHight:function(){var t=this;this.$nextTick((function(){t.$refs.elitem&&(t.tableHeight=t.$refs.elitem.getBoundingClientRect().height-32)}))},copyRow:function(){for(var t=0;t<this.multipleSelection.length;t++){var e=Object.assign({},this.multipleSelection[t]);this.$delete(e,"id"),this.$set(e,"finishqty",0),this.$set(e,"closed",0),this.$set(e,"disannulmark",0),this.$delete(e,"rowKeys"),this.lst.push(e)}this.multipleSelection=[],this.checkboxOption.selectedRowKeys=[]},getAdd:function(t){var e=Object.assign({},T);0!=this.idx&&(e.pid=this.idx),this.lst.push(e)},monthFilter:function(t){if(t){var e=new Date(t),i=(e.getMonth()+1).toString().padStart(2,"0"),a=e.getDate().toString().padStart(2,"0");return"".concat(i,"-").concat(a)}},getNewDate:function(t){if(t){var e=new Date(t);return e}}}},B=I,R=(i("e420"),Object(d["a"])(B,C,D,!1,null,"4e1d3ce6",null)),M=R.exports,V=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-form",{staticClass:"footFormContent",attrs:{"label-width":"100px"}},[i("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[i("el-col",{attrs:{span:20}},[i("el-form-item",{staticStyle:{"margin-bottom":"0px"},attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[i("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.summary,callback:function(e){t.$set(t.formdata,"summary",e)},expression:"formdata.summary"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建人"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[t._v(t._s(t._f("dateFormats")(t.formdata.createdate)))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"制表"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"修改日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[t._v(t._s(t._f("dateFormats")(t.formdata.modifydate)))])])],1)],1)],1)],1)},j=[],N={props:{formdata:{type:Object}},components:{selDictionaries:y["a"]},data:function(){return{seloperatorvisible:!1}}},z=N,L=Object(d["a"])(z,V,j,!1,null,"651f3eca",null),E=L.exports,q=i("dcb4"),H=["id","subject","type","customer","project","publicmark","operator","operatorid","assistantid","assistantname","custom1","custom2","custom3","custom4","custom5"],A=["id","pid","phasename","taskname","description","planstart","planend","milestone","actualstart","completiondate","outputresult","mainresponsibleperson","subresponsibleperson","rownum","remark","custom1","custom2","custom3","custom4","custom5"],K={params:H,paramsItem:A},U=[{show:1,divided:!1,label:"删 除",icon:"el-icon-delete",disabled:"this.formstate!=1",methods:"deleteForm",param:"",children:[]},{show:1,divided:!1,label:"引入历史单据",icon:"el-icon-plus",disabled:"this.formstate!=0",methods:"getHistory",param:"",children:[]},{show:1,divided:!0,label:"中止单据...",icon:"",methods:"",param:"",children:[{show:1,divided:!1,label:"作废",icon:"zuofei",methods:"discontinue",param:1,children:[]},{show:1,divided:!1,label:"恢复",icon:"huifu",methods:"discontinue",param:2,children:[]},{show:1,divided:!0,label:"中止",icon:"zhongzhi",methods:"discontinue",param:3,children:[]},{show:1,divided:!1,label:"开启",icon:"kaiqi",methods:"discontinue",param:4,children:[]}]}],J=[],W={header:{type:0,title:"跟踪单",content:[{rowitem:[{col:5,code:"billtype",label:"单据类型",type:"select",methods:"changeBillType",param:"",disabled:"!!this.formdata.id",options:[{label:"其他发货",value:"其他发货"},{label:"其他退货",value:"其他退货"}]},{col:5,code:"billtitle",label:"单据标题",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"groupname",label:"客户",searchtype:"customer",type:"autocomplete",methods:"",param:""},{col:5,code:"linkman",label:"联系人",type:"input",methods:"",param:""},{col:5,code:"telephone",label:"联系电话",type:"input",methods:"",param:""}]},{rowitem:[{col:5,code:"deliadd",label:"送货地址",type:"input",methods:"",param:""},{col:5,code:"transport",label:"运输方式",type:"input",methods:"",param:""},{col:5,code:"salesman",label:"业务员",billcode:"sale.salesman",type:"dictionary",methods:"",param:""}]}]},item:{type:0,content:[{itemcode:"goodsuid",itemname:"货品编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:0,overflow:1,aligntype:"center"},{itemcode:"goodsname",itemname:"货品名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"goodsspec",itemname:"规格",minwidth:"100",displaymark:1,overflow:1},{itemcode:"goodsunit",itemname:"单位",minwidth:"80",displaymark:1,overflow:1},{itemcode:"partid",itemname:"外部编码",minwidth:"80",displaymark:1,overflow:1},{itemcode:"quantity",itemname:"数量",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"price",itemname:"未税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"amount",itemname:"未税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"itemtaxrate",itemname:"税率",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxprice",itemname:"含税单价",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"taxamount",itemname:"含税金额",minwidth:"80",displaymark:1,overflow:1,editmark:1},{itemcode:"citeuid",itemname:"销售订单",minwidth:"100",displaymark:1,overflow:1},{itemcode:"finishqty",itemname:"已出入库",minwidth:"80",displaymark:1,overflow:1},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,editmark:1}]},footer:{type:0,content:[{rowitem:[{col:18,code:"summary",label:"摘要",type:"input",methods:"",param:""},{col:5,code:"operator",label:"经办人",type:"input",methods:"",param:""}]},{type:"foot",rowitem:[{col:4,code:"createby",label:"创建人",type:"text"},{col:4,code:"createdate",label:"创建日期",type:"text"},{col:4,code:"lister",label:"制表",type:"text"},{col:4,code:"modifydate",label:"修改日期",type:"text"},{col:4,code:"assessor",label:"审核",type:"text"},{col:4,code:"assessdate",label:"审核日期",type:"text"}]}]}},Y=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.search(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",[i("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selectVal",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"351px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?i("el-table-column",{attrs:{type:"selection",width:"40"}}):i("el-table-column",{attrs:{label:"",width:"40","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(i){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),i("el-table-column",{attrs:{label:"类型",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.type))])]}}])}),i("el-table-column",{attrs:{label:"客户名称",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.customer))])]}}])}),i("el-table-column",{attrs:{label:"工程项目",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.project))])]}}])}),i("el-table-column",{attrs:{label:"经办人",align:"center","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.operator))])]}}])}),i("el-table-column",{attrs:{label:"制表人",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(e.row.lister))])]}}])}),i("el-table-column",{attrs:{label:"修改日期",align:"center","min-width":"50","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[i("span",[t._v(t._s(t._f("dateFormats")(e.row.createdate)))])]}}])})],1)],1),i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},G=[],Q=i("c7eb"),X=i("1da1"),Z={props:["multi","groupid","selecturl"],data:function(){return{listLoading:!0,lst:[],strfilter:"",total:0,radio:"",selrows:"",queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1}}},created:function(){this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;return Object(X["a"])(Object(Q["a"])().mark((function e(){var i;return Object(Q["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.listLoading=!0,i=t.selecturl?t.selecturl:"/S06M19B1/getPageTh",e.next=4,p["a"].post(i,JSON.stringify(t.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}));case 4:case"end":return e.stop()}}),e)})))()},search:function(t){""!=t?this.queryParams.SearchPojo={type:t,customer:t,project:t,summary:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},tt=Z,et=(i("12ea"),Object(d["a"])(tt,Y,G,!1,null,"3540c287",null)),it=et.exports,at={name:"Formedit",components:{FormTemp:q["a"],EditHeader:_,EditFooter:E,EditItem:M,HistoryBill:it},props:["idx","isDialog","initData","billcode"],data:function(){return{title:"多任务跟踪表",operateBar:U,processBar:J,formdata:{type:"",subject:"",customer:"",project:"",operator:"",summary:"",assistantid:[],assistantname:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,item:[]},engineerData:[],operationVisible:!1,dialogIdx:0,processVisible:!1,processTitle:"",processModel:"",formtemplate:W,formstate:0,submitting:0,ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,printType:"print",historyvisible:!1}},computed:{formcontainHeight:function(){var t=window.innerHeight-113;return t<600&&(t=600),t+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindTemp()},mounted:function(){this.getEngineerData(),this.bindData(),this.isDialog&&this.billSwitch(this.billcode)},methods:{bindTemp:function(){this.formtemplate=W},bindData:function(){var t=this;this.formstate=0,0!=this.idx&&p["a"].get("/S06M19B1/getBillEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formdata.assistantid=t.formdata.assistantid?t.formdata.assistantid.split(","):[],t.formstate=t.formdata.assessor?2:1):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}})})).catch((function(e){t.$message.error("请求错误")}))},setOperatorRow:function(t){this.formdata.operatorid=t.id,this.formdata.operator=t.engineername},autoClear:function(){this.formdata.operatorid="",this.formdata.operator=""},getEngineerData:function(){var t=this,e={PageNum:1,PageSize:100,OrderType:1,SearchType:1};this.engineerData=[];var i="/S06M02S2/getPageList";this.$request.post(i,JSON.stringify(e)).then((function(e){200==e.data.code&&(t.engineerData=e.data.data.list)}))},submitForm:function(){var t,e=this;t=this.formtemplate.header.type?this.$refs.formtemp.$refs.formHeader:this.$refs.formHeader,t.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;if(0!=this.$refs.elitem.lst.length){this.formdata.item=this.$refs.elitem.lst;var e={item:[]};if(e=this.$getParam(K,e,this.formdata),this.formdata.assistantid.length){var i=this.formdata.assistantid.join(","),a=this.formdata.assistantid.map((function(e){var i=t.engineerData.find((function(t){return t.id===e}));return i?i.engineername:null})).filter(Boolean).join(",");e.assistantid=i,e.assistantname=a}else e.assistantid="",e.assistantname="";this.submitting=1,0==this.idx?g.add(e).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0})):g.update(e).then((function(e){t.$message.success(e.msg||"保存成功"),t.$emit("bindData"),t.formdata=e.data,t.submitting=0,t.formstate=t.formdata.assessor?2:1})).catch((function(e){t.$message.warning(e||"保存失败"),t.submitting=0}))}else this.$message.warning("单据内容不能为空")},closeForm:function(){this.isDialog?this.$emit("closeDialog"):this.$emit("closeForm")},deleteForm:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){g.delete(t)})).catch((function(){}))},approval:function(){g.approval(this)},discontinue:function(t){var e=this;if(0!=this.$refs.elitem.multipleSelection.length){var i=this.$refs.elitem.multipleSelection;2==t||4==t?g.reqitembill(t,i,this):this.$confirm("是否确认"+(1==t?"作废":"中止")+"勾选内容?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){g.reqitembill(t,i,e)}))}else this.$message.warning("请选择一个货品信息")},clickMethods:function(t){this[t.meth](t.param)},flowable:function(){this.$refs.flowable.action()},operateBill:function(t){this.operationVisible=!0,this.dialogIdx=0,this.processModel=t},processBill:function(t){this.processVisible=!0,this.processModel=t.code,this.processTitle=t.label},getHistory:function(){this.historyvisible=!0,this.dialogIdx=0},changeBillType:function(){formdata.item=[]},changeIdx:function(t){this.dialogIdx=t},billSwitch:function(t){},submitHistory:function(){for(var t=this.$refs.Historybill.selrows,e={type:"",item:[]},i=0;i<t.item.length;i++){var a=t.item[i],o=Object.assign({},T);o.phasename=a.phasename,o.taskname=a.taskname,o.description=a.description,e.item.push(o)}this.formdata=e,this.historyvisible=!1,console.log("formdata",this.formdata)},printButton:function(){var t=this;p["a"].get("/SaReports/getListByModuleCode?code=S06M19B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?p["a"].get("/S06M19B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var i=[];i.push(e.data);var a=window.URL.createObjectURL(new Blob(i,{type:"application/pdf"}));t.pdfUrl=a,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;if(""!=this.reportModel){var e="/S06M19B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel;"preview"==this.printType&&(e="/S06M19B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel+"&cmd=1"),p["a"].get(e).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")}))}else this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.formdata.printcount+=1,this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},fastKey:function(){var t=this;document.onkeydown=function(e){var i=e.keyCode;83==i&&e.ctrlKey?e.preventDefault():27==i&&(e.preventDefault(),t.closeForm())}}}},ot=at,nt=(i("7ea5"),Object(d["a"])(ot,f,h,!1,null,"688bfe73",null)),st=nt.exports,rt=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableTh",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize,pageSizes:[10,20,100,500]},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1)])],1)},lt=[],ct={components:{},props:["online","own"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:O,selectList:[],totalfields:[],exportitle:"跟踪单",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this,e="/S06M19B1/getPageTh";this.online&&(e="/S06M19B1/getOnlinePageTh"),this.own&&(e+="?own=1"),p["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){this.initTable(this.tableForm),this.$emit("sendTableForm",this.tableForm)},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex,"");return"billplandate"==t.itemcode||"modifydate"==t.itemcode||"billfinishdate"==t.itemcode?e.$options.filters.dateFormat(o[t.itemcode]):"type"==t.itemcode?(n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.id)}}},[o[t.itemcode]?o[t.itemcode]:"类型"]),n):o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:40,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),i.unshift({field:"",key:"checkbox",type:"checkbox",title:"",width:50,align:"center",fixed:"left"}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableTh.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableTh.getRangeCellSelection().selectionRangeIndexes,i=["planfinishcount","billfinishcount"];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){var e=t.strfilter;""!=e?this.queryParams.SearchPojo={type:e,customer:e,project:e}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)}}},dt=ct,mt=(i("85a2"),Object(d["a"])(dt,rt,lt,!1,null,"8b0672b6",null)),ut=mt.exports,ft=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.lst,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),i("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[i("div",{staticClass:"flex a-c"},[i("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}}),0!=t.cellNum?i("div",{staticStyle:{"margin-left":"10px","font-size":"14px"}},[i("span",[t._v("计数="+t._s(t.cellNum))]),i("span",{staticStyle:{"margin-left":"10px"}},[t._v("求和="+t._s(t.cellTotal))])]):t._e()],1),i("div",{staticStyle:{"margin-right":"40px"}})])],1)},ht=[],pt={components:{},props:["online"],data:function(){var t=this;return{lst:[],total:0,idx:0,keynum:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:P,customList:[],selectList:[],totalfields:[],exportitle:"跟踪单明细表",customData:[],columnHidden:[],footerData:[],cellTotal:0,cellNum:0,rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var i=e.startRowIndex;t.rowScroll=i}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var i=e.selectedRowKeys;if(t.selectList=[],0!=i.length)for(var a=0;a<i.length;a++)t.selectList.push({id:i[a]})},selectedAllChange:function(e){var i=e.isSelected;e.selectedRowKeys;t.selectList=i?t.lst:[]}},eventCustomOption:{bodyCellEvents:function(e){e.row,e.column,e.rowIndx;return{mouseup:function(e){t.countCellData()}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var i=this.tableForm.item[e];i.displaymark&&(t+=Number(i.minwidth))}}return t}},watch:{lst:function(t,e){0!=this.lst.length&&this.getSummary()}},methods:{bindData:function(){var t=this;if(this.online)var e="/S06M19B1/getOnlinePageList";else e="/S06M19B1/getPageList";p["a"].post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.lst=e.data.data.list,t.total=e.data.data.total):t.$message.warning(response.data.msg||"请求失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getColumn:function(){var t=this;return Object(X["a"])(Object(Q["a"])().mark((function e(){return Object(Q["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.initTable(t.tableForm),t.$emit("sendTableForm",t.tableForm);case 2:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,i=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,a){var o={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(i,a){var o=i.row,n=(i.column,i.rowIndex,"");return"billdate"==t.itemcode||"plandate"==t.itemcode?e.$options.filters.dateFormat(o[t.itemcode]):"phasename"==t.itemcode?(n=a("el-button",{attrs:{type:"text",size:"small"},style:"padding: 4px 15px",on:{click:function(){return e.showForm(o.pid)}}},[o[t.itemcode]?o[t.itemcode]:"阶段名"]),n):"planstart"==t.itemcode||"planend"==t.itemcode||"actualstart"==t.itemcode||"completiondate"==t.itemcode?e.$options.filters.dateFormat(o[t.itemcode]):o[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),i.push(o)})),i.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,i){t.row,t.column;var a=t.rowIndex;return a+e.rowScroll+1}}),this.customData=i,this.keynum+=1},countCellData:function(){var t=this.$refs.tableList.getRangeCellSelection().selectionRangeKeys,e=this.$refs.tableList.getRangeCellSelection().selectionRangeIndexes,i=[];this.$countCellData(this,i,t,e)},getSummary:function(){this.$getSummary(this,this.totalfields)},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){var e=t.strfilter;""!=e?this.queryParams.SearchPojo={phasename:e,taskname:e,milestone:e}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},advancedSearch:function(t){this.$advancedSearch(this,t,1)},changeSort:function(t){for(var e in t)if(""!=t[e]){var i={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(i,this.tableForm),this.bindData();break}},showForm:function(t){this.$emit("showForm",t)},btnExport:function(){this.$btnExport(this.lst,this.tableForm,this.exportitle)},btnPrint:function(){this.$refs.PrintServer.printButton(1)}}},bt=pt,gt=(i("b27f"),Object(d["a"])(bt,ft,ht,!1,null,"bf528186",null)),vt=gt.exports,wt={name:"S06M19B1",components:{ListHeader:u,FormEdit:st,TableTh:ut,TableList:vt},data:function(){return{formvisible:!1,idx:0,online:1,tableForm:{},thorList:!0,own:!0}},watch:{},mounted:function(){this.bindData(),this.thorList?this.$refs.tableTh.getColumn():this.$refs.tableList.getColumn()},methods:{bindData:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.bindData()})):this.$nextTick((function(){t.$refs.tableList.bindData()}))},sendTableForm:function(t){this.tableForm=t},changeModelUrl:function(t){this.thorList=!!t,this.bindData()},btnExport:function(){var t=this;this.thorList?this.$nextTick((function(){t.$refs.tableTh.btnExport()})):this.$nextTick((function(){t.$refs.tableList.btnExport()}))},changeOwn:function(t){this.own=t,this.bindData()},search:function(t){this.thorList?this.$refs.tableTh.search(t):this.$refs.tableList.search(t)},advancedSearch:function(t){this.thorList?this.$refs.tableTh.advancedSearch(t):this.$refs.tableList.advancedSearch(t)},showForm:function(t){this.idx=t,this.formvisible=!0},closeForm:function(){this.formvisible=!1},compForm:function(){this.bindData(),this.formvisible=!1},changeIdx:function(t){this.idx=t},changeBalance:function(t){this.online=t,this.bindData()}}},yt=wt,xt=(i("4dfb"),Object(d["a"])(yt,a,o,!1,null,"4e6f5540",null));e["default"]=xt.exports},"4dfb":function(t,e,i){"use strict";i("311f")},"5c4b":function(t,e,i){},"5c73":function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{},[i("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=t.lst.length?i("ul",{},t._l(t.lst,(function(e,a){return i("li",{key:a,class:t.radio==a?"active":"",on:{click:function(i){t.getCurrentRow(e),t.radio=a}}},[i("span",[t._v(t._s(e.dictvalue))])])})),0):i("div",{staticClass:"noData"},[t._v("暂无数据")])]),i("div",{staticClass:"foots"},[i("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(e){t.SYSM07B1Visible=!0,t.lst=t.formdata.item}}},[t._v("编辑")]),i("span",{on:{click:function(e){return t.$emit("closedic")}}},[t._v("关闭")])])]),t.SYSM07B1Visible?i("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:t.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.SYSM07B1Visible=e}}},[i("div",{staticClass:"dialog-body customDialog"},[i("div",{staticClass:"left"},t._l(t.lst,(function(e,a){return i("p",{key:a,class:t.ActiveIndex==a?"isActive":"",on:{click:function(e){t.ActiveIndex=a}}},[t._v(" "+t._s(e.dictvalue)+" ")])})),0),i("div",{staticClass:"right"},[i("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(e){return t.addInput()}}},[t._v("添 加")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.editInput()}}},[t._v("编 辑")]),i("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.delItem()}}},[t._v("删 除")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveUp()}}},[t._v("上 移")]),i("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==t.ActiveIndex},on:{click:function(e){return t.getMoveDown()}}},[t._v("下 移")])],1)]),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitUpdate}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.SYSM07B1Visible=!1,t.bindData()}}},[t._v("取 消")])],1)]):t._e()],1)},o=[],n=(i("a434"),i("e9c4"),i("b775")),s=i("333d"),r=i("b0b8"),l={components:{Pagination:s["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{dictcode:"",item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(t,e){void 0==t&&(this.lst=[]);for(var i=0;i<t.length;i++)t[i].rownum=i}},created:function(){},methods:{getCurrentRow:function(t){this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},bindData:function(){var t=this;this.lst=[],n["a"].get("/SaDict/getBillEntityByDictCode?key="+this.billcode).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata={item:[]},void(t.listLoading=!1);if(t.formdata=e.data.data,0==t.formdata.item.length)return void(t.lst=[]);for(var i=0;i<t.formdata.item.length;i++)t.lst.push(t.formdata.item[i])}})).catch((function(e){t.$message.error("请求出错")}))},rowIndex:function(t){var e=t.row,i=t.rowIndex;e.row_index=i},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)},addInput:function(){var t=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.addItem(i)})).catch((function(t){console.log(t)}))},addItem:function(t){r.setOptions({checkPolyphone:!1,charCase:1});var e={cssclass:"",defaultmark:0,dictcode:r.getFullChars(t),dictvalue:t,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(e)},editInput:function(){if(-1!=this.ActiveIndex){var t=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:t.lst[t.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(t){if(!t)return"输入不能为空"}}).then((function(e){var i=e.value;t.lst[t.ActiveIndex].dictvalue=i,r.setOptions({checkPolyphone:!1,charCase:1}),t.lst[t.ActiveIndex].dictcode=r.getFullChars(i)})).catch((function(t){console.log(t)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.lst.splice(t.ActiveIndex,1),t.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e-1,0,t),this.ActiveIndex-=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var t=this.lst[this.ActiveIndex],e=this.lst[this.ActiveIndex].rownum;this.lst.splice(e,1),this.lst.splice(e+1,0,t),this.ActiveIndex+=1;for(var i=0;i<this.lst.length;i++)this.lst[i].rownum=i}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var t=this;this.formdata.item=this.lst,n["a"].post("/SaDict/update",JSON.stringify(this.formdata)).then((function(e){200==e.data.code?(t.$message.success(e.data.msg||"保存成功"),t.SYSM07B1Visible=!1):t.$message.warning(e.data.msg||"保存失败")})).catch((function(e){t.$message.error("请求错误")}))}}},c=l,d=(i("af2b"),i("2877")),m=Object(d["a"])(c,a,o,!1,null,"d2ba3d7a",null);e["a"]=m.exports},"7de4":function(t,e,i){},"7ea5":function(t,e,i){"use strict";i("0b24")},"841c":function(t,e,i){"use strict";var a=i("d784"),o=i("825a"),n=i("1d80"),s=i("129f"),r=i("14c3");a("search",1,(function(t,e,i){return[function(e){var i=n(this),a=void 0==e?void 0:e[t];return void 0!==a?a.call(e,i):new RegExp(e)[t](String(i))},function(t){var a=i(e,t,this);if(a.done)return a.value;var n=o(t),l=String(this),c=n.lastIndex;s(c,0)||(n.lastIndex=0);var d=r(n,l);return s(n.lastIndex,c)||(n.lastIndex=c),null===d?-1:d.index}]}))},"85a2":function(t,e,i){"use strict";i("5c4b")},"9b97":function(t,e,i){},af2b:function(t,e,i){"use strict";i("7de4")},b27f:function(t,e,i){"use strict";i("9b97")},c8eb:function(t,e,i){},e420:function(t,e,i){"use strict";i("c8eb")},fb95:function(t,e,i){}}]);