(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-73b4d322"],{"21cc":function(t,a,i){},"22d5":function(t,a,i){"use strict";i("3219")},3219:function(t,a,i){},"327d":function(t,a,i){"use strict";i("d973")},"65ca":function(t,a,i){"use strict";i("21cc")},"9ad4":function(t,a,i){"use strict";i.r(a);var s=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticStyle:{"background-color":"#f5f6f7","min-height":"100vh","padding-bottom":"0px"}},[i("templet01",{staticClass:"main"})],1)},e=[],n=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"page"},[t.mainData.header?i("div",{staticClass:"banner"},[i("div",[i("img",{staticClass:"banner-bg",attrs:{src:t.windowWidth>768?t.mainData.header.bg:t.mainData.header.h5bg,alt:""}}),i("div",{staticClass:"banner-box"},[i("div",{staticClass:"banner-wrap"},[i("h1",{staticClass:"banner-title"},[t._v(t._s(t.mainData.header.title))]),i("h2",{staticClass:"banner-info"},[i("span",{staticClass:"banner-info-txt"},[t._v(t._s(t.mainData.header.subtitle))])])])])])]):t._e(),t.mainData.cost?i("div",{staticClass:"container-fluid",style:"background-color:"+t.mainData.cost.bgColor},[i("div",{staticClass:"container section-item section-item-cost"},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.cost.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body  row"},t._l(t.mainData.cost.datas,(function(a){return i("div",{staticClass:"body-cost  col-xl-3  col-sm-6 mt-5"},[i("div",{staticClass:"cost-icon"},[i("img",{attrs:{src:a.icon,alt:""}})]),i("div",{staticClass:"cost-info"},[i("div",{staticClass:"info-title"},[t._v(t._s(a.title))]),i("div",{staticClass:"info-text"},[t._v(" "+t._s(a.content)+" ")])])])})),0)])]):t._e(),t.mainData.flow?i("div",{staticClass:"container-fluid",style:"background-color:"+t.mainData.flow.bgColor},[i("div",{staticClass:"container section-item section-item-flow"},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.flow.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body mt-5"},[i("img",{staticClass:"image",attrs:{src:t.mainData.flow.imgUrl,alt:""}})])])]):t._e(),t.mainData.frame?i("div",{staticClass:"container-fluid",style:"background-color:"+t.mainData.frame.bgColor},[i("div",{staticClass:"container section-item section-item-flow"},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.frame.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body mt-5"},[i("img",{staticClass:"image",attrs:{src:t.mainData.frame.imgUrl,alt:""}})])])]):t._e(),t.mainData.fun?i("div",{staticClass:"container-fluid",style:"background-color:"+t.mainData.fun.bgColor},[i("div",{staticClass:"container section-item section-item-fun"},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.fun.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body page-base-cotent"},t._l(t.mainData.fun.datas,(function(a){return i("div",{staticClass:"item col-xl-1-5  col-lg-3 col-md-4 col-sm-12 col-12 mt-5"},[i("div",{staticClass:"body-fun"},[i("div",{staticClass:"fun-icon"},[i("img",{staticClass:"icon",attrs:{src:a.icon,alt:""}})]),i("div",{staticClass:"fun-info"},[i("h2",{staticClass:"info-title"},[t._v(t._s(a.title))]),i("p",{staticClass:"info-text"},[t._v(" "+t._s(a.content)+" ")])])])])})),0)])]):t._e(),t.mainData.des&&t.mainData.des.datas?t._l(t.mainData.des.datas,(function(a,s){return i("div",{staticClass:"container-fluid",style:"background-color:"+a.bgColor},[i("div",{staticClass:" container section-item section-item-left-right"},[0==s&&t.mainData.des.title?i("div",{staticClass:"item-title"},[t._v(" "+t._s(t.mainData.des.title)+" ")]):t._e(),0==s&&t.mainData.des.title?i("div",{staticClass:"item-title-line"}):t._e(),"left"==a.layout?i("div",{staticClass:"page-base-cotent row"},[i("div",{staticClass:"connect-info  col-md-4  col-12"},[i("h3",{staticClass:"info-title"},[t._v(t._s(a.title))]),i("div",{staticClass:"info-text-list"},t._l(a.contents,(function(a){return i("div",{staticClass:"info-text-item"},[t._v(" "+t._s(a)+" ")])})),0),i("div",{staticClass:"info-label-text"},[t._v(t._s(a.subject))]),i("div",{staticClass:"info-btn"},[i("a",{attrs:{href:a.btnUrl,target:"_blank"}},[t._v(" "+t._s(a.btnText)+" ")])])]),i("div",{staticClass:"connect-img  col-md-8  col-12"},[i("img",{attrs:{src:a.imgUrl,alt:""}})])]):t._e(),"right"==a.layout?i("div",{staticClass:"page-base-cotent row"},[i("div",{staticClass:"connect-img col-md-8  col-12"},[i("img",{attrs:{src:a.imgUrl,alt:""}})]),i("div",{staticClass:"connect-info col-md-4  col-12"},[i("h3",{staticClass:"info-title"},[t._v(t._s(a.title))]),i("div",{staticClass:"info-text-list"},t._l(a.contents,(function(a){return i("div",{staticClass:"info-text-item"},[t._v(" "+t._s(a)+" ")])})),0),i("div",{staticClass:"info-label-text"},[t._v(t._s(a.subject))]),i("div",{staticClass:"info-btn"},[i("a",{attrs:{href:a.btnUrl,target:"_blank"}},[t._v(" "+t._s(a.btnText)+" ")])])])]):t._e()])])})):t._e(),t.mainData.ver?i("div",{staticClass:"container-fluid",style:"background-color:"+t.mainData.ver.bgColor},[i("div",{staticClass:"container section-item section-item-version"},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.ver.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body  row"},t._l(t.mainData.ver.datas,(function(a){return i("div",{staticClass:"item col-xl-1-5 col-md-4 col-sm-6 col-12 mt-5"},[i("div",{staticClass:"version-item"},[i("h3",{staticClass:"version-item-title"},[t._v(t._s(a.title))]),i("div",{staticClass:"version-item-info"},[a.price?i("p",{staticClass:"info-price",style:"color:"+a.price.color},[t._v(" "+t._s(a.price.unit)),i("span",{staticClass:"version-item-price"},[t._v(t._s(a.price.value))]),t._v(" "+t._s(a.price.cycle)+" ")]):t._e(),a.btn?i("div",{staticClass:"info-btn"},[i("a",{attrs:{href:a.btn.url,target:"_blank"}},[t._v(" "+t._s(a.btn.text)+" ")])]):t._e(),i("p",{staticClass:"info-text"},[t._v(t._s(a.text1.text)),i("span",{staticClass:"info-bold"},[t._v(t._s(a.text1.boldText))])]),i("p",{staticClass:"info-text"},[t._v(t._s(a.text2))])])])])})),0)])]):t._e(),t.mainData.footer?i("div",{staticClass:"container-fluid",style:"background-color:"+t.mainData.footer.bgColor},[i("div",{staticClass:"footer"},[i("div",{staticClass:"container footer-wrap"},[i("div",{staticClass:"footer-h2"},[t._v(t._s(t.mainData.footer.title))]),i("div",{staticClass:"footer-box"},[i("div",{staticClass:"box-left"},[i("div",{staticClass:"box-content"},[i("dt",[t._v(t._s(t.mainData.footer.contact.title))]),t._l(t.mainData.footer.contact.datas,(function(a){return i("dd",{staticClass:"c"},[t._v(" "+t._s(a)+" ")])}))],2)]),i("div",{staticClass:"box-right"},[i("div",{staticClass:"footer-img-o"},[i("div",{staticClass:"footer-img"},[i("img",{attrs:{src:t.mainData.footer.qrcode.url,alt:""}}),i("span",{staticStyle:{"margin-top":"10px"}},[t._v(t._s(t.mainData.footer.qrcode.title))])])])])])]),i("div",{staticClass:"footer-beian footer-beian-pc",style:"background-color:"+t.mainData.footer.beian.bgColor},[i("span",{staticClass:"beian-one"},[t._v(t._s(t.mainData.footer.beian.text1))]),i("span",{staticClass:"beian-one"},[i("a",{attrs:{href:t.mainData.footer.beian.pso.url,target:"_blank"}},[i("img",{staticClass:"footer-beian-img",attrs:{src:t.mainData.footer.beian.pso.icon,alt:""}}),t._v(t._s(t.mainData.footer.beian.pso.num))])]),i("span",{staticClass:"beian-one"},[i("a",{attrs:{href:t.mainData.footer.beian.icp.url,target:"_blank"}},[t._v(t._s(t.mainData.footer.beian.icp.num))])]),t._v(" "+t._s(t.mainData.footer.beian.text2)+" ")])])]):t._e()],2)},o=[],c=(i("b64b"),i("45a3")),l=(i("f9e3"),i("2dd8"),i("a18c"),{components:{vueJsonEditor:c["a"]},props:{moreurl:{type:String,default:""},lst:{type:Array}},data:function(){return{windowWidth:0,mainData:{}}},mounted:function(){var t=this;window.onresize=function(){var a=document.documentElement.clientWidth;(a>768&&t.windowWidth<=768||a<=768&&t.windowWidth>768)&&(console.log("宽度=",a),t.checkPic(a))},this.windowWidth=document.documentElement.clientWidth,console.log("宽度=",this.windowWidth)},created:function(){this.bindData()},methods:{routerTo:function(t){console.log(t),t.url&&this.$route.push(t.url)},checkPic:function(t){var a=this;this.$nextTick((function(){a.windowWidth=t}))},clickTest:function(){this.windowWidth>1e3?this.windowWidth=500:this.windowWidth=1200},bindData:function(){var t=this;this.formKey=this.$route.query.key||this.$route.params.key,console.log("formkey",this.formKey),this.$request.get(this.$store.getters.BASE_API+"/S06M22B1/getEntityBySolutionCode?code="+this.formKey).then((function(a){console.log("res",a),200==a.data.code&&a.data.data?(t.mainData=JSON.parse(a.data.data.descjson),console.log("this.mainData",t.mainData)):t.$alert(a.data.msg||"获取产品信息失败",{type:"warning",callback:function(a){t.$router.go(-1)}})})).catch((function(a){t.$alert(res.data.msg||"获取产品信息失败",{type:"warning",callback:function(a){t.$router.go(-1)}})}))}}}),r=l,d=(i("22d5"),i("327d"),i("2877")),m=Object(d["a"])(r,n,o,!1,null,"3ecacfc2",null),f=m.exports,v={name:"",components:{templet01:f},data:function(){return{}},mounted:function(){},methods:{bindData:function(){}}},u=v,C=(i("65ca"),Object(d["a"])(u,s,e,!1,null,"ca2b744c",null));a["default"]=C.exports},d973:function(t,a,i){}}]);