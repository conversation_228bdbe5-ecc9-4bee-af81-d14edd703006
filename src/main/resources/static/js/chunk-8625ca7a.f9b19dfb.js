(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8625ca7a"],{2519:function(t,a,i){},"5f85":function(t,a,i){"use strict";i("e9a1")},"7e97":function(t,a,i){"use strict";i.r(a);var s=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticStyle:{"background-color":"#f5f6f7","min-height":"100vh","padding-bottom":"0px"}},[i("templet01",{staticClass:"main"})],1)},e=[],n=function(){var t=this,a=t.$createElement,i=t._self._c||a;return i("div",{staticClass:"page"},[t.mainData.header?i("div",{staticClass:"banner"},[i("div",[i("img",{staticClass:"banner-bg",attrs:{src:t.mainData.header.bg,alt:""}}),i("div",{staticClass:"banner-box"},[i("div",{staticClass:"banner-wrap"},[i("h1",{staticClass:"banner-title"},[t._v(t._s(t.mainData.header.title))]),i("h2",{staticClass:"banner-info"},[i("span",{staticClass:"banner-info-txt"},[t._v(t._s(t.mainData.header.subtitle))])])])])])]):t._e(),t.mainData.cost?i("div",{staticClass:"section-item section-item-cost",style:"background-color:"+t.mainData.cost.bgColor},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.cost.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body page-base-cotent"},t._l(t.mainData.cost.datas,(function(a){return i("div",{staticClass:"body-cost"},[i("div",{staticClass:"cost-icon"},[i("img",{attrs:{src:a.icon,alt:""}})]),i("div",{staticClass:"cost-info"},[i("div",{staticClass:"info-title"},[t._v(t._s(a.title))]),i("div",{staticClass:"info-text"},[t._v(" "+t._s(a.content)+" ")])])])})),0)]):t._e(),t.mainData.flow?i("div",{staticClass:"section-item section-item-flow",style:"background-color:"+t.mainData.flow.bgColor},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.flow.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body page-base-cotent"},[i("img",{staticClass:"image",attrs:{src:t.mainData.flow.imgUrl,alt:""}})])]):t._e(),t.mainData.frame?i("div",{staticClass:"section-item section-item-flow",style:"background-color:"+t.mainData.frame.bgColor},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.frame.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body page-base-cotent"},[i("img",{staticClass:"image",attrs:{src:t.mainData.frame.imgUrl,alt:""}})])]):t._e(),t.mainData.fun?i("div",{staticClass:"section-item section-item-fun",style:"background-color:"+t.mainData.fun.bgColor},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.fun.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body page-base-cotent"},t._l(t.mainData.fun.datas,(function(a){return i("div",{staticClass:"body-fun"},[i("div",{staticClass:"fun-icon"},[i("img",{staticClass:"icon",attrs:{src:a.icon,alt:""}})]),i("div",{staticClass:"fun-info"},[i("h2",{staticClass:"info-title"},[t._v(t._s(a.title))]),i("p",{staticClass:"info-text",staticStyle:{padding:"0px 16px"}},[t._v(" "+t._s(a.content)+" ")])])])})),0)]):t._e(),t.mainData.des&&t.mainData.des.datas?t._l(t.mainData.des.datas,(function(a,s){return i("div",{staticClass:"section-item section-item-left-right",style:"background-color:"+a.bgColor},[0==s&&t.mainData.des.title?i("div",{staticClass:"item-title"},[t._v(" "+t._s(t.mainData.des.title)+" ")]):t._e(),0==s&&t.mainData.des.title?i("div",{staticClass:"item-title-line"}):t._e(),"left"==a.layout?i("div",{staticClass:"page-base-cotent"},[i("div",{staticClass:"connect-info"},[i("h3",{staticClass:"info-title"},[t._v(t._s(a.title))]),i("div",{staticClass:"info-text-list"},t._l(a.contents,(function(a){return i("div",{staticClass:"info-text-item"},[t._v(" "+t._s(a)+" ")])})),0),i("div",{staticClass:"info-label-text"},[t._v(t._s(a.subject))]),i("div",{staticClass:"info-btn"},[i("a",{attrs:{href:a.btnUrl,target:"_blank"}},[t._v(" "+t._s(a.btnText)+" ")])])]),i("div",{staticClass:"connect-img"},[i("img",{attrs:{src:a.imgUrl,alt:""}})])]):t._e(),"right"==a.layout?i("div",{staticClass:"page-base-cotent"},[i("div",{staticClass:"connect-img"},[i("img",{attrs:{src:a.imgUrl,alt:""}})]),i("div",{staticClass:"connect-info"},[i("h3",{staticClass:"info-title"},[t._v(t._s(a.title))]),i("div",{staticClass:"info-text-list"},t._l(a.contents,(function(a){return i("div",{staticClass:"info-text-item"},[t._v(" "+t._s(a)+" ")])})),0),i("div",{staticClass:"info-label-text"},[t._v(t._s(a.subject))]),i("div",{staticClass:"info-btn"},[i("a",{attrs:{href:a.btnUrl,target:"_blank"}},[t._v(" "+t._s(a.btnText)+" ")])])])]):t._e()])})):t._e(),t.mainData.ver?i("div",{staticClass:"section-item section-item-version",style:"background-color:"+t.mainData.ver.bgColor},[i("div",{staticClass:"item-title"},[t._v(t._s(t.mainData.ver.title))]),i("div",{staticClass:"item-title-line"}),i("div",{staticClass:"item-body page-base-cotent"},t._l(t.mainData.ver.datas,(function(a){return i("div",{staticClass:"version-item"},[i("h3",{staticClass:"version-item-title"},[t._v(t._s(a.title))]),i("div",{staticClass:"version-item-info"},[a.price?i("p",{staticClass:"info-price",style:"color:"+a.price.color},[t._v(" "+t._s(a.price.unit)),i("span",{staticClass:"version-item-price"},[t._v(t._s(a.price.value))]),t._v(" "+t._s(a.price.cycle)+" ")]):t._e(),a.btn?i("div",{staticClass:"info-btn"},[i("a",{attrs:{href:a.btn.url,target:"_blank"}},[t._v(" "+t._s(a.btn.text)+" ")])]):t._e(),i("p",{staticClass:"info-text"},[t._v(t._s(a.text1.text)),i("span",{staticClass:"info-bold"},[t._v(t._s(a.text1.boldText))])]),i("p",{staticClass:"info-text"},[t._v(t._s(a.text2))])])])})),0)]):t._e(),t.mainData.footer?i("div",{staticClass:"footer",style:"background-color:"+t.mainData.footer.bgColor},[i("div",{staticClass:"footer-wrap"},[i("div",{staticClass:"footer-h2"},[t._v(t._s(t.mainData.footer.title))]),i("div",{staticClass:"footer-box"},[i("div",{staticClass:"box-left"},[i("div",{staticClass:"box-content"},[i("dt",[t._v(t._s(t.mainData.footer.contact.title))]),t._l(t.mainData.footer.contact.datas,(function(a){return i("dd",{staticClass:"c"},[t._v(" "+t._s(a)+" ")])}))],2)]),i("div",{staticClass:"box-right"},[i("div",{staticClass:"footer-img-o"},[i("div",{staticClass:"footer-img"},[i("img",{attrs:{src:t.mainData.footer.qrcode.url,alt:""}}),i("span",{staticStyle:{"margin-top":"10px"}},[t._v(t._s(t.mainData.footer.qrcode.title))])])])])])]),i("div",{staticClass:"footer-beian footer-beian-pc",style:"background-color:"+t.mainData.footer.beian.bgColor},[t._v(" "+t._s(t.mainData.footer.beian.text1)+" "),i("span",{staticClass:"beian-one"},[i("a",{attrs:{href:t.mainData.footer.beian.pso.url,target:"_blank"}},[i("img",{staticClass:"footer-beian-img",attrs:{src:t.mainData.footer.beian.pso.icon,alt:""}}),t._v(t._s(t.mainData.footer.beian.pso.num))])]),i("span",{staticClass:"beian-one"},[i("a",{attrs:{href:t.mainData.footer.beian.icp.url,target:"_blank"}},[t._v(t._s(t.mainData.footer.beian.icp.num))])]),t._v(" "+t._s(t.mainData.footer.beian.text2)+" ")])]):t._e()],2)},o=[],l=(i("b64b"),i("45a3")),c={components:{vueJsonEditor:l["a"]},props:{moreurl:{type:String,default:""},lst:{type:Array}},data:function(){return{mainData:{}}},mounted:function(){this.bindData()},onCreate:function(){},methods:{routerTo:function(t){console.log(t),t.url&&this.$route.push(t.url)},bindData:function(){var t=this;this.formKey=this.$route.query.key||this.$route.params.key,console.log("formkey",this.formKey),this.$request.get("/S06M22B1/getEntityBySolutionCode?code="+this.formKey).then((function(a){console.log("res",a),200==a.data.code&&a.data.data?(t.mainData=JSON.parse(a.data.data.descjson),console.log("this.mainData",t.mainData)):t.$alert(a.data.msg||"获取产品信息失败",{type:"warning",callback:function(a){t.$router.go(-1)}})})).catch((function(a){t.$alert(res.data.msg||"获取产品信息失败",{type:"warning",callback:function(a){t.$router.go(-1)}})}))}}},r=c,d=(i("b82d"),i("2877")),m=Object(d["a"])(r,n,o,!1,null,"1329016b",null),v=m.exports,f={name:"",components:{templet01:v},data:function(){return{}},mounted:function(){},methods:{bindData:function(){}}},_=f,b=(i("5f85"),Object(d["a"])(_,s,e,!1,null,"6d4314e0",null));a["default"]=b.exports},b82d:function(t,a,i){"use strict";i("2519")},e9a1:function(t,a,i){}}]);