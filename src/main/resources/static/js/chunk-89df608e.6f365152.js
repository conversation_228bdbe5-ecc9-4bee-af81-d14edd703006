(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-89df608e"],{"00b0":function(t,e,a){"use strict";a("b6f9")},"278b":function(t,e,a){},"2e673":function(t,e,a){"use strict";a("d3ee")},9135:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm},on:{btnadd:function(e){return t.showform(0)},btnsearch:t.search,bindData:t.bindData,AdvancedSearch:t.AdvancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["statusname"==e.itemcode?a("span",{class:"status-color label-"+i.row.statuscolor,on:{click:function(e){return t.showform(i.row.id)}}},[t._v(" "+t._s(i.row.statusname?i.row.statusname:"名称")+" ")]):"createdate"==e.itemcode||"modifydate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormats")(i.row[e.itemcode])))]):"enabledmark"==e.itemcode?a("div",[i.row.enabledmark?a("el-tag",{attrs:{size:"small"}},[t._v("启用")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[t._v("停 用")])],1):"statusattr"==e.itemcode?a("div",[0==i.row[e.itemcode]?a("span",[t._v("待办")]):1==i.row[e.itemcode]?a("span",[t._v("进行中")]):2==i.row[e.itemcode]?a("span",[t._v("已完成")]):-1==i.row[e.itemcode]?a("span",[t._v("已拒绝")]):t._e()]):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]})),2==t.$store.state.user.userinfo.isadmin||null!=t.$store.state.user.userinfo.engineer&&"管理者"==t.$store.state.user.userinfo.engineer.engineertype?a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return t.handlePiPerMission(e.row.id,e.row)}}},[t._v("删除")])]}}],null,!1,828007154)}):t._e()],2),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)],1)],1)],1),t.FormVisible?a("el-dialog",{attrs:{width:"600px",title:"状态设置","append-to-body":!0,visible:t.FormVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.FormVisible=e},close:t.bindData}},[a("formedit",{ref:"formedit",attrs:{idx:t.idx},on:{bindData:t.bindData,changeidx:t.changeidx,closeDialog:function(e){t.FormVisible=!1}}})],1):t._e()],1)},n=[],r=(a("e9c4"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),2==t.$store.state.user.userinfo.isadmin||null!=t.$store.state.user.userinfo.engineer&&"管理者"==t.$store.state.user.userinfo.engineer.engineertype?a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")]):t._e()],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}})],1)])])}),s=[],o={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M02S2List"}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},l=o,c=(a("e20a"),a("2877")),d=Object(c["a"])(l,r,s,!1,null,"480fd06f",null),u=d.exports,m=a("b775"),f=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("statusname")}}},[a("el-form-item",{attrs:{label:"名称",prop:"statusname"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},model:{value:t.formdata.statusname,callback:function(e){t.$set(t.formdata,"statusname",e)},expression:"formdata.statusname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("statuscolor")}}},[a("el-form-item",{attrs:{label:"颜色",prop:"statuscolor"}},[a("div",{staticClass:"flex",staticStyle:{padding:"10px"}},t._l(t.discColor,(function(e,i){return a("div",{key:i,class:"disc-color disc-"+e,on:{click:function(a){t.formdata.statuscolor=e}}},[t.formdata.statuscolor==e?a("i",{staticClass:"el-icon-check"}):t._e()])})),0)])],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(e){return t.cleValidate("statusattr")}}},[a("el-form-item",{attrs:{label:"属性",prop:"statusattr"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择属性"},model:{value:t.formdata.statusattr,callback:function(e){t.$set(t.formdata,"statusattr",e)},expression:"formdata.statusattr"}},[a("el-option",{attrs:{label:"已拒绝",value:-1}}),a("el-option",{attrs:{label:"待办",value:0}}),a("el-option",{attrs:{label:"进行中",value:1}}),a("el-option",{attrs:{label:"已完成",value:2}})],1)],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent"},[a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",clearable:""},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1)],1),a("el-row",{directives:[{name:"show",rawName:"v-show",value:2==t.$store.state.user.userinfo.isadmin||null!=t.$store.state.user.userinfo.engineer&&"管理者"==t.$store.state.user.userinfo.engineer.engineertype,expression:"\n          $store.state.user.userinfo.isadmin == 2 ||\n          ($store.state.user.userinfo.engineer == null\n            ? false\n            : $store.state.user.userinfo.engineer.engineertype == '管理者')\n        "}]},[a("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px"},attrs:{span:24}},[a("div",[t.formdata.id?a("el-button",{attrs:{type:"danger"},on:{click:function(e){return t.deleteBtn()}}},[t._v("删除")]):t._e()],1),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitForm("formdata")}}},[t._v("保存")]),a("el-button",{on:{click:t.closeDialog}},[t._v("关闭")])],1)])],1)],1)])])},h=[];a("b64b");const p={add(t){return new Promise((e,a)=>{var i=JSON.stringify(t);m["a"].post("/S06M02S3/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);m["a"].post("/S06M02S3/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{m["a"].get("/S06M02S3/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var g=p,b=a("5b24"),w={name:"addDialog",props:["idx"],components:{autoComplete:b["a"]},data:function(){return{title:"状态设置",currentId:this.idx,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,statusname:"",statusattr:0,rownum:0,statuscolor:"blue",remark:""},formRules:{statusname:[{required:!0,trigger:"blur",message:"名称不能为空"}]},discColor:["blue","green","zi","orange","red","grey"]}},watch:{idx:function(t,e){this.currentId=t,this.binddata()}},created:function(){this.binddata()},methods:{binddata:function(){var t=this;0!=this.currentId&&m["a"].get("/S06M02S3/getEntity?key=".concat(this.currentId)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeDialog()}})})).catch((function(e){t.$message.error(e||"请求错误")}))},submitForm:function(t){var e=this;this.$refs["formdata"].validate((function(t){if(!t)return!1;e.saveForm()}))},saveForm:function(){var t=this;if(0==this.idx){var e=Object.assign({},this.formdata);g.add(e).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.closeDialog())})).catch((function(e){t.$message.warning(e||"保存失败")}))}else g.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeIdx",e.data.id),t.$emit("bindData"))})).catch((function(e){t.$message.warning(e||"保存失败")}))},deleteBtn:function(){var t=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){g.delete(t.formdata.id).then((function(e){200==e.code&&t.$message.success("删除成功"),t.$emit("bindData"),t.closeDialog()})).catch((function(){t.$message.warning("删除失败")}))})).catch((function(){}))},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},y=w,v=(a("00b0"),Object(c["a"])(y,f,h,!1,null,"f6ea98a8",null)),S=v.exports,x={formcode:"S06M02S3List",item:[{itemcode:"statusname",itemname:"名称",minwidth:"100",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center",datasheet:"Sa_DemandStatus.statusname"},{itemcode:"statusattr",itemname:"属性",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_DemandStatus.statusattr"},{itemcode:"rownum",itemname:"排序",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_DemandStatus.rownum"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_DemandStatus.remark"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_DemandStatus.lister"}]},k={name:"S06M02S3",components:{listheader:u,formedit:S},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:10,OrderType:0,SearchType:1,OrderBy:"Sa_DemandStatus.rownum"},tableForm:x}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;this.listLoading=!0,m["a"].post("/S06M02S3/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(t,this.tableForm),this.bindData()},handlePiPerMission:function(t,e){var a=this;this.$confirm("是否确定删除【"+e.statusname+"】，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.idx=t,m["a"].get("/S06M02S3/delete?key=".concat(t)).then((function(t){200==t.data.code&&(a.$message.success("删除成功"),a.bindData())})).catch((function(t){a.$message.warning("删除失败")}))})).catch((function(){}))},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={engineercode:t,engineername:t,engineertype:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.SearchPojo=t,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(t){this.idx=t,this.FormVisible=!0},closeForm:function(){this.bindData(),this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(t){this.idx=t}}},_=k,$=(a("2e673"),Object(c["a"])(_,i,n,!1,null,"62947d72",null));e["default"]=$.exports},b6f9:function(t,e,a){},d3ee:function(t,e,a){},e20a:function(t,e,a){"use strict";a("278b")}}]);