(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9310904e"],{"1ab2":function(t,e,n){},"704a":function(t,e,n){"use strict";n("1ab2")},ec6b:function(t,e,n){"use strict";n.r(e);var i=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"pageStyle",style:{"min-height":t.tableMaxHeight}},[t._m(0),n("div",{staticClass:"body"},[n("p",{staticStyle:{width:"100%",margin:"15px 0"}},[t._v("我们是业务顾问的同时，也是技术与服务的工程师，所以我们完全明白好的服务流程意味着什么")]),t._l(t.lst,(function(e,i){return n("div",{key:i,staticClass:"prod-item"},[n("h4",[t._v(t._s(e.title))]),n("p",{staticClass:"info"},[t._v(" "+t._s(e.content)+" ")])])}))],2)])},a=[function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"header"},[n("h3",{staticClass:"underline"},[n("span",[t._v("相关服务")])]),n("p",{staticStyle:{color:"#9b9a9a","margin-top":"16px"}},[t._v("Related Services")])])}],s={data:function(){return{lst:[]}},computed:{tableMaxHeight:function(){var t=window.innerHeight-408;return t<500&&(t=500),t+"px"}},mounted:function(){this.bindData()},methods:{bindData:function(){this.lst=[{title:"管理软件",content:"企业标准化软件、个性化软件开发"},{title:"数据安全",content:"加强数据保护,防止信息泄露"},{title:"物联网",content:"数据驱动,物联改变世界"},{title:"智慧工厂",content:"打造数字工厂，实现智能制造"}]}}},c=s,l=(n("704a"),n("2877")),o=Object(l["a"])(c,i,a,!1,null,"c3f784d2",null);e["default"]=o.exports}}]);