(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-98b06a48"],{"0c17":function(t,e,i){"use strict";i("3b5f")},"3b5f":function(t,e,i){},4555:function(t,e,i){"use strict";i("7687")},"62d8":function(t,e,i){"use strict";i("7238")},7238:function(t,e,i){},7687:function(t,e,i){},9195:function(t,e,i){"use strict";i.r(e);var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"todoContainer"},[i("listheader",{ref:"listheader",attrs:{userList:t.userList},on:{btnadd:function(e){return t.btnadd()},btnsearch:t.search,bindData:function(e){return t.bindData()},btnHelp:t.btnHelp,quickTickBtn:t.quickTickBtn}}),i("el-row",[i("el-col",{attrs:{span:t.showHelp?20:24}},[i("div",{staticStyle:{display:"flex","justify-content":"space-between"}},[i("div",{staticClass:"todoContai"},[i("div",{staticClass:"todoHead"},[i("div",{staticClass:"headorder"},[t._v("序号")]),t._l(t.userList,(function(e){return[i("div",{directives:[{name:"show",rawName:"v-show",value:e.show,expression:"t.show"}],key:e.id,staticClass:"headItem",style:{background:e.backcolorargb}},[t._v(" "+t._s(e.name)+" "+t._s(e.taskNum?"（"+e.taskNum+"）":"")+" ")])]}))],2),t._l(t.typeList,(function(e,a){return i("div",{key:a,staticClass:"todoBody"},[i("div",{staticClass:"left",style:{background:e.backTypeColor}},[t._v(" "+t._s(e.billtitle)+" ")]),t._l(t.userList,(function(a,n){return i("div",{key:n},[a.show?i("div",{staticClass:"bodyType"},[i("div",{staticClass:"right"},[i("draggable",{staticClass:"itemBox",staticStyle:{height:"100%"},attrs:{group:"items",tag:"div","data-row":e.billtitle,"data-col":a.id,"data-name":a.name},on:{end:function(i){return t.dragChange(i,e.billtitle,a.id,a.name)}}},[t._l(t.list,(function(n,s){return[n.accepterid==a.id&&new Date(n.endplan).getTime()>=new Date(e.startDate).getTime()&&new Date(n.endplan).getTime()<=new Date(e.endDate).getTime()?i("div",{key:s,staticClass:"items",style:{borderLeft:"5px solid "+t.getBorderColor(n.endplan,n.finishmark)},on:{mousedown:function(e){return t.sendMsg(n,n.id)}}},[i("div",{staticClass:"iconStyle"},[i("div",{staticClass:"iconStyle_left"},[t._v(" "+t._s(n.billtitle)+" ")]),i("div",{staticClass:"iconStyle_right"},[i("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"right-start"}},[i("i",{staticClass:"el-icon-more"}),i("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(e){return t.editItem(n,s)}}},[t._v("编辑")]),i("el-dropdown-item",{attrs:{icon:"el-icon-finished"},nativeOn:{click:function(e){return t.finish(n,s)}}},[t._v("完成")]),i("el-dropdown-item",{attrs:{icon:"el-icon-circle-close"},nativeOn:{click:function(e){return t.showDown(n)}}},[t._v("关闭 ")])],1)],1)],1)]),i("div",{staticClass:"dateStyle"},[i("div",{staticClass:"dateStyle_date"},[i("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t._f("dateFormat")(n.endplan))+" ")]),i("div",{staticClass:"dateStyle_public"},[i("span",{attrs:{title:"公共"},on:{click:function(e){return t.changeClick(n,"publicmark")}}},[i("i",{class:n.publicmark?"el-icon-message-solid":"el-icon-bell"})]),i("span",{attrs:{title:"重要"},on:{click:function(e){return t.changeClick(n,"importantmark")}}},[i("i",{class:n.importantmark?"el-icon-star-on":"el-icon-star-off"})]),i("span",{attrs:{title:"紧急"},on:{click:function(e){return t.changeClick(n,"urgentmark")}}},[i("i",{class:n.urgentmark?"el-icon-warning":"el-icon-warning-outline"})])])])]):t._e()]}))],2)],1)]):t._e()])}))],2)}))],2),i("div",{staticClass:"todoPublic"},[i("div",{staticClass:"todoHead",staticStyle:{"margin-left":"0px"}},[i("div",{staticClass:"headItem",style:{background:"#8bc34a"}},[t._v(" 公海 ")])]),i("div",{staticClass:"todoPublicBody"},[i("draggable",{staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px"},attrs:{group:"items",tag:"div","data-row":"公海","data-col":"root","data-name":"公海"},on:{end:function(e){return t.dragChange(e,"公海","root","公海")}}},[t._l(t.list,(function(e,a){return["公海"==e.billtype&&"root"==e.accepterid?i("div",{key:a,staticClass:"items",style:{borderLeft:"5px solid "+t.getBorderColor(e.endplan,e.finishmark)},on:{mouseover:function(i){return t.mouseOver(e,0)},mousedown:function(i){return t.sendMsg(e,e.id)}}},[i("div",{staticClass:"iconStyle"},[i("div",{staticClass:"iconStyle_left"},[t._v(t._s(e.billtitle))]),i("div",{staticClass:"iconStyle_right"},[i("el-dropdown",{attrs:{"hide-on-click":!1,trigger:"click",placement:"bottom-start"}},[i("i",{staticClass:"el-icon-more"}),i("el-dropdown-menu",{staticClass:"dropdownPop",attrs:{slot:"dropdown"},slot:"dropdown"},[i("el-dropdown-item",{attrs:{icon:"el-icon-edit"},nativeOn:{click:function(i){return t.editItem(e,a)}}},[t._v("编辑")]),i("el-dropdown-item",{attrs:{icon:"el-icon-delete"},nativeOn:{click:function(i){return t.deleteItem(e)}}},[t._v(" 删除 ")])],1)],1)],1)]),i("div",{staticClass:"dateStyle"},[i("div",{staticClass:"dateStyle_date"},[i("i",{staticClass:"el-icon-time"}),t._v(" "+t._s(t._f("dateFormat")(e.endplan))+" ")]),i("div",{staticClass:"dateStyle_public"},[i("span",{attrs:{title:"公共"},on:{click:function(i){return t.changeClick(e,"publicmark")}}},[i("i",{class:e.publicmark?"el-icon-message-solid":"el-icon-bell"})]),i("span",{attrs:{title:"紧急"},on:{click:function(i){return t.changeClick(e,"importantmark")}}},[i("i",{class:e.importantmark?"el-icon-star-on":"el-icon-star-off"})]),i("span",{attrs:{title:"重要"},on:{click:function(i){return t.changeClick(e,"urgentmark")}}},[i("i",{class:e.urgentmark?"el-icon-warning":"el-icon-warning-outline"})])])])]):t._e()]}))],2)],1)])])]),i("el-col",{attrs:{span:t.showHelp?4:0}},[i("helpmodel",{ref:"helpmodel",attrs:{code:"S06M09R1"}})],1)],1),t.finishVisible?i("el-dialog",{attrs:{width:"400px",title:"完成描述","append-to-body":!0,visible:t.finishVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.finishVisible=e}}},[i("div",{staticStyle:{padding:"15px"}},[i("el-form",{ref:"finishform",attrs:{model:t.finishform,"label-width":"60px",rules:t.finishformRules}},[i("el-form-item",{attrs:{label:"完工工时",prop:"finishhours"}},[i("el-input-number",{attrs:{min:0,step:.1,size:"small","controls-position":"right"},model:{value:t.finishform.finishhours,callback:function(e){t.$set(t.finishform,"finishhours",e)},expression:"finishform.finishhours"}}),i("span",{staticStyle:{"margin-left":"10px","font-weight":"bold"}},[t._v("H")])],1),i("div",{on:{click:function(e){return t.finishValidate("endremark")}}},[i("el-form-item",{attrs:{label:"描述",prop:"endremark"}},[i("el-input",{staticStyle:{width:"100%"},attrs:{type:"textarea",rows:3,placeholder:"请输入完成描述"},model:{value:t.finishform.endremark,callback:function(e){t.$set(t.finishform,"endremark",e)},expression:"finishform.endremark"}})],1)],1),i("div",{staticStyle:{"text-align":"right"}},[i("el-button",{attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.finishUpdate()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.finishVisible=!1}}},[t._v("取 消")])],1)],1)],1)]):t._e(),t.formeditVisible?i("el-dialog",{attrs:{width:"800px",title:"Todo","append-to-body":!0,visible:t.formeditVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.formeditVisible=e}}},[i("formedit",{ref:"formedit",attrs:{idx:t.idx}}),i("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.formeditSave()}}},[t._v("确 定")]),i("el-button",{on:{click:function(e){t.formeditVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},n=[],s=(i("99af"),i("c740"),i("b0c0"),i("e9c4"),i("d3b7"),i("25f0"),i("4d90"),i("0643"),i("4e3e"),i("159b"),i("b76a")),o=i.n(s),r=i("b775"),l=i("b893"),c=i("da36"),d=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[i("div",{staticStyle:{display:"flex"}},[i("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[i("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1)],1),i("div",{staticClass:"iShowBtn",staticStyle:{display:"flex"}},[i("quickTick",{ref:"quickTick",attrs:{droupList:t.droupList},on:{quickTickBtn:function(e){return t.$emit("quickTickBtn",e)}}}),i("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),i("el-button",{attrs:{size:"mini",title:"帮助",icon:"el-icon-s-help"},on:{click:function(e){return t.$emit("btnHelp")}}})],1)])])},f=[],u=i("e82a"),m={name:"Listheader",props:["tableForm","userList"],components:{quickTick:u["a"]},data:function(){return{strfilter:"",iShow:!1,formdata:{},droupList:[],thorList:!0,content:[{color:"#ff9800",value:"明天待完成"},{color:"#2196f3",value:"今天待完成"},{color:"#8bc34a",value:"正常完成"},{color:"#f44336",value:"逾期未完成"},{color:"#ffeb3b",value:"逾期完成"}]}},methods:{btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},changeList:function(t){this.droupList=[].concat(t)}}},p=m,h=(i("4555"),i("2877")),b=Object(h["a"])(p,d,f,!1,null,"635c07e1",null),g=b.exports,v=i("0521"),k={components:{draggable:o.a,listheader:g,helpmodel:v["a"],formedit:c["a"]},data:function(){return{list:[],userList:[],idx:0,formType:0,typeList:[{id:1,billtitle:"逾期",backTypeColor:"#D9001B",startDate:Object(l["b"])(this.getDay(-1e4)),endDate:Object(l["a"])(this.getDay(-1))},{id:2,billtitle:"今天",backTypeColor:"#7EC5FF",startDate:Object(l["b"])(new Date),endDate:Object(l["a"])(new Date)},{id:3,billtitle:"明天",backTypeColor:"#409EFF",startDate:Object(l["b"])(new Date(this.getDay(1))),endDate:Object(l["a"])(new Date(this.getDay(1)))},{id:4,billtitle:"后期",backTypeColor:"#95C804",startDate:Object(l["b"])(new Date(this.getDay(2))),endDate:Object(l["a"])(new Date(this.getDay(1e4)))}],currentId:0,todoform:{},todoformRules:{billtitle:[{required:!0,trigger:"blur",message:"标题为必填项"}]},finishVisible:!1,finishform:{id:"",endremark:"",finishhours:0},finishformRules:{endremark:[{required:!0,trigger:"blur",message:"完成描述为必填项"}]},showHelp:!1,queryParams:{PageNum:1,PageSize:1e3,OrderType:0,SearchType:1},formeditVisible:!1,dicList:[]}},created:function(){this.bindData(),this.getDicist()},methods:{bindData:function(){var t=this;r["a"].post("/S06M09B1/getOnlinePageList?type=1",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.list=e.data.data.list,t.getUserList(),t.$forceUpdate()),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getUserList:function(){var t=this,e={PageNum:1,PageSize:20,OrderType:0,SearchType:1};r["a"].post("/S06M09B1User/getPageList",JSON.stringify(e)).then((function(e){if(200==e.data.code){t.userList=e.data.data.list;for(var i=0;i<t.userList.length;i++){t.userList[i].show=!0,t.userList[i].taskNum=0;for(var a=0;a<t.list.length;a++)t.userList[i].id==t.list[a].accepterid&&(t.userList[i].taskNum+=1)}t.$forceUpdate(),t.$refs.listheader.changeList(t.userList)}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},quickTickBtn:function(t){var e=this.userList.findIndex((function(e){return e.id==t.id}));-1!=e&&(this.$set(this.userList[e],"show",t.show),this.$refs.listheader.changeList(this.userList)),this.$forceUpdate()},getDicist:function(){var t=this;r["a"].get("/SaDict/getBillEntityByDictCode?key=sa_todo.billtype").then((function(e){if(200==e.data.code){var i=e.data.data.item;t.dicList=i}t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},changeClick:function(t,e){t[e]=t[e]?0:1,r["a"].post("/S06M09B1/update",JSON.stringify(t)).then((function(e){200==e.data.code&&(t=e.data.data)})).catch((function(t){}))},dragChange:function(t){var e=this,i=t.to.dataset.row,a=t.to.dataset.col,n=t.to.dataset.name;this.list.forEach((function(t){t.id===e.currentId&&("逾期"==i?t.endplan=Object(l["a"])(e.getDay(-1)):"今天"==i?t.endplan=new Date:"明天"==i?t.endplan=Object(l["b"])(e.getDay(1)):"后期"==i&&(t.endplan=Object(l["b"])(e.getDay(2))),t.accepterid=a,t.accepter=n,e.favemove(t))})),this.$forceUpdate()},sendMsg:function(t,e){this.currentId=e},btnHelp:function(){this.showHelp=!this.showHelp,this.showHelp&&this.$refs.helpmodel.bindData()},showform:function(t){this.todoform.billtype="公海",this.todoform.accepterid="root"},todosave:function(){var t=this;r["a"].post("/S06M09B1/create",JSON.stringify(this.todoform)).then((function(e){200==e.data.code&&(t.todoform.billtitle="",t.list.push(e.data.data),t.$message.success("新增成功"),t.$refs.addpopover.doClose())})).catch((function(t){}))},btnadd:function(){var t=this;this.formType=0,setTimeout((function(){t.formeditVisible=!0}),100)},editItem:function(t,e){var i=this;this.idx=t.id,this.formType=1,this.formeditVisible=!0,setTimeout((function(){i.$refs.formedit.bindData()}),100)},formeditSave:function(){var t=this,e="/S06M09B1/create";this.formType&&(e="/S06M09B1/update");var i=Object.assign({},this.$refs.formedit.formdata);i.finishhours=this.finishform.finishhours,i.endremark=this.finishform.endremark,r["a"].post(e,JSON.stringify(i)).then((function(e){200==e.data.code&&(t.$message.success("新增成功"),t.formeditVisible=!1,t.bindData())})).catch((function(t){}))},favemove:function(t){var e={id:t.id,accepterid:t.accepterid,accepter:t.accepter,endplan:t.endplan,billtype:this.dicList[0].dictvalue};r["a"].post("/S06M09B1/update",JSON.stringify(e)).then((function(t){t.data.code})).catch((function(t){}))},cleValidate:function(t){this.$refs.todoform.clearValidate(t)},finishValidate:function(t){this.$refs.finishform.clearValidate(t)},finish:function(t,e){this.finishform.id=t.id,this.finishform.endremark="",this.finishform.finishmark=1,this.finishVisible=!0},finishUpdate:function(){var t=this;r["a"].post("/S06M09B1/update",JSON.stringify(this.finishform)).then((function(e){t.finishVisible=!1,200==e.data.code&&(t.$message.success(e.data.data.billtitle+"完成"),t.bindData(),t.finishVisible=!1)})).catch((function(t){}))},mouseOver:function(t,e){},showDown:function(t){var e=this;this.$confirm("是否确认关闭?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.reqClosed(t)}))},reqClosed:function(t){var e=this,i={id:t.id,closed:1};r["a"].post("/S06M09B1/update",JSON.stringify(i)).then((function(t){200==t.data.code&&(e.$message.success(t.data.data.billtitle+"已关闭"),e.bindData())})).catch((function(t){}))},deleteItem:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r["a"].get("/S06M09B1/delete?key="+t.id).then((function(t){200==t.data.code?(e.$message.success("删除成功"),e.bindData()):e.$message.warning(t.data.msg||"删除失败")}))}))},getDay:function(t){var e=new Date,i=e.getTime()+864e5*t;e.setTime(i);var a=e.getFullYear(),n=e.getMonth(),s=e.getDate();return n=this.doHandleMonth(n+1),s=this.doHandleMonth(s),a+"-"+n+"-"+s},doHandleMonth:function(t){var e=t;return 1===t.toString().length&&(e="0"+t),e},getBorderColor:function(t,e){if(!t)return"#9e9e9e";var i="",a=new Date(t).setHours(23,59,59,0),n=(new Date).getTime();return i=a-n<0?e?"#ffeb3b":"#f44336":a-n<864e5?e?"#8bc34a":"#2196f3":a-n>864e5&&a-n<1728e5?"#ff9800":"#9e9e9e",i},search:function(t){""!=t?this.queryParams.SearchPojo={billtitle:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()}},filters:{dateFormat:function(t){if(t){var e=new Date(t),i=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),a=e.getDate().toString().padStart(2,"0");return"".concat(i,"-").concat(a)}}}},y=k,w=(i("62d8"),Object(h["a"])(y,a,n,!1,null,"e6b7901a",null));e["default"]=w.exports},da36:function(t,e,i){"use strict";var a=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[i("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[i("el-row",[i("el-col",{attrs:{span:12}},[i("div",{on:{click:function(e){return t.cleValidate("billtype")}}},[i("el-form-item",{attrs:{label:"类型",prop:"billtype"}},[i("el-input",{attrs:{placeholder:"类型",size:"small",readonly:""},model:{value:t.formdata.billtype,callback:function(e){t.$set(t.formdata,"billtype",e)},expression:"formdata.billtype"}})],1)],1)])],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("div",{on:{click:function(e){return t.cleValidate("billtitle")}}},[i("el-form-item",{attrs:{label:"标题",prop:"billtitle"}},[i("el-input",{attrs:{placeholder:"请输入标题",clearable:"",size:"small"},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1)],1)]),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"计划完成"}},[i("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:"small",clearable:"",placeholder:"时间"},model:{value:t.formdata.endplan,callback:function(e){t.$set(t.formdata,"endplan",e)},expression:"formdata.endplan"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"客户名"}},[i("el-popover",{ref:"groupnamePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selDictionariesRef.bindData()}}},[i("selDictionaries",{ref:"selDictionariesRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.groupname"},on:{singleSel:function(e){t.formdata.groupname=e.dictvalue,t.$refs.groupnamePopverRef.doClose()},closedic:function(e){return t.$refs.groupnamePopverRef.doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"客户名",clearable:"",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)],1)],1),i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"产品名"}},[i("el-popover",{ref:"productnamePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.selProductnameRef.bindData()}}},[i("selDictionaries",{ref:"selProductnameRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.productname"},on:{singleSel:function(e){t.formdata.productname=e.dictvalue,t.$refs.productnamePopverRef.doClose()},closedic:function(e){return t.$refs.productnamePopverRef.doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"产品名",clearable:"",size:"small"},model:{value:t.formdata.productname,callback:function(e){t.$set(t.formdata,"productname",e)},expression:"formdata.productname"}})],1)],1)],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:12}},[i("el-form-item",{attrs:{label:"来源"}},[i("el-popover",{ref:"sourcePopverRef",attrs:{placement:"bottom",trigger:"click"},on:{show:function(e){return t.$refs.sourceRef.bindData()}}},[i("selDictionaries",{ref:"sourceRef",staticStyle:{width:"300px"},attrs:{multi:0,billcode:"Sa_Todo.source"},on:{singleSel:function(e){t.formdata.source=e.dictvalue,t.$refs.sourcePopverRef.doClose()},closedic:function(e){return t.$refs.sourcePopverRef.doClose()}}}),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("el-input",{attrs:{placeholder:"来源",clearable:"",size:"small"},model:{value:t.formdata.source,callback:function(e){t.$set(t.formdata,"source",e)},expression:"formdata.source"}})],1)],1)],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:""}},[i("el-checkbox",{attrs:{label:"公共","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.publicmark,callback:function(e){t.$set(t.formdata,"publicmark",e)},expression:"formdata.publicmark"}})],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:""}},[i("el-checkbox",{attrs:{label:"重要","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.importantmark,callback:function(e){t.$set(t.formdata,"importantmark",e)},expression:"formdata.importantmark"}})],1)],1),i("el-col",{attrs:{span:2}},[i("el-form-item",{attrs:{label:""}},[i("el-checkbox",{attrs:{label:"紧急","true-label":1,"false-label":0,size:"mini"},model:{value:t.formdata.urgentmark,callback:function(e){t.$set(t.formdata,"urgentmark",e)},expression:"formdata.urgentmark"}})],1)],1)],1),i("el-row",[i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"创建人"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"创建日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),i("el-col",{attrs:{span:4}},[i("el-form-item",{attrs:{label:"制表"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),i("el-col",{attrs:{span:8}},[i("el-form-item",{attrs:{label:"修改日期"}},[i("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)},n=[],s=(i("b64b"),i("b775")),o={name:"Formedit",props:["idx"],data:function(){return{title:"TODO列表",formdata:{billtype:"公海",accepterid:"root",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,productname:"",groupname:"",endplan:new Date},formRules:{type:[{required:!0,trigger:"blur",message:"类型不能为空"}],title:[{required:!0,trigger:"blur",message:"标题不能为空"}],content:[{required:!0,trigger:"blur",message:"公告内容不能为空"}]},multi:0,selVisible:!1,formLabelWidth:"100px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&s["a"].get("/S06M09B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?t.formdata=e.data.data:t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.closeForm()}}),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs.formdata.validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?CRUD.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")})):CRUD.update(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.$emit("bindData"),t.bindData(),t.$emit("compForm"))})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),CRUD.delete(t).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("compForm")})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},cleValidate:function(t){this.$refs.formdata.clearValidate(t)}}},r=o,l=(i("0c17"),i("2877")),c=Object(l["a"])(r,a,n,!1,null,"007443e8",null);e["a"]=c.exports}}]);