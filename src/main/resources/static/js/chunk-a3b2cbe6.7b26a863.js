(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a3b2cbe6"],{"4f0e":function(e,t,a){"use strict";a("5645")},5645:function(e,t,a){},"7db8":function(e,t,a){"use strict";a("e155")},"7fac":function(e,t,a){"use strict";a.r(t);var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:e.tableForm},on:{btnadd:function(t){return e.showform(0)},btnsearch:e.search,bindData:e.bindData,AdvancedSearch:e.AdvancedSearch}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.listLoading,expression:"listLoading"}],staticStyle:{overflow:"auto"},attrs:{data:e.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},height:e.tableMaxHeight},on:{"sort-change":e.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"40"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),e._l(e.tableForm.item,(function(t,i){return[!t.displaymark?e._e():a("el-table-column",{key:i,attrs:{prop:t.itemcode,columnKey:t.itemcode,label:t.itemname,align:t.aligntype?t.aligntype:"center","min-width":t.minwidth,"show-overflow-tooltip":!!t.overflow,sortable:!!t.sortable},scopedSlots:e._u([{key:"default",fn:function(i){return["labelname"==t.itemcode?a("span",{class:"label-color label-"+i.row.labelcolor,on:{click:function(t){return e.showform(i.row.id)}}},[e._v(" "+e._s(i.row[t.itemcode]?i.row[t.itemcode]:"名称")+" ")]):"createdate"==t.itemcode||"modifydate"==t.itemcode?a("span",[e._v(e._s(e._f("dateFormats")(i.row[t.itemcode])))]):"enabledmark"==t.itemcode?a("div",[i.row.enabledmark?a("el-tag",{attrs:{size:"small"}},[e._v("启用")]):a("el-tag",{attrs:{type:"warning",size:"small"}},[e._v("停 用")])],1):"statusattr"==t.itemcode?a("div",[0==i.row[t.itemcode]?a("span",[e._v("待办")]):1==i.row[t.itemcode]?a("span",[e._v("进行中")]):2==i.row[t.itemcode]?a("span",[e._v("已完成")]):-1==i.row[t.itemcode]?a("span",[e._v("已拒绝")]):e._e()]):a("span",[e._v(e._s(i.row[t.itemcode]))])]}}],null,!0)})]})),2==e.$store.state.user.userinfo.isadmin||null!=e.$store.state.user.userinfo.engineer&&"管理者"==e.$store.state.user.userinfo.engineer.engineertype?a("el-table-column",{attrs:{label:"操作",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{color:"#f56c6c"},attrs:{size:"mini",type:"text",icon:"el-icon-delete"},on:{click:function(a){return e.handlePiPerMission(t.row.id,t.row)}}},[e._v("删除")])]}}],null,!1,828007154)}):e._e()],2),a("Pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total > 0"}],attrs:{total:e.total,page:e.queryParams.PageNum,limit:e.queryParams.PageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"PageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"PageSize",t)},pagination:e.getList}})],1)],1)],1)],1),e.FormVisible?a("el-dialog",{attrs:{width:"600px",title:"状态设置","append-to-body":!0,visible:e.FormVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.FormVisible=t},close:e.bindData}},[a("formedit",{ref:"formedit",attrs:{idx:e.idx},on:{bindData:e.bindData,changeIdx:e.changeIdx,closeDialog:function(t){e.FormVisible=!1}}})],1):e._e()],1)},n=[],r=(a("e9c4"),function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{class:"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.btnsearch(t)}},model:{value:e.strfilter,callback:function(t){e.strfilter=t},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:e.btnsearch},slot:"append"},[e._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:e.btnadd}},[e._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:e.bindData}})],1)])])}),o=[],s={name:"Listheader",props:["tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{}}},methods:{AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")}}},l=s,c=(a("cacf"),a("2877")),d=Object(c["a"])(l,r,o,!1,null,"399c3e76",null),m=d.exports,u=a("b775"),f=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("div",[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:e.formdata,"label-width":"100px",rules:e.formRules}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("labelname")}}},[a("el-form-item",{attrs:{label:"名称",prop:"labelname"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},model:{value:e.formdata.labelname,callback:function(t){e.$set(e.formdata,"labelname",t)},expression:"formdata.labelname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:24}},[a("div",{on:{click:function(t){return e.cleValidate("labelcolor")}}},[a("el-form-item",{attrs:{label:"颜色",prop:"labelcolor"}},[a("div",{staticClass:"flex",staticStyle:{padding:"10px"}},e._l(e.discColor,(function(t,i){return a("div",{key:i,class:"disc-color disc-"+t,on:{click:function(a){e.formdata.labelcolor=t}}},[e.formdata.labelcolor==t?a("i",{staticClass:"el-icon-check"}):e._e()])})),0)])],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0},model:{value:e.formdata.rownum,callback:function(t){e.$set(e.formdata,"rownum",t)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent"},[a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入备注",clearable:""},model:{value:e.formdata.remark,callback:function(t){e.$set(e.formdata,"remark",t)},expression:"formdata.remark"}})],1)],1)],1)],1),a("el-row",[a("el-col",{staticStyle:{display:"flex","align-items":"center","justify-content":"space-between","margin-top":"20px"},attrs:{span:24}},[a("div",[e.formdata.id?a("el-button",{attrs:{type:"danger"},on:{click:function(t){return e.deleteBtn()}}},[e._v("删除")]):e._e()],1),a("div",[a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("formdata")}}},[e._v("保存")]),a("el-button",{on:{click:e.closeDialog}},[e._v("关闭")])],1)])],1)],1)])])},h=[];a("b64b");const p={add(e){return new Promise((t,a)=>{var i=JSON.stringify(e);u["a"].post("/S06M02S6/create",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},update(e){return new Promise((t,a)=>{var i=JSON.stringify(e);u["a"].post("/S06M02S6/update",i).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})},delete(e){return new Promise((t,a)=>{u["a"].get("/S06M02S6/delete?key="+e).then(e=>{200==e.data.code?t(e.data):a(e.data.msg)}).catch(e=>{a(e)})})}};var b=p,g=a("5b24"),w={name:"addDialog",props:["idx"],components:{autoComplete:g["a"]},data:function(){return{title:"标签设置",currentId:this.idx,formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,labelname:"",labelcolor:"blue",rownum:0,remark:""},formRules:{labelname:[{required:!0,trigger:"blur",message:"名称不能为空"}]},discColor:["blue","green","zi","orange","red"]}},watch:{idx:function(e,t){this.currentId=e,this.binddata()}},created:function(){this.binddata()},methods:{binddata:function(){var e=this;0!=this.currentId&&u["a"].get("/S06M02S6/getEntity?key=".concat(this.currentId)).then((function(t){200==t.data.code?e.formdata=t.data.data:e.$alert(t.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(t){e.closeDialog()}})})).catch((function(t){e.$message.error(t||"请求错误")}))},submitForm:function(e){var t=this;this.$refs["formdata"].validate((function(e){if(!e)return!1;t.saveForm()}))},saveForm:function(){var e=this;if(0==this.idx){var t=Object.assign({},this.formdata);b.add(t).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.formdata=t.data.data,e.$emit("bindData"),e.closeDialog())})).catch((function(t){e.$message.warning(t||"保存失败")}))}else b.update(this.formdata).then((function(t){200==t.code&&(e.$message.success("保存成功"),e.$emit("changeIdx",t.data.id),e.$emit("bindData"))})).catch((function(t){e.$message.warning(t||"保存失败")}))},deleteBtn:function(){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){b.delete(e.formdata.id).then((function(t){200==t.code&&e.$message.success("删除成功"),e.$emit("bindData"),e.closeDialog()})).catch((function(){e.$message.warning("删除失败")}))})).catch((function(){}))},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}},y=w,v=(a("4f0e"),Object(c["a"])(y,f,h,!1,null,"441e1f0e",null)),x=v.exports,S={formcode:"S06M02S6List",item:[{itemcode:"labelname",itemname:"名称",minwidth:"100",defwidth:"",displaymark:1,fixed:0,overflow:1,aligntype:"center",datasheet:"Sa_DemandLabel.labelname"},{itemcode:"rownum",itemname:"排序",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_DemandLabel.rownum"},{itemcode:"remark",itemname:"备注",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_DemandLabel.remark"},{itemcode:"lister",itemname:"制表",minwidth:"70",displaymark:1,overflow:1,datasheet:"Sa_DemandLabel.lister"}]},k={name:"S06M02S6",components:{listheader:m,formedit:x},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:10,OrderType:0,SearchType:1},tableForm:S}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},mounted:function(){this.bindData()},methods:{bindData:function(){var e=this;this.listLoading=!0,u["a"].post("/S06M02S6/getPageList",JSON.stringify(this.queryParams)).then((function(t){200==t.data.code&&(e.lst=t.data.data.list,e.total=t.data.data.total),e.listLoading=!1})).catch((function(t){e.listLoading=!1}))},changeSort:function(e){"descending"==e.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(e,this.tableForm),this.bindData()},handlePiPerMission:function(e,t){var a=this;this.$confirm("是否确定删除【"+t.statusname+"】，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.idx=e,u["a"].get("/S06M02S6/delete?key=".concat(e)).then((function(e){200==e.data.code&&(a.$message.success("删除成功"),a.bindData())})).catch((function(e){a.$message.warning("删除失败")}))})).catch((function(){}))},getList:function(e){this.queryParams.PageNum=e.page,this.queryParams.PageSize=e.limit,this.bindData()},search:function(e){""!=e?this.queryParams.SearchPojo={engineercode:e,engineername:e,engineertype:e}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(e){this.queryParams.SearchPojo=e,this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},showform:function(e){this.idx=e,this.FormVisible=!0},closeForm:function(){this.bindData(),this.FormVisible=!1},compForm:function(){this.bindData(),this.FormVisible=!1},changeidx:function(e){this.idx=e}}},_=k,P=(a("7db8"),Object(c["a"])(_,i,n,!1,null,"63955cf4",null));t["default"]=P.exports},cacf:function(e,t,a){"use strict";a("d510")},d510:function(e,t,a){},e155:function(e,t,a){}}]);