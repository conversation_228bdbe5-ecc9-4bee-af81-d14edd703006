(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-a3ba31ea"],{"1c63":function(t,e,a){},"3d7b":function(t,e,a){"use strict";a("5c2d")},"43f9":function(t,e,a){},4678:function(t,e,a){var i={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"2554","./bs.js":"2554","./ca":"d7167","./ca.js":"d7167","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eba","./de-at.js":"b3eba","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e9","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e9","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function s(t){var e=n(t);return a(e)}function n(t){if(!a.o(i,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return i[t]}s.keys=function(){return Object.keys(i)},s.resolve=n,t.exports=s,s.id="4678"},"5c2d":function(t,e,a){},6172:function(t,e,a){"use strict";a("7426")},"6f6b":function(t,e,a){},7426:function(t,e,a){},"77c2":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticStyle:{height:"40px"}},[a("ListHeader",{ref:"ListHeader",attrs:{userList:t.userList,statusList:t.statusList,labelList:t.labelList,projectAllList:t.projectAllList,billtypeArr:t.billtypeArr},on:{btnadd:function(e){return t.openWork(1,e)},btnsearch:t.search,bindData:t.bindData,historyBtn:t.historyBtn,changeShowType:t.changeShowType,changeIsOwn:t.changeIsOwn,changeBillType:t.changeBillType}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"成员看板"==t.showType,expression:"showType == '成员看板'"}]},[a("KanBanView",{ref:"KanBanView",attrs:{userList:t.userList,statusList:t.statusList,labelList:t.labelList,list:t.list,listCopy:t.listCopy,billtypeArr:t.billtypeArr},on:{checkhistory:t.checkhistory,openHistoryFormedit:t.openHistoryFormedit,bindData:t.bindData}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"状态看板"==t.showType,expression:"showType == '状态看板'"}]},[a("StatusKanBanView",{ref:"StatusKanBanView",attrs:{userList:t.userList,statusList:t.statusList,labelList:t.labelList,list:t.list,listCopy:t.listCopy,billtypeArr:t.billtypeArr},on:{checkhistory:t.checkhistory,openHistoryFormedit:t.openHistoryFormedit,bindData:t.bindData}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"类型看板"==t.showType,expression:"showType == '类型看板'"}]},[a("TypeKanBanView",{ref:"TypeKanBanView",attrs:{userList:t.userList,statusList:t.statusList,labelList:t.labelList,list:t.list,listCopy:t.listCopy,billtypeArr:t.billtypeArr},on:{checkhistory:t.checkhistory,openHistoryFormedit:t.openHistoryFormedit,bindData:t.bindData}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"标签看板"==t.showType,expression:"showType == '标签看板'"}]},[a("LabelKanBanView",{ref:"LabelKanBanView",attrs:{userList:t.userList,statusList:t.statusList,labelList:t.labelList,list:t.list,listCopy:t.listCopy,billtypeArr:t.billtypeArr},on:{checkhistory:t.checkhistory,openHistoryFormedit:t.openHistoryFormedit,bindData:t.bindData}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"日历看板"==t.showType,expression:"showType == '日历看板'"}]},[a("FullCalendarView",{ref:"FullCalendarView",attrs:{userList:t.userList,statusList:t.statusList,labelList:t.labelList,list:t.list,listCopy:t.listCopy,billtypeArr:t.billtypeArr},on:{openHistoryFormedit:t.openHistoryFormedit,bindData:t.bindData}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:"表格"==t.showType,expression:"showType == '表格'"}]},[a("TableList",{ref:"tableList",attrs:{userList:t.userList,statusList:t.statusList,labelList:t.labelList,list:t.list,listCopy:t.listCopy},on:{openHistoryFormedit:t.openHistoryFormedit,changeTimeStatus:t.changeTimeStatus,bindData:t.bindData}})],1),t.formeditVisible?a("el-dialog",{key:t.formeditKey,attrs:{width:"56vw",title:"任务","append-to-body":!0,visible:t.formeditVisible,"close-on-click-modal":!1,"destroy-on-close":!0,top:"6vh"},on:{"update:visible":function(e){t.formeditVisible=e},close:t.bindData}},[a("div",{staticClass:"flex",staticStyle:{"justify-content":"space-between"},attrs:{slot:"title"},slot:"title"},[a("div",{staticClass:"flex"},[a("span",[t._v("任务 -")]),a("el-popover",{ref:"timestatusRef",attrs:{placement:"bottom-start",trigger:"click","popper-class":""}},[a("div",[t.selForm.timestatus&&"暂停"!=t.selForm.timestatus?"开始"==t.selForm.timestatus?a("div",[a("el-button",{staticStyle:{color:"#fa8c15"},attrs:{type:"text",icon:"el-icon-video-pause"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"暂停")}}},[t._v(" 暂停 ")])],1):t._e():a("div",[a("el-button",{attrs:{type:"text",icon:"el-icon-video-play"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"开始")}}},[t._v(" 开始 ")])],1),"开始"==t.selForm.timestatus?a("div",[a("el-button",{staticStyle:{color:"#14a10f"},attrs:{type:"text",icon:"el-icon-finished"},on:{click:function(e){return t.changeTimeStatus(t.selForm,"完成")}}},[t._v(" 完成 ")])],1):t._e()]),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("span",{staticStyle:{"text-decoration":"underline",cursor:"pointer","margin-left":"4px"}},[t._v(t._s(t.selForm.timestatus?t.selForm.timestatus:"未开始"))])])])],1),a("div",{staticClass:"flex a-c",staticStyle:{"margin-right":"20px",cursor:"pointer"}},[a("el-dropdown",{staticStyle:{margin:"0px 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("i",{staticClass:"el-icon-more"}),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-refresh-right"},nativeOn:{click:function(e){return t.$refs.formedit.bindData()}}},[t._v("刷 新")]),a("el-dropdown-item",{attrs:{disabled:2!=t.$store.state.user.userinfo.isadmin&&(t.$store.state.user.userinfo.engineer.id!=t.selForm.appointeeid&&t.$store.state.user.userinfo.userid!=t.selForm.createbyid),icon:"el-icon-delete"},nativeOn:{click:function(e){return t.deleteForm()}}},[t._v("删 除 ")])],1)],1)],1)]),a("FormEdit",{ref:"formedit",attrs:{idx:t.selForm.id,userList:t.userList,statusList:t.statusList,billtypeArr:t.billtypeArr},on:{closeDialog:function(e){t.formeditVisible=!1},bindData:function(e){return t.bindData(t.project)}}})],1):t._e(),t.workVisible?a("el-dialog",{attrs:{width:"480px",title:"新建"+t.workdata.billtype,"append-to-body":!0,visible:t.workVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.workVisible=e},close:t.bindData}},[a("el-form",{ref:"workdata",attrs:{model:t.workdata,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"项目名称",prop:"billtitle"}},[a("el-popover",{ref:"labeljsonRef",attrs:{placement:"bottom-start",trigger:"click","popper-class":"popper-labeljson"}},[a("div",{staticClass:"labeljsonBody",staticStyle:{height:"250px",width:"240px","overflow-y":"hidden"}},[a("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}]},[a("div",{staticClass:"labeljson-header"},[a("input",{directives:[{name:"model",rawName:"v-model",value:t.projectVal,expression:"projectVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索项目"},domProps:{value:t.projectVal},on:{input:[function(e){e.target.composing||(t.projectVal=e.target.value)},t.searchProject]}})]),t.projectAllListCopy.length?a("div",{staticStyle:{height:"220px","overflow-y":"auto"}},t._l(t.projectAllListCopy,(function(e,i){return a("div",{key:i},[a("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeProject(e)}}},[a("span",{class:"label-color"},[t._v(t._s(e.projname))]),a("div",[a("i",{staticClass:"el-icon-check",style:{opacity:t.workdata.projectid==e.id?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):a("div",[a("el-empty",{attrs:{description:"暂无项目","image-size":90}})],1)])]),a("div",{staticStyle:{display:"inline-block"},attrs:{slot:"reference"},slot:"reference"},[a("span",{staticStyle:{cursor:"pointer"}},[t._v(t._s(t.workdata.itemname?t.workdata.itemname:"暂无项目"))])])])],1),a("el-form-item",{attrs:{label:"事务标题",prop:"billtitle"}},[a("el-input",{attrs:{placeholder:"请输入事务标题",clearable:""},model:{value:t.workdata.billtitle,callback:function(e){t.$set(t.workdata,"billtitle",e)},expression:"workdata.billtitle"}})],1),a("el-form-item",{attrs:{label:"事务描述",prop:"remark"}},[a("el-input",{attrs:{placeholder:"请输入任务描述",clearable:"",size:"small",type:"textarea",autosize:{minRows:3,maxRows:6}},model:{value:t.workdata.remark,callback:function(e){t.$set(t.workdata,"remark",e)},expression:"workdata.remark"}})],1),a("el-form-item",{attrs:{label:"开始时间",prop:"startdate"}},[a("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择开始时间","default-time":"08:30:00",clearable:!0,editable:!1},on:{clear:function(e){t.workdata.startdate=""},input:function(e){t.workdata.startdate=e}},model:{value:t.workdata.startdate,callback:function(e){t.$set(t.workdata,"startdate",e)},expression:"workdata.startdate"}})],1),a("el-form-item",{attrs:{label:"截止时间",prop:"startdate"}},[a("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择截止时间","default-time":"17:00:00",clearable:!0,editable:!1},on:{clear:function(e){t.workdata.deaddate=""},input:function(e){t.workdata.deaddate=e}},model:{value:t.workdata.deaddate,callback:function(e){t.$set(t.workdata,"deaddate",e)},expression:"workdata.deaddate"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.submitWorkUpdate()}}},[t._v("提 交")]),a("el-button",{on:{click:function(e){t.workVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.historyopen.show?a("HistoryView",t._b({ref:"historyView",attrs:{show:t.historyopen.show,userList:t.userList,statusList:t.statusList},on:{"update:show":function(e){return t.$set(t.historyopen,"show",e)},openHistoryFormedit:t.openHistoryFormedit}},"HistoryView",t.historyopen,!1)):t._e()],1)},s=[],n=a("c7eb"),o=a("1da1"),l=a("2909"),r=(a("99af"),a("caad"),a("e9c4"),a("2532"),function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"laneBody"},[a("div",{staticClass:"leftCol"},t._l(t.labelList,(function(e,i){return a("div",{key:i,staticClass:"leftColItem"},[a("div",{staticClass:"leftColHeader"},[a("div",{staticClass:"leftColTitle flex j-s"},[a("span"),a("span",{class:"label-color label-"+e.labelcolor},[t._v(" "+t._s(e.labelname))]),a("span")])]),a("div",{staticClass:"leftColBody"},[a("draggable",{key:t.keynum,staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px","overflow-y":"auto","margin-bottom":"8px"},attrs:{group:"aItemBox",tag:"div","data-index":i,"data-id":e.id,dragClass:"dragClass",ghostClass:"ghostClass",animation:"500",touchStartThreshold:"50px"},on:{end:function(a){return t.endaItem(a,e)}}},[a("transition-group",{staticStyle:{height:"100%",display:"block"},attrs:{"data-index":i,"data-id":e.id}},[t._l(t.list,(function(i,s){return[t.isShowView(i,e)?a("div",{key:s,staticClass:"itemBox-li",style:{"border-color":t.setitemBoxli(i.level)},on:{mousedown:function(e){return t.handleSelect(i)},click:function(e){return t.$emit("openHistoryFormedit",i)}}},[a("div",{staticClass:"aItemTitle",style:{color:"#333"},attrs:{title:i.billtitle}},[t._v(" "+t._s(i.billtitle)+" ")]),a("el-tag",{staticStyle:{"margin-right":"10px"},attrs:{type:["缺陷"].includes(i.billtype)?"warning":"primary",size:"mini"}},[t._v(t._s(i.billtype))]),a("div",{staticClass:"flex",staticStyle:{"margin-top":"4px"}},t._l(i.labeljson?JSON.parse(i.labeljson):[],(function(e,i){return a("div",{key:i},[a("span",{class:"label-color label-"+e.color,staticStyle:{"font-size":"12px","margin-right":"4px"}},[t._v(" "+t._s(e.name))])])})),0),t.isdeaddate(i.deaddate)?a("div",{staticClass:"deaddateSty"},[i.startdate?a("span",{staticClass:"dateBlue",staticStyle:{"margin-bottom":"4px"}},[t._v(" "+t._s(t.startdata(i.startdate))+" 开始 ")]):t._e(),a("span",{class:(new Date).getTime()>new Date(i.deaddate).getTime()?"dateRed":new Date(i.deaddate).getTime()-(new Date).getTime()<864e5?"dateOrange":"dateBlue"},[t._v(t._s(t.isdeaddate(i.deaddate))+" 截止")])]):t._e(),a("div",{staticClass:"createbySty",style:{color:"#9e9e9e","margin-right":"4px",float:"right"},attrs:{title:"执行者"}},[a("span",{staticClass:"timestatusSty",style:t.getitemStatus(i.timestatus)},[t._v(" "+t._s(i.timestatus))]),a("i",{staticClass:"el-icon-user-solid"}),t._v(" "+t._s(i.appointee)+" ")])],1):t._e()]}))],2)],1)],1)])})),0)])}),c=[],d=(a("c740"),a("b0c0"),a("b64b"),a("d3b7"),a("25f0"),a("4d90"),a("b76a")),m=a.n(d),p={components:{draggable:m.a},props:{userList:{type:Array},labelList:{type:Array},list:{type:Array},listCopy:{type:Array}},data:function(){return{selForm:{},searchAll:"",searchList:[],balance:!0,keynum:0}},methods:{bindData:function(){this.searchList=[];for(var t=0;t<this.list.length;t++){var e=this.list[t];e.appointeeid||this.searchList.push(e)}},handleSelect:function(t){this.selForm=Object.assign({},t)},endaItem:function(t,e){var a=this;if(console.log("endaItem",t,t.to.dataset.id,e),void 0!=e||void 0!=t.to.dataset.id){var i=this.selForm.labeljson?JSON.parse(this.selForm.labeljson):[];i.length?i[0]=Object.assign({},this.getLableRow(t.to.dataset.id)):i.push(this.getLableRow(t.to.dataset.id));var s={id:this.selForm.id,labeljson:i};e&&t.to.dataset.id==e.id||(console.log("paramsdata",s),this.$request.post("/S06M02B1/update",JSON.stringify(s)).then((function(t){200==t.data.code?(a.$message.success("标签修改成功"),a.$emit("bindData")):(a.$message.warning(t.data.msg||"操作失败"),a.bindData())})))}},getLableRow:function(t){var e={name:"",color:"",labelid:""};if(t){var a=this.labelList.findIndex((function(e){return e.id==t}));-1!=a&&(e.name=this.labelList[a].labelname,e.color=this.labelList[a].labelcolor,e.labelid=this.labelList[a].id)}return e},isShowView:function(t,e){for(var a=!1,i=t.labeljson?JSON.parse(t.labeljson):[],s=0;s<i.length;s++){var n=i[s];n.labelid==e.id&&(a=!0)}return a},getitemStatus:function(t){var e={color:"#000",padding:"0px 6px","border-radius":"4px",display:"inline-block","font-size":"12px","margin-right":"10px"};return"开始"==t?(e.color="#409EFF",e.border="1px solid"):"暂停"==t?(e.color="#fa8c15",e.border="1px solid"):"完成"==t&&(e.color="#14a10f",e.border="1px solid"),e},setitemBoxli:function(t){return 1==t?"rgb(27, 154, 238)":2==t?"rgb(250, 140, 21)":3==t?"rgb(230, 36, 18)":"rgb(140, 140, 140)"},isdeaddate:function(t){if(!t)return!1;var e=new Date(t).getTime(),a=new Date(t),i=(a.getFullYear(),(a.getMonth()+1).toString().padStart(2,"0")),s=a.getDate().toString().padStart(2,"0"),n=a.getHours().toString().padStart(2,"0"),o=a.getMinutes().toString().padStart(2,"0");return(new Date).getTime()>e||(new Date).getTime(),"".concat(i,"月").concat(s,"日").concat(n,":").concat(o)},startdata:function(t){if(!t)return!1;new Date(t).getTime();var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),i=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0");return"".concat(a,"月").concat(i,"日").concat(s,":").concat(n)}}},u=p,f=(a("3d7b"),a("2877")),h=Object(f["a"])(u,r,c,!1,null,"6d83b844",null),g=h.exports,b=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"laneBody"},[a("div",{staticClass:"leftCol"},t._l(t.statusList,(function(e,i){return a("div",{key:i,staticClass:"leftColItem"},[a("div",{staticClass:"leftColHeader"},[a("div",{staticClass:"leftColTitle flex j-s",class:"label-"+e.statuscolor},[a("span"),a("span",[t._v(" "+t._s(e.statusname))]),a("span")])]),a("div",{staticClass:"leftColBody"},[a("draggable",{key:t.keynum,staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px","overflow-y":"auto","margin-bottom":"8px"},attrs:{group:"aItemBox",tag:"div","data-index":i,"data-name":e.statusname,"data-id":e.id,dragClass:"dragClass",ghostClass:"ghostClass",animation:"500",touchStartThreshold:"50px"},on:{end:function(a){return t.endaItem(a,e)}}},[a("transition-group",{staticStyle:{height:"100%",display:"block"},attrs:{"data-index":i,"data-name":e.statusname,"data-id":e.id}},[t._l(t.list,(function(i,s){return[i.demandstatus==e.id?a("div",{key:s,staticClass:"itemBox-li",style:{"border-color":t.setitemBoxli(i.level)},on:{mousedown:function(e){return t.handleSelect(i)},click:function(e){return t.$emit("openHistoryFormedit",i)}}},[a("div",{staticClass:"aItemTitle",style:{color:"#333"},attrs:{title:i.billtitle}},[t._v(" "+t._s(i.billtitle)+" ")]),a("el-tag",{staticStyle:{"margin-right":"10px"},attrs:{type:["缺陷"].includes(i.billtype)?"warning":"primary",size:"mini"}},[t._v(t._s(i.billtype))]),a("div",{staticClass:"flex a-c"},t._l(i.labeljson?JSON.parse(i.labeljson):[],(function(e,i){return a("div",{key:i,staticStyle:{"margin-top":"4px"},attrs:{title:"标签"}},[a("span",{class:"label-color label-"+e.color,staticStyle:{"font-size":"12px","margin-right":"4px"}},[t._v(" "+t._s(e.name))])])})),0),t.isdeaddate(i.deaddate)?a("div",{staticClass:"deaddateSty"},[a("span",{class:(new Date).getTime()>new Date(i.deaddate).getTime()?"dateRed":new Date(i.deaddate).getTime()-(new Date).getTime()<864e5?"dateOrange":"dateBlue"},[t._v(t._s(t.isdeaddate(i.deaddate))+" 截止")])]):t._e(),a("div",{staticClass:"createbySty",style:{color:"#9e9e9e","margin-right":"4px",float:"right"},attrs:{title:"执行者"}},[a("span",{staticClass:"timestatusSty",style:t.getitemStatus(i.timestatus)},[t._v(" "+t._s(i.timestatus))]),a("i",{staticClass:"el-icon-user-solid"}),t._v(" "+t._s(i.appointee)+" ")])],1):t._e()]}))],2)],1)],1)])})),0)])},v=[],y=(a("c533"),{components:{draggable:m.a},props:{userList:{type:Array},statusList:{type:Array},list:{type:Array},listCopy:{type:Array}},data:function(){return{selForm:{},searchAll:"",searchList:[],balance:!0,keynum:0}},methods:{bindData:function(){this.searchList=[];for(var t=0;t<this.list.length;t++){var e=this.list[t];e.demandstatus||this.searchList.push(e)}},handleSelect:function(t){this.selForm=Object.assign({},t)},endaItem:function(t,e){var a=this;if(console.log("endaItem",t,t.to.dataset.id,e),void 0!=e||void 0!=t.to.dataset.id){var i={id:this.selForm.id,demandstatus:t.to.dataset.id};if(!e||i.demandstatus!=e.id){var s=this.statusList.findIndex((function(t){return t.id==i.demandstatus}));-1!=s&&(2==this.statusList[s].statusattr?this.$prompt("","完成说明",{confirmButtonText:"提交",cancelButtonText:"关闭",closeOnClickModal:!1,inputType:"textarea",inputPlaceholder:"请输入"}).then((function(t){var e=t.value;i.finishdes=e,paramobj.finishdate=new Date,a.updateStatus(i)})).catch((function(){a.$emit("bindData"),a.keynum+=1})):this.updateStatus(i))}}},updateStatus:function(t){var e=this;this.$request.post("/S06M02B1/update",JSON.stringify(t)).then((function(a){200==a.data.code?(e.$set(data,"demandstatus",t.demandstatus),e.$message.success("成功修改状态:"+e.getStatusName(a.data.data.demandstatus))):(e.$message.warning(a.data.msg||"操作失败"),e.bindData())}))},getStatusName:function(t){var e="";if(t){var a=this.statusList.findIndex((function(e){return e.id==t}));-1!=a&&(e=this.statusList[a].statusname)}return e},searchBtn:function(){if(""!=this.searchAll){this.searchList=[];for(var t=0;t<this.list.length;t++){var e=this.list[t];!e.appointeeid&&e.billtitle.includes(this.searchAll)&&this.searchList.push(e)}}else this.bindData()},setitemBoxli:function(t){return 1==t?"rgb(27, 154, 238)":2==t?"rgb(250, 140, 21)":3==t?"rgb(230, 36, 18)":"rgb(140, 140, 140)"},getBilltypeSty:function(t){var e={color:"#FFF",fontSize:"12px",cursor:"default",padding:"2px 4px","border-radius":"4px","margin-right":"4px"};return e.background="需求"==t?"#6a70b8":"任务"==t?"#1b9aee":"缺陷"==t?"#e62412":"#909399",e},getitemStatus:function(t){var e={color:"#000",padding:"0px 6px","border-radius":"4px",display:"inline-block","font-size":"12px","margin-right":"10px"};return"开始"==t?(e.color="#409EFF",e.border="1px solid"):"暂停"==t?(e.color="#fa8c15",e.border="1px solid"):"完成"==t&&(e.color="#14a10f",e.border="1px solid"),e},isdeaddate:function(t){if(!t)return!1;var e=new Date(t).getTime(),a=new Date(t),i=(a.getFullYear(),(a.getMonth()+1).toString().padStart(2,"0")),s=a.getDate().toString().padStart(2,"0"),n=a.getHours().toString().padStart(2,"0"),o=a.getMinutes().toString().padStart(2,"0");return(new Date).getTime()>e||(new Date).getTime(),"".concat(i,"月").concat(s,"日").concat(n,":").concat(o)},startdata:function(t){if(!t)return!1;new Date(t).getTime();var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),i=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0");return"".concat(a,"月").concat(i,"日").concat(s,":").concat(n)}}}),w=y,S=(a("d8a4"),Object(f["a"])(w,b,v,!1,null,"4a21d35c",null)),k=S.exports,j=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"laneBody"},[a("div",{staticClass:"leftCol"},t._l(t.typeList,(function(e,i){return a("div",{key:i,staticClass:"leftColItem"},[a("div",{staticClass:"leftColHeader"},[a("div",{staticClass:"leftColTitle flex j-s"},[a("span"),a("span",[t._v(" "+t._s(e.name))]),a("span")])]),a("div",{staticClass:"leftColBody"},[a("draggable",{key:t.keynum,staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px","overflow-y":"auto","margin-bottom":"8px"},attrs:{group:"aItemBox",tag:"div","data-index":i,"data-name":e.name,"data-id":e.id,dragClass:"dragClass",ghostClass:"ghostClass",animation:"500",touchStartThreshold:"50px"},on:{end:function(a){return t.endaItem(a,e)}}},[a("transition-group",{staticStyle:{height:"100%",display:"block"},attrs:{"data-index":i,"data-name":e.statusname,"data-id":e.id}},[t._l(t.list,(function(i,s){return[i.billtype==e.name?a("div",{key:s,staticClass:"itemBox-li",style:{"border-color":t.setitemBoxli(i.level)},on:{mousedown:function(e){return t.handleSelect(i)},click:function(e){return t.$emit("openHistoryFormedit",i)}}},[a("div",{staticClass:"aItemTitle",style:{color:"#333"},attrs:{title:i.billtitle}},[t._v(" "+t._s(i.billtitle)+" ")]),a("el-tag",{staticStyle:{"margin-right":"10px"},attrs:{type:["缺陷"].includes(i.billtype)?"warning":"primary",size:"mini"}},[t._v(t._s(i.billtype))]),a("div",{staticClass:"flex a-c"},t._l(i.labeljson?JSON.parse(i.labeljson):[],(function(e,i){return a("div",{key:i,staticStyle:{"margin-top":"4px"},attrs:{title:"标签"}},[a("span",{class:"label-color label-"+e.color,staticStyle:{"font-size":"12px","margin-right":"4px"}},[t._v(" "+t._s(e.name))])])})),0),t.isdeaddate(i.deaddate)?a("div",{staticClass:"deaddateSty"},[a("span",{class:(new Date).getTime()>new Date(i.deaddate).getTime()?"dateRed":new Date(i.deaddate).getTime()-(new Date).getTime()<864e5?"dateOrange":"dateBlue"},[t._v(t._s(t.isdeaddate(i.deaddate))+" 截止")])]):t._e(),a("div",{staticClass:"createbySty",style:{color:"#9e9e9e","margin-right":"4px",float:"right"},attrs:{title:"执行者"}},[a("span",{staticClass:"timestatusSty",style:t.getitemStatus(i.timestatus)},[t._v(" "+t._s(i.timestatus))]),a("i",{staticClass:"el-icon-user-solid"}),t._v(" "+t._s(i.appointee)+" ")])],1):t._e()]}))],2)],1)],1)])})),0)])},C=[],x=(a("d81d"),a("0643"),a("a573"),{components:{draggable:m.a},props:{userList:{type:Array},statusList:{type:Array},list:{type:Array},listCopy:{type:Array},billtypeArr:{type:Array}},data:function(){return{selForm:{},searchAll:"",searchList:[],balance:!0,typeList:[{name:"需求",id:1},{name:"任务",id:2},{name:"缺陷",id:3}],keynum:0}},methods:{bindData:function(){this.typeList=this.billtypeArr.map((function(t,e){return{name:t,id:e+1}})),this.searchList=[];for(var t=0;t<this.list.length;t++){var e=this.list[t];e.appointeeid||this.searchList.push(e)}},handleSelect:function(t){this.selForm=Object.assign({},t)},endaItem:function(t,e){var a=this;if(console.log("endaItem",t,t.to.dataset.id,e),void 0!=e||void 0!=t.to.dataset.id){var i={id:this.selForm.id,billtype:this.getTypeName(t.to.dataset.id)};e&&i.billtype==e.name||this.$request.post("/S06M02B1/update",JSON.stringify(i)).then((function(t){200==t.data.code?a.$message.success("成功修改类型:"+t.data.data.billtype):(a.$message.warning(t.data.msg||"操作失败"),a.bindData())}))}},getTypeName:function(t){var e="";if(t){var a=this.typeList.findIndex((function(e){return e.id==t}));-1!=a&&(e=this.typeList[a].name)}return e},searchBtn:function(){if(""!=this.searchAll){this.searchList=[];for(var t=0;t<this.list.length;t++){var e=this.list[t];!e.appointeeid&&e.billtitle.includes(this.searchAll)&&this.searchList.push(e)}}else this.bindData()},getitemStatus:function(t){var e={color:"#000",padding:"0px 6px","border-radius":"4px",display:"inline-block","font-size":"12px","margin-right":"10px"};return"开始"==t?(e.color="#409EFF",e.border="1px solid"):"暂停"==t?(e.color="#fa8c15",e.border="1px solid"):"完成"==t&&(e.color="#14a10f",e.border="1px solid"),e},setitemBoxli:function(t){return 1==t?"rgb(27, 154, 238)":2==t?"rgb(250, 140, 21)":3==t?"rgb(230, 36, 18)":"rgb(140, 140, 140)"},isdeaddate:function(t){if(!t)return!1;var e=new Date(t).getTime(),a=new Date(t),i=(a.getFullYear(),(a.getMonth()+1).toString().padStart(2,"0")),s=a.getDate().toString().padStart(2,"0"),n=a.getHours().toString().padStart(2,"0"),o=a.getMinutes().toString().padStart(2,"0");return(new Date).getTime()>e||(new Date).getTime(),"".concat(i,"月").concat(s,"日").concat(n,":").concat(o)},startdata:function(t){if(!t)return!1;new Date(t).getTime();var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),i=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0");return"".concat(a,"月").concat(i,"日").concat(s,":").concat(n)}}}),_=x,L=(a("80f01"),Object(f["a"])(_,j,C,!1,null,"c1e88132",null)),D=L.exports,T=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"laneBody"},[a("div",{staticClass:"leftCol"},t._l(t.userList,(function(e,i){return a("div",{key:i,staticClass:"leftColItem"},[a("div",{staticClass:"leftColHeader"},[a("div",{staticClass:"leftColTitle flex j-s"},[a("span"),a("span",[t._v(" "+t._s(e.engineername))]),a("span",{staticStyle:{cursor:"pointer","margin-right":"10px"},on:{click:function(a){return t.$emit("checkhistory",e)}}},[t._v("H")])])]),a("div",{staticClass:"leftColBody"},[a("draggable",{key:t.keynum,staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px","overflow-y":"auto","margin-bottom":"8px"},attrs:{group:"aItemBox",tag:"div","data-index":i,"data-name":e.engineername,"data-id":e.id,dragClass:"dragClass",ghostClass:"ghostClass",animation:"500",touchStartThreshold:"50px"},on:{end:function(a){return t.endaItem(a,e)}}},[a("transition-group",{staticStyle:{height:"100%",display:"block"},attrs:{"data-index":i,"data-name":e.engineername,"data-id":e.id}},[t._l(t.list,(function(i,s){return[i.appointeeid==e.id?a("div",{key:s,staticClass:"itemBox-li",style:{"border-color":t.setitemBoxli(i.level)},on:{mousedown:function(e){return t.handleSelect(i)},click:function(e){return t.$emit("openHistoryFormedit",i)}}},[a("div",{staticClass:"aItemTitle",style:{color:"#333"},attrs:{title:i.billtitle}},[t._v(" "+t._s(i.billtitle)+" ")]),a("div",{staticClass:"createbySty",style:{color:"#9e9e9e"}},[a("el-tag",{staticStyle:{"margin-right":"10px"},attrs:{type:["缺陷"].includes(i.billtype)?"warning":"primary",size:"mini"}},[t._v(t._s(i.billtype))]),a("i",{staticClass:"el-icon-user-solid"}),t._v(" "+t._s(i.createby)+" ")],1),t.isdeaddate(i.deaddate)?a("div",{staticClass:"deaddateSty"},[i.startdate?a("span",{staticClass:"dateBlue",staticStyle:{"margin-bottom":"4px"}},[t._v(" "+t._s(t.startdata(i.startdate))+" 开始 ")]):t._e(),a("span",{class:(new Date).getTime()>new Date(i.deaddate).getTime()?"dateRed":new Date(i.deaddate).getTime()-(new Date).getTime()<864e5?"dateOrange":"dateBlue"},[t._v(t._s(t.isdeaddate(i.deaddate))+" 截止")])]):t._e(),a("span",{staticClass:"timestatusSty",style:t.getitemStatus(i.timestatus)},[t._v(" "+t._s(i.timestatus))])]):t._e()]}))],2)],1)],1)])})),0),a("div",{staticClass:"rightCol"},[a("div",{staticClass:"rightColHeader"},[t._m(0),a("div",{staticClass:"flex a-c",staticStyle:{padding:"2px 6px"}},[a("el-input",{staticStyle:{flex:"1","margin-right":"10px"},attrs:{placeholder:"请输入内容",size:"small"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.searchBtn()}},model:{value:t.searchAll,callback:function(e){t.searchAll=e},expression:"searchAll"}},[a("el-button",{attrs:{slot:"append",icon:"el-icon-search",size:"mini"},on:{click:t.searchBtn},slot:"append"})],1),a("el-button",{staticStyle:{padding:"7px 10px"},attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:function(e){return t.openWork(0)}}})],1)]),a("div",{staticClass:"rightColBody"},[a("draggable",{key:t.keynum,staticClass:"itemBox",staticStyle:{height:"100%",padding:"5px","overflow-y":"auto","margin-bottom":"8px"},attrs:{group:"aItemBox",tag:"div",dragClass:"dragClass",ghostClass:"ghostClass",animation:"500",touchStartThreshold:"50px","data-name":"","data-id":""},on:{end:function(e){return t.endaItem(e,t.i)}}},[a("transition-group",{staticStyle:{height:"100%",display:"block"}},[t._l(t.searchList,(function(e,i){return[e.appointeeid?t._e():a("div",{key:i,staticClass:"itemBox-li",style:{"border-color":t.setitemBoxli(e.level)},on:{mousedown:function(a){return t.handleSelect(e)},click:function(a){return t.$emit("openHistoryFormedit",e)}}},[a("div",{staticClass:"aItemTitle",style:{color:"#333"},attrs:{title:e.billtitle}},[t._v(" "+t._s(e.billtitle)+" ")]),a("div",{staticClass:"createbySty",style:{color:"#9e9e9e"}},[a("i",{staticClass:"el-icon-user-solid"}),t._v(" "+t._s(e.createby)+" ")]),t.isdeaddate(e.deaddate)?a("div",{staticClass:"deaddateSty"},[e.startdate?a("span",{staticClass:"dateBlue",staticStyle:{"margin-bottom":"4px"}},[t._v(" "+t._s(t.startdata(e.startdate))+" 开始 ")]):t._e(),a("span",{class:(new Date).getTime()>new Date(e.deaddate).getTime()?"dateRed":new Date(e.deaddate).getTime()-(new Date).getTime()<864e5?"dateOrange":"dateBlue"},[t._v(t._s(t.isdeaddate(e.deaddate))+" 截止")])]):t._e()])]}))],2)],1)],1)])])},$=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"rightColTitle flex a-c j-s"},[a("span"),a("span",{staticStyle:{cursor:"default"}},[t._v("待指派")]),a("span")])}],O={components:{draggable:m.a},props:{userList:{type:Array},list:{type:Array},listCopy:{type:Array}},data:function(){return{selForm:{},searchAll:"",searchList:[],balance:!0,keynum:0}},methods:{bindData:function(){this.searchList=[];for(var t=0;t<this.list.length;t++){var e=this.list[t];e.appointeeid||this.searchList.push(e)}},handleSelect:function(t){this.selForm=Object.assign({},t)},endaItem:function(t,e){var a=this;if(console.log("endaItem",t,t.to.dataset.id,e),void 0!=e||void 0!=t.to.dataset.id){var i={id:this.selForm.id,appointeeid:t.to.dataset.id?t.to.dataset.id:"",appointee:t.to.dataset.name?t.to.dataset.name:""};e&&i.appointeeid==e.id||this.$request.post("/S06M02B1/update",JSON.stringify(i)).then((function(t){200==t.data.code?i.appointee?a.$message.success("成功修改执行人:"+i.appointee):a.$message.success("事务成功移回公海！"):(a.$message.warning(t.data.msg||"操作失败"),a.bindData())}))}},searchBtn:function(){if(""!=this.searchAll){this.searchList=[];for(var t=0;t<this.list.length;t++){var e=this.list[t];!e.appointeeid&&e.billtitle.includes(this.searchAll)&&this.searchList.push(e)}}else this.bindData()},setitemBoxli:function(t){return 1==t?"rgb(27, 154, 238)":2==t?"rgb(250, 140, 21)":3==t?"rgb(230, 36, 18)":"rgb(140, 140, 140)"},getitemStatus:function(t){var e={color:"#000",padding:" 2px 6px","border-radius":"4px",display:"inline-block","font-size":"12px"};return"开始"==t?(e.color="#409EFF",e.border="1px solid"):"暂停"==t?(e.color="#fa8c15",e.border="1px solid"):"完成"==t&&(e.color="#14a10f",e.border="1px solid"),e},isdeaddate:function(t){if(!t)return!1;var e=new Date(t).getTime(),a=new Date(t),i=(a.getFullYear(),(a.getMonth()+1).toString().padStart(2,"0")),s=a.getDate().toString().padStart(2,"0"),n=a.getHours().toString().padStart(2,"0"),o=a.getMinutes().toString().padStart(2,"0");return(new Date).getTime()>e||(new Date).getTime(),"".concat(i,"月").concat(s,"日").concat(n,":").concat(o)},startdata:function(t){if(!t)return!1;new Date(t).getTime();var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),i=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0");return"".concat(a,"月").concat(i,"日").concat(s,":").concat(n)}}},F=O,B=(a("be80"),Object(f["a"])(F,T,$,!1,null,"c556a7f6",null)),N=B.exports,A=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"calendarWrap"},[a("div",{staticClass:"engineerSty"},[a("span",{staticStyle:{"margin-right":"10px",cursor:"pointer"},attrs:{title:"单选"},on:{click:t.changeismultiple}},[t._v("工程师")]),a("el-select",{attrs:{multiple:t.ismultiple,"collapse-tags":"",clearable:"",placeholder:"请选择工程师"},on:{change:t.bindData},model:{value:t.engineer,callback:function(e){t.engineer=e},expression:"engineer"}},t._l(t.userList,(function(t){return a("el-option",{key:t.id,attrs:{label:t.engineername,value:t.id}})})),1),a("span",{staticClass:"spanTip",staticStyle:{background:"#d6f1ff",color:"#06a7ff"}},[t._v("开始")]),a("span",{staticClass:"spanTip",staticStyle:{background:"rgb(245, 236, 228)",color:"#fa8c15"}},[t._v("暂停")]),a("span",{staticClass:"spanTip",staticStyle:{background:"#bcffdd",color:"#12b161"}},[t._v("完成")]),a("span",{staticClass:"spanTip",staticStyle:{background:"#dfdfdf",color:"#6d6d6d"}},[t._v("未开始")])],1),a("FullCalendar",{ref:"funllCalendarRef",attrs:{options:t.calendarOptions},scopedSlots:t._u([{key:"eventContent",fn:function(e){return[a("div",{staticStyle:{color:"#4d4d4d",padding:"6px 0px 6px 4px",height:"100%"},attrs:{title:e.event.title+"\n执行："+e.event.extendedProps.appointee+"\n优先："+t.levelFormate(e.event.extendedProps.level)+"\n截止："+t.startdata(e.event.end)}},[a("div",{staticClass:"worktitle"},[t._v(t._s(e.event.title))]),a("i",{staticClass:"el-icon-user-solid"}),t._v(" "+t._s(e.event.extendedProps.appointee)+" "),a("el-tag",{staticStyle:{"margin-left":"4px"},attrs:{type:["缺陷"].includes(e.event.extendedProps.billtype)?"warning":"primary",size:"mini"}},[t._v(t._s(e.event.extendedProps.billtype))]),a("div")],1)]}}])})],1)},P=[],M=a("ade3"),V=a("7516"),z=a("4387"),I=a("3cdb"),H=a("ed70"),q=a("f46e"),J=a("271e"),R=(a("b893"),{components:{FullCalendar:V["a"]},props:["userList"],data:function(){var t;return{ismultiple:!0,myCalendar:null,calendarOptions:(t={plugins:[I["a"],q["a"],H["a"],J["a"]],initialView:"timeGridWeek",locale:z["a"],firstDay:1,customButtons:{next:{click:this.nextClick},prev:{click:this.prevClick},dayGridMonth:{text:"月",click:this.dayGridMonthBtn},timeGridWeek:{text:"周",click:this.timeGridWeekBtn}},headerToolbar:{left:null,center:"title",right:"prev next dayGridMonth,timeGridWeek"},weekNumberCalculation:"ISO",slotLabelFormat:"HH:mm",scrollTime:"07:00:00",eventTimeFormat:{hour:"numeric",minute:"2-digit",meridiem:!1,hour12:!1},allDaySlot:!1},Object(M["a"])(Object(M["a"])(Object(M["a"])(Object(M["a"])(Object(M["a"])(Object(M["a"])(Object(M["a"])(Object(M["a"])(Object(M["a"])(Object(M["a"])(t,"firstDay",1),"dayMaxEventRows",10),"fixedWeekCount",!1),"selectable",!0),"selectOverlap",!1),"slotEventOverlap",!1),"eventOverlap",!1),"nowIndicator",!0),"aspectRatio","2.3"),"unselectAuto",!1),Object(M["a"])(Object(M["a"])(Object(M["a"])(t,"events",[]),"eventDidMount",(function(t){})),"eventClick",this.handleClick)),engineer:[this.$store.state.user.userinfo.engineer.id],selRowId:"",list:[]}},mounted:function(){this.myCalendar=this.$refs.funllCalendarRef.getApi()},methods:{bindData:function(){var t=this;return Object(o["a"])(Object(n["a"])().mark((function e(){var a,i,s,o,l,r,c;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.list=[],e.prev=1,a={PageNum:1,PageSize:1e4,OrderType:1,SearchType:1,scenedata:[]},i="/S06M02B2/getPageList",i+="?own="+(t.own?1:0),e.next=7,t.$request.post(i,JSON.stringify(a));case 7:if(s=e.sent,200==s.data.code){for(t.list=s.data.data.list,o=[],l=0;l<t.list.length;l++)r=t.list[l],c={id:r.id,title:r.billtitle,billtype:r.billtype,createby:r.createby,appointee:r.appointee,appointeeid:r.appointeeid,finishmark:r.finishmark,level:r.level,projectid:r.projectid,projname:r.projname,timestatus:r.timestatus,end:r.deaddate?new Date(r.deaddate):"",start:r.startdate?new Date(r.startdate):"",className:"开始"==r.timestatus?"borderBlue":"完成"==r.timestatus?"borderGreen":"暂停"==r.timestatus?"borderOrange":"borderGray",color:"开始"==r.timestatus?"#D6F1FF":"完成"==r.timestatus?"#bcffdd":"暂停"==r.timestatus?"#f5ece4":"#dfdfdf"},t.engineer.includes(r.appointeeid)&&o.push(c);t.calendarOptions.events=o}else t.$message.warning(s.data.msg||"查询需求失败");e.next=14;break;case 11:e.prev=11,e.t0=e["catch"](1),t.$message.warning(e.t0);case 14:case"end":return e.stop()}}),e,null,[[1,11]])})))()},changeismultiple:function(){this.ismultiple=!this.ismultiple,this.engineer=null,this.ismultiple?this.engineer=[this.$store.state.user.userinfo.engineer.id]:this.engineer=this.$store.state.user.userinfo.engineer.id},levelFormate:function(t){if(!t)return"较低";var e=["较低","普通","紧急","非常紧急"];return e[t]},startdata:function(t){if(!t)return!1;new Date(t).getTime();var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0"),e.getDate().toString().padStart(2,"0"),e.getHours().toString().padStart(2,"0")),i=e.getMinutes().toString().padStart(2,"0");return"".concat(a,":").concat(i)},handleClick:function(t){var e=this;console.log(t),this.selRowId=t.event.id;var a=this.list.findIndex((function(t){return t.id==e.selRowId}));console.log(a),-1!=a&&this.$emit("openHistoryFormedit",this.list[a])},nextClick:function(){this.myCalendar.next(),this.bindData()},prevClick:function(){this.myCalendar.prev(),this.bindData()},timeGridWeekBtn:function(){this.myCalendar.changeView("timeGridWeek"),this.bindData()},dayGridMonthBtn:function(){this.myCalendar.changeView("dayGridMonth"),this.bindData()}}}),E=R,U=(a("aee0"),Object(f["a"])(E,A,P,!1,null,"03af707f",null)),K=U.exports,G=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("ve-table",{key:t.keynum,ref:"tableList",style:{"word-break":"break-all"},attrs:{rowKeyFieldName:"id","max-height":t.tableMaxHeight,"scroll-width":t.tableMinWidth,"is-horizontal-resize":"","fixed-header":!0,columns:t.customData,"table-data":t.list,"border-x":"","border-y":"","border-around":!0,columnHiddenOption:{defaultHiddenColumnKeys:t.columnHidden},"column-width-resize-option":{enable:!0,minWidth:50},"virtual-scroll-option":t.virtualScrollOption,"checkbox-option":t.checkboxOption,"footer-data":t.footerData,"fixed-footer":!0,"event-custom-option":t.eventCustomOption,"sort-option":t.sortOption}}),a("div",{staticClass:"footPaginationSty"},[t._v("共 "+t._s(t.list.length)+" 条")])],1)},W=[],Y=(a("a9e3"),a("c7cd"),a("4e3e"),a("159b"),{formcode:"S06M02B4List",item:[{itemcode:"itemname",itemname:"项目名称",minwidth:"120",displaymark:1,overflow:1,datasheet:"Sa_Demand.itemname"},{itemcode:"groupname",itemname:"客户",minwidth:"120",displaymark:1,overflow:1,datasheet:"Sa_Demand.groupname"},{itemcode:"billtitle",itemname:"标题",minwidth:"180",displaymark:1,overflow:1,datasheet:"Sa_Demand.billtitle"},{itemcode:"billtype",itemname:"类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Demand.billtype"},{itemcode:"remark",itemname:"内容描述",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.remark"},{itemcode:"demandstatus",itemname:"状态",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.demandstatus"},{itemcode:"level",itemname:"优先级",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.state"},{itemcode:"appointee",itemname:"执行者",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.appointee"},{itemcode:"collaborators",itemname:"协作者",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.collaborators"},{itemcode:"labeljson",itemname:"标签",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.labeljson"},{itemcode:"timestatus",itemname:"工时状态",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.timestatus"},Object(M["a"])(Object(M["a"])({itemcode:"startdate",itemname:"开始日期",minwidth:"150",sortable:0,displaymark:1,overflow:1},"sortable",1),"datasheet","Sa_Demand.startdate"),Object(M["a"])(Object(M["a"])({itemcode:"deaddate",itemname:"截止日期",minwidth:"150",sortable:0,displaymark:1,overflow:1},"sortable",1),"datasheet","Sa_Demand.deaddate"),Object(M["a"])(Object(M["a"])({itemcode:"finishdate",itemname:"完成日期",minwidth:"150",sortable:0,displaymark:1,overflow:1},"sortable",1),"datasheet","Sa_Demand.finishdate"),{itemcode:"lister",itemname:"制表",minwidth:"70",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.lister"},{itemcode:"operate",itemname:"操作",minwidth:"150",fixed:"right",sortable:0,displaymark:1,overflow:1}]}),Q={formcode:"S06M02B4History",item:[{itemcode:"itemname",itemname:"项目名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Demand.itemname"},{itemcode:"billtitle",itemname:"标题",minwidth:"180",displaymark:1,overflow:1,datasheet:"Sa_Demand.billtitle"},{itemcode:"billtype",itemname:"类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Demand.billtype"},{itemcode:"remark",itemname:"内容描述",minwidth:"150",sortable:0,displaymark:1,overflow:0,datasheet:"Sa_Demand.remark"},{itemcode:"demandstatus",itemname:"状态",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.demandstatus"},{itemcode:"level",itemname:"优先级",minwidth:"70",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.state"},{itemcode:"appointee",itemname:"执行者",minwidth:"80",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.appointee"},{itemcode:"collaborators",itemname:"协作者",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.collaborators"},{itemcode:"labeljson",itemname:"标签",minwidth:"150",sortable:0,displaymark:1,overflow:1,datasheet:"Sa_Demand.labeljson"},Object(M["a"])(Object(M["a"])({itemcode:"finishdate",itemname:"完成日期",minwidth:"150",sortable:0,displaymark:1,overflow:1},"sortable",1),"datasheet","Sa_Demand.finishdate"),{itemcode:"finishdes",itemname:"完成说明",minwidth:"150",sortable:0,displaymark:1,overflow:0,datasheet:"Sa_Demand.finishdes"}]},X={props:{userList:{type:Array},statusList:{type:Array,default:[]},list:{type:Array},listCopy:{type:Array}},data:function(){var t=this;return{keynum:0,tableForm:Y,customData:[],columnHidden:[],footerData:[],rowScroll:0,virtualScrollOption:{enable:!0,scrolling:function(e){var a=e.startRowIndex;t.rowScroll=a}},checkboxOption:{selectedRowChange:function(e){e.row,e.isSelected;var a=e.selectedRowKeys;if(t.selectList=[],0!=a.length)for(var i=0;i<a.length;i++)t.selectList.push({id:a[i]})},selectedAllChange:function(e){var a=e.isSelected;e.selectedRowKeys;t.selectList=a?t.list:[]}},eventCustomOption:{bodyCellEvents:function(t){t.row,t.column,t.rowIndx;return{mouseup:function(t){}}}},sortOption:{sortChange:function(e){t.changeSort(e)}}}},computed:{tableMaxHeight:function(){var t=window.innerHeight-160;return t<600&&(t=600),t+"px"},tableMinWidth:function(){var t="calc(100vw - 64px)";if(0!=this.tableForm.item.length){t=0;for(var e=0;e<this.tableForm.item.length;e++){var a=this.tableForm.item[e];a.displaymark&&(t+=Number(a.minwidth))}}return t}},mounted:function(){},methods:{getColumn:function(){var t=this;return Object(o["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.initTable(t.tableForm);case 1:case"end":return e.stop()}}),e)})))()},initTable:function(t){var e=this,a=(this.$createElement,[]);this.columnHidden=[],t["item"].forEach((function(t,i){var s={field:t.itemcode,key:t.itemcode,title:t.itemname,width:isNaN(t.minwidth)?t.minwidth:Number(t.minwidth),displaymark:t.displaymark,fixed:!!t.fixed&&(1==t.fixed?"left":"right"),ellipsis:!!t.overflow&&{showTitle:!0},align:t.aligntype?t.aligntype:"center",sortBy:!!t.sortable&&"",renderBodyCell:function(a,i){var s=a.row,n=(a.column,a.rowIndex,"");if("billdate"==t.itemcode||"plandate"==t.itemcode)return e.$options.filters.dateFormat(s[t.itemcode]);if("billtitle"==t.itemcode)return n=i("el-button",{attrs:{type:"text",size:"small",title:s[t.itemcode]?s[t.itemcode]:"需求标题"},style:"padding: 4px 15px",on:{click:function(){return e.$emit("openHistoryFormedit",s)}}},[s[t.itemcode]?s[t.itemcode]:"需求标题"]),n;if("planstart"==t.itemcode||"planend"==t.itemcode||"actualstart"==t.itemcode||"completiondate"==t.itemcode)return e.$options.filters.dateFormat(s[t.itemcode]);if("level"==t.itemcode)return 0==s[t.itemcode]?n=i("span",{class:"levelTag taggray"},["较低"]):1==s[t.itemcode]?n=i("span",{class:"levelTag tagblue"},["普通"]):2==s[t.itemcode]?n=i("span",{class:"levelTag tagorange"},["紧急"]):3==s[t.itemcode]&&(n=i("span",{class:"levelTag tagred"},["非常紧急"])),n;if("timestatus"==t.itemcode)return"开始"==s[t.itemcode]?n=i("span",{class:"timestatusSty",style:"color: #409EFF;"},["开始"]):"暂停"==s[t.itemcode]?n=i("span",{class:"timestatusSty",style:"color:#fa8c15;border 1px solid"},["暂停"]):"完成"==s[t.itemcode]&&(n=i("span",{class:"timestatusSty",style:"color:#14a10f;border 1px solid"},["完成"])),n;if("demandstatus"==t.itemcode)return n=i("span",{style:"background:transparent",class:"label-"+e.getStatusName(s[t.itemcode])["statuscolor"]},[e.getStatusName(s[t.itemcode])["statusname"]]),n;if("labeljson"==t.itemcode){n="";var o=s[t.itemcode]?JSON.parse(s[t.itemcode]):[];return o.length&&(n=i("div",[i("div",o.map((function(t){var e=t.name,a=t.color;return i("span",{class:"label-color label-"+a},[e])})))])),n}if("operate"==t.itemcode){var l="";return s.timestatus&&"暂停"!=s.timestatus?"开始"==s.timestatus&&(l=i("el-button",{attrs:{type:"text",icon:"el-icon-video-pause"},style:"color:#fa8c15",on:{click:function(){return e.changeTimeStatus(s,"暂停")}}},["暂停"])):l=i("el-button",{attrs:{type:"text",icon:"el-icon-video-play"},on:{click:function(){return e.changeTimeStatus(s,"开始")}}},["开始"]),n=i("div",[l,i("el-button",{directives:[{name:"show",value:"开始"==s.timestatus}],attrs:{type:"text",icon:"el-icon-finished"},style:"color:#14a10f",on:{click:function(){return e.changeTimeStatus(s,"完成")}}},["完成"])]),n}return s[t.itemcode]}};t.displaymark||e.columnHidden.push(t.itemcode),a.push(s)})),a.unshift({field:"index",key:"index",title:"ID",width:50,align:"center",fixed:"left",renderBodyCell:function(t,a){t.row,t.column;var i=t.rowIndex;return i+e.rowScroll+1}}),this.customData=a,this.keynum+=1},stringToHTML:function(t){var e=new DOMParser,a=e.parseFromString(t,"text/html");return a.body.firstChild},changeSort:function(t){for(var e in t)if(""!=t[e]){var a={prop:e};"desc"==t[e]?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tableSort(a,this.tableForm),this.bindData();break}},getStatusName:function(t){var e={statusname:"",statuscolor:"grey"};if(t){var a=this.statusList.findIndex((function(e){return e.id==t}));-1!=a&&(e.statusname=this.statusList[a].statusname,e.statuscolor=this.statusList[a].statuscolor)}return e},changeTimeStatus:function(t,e){this.$emit("changeTimeStatus",t,e)}}},Z=X,tt=(a("6172"),Object(f["a"])(Z,G,W,!1,null,"0d6ccdaa",null)),et=tt.exports,at=a("a116"),it=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("el-dialog",{attrs:{"close-on-click-modal":!1,title:"历史需求",top:"1vh",visible:t.shows,width:"88vw","append-to-body":""},on:{"update:visible":function(e){t.shows=e}}},[a("div",{staticClass:"flex j-s",staticStyle:{height:"34px"}},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px"},attrs:{placeholder:"标题查询","prefix-icon":"el-icon-search",size:"small",clearable:""},on:{clear:function(e){return t.btnsearch(t.strfilter)}},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(t.strfilter)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"small"},on:{click:function(e){return t.btnsearch(t.strfilter)}},slot:"append"},[t._v("搜索 ")])],1)],1),a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"我的",size:"mini",border:""},on:{change:t.changeIsMy},model:{value:t.ismy,callback:function(e){t.ismy=e},expression:"ismy"}})],1),a("div",{staticClass:"itemBox-Table",staticStyle:{height:"100%"}},[a("el-table",{ref:"tableList",staticStyle:{overflow:"auto"},attrs:{data:t.list,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"",height:"calc(100vh - 200px)","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["billdate"==e.itemcode||"receivetime"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):"billtitle"==e.itemcode?a("div",[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.openFormedit(i.row)}}},[t._v(t._s(i.row[e.itemcode]?i.row[e.itemcode]:"需求标题"))])],1):"level"==e.itemcode?a("div",[0==i.row[e.itemcode]?a("span",{staticClass:"levelTag"},[t._v("较低")]):t._e(),1==i.row[e.itemcode]?a("span",{staticClass:"levelTag tagblue"},[t._v("普通")]):t._e(),2==i.row[e.itemcode]?a("span",{staticClass:"levelTag tagorange"},[t._v("紧急")]):t._e(),3==i.row[e.itemcode]?a("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")]):t._e()]):"startdate"==e.itemcode||"deaddate"==e.itemcode||"finishdate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormats")(i.row[e.itemcode])))]):"demandstatus"==e.itemcode?a("div",[a("span",{class:"label-"+t.getStatusName(i.row[e.itemcode])["statuscolor"],staticStyle:{background:"transparent"}},[t._v(" "+t._s(t.getStatusName(i.row[e.itemcode])["statusname"])+" ")])]):"labeljson"==e.itemcode?a("div",t._l(i.row[e.itemcode]?JSON.parse(i.row[e.itemcode]):[],(function(e,i){return a("div",{key:i},[a("span",{class:"label-color label-"+e.color},[t._v(t._s(e.name))])])})),0):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.getList}})],1)],1)])},st=[],nt=(a("a434"),{props:{show:Boolean,data:{type:Object},userList:{type:Array},statusList:{type:Array,default:[]}},data:function(){return{engineerForm:{},list:[],tableForm:Q,total:0,queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1,scenedata:[]},ismy:!0,strfilter:""}},computed:{shows:{get:function(){return this.show},set:function(t){this.$emit("update:show",t)}}},mounted:function(){var t=this;return Object(o["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,t.bindData();case 2:case"end":return e.stop()}}),e)})))()},methods:{bindData:function(){var t=this;if(this.engineerForm=Object.assign({},this.data),console.log("this.ismy",this.ismy),this.ismy){var e={field:"appointeeid",fieldtype:1,math:"equal",value:this.engineerForm.id};this.queryParams.scenedata.push(e)}else{var a=this.queryParams.scenedata.findIndex((function(t){return"appointeeid"==t.field}));-1!=a&&this.queryParams.scenedata.splice(a,1)}var i="/S06M02B1/getPageList";this.$request.post(i,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.list=e.data.data.list,t.total=e.data.data.total):t.$message.warning(e.data.msg||"查询失败")}))},changeIsMy:function(t){var e=this;this.ismy=t,this.$nextTick((function(){e.bindData()}))},getList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},btnsearch:function(){""!=this.strfilter?this.queryParams.scenedata=[{field:"Sa_Demand.billtitle",fieldtype:1,math:"ilike",value:this.strfilter}]:this.queryParams.scenedata=[],this.bindData()},openFormedit:function(t){this.$emit("openHistoryFormedit",t)},setitemBoxli:function(t){return 1==t?"rgb(27, 154, 238)":2==t?"rgb(250, 140, 21)":3==t?"rgb(230, 36, 18)":"rgb(140, 140, 140)"},isdeaddate:function(t){if(!t)return!1;var e=new Date(t).getTime(),a=new Date(t),i=(a.getFullYear(),(a.getMonth()+1).toString().padStart(2,"0")),s=a.getDate().toString().padStart(2,"0"),n=a.getHours().toString().padStart(2,"0"),o=a.getMinutes().toString().padStart(2,"0");return(new Date).getTime()>e||(new Date).getTime(),"".concat(i,"月").concat(s,"日").concat(n,":").concat(o)},startdata:function(t){if(!t)return!1;new Date(t).getTime();var e=new Date(t),a=(e.getFullYear(),(e.getMonth()+1).toString().padStart(2,"0")),i=e.getDate().toString().padStart(2,"0"),s=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0");return"".concat(a,"月").concat(i,"日").concat(s,":").concat(n)},getStatusName:function(t){var e={statusname:"",statuscolor:"grey"};if(t){var a=this.statusList.findIndex((function(e){return e.id==t}));-1!=a&&(e.statusname=this.statusList[a].statusname,e.statuscolor=this.statusList[a].statuscolor)}return e}}}),ot=nt,lt=(a("8b20"),Object(f["a"])(ot,it,st,!1,null,"15a1aeb3",null)),rt=lt.exports,ct=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container flex j-s"},[a("div",[a("el-input",{staticClass:"filter-item",staticStyle:{width:"260px",height:"100%"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.btnsearch(e)}},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{type:"primary",size:"mini",icon:"el-icon-plus"}},[t._v(" 新建 "),a("i",{staticClass:"el-icon-arrow-down"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},t._l(t.billtypeArr,(function(e,i){return a("el-dropdown-item",{key:i,nativeOn:{click:function(a){return t.$emit("btnadd",e)}}},[t._v(t._s(e))])})),1)],1)],1),a("div",{staticClass:"iShowBtn"},[a("el-select",{staticStyle:{width:"100px","margin-right":"10px"},attrs:{placeholder:"状态",size:"small"},on:{change:function(e){return t.$emit("changeBillType",e)}},model:{value:t.billtype,callback:function(e){t.billtype=e},expression:"billtype"}},[a("el-option",{attrs:{label:"全部",value:""}}),t._l(t.billtypeArr,(function(t,e){return a("el-option",{key:e,attrs:{label:t,value:t}})}))],2),a("el-checkbox",{staticStyle:{"margin-right":"10px","line-height":"20px"},attrs:{label:"我的",size:"mini",border:""},on:{change:t.changeIsOwn},model:{value:t.isOwn,callback:function(e){t.isOwn=e},expression:"isOwn"}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{"hide-on-click":!1,trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"mini"}},[t._v(" "+t._s(t.showType)+" "),a("i",{staticClass:"el-icon-arrow-down"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{class:"表格"==t.showType?"isChosed":"",attrs:{icon:"el-icon-s-grid"},nativeOn:{click:function(e){return t.changeShowType("表格")}}},[t._v("表格")]),a("el-dropdown-item",{attrs:{icon:"el-icon-data-analysis"}},[a("el-dropdown",{attrs:{trigger:"click",placement:"left-start"}},[a("span",[t._v("看板 "),a("i",{staticClass:"el-icon-arrow-right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{class:"成员看板"==t.showType?"isChosed":"",nativeOn:{click:function(e){return t.changeShowType("成员看板")}}},[t._v("成员看板")]),a("el-dropdown-item",{class:"状态看板"==t.showType?"isChosed":"",nativeOn:{click:function(e){return t.changeShowType("状态看板")}}},[t._v("状态看板")]),a("el-dropdown-item",{class:"类型看板"==t.showType?"isChosed":"",nativeOn:{click:function(e){return t.changeShowType("类型看板")}}},[t._v("类型看板")]),a("el-dropdown-item",{class:"标签看板"==t.showType?"isChosed":"",nativeOn:{click:function(e){return t.changeShowType("标签看板")}}},[t._v("标签看板")]),a("el-dropdown-item",{class:"日历看板"==t.showType?"isChosed":"",nativeOn:{click:function(e){return t.changeShowType("日历看板")}}},[t._v("日历看板")])],1)],1)],1)],1)],1),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",plain:"",size:"mini"},on:{click:function(e){return t.$emit("historyBtn")}}},[t._v(" 历史 ")])],1)])])},dt=[],mt={name:"Listheader",props:["tableForm","userList","statusList","projectAllList","billtypeArr"],components:{},data:function(){return{strfilter:"",iShow:!1,formdata:{},thorList:!0,showType:"成员看板",isOwn:!1,billtype:""}},methods:{changeShowType:function(t){this.showType=t,this.$emit("changeShowType",t)},AdvancedSearch:function(){this.iShow=!1,this.$emit("AdvancedSearch",this.formdata)},btnadd:function(){this.$emit("btnadd")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},bindData:function(){this.$emit("bindData")},changeModelUrl:function(){this.$emit("changeModelUrl",this.thorList)},btnExport:function(){this.$emit("btnExport")},downloadGitLab:function(){var t=this;this.$confirm(" 是否确认重新GitLab同步需求?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.initGitLab()})).catch((function(){}))},initGitLab:function(){var t=this,e="/GitLab/syncDemandFromGitLabByLogin";this.$request.get(e).then((function(e){200==e.data.code?t.$message.success("同步成功"):t.$message.warning(e.data.msg||"同步失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},changeIsOwn:function(t){this.isOwn=t,this.$emit("changeIsOwn",t)}}},pt=mt,ut=(a("b724"),Object(f["a"])(pt,ct,dt,!1,null,"1390a242",null)),ft=ut.exports,ht=a("6e25"),gt={components:{KanBanView:N,LabelKanBanView:g,StatusKanBanView:k,TypeKanBanView:D,FullCalendarView:K,ListHeader:ft,FormEdit:at["a"],HistoryView:rt,TableList:et,picker:ht["default"]},data:function(){return{list:[],listCopy:[],userList:[],statusList:[],labelList:[],billtypeArr:["需求","任务","测试","缺陷","实施"],queryParams:{PageNum:1,PageSize:1e4,OrderType:1,SearchType:1,scenedata:[]},selForm:{},formeditVisible:!1,workdata:{billtype:"需求",billtitle:"",remark:"",itemname:"",startdate:"",deaddate:""},workVisible:!1,projectAllList:[],projectAllListCopy:[],projectVal:"",historyopen:{show:!1,data:null},showType:"成员看板",own:!1,formeditKey:0}},mounted:function(){this.getInfoList(),this.getprojectAllList(),this.bindData()},methods:{bindData:function(){var t=this,e="/S06M02B2/getOnlinePageList";e+="?own="+(this.own?1:0),this.$request.post(e,JSON.stringify(this.queryParams)).then((function(e){200==e.data.code?(t.list=e.data.data.list,t.listCopy=Object(l["a"])(e.data.data.list),t.$nextTick((function(){t.changeShowType(t.showType)}))):t.$message.warning(e.data.msg||"查询失败")}))},changeBillType:function(t){var e=this;console.log(t),this.queryParams.scenedata=t?[{field:"Sa_Demand.billtype",fieldtype:0,math:"equal",value:t}]:[],this.$nextTick((function(){e.bindData()}))},getInfoList:function(){var t=this;return Object(o["a"])(Object(n["a"])().mark((function e(){var a,i;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:a={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1,OrderBy:"rownum"},i="/S06M02S2/getOnlinePageList",t.$request.post(i,JSON.stringify(a)).then((function(e){200==e.data.code&&(t.userList=[].concat(e.data.data.list),console.log(t.userList,"userList"))})),t.$request.post("/S06M02S3/getPageList",JSON.stringify(a)).then((function(e){200==e.data.code&&(t.statusList=[].concat(e.data.data.list),console.log(t.statusList,"statusList"))})),t.$request.post("/S06M02S6/getPageList",JSON.stringify(a)).then((function(e){200==e.data.code&&(t.labelList=[].concat(e.data.data.list),console.log(t.labelList,"labelList"))}));case 5:case"end":return e.stop()}}),e)})))()},getprojectAllList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};this.$request.post("/S06M01S1/getBillList?own=true",JSON.stringify(e)).then((function(e){200==e.data.code?(t.projectAllList=e.data.data.list,t.projectAllListCopy=[].concat(t.projectAllList)):t.$message.warning(e.data.msg||"查询项目信息失败")}))},changeShowType:function(t){this.showType=t,"表格"==t?(this.$refs.tableList.keynum+=1,this.$refs.tableList.getColumn()):"状态看板"==t?(this.$refs.StatusKanBanView.keynum+=1,this.$refs.StatusKanBanView.bindData()):"类型看板"==t?(this.$refs.TypeKanBanView.keynum+=1,this.$refs.TypeKanBanView.bindData()):"成员看板"==t?(this.$refs.KanBanView.keynum+=1,this.$refs.KanBanView.bindData()):"标签看板"==t?(this.$refs.LabelKanBanView.keynum+=1,this.$refs.LabelKanBanView.bindData()):"日历看板"==t&&this.$refs.FullCalendarView.bindData()},search:function(t){""!=t?this.queryParams.scenedata=[{field:"Sa_Demand.billtitle",fieldtype:0,math:"ilike",value:t}]:this.$delete(this.queryParams,"scenedata"),this.bindData()},changeIsOwn:function(t){this.own=t,this.bindData()},openWork:function(t,e){this.workdata={billtype:e,billtitle:"",billdate:new Date,remark:"",level:1,status:null,projectid:null,itemcode:null,itemname:null,appointeeid:t?this.$store.state.user.userinfo.engineer.id:"",appointee:t?this.$store.state.user.userinfo.engineer.engineername:""},this.workVisible=!0},submitWorkUpdate:function(){var t=this,e=Object.assign({},this.workdata);this.$request.post("/S06M02B1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(t.workdata.billtype+"创建成功"),t.workVisible=!1,t.bindData()):t.$message.warning(e.data.msg||t.workdata.billtype+"创建失败")}))},changeProject:function(t){this.$set(this.workdata,"projectid",t.id),this.$set(this.workdata,"itemname",t.projname),this.$set(this.workdata,"itemcode",t.projcode)},searchProject:function(){if(this.projectVal){this.projectAllListCopy=[];for(var t=0;t<this.projectAllList.length;t++){var e=this.projectAllList[t];e.projname.includes(this.projectVal)&&this.projectAllListCopy.push(e)}}else this.projectAllListCopy=[].concat(this.projectAllList)},checkhistory:function(t){this.historyopen={show:!0,data:t}},historyBtn:function(){this.historyopen={show:!0,data:this.$store.state.user.userinfo.engineer}},deleteForm:function(){var t=this;this.$confirm("是否确定删除【"+this.selForm.billtitle+"】，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.$request.get("/S06M02B1/delete?key=".concat(t.selForm.id)).then((function(e){200==e.data.code?(t.$message.success("删除成功"),t.bindData(),t.formeditVisible=!1):t.$message.warning(res.data.msg||"删除失败")})).catch((function(e){t.$message.warning(e||"删除失败")}))})).catch((function(){}))},openFormedit:function(){var t=this;this.formeditVisible=!0,this.$nextTick((function(){t.$refs.formedit.projectAllList=t.projectAllList,t.$refs.formedit.projectAllListCopy=t.projectAllListCopy}))},openHistoryFormedit:function(t){this.selForm=Object.assign({},t),this.openFormedit()},changeTimeStatus:function(t,e){var a=this;"完成"==e?this.$confirm("是否确定完成该任务，该操作不可逆，请谨慎操作?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){a.updateFun(t,e)})).catch((function(){})):this.updateFun(t,e)},updateFun:function(t,e){var a=this,i="/S06M02S7/start";"暂停"==e?i="/S06M02S7/pause":"完成"==e&&(i="/S06M02S7/complete"),this.$request.get(i+"?key="+t.id).then((function(t){200==t.data.code?(a.$message.success(t.data.msg||"操作成功，该任务已"+e),a.$set(a.selForm,"timestatus",e),a.bindData(),a.formeditKey+=1):a.$message.warning(t.data.msg||"操作失败")}))}}},bt=gt,vt=Object(f["a"])(bt,i,s,!1,null,"675c7c60",null);e["default"]=vt.exports},8026:function(t,e,a){},"80f01":function(t,e,a){"use strict";a("8c92")},"85c4f":function(t,e,a){},"8b20":function(t,e,a){"use strict";a("85c4f")},"8c92":function(t,e,a){},a116:function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"wrap"},[i("div",{staticClass:"left"},[i("div",{staticClass:"left-item"},[i("el-input",{attrs:{readonly:"已完成"==t.status.statustype,placeholder:"请输入任务标题",clearable:""},on:{change:function(e){return t.changeMsg("任务标题")}},model:{value:t.formdata.billtitle,callback:function(e){t.$set(t.formdata,"billtitle",e)},expression:"formdata.billtitle"}})],1),i("div",{staticClass:"left-item"},[i("el-input",{attrs:{placeholder:"请输入任务描述",clearable:"",type:"textarea",autosize:{minRows:4,maxRows:6}},on:{change:function(e){return t.changeMsg("任务描述")}},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1),i("div",{staticClass:"left-item"},[t._m(0),i("el-popover",{ref:"billTypeRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-selectTag"}},[i("div",{ref:"selectTag",staticClass:"levelTag-content"},t._l(t.billtypeArr,(function(e,a){return i("div",{key:a,staticClass:"levelTag-content-item",on:{click:function(a){return t.changeBilltype(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.billtype==e?1:0}}),i("span",[t._v(t._s(e))])])})),0),i("div",{attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.billtype?t.formdata.billtype:"待选择"))])])])],1),i("div",{staticClass:"left-item"},[t._m(1),i("div",[i("el-popover",{ref:"statusRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[t.formdata.demandstatus?t._e():i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeStatus({id:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.demandstatus?0:1,color:"#1b9aee"}}),i("span",[t._v("待处理")])]),t._l(t.statusList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeStatus(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.demandstatus==e.id?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.statusname))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.demandstatus?i("div",[i("span",[t._v(t._s(this.getStatusName(t.formdata.demandstatus)))])]):i("div",[t._v("待处理")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(2),i("div",[i("el-popover",{ref:"appointeeRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeAppointee({id:"",engineername:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.appointee?0:1,color:"#1b9aee"}}),i("span",[t._v("待认领")])]),t._l(t.userList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeAppointee(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.appointeeid==e.id?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.engineername))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.appointeeid?i("span",{staticClass:"appointeeTag"},[t._v(" "+t._s(t.formdata.appointee)+" "),i("i",{staticClass:"CloseBtnSty el-icon-close",on:{click:function(e){return e.stopPropagation(),t.changeAppointee({id:"",engineername:""})}}})]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("待认领")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(3),i("div",[i("el-popover",{ref:"collaboratorRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-appointee"}},[i("div",{staticClass:"appointee-item",on:{click:function(e){return t.changeCollaborator({id:"",engineername:""})}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.collaboratorids?0:1,color:"#1b9aee"}}),i("span",[t._v("待添加")])]),t._l(t.userList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"appointee-item",on:{click:function(a){return t.changeCollaborator(e)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.collaboratorids&&t.formdata.collaboratorids.includes(e.id)?1:0,color:"#1b9aee"}}),i("span",[t._v(t._s(e.engineername))])])])})),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.collaboratorids?i("div",[i("span",{staticClass:"collaboratorsTag"},[t._v(t._s(t.formdata.collaborators)+" "),i("i",{staticClass:"CloseBtnSty el-icon-close",on:{click:function(e){return e.stopPropagation(),t.changeCollaborator({id:"",engineername:""})}}})])]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("待添加")])])],2)],1)]),i("div",{staticClass:"left-item"},[t._m(4),i("el-popover",{ref:"labeljsonRef",attrs:{placement:"right-end",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"250px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.groupVal,expression:"groupVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索客户"},domProps:{value:t.groupVal},on:{input:[function(e){e.target.composing||(t.groupVal=e.target.value)},function(e){return t.getGroupAllList(t.groupVal)}]}})]),t.groupAllList.length?i("div",{staticStyle:{height:"220px","overflow-y":"auto"}},t._l(t.groupAllList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeGroup(e)}}},[i("span",{class:"label-color"},[t._v(t._s(e.groupname))]),i("div",[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.groupid==e.id?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无客户","image-size":90}})],1)])]),i("div",{staticClass:"flex a-c",attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.groupname?t.formdata.groupname:"暂无客户"))]),t.formdata.groupname?i("div",{staticClass:"CloseBtnSty",on:{click:function(e){return e.stopPropagation(),t.changeGroup({id:""})}}},[i("i",{staticClass:"el-icon-close"})]):t._e()])])],1),i("div",{staticClass:"left-item"},[t._m(5),i("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择开始时间","default-time":"08:30:00",clearable:!0,editable:!1},on:{clear:function(e){t.formdata.startdate=""},input:function(e){t.formdata.startdate=e},change:function(e){return t.changeDate("开始时间")}},model:{value:t.formdata.startdate,callback:function(e){t.$set(t.formdata,"startdate",e)},expression:"formdata.startdate"}})],1),i("div",{staticClass:"left-item"},[t._m(6),i("picker",{attrs:{disabled:!1,format:"yyyy-MM-dd HH:mm",type:"datetime",placeholder:"选择截止时间","default-time":"17:00:00",clearable:!0,editable:!1},on:{clear:function(e){t.formdata.deaddate=""},input:function(e){t.formdata.deaddate=e},change:function(e){return t.changeDate("截止时间")}},model:{value:t.formdata.deaddate,callback:function(e){t.$set(t.formdata,"deaddate",e)},expression:"formdata.deaddate"}})],1),i("div",{staticClass:"left-item"},[t._m(7),i("el-popover",{ref:"labeljsonRef",attrs:{placement:"right-end",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"250px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:!0,expression:"true"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.projectVal,expression:"projectVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索项目"},domProps:{value:t.projectVal},on:{input:[function(e){e.target.composing||(t.projectVal=e.target.value)},t.searchProject]}})]),t.projectAllListCopy.length?i("div",{staticStyle:{height:"220px","overflow-y":"auto"}},t._l(t.projectAllListCopy,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeProject(e)}}},[i("span",{class:"label-color"},[t._v(t._s(e.projname))]),i("div",[i("i",{staticClass:"el-icon-check",style:{opacity:t.formdata.projectid==e.id?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无项目","image-size":90}})],1)])]),i("div",{staticClass:"flex a-c",attrs:{slot:"reference"},slot:"reference"},[i("p",[t._v(t._s(t.formdata.projname?t.formdata.projname:"暂无项目"))]),t.formdata.projname?i("div",{staticClass:"CloseBtnSty",on:{click:function(e){return e.stopPropagation(),t.changeProject({id:""})}}},[i("i",{staticClass:"el-icon-close"})]):t._e()])])],1),i("div",{staticClass:"left-item"},[t._m(8),i("div",[i("el-popover",{ref:"selectTagRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-selectTag"}},[i("div",{ref:"selectTag",staticClass:"levelTag-content"},[i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(0)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:0==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag taggray"},[t._v("较低")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(1)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:1==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagblue"},[t._v("普通")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(2)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:2==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagorange"},[t._v("紧急")])]),i("div",{staticClass:"levelTag-content-item",on:{click:function(e){return t.changelevel(3)}}},[i("i",{staticClass:"el-icon-check",style:{opacity:3==t.formdata.level?1:0}}),i("span",{staticClass:"levelTag tagred"},[t._v("非常紧急")])])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[0==t.formdata.level?i("div",{staticClass:"levelTag taggray"},[t._v(" 较低 ")]):1==t.formdata.level?i("div",{staticClass:"levelTag tagblue"},[t._v(" 普通 ")]):2==t.formdata.level?i("div",{staticClass:"levelTag tagorange"},[t._v(" 紧急 ")]):3==t.formdata.level?i("div",{staticClass:"levelTag tagred"},[t._v(" 非常紧急 ")]):t._e()])])],1)]),i("div",{staticClass:"left-item"},[t._m(9),i("div",[i("el-popover",{ref:"labeljsonRef",attrs:{placement:"bottom",trigger:"click","popper-class":"popper-labeljson"}},[i("div",{staticClass:"labeljsonBody",staticStyle:{height:"220px",width:"240px","overflow-y":"hidden"}},[i("div",{directives:[{name:"show",rawName:"v-show",value:t.showSetLbel,expression:"showSetLbel"}]},[i("div",{staticClass:"labeljson-header"},[i("input",{directives:[{name:"model",rawName:"v-model",value:t.labelVal,expression:"labelVal"}],staticClass:"searchLabel",attrs:{type:"text",placeholder:"搜索标签"},domProps:{value:t.labelVal},on:{input:[function(e){e.target.composing||(t.labelVal=e.target.value)},t.searchLabel]}}),i("i",{staticClass:"el-icon-circle-plus-outline",on:{click:function(e){t.showSetLbel=!1,t.labeloperaType=1,t.labelname=""}}})]),t.labelList.length?i("div",{staticStyle:{height:"180px","overflow-y":"auto"}},t._l(t.labelList,(function(e,a){return i("div",{key:a},[i("div",{staticClass:"labeljson-item",on:{click:function(a){return t.changeLabeljson(e)}}},[i("span",{class:"label-color label-"+e.labelcolor},[t._v(t._s(e.labelname))]),i("div",[i("i",{staticClass:"el-icon-edit",staticStyle:{color:"#15ad31"},on:{click:function(a){return a.stopPropagation(),t.editLabel(e)}}}),i("i",{staticClass:"el-icon-check",style:{opacity:-1!=t.formdata.labeljson.findIndex((function(t){return t.labelid==e.id}))?1:0,"line-height":"22px",color:"#1b9aee","font-size":"18px","margin-left":"15px"}})])])])})),0):i("div",[i("el-empty",{attrs:{description:"暂无标签","image-size":80}})],1)]),i("div",{directives:[{name:"show",rawName:"v-show",value:!t.showSetLbel,expression:"!showSetLbel"}]},[i("div",{staticClass:"createLabel flex j-s a-c"},[i("i",{staticClass:"el-icon-arrow-left",on:{click:function(e){t.showSetLbel=!0}}}),i("strong",[t._v(" 创建标签")]),i("i",{staticClass:"el-icon-close",on:{click:function(e){t.showSetLbel=!0,t.$refs["labeljsonRef"].doClose()}}})]),i("div",{},[i("el-input",{staticStyle:{padding:"10px","margin-top":"12px"},attrs:{placeholder:"标签名称"},model:{value:t.labelname,callback:function(e){t.labelname=e},expression:"labelname"}}),i("div",{staticClass:"flex",staticStyle:{padding:"10px","margin-top":"12px"}},t._l(t.discColor,(function(e,a){return i("div",{key:a,class:"disc-color disc-"+e,on:{click:function(a){t.labelcolor=e}}},[t.labelcolor==e?i("i",{staticClass:"el-icon-check"}):t._e()])})),0),i("el-button",{staticStyle:{width:"calc(100% - 20px)",margin:"12px 10px"},attrs:{type:"primary"},on:{click:t.createLabel}},[t._v("创建")])],1)])]),i("div",{attrs:{slot:"reference"},slot:"reference"},[t.formdata.labeljson.length?i("div",{staticStyle:{display:"inline-block",cursor:"pointer"}},[i("i",{staticClass:"el-icon-circle-plus-outline"})]):i("span",{staticStyle:{cursor:"pointer"}},[t._v("添加标签")])])])],1),t.formdata.labeljson.length?i("div",{staticClass:"popper-labeljson labstyle"},t._l(t.formdata.labeljson,(function(e,a){return i("span",{key:a,class:"label-color label-"+e.color},[t._v(" "+t._s(e.name)+" "),i("i",{staticClass:"el-icon-close",on:{click:function(i){return t.closeLabeljson(e,a)}}})])})),0):t._e()]),i("div",{staticClass:"left-item"},[t._m(10),i("el-input-number",{attrs:{min:0,placeholder:"请输入工时",controls:!0,step:.5},on:{change:t.changeWorkTime},model:{value:t.formdata.worktime,callback:function(e){t.$set(t.formdata,"worktime",e)},expression:"formdata.worktime"}})],1),i("div",{staticClass:"left-item"},[t._m(11),i("el-checkbox",{attrs:{label:"","true-label":1,"false-label":0,size:"mini"},on:{change:t.changeWeekMark},model:{value:t.formdata.weekmark,callback:function(e){t.$set(t.formdata,"weekmark",e)},expression:"formdata.weekmark"}})],1)]),i("div",{staticClass:"right"},[i("div",{staticClass:"right-header"},[i("p",[t._v("参与者·"+t._s(t.Participants.length))]),i("div",t._l(t.Participants,(function(e,a){return i("span",{key:a,staticClass:"right-header-tag"},[t._v(t._s(e.engineername))])})),0)]),i("div",{staticClass:"right-content myscrollbar"},[i("div",{staticClass:"tigs"},[i("div",{staticClass:"tigs-item",class:0==t.islogtype?"tigs-active":"",on:{click:function(e){t.islogtype=0,t.getDemandLog()}}},[t._v(" 所有动态 ")]),i("div",{staticClass:"tigs-item",class:1==t.islogtype?"tigs-active":"",on:{click:function(e){t.islogtype=1,t.getDemandLog()}}},[t._v(" 仅评论 ")])]),i("div",{staticClass:"logs"},[t.logTotal>10?i("div",{staticClass:"logs-more",on:{click:function(e){return t.getDemandLog(!0)}}},[t._v(" 默认显示最新10条动态，点击查看所有... ")]):t._e(),t._l(t.logList,(function(e,s){return i("div",{key:s,staticClass:"logs-item"},["评论"==e.type?i("div",{staticClass:"type-content"},[i("p",{staticClass:"flex j-s"},[i("span",{staticStyle:{color:"#333"}},[i("i",{staticClass:"el-icon-user"}),t._v(" "+t._s(e.createby))]),i("span",[t._v(t._s(t._f("dateFormats")(e.createdate)))])]),i("p",{staticStyle:{color:"#505050","text-indent":"16px"},domProps:{innerHTML:t._s(e.content.replace(/\n/gm,"<br>"))}}),e.attachment.name?i("div",{staticClass:"fileUploadsty"},[i("div",{staticClass:"fileUploadShow"},[i("img",{attrs:{src:e.attachment.type?a("b484")("./"+e.attachment.type+".png"):a("692f"),alt:""}})]),i("div",{staticClass:"fileUploadInfo"},[i("p",{staticClass:"ellipsis"},[t._v("名称："+t._s(e.attachment.name))]),i("p",[t._v("大小："+t._s(e.attachment.size+"KB"))]),i("span",{staticClass:"downFile",on:{click:function(a){return t.downFileName(e.attachment)}}},[t._v(" 下载附件 ")]),i("span",{staticClass:"downFile",on:{click:function(a){return t.getFileName(e.attachment)}}},[t._v(" 预览附件 ")])])]):t._e()]):i("div",[i("p",{staticClass:"flex j-s",staticStyle:{color:"#8c8c8c","font-size":"12px"}},[i("span",{staticStyle:{flex:"1"}},[i("i",{staticClass:"el-icon-edit-outline"}),i("span",{staticStyle:{"margin-left":"6px"},domProps:{innerHTML:t._s(e.content.replace(/\n/gm,"<br>"))}})]),i("span",[t._v(t._s(t._f("dateFormats")(e.createdate)))])])])])}))],2)]),i("div",{staticClass:"right-footer"},[i("textarea",{directives:[{name:"model",rawName:"v-model",value:t.textareaVal,expression:"textareaVal"}],staticClass:"textareaSty",attrs:{cols:"30",rows:"3",placeholder:"请输入评论"},domProps:{value:t.textareaVal},on:{input:function(e){e.target.composing||(t.textareaVal=e.target.value)}}}),i("div",{staticClass:"filesty"},t._l(t.fileList,(function(e,a){return i("span",{key:a,staticStyle:{cursor:"default"}},[t._v(" "+t._s(e.name)+" "),i("i",{staticClass:"el-icon-close",staticStyle:{cursor:"pointer"},on:{click:function(i){return t.deleteFile(e,a)}}})])})),0),i("div",[i("i",{staticClass:"el-icon-paperclip paperclipFile",on:{click:t.openFileUpload}}),i("el-button",{staticStyle:{float:"right"},attrs:{type:"primary",size:"small"},on:{click:t.addLog}},[t._v("回复")])],1)])]),t.fileUploadVisible?i("el-dialog",{attrs:{title:"添加附件",visible:t.fileUploadVisible,width:"500px","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.fileUploadVisible=e}}},[i("FileUpload",{ref:"fileUpload",on:{closeDialog:function(e){t.fileUploadVisible=!1}}}),i("div",{attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.fileUploadBtn}},[t._v("确定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.fileUploadVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.isViewPdf20?i("el-dialog",{attrs:{title:"附件预览",visible:t.isViewPdf20,top:"2vh",width:"80%","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[t.isImg?i("div",{staticStyle:{width:"100%","text-align":"center"}},[i("img",{staticStyle:{height:"auto",width:"auto"},attrs:{src:t.blobUrl,alt:""}})]):i("iframe",{staticStyle:{height:"80vh",width:"100%"},attrs:{id:"iframeId",src:t.blobUrl,frameborder:"0",scrolling:"auto"}})]):t._e()],1)},s=[function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-info"}),t._v(" 类型")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-folder-checked"}),t._v(" 状态")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user"}),t._v(" 执行者")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user-solid"}),t._v(" 协作者")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-user-solid"}),t._v(" 客户")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-date"}),t._v(" 开始时间")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-date"}),t._v(" 截止时间")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-coin"}),t._v(" 项目")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-s-flag"}),t._v(" 优先级")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-price-tag"}),t._v(" 标签")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-time"}),t._v(" 工时")])},function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("span",{staticClass:"left-item-title"},[a("i",{staticClass:"el-icon-share"}),t._v(" 周报")])}],n=a("c7eb"),o=a("1da1"),l=(a("99af"),a("c740"),a("caad"),a("a434"),a("b0c0"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("25f0"),a("2532"),a("3ca3"),a("4d90"),a("5319"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("08dd")),r=a("6e25"),c={props:["idx","statusList","userList","billtypeArr"],components:{FileUpload:l["a"],picker:r["default"]},data:function(){return{projectList:{item:[]},Participants:[],status:{},formdata:{labeljson:[],remark:"",billtitle:""},islogtype:0,logTotal:0,logList:[],labelList:[],labelListCopy:[],textareaVal:"",labelVal:"",labelname:"",labelId:"",showSetLbel:!0,labelcolor:"blue",labeloperaType:1,fileUploadVisible:!1,fileList:[],projectVal:"",projectAllList:[],projectAllListCopy:[],groupVal:"",groupAllList:[],blobUrl:"",isViewPdf20:!1,isImg:!1,workItemList:[],discColor:["blue","green","zi","orange","red"]}},mounted:function(){this.bindData(),this.getLabelList(),this.getGroupAllList()},methods:{bindData:function(){var t=this;0!=this.idx&&this.$request.get("/S06M02B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.formdata.weekmark=t.formdata.weekmark?1:0,t.formdata.labeljson=t.formdata.labeljson?JSON.parse(t.formdata.labeljson):[],t.fileList=[],t.$forceUpdate(),t.getDemandLog(),t.bindRightContent()):t.$message.warning(e.data.msg||"获取任务信息失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},getDemandLog:function(t){var e=this,a={PageNum:1,PageSize:10,OrderType:1,SearchType:1,SearchPojo:{demandid:this.formdata.id}};this.islogtype&&(a.scenedata=[{field:"Sa_DemandLog.type",fieldtype:1,math:"equal",value:"评论"}]),t&&(a.PageSize=100),this.$request.post("/S06M02S1/getPageList",JSON.stringify(a)).then((function(t){if(200==t.data.code){e.logList=t.data.data.list;for(var a=0;a<e.logList.length;a++){var i=e.logList[a];i.attachment=JSON.parse(i.attachment)}e.logTotal=t.data.data.total,console.log(e.logList)}else e.$message.warning(t.data.msg||"获取任务日志失败")}))},openFileUpload:function(){this.fileList.length>=1?this.$message.warning("只能添加一个附件"):this.fileUploadVisible=!0},fileUploadBtn:function(){var t=this,e=new FormData;e.append("file",this.$refs.fileUpload.file),this.$request.post("/File/upload?dirname="+this.$refs.fileUpload.uploadFileName,e).then((function(e){if(200==e.data.code){var a={name:t.$refs.fileUpload.uploadFileName,type:t.$refs.fileUpload.uploadFileType,size:t.$refs.fileUpload.uploadFileSize,fileurl:"/"+e.data.data.dirname+"/"+e.data.data.filename};t.fileList.push(a),t.fileUploadVisible=!1}else t.$message.warning(e.data.msg||"上传失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},deleteFile:function(t,e){var a=this;this.$request.post("/File/deleteFile?bucketName=inkspms&objectName="+t.fileurl).then((function(t){200==t.data.code?a.fileList.splice(e,1):a.$message.warning(t.data.msg||"删除附件失败")})).catch((function(t){a.$message.error(t||"请求错误")}))},downFileName:function(t){this.axios.get("http://dev.inksyun.com:9080/inkspms"+t.fileurl,{responseType:"blob"}).then((function(e){var a=window.URL.createObjectURL(e.data),i=document.createElement("a");i.href=a,i.download=t.name,i.click(),i.remove()}))},getFileName:function(t){var e=this;console.log(t),this.axios.get("http://dev.inksyun.com:9080/inkspms"+t.fileurl,{responseType:"blob"}).then((function(a){var i="application/pdf";if(e.isImg=!1,"pdf"==t.type.toLowerCase())i="application/pdf";else if("txt"===t.type.toLowerCase())i="text/plain";else{if(["jpg","jpeg","gif","bmp","png"].includes(t.name.toLowerCase()))return e.isImg=!0,e.blobUrl=window.URL.createObjectURL(a.data),e.isViewPdf20=!0,void e.$forceUpdate();e.$utils.message("info","此类型文件不支持预览,请下载查看！")}var s=[];s.push(a.data),e.blobUrl=window.URL.createObjectURL(new Blob(s,{type:i})),e.isViewPdf20=!0}))},addLog:function(){var t=this,e={type:"评论",content:this.textareaVal,demandid:this.formdata.id};this.fileList.length&&(e.attachment=JSON.stringify(this.fileList[0])),this.$request.post("/S06M02S1/create",JSON.stringify(e)).then((function(e){200==e.data.code?(t.textareaVal="",t.fileList=[],t.getDemandLog()):t.$message.warning(e.data.msg||"评论失败")})).catch((function(e){t.$message.error(e||"请求错误")}))},changeMsg:function(t){var e=this,a={id:this.formdata.id};"任务描述"==t?a.remark=this.formdata.remark:a.billtitle=this.formdata.billtitle,this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(a){200==a.data.code?(e.formdata=a.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(a.data.msg||"修改"+t+"失败"),e.bindData())}))},changeBilltype:function(t){var e=this;if(!t||this.formdata.billtype!=t){this.$refs["billTypeRef"].doClose();var a={billtype:t,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改优先级失败"),e.bindData())}))}},changeStatus:function(t){var e=this;if(this.formdata.demandstatus!=t.id){this.$refs["statusRef"].doClose();var a={demandstatus:t.id,id:this.formdata.id};2==t.statusattr?this.$prompt("","完成说明",{confirmButtonText:"提交",cancelButtonText:"关闭",closeOnClickModal:!1,inputValue:this.formdata.finishdes,inputType:"textarea",inputPlaceholder:"请输入"}).then((function(t){var i=t.value;a.finishdes=i,a.finishdate=new Date,e.updateStatus(a)})).catch((function(){e.$emit("bindData")})):this.updateStatus(a)}},updateStatus:function(t){var e=this;this.$request.post("/S06M02B1/update",JSON.stringify(t)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindData()):(e.$message.warning(t.data.msg||"修改状态失败"),e.bindData())}))},changeAppointee:function(t){var e=this;if(this.formdata.appointeeid!=t.id){this.$refs["appointeeRef"].doClose();var a={appointeeid:t.id,appointee:t.engineername,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindRightContent()):(e.$message.warning(t.data.msg||"修改执行者失败"),e.bindData())}))}},changeCollaborator:function(t){var e=this,a={collaboratorids:"",collaborators:"",id:this.formdata.id};if(""==t.id)this.$refs["collaboratorRef"].doClose();else if(this.formdata.collaboratorids){var i=this.formdata.collaboratorids.split(","),s=this.formdata.collaborators.split(",");if(console.log(i,"collaboratoridArr"),i.includes(t.id)){var n=i.findIndex((function(e){return e==t.id}));if(console.log(n),-1!=n){i.splice(n,1),s.splice(n,1),console.log(s,"collaboratorArr2");for(var o=/,$/gi,l=0;l<i.length;l++){var r=i[l];a.collaboratorids+=r+","}a.collaboratorids=a.collaboratorids.replace(o,"");for(l=0;l<s.length;l++){r=s[l];a.collaborators+=r+","}a.collaborators=a.collaborators.replace(o,"")}}else a.collaboratorids=this.formdata.collaboratorids+","+t.id,a.collaborators=this.formdata.collaborators+","+t.engineername}else a.collaboratorids=t.id,a.collaborators=t.engineername;this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog(),e.bindRightContent()):(e.$message.warning(t.data.msg||"修改执行者失败"),e.bindData())}))},changeGroup:function(t){var e=this,a={groupid:t.id||"",groupname:t.groupname||"",groupuid:t.groupuid||"",id:this.formdata.id};this.formdata.groupid!=t.id&&this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改客户信息失败"),e.bindData())}))},getGroupAllList:function(t){var e=this,a={PageNum:1,PageSize:1e3,OrderType:1,SearchType:1};t&&(a.SearchPojo={groupname:t}),this.$request.post("/S06M14B1/getPageList",JSON.stringify(a)).then((function(t){200==t.data.code?e.groupAllList=t.data.data.list:e.$message.warning(t.data.msg||"查询客户信息失败")}))},changelevel:function(t){var e=this;if(this.formdata.level!=t){this.$refs["selectTagRef"].doClose();var a={level:t,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改优先级失败"),e.bindData())}))}},changeDate:function(t){var e=this,a={id:this.formdata.id};"截止时间"==t?this.formdata.deaddate?a.deaddate=this.dateFormat(this.formdata.deaddate):a.deaddate=new Date(0):this.formdata.startdate?a.startdate=this.dateFormat(this.formdata.startdate):a.startdate=new Date(0),this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.$forceUpdate(),e.getDemandLog()):(e.$message.warning(t.data.msg||"修改时间失败"),e.bindData())}))},dateFormat:function(t){if(console.log(t),t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),n=e.getHours().toString().padStart(2,"0"),o=e.getMinutes().toString().padStart(2,"0");e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(s," ").concat(n,":").concat(o)}},changeWeekMark:function(t){var e=this,a={weekmark:t||0,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改工时失败"),e.bindData())}))},changeWorkTime:function(t){var e=this,a={worktime:t||0,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改工时失败"),e.bindData())}))},changeLabeljson:function(t){var e=this,a=this.formdata.labeljson.findIndex((function(e){return e.labelid==t.id}));if(console.log(a),-1==a){var i={name:t.labelname,color:t.labelcolor,labelid:t.id};this.formdata.labeljson.push(i)}-1!=a&&this.formdata.labeljson.splice(a,1);var s={labeljson:this.formdata.labeljson,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(s)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改标签失败"),e.bindData())}))},closeLabeljson:function(t){var e=this,a=this.formdata.labeljson.findIndex((function(e){return e.labelid==t.labelid}));-1!=a&&this.formdata.labeljson.splice(a,1);var i={labeljson:this.formdata.labeljson,id:this.formdata.id};this.$request.post("/S06M02B1/update",JSON.stringify(i)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改时间失败"),e.bindData())}))},createLabel:function(){var t=this,e={labelname:this.labelname,labelcolor:this.labelcolor},a="标签创建";if(this.labeloperaType)var i="/S06M02S6/create";else{i="/S06M02S6/update";e.id=this.labelId,a="标签修改"}this.$request.post(i,JSON.stringify(e)).then((function(e){200==e.data.code?(t.$message.success(a+"成功"),t.showSetLbel=!0,t.labelcolor="blue",t.$nextTick((function(){t.getLabelList()}))):t.$message.warning(e.data.msg||a+"失败")}))},editLabel:function(t){console.log(t),this.labelname=t.labelname,this.labelcolor=t.labelcolor,this.labelId=t.id,this.showSetLbel=!1,this.labeloperaType=0},searchLabel:function(){if(this.labelVal){this.labelList=[];for(var t=0;t<this.labelListCopy.length;t++){var e=this.labelListCopy[t];e.labelname.includes(this.labelVal)&&this.labelList.push(e)}}else this.labelList=[].concat(this.labelListCopy)},changeProject:function(t){var e=this;if(""==t.id)var a={projectid:"",itemname:"",itemcode:"",status:null,id:this.formdata.id};else{if(this.formdata.projectid==t.id)return;a={projectid:t.id,itemname:t.projname,itemcode:t.projcode,status:t.status[0].id,id:this.formdata.id}}this.$request.post("/S06M02B1/update",JSON.stringify(a)).then((function(t){200==t.data.code?(e.formdata=t.data.data,e.formdata.labeljson=e.formdata.labeljson?JSON.parse(e.formdata.labeljson):[],e.getDemandLog()):(e.$message.warning(t.data.msg||"修改项目失败"),e.bindData())}))},searchProject:function(){if(this.projectVal){this.projectAllListCopy=[];for(var t=0;t<this.projectAllList.length;t++){var e=this.projectAllList[t];e.projname.includes(this.projectVal)&&this.projectAllListCopy.push(e)}}else this.projectAllListCopy=[].concat(this.projectAllList)},bindRightContent:function(){var t=this;return Object(o["a"])(Object(n["a"])().mark((function e(){var a,i,s,o;return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(t.Participants=[],t.formdata.appointee&&(a={engineername:t.formdata.appointee},t.Participants.push(a)),t.formdata.collaborators)for(i=t.formdata.collaborators.split(","),s=0;s<i.length;s++)o=i[s],a={engineername:o},t.Participants.push(a);case 3:case"end":return e.stop()}}),e)})))()},getLabelList:function(){var t=this,e={PageNum:1,PageSize:1e3,OrderType:0,SearchType:1};this.$request.post("/S06M02S6/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code&&(t.labelList=[].concat(e.data.data.list),t.labelListCopy=[].concat(e.data.data.list))}))},getStatusName:function(t){var e="";if(t){var a=this.statusList.findIndex((function(e){return e.id==t}));-1!=a&&(e=this.statusList[a].statusname)}return e}}},d=c,m=(a("c9bd"),a("d36e"),a("2877")),p=Object(m["a"])(d,i,s,!1,null,"403d2814",null);e["a"]=p.exports},a18e:function(t,e,a){},aee0:function(t,e,a){"use strict";a("8026")},b724:function(t,e,a){"use strict";a("6f6b")},be80:function(t,e,a){"use strict";a("43f9")},c9bd:function(t,e,a){"use strict";a("f88f")},d36e:function(t,e,a){"use strict";a("a18e")},d8a4:function(t,e,a){"use strict";a("1c63")},f88f:function(t,e,a){}}]);