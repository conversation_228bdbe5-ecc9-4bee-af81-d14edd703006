(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bd43b1c0"],{"09f4":function(t,e,a){"use strict";a.d(e,"a",(function(){return l})),Math.easeInOutQuad=function(t,e,a,i){return t/=i/2,t<1?a/2*t*t+e:(t--,-a/2*(t*(t-2)-1)+e)};var i=function(){return window.requestAnimationFrame||window.webkitRequestAnimationFrame||window.mozRequestAnimationFrame||function(t){window.setTimeout(t,1e3/60)}}();function s(t){document.documentElement.scrollTop=t,document.body.parentNode.scrollTop=t,document.body.scrollTop=t}function n(){return document.documentElement.scrollTop||document.body.parentNode.scrollTop||document.body.scrollTop}function l(t,e,a){var l=n(),o=t-l,r=20,c=0;e="undefined"===typeof e?500:e;var d=function(){c+=r;var t=Math.easeInOutQuad(c,l,o,e);s(t),c<e?i(d):a&&"function"===typeof a&&a()};d()}},"63fe":function(t,e,a){},"695c":function(t,e,a){"use strict";a("e30f")},"8ab3":function(t,e,a){"use strict";a("63fe")},aad1:function(t,e,a){t.exports=a.p+"static/img/noFace.855a718f.jpg"},c3bd:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"container"},[a("div",{staticClass:"page-content",style:{height:t.tableMaxHeight+"px"}},[a("div",{staticClass:"page-content-left"},[a("h3",[t._v("参数设置")]),a("ul",{staticClass:"navigation ulstyle",style:{height:t.tableMaxHeight-40+"px"}},t._l(t.navlist,(function(e,i){return a("li",{key:i,class:t.navindex==i?"li-active":"",on:{click:function(a){t.navindex=i,t.navtitle=e.cfgname,t.childlst=e.children}}},[null==e.cfgicon||""==e.cfgicon?a("div",[a("i",{staticClass:"el-icon-price-tag"})]):a("div",[e.cfgicon.includes("el-icon")?a("i",{class:e.cfgicon}):a("svg-icon",{attrs:{"icon-class":e.cfgicon}})],1),a("span",{staticStyle:{"margin-left":"5px"}},[t._v(t._s(e.cfgname))])])})),0)]),a("div",{staticClass:"page-content-right"},[a("div",{staticClass:"settings-section"},[a("div",{staticClass:"header"},[a("h2",{staticClass:"title"},[t._v(t._s(t.navtitle))])]),a("div",{staticClass:"card"},[a("ul",{staticClass:"ulstyle",style:{"max-height":t.tableMaxHeight-90+"px"}},t._l(t.childlst,(function(e,i){return a("li",{key:i},[a("div",[a("div",{staticClass:"nameStyle"},[e.cfgicon&&e.cfgicon.includes("el-icon")?a("i",{class:e.cfgicon}):a("svg-icon",{attrs:{"icon-class":e.cfgicon?e.cfgicon:""}}),a("span",{staticStyle:{"margin-left":"5px"}},[t._v(t._s(e.cfgname))])],1),a("div",{staticClass:"valStyle"},[6==e.ctrltype?a("div",{staticClass:"valImg"},[a("img",{attrs:{src:e.cfgvalue,alt:""}}),a("span",{staticClass:"ellipsis tipStyle",attrs:{title:e.cfgvalue}},[t._v(t._s(e.cfgvalue))])]):a("span",{staticClass:"ellipsis tipStyle",attrs:{title:e.cfgvalue}},[t._v(t._s(e.cfgvalue))])])]),a("div",[a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",icon:"el-icon-edit"},on:{click:function(a){return t.editShow(e,i)}}},[t._v(" 编辑 ")])],1)])})),0)])])])])]),t.dialogVisible?a("el-dialog",{attrs:{title:t.dialogTitle,width:t.dialogwidth,visible:t.dialogVisible,"close-on-click-modal":!1},on:{"update:visible":function(e){t.dialogVisible=e}}},[a("editItem",{ref:"editItem",attrs:{row:t.childRow}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitDialog()}}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.dialogVisible=!1}}},[t._v("取 消")])],1)],1):t._e(),t.ImgInfoVisible?a("el-dialog",{attrs:{title:"素材库",visible:t.ImgInfoVisible,width:"860px",top:"5vh","append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.ImgInfoVisible=e}}},[a("selImgInfo",{ref:"selImgInfo",attrs:{multi:1}}),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitselImg}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ImgInfoVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},s=[],n=a("c7eb"),l=a("1da1"),o=(a("99af"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),a("0643"),a("4e3e"),a("159b"),a("b775")),r=a("333d"),c=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-position":"top"}},[a("el-row",[a("el-col",{attrs:{span:24}},[a("el-form-item",{staticStyle:{"line-height":"30px"},attrs:{label:t.formdata.cfgname}},[0==t.formdata.ctrltype?a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"请输入参数内容",clearable:""},model:{value:t.formdata.cfgvalue,callback:function(e){t.$set(t.formdata,"cfgvalue",e)},expression:"formdata.cfgvalue"}}):1==t.formdata.ctrltype?a("el-input-number",{staticStyle:{width:"80%"},attrs:{step:1,min:0},model:{value:t.formdata.cfgvalue,callback:function(e){t.$set(t.formdata,"cfgvalue",e)},expression:"formdata.cfgvalue"}}):2==t.formdata.ctrltype?a("el-select",{staticStyle:{width:"100%"},model:{value:t.formdata.cfgvalue,callback:function(e){t.$set(t.formdata,"cfgvalue",e)},expression:"formdata.cfgvalue"}},t._l(t.option,(function(t,e){return a("el-option",{key:e,attrs:{label:t.name,value:t.value}})})),1):t._e()],1)],1)],1)],1)],1)])},d=[],u=(a("b64b"),{props:["row"],data:function(){return{formdata:{},option:[]}},created:function(){this.bindData()},methods:{bindData:function(){this.formdata=Object.assign({},this.row),this.formdata.cfgoption?this.option=JSON.parse(this.formdata.cfgoption):this.option=[]}}}),f=u,p=(a("8ab3"),a("2877")),g=Object(p["a"])(f,c,d,!1,null,"8f2724d2",null),m=g.exports,h=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{},[i("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[i("div",[i("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"small"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),i("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),i("div",{staticClass:"uploadImg"},[i("el-button",{attrs:{type:"primary",size:"small\n        "},on:{click:function(e){return t.openImgUpload()}}},[t._v("上传")])],1)]),i("div",{staticClass:"material-content"},[0!=t.lst.length?i("div",{staticStyle:{display:"flex","flex-wrap":"wrap",flex:"1","align-content":"flex-start"}},t._l(t.lst,(function(e,s){return i("div",{key:s,staticClass:"img-item",on:{click:function(a){return t.getCurrentRow(e,s)}}},[i("div",{staticClass:"imgcountent",style:t.selIndex==s?" border: 2px solid #409eff; ":""},[e.fileurl?i("img",{attrs:{src:e.fileurl,alt:""}}):i("img",{attrs:{src:a("aad1"),alt:""}})]),i("div",{staticClass:"imgTitle ellipsis"},[i("span",{style:t.selIndex==s?"color:#409eff":""},[t._v(t._s(e.fileoriname)+" ")])])])})),0):i("div",{staticClass:"noData"},[t._v("暂无图片内容")])]),i("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}}),i("div",{directives:[{name:"show",rawName:"v-show",value:!1,expression:"false"}]},[i("input",{ref:"upload",attrs:{type:"file"},on:{change:t.getFile}})]),t.ImgUploadVisible?i("el-dialog",{attrs:{title:"图片上传",width:"500px",visible:t.ImgUploadVisible,"append-to-body":!0,"close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.ImgUploadVisible=e}}},[i("div",[i("el-button",{attrs:{type:"primary"},on:{click:function(e){return t.$refs.upload.click()}}},[t._v("选择文件")]),i("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[i("el-cascader",{attrs:{options:t.groupData,props:t.defaultProps,placeholder:"请选择分组",clearable:"","show-all-levels":!1},on:{change:t.handleChange},model:{value:t.gengroupid,callback:function(e){t.gengroupid=e},expression:"gengroupid"}})],1),i("div",{staticClass:"imgUpload"},[i("div",{staticClass:"imgUploadShow"},[i("img",{attrs:{src:t.uploadImg?t.uploadImg:a("aad1"),alt:""},on:{click:function(e){return t.$refs.upload.click()}}})]),t.uploadImg?i("div",{staticClass:"imgUploadInfo"},[i("p",[t._v("名称："+t._s(t.uploadImgName))]),i("p",[t._v("大小："+t._s(t.uploadImgSize))]),i("p",[t._v("类型："+t._s(t.uploadImgType))])]):t._e()])],1),i("div",{staticClass:"dialog-footer",staticStyle:{"margin-top":"-20px"},attrs:{slot:"footer"},slot:"footer"},[i("el-button",{attrs:{type:"primary",size:"small"},on:{click:function(e){return t.submitImgUpload()}}},[t._v("确 定")]),i("el-button",{attrs:{size:"small"},on:{click:function(e){t.ImgUploadVisible=!1}}},[t._v("取 消")])],1)]):t._e()],1)},v=[],b=a("2909"),y=(a("d81d"),a("b0c0"),a("a9e3"),a("b680"),a("a573"),a("6ca8")),I=a.n(y),w={components:{Pagination:r["a"]},props:["multi"],data:function(){return{title:"素材库",lst:[],strfilter:"",selrows:"",total:0,selIndex:-1,queryParams:{PageNum:1,PageSize:10,OrderType:1,SearchType:1},ImgUploadVisible:!1,uploadImg:"",uploadImgName:"",uploadImgSize:0,uploadImgType:"",fileTemp:{},pid:"root",treeTitle:"素材库",groupData:[],gengroupid:"",defaultProps:{children:"children",label:"label",value:"id"}}},watch:{},created:function(){this.bindData(),this.BindTreeData()},methods:{getCurrentRow:function(t,e){this.selIndex=e,this.selrows=t,this.$forceUpdate(),this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;o["a"].post("/utils/D96M02B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total)})).catch((function(e){t.$message.error("请求错误")}))},search:function(t){""!=t?this.queryParams.SearchPojo={fileoriname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},openImgUpload:function(){this.ImgUploadVisible=!0,this.uploadImg="",this.uploadImgName="",this.uploadImgSize="0KB",this.uploadImgType=""},getFile:function(){var t=this,e=this.$refs.upload,a=e.files[0];I()(a).then((function(e){console.log(e),t.uploadImg=e.base64,t.uploadImgName=e.origin.name,t.uploadImgSize=Number(e.fileLen/1024).toFixed(2)+"KB",t.uploadImgType=e.file.type,t.fileTemp=e}))},submitImgUpload:function(){this.uploadBase64(this.fileTemp.formData)},uploadBase64:function(t){var e=this;o["a"].post("/utils/D96M02B1/uploadImage",t,{headers:{"Content-Type":"multipart/form-data"}}).then((function(t){200==t.data.code?(e.uploadImg="",e.$refs.upload.value="",e.fileTemp={},e.ImgUploadVisible=!1,e.bindData()):e.$message.warning(t.data.msg||"上传图片失败")}))},handleChange:function(t){console.log(t),t.length>0?this.gengroupid=t[t.length-1]:this.gengroupid="0"},BindTreeData:function(){var t=this;o["a"].get("/system/SYSM07B8/getListByModuleCode?Code=D96M02B1").then((function(e){if(200==e.data.code){var a=e.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname}})),i=[{id:"0",pid:"root",label:t.treeTitle}],s=[].concat(Object(b["a"])(a),i);t.gengroupData=s,t.groupData=t.transData(s,"id","pid","children")}}))},transData:function(t,e,a,i){for(var s=[],n={},l=e,o=a,r=i,c=0,d=0,u=t.length;c<u;c++)n[t[c][l]]=t[c];for(;d<u;d++){var f=t[d],p=n[f[o]];p?(!p[r]&&(p[r]=[]),p[r].push(f)):s.push(f)}return s}}},S=w,x=(a("f0d2"),Object(p["a"])(S,h,v,!1,null,"543a9d6c",null)),_=x.exports,C={components:{Pagination:r["a"],editItem:m,selImgInfo:_},data:function(){return{title:"系统参数",lst:[],navlist:[],childlst:[],childRow:{},navtitle:"",navindex:0,searchstr:" ",formvisible:!1,ImgInfoVisible:!1,refreshTable:!1,isShowAll:!0,getType:"create",idx:0,total:0,queryParams:{PageNum:1,PageSize:1e3,OrderType:1,SearchType:0},dialogwidth:"500px",dialogTitle:"编辑",dialogVisible:!1,formdata:{}}},computed:{tableMaxHeight:function(){return window.innerHeight-90}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;return Object(l["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:t.$request.post("/SaConfig/getPageList",JSON.stringify(t.queryParams)).then((function(e){200===e.data.code?(t.lst=e.data.data.list,t.navlist=t.changeFormat(e.data.data.list),console.log("this.",t.navlist),t.navtitle=t.navlist[t.navindex].cfgname,t.childlst=t.navlist[t.navindex].children):t.$message.warning("查询参数列表失败")})),t.listLoading=!0;case 2:case"end":return e.stop()}}),e)})))()},getAppUrlConfig:function(){},editShow:function(t,e){this.childRow=t,6==t.ctrltype?this.ImgInfoVisible=!0:this.dialogVisible=!0},submitselImg:function(){var t=this,e=this.$refs.selImgInfo.selrows;this.childRow.cfgvalue=e.fileurl,this.ImgInfoVisible=!1;for(var a=0;a<this.lst.length;a++){var i=this.lst[a];if(i.id==this.childRow.id){this.lst[a]=this.childRow;break}}o["a"].post("/system/SYSM06B2/updateList",JSON.stringify(this.lst)).then((function(e){200==e.data.code?(t.dialogVisible=!1,t.$message.success("保存成功"),t.bindData()):t.$message.warning("保存失败")})).catch((function(e){t.$message.error("请求错误")}))},submitDialog:function(){var t=this,e=this.$refs.editItem.formdata;this.$request.post("/SaConfig/update",JSON.stringify(e)).then((function(e){200==e.data.code?(t.dialogVisible=!1,t.$message.success("保存成功"),t.bindData()):t.$message.warning("保存失败")}))},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.id]=t})),t.forEach((function(t){var i=a[t.parentid];i?(i.children||(i.children=[])).push(t):e.push(t)})),e}},filters:{dateFormat:function(t){if(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),s=e.getDate().toString().padStart(2,"0"),n=e.getHours().toString().padStart(2,"0"),l=e.getMinutes().toString().padStart(2,"0"),o=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(s," ").concat(n,":").concat(l,":").concat(o)}}}},k=C,P=(a("695c"),Object(p["a"])(k,i,s,!1,null,"6f291d31",null));e["default"]=P.exports},c7bf:function(t,e,a){},e30f:function(t,e,a){},f0d2:function(t,e,a){"use strict";a("c7bf")}}]);