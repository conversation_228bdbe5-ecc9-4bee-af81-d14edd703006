(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-commons"],{"038f":function(e,t,a){},"0521":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"help-warp"},[a("div",{staticClass:"help-header"},[a("span",[e._v(e._s(e.formdata.introname?e.formdata.introname:"功能简介"))])]),a("div",{staticClass:"help-content"},[e.formdata.introcontent?a("div",[a("div",{domProps:{innerHTML:e._s(e.formdata.introcontent)}})]):a("div",{staticClass:"noData",staticStyle:{"font-size":"22px"}},[e._v("暂无内容")])])])},o=[],r=a("b775"),n={props:["code"],data:function(){return{formdata:{}}},created:function(){},methods:{bindData:function(){var e=this;r["a"].get("/system/SYSM06B7/getEntityByCode?key="+this.code).then((function(t){if(200==t.data.code){if(null==t.data.data)return void(e.formdata={introname:"",introcontent:""});e.formdata=t.data.data}else e.$message.warning(t.data.msg||"获取功能简介失败")})).catch((function(t){e.$message.error(t||"请求错误")}))}}},l=n,s=(a("3f31"),a("2877")),c=Object(s["a"])(l,i,o,!1,null,"2f42eac9",null);t["a"]=c.exports},"0d6b":function(e,t,a){},1975:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{border:"1px solid #ccc","z-index":"999"}},[a("Toolbar",{staticStyle:{"border-bottom":"1px solid #ccc"},attrs:{editor:e.editor,defaultConfig:e.toolbarConfig,mode:e.mode}}),a("Editor",{staticStyle:{"overflow-y":"hidden"},style:{height:e.height+"px"},attrs:{defaultConfig:e.editorConfig,mode:e.mode},on:{onCreated:e.onCreated,onChange:e.onChange},model:{value:e.docHtml,callback:function(t){e.docHtml=t},expression:"docHtml"}})],1)},o=[],r=(a("a9e3"),a("af93"),a("2b0e")),n=a("4e15"),l=(a("cb23"),a("b775"),a("6ca8")),s=a.n(l),c=r["default"].extend({name:"MyEditor",components:{Editor:n["a"],Toolbar:n["b"]},props:{html:{type:String,required:!1,default:""},height:{type:[Number,String],required:!1,default:500},excludeKeys:{type:Array,default:function(){return[]}},toolbarKeys:{type:Array,default:function(){return["headerSelect","|","bold","underline","italic",{iconSvg:'<svg viewBox="0 0 1024 1024"><path d="M204.8 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M505.6 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path><path d="M806.4 505.6m-76.8 0a76.8 76.8 0 1 0 153.6 0 76.8 76.8 0 1 0-153.6 0Z"></path></svg>',key:"group-more-style",menuKeys:["through","code","sup","sub","clearStyle"],title:"更多"},"color","bgColor","|","fontSize","fontFamily","lineHeight","|","bulletedList","numberedList","todo",{iconSvg:'<svg viewBox="0 0 1024 1024"><path d="M768 793.6v102.4H51.2v-102.4h716.8z m204.8-230.4v102.4H51.2v-102.4h921.6z m-204.8-230.4v102.4H51.2v-102.4h716.8zM972.8 102.4v102.4H51.2V102.4h921.6z"></path></svg>',key:"group-justify",menuKeys:["justifyLeft","justifyRight","justifyCenter","justifyJustify"],title:"对齐"},{iconSvg:'<svg viewBox="0 0 1024 1024"><path d="M0 64h1024v128H0z m384 192h640v128H384z m0 192h640v128H384z m0 192h640v128H384zM0 832h1024v128H0z m0-128V320l256 192z"></path></svg>',key:"group-indent",menuKeys:["indent","delIndent"],title:"缩进"},"|","emotion","insertLink",{iconSvg:'<svg viewBox="0 0 1024 1024"><path d="M959.877 128l0.123 0.123v767.775l-0.123 0.122H64.102l-0.122-0.122V128.123l0.122-0.123h895.775zM960 64H64C28.795 64 0 92.795 0 128v768c0 35.205 28.795 64 64 64h896c35.205 0 64-28.795 64-64V128c0-35.205-28.795-64-64-64zM832 288.01c0 53.023-42.988 96.01-96.01 96.01s-96.01-42.987-96.01-96.01S682.967 192 735.99 192 832 234.988 832 288.01zM896 832H128V704l224.01-384 256 320h64l224.01-192z"></path></svg>',key:"group-image",menuKeys:["insertImage","uploadImage"],title:"图片"},{iconSvg:'<svg viewBox="0 0 1024 1024"><path d="M981.184 160.096C837.568 139.456 678.848 128 512 128S186.432 139.456 42.816 160.096C15.296 267.808 0 386.848 0 512s15.264 244.16 42.816 351.904C186.464 884.544 345.152 896 512 896s325.568-11.456 469.184-32.096C1008.704 756.192 1024 637.152 1024 512s-15.264-244.16-42.816-351.904zM384 704V320l320 192-320 192z"></path></svg>',key:"group-video",menuKeys:["insertVideo","uploadVideo"],title:"视频"},"insertTable","codeBlock","divider","|","undo","redo","|","uploadImage","fullScreen"]}}},data:function(){return{editor:null,docHtml:this.html,toolbarConfig:{toolbarKeys:this.toolbarKeys,excludeKeys:this.excludeKeys},editorConfig:{placeholder:"请输入内容...",MENU_CONF:{uploadImage:{customUpload:this.updateImg},uploadVideo:{customUpload:this.updataVideo}}},mode:"default"}},watch:{html:function(e,t){this.docHtml=e}},methods:{onCreated:function(e){this.editor=Object.seal(e)},onChange:function(e){this.$emit("changeHtml",e.getHtml())},getEditorText:function(){this.editor},printEditorHtml:function(){this.editor},updateImg:function(e,t){console.log(e,t),s()(e).then((function(e){t(e.base64,"","")}))},updataVideo:function(e,t){}},mounted:function(){},beforeDestroy:function(){var e=this.editor;null!=e&&e.destroy()}}),d=c,u=(a("1c3c"),a("2877")),m=Object(u["a"])(d,i,o,!1,null,null,null);t["a"]=m.exports},2172:function(e,t,a){"use strict";a("cb0a")},"2dba":function(e,t,a){},"333d":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"pagination-container",class:{hidden:e.hidden}},[a("el-pagination",e._b({attrs:{background:e.background,"current-page":e.currentPage,"page-size":e.pageSize,layout:e.layout,"page-sizes":e.pageSizes,"pager-count":e.pagerCount,total:e.total},on:{"update:currentPage":function(t){e.currentPage=t},"update:current-page":function(t){e.currentPage=t},"update:pageSize":function(t){e.pageSize=t},"update:page-size":function(t){e.pageSize=t},"size-change":e.handleSizeChange,"current-change":e.handleCurrentChange}},"el-pagination",e.$attrs,!1))],1)},o=[],r=(a("a9e3"),a("09f4")),n={name:"Pagination",props:{total:{required:!0,type:Number},page:{type:Number,default:1},limit:{type:Number,default:20},pagerCount:{type:Number,default:7},pageSizes:{type:Array,default:function(){return[5,10,20,50,100,500]}},layout:{type:String,default:"total, sizes, prev, pager, next, jumper"},background:{type:Boolean,default:!0},autoScroll:{type:Boolean,default:!0},hidden:{type:Boolean,default:!1}},computed:{currentPage:{get:function(){return this.page},set:function(e){this.$emit("update:page",e)}},pageSize:{get:function(){return this.limit},set:function(e){this.$emit("update:limit",e)}}},methods:{handleSizeChange:function(e){this.$emit("pagination",{page:this.currentPage,limit:e}),this.autoScroll&&Object(r["a"])(0,800)},handleCurrentChange:function(e){this.$emit("pagination",{page:e,limit:this.pageSize}),this.autoScroll&&Object(r["a"])(0,800)}}},l=n,s=(a("3471"),a("2877")),c=Object(s["a"])(l,i,o,!1,null,"51fb825e",null);t["a"]=c.exports},3471:function(e,t,a){"use strict";a("7709")},"3f31":function(e,t,a){"use strict";a("2dba")},"48da":function(e,t,a){"use strict";a.d(t,"a",(function(){return s}));a("2909"),a("99af"),a("d81d"),a("c19f"),a("ace42"),a("d3b7"),a("ac1f"),a("00b4"),a("25f0"),a("4d90"),a("5319"),a("5cc6"),a("9a8c"),a("a975"),a("735e"),a("c1ac"),a("d139"),a("3a7b"),a("d5d6"),a("82f8"),a("e91f"),a("60bd"),a("5f96"),a("3280"),a("3fcc"),a("ca91"),a("25a1"),a("cd26"),a("3c5d"),a("2954"),a("649e"),a("219c"),a("170b"),a("b39a"),a("72f7"),a("0643"),a("4e3e"),a("a573"),a("159b");function i(e,t){t&&(e+=1462);var a=Date.parse(e);return(a-new Date(Date.UTC(1899,11,30)))/864e5}function o(e,t){for(var a={},o={s:{c:1e7,r:1e7},e:{c:0,r:0}},r=0;r!=e.length;++r)for(var n=0;n!=e[r].length;++n){o.s.r>r&&(o.s.r=r),o.s.c>n&&(o.s.c=n),o.e.r<r&&(o.e.r=r),o.e.c<n&&(o.e.c=n);var l={v:e[r][n]};if(null!=l.v){var s=XLSX.utils.encode_cell({c:n,r:r});"number"===typeof l.v?l.t="n":"boolean"===typeof l.v?l.t="b":l.v instanceof Date?(l.t="n",l.z=XLSX.SSF._table[14],l.v=i(l.v)):l.t="s",a[s]=l}}return o.s.c<1e7&&(a["!ref"]=XLSX.utils.encode_range(o)),a}function r(){if(!(this instanceof r))return new r;this.SheetNames=[],this.Sheets={}}function n(e){for(var t=new ArrayBuffer(e.length),a=new Uint8Array(t),i=0;i!=e.length;++i)a[i]=255&e.charCodeAt(i);return t}function l(e){if(e){var t=new Date(e),a=t.getFullYear(),i=(t.getMonth()+1).toString().padStart(2,"0"),o=t.getDate().toString().padStart(2,"0"),r=t.getHours().toString().padStart(2,"0"),n=t.getMinutes().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(r,":").concat(n)}}function s(e,t,a){var i=[],s=/^\d{4}-(?:0[1-9]|1[0-2])-(?:0[1-9]|[1-2]\d|3[0-1])T(?:[0-1]\d|2[0-3]):[0-5]\d:[0-5]\d(?:\.\d+|)(?:Z|(?:\+|\-)(?:\d{2}):?(?:\d{2}))$/;t.forEach((function(e,t){for(var a=[],o=0;o<e.length;o++){var r=e[o];s.test(r)&&(r=l(r)),a.push(r)}i.push(a)}));var c=i;c.unshift(e);for(var d="SheetJS",u=new r,m=o(c),f=c.map((function(e){return e.map((function(e){return null==e?{wch:10}:e.toString().charCodeAt(0)>255?{wch:2*e.toString().length}:{wch:e.toString().length}}))})),p=f[0],h=1;h<f.length;h++)for(var b=0;b<f[h].length;b++)p[b]["wch"]<f[h][b]["wch"]&&(p[b]["wch"]=f[h][b]["wch"]);m["!cols"]=p,u.SheetNames.push(d),u.Sheets[d]=m;var v=XLSX.write(u,{bookType:"xlsx",bookSST:!1,type:"binary"}),g=a||"列表";saveAs(new Blob([n(v)],{type:"application/octet-stream"}),g+".xlsx")}a("0fd4"),a("f71d"),a("1447")},"50f4":function(e,t,a){},"64c9":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{},[a("div",{staticClass:"dictionaries_ul myscrollbar"},[0!=e.lst.length?a("ul",{},e._l(e.lst,(function(t,i){return a("li",{key:i,class:e.radio==i?"active":"",on:{click:function(a){e.getCurrentRow(t),e.radio=i}}},[a("span",[e._v(e._s(t.dictvalue))])])})),0):a("div",{staticClass:"noData"},[e._v("暂无数据")])]),a("div",{staticClass:"foots"},[a("span",{staticStyle:{"border-right":"1px solid #dcdfe6"},on:{click:function(t){e.SYSM07B1Visible=!0,e.lst=e.formdata.item}}},[e._v("编辑")]),a("span",{on:{click:function(t){return e.$emit("closedic")}}},[e._v("关闭")])])]),e.SYSM07B1Visible?a("el-dialog",{attrs:{width:"400px",title:"编辑","append-to-body":!0,visible:e.SYSM07B1Visible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(t){e.SYSM07B1Visible=t}}},[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"left"},e._l(e.lst,(function(t,i){return a("p",{key:i,class:e.ActiveIndex==i?"isActive":"",on:{click:function(t){e.ActiveIndex=i}}},[e._v(" "+e._s(t.dictvalue)+" ")])})),0),a("div",{staticClass:"right"},[a("el-button",{staticStyle:{"margin-left":"10px"},attrs:{type:"primary",size:"mini"},on:{click:function(t){return e.addInput()}}},[e._v("添 加")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex},on:{click:function(t){return e.editInput()}}},[e._v("编 辑")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:-1==e.ActiveIndex},on:{click:function(t){return e.delItem()}}},[e._v("删 除")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex},on:{click:function(t){return e.getMoveUp()}}},[e._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex},on:{click:function(t){return e.getMoveDown()}}},[e._v("下 移")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.submitUpdate}},[e._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.SYSM07B1Visible=!1,e.bindData()}}},[e._v("取 消")])],1)]):e._e()],1)},o=[],r=(a("a434"),a("e9c4"),a("b775")),n=a("333d"),l=a("b0b8"),s={components:{Pagination:n["a"]},props:["multi","billcode"],data:function(){return{title:"数据字典",formdata:{item:[]},lst:[],total:0,radio:-1,selrows:"",SYSM07B1Visible:!1,ActiveIndex:-1,queryParams:{PageNum:1,PageSize:100,OrderType:1,SearchType:1}}},watch:{lst:function(e,t){void 0==e&&(this.lst=[]);for(var a=0;a<e.length;a++)e[a].rownum=a}},created:function(){},methods:{getCurrentRow:function(e){this.selrows=e,this.$forceUpdate(),this.$emit("singleSel",e)},bindData:function(){var e=this;this.lst=[],r["a"].get("/system/SYSM07B1/getBillEntityByDictCode?key="+this.billcode).then((function(t){if(200==t.data.code){if(null==t.data.data)return e.formdata={item:[]},void(e.listLoading=!1);if(e.formdata=t.data.data,0==e.formdata.item.length)return void(e.lst=[]);for(var a=0;a<e.formdata.item.length;a++)e.lst.push(e.formdata.item[a])}})).catch((function(t){e.$message.error("请求出错")}))},rowIndex:function(e){var t=e.row,a=e.rowIndex;t.row_index=a},rowClick:function(e){this.radio=e.row_index,this.getCurrentRow(e)},addInput:function(){var e=this;this.$prompt("数值：","添加",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:"",inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(t){var a=t.value;e.addItem(a)})).catch((function(e){console.log(e)}))},addItem:function(e){l.setOptions({checkPolyphone:!1,charCase:1});var t={cssclass:"",defaultmark:0,dictcode:l.getFullChars(e),dictvalue:e,enabledmark:1,essential:0,pid:this.formdata.id,remark:"",rownum:0};this.lst.push(t)},editInput:function(){if(-1!=this.ActiveIndex){var e=this;this.$prompt("数值：","编辑",{confirmButtonText:"确定",cancelButtonText:"取消",inputValue:e.lst[e.ActiveIndex].dictvalue,inputErrorMessage:"输入不能为空",closeOnClickModal:!1,inputValidator:function(e){if(!e)return"输入不能为空"}}).then((function(t){var a=t.value;e.lst[e.ActiveIndex].dictvalue=a,l.setOptions({checkPolyphone:!1,charCase:1}),e.lst[e.ActiveIndex].dictcode=l.getFullChars(a)})).catch((function(e){console.log(e)}))}else this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.lst.splice(e.ActiveIndex,1),e.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t-1,0,e),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t+1,0,e),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var e=this;this.formdata.item=this.lst,r["a"].post("/system/SYSM07B1/update",JSON.stringify(this.formdata)).then((function(t){200==t.data.code?(e.$message.success(t.data.msg||"保存成功"),e.SYSM07B1Visible=!1):e.$message.warning(t.data.msg||"保存失败")})).catch((function(t){e.$message.error("请求错误")}))}}},c=s,d=(a("e708"),a("2877")),u=Object(d["a"])(c,i,o,!1,null,"0743e156",null);t["a"]=u.exports},7709:function(e,t,a){},7997:function(e,t,a){},"812b":function(e,t,a){"use strict";a("0d6b")},"82f0":function(module,__webpack_exports__,__webpack_require__){"use strict";var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("b64b"),core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__),_selDict_vue__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("64c9");__webpack_exports__["a"]={components:{selDictionaries:_selDict_vue__WEBPACK_IMPORTED_MODULE_1__["a"]},props:{title:{type:String,default:"单据标题"},formdata:{type:Object,default:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname}},formtemplate:{type:[Array],default:[]},selectform:{type:[Array],default:function(){return[]}}},mounted:function(){},data:function(){return{}},methods:{returnEval:function returnEval(data,itrue){return"string"==typeof data?eval(data):data},clickMethods:function(e,t){if(e){var a={meth:e,param:t};this.$emit("clickMethods",a)}},cleValidate:function(e){this.$refs.formdata.clearValidate(e)}}}},"8daf":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("div",{staticClass:"dialog-body customDialog"},[a("div",{staticClass:"right"},[a("el-button-group",[a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex,icon:"el-icon-top"},on:{click:function(t){return e.getMoveUp()}}},[e._v("上 移")]),a("el-button",{attrs:{type:"primary",size:"mini",disabled:-1==e.ActiveIndex,icon:"el-icon-bottom"},on:{click:function(t){return e.getMoveDown()}}},[e._v("下 移")])],1),a("div",[e.formdata.id?a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"mini",icon:"el-icon-refresh-left"},on:{click:function(t){return e.refreshLeft()}}},[e._v("重置")]):e._e()],1)],1),a("div",{staticClass:"left"},[a("div",{staticStyle:{"max-height":"520px"}},[a("table",{staticClass:"productlTable",attrs:{cellspacing:"0",cellpadding:"0"}},[e._m(0),a("tbody",e._l(e.lst,(function(t,i){return a("tr",{key:i,class:e.ActiveIndex==i?"isActive":"",on:{click:function(t){e.ActiveIndex=i}}},[a("td",[e._v(e._s(t.itemcode))]),a("td",[e._v(e._s(t.itemname))]),a("td",{staticStyle:{width:"180px"}},[e.ActiveIndex==i?a("el-input",{attrs:{placeholder:"最小宽度",size:"small"},model:{value:t.minwidth,callback:function(a){e.$set(t,"minwidth",a)},expression:"i.minwidth"}}):a("span",[e._v(e._s(t.minwidth))])],1),a("td",[a("el-switch",{staticStyle:{"margin-right":"10px"},attrs:{"inactive-color":"#ff4949","active-value":1,"inactive-value":0},model:{value:t.displaymark,callback:function(a){e.$set(t,"displaymark",a)},expression:"i.displaymark"}})],1)])})),0)])])])]),e.PwProcessFormVisible?a("el-dialog",{attrs:{title:"列表信息","append-to-body":!0,visible:e.PwProcessFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(t){e.PwProcessFormVisible=t}}},[a("el-form",{ref:"itemFormdata",staticClass:"custInfo",attrs:{model:e.itemFormdata,rules:e.itemFormRules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("itemcode")}}},[a("el-form-item",{attrs:{label:"编码",prop:"itemcode"}},[a("el-input",{attrs:{placeholder:"请输入编码",clearable:""},model:{value:e.itemFormdata.itemcode,callback:function(t){e.$set(e.itemFormdata,"itemcode",t)},expression:"itemFormdata.itemcode"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(t){return e.cleValidate("itemname")}}},[a("el-form-item",{attrs:{label:"名称",prop:"itemname"}},[a("el-input",{attrs:{placeholder:"请输入名称",clearable:""},model:{value:e.itemFormdata.itemname,callback:function(t){e.$set(e.itemFormdata,"itemname",t)},expression:"itemFormdata.itemname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"最小宽度"}},[a("el-input",{attrs:{placeholder:"请输入最小宽度",clearable:""},model:{value:e.itemFormdata.minwidth,callback:function(t){e.$set(e.itemFormdata,"minwidth",t)},expression:"itemFormdata.minwidth"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"默认宽度"}},[a("el-input",{attrs:{placeholder:"请输入默认宽度",clearable:""},model:{value:e.itemFormdata.defwidth,callback:function(t){e.$set(e.itemFormdata,"defwidth",t)},expression:"itemFormdata.defwidth"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"格式化"}},[a("el-input",{attrs:{placeholder:"请输入格式化",clearable:""},model:{value:e.itemFormdata.formatter,callback:function(t){e.$set(e.itemFormdata,"formatter",t)},expression:"itemFormdata.formatter"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"calss"}},[a("el-input",{attrs:{placeholder:"请输入自定义calss",clearable:""},model:{value:e.itemFormdata.classname,callback:function(t){e.$set(e.itemFormdata,"classname",t)},expression:"itemFormdata.classname"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"位置"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"},model:{value:e.itemFormdata.aligntype,callback:function(t){e.$set(e.itemFormdata,"aligntype",t)},expression:"itemFormdata.aligntype"}},[a("el-option",{attrs:{label:"left",value:"left"}}),a("el-option",{attrs:{label:"center",value:"center"}}),a("el-option",{attrs:{label:"right",value:"right"}})],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"事件名称",prop:"eventname"}},[a("el-input",{attrs:{placeholder:"请输入事件名称",clearable:""},model:{value:e.itemFormdata.eventname,callback:function(t){e.$set(e.itemFormdata,"eventname",t)},expression:"itemFormdata.eventname"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注"},model:{value:e.itemFormdata.remark,callback:function(t){e.$set(e.itemFormdata,"remark",t)},expression:"itemFormdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"是否显示"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:e.itemFormdata.displaymark,callback:function(t){e.$set(e.itemFormdata,"displaymark",t)},expression:"itemFormdata.displaymark"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"溢出隐藏"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:e.itemFormdata.overflow,callback:function(t){e.$set(e.itemFormdata,"overflow",t)},expression:"itemFormdata.overflow"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"固定"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:e.itemFormdata.fixed,callback:function(t){e.$set(e.itemFormdata,"fixed",t)},expression:"itemFormdata.fixed"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-checkbox",{attrs:{label:"是","true-label":1,"false-label":0,size:"mini"},model:{value:e.itemFormdata.sortable,callback:function(t){e.$set(e.itemFormdata,"sortable",t)},expression:"itemFormdata.sortable"}})],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(t){return e.selPwProcess()}}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.PwProcessFormVisible=!1}}},[e._v("取 消")])],1)],1):e._e()],1)},o=[function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("thead",[a("tr",[a("td",{staticClass:"tabTh"},[e._v("编码")]),a("td",{staticClass:"tabTh"},[e._v("名称")]),a("td",{staticClass:"tabTh"},[e._v("最小宽度")]),a("td",{staticClass:"tabTh"},[e._v("显示/隐藏")])])])}],r=(a("a434"),a("e9c4"),a("c7cd"),a("b775")),n=(a("b0b8"),{inject:["reload"],props:["code","tableForm"],data:function(){return{title:"列设置",lst:[],formdata:{item:[]},ActiveIndex:-1,btnType:"Add",PwProcessFormVisible:!1,itemFormRules:{itemcode:[{required:!0,trigger:"blur",message:"编码为必填项"}],itemname:[{required:!0,trigger:"blur",message:"名称为必填项"}]},itemFormdata:{aligntype:"center",classname:"",defwidth:"",displaymark:1,eventname:"",fixed:0,formatter:"",itemcode:"",itemname:"",minwidth:100,overflow:1,pid:this.idx,remark:"",rownum:0,sortable:0}}},watch:{lst:function(e,t){void 0==e&&(this.lst=[]);for(var a=0;a<e.length;a++)e[a].rownum=a}},created:function(){this.searchstr="",this.bindData()},methods:{bindData:function(){this.formdata=this.tableForm,this.lst=this.tableForm.item},restForm:function(){this.$refs.itemFormdata.resetFields()},refreshLeft:function(){var e=this,t=this;this.$confirm("此操作将初始化表格内容, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){r["a"].get("/system/SYSM07B9/delete?key="+t.formdata.id).then((function(a){200==a.data.code?(t.$message.success(a.data.msg||"重置内容成功"),t.getcolumn(e.formdata.formcode),t.$emit("bindData"),t.$emit("closeDialog")):t.$message.warning(a.data.msg||"重置内容失败")}))})).catch((function(){}))},selPwProcess:function(e){this.PwProcessFormVisible=!1;var t={aligntype:this.itemFormdata.aligntype,classname:this.itemFormdata.classname,defwidth:this.itemFormdata.defwidth,displaymark:this.itemFormdata.displaymark,eventname:this.itemFormdata.eventname,fixed:this.itemFormdata.fixed,formatter:this.itemFormdata.formatter,itemcode:this.itemFormdata.itemcode,itemname:this.itemFormdata.itemname,minwidth:this.itemFormdata.minwidth,overflow:this.itemFormdata.overflow,pid:this.formdata.id,remark:this.itemFormdata.remark,rownum:this.itemFormdata.rownum,sortable:this.itemFormdata.sortable};"Add"==this.btnType&&this.lst.push(t),this.$refs.itemFormdata.resetFields()},editInput:function(){-1!=this.ActiveIndex?(this.btnType="Edit",this.PwProcessFormVisible=!0,this.itemFormdata=this.lst[this.ActiveIndex],console.log("this.itemFormdata",this.itemFormdata)):this.$message.warning("请选择内容")},delItem:function(){if(-1!=this.ActiveIndex){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){e.lst.splice(e.ActiveIndex,1),e.ActiveIndex=-1})).catch((function(){}))}else this.$message.warning("请选择内容")},getMoveUp:function(){if(0!=this.ActiveIndex){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t-1,0,e),this.ActiveIndex-=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是第一行了！")},getMoveDown:function(){if(this.ActiveIndex!=this.lst.length-1){var e=this.lst[this.ActiveIndex],t=this.lst[this.ActiveIndex].rownum;this.lst.splice(t,1),this.lst.splice(t+1,0,e),this.ActiveIndex+=1;for(var a=0;a<this.lst.length;a++)this.lst[a].rownum=a}else this.$message.warning("已经是最后一行了！")},submitUpdate:function(){var e=this;if(this.formdata.item=this.lst,console.log("submitUpdate",this.formdata),this.formdata.id)t="/system/SYSM07B9/update";else var t="/system/SYSM07B9/create";r["a"].post(t,JSON.stringify(this.formdata)).then((function(t){200==t.data.code?(e.$message.success(t.data.msg||"保存成功"),e.formdata.id=t.data.data.id,e.$forceUpdate(),e.$emit("bindData"),e.$emit("closeDialog")):e.$message.warning(t.data.msg||"保存失败")})).catch((function(t){e.$message.error(t||"请求错误")}))},getcolumn:function(e){var t=this;r["a"].get("/system/SYSM07B9/getBillEntityByCode?code="+e).then((function(e){if(200==e.data.code){if(null==e.data.data)return t.formdata.id="",void t.$forceUpdate();t.formdata=e.data.data,t.lst=e.data.data.item,t.$forceUpdate()}})).catch((function(e){t.$message.error("请求出错")}))},cleValidate:function(e){this.$refs.itemFormdata.clearValidate(e)}}}),l=n,s=(a("812b"),a("2877")),c=Object(s["a"])(l,i,o,!1,null,"be26f16a",null);t["a"]=c.exports},a213:function(e,t,a){},ab18:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-menu",{ref:"domMenu",staticClass:"domMenu",attrs:{"default-active":e.activeMenu,"background-color":"#fff","text-color":"#40454b","active-text-color":"#409eff"}},[a("ItemMenu",{ref:"itemMenu",attrs:{subMenu:e.subMenu},on:{clickMenuItem:e.clickVal}})],1)],1)},o=[],r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e._l(e.subMenu,(function(t,i){return[t.children?[a("el-submenu",{key:t.id+"_"+i,attrs:{index:t.id},scopedSlots:e._u([{key:"title",fn:function(){return[a("i",{class:"file"==t.type?t.icon?t.icon:"el-icon-document":"el-icon-folder"}),a("span",[e._v(e._s(t.label))])]},proxy:!0}],null,!0)},[a("SubMenuItem",{attrs:{subMenu:t.children},on:{clickMenuItem:e.clickMenuItem}})],1)]:a("el-menu-item",{key:t.id+"_"+i,attrs:{index:t.id},on:{click:function(a){return e.clickMenuItem(t)}},scopedSlots:e._u([{key:"title",fn:function(){return[a("i",{class:"file"==t.type?t.icon?t.icon:"el-icon-document":"el-icon-folder",style:{color:t.iconbg?t.iconbg:"#909399"},attrs:{title:t.icontip}}),a("span",{staticClass:"menutitle",attrs:{title:t.label}},[e._v(e._s(t.label))])]},proxy:!0}],null,!0)})]}))],2)},n=[],l={name:"SubMenuItem",props:{subMenu:{typeof:Array}},methods:{clickMenuItem:function(e){"file"==e.type?this.$emit("clickMenuItem",e):this.$emit("clickMenuItem",0)}}},s=l,c=(a("d759"),a("2877")),d=Object(c["a"])(s,r,n,!1,null,"0a3e585a",null),u=d.exports,m={name:"SubMenu",components:{ItemMenu:u},props:{subMenu:{typeof:Array,required:!0}},data:function(){return{activeMenu:"1"}},methods:{clickVal:function(e){this.activeMenu=e.id,this.$emit("clickMenu",e)}}},f=m,p=Object(c["a"])(f,i,o,!1,null,"5e0b07ea",null);t["a"]=p.exports},ab2e:function(e,t,a){"use strict";a("a213")},acb9:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.ExamineVisible?a("el-dialog",{attrs:{title:"审批模板",width:"400px",visible:e.ExamineVisible,"append-to-body":!0},on:{"update:visible":function(t){e.ExamineVisible=t}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择审批模板"},on:{change:e.changeExamineModel},model:{value:e.ExamineModel,callback:function(t){e.ExamineModel=t},expression:"ExamineModel"}},e._l(e.ExamineData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.apprname,value:t.id}},[a("span",{staticStyle:{float:"left"}},[e._v(e._s(t.apprname))]),a("span",{staticClass:"selectSpan",staticStyle:{float:"right","text-align":"right",color:"#8492a6","font-size":"13px"}},[e._v(e._s("wxe"==t.sendType?"企业微信":"钉钉"))])])})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:e.submitExamine}},[e._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(t){e.ExamineVisible=!1}}},[e._v("取 消")])],1)],1):e._e()],1)},o=[],r=a("c7eb"),n=a("1da1"),l=(a("d3b7"),a("0643"),a("4e3e"),a("159b"),{name:"Flowable",props:["examinecode","examineurl","formdata"],data:function(){return{sendType:"wxe",ExamineVisible:!1,ExamineData:[],ExamineModel:""}},methods:{action:function(){var e=this;return Object(n["a"])(Object(r["a"])().mark((function t(){var a,i;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:e.ExamineData=[],a="/S06M36B1/getListByModuleCode?code="+e.examinecode,i="wxe",e.$request.get(a).then((function(t){if(t.data.data.length)for(var a=0;a<t.data.data.length;a++){var o=t.data.data[a];o.sendType=i,e.ExamineData.push(o),e.$nextTick((function(){0!=e.ExamineData.length&&(e.ExamineModel=e.ExamineData[0].id,e.sendType=e.ExamineData[0].sendType),e.ExamineVisible=!0}))}else e.$message.warning("暂无模板数据")}));case 4:case"end":return t.stop()}}),t)})))()},changeExamineModel:function(e){var t=this;this.ExamineData.forEach((function(a){a.id==e&&(t.sendType=a.sendType,t.ExamineModel=a.id)}))},submitExamine:function(){var e=this;""!=this.ExamineModel?this.$request.get(this.examineurl+"?key="+this.formdata.id+"&apprid="+this.ExamineModel+"&type="+this.sendType).then((function(t){200==t.data.code?(e.$message.success("通知发送成功"),e.ExamineVisible=!1,e.$emit("bindData")):e.$message.warning(t.data.msg||"通知发送失败")})):this.$message.warning("审批模板不能为空!")}}}),s=l,c=a("2877"),d=Object(c["a"])(s,i,o,!1,null,null,null);t["a"]=d.exports},c7ebf:function(e,t,a){"use strict";a("7997")},cb0a:function(e,t,a){},d759:function(e,t,a){"use strict";a("50f4")},dcb4:function(e,t,a){"use strict";var i,o,r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.$slots.Header?a("div",[e._t("Header")],2):a("div",{staticStyle:{"min-height":"150px"}},[a("Header",{ref:"formHeader",attrs:{formdata:e.formdata,formtemplate:e.formtemplate.header.content,title:e.formtemplate.header.title,selectform:e.selectform},on:{clickMethods:function(t){return e.$emit("clickMethods",t)},cleValidate:function(t){return e.$emit("cleValidate",t)},getAllGroupName:function(t){return e.$emit("getAllGroupName",t)},getGroupName:function(t){return e.$emit("getGroupName",t)},getSuppGroupName:function(t){return e.$emit("getSuppGroupName",t)},getFactGroupName:function(t){return e.$emit("getFactGroupName",t)},getWorkGroupName:function(t){return e.$emit("getWorkGroupName",t)},getBranGroupName:function(t){return e.$emit("getBranGroupName",t)},getProsGroupName:function(t){return e.$emit("getProsGroupName",t)},getStoreName:e.getStoreName,getProcName:function(t){return e.$emit("getProcName",t)},getRoleProcName:function(t){return e.$emit("getRoleProcName",t)},getGoodsName:function(t){return e.$emit("getGoodsName",t)},getFlawName:function(t){return e.$emit("getFlawName",t)},autoClear:function(t){return e.$emit("autoClear")},autoStoreClear:function(t){return e.$emit("autoStoreClear",t)},autoProcClear:function(t){return e.$emit("autoProcClear")},autoRoleProcClear:function(t){return e.$emit("autoRoleProcClear")},autoFlawClear:function(t){return e.$emit("autoFlawClear")}}})],1),e._t("Item"),e.$slots.Footer?a("div",[e._t("Footer")],2):a("div",[a("Footer",{attrs:{formdata:e.formdata,formtemplate:e.formtemplate.footer.content}})],1)],2)},n=[],l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"formdata",staticClass:"custInfo",staticStyle:{"margin-top":"20px"},attrs:{model:e.formdata,"label-width":"100px"}},[a("p",{staticClass:"formTitle"},[e._v(e._s(e.title?e.title:"单据标题"))]),e._l(e.formtemplate,(function(t,i){return[a("div",{key:i},["divider"==t.type?a("el-divider",{attrs:{"content-position":t.center?t.center:"left"}},[e._v(e._s(t.label))]):a("el-row",[e._l(t.rowitem,(function(t,i){return[(t.show?e.returnEval(t.show):!t.show)?a("el-col",{key:i,attrs:{span:t.col}},[a("div",{on:{click:function(a){return e.cleValidate(t.code)}}},[a("el-form-item",{attrs:{label:"checkbox"==t.type?"":t.label,prop:t.code,"label-width":t.labelwidth?t.labelwidth:"100px",rules:[{required:!!t.required&&t.required,trigger:"blur",message:t.label+"为必填项"}]}},["input"==t.type?a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},[t.iconbtn?[a("i",{directives:[{name:"show",rawName:"v-show",value:e.returnEval(t.iconbtn.show,1),expression:"returnEval(b.iconbtn.show, 1)"}],staticClass:"getNextCode",class:t.iconbtn.icon,attrs:{slot:"suffix"},on:{click:function(a){return e.clickMethods(t.iconbtn.methods,t.iconbtn.param)}},slot:"suffix"})]:e._e()],2):"select"==t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"+t.label,size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},e._l(-1!=e.selectform.findIndex((function(e){return e.code==t.code}))?e.selectform[e.selectform.findIndex((function(e){return e.code==t.code}))].data:t.options,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1):"checkbox"==t.type?a("div",[a("el-checkbox",{attrs:{label:t.label,"true-label":1,"false-label":0,disabled:!!t.disabled&&e.returnEval(t.disabled)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"number"==t.type?a("div",[a("el-input-number",{attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"date"==t.type?a("div",[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:t.size?t.size:"small",clearable:"",placeholder:"选择日期",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"dictionary"==t.type?a("div",[a("el-popover",{ref:t.code+"PropRef",refInFor:!0,attrs:{placement:"bottom",trigger:"click"},on:{show:function(a){e.$refs[t.code+"Ref"][0].bindData()}}},[a("selDictionaries",{ref:t.code+"Ref",refInFor:!0,staticStyle:{width:"200px"},attrs:{multi:0,billcode:t.billcode},on:{singleSel:function(a){e.formdata[t.code]=a.dictvalue,e.$refs[t.code+"PropRef"][0].doClose()},closedic:function(a){e.$refs[t.code+"PropRef"][0].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1)],1)],1):"textarea"==t.type?a("div",[a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",type:"textarea",size:t.size?t.size:"small",autosize:t.autosize?t.autosize:{minRows:2,maxRows:4},disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"text"==t.type?a("div",[a("span",{staticStyle:{"font-size":"18px",color:"#666"}},[e._v(e._s(e.formdata[t.code]))])]):"autocomplete"==t.type?a("div",["customer"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B1/getOnlinePageList",type:"客户",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"supplier"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B2/getOnlinePageList",type:"供应商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getSuppGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"workshop"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B3/getPageList",type:"生产车间",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getWorkGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"factory"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B4/getPageList",type:"外协厂商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getFactGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"branch"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/sale/D01M01B5/getPageList",type:"部门"},on:{setRow:function(a){e.$emit("getBranGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"prospects"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/sale/D01M01B6/getPageList",type:"潜在客户"},on:{setRow:function(a){e.$emit("getProsGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"group"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getAllGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"store"==t.searchtype?a("div",[a("StoreAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getStoreName",a,t.code),e.cleValidate(t.code)},autoClear:function(a){return e.$emit("autoStoreClear",t.code)}}})],1):"procedure"==t.searchtype?a("div",[a("ProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getProcName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoProcClear")}}})],1):"roleproc"==t.searchtype?a("div",[a("RoleProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getRoleProcName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoRoleProcClear")}}})],1):"goods"==t.searchtype?a("div",[a("GoodsAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGoodsName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):e._e()]):e._e()],1)],1)]):e._e()]}))],2)],1)]}))],2)},s=[],c=a("82f0"),d=c["a"],u=(a("ab2e"),a("2877")),m=Object(u["a"])(d,l,s,!1,null,"7341fcfa",null),f=m.exports,p=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{"margin-top":"15px"}},[a("el-form",{ref:"formdata",staticClass:"footFormContent",attrs:{"label-width":"100px"}},[e._l(e.tempData,(function(t,i){return[a("div",{key:i},["divider"==t.type?a("el-divider",{attrs:{"content-position":t.center?t.center:"left"}},[e._v(e._s(t.label))]):"foot"==t.type?a("el-row",[e._l(t.rowitem,(function(t,i){return[a("el-col",{directives:[{name:"show",rawName:"v-show",value:"assessdate"!=t.code||e.formdata.assessor,expression:"b.code == 'assessdate' ? formdata.assessor : true"}],key:i,attrs:{span:t.col}},[a("el-form-item",{attrs:{label:t.label,prop:t.code}},["assessdate"==t.code||"modifydate"==t.code||"createdate"==t.code?a("div",[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata[t.code],expression:"formdata[b.code]"}],staticClass:"el-form-item__label",staticStyle:{"white-space":"nowrap"}},[e._v(e._s(e._f("dateFormats")(e.formdata[t.code])))])]):a("div",[a("span",{directives:[{name:"show",rawName:"v-show",value:e.formdata[t.code],expression:"formdata[b.code]"}],staticClass:"el-form-item__label"},[e._v(e._s(e.formdata[t.code]))])])])],1)]}))],2):a("el-row",[e._l(t.rowitem,(function(t,i){return[(t.show?e.returnEval(t.show):!t.show)?a("el-col",{key:i,attrs:{span:t.col}},[a("div",{on:{click:function(a){return e.cleValidate(t.code)}}},[a("el-form-item",{attrs:{label:"checkbox"==t.type?"":t.label,prop:t.code}},["input"==t.type?a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}}):"select"==t.type?a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择"+t.label,size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}},e._l(t.options,(function(e,t){return a("el-option",{key:t,attrs:{label:e.label,value:e.value}})})),1):"checkbox"==t.type?a("div",[a("el-checkbox",{attrs:{label:t.label,"true-label":0,"false-label":1,size:t.size?t.size:"mini",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"number"==t.type?a("div",[a("el-input-number",{attrs:{controls:!0,type:"number",min:0,"controls-position":"right",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},on:{change:function(a){return e.clickMethods(t.methods,t.param)}},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"date"==t.type?a("div",[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{type:"date",size:t.size?t.size:"small",clearable:"",placeholder:"选择日期",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"dictionary"==t.type?a("div",[a("el-popover",{ref:t.code+"PropRef",refInFor:!0,attrs:{placement:"bottom",trigger:"click"},on:{show:function(a){e.$refs[t.code+"Ref"][0].bindData()}}},[a("selDictionaries",{ref:t.code+"Ref",refInFor:!0,staticStyle:{width:"200px"},attrs:{multi:0,billcode:t.billcode},on:{singleSel:function(a){e.formdata[t.code]=a.dictvalue,e.$refs[t.code+"PropRef"][0].doClose()},closedic:function(a){e.$refs[t.code+"PropRef"][0].doClose()}}}),a("div",{attrs:{slot:"reference"},slot:"reference"},[a("el-input",{attrs:{placeholder:"请选择"+t.label,clearable:"",size:t.size?t.size:"small",disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1)],1)],1):"textarea"==t.type?a("div",[a("el-input",{attrs:{placeholder:"请输入"+t.label,clearable:"",type:"textarea",size:t.size?t.size:"small",autosize:{minRows:2,maxRows:4},disabled:!!t.disabled&&e.returnEval(t.disabled),readonly:!!t.readonly&&e.returnEval(t.readonly)},model:{value:e.formdata[t.code],callback:function(a){e.$set(e.formdata,t.code,a)},expression:"formdata[b.code]"}})],1):"text"==t.type?a("div",[a("span",{staticStyle:{"font-size":"18px",color:"#606266"}},[e._v(e._s(e.formdata[t.code]))])]):"autocomplete"==t.type?a("div",["customer"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B1/getOnlinePageList",type:"客户",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"supplier"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B2/getOnlinePageList",type:"供应商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getSuppGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"workshop"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B3/getPageList",type:"生产车间",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getWorkGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"factory"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],baseurl:"/sale/D01M01B4/getPageList",type:"外协厂商",isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getFactGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"branch"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/sale/D01M01B5/getPageList",type:"部门"},on:{setRow:function(a){e.$emit("getBranGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"prospects"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled),baseurl:"/sale/D01M01B6/getPageList",type:"潜在客户"},on:{setRow:function(a){e.$emit("getProsGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"group"==t.searchtype?a("div",[a("GroupAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(a){e.$emit("getAllGroupName",a),e.cleValidate(t.code)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"store"==t.searchtype?a("div",[a("StoreAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getStoreName",t)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"proc"==t.searchtype?a("div",[a("ProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getProcName",t)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"roleproc"==t.searchtype?a("div",[a("RoleProcAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getRoleProcName",t)},autoClear:function(t){return e.$emit("autoClear")}}})],1):"goods"==t.searchtype?a("div",[a("GoodsAutoComplete",{attrs:{size:t.size,value:e.formdata[t.code],isdisabled:!!t.disabled&&e.returnEval(t.disabled)},on:{setRow:function(t){return e.$emit("getGoodsName",t)},autoClear:function(t){return e.$emit("autoClear")}}})],1):e._e()]):e._e()],1)],1)]):e._e()]}))],2)],1)]}))],2)],1)},h=[],b=a("e08f"),v=b["a"],g=(a("c7ebf"),Object(u["a"])(v,p,h,!1,null,"fe5bd2e4",null)),y=g.exports,_={},w=Object(u["a"])(_,i,o,!1,null,null,null),x=w.exports,k={components:{Header:f,Item:x,Footer:y},props:{formdata:{type:Object},formtemplate:{type:Object},selectform:{type:[Object,Array]}},methods:{getStoreName:function(e,t){this.$emit("getStoreName",e,t)}}},C=k,$=Object(u["a"])(C,r,n,!1,null,null,null);t["a"]=$.exports},e08f:function(module,__webpack_exports__,__webpack_require__){"use strict";var core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__=__webpack_require__("b64b"),core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0___default=__webpack_require__.n(core_js_modules_es_object_keys_js__WEBPACK_IMPORTED_MODULE_0__),_selDict_vue__WEBPACK_IMPORTED_MODULE_1__=__webpack_require__("64c9");__webpack_exports__["a"]={components:{selDictionaries:_selDict_vue__WEBPACK_IMPORTED_MODULE_1__["a"]},props:{title:{type:String,default:"单据标题"},formdata:{type:Object,default:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname}},formtemplate:{type:[Array],default:function(){return[]}}},data:function(){return{tempData:this.formtemplate}},watch:{formtemplate:function(e,t){console.log("foot",e),this.formtemplate.length?this.tempData=this.formtemplate:this.tempData=[{type:"divider",label:"",center:""},{type:"form",item:[{col:23,type:"input",code:"summary",label:"摘  要"}]},{type:"foot",item:[{col:4,type:"text",code:"createby",label:"创建人"},{col:4,type:"text",code:"createdate",label:"创建日期"},{col:4,type:"text",code:"lister",label:"制表"},{col:4,type:"text",code:"modifydate",label:"修改日期"},{col:4,type:"text",code:"assessor",label:"审核"},{col:4,type:"text",code:"assessdate",label:"审核日期"}]}]}},mounted:function(){},methods:{returnEval:function returnEval(data,itrue){return"string"==typeof data?eval(data):data},cleValidate:function(e){this.$refs.formdata.clearValidate(e)},clickMethods:function(e,t){var a={meth:e,param:t};this.$emit("clickMethods",a)}}}},e708:function(e,t,a){"use strict";a("038f")},e82a:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click","hide-on-click":!1,placement:"bottom"}},[a("el-button",{staticStyle:{padding:"7px 15px"},attrs:{size:"small",icon:"el-icon-edit-outline"}},[e._v("选择"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{staticClass:"quickTick myscrollbar",attrs:{slot:"dropdown"},slot:"dropdown"},[a("div",{staticClass:"fixed"},[a("el-dropdown-item",{staticStyle:{"border-bottom":"1px solid #edecec"}},[a("el-checkbox",{attrs:{indeterminate:e.isIndeterminate},on:{change:e.changeAll},model:{value:e.checkAll,callback:function(t){e.checkAll=t},expression:"checkAll"}},[e._v("全选")])],1)],1),e._l(e.droupList,(function(t,i){return a("el-dropdown-item",{key:i},[a("el-tooltip",{attrs:{effect:"dark",content:t.name,placement:"left"}},[a("el-checkbox",{attrs:{label:t.name},on:{change:function(a){return e.$emit("quickTickBtn",t)}},model:{value:t.show,callback:function(a){e.$set(t,"show",a)},expression:"item.show"}},[a("span",{staticClass:"quickTickLabel"},[e._v(e._s(t.name))])])],1)],1)}))],2)],1)],1)},o=[],r={props:["droupList"],data:function(){return{checkAll:!1,isIndeterminate:!1,searchVal:""}},created:function(){},watch:{droupList:function(e,t){console.log(e,"droupList"),this.checkAll=!0;for(var a=0;a<this.droupList.length;a++)if(!this.droupList[a].show){this.checkAll=!1;break}}},methods:{changeAll:function(e){console.log(e);for(var t=0;t<this.droupList.length;t++){var a=this.droupList[t];a.show=e,this.$emit("quickTickBtn",a)}}}},n=r,l=(a("2172"),a("2877")),s=Object(l["a"])(n,i,o,!1,null,"3c56a2fa",null);t["a"]=s.exports},f71d:function(e,t,a){a("a15b"),a("fb6a"),a("b0c0"),a("d3b7"),a("ac1f"),a("25f0"),a("466d"),
/*! @source http://purl.eligrey.com/github/Blob.js/blob/master/Blob.js */
function(e){"use strict";if(e.URL=e.URL||e.webkitURL,e.Blob&&e.URL)try{return void new Blob}catch(a){}var t=e.BlobBuilder||e.WebKitBlobBuilder||e.MozBlobBuilder||function(e){var t=function(e){return Object.prototype.toString.call(e).match(/^\[object\s(.*)\]$/)[1]},a=function(){this.data=[]},i=function(e,t,a){this.data=e,this.size=e.length,this.type=t,this.encoding=a},o=a.prototype,r=i.prototype,n=e.FileReaderSync,l=function(e){this.code=this[this.name=e]},s="NOT_FOUND_ERR SECURITY_ERR ABORT_ERR NOT_READABLE_ERR ENCODING_ERR NO_MODIFICATION_ALLOWED_ERR INVALID_STATE_ERR SYNTAX_ERR".split(" "),c=s.length,d=e.URL||e.webkitURL||e,u=d.createObjectURL,m=d.revokeObjectURL,f=d,p=e.btoa,h=e.atob,b=e.ArrayBuffer,v=e.Uint8Array;i.fake=r.fake=!0;while(c--)l.prototype[s[c]]=c+1;return d.createObjectURL||(f=e.URL={}),f.createObjectURL=function(e){var t,a=e.type;return null===a&&(a="application/octet-stream"),e instanceof i?(t="data:"+a,"base64"===e.encoding?t+";base64,"+e.data:"URI"===e.encoding?t+","+decodeURIComponent(e.data):p?t+";base64,"+p(e.data):t+","+encodeURIComponent(e.data)):u?u.call(d,e):void 0},f.revokeObjectURL=function(e){"data:"!==e.substring(0,5)&&m&&m.call(d,e)},o.append=function(e){var a=this.data;if(v&&(e instanceof b||e instanceof v)){for(var o="",r=new v(e),s=0,c=r.length;s<c;s++)o+=String.fromCharCode(r[s]);a.push(o)}else if("Blob"===t(e)||"File"===t(e)){if(!n)throw new l("NOT_READABLE_ERR");var d=new n;a.push(d.readAsBinaryString(e))}else e instanceof i?"base64"===e.encoding&&h?a.push(h(e.data)):"URI"===e.encoding?a.push(decodeURIComponent(e.data)):"raw"===e.encoding&&a.push(e.data):("string"!==typeof e&&(e+=""),a.push(unescape(encodeURIComponent(e))))},o.getBlob=function(e){return arguments.length||(e=null),new i(this.data.join(""),e,"raw")},o.toString=function(){return"[object BlobBuilder]"},r.slice=function(e,t,a){var o=arguments.length;return o<3&&(a=null),new i(this.data.slice(e,o>1?t:this.data.length),a,this.encoding)},r.toString=function(){return"[object Blob]"},r.close=function(){this.size=this.data.length=0},a}(e);e.Blob=function(e,a){var i=a&&a.type||"",o=new t;if(e)for(var r=0,n=e.length;r<n;r++)o.append(e[r]);return o.getBlob(i)}}("undefined"!==typeof self&&self||"undefined"!==typeof window&&window||this.content||this)}}]);