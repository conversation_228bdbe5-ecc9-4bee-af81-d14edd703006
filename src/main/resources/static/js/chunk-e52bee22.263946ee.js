(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e52bee22"],{13:function(t,e){},14:function(t,e){},15:function(t,e){},16:function(t,e){},17:function(t,e){},18:function(t,e){},2:function(t,e){},"25d6":function(t,e,a){"use strict";a("a007")},"41a0":function(t,e,a){},a007:function(t,e,a){},c50f4:function(t,e,a){"use strict";a.r(e);var o=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{ref:"index",staticClass:"index"},[a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{attrs:{span:24}},[a("TableList",{ref:"tableList"})],1)],1)],1)])])},n=[],r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"Company",staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"saveBtn"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("保存")])],1),a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":"100px",rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("name")}}},[a("el-form-item",{attrs:{label:"公司名称",prop:"name"}},[a("el-input",{attrs:{placeholder:"请输入公司名称",clearable:"",size:"small"},model:{value:t.formdata.name,callback:function(e){t.$set(t.formdata,"name",e)},expression:"formdata.name"}})],1)],1)]),a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("englishname")}}},[a("el-form-item",{attrs:{label:"英文名称",prop:"englishname"}},[a("el-input",{attrs:{placeholder:"请输入英文名称",clearable:"",size:"small"},model:{value:t.formdata.englishname,callback:function(e){t.$set(t.formdata,"englishname",e)},expression:"formdata.englishname"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("creditcode")}}},[a("el-form-item",{attrs:{label:"信用代码",prop:"creditcode"}},[a("el-input",{attrs:{placeholder:"请输入英文名称",clearable:"",size:"small"},model:{value:t.formdata.creditcode,callback:function(e){t.$set(t.formdata,"creditcode",e)},expression:"formdata.creditcode"}})],1)],1)]),a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("bankofdeposit")}}},[a("el-form-item",{attrs:{label:"开户银行",prop:"bankofdeposit"}},[a("el-input",{attrs:{placeholder:"请输入开户银行",clearable:"",size:"small"},model:{value:t.formdata.bankofdeposit,callback:function(e){t.$set(t.formdata,"bankofdeposit",e)},expression:"formdata.bankofdeposit"}})],1)],1)]),a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("bankaccount")}}},[a("el-form-item",{attrs:{label:"银行账号",prop:"bankaccount"}},[a("el-input",{attrs:{placeholder:"请输入银行账号",clearable:"",size:"small"},model:{value:t.formdata.bankaccount,callback:function(e){t.$set(t.formdata,"bankaccount",e)},expression:"formdata.bankaccount"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("contactperson")}}},[a("el-form-item",{attrs:{label:"联系人",prop:"contactperson"}},[a("el-input",{attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:t.formdata.contactperson,callback:function(e){t.$set(t.formdata,"contactperson",e)},expression:"formdata.contactperson"}})],1)],1)]),a("el-col",{attrs:{span:6}},[a("div",{on:{click:function(e){return t.cleValidate("tel")}}},[a("el-form-item",{attrs:{label:"联系电话",prop:"tel"}},[a("el-input",{attrs:{placeholder:"请输入联系电话",clearable:"",size:"small"},model:{value:t.formdata.tel,callback:function(e){t.$set(t.formdata,"tel",e)},expression:"formdata.tel"}})],1)],1)])],1),a("el-row",{staticStyle:{"margin-top":"0px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"公司地址","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{type:"textarea",placeholder:"请输入公司地址",clearable:""},model:{value:t.formdata.address,callback:function(e){t.$set(t.formdata,"address",e)},expression:"formdata.address"}})],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备 注","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入备注",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建人"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createby,expression:"formdata.createby"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.createby))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"创建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)},l=[],s=(a("99af"),a("e9c4"),a("b64b"),a("d3b7"),a("25f0"),a("4d90"),a("b775"));const c={add(t){return new Promise((e,a)=>{var o=JSON.stringify(t);s["a"].post("/SaCompany/save",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var o=JSON.stringify(t);s["a"].post("/SaCompany/update",o).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{s["a"].get("/SaCompany/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.msg)}).catch(t=>{a(t)})})}};var i=c,d=a("e7fc"),m=a.n(d),f=null,u=function(t){var e={clientId:"",port:t.mqttport,connectTimeout:4e3,reconnectPeriod:4e3,username:"inksinfo ",password:"inksinfo@123",keepalive:60};return f=m.a.connect(t.mqtturl,e),f.on("connect",(function(t){console.log("连接成功")})),f.on("error",(function(t){console.log("mqtt连接失败",t)})),f},p=u,b={data:function(){return{title:"公司信息",formdata:{id:"",name:"",englishname:"",creditcode:"",address:"",bankaccount:"",bankofdeposit:"",contactperson:"",tel:"",createdate:"",modifydate:new Date,lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,remark:""},formRules:{name:[{required:!0,trigger:"blur",message:"公司名称名称不能为空"}],creditcode:[{required:!0,trigger:"blur",message:"信用编码不能为空"}],bankaccount:[{required:!0,trigger:"blur",message:"银行账号不能为空"}]},currentId:1,formLabelWidth:"100px",topic:[]}},created:function(){this.binddata()},methods:{binddata:function(){var t=this;0!=this.currentId&&s["a"].get("/SaCompany/getEntity?key=".concat(this.currentId)).then((function(e){200==e.data.code?(console.log(e,"response"),t.formdata=e.data.data):t.$alert(e.data.code+" 读取异常，请刷新列表后重试","错误",{confirmButtonText:"确定",type:"warning",callback:function(e){t.$router.go(-1)}})})).catch((function(e){t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs["formdata"].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;i.add(this.formdata).then((function(e){200==e.code&&(t.$message.success("保存成功"),t.closeDialog(),t.sendMqttMsg(e.data))})).catch((function(e){t.$message.warning(e)}))},closeDialog:function(){this.$emit("closeDialog")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},dateFormats:function(){var t=new Date,e=t.getFullYear(),a=(t.getMonth()+1).toString().padStart(2,"0"),o=t.getDate().toString().padStart(2,"0");return"".concat(e,"-").concat(a,"-").concat(o)},sendMqttMsg:function(t){var e=p(this.$store.state.app.config),a=JSON.parse(localStorage.getItem("getInfo"));e.publish(JSON.parse(localStorage.getItem("topic")).send,JSON.stringify({msg:{log:{code:"Company",date:this.dateFormats(new Date),content:"姓名:".concat(a.realname,",IP:").concat(a.ipaddr,",公司名称:").concat(a.tenantinfo.company,",联系电话:").concat(a.tenantinfo.companytel,",sn:").concat(a.sn)},msgtype:"log",sn:a.sn},modulecode:"system"}),{qos:2},(function(t,e){t?console.log("发送信息失败"):console.log("发送信息成功")}))}},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),o=(e.getMonth()+1).toString().padStart(2,"0"),n=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),l=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(o,"-").concat(n," ").concat(r,":").concat(l,":").concat(s)}}},g=b,h=(a("25d6"),a("2877")),v=Object(h["a"])(g,r,l,!1,null,"8e404aee",null),k=v.exports,w={name:"SaCompany",components:{TableList:k},data:function(){return{title:"公司信息"}},mounted:function(){},methods:{}},S=w,x=(a("d071"),Object(h["a"])(S,o,n,!1,null,"67a1280c",null));e["default"]=x.exports},d071:function(t,e,a){"use strict";a("41a0")}}]);