(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e918a580"],{"0050":function(t,e,r){"use strict";(function(e){function r(t){return t instanceof e?e.from(t):new t.constructor(t.buffer.slice(),t.byteOffset,t.length)}function n(t){if(t=t||{},t.circles)return i(t);const e=new Map;if(e.set(Date,t=>new Date(t)),e.set(Map,(t,e)=>new Map(o(Array.from(t),e))),e.set(Set,(t,e)=>new Set(o(Array.from(t),e))),t.constructorHandlers)for(const r of t.constructorHandlers)e.set(r[0],r[1]);let n=null;return t.proto?a:s;function o(t,i){const o=Object.keys(t),s=new Array(o.length);for(let a=0;a<o.length;a++){const c=o[a],u=t[c];"object"!==typeof u||null===u?s[c]=u:u.constructor!==Object&&(n=e.get(u.constructor))?s[c]=n(u,i):ArrayBuffer.isView(u)?s[c]=r(u):s[c]=i(u)}return s}function s(t){if("object"!==typeof t||null===t)return t;if(Array.isArray(t))return o(t,s);if(t.constructor!==Object&&(n=e.get(t.constructor)))return n(t,s);const i={};for(const o in t){if(!1===Object.hasOwnProperty.call(t,o))continue;const a=t[o];"object"!==typeof a||null===a?i[o]=a:a.constructor!==Object&&(n=e.get(a.constructor))?i[o]=n(a,s):ArrayBuffer.isView(a)?i[o]=r(a):i[o]=s(a)}return i}function a(t){if("object"!==typeof t||null===t)return t;if(Array.isArray(t))return o(t,a);if(t.constructor!==Object&&(n=e.get(t.constructor)))return n(t,a);const i={};for(const o in t){const s=t[o];"object"!==typeof s||null===s?i[o]=s:s.constructor!==Object&&(n=e.get(s.constructor))?i[o]=n(s,a):ArrayBuffer.isView(s)?i[o]=r(s):i[o]=a(s)}return i}}function i(t){const e=[],n=[],i=new Map;if(i.set(Date,t=>new Date(t)),i.set(Map,(t,e)=>new Map(s(Array.from(t),e))),i.set(Set,(t,e)=>new Set(s(Array.from(t),e))),t.constructorHandlers)for(const r of t.constructorHandlers)i.set(r[0],r[1]);let o=null;return t.proto?c:a;function s(t,s){const a=Object.keys(t),c=new Array(a.length);for(let u=0;u<a.length;u++){const l=a[u],h=t[l];if("object"!==typeof h||null===h)c[l]=h;else if(h.constructor!==Object&&(o=i.get(h.constructor)))c[l]=o(h,s);else if(ArrayBuffer.isView(h))c[l]=r(h);else{const t=e.indexOf(h);c[l]=-1!==t?n[t]:s(h)}}return c}function a(t){if("object"!==typeof t||null===t)return t;if(Array.isArray(t))return s(t,a);if(t.constructor!==Object&&(o=i.get(t.constructor)))return o(t,a);const c={};e.push(t),n.push(c);for(const s in t){if(!1===Object.hasOwnProperty.call(t,s))continue;const u=t[s];if("object"!==typeof u||null===u)c[s]=u;else if(u.constructor!==Object&&(o=i.get(u.constructor)))c[s]=o(u,a);else if(ArrayBuffer.isView(u))c[s]=r(u);else{const t=e.indexOf(u);c[s]=-1!==t?n[t]:a(u)}}return e.pop(),n.pop(),c}function c(t){if("object"!==typeof t||null===t)return t;if(Array.isArray(t))return s(t,c);if(t.constructor!==Object&&(o=i.get(t.constructor)))return o(t,c);const a={};e.push(t),n.push(a);for(const s in t){const u=t[s];if("object"!==typeof u||null===u)a[s]=u;else if(u.constructor!==Object&&(o=i.get(u.constructor)))a[s]=o(u,c);else if(ArrayBuffer.isView(u))a[s]=r(u);else{const t=e.indexOf(u);a[s]=-1!==t?n[t]:c(u)}}return e.pop(),n.pop(),a}}t.exports=n}).call(this,r("1c35").Buffer)},"00ce":function(t,e,r){"use strict";var n,i=r("a284"),o=r("a645"),s=r("417f7"),a=r("dc99"),c=r("1409"),u=r("67ee"),l=r("0d25"),h=r("67d9"),f=r("17aa"),p=r("6591"),d=r("e050"),y=r("ab2b"),b=r("59eb"),g=r("c3ae5"),m=r("8ca0"),w=Function,v=function(t){try{return w('"use strict"; return ('+t+").constructor;")()}catch(e){}},_=r("2aa9"),S=r("71c9"),E=function(){throw new l},k=_?function(){try{return E}catch(t){try{return _(arguments,"callee").get}catch(e){return E}}}():E,A=r("5156")(),O=r("833a"),P=r("17bc"),R=r("f2e1"),M=r("e16f"),x=r("926d"),I={},T="undefined"!==typeof Uint8Array&&O?O(Uint8Array):n,j={__proto__:null,"%AggregateError%":"undefined"===typeof AggregateError?n:AggregateError,"%Array%":Array,"%ArrayBuffer%":"undefined"===typeof ArrayBuffer?n:ArrayBuffer,"%ArrayIteratorPrototype%":A&&O?O([][Symbol.iterator]()):n,"%AsyncFromSyncIteratorPrototype%":n,"%AsyncFunction%":I,"%AsyncGenerator%":I,"%AsyncGeneratorFunction%":I,"%AsyncIteratorPrototype%":I,"%Atomics%":"undefined"===typeof Atomics?n:Atomics,"%BigInt%":"undefined"===typeof BigInt?n:BigInt,"%BigInt64Array%":"undefined"===typeof BigInt64Array?n:BigInt64Array,"%BigUint64Array%":"undefined"===typeof BigUint64Array?n:BigUint64Array,"%Boolean%":Boolean,"%DataView%":"undefined"===typeof DataView?n:DataView,"%Date%":Date,"%decodeURI%":decodeURI,"%decodeURIComponent%":decodeURIComponent,"%encodeURI%":encodeURI,"%encodeURIComponent%":encodeURIComponent,"%Error%":o,"%eval%":eval,"%EvalError%":s,"%Float16Array%":"undefined"===typeof Float16Array?n:Float16Array,"%Float32Array%":"undefined"===typeof Float32Array?n:Float32Array,"%Float64Array%":"undefined"===typeof Float64Array?n:Float64Array,"%FinalizationRegistry%":"undefined"===typeof FinalizationRegistry?n:FinalizationRegistry,"%Function%":w,"%GeneratorFunction%":I,"%Int8Array%":"undefined"===typeof Int8Array?n:Int8Array,"%Int16Array%":"undefined"===typeof Int16Array?n:Int16Array,"%Int32Array%":"undefined"===typeof Int32Array?n:Int32Array,"%isFinite%":isFinite,"%isNaN%":isNaN,"%IteratorPrototype%":A&&O?O(O([][Symbol.iterator]())):n,"%JSON%":"object"===typeof JSON?JSON:n,"%Map%":"undefined"===typeof Map?n:Map,"%MapIteratorPrototype%":"undefined"!==typeof Map&&A&&O?O((new Map)[Symbol.iterator]()):n,"%Math%":Math,"%Number%":Number,"%Object%":i,"%Object.getOwnPropertyDescriptor%":_,"%parseFloat%":parseFloat,"%parseInt%":parseInt,"%Promise%":"undefined"===typeof Promise?n:Promise,"%Proxy%":"undefined"===typeof Proxy?n:Proxy,"%RangeError%":a,"%ReferenceError%":c,"%Reflect%":"undefined"===typeof Reflect?n:Reflect,"%RegExp%":RegExp,"%Set%":"undefined"===typeof Set?n:Set,"%SetIteratorPrototype%":"undefined"!==typeof Set&&A&&O?O((new Set)[Symbol.iterator]()):n,"%SharedArrayBuffer%":"undefined"===typeof SharedArrayBuffer?n:SharedArrayBuffer,"%String%":String,"%StringIteratorPrototype%":A&&O?O(""[Symbol.iterator]()):n,"%Symbol%":A?Symbol:n,"%SyntaxError%":u,"%ThrowTypeError%":k,"%TypedArray%":T,"%TypeError%":l,"%Uint8Array%":"undefined"===typeof Uint8Array?n:Uint8Array,"%Uint8ClampedArray%":"undefined"===typeof Uint8ClampedArray?n:Uint8ClampedArray,"%Uint16Array%":"undefined"===typeof Uint16Array?n:Uint16Array,"%Uint32Array%":"undefined"===typeof Uint32Array?n:Uint32Array,"%URIError%":h,"%WeakMap%":"undefined"===typeof WeakMap?n:WeakMap,"%WeakRef%":"undefined"===typeof WeakRef?n:WeakRef,"%WeakSet%":"undefined"===typeof WeakSet?n:WeakSet,"%Function.prototype.call%":x,"%Function.prototype.apply%":M,"%Object.defineProperty%":S,"%Object.getPrototypeOf%":P,"%Math.abs%":f,"%Math.floor%":p,"%Math.max%":d,"%Math.min%":y,"%Math.pow%":b,"%Math.round%":g,"%Math.sign%":m,"%Reflect.getPrototypeOf%":R};if(O)try{null.error}catch($){var C=O(O($));j["%Error.prototype%"]=C}var N=function t(e){var r;if("%AsyncFunction%"===e)r=v("async function () {}");else if("%GeneratorFunction%"===e)r=v("function* () {}");else if("%AsyncGeneratorFunction%"===e)r=v("async function* () {}");else if("%AsyncGenerator%"===e){var n=t("%AsyncGeneratorFunction%");n&&(r=n.prototype)}else if("%AsyncIteratorPrototype%"===e){var i=t("%AsyncGenerator%");i&&O&&(r=O(i.prototype))}return j[e]=r,r},B={__proto__:null,"%ArrayBufferPrototype%":["ArrayBuffer","prototype"],"%ArrayPrototype%":["Array","prototype"],"%ArrayProto_entries%":["Array","prototype","entries"],"%ArrayProto_forEach%":["Array","prototype","forEach"],"%ArrayProto_keys%":["Array","prototype","keys"],"%ArrayProto_values%":["Array","prototype","values"],"%AsyncFunctionPrototype%":["AsyncFunction","prototype"],"%AsyncGenerator%":["AsyncGeneratorFunction","prototype"],"%AsyncGeneratorPrototype%":["AsyncGeneratorFunction","prototype","prototype"],"%BooleanPrototype%":["Boolean","prototype"],"%DataViewPrototype%":["DataView","prototype"],"%DatePrototype%":["Date","prototype"],"%ErrorPrototype%":["Error","prototype"],"%EvalErrorPrototype%":["EvalError","prototype"],"%Float32ArrayPrototype%":["Float32Array","prototype"],"%Float64ArrayPrototype%":["Float64Array","prototype"],"%FunctionPrototype%":["Function","prototype"],"%Generator%":["GeneratorFunction","prototype"],"%GeneratorPrototype%":["GeneratorFunction","prototype","prototype"],"%Int8ArrayPrototype%":["Int8Array","prototype"],"%Int16ArrayPrototype%":["Int16Array","prototype"],"%Int32ArrayPrototype%":["Int32Array","prototype"],"%JSONParse%":["JSON","parse"],"%JSONStringify%":["JSON","stringify"],"%MapPrototype%":["Map","prototype"],"%NumberPrototype%":["Number","prototype"],"%ObjectPrototype%":["Object","prototype"],"%ObjProto_toString%":["Object","prototype","toString"],"%ObjProto_valueOf%":["Object","prototype","valueOf"],"%PromisePrototype%":["Promise","prototype"],"%PromiseProto_then%":["Promise","prototype","then"],"%Promise_all%":["Promise","all"],"%Promise_reject%":["Promise","reject"],"%Promise_resolve%":["Promise","resolve"],"%RangeErrorPrototype%":["RangeError","prototype"],"%ReferenceErrorPrototype%":["ReferenceError","prototype"],"%RegExpPrototype%":["RegExp","prototype"],"%SetPrototype%":["Set","prototype"],"%SharedArrayBufferPrototype%":["SharedArrayBuffer","prototype"],"%StringPrototype%":["String","prototype"],"%SymbolPrototype%":["Symbol","prototype"],"%SyntaxErrorPrototype%":["SyntaxError","prototype"],"%TypedArrayPrototype%":["TypedArray","prototype"],"%TypeErrorPrototype%":["TypeError","prototype"],"%Uint8ArrayPrototype%":["Uint8Array","prototype"],"%Uint8ClampedArrayPrototype%":["Uint8ClampedArray","prototype"],"%Uint16ArrayPrototype%":["Uint16Array","prototype"],"%Uint32ArrayPrototype%":["Uint32Array","prototype"],"%URIErrorPrototype%":["URIError","prototype"],"%WeakMapPrototype%":["WeakMap","prototype"],"%WeakSetPrototype%":["WeakSet","prototype"]},L=r("0f7c"),D=r("9671"),U=L.call(x,Array.prototype.concat),F=L.call(M,Array.prototype.splice),q=L.call(x,String.prototype.replace),W=L.call(x,String.prototype.slice),K=L.call(x,RegExp.prototype.exec),H=/[^%.[\]]+|\[(?:(-?\d+(?:\.\d+)?)|(["'])((?:(?!\2)[^\\]|\\.)*?)\2)\]|(?=(?:\.|\[\])(?:\.|\[\]|%$))/g,V=/\\(\\)?/g,G=function(t){var e=W(t,0,1),r=W(t,-1);if("%"===e&&"%"!==r)throw new u("invalid intrinsic syntax, expected closing `%`");if("%"===r&&"%"!==e)throw new u("invalid intrinsic syntax, expected opening `%`");var n=[];return q(t,H,(function(t,e,r,i){n[n.length]=r?q(i,V,"$1"):e||t})),n},Q=function(t,e){var r,n=t;if(D(B,n)&&(r=B[n],n="%"+r[0]+"%"),D(j,n)){var i=j[n];if(i===I&&(i=N(n)),"undefined"===typeof i&&!e)throw new l("intrinsic "+t+" exists, but is not available. Please file an issue!");return{alias:r,name:n,value:i}}throw new u("intrinsic "+t+" does not exist!")};t.exports=function(t,e){if("string"!==typeof t||0===t.length)throw new l("intrinsic name must be a non-empty string");if(arguments.length>1&&"boolean"!==typeof e)throw new l('"allowMissing" argument must be a boolean');if(null===K(/^%?[^%]*%?$/,t))throw new u("`%` may not be present anywhere but at the beginning and end of the intrinsic name");var r=G(t),n=r.length>0?r[0]:"",i=Q("%"+n+"%",e),o=i.name,s=i.value,a=!1,c=i.alias;c&&(n=c[0],F(r,U([0,1],c)));for(var h=1,f=!0;h<r.length;h+=1){var p=r[h],d=W(p,0,1),y=W(p,-1);if(('"'===d||"'"===d||"`"===d||'"'===y||"'"===y||"`"===y)&&d!==y)throw new u("property names with quotes must have matching quotes");if("constructor"!==p&&f||(a=!0),n+="."+p,o="%"+n+"%",D(j,o))s=j[o];else if(null!=s){if(!(p in s)){if(!e)throw new l("base intrinsic for "+t+" exists, but the property is not available.");return}if(_&&h+1>=r.length){var b=_(s,p);f=!!b,s=f&&"get"in b&&!("originalValue"in b.get)?b.get:s[p]}else f=D(s,p),s=s[p];f&&!a&&(j[o]=s)}}return s}},"035d":function(t,e,r){e=t.exports=r("85f8"),e.Stream=e,e.Readable=e,e.Writable=r("13a8"),e.Duplex=r("be3f"),e.Transform=r("3ca2"),e.PassThrough=r("7058"),e.finished=r("d9e1"),e.pipeline=r("652a")},"05ee":function(t,e,r){"use strict";const n=r(2),i=r("34eb")("mqttjs:tcp");function o(t,e){e.port=e.port||1883,e.hostname=e.hostname||e.host||"localhost";const r=e.port,o=e.hostname;return i("port %d and host %s",r,o),n.createConnection(r,o)}t.exports=o},"0b16":function(t,e,r){"use strict";var n=r("1985");function i(){this.protocol=null,this.slashes=null,this.auth=null,this.host=null,this.port=null,this.hostname=null,this.hash=null,this.search=null,this.query=null,this.pathname=null,this.path=null,this.href=null}var o=/^([a-z0-9.+-]+:)/i,s=/:[0-9]*$/,a=/^(\/\/?(?!\/)[^?\s]*)(\?[^\s]*)?$/,c=["<",">",'"',"`"," ","\r","\n","\t"],u=["{","}","|","\\","^","`"].concat(c),l=["'"].concat(u),h=["%","/","?",";","#"].concat(l),f=["/","?","#"],p=255,d=/^[+a-z0-9A-Z_-]{0,63}$/,y=/^([+a-z0-9A-Z_-]{0,63})(.*)$/,b={javascript:!0,"javascript:":!0},g={javascript:!0,"javascript:":!0},m={http:!0,https:!0,ftp:!0,gopher:!0,file:!0,"http:":!0,"https:":!0,"ftp:":!0,"gopher:":!0,"file:":!0},w=r("184d");function v(t,e,r){if(t&&"object"===typeof t&&t instanceof i)return t;var n=new i;return n.parse(t,e,r),n}function _(t){return"string"===typeof t&&(t=v(t)),t instanceof i?t.format():i.prototype.format.call(t)}function S(t,e){return v(t,!1,!0).resolve(e)}function E(t,e){return t?v(t,!1,!0).resolveObject(e):e}i.prototype.parse=function(t,e,r){if("string"!==typeof t)throw new TypeError("Parameter 'url' must be a string, not "+typeof t);var i=t.indexOf("?"),s=-1!==i&&i<t.indexOf("#")?"?":"#",c=t.split(s),u=/\\/g;c[0]=c[0].replace(u,"/"),t=c.join(s);var v=t;if(v=v.trim(),!r&&1===t.split("#").length){var _=a.exec(v);if(_)return this.path=v,this.href=v,this.pathname=_[1],_[2]?(this.search=_[2],this.query=e?w.parse(this.search.substr(1)):this.search.substr(1)):e&&(this.search="",this.query={}),this}var S=o.exec(v);if(S){S=S[0];var E=S.toLowerCase();this.protocol=E,v=v.substr(S.length)}if(r||S||v.match(/^\/\/[^@/]+@[^@/]+/)){var k="//"===v.substr(0,2);!k||S&&g[S]||(v=v.substr(2),this.slashes=!0)}if(!g[S]&&(k||S&&!m[S])){for(var A,O,P=-1,R=0;R<f.length;R++){var M=v.indexOf(f[R]);-1!==M&&(-1===P||M<P)&&(P=M)}O=-1===P?v.lastIndexOf("@"):v.lastIndexOf("@",P),-1!==O&&(A=v.slice(0,O),v=v.slice(O+1),this.auth=decodeURIComponent(A)),P=-1;for(R=0;R<h.length;R++){M=v.indexOf(h[R]);-1!==M&&(-1===P||M<P)&&(P=M)}-1===P&&(P=v.length),this.host=v.slice(0,P),v=v.slice(P),this.parseHost(),this.hostname=this.hostname||"";var x="["===this.hostname[0]&&"]"===this.hostname[this.hostname.length-1];if(!x)for(var I=this.hostname.split(/\./),T=(R=0,I.length);R<T;R++){var j=I[R];if(j&&!j.match(d)){for(var C="",N=0,B=j.length;N<B;N++)j.charCodeAt(N)>127?C+="x":C+=j[N];if(!C.match(d)){var L=I.slice(0,R),D=I.slice(R+1),U=j.match(y);U&&(L.push(U[1]),D.unshift(U[2])),D.length&&(v="/"+D.join(".")+v),this.hostname=L.join(".");break}}}this.hostname.length>p?this.hostname="":this.hostname=this.hostname.toLowerCase(),x||(this.hostname=n.toASCII(this.hostname));var F=this.port?":"+this.port:"",q=this.hostname||"";this.host=q+F,this.href+=this.host,x&&(this.hostname=this.hostname.substr(1,this.hostname.length-2),"/"!==v[0]&&(v="/"+v))}if(!b[E])for(R=0,T=l.length;R<T;R++){var W=l[R];if(-1!==v.indexOf(W)){var K=encodeURIComponent(W);K===W&&(K=escape(W)),v=v.split(W).join(K)}}var H=v.indexOf("#");-1!==H&&(this.hash=v.substr(H),v=v.slice(0,H));var V=v.indexOf("?");if(-1!==V?(this.search=v.substr(V),this.query=v.substr(V+1),e&&(this.query=w.parse(this.query)),v=v.slice(0,V)):e&&(this.search="",this.query={}),v&&(this.pathname=v),m[E]&&this.hostname&&!this.pathname&&(this.pathname="/"),this.pathname||this.search){F=this.pathname||"";var G=this.search||"";this.path=F+G}return this.href=this.format(),this},i.prototype.format=function(){var t=this.auth||"";t&&(t=encodeURIComponent(t),t=t.replace(/%3A/i,":"),t+="@");var e=this.protocol||"",r=this.pathname||"",n=this.hash||"",i=!1,o="";this.host?i=t+this.host:this.hostname&&(i=t+(-1===this.hostname.indexOf(":")?this.hostname:"["+this.hostname+"]"),this.port&&(i+=":"+this.port)),this.query&&"object"===typeof this.query&&Object.keys(this.query).length&&(o=w.stringify(this.query,{arrayFormat:"repeat",addQueryPrefix:!1}));var s=this.search||o&&"?"+o||"";return e&&":"!==e.substr(-1)&&(e+=":"),this.slashes||(!e||m[e])&&!1!==i?(i="//"+(i||""),r&&"/"!==r.charAt(0)&&(r="/"+r)):i||(i=""),n&&"#"!==n.charAt(0)&&(n="#"+n),s&&"?"!==s.charAt(0)&&(s="?"+s),r=r.replace(/[?#]/g,(function(t){return encodeURIComponent(t)})),s=s.replace("#","%23"),e+i+r+s+n},i.prototype.resolve=function(t){return this.resolveObject(v(t,!1,!0)).format()},i.prototype.resolveObject=function(t){if("string"===typeof t){var e=new i;e.parse(t,!1,!0),t=e}for(var r=new i,n=Object.keys(this),o=0;o<n.length;o++){var s=n[o];r[s]=this[s]}if(r.hash=t.hash,""===t.href)return r.href=r.format(),r;if(t.slashes&&!t.protocol){for(var a=Object.keys(t),c=0;c<a.length;c++){var u=a[c];"protocol"!==u&&(r[u]=t[u])}return m[r.protocol]&&r.hostname&&!r.pathname&&(r.pathname="/",r.path=r.pathname),r.href=r.format(),r}if(t.protocol&&t.protocol!==r.protocol){if(!m[t.protocol]){for(var l=Object.keys(t),h=0;h<l.length;h++){var f=l[h];r[f]=t[f]}return r.href=r.format(),r}if(r.protocol=t.protocol,t.host||g[t.protocol])r.pathname=t.pathname;else{var p=(t.pathname||"").split("/");while(p.length&&!(t.host=p.shift()));t.host||(t.host=""),t.hostname||(t.hostname=""),""!==p[0]&&p.unshift(""),p.length<2&&p.unshift(""),r.pathname=p.join("/")}if(r.search=t.search,r.query=t.query,r.host=t.host||"",r.auth=t.auth,r.hostname=t.hostname||t.host,r.port=t.port,r.pathname||r.search){var d=r.pathname||"",y=r.search||"";r.path=d+y}return r.slashes=r.slashes||t.slashes,r.href=r.format(),r}var b=r.pathname&&"/"===r.pathname.charAt(0),w=t.host||t.pathname&&"/"===t.pathname.charAt(0),v=w||b||r.host&&t.pathname,_=v,S=r.pathname&&r.pathname.split("/")||[],E=(p=t.pathname&&t.pathname.split("/")||[],r.protocol&&!m[r.protocol]);if(E&&(r.hostname="",r.port=null,r.host&&(""===S[0]?S[0]=r.host:S.unshift(r.host)),r.host="",t.protocol&&(t.hostname=null,t.port=null,t.host&&(""===p[0]?p[0]=t.host:p.unshift(t.host)),t.host=null),v=v&&(""===p[0]||""===S[0])),w)r.host=t.host||""===t.host?t.host:r.host,r.hostname=t.hostname||""===t.hostname?t.hostname:r.hostname,r.search=t.search,r.query=t.query,S=p;else if(p.length)S||(S=[]),S.pop(),S=S.concat(p),r.search=t.search,r.query=t.query;else if(null!=t.search){if(E){r.host=S.shift(),r.hostname=r.host;var k=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@");k&&(r.auth=k.shift(),r.hostname=k.shift(),r.host=r.hostname)}return r.search=t.search,r.query=t.query,null===r.pathname&&null===r.search||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.href=r.format(),r}if(!S.length)return r.pathname=null,r.search?r.path="/"+r.search:r.path=null,r.href=r.format(),r;for(var A=S.slice(-1)[0],O=(r.host||t.host||S.length>1)&&("."===A||".."===A)||""===A,P=0,R=S.length;R>=0;R--)A=S[R],"."===A?S.splice(R,1):".."===A?(S.splice(R,1),P++):P&&(S.splice(R,1),P--);if(!v&&!_)for(;P--;P)S.unshift("..");!v||""===S[0]||S[0]&&"/"===S[0].charAt(0)||S.unshift(""),O&&"/"!==S.join("/").substr(-1)&&S.push("");var M=""===S[0]||S[0]&&"/"===S[0].charAt(0);if(E){r.hostname=M?"":S.length?S.shift():"",r.host=r.hostname;k=!!(r.host&&r.host.indexOf("@")>0)&&r.host.split("@");k&&(r.auth=k.shift(),r.hostname=k.shift(),r.host=r.hostname)}return v=v||r.host&&S.length,v&&!M&&S.unshift(""),S.length>0?r.pathname=S.join("/"):(r.pathname=null,r.path=null),null===r.pathname&&null===r.search||(r.path=(r.pathname?r.pathname:"")+(r.search?r.search:"")),r.auth=t.auth||r.auth,r.slashes=r.slashes||t.slashes,r.href=r.format(),r},i.prototype.parseHost=function(){var t=this.host,e=s.exec(t);e&&(e=e[0],":"!==e&&(this.port=e.substr(1)),t=t.substr(0,t.length-e.length)),t&&(this.hostname=t)},e.parse=v,e.resolve=S,e.resolveObject=E,e.format=_,e.Url=i},"0d25":function(t,e,r){"use strict";t.exports=TypeError},"0e8b":function(t,e,r){"use strict";(function(e,n){var i;t.exports=M,M.ReadableState=R;r("faa1").EventEmitter;var o=function(t,e){return t.listeners(e).length},s=r("b98b"),a=r("1c35").Buffer,c=("undefined"!==typeof e?e:"undefined"!==typeof window?window:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function u(t){return a.from(t)}function l(t){return a.isBuffer(t)||t instanceof c}var h,f=r(15);h=f&&f.debuglog?f.debuglog("stream"):function(){};var p,d,y,b=r("e937"),g=r("f482"),m=r("86c6"),w=m.getHighWaterMark,v=r("9bfc").codes,_=v.ERR_INVALID_ARG_TYPE,S=v.ERR_STREAM_PUSH_AFTER_EOF,E=v.ERR_METHOD_NOT_IMPLEMENTED,k=v.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;r("3fb5")(M,s);var A=g.errorOrDestroy,O=["error","close","destroy","pause","resume"];function P(t,e,r){if("function"===typeof t.prependListener)return t.prependListener(e,r);t._events&&t._events[e]?Array.isArray(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]:t.on(e,r)}function R(t,e,n){i=i||r("a493"),t=t||{},"boolean"!==typeof n&&(n=e instanceof i),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=w(this,t,"readableHighWaterMark",n),this.buffer=new b,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(p||(p=r("7d72").StringDecoder),this.decoder=new p(t.encoding),this.encoding=t.encoding)}function M(t){if(i=i||r("a493"),!(this instanceof M))return new M(t);var e=this instanceof i;this._readableState=new R(t,this,e),this.readable=!0,t&&("function"===typeof t.read&&(this._read=t.read),"function"===typeof t.destroy&&(this._destroy=t.destroy)),s.call(this)}function x(t,e,r,n,i){h("readableAddChunk",e);var o,s=t._readableState;if(null===e)s.reading=!1,B(t,s);else if(i||(o=T(s,e)),o)A(t,o);else if(s.objectMode||e&&e.length>0)if("string"===typeof e||s.objectMode||Object.getPrototypeOf(e)===a.prototype||(e=u(e)),n)s.endEmitted?A(t,new k):I(t,s,e,!0);else if(s.ended)A(t,new S);else{if(s.destroyed)return!1;s.reading=!1,s.decoder&&!r?(e=s.decoder.write(e),s.objectMode||0!==e.length?I(t,s,e,!1):U(t,s)):I(t,s,e,!1)}else n||(s.reading=!1,U(t,s));return!s.ended&&(s.length<s.highWaterMark||0===s.length)}function I(t,e,r,n){e.flowing&&0===e.length&&!e.sync?(e.awaitDrain=0,t.emit("data",r)):(e.length+=e.objectMode?1:r.length,n?e.buffer.unshift(r):e.buffer.push(r),e.needReadable&&L(t)),U(t,e)}function T(t,e){var r;return l(e)||"string"===typeof e||void 0===e||t.objectMode||(r=new _("chunk",["string","Buffer","Uint8Array"],e)),r}Object.defineProperty(M.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),M.prototype.destroy=g.destroy,M.prototype._undestroy=g.undestroy,M.prototype._destroy=function(t,e){e(t)},M.prototype.push=function(t,e){var r,n=this._readableState;return n.objectMode?r=!0:"string"===typeof t&&(e=e||n.defaultEncoding,e!==n.encoding&&(t=a.from(t,e),e=""),r=!0),x(this,t,e,!1,r)},M.prototype.unshift=function(t){return x(this,t,null,!0,!1)},M.prototype.isPaused=function(){return!1===this._readableState.flowing},M.prototype.setEncoding=function(t){p||(p=r("7d72").StringDecoder);var e=new p(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;var n=this._readableState.buffer.head,i="";while(null!==n)i+=e.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this};var j=1073741824;function C(t){return t>=j?t=j:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}function N(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!==t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=C(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function B(t,e){if(h("onEofChunk"),!e.ended){if(e.decoder){var r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,e.sync?L(t):(e.needReadable=!1,e.emittedReadable||(e.emittedReadable=!0,D(t)))}}function L(t){var e=t._readableState;h("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||(h("emitReadable",e.flowing),e.emittedReadable=!0,n.nextTick(D,t))}function D(t){var e=t._readableState;h("emitReadable_",e.destroyed,e.length,e.ended),e.destroyed||!e.length&&!e.ended||(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,G(t)}function U(t,e){e.readingMore||(e.readingMore=!0,n.nextTick(F,t,e))}function F(t,e){while(!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&0===e.length)){var r=e.length;if(h("maybeReadMore read 0"),t.read(0),r===e.length)break}e.readingMore=!1}function q(t){return function(){var e=t._readableState;h("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&o(t,"data")&&(e.flowing=!0,G(t))}}function W(t){var e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&!e.paused?e.flowing=!0:t.listenerCount("data")>0&&t.resume()}function K(t){h("readable nexttick read 0"),t.read(0)}function H(t,e){e.resumeScheduled||(e.resumeScheduled=!0,n.nextTick(V,t,e))}function V(t,e){h("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),G(t),e.flowing&&!e.reading&&t.read(0)}function G(t){var e=t._readableState;h("flow",e.flowing);while(e.flowing&&null!==t.read());}function Q(t,e){return 0===e.length?null:(e.objectMode?r=e.buffer.shift():!t||t>=e.length?(r=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.first():e.buffer.concat(e.length),e.buffer.clear()):r=e.buffer.consume(t,e.decoder),r);var r}function $(t){var e=t._readableState;h("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,n.nextTick(z,e,t))}function z(t,e){if(h("endReadableNT",t.endEmitted,t.length),!t.endEmitted&&0===t.length&&(t.endEmitted=!0,e.readable=!1,e.emit("end"),t.autoDestroy)){var r=e._writableState;(!r||r.autoDestroy&&r.finished)&&e.destroy()}}function J(t,e){for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}M.prototype.read=function(t){h("read",t),t=parseInt(t,10);var e=this._readableState,r=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&((0!==e.highWaterMark?e.length>=e.highWaterMark:e.length>0)||e.ended))return h("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?$(this):L(this),null;if(t=N(t,e),0===t&&e.ended)return 0===e.length&&$(this),null;var n,i=e.needReadable;return h("need readable",i),(0===e.length||e.length-t<e.highWaterMark)&&(i=!0,h("length less than watermark",i)),e.ended||e.reading?(i=!1,h("reading or ended",i)):i&&(h("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=N(r,e))),n=t>0?Q(t,e):null,null===n?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.awaitDrain=0),0===e.length&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&$(this)),null!==n&&this.emit("data",n),n},M.prototype._read=function(t){A(this,new E("_read()"))},M.prototype.pipe=function(t,e){var r=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=t;break;case 1:i.pipes=[i.pipes,t];break;default:i.pipes.push(t);break}i.pipesCount+=1,h("pipe count=%d opts=%j",i.pipesCount,e);var s=(!e||!1!==e.end)&&t!==n.stdout&&t!==n.stderr,a=s?u:m;function c(t,e){h("onunpipe"),t===r&&e&&!1===e.hasUnpiped&&(e.hasUnpiped=!0,p())}function u(){h("onend"),t.end()}i.endEmitted?n.nextTick(a):r.once("end",a),t.on("unpipe",c);var l=q(r);t.on("drain",l);var f=!1;function p(){h("cleanup"),t.removeListener("close",b),t.removeListener("finish",g),t.removeListener("drain",l),t.removeListener("error",y),t.removeListener("unpipe",c),r.removeListener("end",u),r.removeListener("end",m),r.removeListener("data",d),f=!0,!i.awaitDrain||t._writableState&&!t._writableState.needDrain||l()}function d(e){h("ondata");var n=t.write(e);h("dest.write",n),!1===n&&((1===i.pipesCount&&i.pipes===t||i.pipesCount>1&&-1!==J(i.pipes,t))&&!f&&(h("false write response, pause",i.awaitDrain),i.awaitDrain++),r.pause())}function y(e){h("onerror",e),m(),t.removeListener("error",y),0===o(t,"error")&&A(t,e)}function b(){t.removeListener("finish",g),m()}function g(){h("onfinish"),t.removeListener("close",b),m()}function m(){h("unpipe"),r.unpipe(t)}return r.on("data",d),P(t,"error",y),t.once("close",b),t.once("finish",g),t.emit("pipe",r),i.flowing||(h("pipe resume"),r.resume()),t},M.prototype.unpipe=function(t){var e=this._readableState,r={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,r)),this;if(!t){var n=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=J(e.pipes,t);return-1===s||(e.pipes.splice(s,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,r)),this},M.prototype.on=function(t,e){var r=s.prototype.on.call(this,t,e),i=this._readableState;return"data"===t?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"===t&&(i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,h("on readable",i.length,i.reading),i.length?L(this):i.reading||n.nextTick(K,this))),r},M.prototype.addListener=M.prototype.on,M.prototype.removeListener=function(t,e){var r=s.prototype.removeListener.call(this,t,e);return"readable"===t&&n.nextTick(W,this),r},M.prototype.removeAllListeners=function(t){var e=s.prototype.removeAllListeners.apply(this,arguments);return"readable"!==t&&void 0!==t||n.nextTick(W,this),e},M.prototype.resume=function(){var t=this._readableState;return t.flowing||(h("resume"),t.flowing=!t.readableListening,H(this,t)),t.paused=!1,this},M.prototype.pause=function(){return h("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(h("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},M.prototype.wrap=function(t){var e=this,r=this._readableState,n=!1;for(var i in t.on("end",(function(){if(h("wrapped end"),r.decoder&&!r.ended){var t=r.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(i){if(h("wrapped data"),r.decoder&&(i=r.decoder.write(i)),(!r.objectMode||null!==i&&void 0!==i)&&(r.objectMode||i&&i.length)){var o=e.push(i);o||(n=!0,t.pause())}})),t)void 0===this[i]&&"function"===typeof t[i]&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));for(var o=0;o<O.length;o++)t.on(O[o],this.emit.bind(this,O[o]));return this._read=function(e){h("wrapped _read",e),n&&(n=!1,t.resume())},this},"function"===typeof Symbol&&(M.prototype[Symbol.asyncIterator]=function(){return void 0===d&&(d=r("782c")),d(this)}),Object.defineProperty(M.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(M.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(M.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}}),M._fromList=Q,Object.defineProperty(M.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"===typeof Symbol&&(M.from=function(t,e){return void 0===y&&(y=r("a50f")),y(M,t,e)})}).call(this,r("c8ba"),r("4362"))},"0f7c":function(t,e,r){"use strict";var n=r("688e");t.exports=Function.prototype.bind||n},"13a8":function(t,e,r){"use strict";(function(e,n){function i(t){var e=this;this.next=null,this.entry=null,this.finish=function(){V(e,t)}}var o;t.exports=R,R.WritableState=P;var s={deprecate:r("b7d1")},a=r("9ede"),c=r("1c35").Buffer,u=("undefined"!==typeof e?e:"undefined"!==typeof window?window:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function l(t){return c.from(t)}function h(t){return c.isBuffer(t)||t instanceof u}var f,p=r("edb3"),d=r("31b5"),y=d.getHighWaterMark,b=r("fbd7").codes,g=b.ERR_INVALID_ARG_TYPE,m=b.ERR_METHOD_NOT_IMPLEMENTED,w=b.ERR_MULTIPLE_CALLBACK,v=b.ERR_STREAM_CANNOT_PIPE,_=b.ERR_STREAM_DESTROYED,S=b.ERR_STREAM_NULL_VALUES,E=b.ERR_STREAM_WRITE_AFTER_END,k=b.ERR_UNKNOWN_ENCODING,A=p.errorOrDestroy;function O(){}function P(t,e,n){o=o||r("be3f"),t=t||{},"boolean"!==typeof n&&(n=e instanceof o),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode),this.highWaterMark=y(this,t,"writableHighWaterMark",n),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var s=!1===t.decodeStrings;this.decodeStrings=!s,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){B(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new i(this)}function R(t){o=o||r("be3f");var e=this instanceof o;if(!e&&!f.call(R,this))return new R(t);this._writableState=new P(t,this,e),this.writable=!0,t&&("function"===typeof t.write&&(this._write=t.write),"function"===typeof t.writev&&(this._writev=t.writev),"function"===typeof t.destroy&&(this._destroy=t.destroy),"function"===typeof t.final&&(this._final=t.final)),a.call(this)}function M(t,e){var r=new E;A(t,r),n.nextTick(e,r)}function x(t,e,r,i){var o;return null===r?o=new S:"string"===typeof r||e.objectMode||(o=new g("chunk",["string","Buffer"],r)),!o||(A(t,o),n.nextTick(i,o),!1)}function I(t,e,r){return t.objectMode||!1===t.decodeStrings||"string"!==typeof e||(e=c.from(e,r)),e}function T(t,e,r,n,i,o){if(!r){var s=I(e,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=e.objectMode?1:n.length;e.length+=a;var c=e.length<e.highWaterMark;if(c||(e.needDrain=!0),e.writing||e.corked){var u=e.lastBufferedRequest;e.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},u?u.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else j(t,e,!1,a,n,i,o);return c}function j(t,e,r,n,i,o,s){e.writelen=n,e.writecb=s,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new _("write")):r?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function C(t,e,r,i,o){--e.pendingcb,r?(n.nextTick(o,i),n.nextTick(K,t,e),t._writableState.errorEmitted=!0,A(t,i)):(o(i),t._writableState.errorEmitted=!0,A(t,i),K(t,e))}function N(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}function B(t,e){var r=t._writableState,i=r.sync,o=r.writecb;if("function"!==typeof o)throw new w;if(N(r),e)C(t,r,i,e,o);else{var s=F(r)||t.destroyed;s||r.corked||r.bufferProcessing||!r.bufferedRequest||U(t,r),i?n.nextTick(L,t,r,s,o):L(t,r,s,o)}}function L(t,e,r,n){r||D(t,e),e.pendingcb--,n(),K(t,e)}function D(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}function U(t,e){e.bufferProcessing=!0;var r=e.bufferedRequest;if(t._writev&&r&&r.next){var n=e.bufferedRequestCount,o=new Array(n),s=e.corkedRequestsFree;s.entry=r;var a=0,c=!0;while(r)o[a]=r,r.isBuf||(c=!1),r=r.next,a+=1;o.allBuffers=c,j(t,e,!0,e.length,o,"",s.finish),e.pendingcb++,e.lastBufferedRequest=null,s.next?(e.corkedRequestsFree=s.next,s.next=null):e.corkedRequestsFree=new i(e),e.bufferedRequestCount=0}else{while(r){var u=r.chunk,l=r.encoding,h=r.callback,f=e.objectMode?1:u.length;if(j(t,e,!1,f,u,l,h),r=r.next,e.bufferedRequestCount--,e.writing)break}null===r&&(e.lastBufferedRequest=null)}e.bufferedRequest=r,e.bufferProcessing=!1}function F(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function q(t,e){t._final((function(r){e.pendingcb--,r&&A(t,r),e.prefinished=!0,t.emit("prefinish"),K(t,e)}))}function W(t,e){e.prefinished||e.finalCalled||("function"!==typeof t._final||e.destroyed?(e.prefinished=!0,t.emit("prefinish")):(e.pendingcb++,e.finalCalled=!0,n.nextTick(q,t,e)))}function K(t,e){var r=F(e);if(r&&(W(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"),e.autoDestroy))){var n=t._readableState;(!n||n.autoDestroy&&n.endEmitted)&&t.destroy()}return r}function H(t,e,r){e.ending=!0,K(t,e),r&&(e.finished?n.nextTick(r):t.once("finish",r)),e.ended=!0,t.writable=!1}function V(t,e,r){var n=t.entry;t.entry=null;while(n){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree.next=t}r("3fb5")(R,a),P.prototype.getBuffer=function(){var t=this.bufferedRequest,e=[];while(t)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(P.prototype,"buffer",{get:s.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"===typeof Symbol&&Symbol.hasInstance&&"function"===typeof Function.prototype[Symbol.hasInstance]?(f=Function.prototype[Symbol.hasInstance],Object.defineProperty(R,Symbol.hasInstance,{value:function(t){return!!f.call(this,t)||this===R&&(t&&t._writableState instanceof P)}})):f=function(t){return t instanceof this},R.prototype.pipe=function(){A(this,new v)},R.prototype.write=function(t,e,r){var n=this._writableState,i=!1,o=!n.objectMode&&h(t);return o&&!c.isBuffer(t)&&(t=l(t)),"function"===typeof e&&(r=e,e=null),o?e="buffer":e||(e=n.defaultEncoding),"function"!==typeof r&&(r=O),n.ending?M(this,r):(o||x(this,n,t,r))&&(n.pendingcb++,i=T(this,n,o,t,e,r)),i},R.prototype.cork=function(){this._writableState.corked++},R.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.bufferProcessing||!t.bufferedRequest||U(this,t))},R.prototype.setDefaultEncoding=function(t){if("string"===typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new k(t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(R.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(R.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),R.prototype._write=function(t,e,r){r(new m("_write()"))},R.prototype._writev=null,R.prototype.end=function(t,e,r){var n=this._writableState;return"function"===typeof t?(r=t,t=null,e=null):"function"===typeof e&&(r=e,e=null),null!==t&&void 0!==t&&this.write(t,e),n.corked&&(n.corked=1,this.uncork()),n.ending||H(this,n,r),this},Object.defineProperty(R.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(R.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),R.prototype.destroy=p.destroy,R.prototype._undestroy=p.undestroy,R.prototype._destroy=function(t,e){e(t)}}).call(this,r("c8ba"),r("4362"))},1409:function(t,e,r){"use strict";t.exports=ReferenceError},1468:function(t,e){var r=1e3,n=60*r,i=60*n,o=24*i,s=7*o,a=365.25*o;function c(t){if(t=String(t),!(t.length>100)){var e=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(t);if(e){var c=parseFloat(e[1]),u=(e[2]||"ms").toLowerCase();switch(u){case"years":case"year":case"yrs":case"yr":case"y":return c*a;case"weeks":case"week":case"w":return c*s;case"days":case"day":case"d":return c*o;case"hours":case"hour":case"hrs":case"hr":case"h":return c*i;case"minutes":case"minute":case"mins":case"min":case"m":return c*n;case"seconds":case"second":case"secs":case"sec":case"s":return c*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return c;default:return}}}}function u(t){var e=Math.abs(t);return e>=o?Math.round(t/o)+"d":e>=i?Math.round(t/i)+"h":e>=n?Math.round(t/n)+"m":e>=r?Math.round(t/r)+"s":t+"ms"}function l(t){var e=Math.abs(t);return e>=o?h(t,e,o,"day"):e>=i?h(t,e,i,"hour"):e>=n?h(t,e,n,"minute"):e>=r?h(t,e,r,"second"):t+" ms"}function h(t,e,r,n){var i=e>=1.5*r;return Math.round(t/r)+" "+n+(i?"s":"")}t.exports=function(t,e){e=e||{};var r=typeof t;if("string"===r&&t.length>0)return c(t);if("number"===r&&isFinite(t))return e.long?l(t):u(t);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(t))}},1696:function(t,e,r){"use strict";t.exports=function(){if("function"!==typeof Symbol||"function"!==typeof Object.getOwnPropertySymbols)return!1;if("symbol"===typeof Symbol.iterator)return!0;var t={},e=Symbol("test"),r=Object(e);if("string"===typeof e)return!1;if("[object Symbol]"!==Object.prototype.toString.call(e))return!1;if("[object Symbol]"!==Object.prototype.toString.call(r))return!1;var n=42;for(var i in t[e]=n,t)return!1;if("function"===typeof Object.keys&&0!==Object.keys(t).length)return!1;if("function"===typeof Object.getOwnPropertyNames&&0!==Object.getOwnPropertyNames(t).length)return!1;var o=Object.getOwnPropertySymbols(t);if(1!==o.length||o[0]!==e)return!1;if(!Object.prototype.propertyIsEnumerable.call(t,e))return!1;if("function"===typeof Object.getOwnPropertyDescriptor){var s=Object.getOwnPropertyDescriptor(t,e);if(s.value!==n||!0!==s.enumerable)return!1}return!0}},"17aa":function(t,e,r){"use strict";t.exports=Math.abs},"17bc":function(t,e,r){"use strict";var n=r("a284");t.exports=n.getPrototypeOf||null},"184d":function(t,e,r){"use strict";var n=r("f177"),i=r("2500"),o=r("bbc7");t.exports={formats:o,parse:i,stringify:n}},1985:function(t,e,r){(function(t,n){var i;/*! https://mths.be/punycode v1.4.1 by @mathias */(function(o){e&&e.nodeType,t&&t.nodeType;var s="object"==typeof n&&n;s.global!==s&&s.window!==s&&s.self;var a,c=2147483647,u=36,l=1,h=26,f=38,p=700,d=72,y=128,b="-",g=/^xn--/,m=/[^\x20-\x7E]/,w=/[\x2E\u3002\uFF0E\uFF61]/g,v={overflow:"Overflow: input needs wider integers to process","not-basic":"Illegal input >= 0x80 (not a basic code point)","invalid-input":"Invalid input"},_=u-l,S=Math.floor,E=String.fromCharCode;function k(t){throw new RangeError(v[t])}function A(t,e){var r=t.length,n=[];while(r--)n[r]=e(t[r]);return n}function O(t,e){var r=t.split("@"),n="";r.length>1&&(n=r[0]+"@",t=r[1]),t=t.replace(w,".");var i=t.split("."),o=A(i,e).join(".");return n+o}function P(t){var e,r,n=[],i=0,o=t.length;while(i<o)e=t.charCodeAt(i++),e>=55296&&e<=56319&&i<o?(r=t.charCodeAt(i++),56320==(64512&r)?n.push(((1023&e)<<10)+(1023&r)+65536):(n.push(e),i--)):n.push(e);return n}function R(t){return A(t,(function(t){var e="";return t>65535&&(t-=65536,e+=E(t>>>10&1023|55296),t=56320|1023&t),e+=E(t),e})).join("")}function M(t){return t-48<10?t-22:t-65<26?t-65:t-97<26?t-97:u}function x(t,e){return t+22+75*(t<26)-((0!=e)<<5)}function I(t,e,r){var n=0;for(t=r?S(t/p):t>>1,t+=S(t/e);t>_*h>>1;n+=u)t=S(t/_);return S(n+(_+1)*t/(t+f))}function T(t){var e,r,n,i,o,s,a,f,p,g,m=[],w=t.length,v=0,_=y,E=d;for(r=t.lastIndexOf(b),r<0&&(r=0),n=0;n<r;++n)t.charCodeAt(n)>=128&&k("not-basic"),m.push(t.charCodeAt(n));for(i=r>0?r+1:0;i<w;){for(o=v,s=1,a=u;;a+=u){if(i>=w&&k("invalid-input"),f=M(t.charCodeAt(i++)),(f>=u||f>S((c-v)/s))&&k("overflow"),v+=f*s,p=a<=E?l:a>=E+h?h:a-E,f<p)break;g=u-p,s>S(c/g)&&k("overflow"),s*=g}e=m.length+1,E=I(v-o,e,0==o),S(v/e)>c-_&&k("overflow"),_+=S(v/e),v%=e,m.splice(v++,0,_)}return R(m)}function j(t){var e,r,n,i,o,s,a,f,p,g,m,w,v,_,A,O=[];for(t=P(t),w=t.length,e=y,r=0,o=d,s=0;s<w;++s)m=t[s],m<128&&O.push(E(m));n=i=O.length,i&&O.push(b);while(n<w){for(a=c,s=0;s<w;++s)m=t[s],m>=e&&m<a&&(a=m);for(v=n+1,a-e>S((c-r)/v)&&k("overflow"),r+=(a-e)*v,e=a,s=0;s<w;++s)if(m=t[s],m<e&&++r>c&&k("overflow"),m==e){for(f=r,p=u;;p+=u){if(g=p<=o?l:p>=o+h?h:p-o,f<g)break;A=f-g,_=u-g,O.push(E(x(g+A%_,0))),f=S(A/_)}O.push(E(x(f,0))),o=I(r,v,n==i),r=0,++n}++r,++e}return O.join("")}function C(t){return O(t,(function(t){return g.test(t)?T(t.slice(4).toLowerCase()):t}))}function N(t){return O(t,(function(t){return m.test(t)?"xn--"+j(t):t}))}a={version:"1.4.1",ucs2:{decode:P,encode:R},decode:T,encode:j,toASCII:N,toUnicode:C},i=function(){return a}.call(e,r,e,t),void 0===i||(t.exports=i)})()}).call(this,r("62e4")(t),r("c8ba"))},"1e4d":function(t,e,r){"use strict";function n(){if(!(this instanceof n))return new n;this.nextId=Math.max(1,Math.floor(65535*Math.random()))}n.prototype.allocate=function(){const t=this.nextId++;return 65536===this.nextId&&(this.nextId=1),t},n.prototype.getLastAllocated=function(){return 1===this.nextId?65535:this.nextId-1},n.prototype.register=function(t){return!0},n.prototype.deallocate=function(t){},n.prototype.clear=function(){},t.exports=n},"1fad":function(t,e,r){const n=r("51e9"),i=r("faa1"),o=r("a7c9"),s=r("b289"),a=r("34eb")("mqtt-packet:parser");class c extends i{constructor(){super(),this.parser=this.constructor.parser}static parser(t){return this instanceof c?(this.settings=t||{},this._states=["_parseHeader","_parseLength","_parsePayload","_newPacket"],this._resetState(),this):(new c).parser(t)}_resetState(){a("_resetState: resetting packet, error, _list, and _stateCounter"),this.packet=new o,this.error=null,this._list=n(),this._stateCounter=0}parse(t){this.error&&this._resetState(),this._list.append(t),a("parse: current state: %s",this._states[this._stateCounter]);while((-1!==this.packet.length||this._list.length>0)&&this[this._states[this._stateCounter]]()&&!this.error)this._stateCounter++,a("parse: state complete. _stateCounter is now: %d",this._stateCounter),a("parse: packet.length: %d, buffer list length: %d",this.packet.length,this._list.length),this._stateCounter>=this._states.length&&(this._stateCounter=0);return a("parse: exited while loop. packet: %d, buffer list length: %d",this.packet.length,this._list.length),this._list.length}_parseHeader(){const t=this._list.readUInt8(0);return this.packet.cmd=s.types[t>>s.CMD_SHIFT],this.packet.retain=0!==(t&s.RETAIN_MASK),this.packet.qos=t>>s.QOS_SHIFT&s.QOS_MASK,this.packet.dup=0!==(t&s.DUP_MASK),a("_parseHeader: packet: %o",this.packet),this._list.consume(1),!0}_parseLength(){const t=this._parseVarByteNum(!0);return t&&(this.packet.length=t.value,this._list.consume(t.bytes)),a("_parseLength %d",t.value),!!t}_parsePayload(){a("_parsePayload: payload %O",this._list);let t=!1;if(0===this.packet.length||this._list.length>=this.packet.length){switch(this._pos=0,this.packet.cmd){case"connect":this._parseConnect();break;case"connack":this._parseConnack();break;case"publish":this._parsePublish();break;case"puback":case"pubrec":case"pubrel":case"pubcomp":this._parseConfirmation();break;case"subscribe":this._parseSubscribe();break;case"suback":this._parseSuback();break;case"unsubscribe":this._parseUnsubscribe();break;case"unsuback":this._parseUnsuback();break;case"pingreq":case"pingresp":break;case"disconnect":this._parseDisconnect();break;case"auth":this._parseAuth();break;default:this._emitError(new Error("Not supported"))}t=!0}return a("_parsePayload complete result: %s",t),t}_parseConnect(){let t,e,r,n;a("_parseConnect");const i={},o=this.packet,c=this._parseString();if(null===c)return this._emitError(new Error("Cannot parse protocolId"));if("MQTT"!==c&&"MQIsdp"!==c)return this._emitError(new Error("Invalid protocolId"));if(o.protocolId=c,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(o.protocolVersion=this._list.readUInt8(this._pos),o.protocolVersion>=128&&(o.bridgeMode=!0,o.protocolVersion=o.protocolVersion-128),3!==o.protocolVersion&&4!==o.protocolVersion&&5!==o.protocolVersion)return this._emitError(new Error("Invalid protocol version"));if(this._pos++,this._pos>=this._list.length)return this._emitError(new Error("Packet too short"));if(i.username=this._list.readUInt8(this._pos)&s.USERNAME_MASK,i.password=this._list.readUInt8(this._pos)&s.PASSWORD_MASK,i.will=this._list.readUInt8(this._pos)&s.WILL_FLAG_MASK,i.will&&(o.will={},o.will.retain=0!==(this._list.readUInt8(this._pos)&s.WILL_RETAIN_MASK),o.will.qos=(this._list.readUInt8(this._pos)&s.WILL_QOS_MASK)>>s.WILL_QOS_SHIFT),o.clean=0!==(this._list.readUInt8(this._pos)&s.CLEAN_SESSION_MASK),this._pos++,o.keepalive=this._parseNum(),-1===o.keepalive)return this._emitError(new Error("Packet too short"));if(5===o.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(o.properties=t)}const u=this._parseString();if(null===u)return this._emitError(new Error("Packet too short"));if(o.clientId=u,a("_parseConnect: packet.clientId: %s",o.clientId),i.will){if(5===o.protocolVersion){const t=this._parseProperties();Object.getOwnPropertyNames(t).length&&(o.will.properties=t)}if(t=this._parseString(),null===t)return this._emitError(new Error("Cannot parse will topic"));if(o.will.topic=t,a("_parseConnect: packet.will.topic: %s",o.will.topic),e=this._parseBuffer(),null===e)return this._emitError(new Error("Cannot parse will payload"));o.will.payload=e,a("_parseConnect: packet.will.paylaod: %s",o.will.payload)}if(i.username){if(n=this._parseString(),null===n)return this._emitError(new Error("Cannot parse username"));o.username=n,a("_parseConnect: packet.username: %s",o.username)}if(i.password){if(r=this._parseBuffer(),null===r)return this._emitError(new Error("Cannot parse password"));o.password=r}return this.settings=o,a("_parseConnect: complete"),o}_parseConnack(){a("_parseConnack");const t=this.packet;if(this._list.length<1)return null;if(t.sessionPresent=!!(this._list.readUInt8(this._pos++)&s.SESSIONPRESENT_MASK),5===this.settings.protocolVersion)this._list.length>=2?t.reasonCode=this._list.readUInt8(this._pos++):t.reasonCode=0;else{if(this._list.length<2)return null;t.returnCode=this._list.readUInt8(this._pos++)}if(-1===t.returnCode||-1===t.reasonCode)return this._emitError(new Error("Cannot parse return code"));if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}a("_parseConnack: complete")}_parsePublish(){a("_parsePublish");const t=this.packet;if(t.topic=this._parseString(),null===t.topic)return this._emitError(new Error("Cannot parse topic"));if(!(t.qos>0)||this._parseMessageId()){if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}t.payload=this._list.slice(this._pos,t.length),a("_parsePublish: payload from buffer list: %o",t.payload)}}_parseSubscribe(){a("_parseSubscribe");const t=this.packet;let e,r,n,i,o,c,u;if(1!==t.qos)return this._emitError(new Error("Wrong subscribe header"));if(t.subscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}while(this._pos<t.length){if(e=this._parseString(),null===e)return this._emitError(new Error("Cannot parse topic"));if(this._pos>=t.length)return this._emitError(new Error("Malformed Subscribe Payload"));r=this._parseByte(),n=r&s.SUBSCRIBE_OPTIONS_QOS_MASK,c=0!==(r>>s.SUBSCRIBE_OPTIONS_NL_SHIFT&s.SUBSCRIBE_OPTIONS_NL_MASK),o=0!==(r>>s.SUBSCRIBE_OPTIONS_RAP_SHIFT&s.SUBSCRIBE_OPTIONS_RAP_MASK),i=r>>s.SUBSCRIBE_OPTIONS_RH_SHIFT&s.SUBSCRIBE_OPTIONS_RH_MASK,u={topic:e,qos:n},5===this.settings.protocolVersion?(u.nl=c,u.rap=o,u.rh=i):this.settings.bridgeMode&&(u.rh=0,u.rap=!0,u.nl=!0),a("_parseSubscribe: push subscription `%s` to subscription",u),t.subscriptions.push(u)}}}_parseSuback(){a("_parseSuback");const t=this.packet;if(this.packet.granted=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}while(this._pos<this.packet.length)this.packet.granted.push(this._list.readUInt8(this._pos++))}}_parseUnsubscribe(){a("_parseUnsubscribe");const t=this.packet;if(t.unsubscriptions=[],this._parseMessageId()){if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}while(this._pos<t.length){const e=this._parseString();if(null===e)return this._emitError(new Error("Cannot parse topic"));a("_parseUnsubscribe: push topic `%s` to unsubscriptions",e),t.unsubscriptions.push(e)}}}_parseUnsuback(){a("_parseUnsuback");const t=this.packet;if(!this._parseMessageId())return this._emitError(new Error("Cannot parse messageId"));if(5===this.settings.protocolVersion){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e),t.granted=[];while(this._pos<this.packet.length)this.packet.granted.push(this._list.readUInt8(this._pos++))}}_parseConfirmation(){a("_parseConfirmation: packet.cmd: `%s`",this.packet.cmd);const t=this.packet;if(this._parseMessageId(),5===this.settings.protocolVersion&&(t.length>2?(t.reasonCode=this._parseByte(),a("_parseConfirmation: packet.reasonCode `%d`",t.reasonCode)):t.reasonCode=0,t.length>3)){const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}return!0}_parseDisconnect(){const t=this.packet;if(a("_parseDisconnect"),5===this.settings.protocolVersion){this._list.length>0?t.reasonCode=this._parseByte():t.reasonCode=0;const e=this._parseProperties();Object.getOwnPropertyNames(e).length&&(t.properties=e)}return a("_parseDisconnect result: true"),!0}_parseAuth(){a("_parseAuth");const t=this.packet;if(5!==this.settings.protocolVersion)return this._emitError(new Error("Not supported auth packet for this version MQTT"));t.reasonCode=this._parseByte();const e=this._parseProperties();return Object.getOwnPropertyNames(e).length&&(t.properties=e),a("_parseAuth: result: true"),!0}_parseMessageId(){const t=this.packet;return t.messageId=this._parseNum(),null===t.messageId?(this._emitError(new Error("Cannot parse messageId")),!1):(a("_parseMessageId: packet.messageId %d",t.messageId),!0)}_parseString(t){const e=this._parseNum(),r=e+this._pos;if(-1===e||r>this._list.length||r>this.packet.length)return null;const n=this._list.toString("utf8",this._pos,r);return this._pos+=e,a("_parseString: result: %s",n),n}_parseStringPair(){return a("_parseStringPair"),{name:this._parseString(),value:this._parseString()}}_parseBuffer(){const t=this._parseNum(),e=t+this._pos;if(-1===t||e>this._list.length||e>this.packet.length)return null;const r=this._list.slice(this._pos,e);return this._pos+=t,a("_parseBuffer: result: %o",r),r}_parseNum(){if(this._list.length-this._pos<2)return-1;const t=this._list.readUInt16BE(this._pos);return this._pos+=2,a("_parseNum: result: %s",t),t}_parse4ByteNum(){if(this._list.length-this._pos<4)return-1;const t=this._list.readUInt32BE(this._pos);return this._pos+=4,a("_parse4ByteNum: result: %s",t),t}_parseVarByteNum(t){a("_parseVarByteNum");const e=4;let r,n=0,i=1,o=0,c=!1;const u=this._pos?this._pos:0;while(n<e&&u+n<this._list.length){if(r=this._list.readUInt8(u+n++),o+=i*(r&s.VARBYTEINT_MASK),i*=128,0===(r&s.VARBYTEINT_FIN_MASK)){c=!0;break}if(this._list.length<=n)break}return!c&&n===e&&this._list.length>=n&&this._emitError(new Error("Invalid variable byte integer")),u&&(this._pos+=n),c=!!c&&(t?{bytes:n,value:o}:o),a("_parseVarByteNum: result: %o",c),c}_parseByte(){let t;return this._pos<this._list.length&&(t=this._list.readUInt8(this._pos),this._pos++),a("_parseByte: result: %o",t),t}_parseByType(t){switch(a("_parseByType: type: %s",t),t){case"byte":return 0!==this._parseByte();case"int8":return this._parseByte();case"int16":return this._parseNum();case"int32":return this._parse4ByteNum();case"var":return this._parseVarByteNum();case"string":return this._parseString();case"pair":return this._parseStringPair();case"binary":return this._parseBuffer()}}_parseProperties(){a("_parseProperties");const t=this._parseVarByteNum(),e=this._pos,r=e+t,n={};while(this._pos<r){const t=this._parseByte();if(!t)return this._emitError(new Error("Cannot parse property code type")),!1;const e=s.propertiesCodes[t];if(!e)return this._emitError(new Error("Unknown property")),!1;if("userProperties"!==e)n[e]?(Array.isArray(n[e])||(n[e]=[n[e]]),n[e].push(this._parseByType(s.propertiesTypes[e]))):n[e]=this._parseByType(s.propertiesTypes[e]);else{n[e]||(n[e]=Object.create(null));const t=this._parseByType(s.propertiesTypes[e]);if(n[e][t.name])if(Array.isArray(n[e][t.name]))n[e][t.name].push(t.value);else{const r=n[e][t.name];n[e][t.name]=[r],n[e][t.name].push(t.value)}else n[e][t.name]=t.value}}return n}_newPacket(){return a("_newPacket"),this.packet&&(this._list.consume(this.packet.length),a("_newPacket: parser emit packet: packet.cmd: %s, packet.payload: %s, packet.length: %d",this.packet.cmd,this.packet.payload,this.packet.length),this.emit("packet",this.packet)),a("_newPacket: new packet"),this.packet=new o,this._pos=0,!0}_emitError(t){a("_emitError"),this.error=t,this.emit("error",t)}}t.exports=c},2500:function(t,e,r){"use strict";var n=r("a29f"),i=Object.prototype.hasOwnProperty,o=Array.isArray,s={allowDots:!1,allowEmptyArrays:!1,allowPrototypes:!1,allowSparse:!1,arrayLimit:20,charset:"utf-8",charsetSentinel:!1,comma:!1,decodeDotInKeys:!1,decoder:n.decode,delimiter:"&",depth:5,duplicates:"combine",ignoreQueryPrefix:!1,interpretNumericEntities:!1,parameterLimit:1e3,parseArrays:!0,plainObjects:!1,strictDepth:!1,strictNullHandling:!1,throwOnLimitExceeded:!1},a=function(t){return t.replace(/&#(\d+);/g,(function(t,e){return String.fromCharCode(parseInt(e,10))}))},c=function(t,e,r){if(t&&"string"===typeof t&&e.comma&&t.indexOf(",")>-1)return t.split(",");if(e.throwOnLimitExceeded&&r>=e.arrayLimit)throw new RangeError("Array limit exceeded. Only "+e.arrayLimit+" element"+(1===e.arrayLimit?"":"s")+" allowed in an array.");return t},u="utf8=%26%2310003%3B",l="utf8=%E2%9C%93",h=function(t,e){var r={__proto__:null},h=e.ignoreQueryPrefix?t.replace(/^\?/,""):t;h=h.replace(/%5B/gi,"[").replace(/%5D/gi,"]");var f=e.parameterLimit===1/0?void 0:e.parameterLimit,p=h.split(e.delimiter,e.throwOnLimitExceeded?f+1:f);if(e.throwOnLimitExceeded&&p.length>f)throw new RangeError("Parameter limit exceeded. Only "+f+" parameter"+(1===f?"":"s")+" allowed.");var d,y=-1,b=e.charset;if(e.charsetSentinel)for(d=0;d<p.length;++d)0===p[d].indexOf("utf8=")&&(p[d]===l?b="utf-8":p[d]===u&&(b="iso-8859-1"),y=d,d=p.length);for(d=0;d<p.length;++d)if(d!==y){var g,m,w=p[d],v=w.indexOf("]="),_=-1===v?w.indexOf("="):v+1;-1===_?(g=e.decoder(w,s.decoder,b,"key"),m=e.strictNullHandling?null:""):(g=e.decoder(w.slice(0,_),s.decoder,b,"key"),m=n.maybeMap(c(w.slice(_+1),e,o(r[g])?r[g].length:0),(function(t){return e.decoder(t,s.decoder,b,"value")}))),m&&e.interpretNumericEntities&&"iso-8859-1"===b&&(m=a(String(m))),w.indexOf("[]=")>-1&&(m=o(m)?[m]:m);var S=i.call(r,g);S&&"combine"===e.duplicates?r[g]=n.combine(r[g],m):S&&"last"!==e.duplicates||(r[g]=m)}return r},f=function(t,e,r,i){var o=0;if(t.length>0&&"[]"===t[t.length-1]){var s=t.slice(0,-1).join("");o=Array.isArray(e)&&e[s]?e[s].length:0}for(var a=i?e:c(e,r,o),u=t.length-1;u>=0;--u){var l,h=t[u];if("[]"===h&&r.parseArrays)l=r.allowEmptyArrays&&(""===a||r.strictNullHandling&&null===a)?[]:n.combine([],a);else{l=r.plainObjects?{__proto__:null}:{};var f="["===h.charAt(0)&&"]"===h.charAt(h.length-1)?h.slice(1,-1):h,p=r.decodeDotInKeys?f.replace(/%2E/g,"."):f,d=parseInt(p,10);r.parseArrays||""!==p?!isNaN(d)&&h!==p&&String(d)===p&&d>=0&&r.parseArrays&&d<=r.arrayLimit?(l=[],l[d]=a):"__proto__"!==p&&(l[p]=a):l={0:a}}a=l}return a},p=function(t,e,r,n){if(t){var o=r.allowDots?t.replace(/\.([^.[]+)/g,"[$1]"):t,s=/(\[[^[\]]*])/,a=/(\[[^[\]]*])/g,c=r.depth>0&&s.exec(o),u=c?o.slice(0,c.index):o,l=[];if(u){if(!r.plainObjects&&i.call(Object.prototype,u)&&!r.allowPrototypes)return;l.push(u)}var h=0;while(r.depth>0&&null!==(c=a.exec(o))&&h<r.depth){if(h+=1,!r.plainObjects&&i.call(Object.prototype,c[1].slice(1,-1))&&!r.allowPrototypes)return;l.push(c[1])}if(c){if(!0===r.strictDepth)throw new RangeError("Input depth exceeded depth option of "+r.depth+" and strictDepth is true");l.push("["+o.slice(c.index)+"]")}return f(l,e,r,n)}},d=function(t){if(!t)return s;if("undefined"!==typeof t.allowEmptyArrays&&"boolean"!==typeof t.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if("undefined"!==typeof t.decodeDotInKeys&&"boolean"!==typeof t.decodeDotInKeys)throw new TypeError("`decodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==t.decoder&&"undefined"!==typeof t.decoder&&"function"!==typeof t.decoder)throw new TypeError("Decoder has to be a function.");if("undefined"!==typeof t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");if("undefined"!==typeof t.throwOnLimitExceeded&&"boolean"!==typeof t.throwOnLimitExceeded)throw new TypeError("`throwOnLimitExceeded` option must be a boolean");var e="undefined"===typeof t.charset?s.charset:t.charset,r="undefined"===typeof t.duplicates?s.duplicates:t.duplicates;if("combine"!==r&&"first"!==r&&"last"!==r)throw new TypeError("The duplicates option must be either combine, first, or last");var i="undefined"===typeof t.allowDots?!0===t.decodeDotInKeys||s.allowDots:!!t.allowDots;return{allowDots:i,allowEmptyArrays:"boolean"===typeof t.allowEmptyArrays?!!t.allowEmptyArrays:s.allowEmptyArrays,allowPrototypes:"boolean"===typeof t.allowPrototypes?t.allowPrototypes:s.allowPrototypes,allowSparse:"boolean"===typeof t.allowSparse?t.allowSparse:s.allowSparse,arrayLimit:"number"===typeof t.arrayLimit?t.arrayLimit:s.arrayLimit,charset:e,charsetSentinel:"boolean"===typeof t.charsetSentinel?t.charsetSentinel:s.charsetSentinel,comma:"boolean"===typeof t.comma?t.comma:s.comma,decodeDotInKeys:"boolean"===typeof t.decodeDotInKeys?t.decodeDotInKeys:s.decodeDotInKeys,decoder:"function"===typeof t.decoder?t.decoder:s.decoder,delimiter:"string"===typeof t.delimiter||n.isRegExp(t.delimiter)?t.delimiter:s.delimiter,depth:"number"===typeof t.depth||!1===t.depth?+t.depth:s.depth,duplicates:r,ignoreQueryPrefix:!0===t.ignoreQueryPrefix,interpretNumericEntities:"boolean"===typeof t.interpretNumericEntities?t.interpretNumericEntities:s.interpretNumericEntities,parameterLimit:"number"===typeof t.parameterLimit?t.parameterLimit:s.parameterLimit,parseArrays:!1!==t.parseArrays,plainObjects:"boolean"===typeof t.plainObjects?t.plainObjects:s.plainObjects,strictDepth:"boolean"===typeof t.strictDepth?!!t.strictDepth:s.strictDepth,strictNullHandling:"boolean"===typeof t.strictNullHandling?t.strictNullHandling:s.strictNullHandling,throwOnLimitExceeded:"boolean"===typeof t.throwOnLimitExceeded&&t.throwOnLimitExceeded}};t.exports=function(t,e){var r=d(e);if(""===t||null===t||"undefined"===typeof t)return r.plainObjects?{__proto__:null}:{};for(var i="string"===typeof t?h(t,r):t,o=r.plainObjects?{__proto__:null}:{},s=Object.keys(i),a=0;a<s.length;++a){var c=s[a],u=p(c,i[c],r,"string"===typeof t);o=n.merge(o,u,r)}return!0===r.allowSparse?o:n.compact(o)}},2527:function(t,e){t.exports=function(){throw new Error("Readable.from is not available in the browser")}},2714:function(t,e,r){(function(e){var n="function"===typeof Map&&Map.prototype,i=Object.getOwnPropertyDescriptor&&n?Object.getOwnPropertyDescriptor(Map.prototype,"size"):null,o=n&&i&&"function"===typeof i.get?i.get:null,s=n&&Map.prototype.forEach,a="function"===typeof Set&&Set.prototype,c=Object.getOwnPropertyDescriptor&&a?Object.getOwnPropertyDescriptor(Set.prototype,"size"):null,u=a&&c&&"function"===typeof c.get?c.get:null,l=a&&Set.prototype.forEach,h="function"===typeof WeakMap&&WeakMap.prototype,f=h?WeakMap.prototype.has:null,p="function"===typeof WeakSet&&WeakSet.prototype,d=p?WeakSet.prototype.has:null,y="function"===typeof WeakRef&&WeakRef.prototype,b=y?WeakRef.prototype.deref:null,g=Boolean.prototype.valueOf,m=Object.prototype.toString,w=Function.prototype.toString,v=String.prototype.match,_=String.prototype.slice,S=String.prototype.replace,E=String.prototype.toUpperCase,k=String.prototype.toLowerCase,A=RegExp.prototype.test,O=Array.prototype.concat,P=Array.prototype.join,R=Array.prototype.slice,M=Math.floor,x="function"===typeof BigInt?BigInt.prototype.valueOf:null,I=Object.getOwnPropertySymbols,T="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?Symbol.prototype.toString:null,j="function"===typeof Symbol&&"object"===typeof Symbol.iterator,C="function"===typeof Symbol&&Symbol.toStringTag&&(typeof Symbol.toStringTag===j||"symbol")?Symbol.toStringTag:null,N=Object.prototype.propertyIsEnumerable,B=("function"===typeof Reflect?Reflect.getPrototypeOf:Object.getPrototypeOf)||([].__proto__===Array.prototype?function(t){return t.__proto__}:null);function L(t,e){if(t===1/0||t===-1/0||t!==t||t&&t>-1e3&&t<1e3||A.call(/e/,e))return e;var r=/[0-9](?=(?:[0-9]{3})+(?![0-9]))/g;if("number"===typeof t){var n=t<0?-M(-t):M(t);if(n!==t){var i=String(n),o=_.call(e,i.length+1);return S.call(i,r,"$&_")+"."+S.call(S.call(o,/([0-9]{3})/g,"$&_"),/_$/,"")}}return S.call(e,r,"$&_")}var D=r(17),U=D.custom,F=X(U)?U:null,q={__proto__:null,double:'"',single:"'"},W={__proto__:null,double:/(["\\])/g,single:/(['\\])/g};function K(t,e,r){var n=r.quoteStyle||e,i=q[n];return i+t+i}function H(t){return S.call(String(t),/"/g,"&quot;")}function V(t){return!C||!("object"===typeof t&&(C in t||"undefined"!==typeof t[C]))}function G(t){return"[object Array]"===nt(t)&&V(t)}function Q(t){return"[object Date]"===nt(t)&&V(t)}function $(t){return"[object RegExp]"===nt(t)&&V(t)}function z(t){return"[object Error]"===nt(t)&&V(t)}function J(t){return"[object String]"===nt(t)&&V(t)}function Y(t){return"[object Number]"===nt(t)&&V(t)}function Z(t){return"[object Boolean]"===nt(t)&&V(t)}function X(t){if(j)return t&&"object"===typeof t&&t instanceof Symbol;if("symbol"===typeof t)return!0;if(!t||"object"!==typeof t||!T)return!1;try{return T.call(t),!0}catch(e){}return!1}function tt(t){if(!t||"object"!==typeof t||!x)return!1;try{return x.call(t),!0}catch(e){}return!1}t.exports=function t(r,n,i,a){var c=n||{};if(rt(c,"quoteStyle")&&!rt(q,c.quoteStyle))throw new TypeError('option "quoteStyle" must be "single" or "double"');if(rt(c,"maxStringLength")&&("number"===typeof c.maxStringLength?c.maxStringLength<0&&c.maxStringLength!==1/0:null!==c.maxStringLength))throw new TypeError('option "maxStringLength", if provided, must be a positive integer, Infinity, or `null`');var h=!rt(c,"customInspect")||c.customInspect;if("boolean"!==typeof h&&"symbol"!==h)throw new TypeError("option \"customInspect\", if provided, must be `true`, `false`, or `'symbol'`");if(rt(c,"indent")&&null!==c.indent&&"\t"!==c.indent&&!(parseInt(c.indent,10)===c.indent&&c.indent>0))throw new TypeError('option "indent" must be "\\t", an integer > 0, or `null`');if(rt(c,"numericSeparator")&&"boolean"!==typeof c.numericSeparator)throw new TypeError('option "numericSeparator", if provided, must be `true` or `false`');var f=c.numericSeparator;if("undefined"===typeof r)return"undefined";if(null===r)return"null";if("boolean"===typeof r)return r?"true":"false";if("string"===typeof r)return ft(r,c);if("number"===typeof r){if(0===r)return 1/0/r>0?"0":"-0";var p=String(r);return f?L(r,p):p}if("bigint"===typeof r){var d=String(r)+"n";return f?L(r,d):d}var y="undefined"===typeof c.depth?5:c.depth;if("undefined"===typeof i&&(i=0),i>=y&&y>0&&"object"===typeof r)return G(r)?"[Array]":"[Object]";var b=mt(c,i);if("undefined"===typeof a)a=[];else if(ot(a,r)>=0)return"[Circular]";function m(e,r,n){if(r&&(a=R.call(a),a.push(r)),n){var o={depth:c.depth};return rt(c,"quoteStyle")&&(o.quoteStyle=c.quoteStyle),t(e,o,i+1,a)}return t(e,c,i+1,a)}if("function"===typeof r&&!$(r)){var w=it(r),v=vt(r,m);return"[Function"+(w?": "+w:" (anonymous)")+"]"+(v.length>0?" { "+P.call(v,", ")+" }":"")}if(X(r)){var E=j?S.call(String(r),/^(Symbol\(.*\))_[^)]*$/,"$1"):T.call(r);return"object"!==typeof r||j?E:dt(E)}if(ht(r)){for(var A="<"+k.call(String(r.nodeName)),M=r.attributes||[],I=0;I<M.length;I++)A+=" "+M[I].name+"="+K(H(M[I].value),"double",c);return A+=">",r.childNodes&&r.childNodes.length&&(A+="..."),A+="</"+k.call(String(r.nodeName))+">",A}if(G(r)){if(0===r.length)return"[]";var U=vt(r,m);return b&&!gt(U)?"["+wt(U,b)+"]":"[ "+P.call(U,", ")+" ]"}if(z(r)){var W=vt(r,m);return"cause"in Error.prototype||!("cause"in r)||N.call(r,"cause")?0===W.length?"["+String(r)+"]":"{ ["+String(r)+"] "+P.call(W,", ")+" }":"{ ["+String(r)+"] "+P.call(O.call("[cause]: "+m(r.cause),W),", ")+" }"}if("object"===typeof r&&h){if(F&&"function"===typeof r[F]&&D)return D(r,{depth:y-i});if("symbol"!==h&&"function"===typeof r.inspect)return r.inspect()}if(st(r)){var V=[];return s&&s.call(r,(function(t,e){V.push(m(e,r,!0)+" => "+m(t,r))})),bt("Map",o.call(r),V,b)}if(ut(r)){var et=[];return l&&l.call(r,(function(t){et.push(m(t,r))})),bt("Set",u.call(r),et,b)}if(at(r))return yt("WeakMap");if(lt(r))return yt("WeakSet");if(ct(r))return yt("WeakRef");if(Y(r))return dt(m(Number(r)));if(tt(r))return dt(m(x.call(r)));if(Z(r))return dt(g.call(r));if(J(r))return dt(m(String(r)));if("undefined"!==typeof window&&r===window)return"{ [object Window] }";if("undefined"!==typeof globalThis&&r===globalThis||"undefined"!==typeof e&&r===e)return"{ [object globalThis] }";if(!Q(r)&&!$(r)){var pt=vt(r,m),_t=B?B(r)===Object.prototype:r instanceof Object||r.constructor===Object,St=r instanceof Object?"":"null prototype",Et=!_t&&C&&Object(r)===r&&C in r?_.call(nt(r),8,-1):St?"Object":"",kt=_t||"function"!==typeof r.constructor?"":r.constructor.name?r.constructor.name+" ":"",At=kt+(Et||St?"["+P.call(O.call([],Et||[],St||[]),": ")+"] ":"");return 0===pt.length?At+"{}":b?At+"{"+wt(pt,b)+"}":At+"{ "+P.call(pt,", ")+" }"}return String(r)};var et=Object.prototype.hasOwnProperty||function(t){return t in this};function rt(t,e){return et.call(t,e)}function nt(t){return m.call(t)}function it(t){if(t.name)return t.name;var e=v.call(w.call(t),/^function\s*([\w$]+)/);return e?e[1]:null}function ot(t,e){if(t.indexOf)return t.indexOf(e);for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}function st(t){if(!o||!t||"object"!==typeof t)return!1;try{o.call(t);try{u.call(t)}catch(e){return!0}return t instanceof Map}catch(r){}return!1}function at(t){if(!f||!t||"object"!==typeof t)return!1;try{f.call(t,f);try{d.call(t,d)}catch(e){return!0}return t instanceof WeakMap}catch(r){}return!1}function ct(t){if(!b||!t||"object"!==typeof t)return!1;try{return b.call(t),!0}catch(e){}return!1}function ut(t){if(!u||!t||"object"!==typeof t)return!1;try{u.call(t);try{o.call(t)}catch(e){return!0}return t instanceof Set}catch(r){}return!1}function lt(t){if(!d||!t||"object"!==typeof t)return!1;try{d.call(t,d);try{f.call(t,f)}catch(e){return!0}return t instanceof WeakSet}catch(r){}return!1}function ht(t){return!(!t||"object"!==typeof t)&&("undefined"!==typeof HTMLElement&&t instanceof HTMLElement||"string"===typeof t.nodeName&&"function"===typeof t.getAttribute)}function ft(t,e){if(t.length>e.maxStringLength){var r=t.length-e.maxStringLength,n="... "+r+" more character"+(r>1?"s":"");return ft(_.call(t,0,e.maxStringLength),e)+n}var i=W[e.quoteStyle||"single"];i.lastIndex=0;var o=S.call(S.call(t,i,"\\$1"),/[\x00-\x1f]/g,pt);return K(o,"single",e)}function pt(t){var e=t.charCodeAt(0),r={8:"b",9:"t",10:"n",12:"f",13:"r"}[e];return r?"\\"+r:"\\x"+(e<16?"0":"")+E.call(e.toString(16))}function dt(t){return"Object("+t+")"}function yt(t){return t+" { ? }"}function bt(t,e,r,n){var i=n?wt(r,n):P.call(r,", ");return t+" ("+e+") {"+i+"}"}function gt(t){for(var e=0;e<t.length;e++)if(ot(t[e],"\n")>=0)return!1;return!0}function mt(t,e){var r;if("\t"===t.indent)r="\t";else{if(!("number"===typeof t.indent&&t.indent>0))return null;r=P.call(Array(t.indent+1)," ")}return{base:r,prev:P.call(Array(e+1),r)}}function wt(t,e){if(0===t.length)return"";var r="\n"+e.prev+e.base;return r+P.call(t,","+r)+"\n"+e.prev}function vt(t,e){var r=G(t),n=[];if(r){n.length=t.length;for(var i=0;i<t.length;i++)n[i]=rt(t,i)?e(t[i],t):""}var o,s="function"===typeof I?I(t):[];if(j){o={};for(var a=0;a<s.length;a++)o["$"+s[a]]=s[a]}for(var c in t)rt(t,c)&&(r&&String(Number(c))===c&&c<t.length||j&&o["$"+c]instanceof Symbol||(A.call(/[^\w$]/,c)?n.push(e(c,t)+": "+e(t[c],t)):n.push(c+": "+e(t[c],t))));if("function"===typeof I)for(var u=0;u<s.length;u++)N.call(t,s[u])&&n.push("["+e(s[u])+"]: "+e(t[s[u]],t));return n}}).call(this,r("c8ba"))},"29a2":function(t,e,r){"use strict";function n(t,e,r){var n=this;this._callback=t,this._args=r,this._interval=setInterval(t,e,this._args),this.reschedule=function(t){t||(t=n._interval),n._interval&&clearInterval(n._interval),n._interval=setInterval(n._callback,t,n._args)},this.clear=function(){n._interval&&(clearInterval(n._interval),n._interval=void 0)},this.destroy=function(){n._interval&&clearInterval(n._interval),n._callback=void 0,n._interval=void 0,n._args=void 0}}function i(){if("function"!==typeof arguments[0])throw new Error("callback needed");if("number"!==typeof arguments[1])throw new Error("interval needed");var t;if(arguments.length>0){t=new Array(arguments.length-2);for(var e=0;e<t.length;e++)t[e]=arguments[e+2]}return new n(arguments[0],arguments[1],t)}t.exports=i},"2a28":function(t,e,r){"use strict";t.exports=r("0050")()},"2aa9":function(t,e,r){"use strict";var n=r("6c3d");if(n)try{n([],"length")}catch(i){n=null}t.exports=n},"2fae":function(t,e,r){"use strict";function n(t){var e=this;if(e instanceof n||(e=new n),e.tail=null,e.head=null,e.length=0,t&&"function"===typeof t.forEach)t.forEach((function(t){e.push(t)}));else if(arguments.length>0)for(var r=0,i=arguments.length;r<i;r++)e.push(arguments[r]);return e}function i(t,e,r){var n=e===t.head?new a(r,null,e,t):new a(r,e,e.next,t);return null===n.next&&(t.tail=n),null===n.prev&&(t.head=n),t.length++,n}function o(t,e){t.tail=new a(e,t.tail,null,t),t.head||(t.head=t.tail),t.length++}function s(t,e){t.head=new a(e,null,t.head,t),t.tail||(t.tail=t.head),t.length++}function a(t,e,r,n){if(!(this instanceof a))return new a(t,e,r,n);this.list=n,this.value=t,e?(e.next=this,this.prev=e):this.prev=null,r?(r.prev=this,this.next=r):this.next=null}t.exports=n,n.Node=a,n.create=n,n.prototype.removeNode=function(t){if(t.list!==this)throw new Error("removing node which does not belong to this list");var e=t.next,r=t.prev;return e&&(e.prev=r),r&&(r.next=e),t===this.head&&(this.head=e),t===this.tail&&(this.tail=r),t.list.length--,t.next=null,t.prev=null,t.list=null,e},n.prototype.unshiftNode=function(t){if(t!==this.head){t.list&&t.list.removeNode(t);var e=this.head;t.list=this,t.next=e,e&&(e.prev=t),this.head=t,this.tail||(this.tail=t),this.length++}},n.prototype.pushNode=function(t){if(t!==this.tail){t.list&&t.list.removeNode(t);var e=this.tail;t.list=this,t.prev=e,e&&(e.next=t),this.tail=t,this.head||(this.head=t),this.length++}},n.prototype.push=function(){for(var t=0,e=arguments.length;t<e;t++)o(this,arguments[t]);return this.length},n.prototype.unshift=function(){for(var t=0,e=arguments.length;t<e;t++)s(this,arguments[t]);return this.length},n.prototype.pop=function(){if(this.tail){var t=this.tail.value;return this.tail=this.tail.prev,this.tail?this.tail.next=null:this.head=null,this.length--,t}},n.prototype.shift=function(){if(this.head){var t=this.head.value;return this.head=this.head.next,this.head?this.head.prev=null:this.tail=null,this.length--,t}},n.prototype.forEach=function(t,e){e=e||this;for(var r=this.head,n=0;null!==r;n++)t.call(e,r.value,n,this),r=r.next},n.prototype.forEachReverse=function(t,e){e=e||this;for(var r=this.tail,n=this.length-1;null!==r;n--)t.call(e,r.value,n,this),r=r.prev},n.prototype.get=function(t){for(var e=0,r=this.head;null!==r&&e<t;e++)r=r.next;if(e===t&&null!==r)return r.value},n.prototype.getReverse=function(t){for(var e=0,r=this.tail;null!==r&&e<t;e++)r=r.prev;if(e===t&&null!==r)return r.value},n.prototype.map=function(t,e){e=e||this;for(var r=new n,i=this.head;null!==i;)r.push(t.call(e,i.value,this)),i=i.next;return r},n.prototype.mapReverse=function(t,e){e=e||this;for(var r=new n,i=this.tail;null!==i;)r.push(t.call(e,i.value,this)),i=i.prev;return r},n.prototype.reduce=function(t,e){var r,n=this.head;if(arguments.length>1)r=e;else{if(!this.head)throw new TypeError("Reduce of empty list with no initial value");n=this.head.next,r=this.head.value}for(var i=0;null!==n;i++)r=t(r,n.value,i),n=n.next;return r},n.prototype.reduceReverse=function(t,e){var r,n=this.tail;if(arguments.length>1)r=e;else{if(!this.tail)throw new TypeError("Reduce of empty list with no initial value");n=this.tail.prev,r=this.tail.value}for(var i=this.length-1;null!==n;i--)r=t(r,n.value,i),n=n.prev;return r},n.prototype.toArray=function(){for(var t=new Array(this.length),e=0,r=this.head;null!==r;e++)t[e]=r.value,r=r.next;return t},n.prototype.toArrayReverse=function(){for(var t=new Array(this.length),e=0,r=this.tail;null!==r;e++)t[e]=r.value,r=r.prev;return t},n.prototype.slice=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var r=new n;if(e<t||e<0)return r;t<0&&(t=0),e>this.length&&(e=this.length);for(var i=0,o=this.head;null!==o&&i<t;i++)o=o.next;for(;null!==o&&i<e;i++,o=o.next)r.push(o.value);return r},n.prototype.sliceReverse=function(t,e){e=e||this.length,e<0&&(e+=this.length),t=t||0,t<0&&(t+=this.length);var r=new n;if(e<t||e<0)return r;t<0&&(t=0),e>this.length&&(e=this.length);for(var i=this.length,o=this.tail;null!==o&&i>e;i--)o=o.prev;for(;null!==o&&i>t;i--,o=o.prev)r.push(o.value);return r},n.prototype.splice=function(t,e,...r){t>this.length&&(t=this.length-1),t<0&&(t=this.length+t);for(var n=0,o=this.head;null!==o&&n<t;n++)o=o.next;var s=[];for(n=0;o&&n<e;n++)s.push(o.value),o=this.removeNode(o);null===o&&(o=this.tail),o!==this.head&&o!==this.tail&&(o=o.prev);for(n=0;n<r.length;n++)o=i(this,o,r[n]);return s},n.prototype.reverse=function(){for(var t=this.head,e=this.tail,r=t;null!==r;r=r.prev){var n=r.prev;r.prev=r.next,r.next=n}return this.head=e,this.tail=t,this};try{r("aff9")(n)}catch(c){}},"31b5":function(t,e,r){"use strict";var n=r("fbd7").codes.ERR_INVALID_OPT_VALUE;function i(t,e,r){return null!=t.highWaterMark?t.highWaterMark:e?t[r]:null}function o(t,e,r,o){var s=i(e,o,r);if(null!=s){if(!isFinite(s)||Math.floor(s)!==s||s<0){var a=o?r:"highWaterMark";throw new n(a,s)}return Math.floor(s)}return t.objectMode?16:16384}t.exports={getHighWaterMark:o}},33013:function(t,e,r){"use strict";const{Buffer:n}=r("1c35"),i=Symbol.for("BufferList");function o(t){if(!(this instanceof o))return new o(t);o._init.call(this,t)}o._init=function(t){Object.defineProperty(this,i,{value:!0}),this._bufs=[],this.length=0,t&&this.append(t)},o.prototype._new=function(t){return new o(t)},o.prototype._offset=function(t){if(0===t)return[0,0];let e=0;for(let r=0;r<this._bufs.length;r++){const n=e+this._bufs[r].length;if(t<n||r===this._bufs.length-1)return[r,t-e];e=n}},o.prototype._reverseOffset=function(t){const e=t[0];let r=t[1];for(let n=0;n<e;n++)r+=this._bufs[n].length;return r},o.prototype.get=function(t){if(t>this.length||t<0)return;const e=this._offset(t);return this._bufs[e[0]][e[1]]},o.prototype.slice=function(t,e){return"number"===typeof t&&t<0&&(t+=this.length),"number"===typeof e&&e<0&&(e+=this.length),this.copy(null,0,t,e)},o.prototype.copy=function(t,e,r,i){if(("number"!==typeof r||r<0)&&(r=0),("number"!==typeof i||i>this.length)&&(i=this.length),r>=this.length)return t||n.alloc(0);if(i<=0)return t||n.alloc(0);const o=!!t,s=this._offset(r),a=i-r;let c=a,u=o&&e||0,l=s[1];if(0===r&&i===this.length){if(!o)return 1===this._bufs.length?this._bufs[0]:n.concat(this._bufs,this.length);for(let e=0;e<this._bufs.length;e++)this._bufs[e].copy(t,u),u+=this._bufs[e].length;return t}if(c<=this._bufs[s[0]].length-l)return o?this._bufs[s[0]].copy(t,e,l,l+c):this._bufs[s[0]].slice(l,l+c);o||(t=n.allocUnsafe(a));for(let n=s[0];n<this._bufs.length;n++){const e=this._bufs[n].length-l;if(!(c>e)){this._bufs[n].copy(t,u,l,l+c),u+=e;break}this._bufs[n].copy(t,u,l),u+=e,c-=e,l&&(l=0)}return t.length>u?t.slice(0,u):t},o.prototype.shallowSlice=function(t,e){if(t=t||0,e="number"!==typeof e?this.length:e,t<0&&(t+=this.length),e<0&&(e+=this.length),t===e)return this._new();const r=this._offset(t),n=this._offset(e),i=this._bufs.slice(r[0],n[0]+1);return 0===n[1]?i.pop():i[i.length-1]=i[i.length-1].slice(0,n[1]),0!==r[1]&&(i[0]=i[0].slice(r[1])),this._new(i)},o.prototype.toString=function(t,e,r){return this.slice(e,r).toString(t)},o.prototype.consume=function(t){if(t=Math.trunc(t),Number.isNaN(t)||t<=0)return this;while(this._bufs.length){if(!(t>=this._bufs[0].length)){this._bufs[0]=this._bufs[0].slice(t),this.length-=t;break}t-=this._bufs[0].length,this.length-=this._bufs[0].length,this._bufs.shift()}return this},o.prototype.duplicate=function(){const t=this._new();for(let e=0;e<this._bufs.length;e++)t.append(this._bufs[e]);return t},o.prototype.append=function(t){if(null==t)return this;if(t.buffer)this._appendBuffer(n.from(t.buffer,t.byteOffset,t.byteLength));else if(Array.isArray(t))for(let e=0;e<t.length;e++)this.append(t[e]);else if(this._isBufferList(t))for(let e=0;e<t._bufs.length;e++)this.append(t._bufs[e]);else"number"===typeof t&&(t=t.toString()),this._appendBuffer(n.from(t));return this},o.prototype._appendBuffer=function(t){this._bufs.push(t),this.length+=t.length},o.prototype.indexOf=function(t,e,r){if(void 0===r&&"string"===typeof e&&(r=e,e=void 0),"function"===typeof t||Array.isArray(t))throw new TypeError('The "value" argument must be one of type string, Buffer, BufferList, or Uint8Array.');if("number"===typeof t?t=n.from([t]):"string"===typeof t?t=n.from(t,r):this._isBufferList(t)?t=t.slice():Array.isArray(t.buffer)?t=n.from(t.buffer,t.byteOffset,t.byteLength):n.isBuffer(t)||(t=n.from(t)),e=Number(e||0),isNaN(e)&&(e=0),e<0&&(e=this.length+e),e<0&&(e=0),0===t.length)return e>this.length?this.length:e;const i=this._offset(e);let o=i[0],s=i[1];for(;o<this._bufs.length;o++){const e=this._bufs[o];while(s<e.length){const r=e.length-s;if(r>=t.length){const r=e.indexOf(t,s);if(-1!==r)return this._reverseOffset([o,r]);s=e.length-t.length+1}else{const e=this._reverseOffset([o,s]);if(this._match(e,t))return e;s++}}s=0}return-1},o.prototype._match=function(t,e){if(this.length-t<e.length)return!1;for(let r=0;r<e.length;r++)if(this.get(t+r)!==e[r])return!1;return!0},function(){const t={readDoubleBE:8,readDoubleLE:8,readFloatBE:4,readFloatLE:4,readInt32BE:4,readInt32LE:4,readUInt32BE:4,readUInt32LE:4,readInt16BE:2,readInt16LE:2,readUInt16BE:2,readUInt16LE:2,readInt8:1,readUInt8:1,readIntBE:null,readIntLE:null,readUIntBE:null,readUIntLE:null};for(const e in t)(function(e){null===t[e]?o.prototype[e]=function(t,r){return this.slice(t,t+r)[e](0,r)}:o.prototype[e]=function(r=0){return this.slice(r,r+t[e])[e](0)}})(e)}(),o.prototype._isBufferList=function(t){return t instanceof o||o.isBufferList(t)},o.isBufferList=function(t){return null!=t&&t[i]},t.exports=o},"33f3":function(t,e,r){"use strict";var n=r("00ce"),i=r("3bbf"),o=r("2714"),s=r("f213"),a=r("0d25"),c=n("%WeakMap%",!0),u=i("WeakMap.prototype.get",!0),l=i("WeakMap.prototype.set",!0),h=i("WeakMap.prototype.has",!0),f=i("WeakMap.prototype.delete",!0);t.exports=c?function(){var t,e,r={assert:function(t){if(!r.has(t))throw new a("Side channel does not contain "+o(t))},delete:function(r){if(c&&r&&("object"===typeof r||"function"===typeof r)){if(t)return f(t,r)}else if(s&&e)return e["delete"](r);return!1},get:function(r){return c&&r&&("object"===typeof r||"function"===typeof r)&&t?u(t,r):e&&e.get(r)},has:function(r){return c&&r&&("object"===typeof r||"function"===typeof r)&&t?h(t,r):!!e&&e.has(r)},set:function(r,n){c&&r&&("object"===typeof r||"function"===typeof r)?(t||(t=new c),l(t,r,n)):s&&(e||(e=s()),e.set(r,n))}};return r}:s},3409:function(t,e,r){e.parser=r("1fad").parser,e.generate=r("7f0f"),e.writeToStream=r("7135")},"34e3":function(t,e,r){"use strict";(function(e){var n;function i(t,e,r){return e=o(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function o(t){var e=s(t,"string");return"symbol"===typeof e?e:String(e)}function s(t,e){if("object"!==typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var a=r("d9e1"),c=Symbol("lastResolve"),u=Symbol("lastReject"),l=Symbol("error"),h=Symbol("ended"),f=Symbol("lastPromise"),p=Symbol("handlePromise"),d=Symbol("stream");function y(t,e){return{value:t,done:e}}function b(t){var e=t[c];if(null!==e){var r=t[d].read();null!==r&&(t[f]=null,t[c]=null,t[u]=null,e(y(r,!1)))}}function g(t){e.nextTick(b,t)}function m(t,e){return function(r,n){t.then((function(){e[h]?r(y(void 0,!0)):e[p](r,n)}),n)}}var w=Object.getPrototypeOf((function(){})),v=Object.setPrototypeOf((n={get stream(){return this[d]},next:function(){var t=this,r=this[l];if(null!==r)return Promise.reject(r);if(this[h])return Promise.resolve(y(void 0,!0));if(this[d].destroyed)return new Promise((function(r,n){e.nextTick((function(){t[l]?n(t[l]):r(y(void 0,!0))}))}));var n,i=this[f];if(i)n=new Promise(m(i,this));else{var o=this[d].read();if(null!==o)return Promise.resolve(y(o,!1));n=new Promise(this[p])}return this[f]=n,n}},i(n,Symbol.asyncIterator,(function(){return this})),i(n,"return",(function(){var t=this;return new Promise((function(e,r){t[d].destroy(null,(function(t){t?r(t):e(y(void 0,!0))}))}))})),n),w),_=function(t){var e,r=Object.create(v,(e={},i(e,d,{value:t,writable:!0}),i(e,c,{value:null,writable:!0}),i(e,u,{value:null,writable:!0}),i(e,l,{value:null,writable:!0}),i(e,h,{value:t._readableState.endEmitted,writable:!0}),i(e,p,{value:function(t,e){var n=r[d].read();n?(r[f]=null,r[c]=null,r[u]=null,t(y(n,!1))):(r[c]=t,r[u]=e)},writable:!0}),e));return r[f]=null,a(t,(function(t){if(t&&"ERR_STREAM_PREMATURE_CLOSE"!==t.code){var e=r[u];return null!==e&&(r[f]=null,r[c]=null,r[u]=null,e(t)),void(r[l]=t)}var n=r[c];null!==n&&(r[f]=null,r[c]=null,r[u]=null,n(y(void 0,!0))),r[h]=!0})),t.on("readable",g.bind(null,r)),r};t.exports=_}).call(this,r("4362"))},"34eb":function(t,e,r){(function(n){function i(){if("undefined"!==typeof window&&window.process&&("renderer"===window.process.type||window.process.__nwjs))return!0;if("undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/))return!1;let t;return"undefined"!==typeof document&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||"undefined"!==typeof window&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||"undefined"!==typeof navigator&&navigator.userAgent&&(t=navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/))&&parseInt(t[1],10)>=31||"undefined"!==typeof navigator&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function o(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+t.exports.humanize(this.diff),!this.useColors)return;const r="color: "+this.color;e.splice(1,0,r,"color: inherit");let n=0,i=0;e[0].replace(/%[a-zA-Z%]/g,t=>{"%%"!==t&&(n++,"%c"===t&&(i=n))}),e.splice(i,0,r)}function s(t){try{t?e.storage.setItem("debug",t):e.storage.removeItem("debug")}catch(r){}}function a(){let t;try{t=e.storage.getItem("debug")}catch(r){}return!t&&"undefined"!==typeof n&&"env"in n&&(t=Object({NODE_ENV:"production",VUE_APP_BASE_API:"/prod-api",BASE_URL:"/"}).DEBUG),t}function c(){try{return localStorage}catch(t){}}e.formatArgs=o,e.save=s,e.load=a,e.useColors=i,e.storage=c(),e.destroy=(()=>{let t=!1;return()=>{t||(t=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})(),e.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"],e.log=console.debug||console.log||(()=>{}),t.exports=r("dc90")(e);const{formatters:u}=t.exports;u.j=function(t){try{return JSON.stringify(t)}catch(e){return"[UnexpectedJSONParseError]: "+e.message}}}).call(this,r("4362"))},"386b":function(t,e,r){"use strict";var n;function i(t){var e=!1;return function(){e||(e=!0,t.apply(void 0,arguments))}}var o=r("9bfc").codes,s=o.ERR_MISSING_ARGS,a=o.ERR_STREAM_DESTROYED;function c(t){if(t)throw t}function u(t){return t.setHeader&&"function"===typeof t.abort}function l(t,e,o,s){s=i(s);var c=!1;t.on("close",(function(){c=!0})),void 0===n&&(n=r("bf09")),n(t,{readable:e,writable:o},(function(t){if(t)return s(t);c=!0,s()}));var l=!1;return function(e){if(!c&&!l)return l=!0,u(t)?t.abort():"function"===typeof t.destroy?t.destroy():void s(e||new a("pipe"))}}function h(t){t()}function f(t,e){return t.pipe(e)}function p(t){return t.length?"function"!==typeof t[t.length-1]?c:t.pop():c}function d(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n,i=p(e);if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new s("streams");var o=e.map((function(t,r){var s=r<e.length-1,a=r>0;return l(t,s,a,(function(t){n||(n=t),t&&o.forEach(h),s||(o.forEach(h),i(n))}))}));return e.reduce(f)}t.exports=d},"3b6a":function(t,e,r){"use strict";var n=r("0f7c"),i=r("e16f"),o=r("926d"),s=r("6b3f");t.exports=s||n.call(o,i)},"3bbf":function(t,e,r){"use strict";var n=r("00ce"),i=r("f9ae"),o=i([n("%String.prototype.indexOf%")]);t.exports=function(t,e){var r=n(t,!!e);return"function"===typeof r&&o(t,".prototype.")>-1?i([r]):r}},"3ca2":function(t,e,r){"use strict";t.exports=l;var n=r("fbd7").codes,i=n.ERR_METHOD_NOT_IMPLEMENTED,o=n.ERR_MULTIPLE_CALLBACK,s=n.ERR_TRANSFORM_ALREADY_TRANSFORMING,a=n.ERR_TRANSFORM_WITH_LENGTH_0,c=r("be3f");function u(t,e){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(null===n)return this.emit("error",new o);r.writechunk=null,r.writecb=null,null!=e&&this.push(e),n(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function l(t){if(!(this instanceof l))return new l(t);c.call(this,t),this._transformState={afterTransform:u.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"===typeof t.transform&&(this._transform=t.transform),"function"===typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",h)}function h(){var t=this;"function"!==typeof this._flush||this._readableState.destroyed?f(this,null,null):this._flush((function(e,r){f(t,e,r)}))}function f(t,e,r){if(e)return t.emit("error",e);if(null!=r&&t.push(r),t._writableState.length)throw new a;if(t._transformState.transforming)throw new s;return t.push(null)}r("3fb5")(l,c),l.prototype.push=function(t,e){return this._transformState.needTransform=!1,c.prototype.push.call(this,t,e)},l.prototype._transform=function(t,e,r){r(new i("_transform()"))},l.prototype._write=function(t,e,r){var n=this._transformState;if(n.writecb=r,n.writechunk=t,n.writeencoding=e,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},l.prototype._read=function(t){var e=this._transformState;null===e.writechunk||e.transforming?e.needTransform=!0:(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform))},l.prototype._destroy=function(t,e){c.prototype._destroy.call(this,t,(function(t){e(t)}))}},"3d67":function(t,e,r){"use strict";r.r(e),r.d(e,"Stack",(function(){return u})),r.d(e,"Queue",(function(){return f})),r.d(e,"PriorityQueue",(function(){return g})),r.d(e,"Vector",(function(){return I})),r.d(e,"LinkList",(function(){return B})),r.d(e,"Deque",(function(){return K})),r.d(e,"OrderedSet",(function(){return st})),r.d(e,"OrderedMap",(function(){return ft})),r.d(e,"HashSet",(function(){return _t})),r.d(e,"HashMap",(function(){return Ot}));var n=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),i=function(){function t(t){void 0===t&&(t=0),this.iteratorType=t}return t.prototype.equals=function(t){return this.o===t.o},t}(),o=function(){function t(){this.M=0}return Object.defineProperty(t.prototype,"length",{get:function(){return this.M},enumerable:!1,configurable:!0}),t.prototype.size=function(){return this.M},t.prototype.empty=function(){return 0===this.M},t}(),s=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return n(e,t),e}(o),a=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),c=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this;r.nt=[];var n=r;return e.forEach((function(t){n.push(t)})),r}return a(e,t),e.prototype.clear=function(){this.M=0,this.nt=[]},e.prototype.push=function(t){return this.nt.push(t),this.M+=1,this.M},e.prototype.pop=function(){if(0!==this.M)return this.M-=1,this.nt.pop()},e.prototype.top=function(){return this.nt[this.M-1]},e}(o),u=c,l=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),h=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this;r.A=0,r.tt=[];var n=r;return e.forEach((function(t){n.push(t)})),r}return l(e,t),e.prototype.clear=function(){this.tt=[],this.M=this.A=0},e.prototype.push=function(t){var e=this.tt.length;if(this.A/e>.5&&this.A+this.M>=e&&e>4096){for(var r=this.M,n=0;n<r;++n)this.tt[n]=this.tt[this.A+n];this.A=0,this.tt[this.M]=t}else this.tt[this.A+this.M]=t;return++this.M},e.prototype.pop=function(){if(0!==this.M){var t=this.tt[this.A++];return this.M-=1,t}},e.prototype.front=function(){if(0!==this.M)return this.tt[this.A]},e}(o),f=h,p=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),d=function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{while((void 0===e||e-- >0)&&!(n=o.next()).done)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o["return"])&&r.call(o)}finally{if(i)throw i.error}}return s},y=function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))},b=function(t){function e(e,r,n){void 0===e&&(e=[]),void 0===r&&(r=function(t,e){return t>e?-1:t<e?1:0}),void 0===n&&(n=!0);var i=t.call(this)||this;if(i.$=r,Array.isArray(e))i.ii=n?y([],d(e),!1):e;else{i.ii=[];var o=i;e.forEach((function(t){o.ii.push(t)}))}i.M=i.ii.length;for(var s=i.M>>1,a=i.M-1>>1;a>=0;--a)i.ri(a,s);return i}return p(e,t),e.prototype.ti=function(t){var e=this.ii[t];while(t>0){var r=t-1>>1,n=this.ii[r];if(this.$(n,e)<=0)break;this.ii[t]=n,t=r}this.ii[t]=e},e.prototype.ri=function(t,e){var r=this.ii[t];while(t<e){var n=t<<1|1,i=n+1,o=this.ii[n];if(i<this.M&&this.$(o,this.ii[i])>0&&(n=i,o=this.ii[i]),this.$(o,r)>=0)break;this.ii[t]=o,t=n}this.ii[t]=r},e.prototype.clear=function(){this.M=0,this.ii.length=0},e.prototype.push=function(t){this.ii.push(t),this.ti(this.M),this.M+=1},e.prototype.pop=function(){if(0!==this.M){var t=this.ii[0],e=this.ii.pop();return this.M-=1,this.M&&(this.ii[0]=e,this.ri(0,this.M>>1)),t}},e.prototype.top=function(){return this.ii[0]},e.prototype.find=function(t){return this.ii.indexOf(t)>=0},e.prototype.remove=function(t){var e=this.ii.indexOf(t);return!(e<0)&&(0===e?this.pop():e===this.M-1?(this.ii.pop(),this.M-=1):(this.ii.splice(e,1,this.ii.pop()),this.M-=1,this.ti(e),this.ri(e,this.M>>1)),!0)},e.prototype.updateItem=function(t){var e=this.ii.indexOf(t);return!(e<0)&&(this.ti(e),this.ri(e,this.M>>1),!0)},e.prototype.toArray=function(){return y([],d(this.ii),!1)},e}(o),g=b,m=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),w=function(t){function e(){return null!==t&&t.apply(this,arguments)||this}return m(e,t),e}(s),v=w;function _(){throw new RangeError("Iterator access denied!")}var S=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),E=function(t){function e(e,r){var n=t.call(this,r)||this;return n.o=e,0===n.iteratorType?(n.pre=function(){return 0===this.o&&_(),this.o-=1,this},n.next=function(){return this.o===this.container.size()&&_(),this.o+=1,this}):(n.pre=function(){return this.o===this.container.size()-1&&_(),this.o+=1,this},n.next=function(){return-1===this.o&&_(),this.o-=1,this}),n}return S(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){return this.container.getElementByPos(this.o)},set:function(t){this.container.setElementByPos(this.o,t)},enumerable:!1,configurable:!0}),e}(i),k=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),A=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},O=function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{while((void 0===e||e-- >0)&&!(n=o.next()).done)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o["return"])&&r.call(o)}finally{if(i)throw i.error}}return s},P=function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))},R=function(t){var e="function"===typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},M=function(t){function e(e,r,n){var i=t.call(this,e,n)||this;return i.container=r,i}return k(e,t),e.prototype.copy=function(){return new e(this.o,this.container,this.iteratorType)},e}(E),x=function(t){function e(e,r){void 0===e&&(e=[]),void 0===r&&(r=!0);var n=t.call(this)||this;if(Array.isArray(e))n.J=r?P([],O(e),!1):e,n.M=e.length;else{n.J=[];var i=n;e.forEach((function(t){i.pushBack(t)}))}return n}return k(e,t),e.prototype.clear=function(){this.M=0,this.J.length=0},e.prototype.begin=function(){return new M(0,this)},e.prototype.end=function(){return new M(this.M,this)},e.prototype.rBegin=function(){return new M(this.M-1,this,1)},e.prototype.rEnd=function(){return new M(-1,this,1)},e.prototype.front=function(){return this.J[0]},e.prototype.back=function(){return this.J[this.M-1]},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;return this.J[t]},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;return this.J.splice(t,1),this.M-=1,this.M},e.prototype.eraseElementByValue=function(t){for(var e=0,r=0;r<this.M;++r)this.J[r]!==t&&(this.J[e++]=this.J[r]);return this.M=this.J.length=e,this.M},e.prototype.eraseElementByIterator=function(t){var e=t.o;return t=t.next(),this.eraseElementByPos(e),t},e.prototype.pushBack=function(t){return this.J.push(t),this.M+=1,this.M},e.prototype.popBack=function(){if(0!==this.M)return this.M-=1,this.J.pop()},e.prototype.setElementByPos=function(t,e){if(t<0||t>this.M-1)throw new RangeError;this.J[t]=e},e.prototype.insert=function(t,e,r){var n;if(void 0===r&&(r=1),t<0||t>this.M)throw new RangeError;return(n=this.J).splice.apply(n,P([t,0],O(new Array(r).fill(e)),!1)),this.M+=r,this.M},e.prototype.find=function(t){for(var e=0;e<this.M;++e)if(this.J[e]===t)return new M(e,this);return this.end()},e.prototype.reverse=function(){this.J.reverse()},e.prototype.unique=function(){for(var t=1,e=1;e<this.M;++e)this.J[e]!==this.J[e-1]&&(this.J[t++]=this.J[e]);return this.M=this.J.length=t,this.M},e.prototype.sort=function(t){this.J.sort(t)},e.prototype.forEach=function(t){for(var e=0;e<this.M;++e)t(this.J[e],e,this)},e.prototype[Symbol.iterator]=function(){return function(){return A(this,(function(t){switch(t.label){case 0:return[5,R(this.J)];case 1:return t.sent(),[2]}}))}.bind(this)()},e}(v),I=x,T=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),j=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},C=function(t){function e(e,r,n,i){var o=t.call(this,i)||this;return o.o=e,o.h=r,o.container=n,0===o.iteratorType?(o.pre=function(){return this.o.L===this.h&&_(),this.o=this.o.L,this},o.next=function(){return this.o===this.h&&_(),this.o=this.o.m,this}):(o.pre=function(){return this.o.m===this.h&&_(),this.o=this.o.m,this},o.next=function(){return this.o===this.h&&_(),this.o=this.o.L,this}),o}return T(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){return this.o===this.h&&_(),this.o.p},set:function(t){this.o===this.h&&_(),this.o.p=t},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(i),N=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this;r.h={},r.H=r.l=r.h.L=r.h.m=r.h;var n=r;return e.forEach((function(t){n.pushBack(t)})),r}return T(e,t),e.prototype.G=function(t){var e=t.L,r=t.m;e.m=r,r.L=e,t===this.H&&(this.H=r),t===this.l&&(this.l=e),this.M-=1},e.prototype.F=function(t,e){var r=e.m,n={p:t,L:e,m:r};e.m=n,r.L=n,e===this.h&&(this.H=n),r===this.h&&(this.l=n),this.M+=1},e.prototype.clear=function(){this.M=0,this.H=this.l=this.h.L=this.h.m=this.h},e.prototype.begin=function(){return new C(this.H,this.h,this)},e.prototype.end=function(){return new C(this.h,this.h,this)},e.prototype.rBegin=function(){return new C(this.l,this.h,this,1)},e.prototype.rEnd=function(){return new C(this.h,this.h,this,1)},e.prototype.front=function(){return this.H.p},e.prototype.back=function(){return this.l.p},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return e.p},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return this.G(e),this.M},e.prototype.eraseElementByValue=function(t){var e=this.H;while(e!==this.h)e.p===t&&this.G(e),e=e.m;return this.M},e.prototype.eraseElementByIterator=function(t){var e=t.o;return e===this.h&&_(),t=t.next(),this.G(e),t},e.prototype.pushBack=function(t){return this.F(t,this.l),this.M},e.prototype.popBack=function(){if(0!==this.M){var t=this.l.p;return this.G(this.l),t}},e.prototype.pushFront=function(t){return this.F(t,this.h),this.M},e.prototype.popFront=function(){if(0!==this.M){var t=this.H.p;return this.G(this.H),t}},e.prototype.setElementByPos=function(t,e){if(t<0||t>this.M-1)throw new RangeError;var r=this.H;while(t--)r=r.m;r.p=e},e.prototype.insert=function(t,e,r){if(void 0===r&&(r=1),t<0||t>this.M)throw new RangeError;if(r<=0)return this.M;if(0===t)while(r--)this.pushFront(e);else if(t===this.M)while(r--)this.pushBack(e);else{for(var n=this.H,i=1;i<t;++i)n=n.m;var o=n.m;this.M+=r;while(r--)n.m={p:e,L:n},n.m.L=n,n=n.m;n.m=o,o.L=n}return this.M},e.prototype.find=function(t){var e=this.H;while(e!==this.h){if(e.p===t)return new C(e,this.h,this);e=e.m}return this.end()},e.prototype.reverse=function(){if(!(this.M<=1)){var t=this.H,e=this.l,r=0;while(r<<1<this.M){var n=t.p;t.p=e.p,e.p=n,t=t.m,e=e.L,r+=1}}},e.prototype.unique=function(){if(this.M<=1)return this.M;var t=this.H;while(t!==this.h){var e=t;while(e.m!==this.h&&e.p===e.m.p)e=e.m,this.M-=1;t.m=e.m,t.m.L=t,t=t.m}return this.M},e.prototype.sort=function(t){if(!(this.M<=1)){var e=[];this.forEach((function(t){e.push(t)})),e.sort(t);var r=this.H;e.forEach((function(t){r.p=t,r=r.m}))}},e.prototype.merge=function(t){var e=this;if(0===this.M)t.forEach((function(t){e.pushBack(t)}));else{var r=this.H;t.forEach((function(t){while(r!==e.h&&r.p<=t)r=r.m;e.F(t,r.L)}))}return this.M},e.prototype.forEach=function(t){var e=this.H,r=0;while(e!==this.h)t(e.p,r++,this),e=e.m},e.prototype[Symbol.iterator]=function(){return function(){var t;return j(this,(function(e){switch(e.label){case 0:if(0===this.M)return[2];t=this.H,e.label=1;case 1:return t===this.h?[3,3]:[4,t.p];case 2:return e.sent(),t=t.m,[3,1];case 3:return[2]}}))}.bind(this)()},e}(v),B=N,L=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),D=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},U=function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{while((void 0===e||e-- >0)&&!(n=o.next()).done)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o["return"])&&r.call(o)}finally{if(i)throw i.error}}return s},F=function(t,e,r){if(r||2===arguments.length)for(var n,i=0,o=e.length;i<o;i++)!n&&i in e||(n||(n=Array.prototype.slice.call(e,0,i)),n[i]=e[i]);return t.concat(n||Array.prototype.slice.call(e))},q=function(t){function e(e,r,n){var i=t.call(this,e,n)||this;return i.container=r,i}return L(e,t),e.prototype.copy=function(){return new e(this.o,this.container,this.iteratorType)},e}(E),W=function(t){function e(e,r){void 0===e&&(e=[]),void 0===r&&(r=4096);var n=t.call(this)||this;n.A=0,n.S=0,n.R=0,n.k=0,n.C=0,n.j=[];var i=function(){if("number"===typeof e.length)return e.length;if("number"===typeof e.size)return e.size;if("function"===typeof e.size)return e.size();throw new TypeError("Cannot get the length or size of the container")}();n.B=r,n.C=Math.max(Math.ceil(i/n.B),1);for(var o=0;o<n.C;++o)n.j.push(new Array(n.B));var s=Math.ceil(i/n.B);n.A=n.R=(n.C>>1)-(s>>1),n.S=n.k=n.B-i%n.B>>1;var a=n;return e.forEach((function(t){a.pushBack(t)})),n}return L(e,t),e.prototype.O=function(){for(var t=[],e=Math.max(this.C>>1,1),r=0;r<e;++r)t[r]=new Array(this.B);for(r=this.A;r<this.C;++r)t[t.length]=this.j[r];for(r=0;r<this.R;++r)t[t.length]=this.j[r];t[t.length]=F([],U(this.j[this.R]),!1),this.A=e,this.R=t.length-1;for(r=0;r<e;++r)t[t.length]=new Array(this.B);this.j=t,this.C=t.length},e.prototype.T=function(t){var e=this.S+t+1,r=e%this.B,n=r-1,i=this.A+(e-r)/this.B;return 0===r&&(i-=1),i%=this.C,n<0&&(n+=this.B),{curNodeBucketIndex:i,curNodePointerIndex:n}},e.prototype.clear=function(){this.j=[new Array(this.B)],this.C=1,this.A=this.R=this.M=0,this.S=this.k=this.B>>1},e.prototype.begin=function(){return new q(0,this)},e.prototype.end=function(){return new q(this.M,this)},e.prototype.rBegin=function(){return new q(this.M-1,this,1)},e.prototype.rEnd=function(){return new q(-1,this,1)},e.prototype.front=function(){if(0!==this.M)return this.j[this.A][this.S]},e.prototype.back=function(){if(0!==this.M)return this.j[this.R][this.k]},e.prototype.pushBack=function(t){return this.M&&(this.k<this.B-1?this.k+=1:this.R<this.C-1?(this.R+=1,this.k=0):(this.R=0,this.k=0),this.R===this.A&&this.k===this.S&&this.O()),this.M+=1,this.j[this.R][this.k]=t,this.M},e.prototype.popBack=function(){if(0!==this.M){var t=this.j[this.R][this.k];return 1!==this.M&&(this.k>0?this.k-=1:this.R>0?(this.R-=1,this.k=this.B-1):(this.R=this.C-1,this.k=this.B-1)),this.M-=1,t}},e.prototype.pushFront=function(t){return this.M&&(this.S>0?this.S-=1:this.A>0?(this.A-=1,this.S=this.B-1):(this.A=this.C-1,this.S=this.B-1),this.A===this.R&&this.S===this.k&&this.O()),this.M+=1,this.j[this.A][this.S]=t,this.M},e.prototype.popFront=function(){if(0!==this.M){var t=this.j[this.A][this.S];return 1!==this.M&&(this.S<this.B-1?this.S+=1:this.A<this.C-1?(this.A+=1,this.S=0):(this.A=0,this.S=0)),this.M-=1,t}},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.T(t),r=e.curNodeBucketIndex,n=e.curNodePointerIndex;return this.j[r][n]},e.prototype.setElementByPos=function(t,e){if(t<0||t>this.M-1)throw new RangeError;var r=this.T(t),n=r.curNodeBucketIndex,i=r.curNodePointerIndex;this.j[n][i]=e},e.prototype.insert=function(t,e,r){if(void 0===r&&(r=1),t<0||t>this.M)throw new RangeError;if(0===t)while(r--)this.pushFront(e);else if(t===this.M)while(r--)this.pushBack(e);else{for(var n=[],i=t;i<this.M;++i)n.push(this.getElementByPos(i));this.cut(t-1);for(i=0;i<r;++i)this.pushBack(e);for(i=0;i<n.length;++i)this.pushBack(n[i])}return this.M},e.prototype.cut=function(t){if(t<0)return this.clear(),0;var e=this.T(t),r=e.curNodeBucketIndex,n=e.curNodePointerIndex;return this.R=r,this.k=n,this.M=t+1,this.M},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;if(0===t)this.popFront();else if(t===this.M-1)this.popBack();else{for(var e=[],r=t+1;r<this.M;++r)e.push(this.getElementByPos(r));this.cut(t),this.popBack();var n=this;e.forEach((function(t){n.pushBack(t)}))}return this.M},e.prototype.eraseElementByValue=function(t){if(0===this.M)return 0;for(var e=[],r=0;r<this.M;++r){var n=this.getElementByPos(r);n!==t&&e.push(n)}var i=e.length;for(r=0;r<i;++r)this.setElementByPos(r,e[r]);return this.cut(i-1)},e.prototype.eraseElementByIterator=function(t){var e=t.o;return this.eraseElementByPos(e),t=t.next(),t},e.prototype.find=function(t){for(var e=0;e<this.M;++e)if(this.getElementByPos(e)===t)return new q(e,this);return this.end()},e.prototype.reverse=function(){var t=0,e=this.M-1;while(t<e){var r=this.getElementByPos(t);this.setElementByPos(t,this.getElementByPos(e)),this.setElementByPos(e,r),t+=1,e-=1}},e.prototype.unique=function(){if(this.M<=1)return this.M;for(var t=1,e=this.getElementByPos(0),r=1;r<this.M;++r){var n=this.getElementByPos(r);n!==e&&(e=n,this.setElementByPos(t++,n))}while(this.M>t)this.popBack();return this.M},e.prototype.sort=function(t){for(var e=[],r=0;r<this.M;++r)e.push(this.getElementByPos(r));e.sort(t);for(r=0;r<this.M;++r)this.setElementByPos(r,e[r])},e.prototype.shrinkToFit=function(){if(0!==this.M){var t=[];this.forEach((function(e){t.push(e)})),this.C=Math.max(Math.ceil(this.M/this.B),1),this.M=this.A=this.R=this.S=this.k=0,this.j=[];for(var e=0;e<this.C;++e)this.j.push(new Array(this.B));for(e=0;e<t.length;++e)this.pushBack(t[e])}},e.prototype.forEach=function(t){for(var e=0;e<this.M;++e)t(this.getElementByPos(e),e,this)},e.prototype[Symbol.iterator]=function(){return function(){var t;return D(this,(function(e){switch(e.label){case 0:t=0,e.label=1;case 1:return t<this.M?[4,this.getElementByPos(t)]:[3,4];case 2:e.sent(),e.label=3;case 3:return++t,[3,1];case 4:return[2]}}))}.bind(this)()},e}(v),K=W,H=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),V=function(){function t(t,e){this.ee=1,this.u=void 0,this.p=void 0,this.K=void 0,this.N=void 0,this.rr=void 0,this.u=t,this.p=e}return t.prototype.L=function(){var t=this;if(1===t.ee&&t.rr.rr===t)t=t.N;else if(t.K){t=t.K;while(t.N)t=t.N}else{var e=t.rr;while(e.K===t)t=e,e=t.rr;t=e}return t},t.prototype.m=function(){var t=this;if(t.N){t=t.N;while(t.K)t=t.K;return t}var e=t.rr;while(e.N===t)t=e,e=t.rr;return t.N!==e?e:t},t.prototype.ne=function(){var t=this.rr,e=this.N,r=e.K;return t.rr===this?t.rr=e:t.K===this?t.K=e:t.N=e,e.rr=t,e.K=this,this.rr=e,this.N=r,r&&(r.rr=this),e},t.prototype.te=function(){var t=this.rr,e=this.K,r=e.N;return t.rr===this?t.rr=e:t.K===this?t.K=e:t.N=e,e.rr=t,e.N=this,this.rr=e,this.K=r,r&&(r.rr=this),e},t}(),G=function(t){function e(){var e=null!==t&&t.apply(this,arguments)||this;return e.tr=1,e}return H(e,t),e.prototype.ne=function(){var e=t.prototype.ne.call(this);return this.ie(),e.ie(),e},e.prototype.te=function(){var e=t.prototype.te.call(this);return this.ie(),e.ie(),e},e.prototype.ie=function(){this.tr=1,this.K&&(this.tr+=this.K.tr),this.N&&(this.tr+=this.N.tr)},e}(V),Q=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),$=function(t,e){var r="function"===typeof Symbol&&t[Symbol.iterator];if(!r)return t;var n,i,o=r.call(t),s=[];try{while((void 0===e||e-- >0)&&!(n=o.next()).done)s.push(n.value)}catch(t){i={error:t}}finally{try{n&&!n.done&&(r=o["return"])&&r.call(o)}finally{if(i)throw i.error}}return s},z=function(t){var e="function"===typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},J=function(t){function e(e,r){void 0===e&&(e=function(t,e){return t<e?-1:t>e?1:0}),void 0===r&&(r=!1);var n=t.call(this)||this;return n.W=void 0,n.$=e,r?(n.re=G,n.v=function(t,e,r){var n=this.se(t,e,r);if(n){var i=n.rr;while(i!==this.h)i.tr+=1,i=i.rr;var o=this.fe(n);if(o){var s=o,a=s.parentNode,c=s.grandParent,u=s.curNode;a.ie(),c.ie(),u.ie()}}return this.M},n.G=function(t){var e=this.he(t);while(e!==this.h)e.tr-=1,e=e.rr}):(n.re=V,n.v=function(t,e,r){var n=this.se(t,e,r);return n&&this.fe(n),this.M},n.G=n.he),n.h=new n.re,n}return Q(e,t),e.prototype.U=function(t,e){var r=this.h;while(t){var n=this.$(t.u,e);if(n<0)t=t.N;else{if(!(n>0))return t;r=t,t=t.K}}return r},e.prototype.X=function(t,e){var r=this.h;while(t){var n=this.$(t.u,e);n<=0?t=t.N:(r=t,t=t.K)}return r},e.prototype.Y=function(t,e){var r=this.h;while(t){var n=this.$(t.u,e);if(n<0)r=t,t=t.N;else{if(!(n>0))return t;t=t.K}}return r},e.prototype.Z=function(t,e){var r=this.h;while(t){var n=this.$(t.u,e);n<0?(r=t,t=t.N):t=t.K}return r},e.prototype.ue=function(t){while(1){var e=t.rr;if(e===this.h)return;if(1===t.ee)return void(t.ee=0);if(t===e.K){var r=e.N;if(1===r.ee)r.ee=0,e.ee=1,e===this.W?this.W=e.ne():e.ne();else{if(r.N&&1===r.N.ee)return r.ee=e.ee,e.ee=0,r.N.ee=0,void(e===this.W?this.W=e.ne():e.ne());r.K&&1===r.K.ee?(r.ee=1,r.K.ee=0,r.te()):(r.ee=1,t=e)}}else{r=e.K;if(1===r.ee)r.ee=0,e.ee=1,e===this.W?this.W=e.te():e.te();else{if(r.K&&1===r.K.ee)return r.ee=e.ee,e.ee=0,r.K.ee=0,void(e===this.W?this.W=e.te():e.te());r.N&&1===r.N.ee?(r.ee=1,r.N.ee=0,r.ne()):(r.ee=1,t=e)}}}},e.prototype.he=function(t){var e,r;if(1===this.M)return this.clear(),this.h;var n=t;while(n.K||n.N){if(n.N){n=n.N;while(n.K)n=n.K}else n=n.K;e=$([n.u,t.u],2),t.u=e[0],n.u=e[1],r=$([n.p,t.p],2),t.p=r[0],n.p=r[1],t=n}this.h.K===n?this.h.K=n.rr:this.h.N===n&&(this.h.N=n.rr),this.ue(n);var i=n.rr;return n===i.K?i.K=void 0:i.N=void 0,this.M-=1,this.W.ee=0,i},e.prototype.ae=function(t,e){if(void 0===t)return!1;var r=this.ae(t.K,e);return!!r||(!!e(t)||this.ae(t.N,e))},e.prototype.fe=function(t){while(1){var e=t.rr;if(0===e.ee)return;var r=e.rr;if(e===r.K){var n=r.N;if(n&&1===n.ee){if(n.ee=e.ee=0,r===this.W)return;r.ee=1,t=r;continue}if(t===e.N){if(t.ee=0,t.K&&(t.K.rr=e),t.N&&(t.N.rr=r),e.N=t.K,r.K=t.N,t.K=e,t.N=r,r===this.W)this.W=t,this.h.rr=t;else{var i=r.rr;i.K===r?i.K=t:i.N=t}return t.rr=r.rr,e.rr=t,r.rr=t,r.ee=1,{parentNode:e,grandParent:r,curNode:t}}e.ee=0,r===this.W?this.W=r.te():r.te(),r.ee=1}else{n=r.K;if(n&&1===n.ee){if(n.ee=e.ee=0,r===this.W)return;r.ee=1,t=r;continue}if(t===e.K){if(t.ee=0,t.K&&(t.K.rr=r),t.N&&(t.N.rr=e),r.N=t.K,e.K=t.N,t.K=r,t.N=e,r===this.W)this.W=t,this.h.rr=t;else{i=r.rr;i.K===r?i.K=t:i.N=t}return t.rr=r.rr,e.rr=t,r.rr=t,r.ee=1,{parentNode:e,grandParent:r,curNode:t}}e.ee=0,r===this.W?this.W=r.ne():r.ne(),r.ee=1}return}},e.prototype.se=function(t,e,r){if(void 0===this.W)return this.M+=1,this.W=new this.re(t,e),this.W.ee=0,this.W.rr=this.h,this.h.rr=this.W,this.h.K=this.W,void(this.h.N=this.W);var n,i=this.h.K,o=this.$(i.u,t);if(0!==o){if(o>0)i.K=new this.re(t,e),i.K.rr=i,n=i.K,this.h.K=n;else{var s=this.h.N,a=this.$(s.u,t);if(0===a)return void(s.p=e);if(a<0)s.N=new this.re(t,e),s.N.rr=s,n=s.N,this.h.N=n;else{if(void 0!==r){var c=r.o;if(c!==this.h){var u=this.$(c.u,t);if(0===u)return void(c.p=e);if(u>0){var l=c.L(),h=this.$(l.u,t);if(0===h)return void(l.p=e);h<0&&(n=new this.re(t,e),void 0===l.N?(l.N=n,n.rr=l):(c.K=n,n.rr=c))}}}if(void 0===n){n=this.W;while(1){var f=this.$(n.u,t);if(f>0){if(void 0===n.K){n.K=new this.re(t,e),n.K.rr=n,n=n.K;break}n=n.K}else{if(!(f<0))return void(n.p=e);if(void 0===n.N){n.N=new this.re(t,e),n.N.rr=n,n=n.N;break}n=n.N}}}}}return this.M+=1,n}i.p=e},e.prototype.g=function(t,e){while(t){var r=this.$(t.u,e);if(r<0)t=t.N;else{if(!(r>0))return t;t=t.K}}return t||this.h},e.prototype.clear=function(){this.M=0,this.W=void 0,this.h.rr=void 0,this.h.K=this.h.N=void 0},e.prototype.updateKeyByIterator=function(t,e){var r=t.o;if(r===this.h&&_(),1===this.M)return r.u=e,!0;if(r===this.h.K)return this.$(r.m().u,e)>0&&(r.u=e,!0);if(r===this.h.N)return this.$(r.L().u,e)<0&&(r.u=e,!0);var n=r.L().u;if(this.$(n,e)>=0)return!1;var i=r.m().u;return!(this.$(i,e)<=0)&&(r.u=e,!0)},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=0,r=this;return this.ae(this.W,(function(n){return t===e?(r.G(n),!0):(e+=1,!1)})),this.M},e.prototype.eraseElementByKey=function(t){if(0===this.M)return!1;var e=this.g(this.W,t);return e!==this.h&&(this.G(e),!0)},e.prototype.eraseElementByIterator=function(t){var e=t.o;e===this.h&&_();var r=void 0===e.N,n=0===t.iteratorType;return n?r&&t.next():r&&void 0!==e.K||t.next(),this.G(e),t},e.prototype.forEach=function(t){var e,r,n=0;try{for(var i=z(this),o=i.next();!o.done;o=i.next()){var s=o.value;t(s,n++,this)}}catch(t){e={error:t}}finally{try{o&&!o.done&&(r=i.return)&&r.call(i)}finally{if(e)throw e.error}}},e.prototype.getElementByPos=function(t){var e,r,n;if(t<0||t>this.M-1)throw new RangeError;var i=0;try{for(var o=z(this),s=o.next();!s.done;s=o.next()){var a=s.value;if(i===t){n=a;break}i+=1}}catch(t){e={error:t}}finally{try{s&&!s.done&&(r=o.return)&&r.call(o)}finally{if(e)throw e.error}}return n},e.prototype.getHeight=function(){if(0===this.M)return 0;var t=function(e){return e?Math.max(t(e.K),t(e.N))+1:0};return t(this.W)},e}(s),Y=J,Z=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),X=function(t){function e(e,r,n){var i=t.call(this,n)||this;return i.o=e,i.h=r,0===i.iteratorType?(i.pre=function(){return this.o===this.h.K&&_(),this.o=this.o.L(),this},i.next=function(){return this.o===this.h&&_(),this.o=this.o.m(),this}):(i.pre=function(){return this.o===this.h.N&&_(),this.o=this.o.m(),this},i.next=function(){return this.o===this.h&&_(),this.o=this.o.L(),this}),i}return Z(e,t),Object.defineProperty(e.prototype,"index",{get:function(){var t=this.o,e=this.h.rr;if(t===this.h)return e?e.tr-1:0;var r=0;t.K&&(r+=t.K.tr);while(t!==e){var n=t.rr;t===n.N&&(r+=1,n.K&&(r+=n.K.tr)),t=n}return r},enumerable:!1,configurable:!0}),e}(i),tt=X,et=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),rt=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},nt=function(t){var e="function"===typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},it=function(t){function e(e,r,n,i){var o=t.call(this,e,r,i)||this;return o.container=n,o}return et(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){return this.o===this.h&&_(),this.o.u},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(tt),ot=function(t){function e(e,r,n){void 0===e&&(e=[]);var i=t.call(this,r,n)||this,o=i;return e.forEach((function(t){o.insert(t)})),i}return et(e,t),e.prototype.P=function(t){return rt(this,(function(e){switch(e.label){case 0:return void 0===t?[2]:[5,nt(this.P(t.K))];case 1:return e.sent(),[4,t.u];case 2:return e.sent(),[5,nt(this.P(t.N))];case 3:return e.sent(),[2]}}))},e.prototype.begin=function(){return new it(this.h.K||this.h,this.h,this)},e.prototype.end=function(){return new it(this.h,this.h,this)},e.prototype.rBegin=function(){return new it(this.h.N||this.h,this.h,this,1)},e.prototype.rEnd=function(){return new it(this.h,this.h,this,1)},e.prototype.front=function(){return this.h.K?this.h.K.u:void 0},e.prototype.back=function(){return this.h.N?this.h.N.u:void 0},e.prototype.insert=function(t,e){return this.v(t,void 0,e)},e.prototype.find=function(t){var e=this.g(this.W,t);return new it(e,this.h,this)},e.prototype.lowerBound=function(t){var e=this.U(this.W,t);return new it(e,this.h,this)},e.prototype.upperBound=function(t){var e=this.X(this.W,t);return new it(e,this.h,this)},e.prototype.reverseLowerBound=function(t){var e=this.Y(this.W,t);return new it(e,this.h,this)},e.prototype.reverseUpperBound=function(t){var e=this.Z(this.W,t);return new it(e,this.h,this)},e.prototype.union=function(t){var e=this;return t.forEach((function(t){e.insert(t)})),this.M},e.prototype[Symbol.iterator]=function(){return this.P(this.W)},e}(Y),st=ot,at=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),ct=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},ut=function(t){var e="function"===typeof Symbol&&Symbol.iterator,r=e&&t[e],n=0;if(r)return r.call(t);if(t&&"number"===typeof t.length)return{next:function(){return t&&n>=t.length&&(t=void 0),{value:t&&t[n++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")},lt=function(t){function e(e,r,n,i){var o=t.call(this,e,r,i)||this;return o.container=n,o}return at(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){this.o===this.h&&_();var t=this;return new Proxy([],{get:function(e,r){return"0"===r?t.o.u:"1"===r?t.o.p:void 0},set:function(e,r,n){if("1"!==r)throw new TypeError("props must be 1");return t.o.p=n,!0}})},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(tt),ht=function(t){function e(e,r,n){void 0===e&&(e=[]);var i=t.call(this,r,n)||this,o=i;return e.forEach((function(t){o.setElement(t[0],t[1])})),i}return at(e,t),e.prototype.P=function(t){return ct(this,(function(e){switch(e.label){case 0:return void 0===t?[2]:[5,ut(this.P(t.K))];case 1:return e.sent(),[4,[t.u,t.p]];case 2:return e.sent(),[5,ut(this.P(t.N))];case 3:return e.sent(),[2]}}))},e.prototype.begin=function(){return new lt(this.h.K||this.h,this.h,this)},e.prototype.end=function(){return new lt(this.h,this.h,this)},e.prototype.rBegin=function(){return new lt(this.h.N||this.h,this.h,this,1)},e.prototype.rEnd=function(){return new lt(this.h,this.h,this,1)},e.prototype.front=function(){if(0!==this.M){var t=this.h.K;return[t.u,t.p]}},e.prototype.back=function(){if(0!==this.M){var t=this.h.N;return[t.u,t.p]}},e.prototype.lowerBound=function(t){var e=this.U(this.W,t);return new lt(e,this.h,this)},e.prototype.upperBound=function(t){var e=this.X(this.W,t);return new lt(e,this.h,this)},e.prototype.reverseLowerBound=function(t){var e=this.Y(this.W,t);return new lt(e,this.h,this)},e.prototype.reverseUpperBound=function(t){var e=this.Z(this.W,t);return new lt(e,this.h,this)},e.prototype.setElement=function(t,e,r){return this.v(t,e,r)},e.prototype.find=function(t){var e=this.g(this.W,t);return new lt(e,this.h,this)},e.prototype.getElementByKey=function(t){var e=this.g(this.W,t);return e.p},e.prototype.union=function(t){var e=this;return t.forEach((function(t){e.setElement(t[0],t[1])})),this.M},e.prototype[Symbol.iterator]=function(){return this.P(this.W)},e}(Y),ft=ht;function pt(t){var e=typeof t;return"object"===e&&null!==t||"function"===e}var dt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),yt=function(t){function e(e,r,n){var i=t.call(this,n)||this;return i.o=e,i.h=r,0===i.iteratorType?(i.pre=function(){return this.o.L===this.h&&_(),this.o=this.o.L,this},i.next=function(){return this.o===this.h&&_(),this.o=this.o.m,this}):(i.pre=function(){return this.o.m===this.h&&_(),this.o=this.o.m,this},i.next=function(){return this.o===this.h&&_(),this.o=this.o.L,this}),i}return dt(e,t),e}(i),bt=function(t){function e(){var e=t.call(this)||this;return e._=[],e.I={},e.HASH_TAG=Symbol("@@HASH_TAG"),Object.setPrototypeOf(e.I,null),e.h={},e.h.L=e.h.m=e.H=e.l=e.h,e}return dt(e,t),e.prototype.G=function(t){var e=t.L,r=t.m;e.m=r,r.L=e,t===this.H&&(this.H=r),t===this.l&&(this.l=e),this.M-=1},e.prototype.v=function(t,e,r){var n;if(void 0===r&&(r=pt(t)),r){var i=t[this.HASH_TAG];if(void 0!==i)return this._[i].p=e,this.M;Object.defineProperty(t,this.HASH_TAG,{value:this._.length,configurable:!0}),n={u:t,p:e,L:this.l,m:this.h},this._.push(n)}else{var o=this.I[t];if(o)return o.p=e,this.M;n={u:t,p:e,L:this.l,m:this.h},this.I[t]=n}return 0===this.M?(this.H=n,this.h.m=n):this.l.m=n,this.l=n,this.h.L=n,++this.M},e.prototype.g=function(t,e){if(void 0===e&&(e=pt(t)),e){var r=t[this.HASH_TAG];return void 0===r?this.h:this._[r]}return this.I[t]||this.h},e.prototype.clear=function(){var t=this.HASH_TAG;this._.forEach((function(e){delete e.u[t]})),this._=[],this.I={},Object.setPrototypeOf(this.I,null),this.M=0,this.H=this.l=this.h.L=this.h.m=this.h},e.prototype.eraseElementByKey=function(t,e){var r;if(void 0===e&&(e=pt(t)),e){var n=t[this.HASH_TAG];if(void 0===n)return!1;delete t[this.HASH_TAG],r=this._[n],delete this._[n]}else{if(r=this.I[t],void 0===r)return!1;delete this.I[t]}return this.G(r),!0},e.prototype.eraseElementByIterator=function(t){var e=t.o;return e===this.h&&_(),this.G(e),t.next()},e.prototype.eraseElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return this.G(e),this.M},e}(s),gt=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),mt=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},wt=function(t){function e(e,r,n,i){var o=t.call(this,e,r,i)||this;return o.container=n,o}return gt(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){return this.o===this.h&&_(),this.o.u},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(yt),vt=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this,n=r;return e.forEach((function(t){n.insert(t)})),r}return gt(e,t),e.prototype.begin=function(){return new wt(this.H,this.h,this)},e.prototype.end=function(){return new wt(this.h,this.h,this)},e.prototype.rBegin=function(){return new wt(this.l,this.h,this,1)},e.prototype.rEnd=function(){return new wt(this.h,this.h,this,1)},e.prototype.front=function(){return this.H.u},e.prototype.back=function(){return this.l.u},e.prototype.insert=function(t,e){return this.v(t,void 0,e)},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return e.u},e.prototype.find=function(t,e){var r=this.g(t,e);return new wt(r,this.h,this)},e.prototype.forEach=function(t){var e=0,r=this.H;while(r!==this.h)t(r.u,e++,this),r=r.m},e.prototype[Symbol.iterator]=function(){return function(){var t;return mt(this,(function(e){switch(e.label){case 0:t=this.H,e.label=1;case 1:return t===this.h?[3,3]:[4,t.u];case 2:return e.sent(),t=t.m,[3,1];case 3:return[2]}}))}.bind(this)()},e}(bt),_t=vt,St=function(){var t=function(e,r){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},t(e,r)};return function(e,r){if("function"!==typeof r&&null!==r)throw new TypeError("Class extends value "+String(r)+" is not a constructor or null");function n(){this.constructor=e}t(e,r),e.prototype=null===r?Object.create(r):(n.prototype=r.prototype,new n)}}(),Et=function(t,e){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"===typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(t){return function(e){return c([t,e])}}function c(o){if(r)throw new TypeError("Generator is already executing.");while(s)try{if(r=1,n&&(i=2&o[0]?n["return"]:o[0]?n["throw"]||((i=n["return"])&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(i=s.trys,!(i=i.length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=e.call(t,s)}catch(t){o=[6,t],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}},kt=function(t){function e(e,r,n,i){var o=t.call(this,e,r,i)||this;return o.container=n,o}return St(e,t),Object.defineProperty(e.prototype,"pointer",{get:function(){this.o===this.h&&_();var t=this;return new Proxy([],{get:function(e,r){return"0"===r?t.o.u:"1"===r?t.o.p:void 0},set:function(e,r,n){if("1"!==r)throw new TypeError("props must be 1");return t.o.p=n,!0}})},enumerable:!1,configurable:!0}),e.prototype.copy=function(){return new e(this.o,this.h,this.container,this.iteratorType)},e}(yt),At=function(t){function e(e){void 0===e&&(e=[]);var r=t.call(this)||this,n=r;return e.forEach((function(t){n.setElement(t[0],t[1])})),r}return St(e,t),e.prototype.begin=function(){return new kt(this.H,this.h,this)},e.prototype.end=function(){return new kt(this.h,this.h,this)},e.prototype.rBegin=function(){return new kt(this.l,this.h,this,1)},e.prototype.rEnd=function(){return new kt(this.h,this.h,this,1)},e.prototype.front=function(){if(0!==this.M)return[this.H.u,this.H.p]},e.prototype.back=function(){if(0!==this.M)return[this.l.u,this.l.p]},e.prototype.setElement=function(t,e,r){return this.v(t,e,r)},e.prototype.getElementByKey=function(t,e){if(void 0===e&&(e=pt(t)),e){var r=t[this.HASH_TAG];return void 0!==r?this._[r].p:void 0}var n=this.I[t];return n?n.p:void 0},e.prototype.getElementByPos=function(t){if(t<0||t>this.M-1)throw new RangeError;var e=this.H;while(t--)e=e.m;return[e.u,e.p]},e.prototype.find=function(t,e){var r=this.g(t,e);return new kt(r,this.h,this)},e.prototype.forEach=function(t){var e=0,r=this.H;while(r!==this.h)t([r.u,r.p],e++,this),r=r.m},e.prototype[Symbol.iterator]=function(){return function(){var t;return Et(this,(function(e){switch(e.label){case 0:t=this.H,e.label=1;case 1:return t===this.h?[3,3]:[4,[t.u,t.p]];case 2:return e.sent(),t=t.m,[3,1];case 3:return[2]}}))}.bind(this)()},e}(bt),Ot=At},"417f7":function(t,e,r){"use strict";t.exports=EvalError},"4d84":function(t,e,r){"use strict";const n=r("3d67").OrderedSet,i=r("34eb")("number-allocator:trace"),o=r("34eb")("number-allocator:error");function s(t,e){this.low=t,this.high=e}function a(t,e){if(!(this instanceof a))return new a(t,e);this.min=t,this.max=e,this.ss=new n([],(t,e)=>t.compare(e)),i("Create"),this.clear()}s.prototype.equals=function(t){return this.low===t.low&&this.high===t.high},s.prototype.compare=function(t){return this.low<t.low&&this.high<t.low?-1:t.low<this.low&&t.high<this.low?1:0},a.prototype.firstVacant=function(){return 0===this.ss.size()?null:this.ss.front().low},a.prototype.alloc=function(){if(0===this.ss.size())return i("alloc():empty"),null;const t=this.ss.begin(),e=t.pointer.low,r=t.pointer.high,n=e;return n+1<=r?this.ss.updateKeyByIterator(t,new s(e+1,r)):this.ss.eraseElementByPos(0),i("alloc():"+n),n},a.prototype.use=function(t){const e=new s(t,t),r=this.ss.lowerBound(e);if(!r.equals(this.ss.end())){const n=r.pointer.low,o=r.pointer.high;return r.pointer.equals(e)?(this.ss.eraseElementByIterator(r),i("use():"+t),!0):!(n>t)&&(n===t?(this.ss.updateKeyByIterator(r,new s(n+1,o)),i("use():"+t),!0):o===t?(this.ss.updateKeyByIterator(r,new s(n,o-1)),i("use():"+t),!0):(this.ss.updateKeyByIterator(r,new s(t+1,o)),this.ss.insert(new s(n,t-1)),i("use():"+t),!0))}return i("use():failed"),!1},a.prototype.free=function(t){if(t<this.min||t>this.max)return void o("free():"+t+" is out of range");const e=new s(t,t),r=this.ss.upperBound(e);if(r.equals(this.ss.end())){if(r.equals(this.ss.begin()))return void this.ss.insert(e);r.pre();const n=r.pointer.high,i=r.pointer.high;i+1===t?this.ss.updateKeyByIterator(r,new s(n,t)):this.ss.insert(e)}else if(r.equals(this.ss.begin()))if(t+1===r.pointer.low){const e=r.pointer.high;this.ss.updateKeyByIterator(r,new s(t,e))}else this.ss.insert(e);else{const n=r.pointer.low,i=r.pointer.high;r.pre();const o=r.pointer.low,a=r.pointer.high;a+1===t?t+1===n?(this.ss.eraseElementByIterator(r),this.ss.updateKeyByIterator(r,new s(o,i))):this.ss.updateKeyByIterator(r,new s(o,t)):t+1===n?(this.ss.eraseElementByIterator(r.next()),this.ss.insert(new s(t,i))):this.ss.insert(e)}i("free():"+t)},a.prototype.clear=function(){i("clear()"),this.ss.clear(),this.ss.insert(new s(this.min,this.max))},a.prototype.intervalCount=function(){return this.ss.size()},a.prototype.dump=function(){console.log("length:"+this.ss.size());for(const t of this.ss)console.log(t)},t.exports=a},5156:function(t,e,r){"use strict";var n="undefined"!==typeof Symbol&&Symbol,i=r("1696");t.exports=function(){return"function"===typeof n&&("function"===typeof Symbol&&("symbol"===typeof n("foo")&&("symbol"===typeof Symbol("bar")&&i())))}},"51e9":function(t,e,r){"use strict";const n=r("f214").Duplex,i=r("3fb5"),o=r("33013");function s(t){if(!(this instanceof s))return new s(t);if("function"===typeof t){this._callback=t;const e=function(t){this._callback&&(this._callback(t),this._callback=null)}.bind(this);this.on("pipe",(function(t){t.on("error",e)})),this.on("unpipe",(function(t){t.removeListener("error",e)})),t=null}o._init.call(this,t),n.call(this)}i(s,n),Object.assign(s.prototype,o.prototype),s.prototype._new=function(t){return new s(t)},s.prototype._write=function(t,e,r){this._appendBuffer(t),"function"===typeof r&&r()},s.prototype._read=function(t){if(!this.length)return this.push(null);t=Math.min(t,this.length),this.push(this.slice(0,t)),this.consume(t)},s.prototype.end=function(t){n.prototype.end.call(this,t),this._callback&&(this._callback(null,this.slice()),this._callback=null)},s.prototype._destroy=function(t,e){this._bufs.length=0,this.length=0,e(t)},s.prototype._isBufferList=function(t){return t instanceof s||t instanceof o||s.isBufferList(t)},s.isBufferList=o.isBufferList,t.exports=s,t.exports.BufferListStream=s,t.exports.BufferList=o},5402:function(t,e,r){"use strict";var n=r("0d25"),i=r("2714"),o=r("ccf1"),s=r("f213"),a=r("33f3"),c=a||s||o;t.exports=function(){var t,e={assert:function(t){if(!e.has(t))throw new n("Side channel does not contain "+i(t))},delete:function(e){return!!t&&t["delete"](e)},get:function(e){return t&&t.get(e)},has:function(e){return!!t&&t.has(e)},set:function(e,r){t||(t=c()),t.set(e,r)}};return e}},"566b":function(t,e,r){var n=r("d633");function i(t){var e=function(){return e.called?e.value:(e.called=!0,e.value=t.apply(this,arguments))};return e.called=!1,e}function o(t){var e=function(){if(e.called)throw new Error(e.onceError);return e.called=!0,e.value=t.apply(this,arguments)},r=t.name||"Function wrapped with `once`";return e.onceError=r+" shouldn't be called more than once",e.called=!1,e}t.exports=n(i),t.exports.strict=n(o),i.proto=i((function(){Object.defineProperty(Function.prototype,"once",{value:function(){return i(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return o(this)},configurable:!0})}))},"56ac":function(t,e,r){(function(e,n){var i=r("035d"),o=r("ab52"),s=r("3fb5"),a=r("f9c1"),c=e.from&&e.from!==Uint8Array.from?e.from([0]):new e([0]),u=function(t,e){t._corked?t.once("uncork",e):e()},l=function(t,e){t._autoDestroy&&t.destroy(e)},h=function(t,e){return function(r){r?l(t,"premature close"===r.message?null:r):e&&!t._ended&&t.end()}},f=function(t,e){return t?t._writableState&&t._writableState.finished?e():t._writableState?t.end(e):(t.end(),void e()):e()},p=function(){},d=function(t){return new i.Readable({objectMode:!0,highWaterMark:16}).wrap(t)},y=function(t,e,r){if(!(this instanceof y))return new y(t,e,r);i.Duplex.call(this,r),this._writable=null,this._readable=null,this._readable2=null,this._autoDestroy=!r||!1!==r.autoDestroy,this._forwardDestroy=!r||!1!==r.destroy,this._forwardEnd=!r||!1!==r.end,this._corked=1,this._ondrain=null,this._drained=!1,this._forwarding=!1,this._unwrite=null,this._unread=null,this._ended=!1,this.destroyed=!1,t&&this.setWritable(t),e&&this.setReadable(e)};s(y,i.Duplex),y.obj=function(t,e,r){return r||(r={}),r.objectMode=!0,r.highWaterMark=16,new y(t,e,r)},y.prototype.cork=function(){1===++this._corked&&this.emit("cork")},y.prototype.uncork=function(){this._corked&&0===--this._corked&&this.emit("uncork")},y.prototype.setWritable=function(t){if(this._unwrite&&this._unwrite(),this.destroyed)t&&t.destroy&&t.destroy();else if(null!==t&&!1!==t){var e=this,r=o(t,{writable:!0,readable:!1},h(this,this._forwardEnd)),i=function(){var t=e._ondrain;e._ondrain=null,t&&t()},s=function(){e._writable.removeListener("drain",i),r()};this._unwrite&&n.nextTick(i),this._writable=t,this._writable.on("drain",i),this._unwrite=s,this.uncork()}else this.end()},y.prototype.setReadable=function(t){if(this._unread&&this._unread(),this.destroyed)t&&t.destroy&&t.destroy();else{if(null===t||!1===t)return this.push(null),void this.resume();var e=this,r=o(t,{writable:!1,readable:!0},h(this)),n=function(){e._forward()},i=function(){e.push(null)},s=function(){e._readable2.removeListener("readable",n),e._readable2.removeListener("end",i),r()};this._drained=!0,this._readable=t,this._readable2=t._readableState?t:d(t),this._readable2.on("readable",n),this._readable2.on("end",i),this._unread=s,this._forward()}},y.prototype._read=function(){this._drained=!0,this._forward()},y.prototype._forward=function(){if(!this._forwarding&&this._readable2&&this._drained){var t;this._forwarding=!0;while(this._drained&&null!==(t=a(this._readable2)))this.destroyed||(this._drained=this.push(t));this._forwarding=!1}},y.prototype.destroy=function(t,e){if(e||(e=p),this.destroyed)return e(null);this.destroyed=!0;var r=this;n.nextTick((function(){r._destroy(t),e(null)}))},y.prototype._destroy=function(t){if(t){var e=this._ondrain;this._ondrain=null,e?e(t):this.emit("error",t)}this._forwardDestroy&&(this._readable&&this._readable.destroy&&this._readable.destroy(),this._writable&&this._writable.destroy&&this._writable.destroy()),this.emit("close")},y.prototype._write=function(t,e,r){if(!this.destroyed)return this._corked?u(this,this._write.bind(this,t,e,r)):t===c?this._finish(r):this._writable?void(!1===this._writable.write(t)?this._ondrain=r:this.destroyed||r()):r()},y.prototype._finish=function(t){var e=this;this.emit("preend"),u(this,(function(){f(e._forwardEnd&&e._writable,(function(){!1===e._writableState.prefinished&&(e._writableState.prefinished=!0),e.emit("prefinish"),u(e,t)}))}))},y.prototype.end=function(t,e,r){return"function"===typeof t?this.end(null,null,t):"function"===typeof e?this.end(t,null,e):(this._ended=!0,t&&this.write(t),this._writableState.ending||this._writableState.destroyed||this.write(c),i.Writable.prototype.end.call(this,r))},t.exports=y}).call(this,r("1c35").Buffer,r("4362"))},"59eb":function(t,e,r){"use strict";t.exports=Math.pow},6155:function(t,e,r){const n=r("4d84");t.exports.NumberAllocator=n},"63f0":function(t,e,r){"use strict";const n=r("2fae"),i=Symbol("max"),o=Symbol("length"),s=Symbol("lengthCalculator"),a=Symbol("allowStale"),c=Symbol("maxAge"),u=Symbol("dispose"),l=Symbol("noDisposeOnSet"),h=Symbol("lruList"),f=Symbol("cache"),p=Symbol("updateAgeOnGet"),d=()=>1;class y{constructor(t){if("number"===typeof t&&(t={max:t}),t||(t={}),t.max&&("number"!==typeof t.max||t.max<0))throw new TypeError("max must be a non-negative number");this[i]=t.max||1/0;const e=t.length||d;if(this[s]="function"!==typeof e?d:e,this[a]=t.stale||!1,t.maxAge&&"number"!==typeof t.maxAge)throw new TypeError("maxAge must be a number");this[c]=t.maxAge||0,this[u]=t.dispose,this[l]=t.noDisposeOnSet||!1,this[p]=t.updateAgeOnGet||!1,this.reset()}set max(t){if("number"!==typeof t||t<0)throw new TypeError("max must be a non-negative number");this[i]=t||1/0,m(this)}get max(){return this[i]}set allowStale(t){this[a]=!!t}get allowStale(){return this[a]}set maxAge(t){if("number"!==typeof t)throw new TypeError("maxAge must be a non-negative number");this[c]=t,m(this)}get maxAge(){return this[c]}set lengthCalculator(t){"function"!==typeof t&&(t=d),t!==this[s]&&(this[s]=t,this[o]=0,this[h].forEach(t=>{t.length=this[s](t.value,t.key),this[o]+=t.length})),m(this)}get lengthCalculator(){return this[s]}get length(){return this[o]}get itemCount(){return this[h].length}rforEach(t,e){e=e||this;for(let r=this[h].tail;null!==r;){const n=r.prev;_(this,t,r,e),r=n}}forEach(t,e){e=e||this;for(let r=this[h].head;null!==r;){const n=r.next;_(this,t,r,e),r=n}}keys(){return this[h].toArray().map(t=>t.key)}values(){return this[h].toArray().map(t=>t.value)}reset(){this[u]&&this[h]&&this[h].length&&this[h].forEach(t=>this[u](t.key,t.value)),this[f]=new Map,this[h]=new n,this[o]=0}dump(){return this[h].map(t=>!g(this,t)&&{k:t.key,v:t.value,e:t.now+(t.maxAge||0)}).toArray().filter(t=>t)}dumpLru(){return this[h]}set(t,e,r){if(r=r||this[c],r&&"number"!==typeof r)throw new TypeError("maxAge must be a number");const n=r?Date.now():0,a=this[s](e,t);if(this[f].has(t)){if(a>this[i])return w(this,this[f].get(t)),!1;const s=this[f].get(t),c=s.value;return this[u]&&(this[l]||this[u](t,c.value)),c.now=n,c.maxAge=r,c.value=e,this[o]+=a-c.length,c.length=a,this.get(t),m(this),!0}const p=new v(t,e,a,n,r);return p.length>this[i]?(this[u]&&this[u](t,e),!1):(this[o]+=p.length,this[h].unshift(p),this[f].set(t,this[h].head),m(this),!0)}has(t){if(!this[f].has(t))return!1;const e=this[f].get(t).value;return!g(this,e)}get(t){return b(this,t,!0)}peek(t){return b(this,t,!1)}pop(){const t=this[h].tail;return t?(w(this,t),t.value):null}del(t){w(this,this[f].get(t))}load(t){this.reset();const e=Date.now();for(let r=t.length-1;r>=0;r--){const n=t[r],i=n.e||0;if(0===i)this.set(n.k,n.v);else{const t=i-e;t>0&&this.set(n.k,n.v,t)}}}prune(){this[f].forEach((t,e)=>b(this,e,!1))}}const b=(t,e,r)=>{const n=t[f].get(e);if(n){const e=n.value;if(g(t,e)){if(w(t,n),!t[a])return}else r&&(t[p]&&(n.value.now=Date.now()),t[h].unshiftNode(n));return e.value}},g=(t,e)=>{if(!e||!e.maxAge&&!t[c])return!1;const r=Date.now()-e.now;return e.maxAge?r>e.maxAge:t[c]&&r>t[c]},m=t=>{if(t[o]>t[i])for(let e=t[h].tail;t[o]>t[i]&&null!==e;){const r=e.prev;w(t,e),e=r}},w=(t,e)=>{if(e){const r=e.value;t[u]&&t[u](r.key,r.value),t[o]-=r.length,t[f].delete(r.key),t[h].removeNode(e)}};class v{constructor(t,e,r,n,i){this.key=t,this.value=e,this.length=r,this.now=n,this.maxAge=i||0}}const _=(t,e,r,n)=>{let i=r.value;g(t,i)&&(w(t,r),t[a]||(i=void 0)),i&&e.call(n,i.value,i.key,t)};t.exports=y},"652a":function(t,e,r){"use strict";var n;function i(t){var e=!1;return function(){e||(e=!0,t.apply(void 0,arguments))}}var o=r("fbd7").codes,s=o.ERR_MISSING_ARGS,a=o.ERR_STREAM_DESTROYED;function c(t){if(t)throw t}function u(t){return t.setHeader&&"function"===typeof t.abort}function l(t,e,o,s){s=i(s);var c=!1;t.on("close",(function(){c=!0})),void 0===n&&(n=r("d9e1")),n(t,{readable:e,writable:o},(function(t){if(t)return s(t);c=!0,s()}));var l=!1;return function(e){if(!c&&!l)return l=!0,u(t)?t.abort():"function"===typeof t.destroy?t.destroy():void s(e||new a("pipe"))}}function h(t){t()}function f(t,e){return t.pipe(e)}function p(t){return t.length?"function"!==typeof t[t.length-1]?c:t.pop():c}function d(){for(var t=arguments.length,e=new Array(t),r=0;r<t;r++)e[r]=arguments[r];var n,i=p(e);if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new s("streams");var o=e.map((function(t,r){var s=r<e.length-1,a=r>0;return l(t,s,a,(function(t){n||(n=t),t&&o.forEach(h),s||(o.forEach(h),i(n))}))}));return e.reduce(f)}t.exports=d},6591:function(t,e,r){"use strict";t.exports=Math.floor},"67d9":function(t,e,r){"use strict";t.exports=URIError},"67ee":function(t,e,r){"use strict";t.exports=SyntaxError},"688e":function(t,e,r){"use strict";var n="Function.prototype.bind called on incompatible ",i=Object.prototype.toString,o=Math.max,s="[object Function]",a=function(t,e){for(var r=[],n=0;n<t.length;n+=1)r[n]=t[n];for(var i=0;i<e.length;i+=1)r[i+t.length]=e[i];return r},c=function(t,e){for(var r=[],n=e||0,i=0;n<t.length;n+=1,i+=1)r[i]=t[n];return r},u=function(t,e){for(var r="",n=0;n<t.length;n+=1)r+=t[n],n+1<t.length&&(r+=e);return r};t.exports=function(t){var e=this;if("function"!==typeof e||i.apply(e)!==s)throw new TypeError(n+e);for(var r,l=c(arguments,1),h=function(){if(this instanceof r){var n=e.apply(this,a(l,arguments));return Object(n)===n?n:this}return e.apply(t,a(l,arguments))},f=o(0,e.length-l.length),p=[],d=0;d<f;d++)p[d]="$"+d;if(r=Function("binder","return function ("+u(p,",")+"){ return binder.apply(this,arguments); }")(h),e.prototype){var y=function(){};y.prototype=e.prototype,r.prototype=new y,y.prototype=null}return r}},"6b3f":function(t,e,r){"use strict";t.exports="undefined"!==typeof Reflect&&Reflect&&Reflect.apply},"6c3d":function(t,e,r){"use strict";t.exports=Object.getOwnPropertyDescriptor},7058:function(t,e,r){"use strict";t.exports=i;var n=r("3ca2");function i(t){if(!(this instanceof i))return new i(t);n.call(this,t)}r("3fb5")(i,n),i.prototype._transform=function(t,e,r){r(null,t)}},7135:function(t,e,r){(function(e){const n=r("b289"),i=e.allocUnsafe(0),o=e.from([0]),s=r("f0cb"),a=r("966d").nextTick,c=r("34eb")("mqtt-packet:writeToStream"),u=s.cache,l=s.generateNumber,h=s.generateCache,f=s.genBufVariableByteInt,p=s.generate4ByteBuffer;let d=j,y=!0;function b(t,e,r){switch(c("generate called"),e.cork&&(e.cork(),a(g,e)),y&&(y=!1,h()),c("generate: packet.cmd: %s",t.cmd),t.cmd){case"connect":return m(t,e,r);case"connack":return w(t,e,r);case"publish":return v(t,e,r);case"puback":case"pubrec":case"pubrel":case"pubcomp":return _(t,e,r);case"subscribe":return S(t,e,r);case"suback":return E(t,e,r);case"unsubscribe":return k(t,e,r);case"unsuback":return A(t,e,r);case"pingreq":case"pingresp":return O(t,e,r);case"disconnect":return P(t,e,r);case"auth":return R(t,e,r);default:return e.emit("error",new Error("Unknown command")),!1}}function g(t){t.uncork()}function m(t,r,i){const o=t||{},s=o.protocolId||"MQTT";let a=o.protocolVersion||4;const c=o.will;let u=o.clean;const l=o.keepalive||0,h=o.clientId||"",f=o.username,p=o.password,y=o.properties;void 0===u&&(u=!0);let b=0;if(!s||"string"!==typeof s&&!e.isBuffer(s))return r.emit("error",new Error("Invalid protocolId")),!1;if(b+=s.length+2,3!==a&&4!==a&&5!==a)return r.emit("error",new Error("Invalid protocol version")),!1;if(b+=1,("string"===typeof h||e.isBuffer(h))&&(h||a>=4)&&(h||u))b+=e.byteLength(h)+2;else{if(a<4)return r.emit("error",new Error("clientId must be supplied before 3.1.1")),!1;if(1*u===0)return r.emit("error",new Error("clientId must be given if cleanSession set to 0")),!1}if("number"!==typeof l||l<0||l>65535||l%1!==0)return r.emit("error",new Error("Invalid keepalive")),!1;if(b+=2,b+=1,5===a){var g=L(r,y);if(!g)return!1;b+=g.length}if(c){if("object"!==typeof c)return r.emit("error",new Error("Invalid will")),!1;if(!c.topic||"string"!==typeof c.topic)return r.emit("error",new Error("Invalid will topic")),!1;if(b+=e.byteLength(c.topic)+2,b+=2,c.payload){if(!(c.payload.length>=0))return r.emit("error",new Error("Invalid will payload")),!1;"string"===typeof c.payload?b+=e.byteLength(c.payload):b+=c.payload.length}var m={};if(5===a){if(m=L(r,c.properties),!m)return!1;b+=m.length}}let w=!1;if(null!=f){if(!W(f))return r.emit("error",new Error("Invalid username")),!1;w=!0,b+=e.byteLength(f)+2}if(null!=p){if(!w)return r.emit("error",new Error("Username is required to use password")),!1;if(!W(p))return r.emit("error",new Error("Invalid password")),!1;b+=q(p)+2}r.write(n.CONNECT_HEADER),x(r,b),B(r,s),o.bridgeMode&&(a+=128),r.write(131===a?n.VERSION131:132===a?n.VERSION132:4===a?n.VERSION4:5===a?n.VERSION5:n.VERSION3);let v=0;return v|=null!=f?n.USERNAME_MASK:0,v|=null!=p?n.PASSWORD_MASK:0,v|=c&&c.retain?n.WILL_RETAIN_MASK:0,v|=c&&c.qos?c.qos<<n.WILL_QOS_SHIFT:0,v|=c?n.WILL_FLAG_MASK:0,v|=u?n.CLEAN_SESSION_MASK:0,r.write(e.from([v])),d(r,l),5===a&&g.write(),B(r,h),c&&(5===a&&m.write(),I(r,c.topic),B(r,c.payload)),null!=f&&B(r,f),null!=p&&B(r,p),!0}function w(t,r,i){const s=i?i.protocolVersion:4,a=t||{},c=5===s?a.reasonCode:a.returnCode,u=a.properties;let l=2;if("number"!==typeof c)return r.emit("error",new Error("Invalid return code")),!1;let h=null;if(5===s){if(h=L(r,u),!h)return!1;l+=h.length}return r.write(n.CONNACK_HEADER),x(r,l),r.write(a.sessionPresent?n.SESSIONPRESENT_HEADER:o),r.write(e.from([c])),null!=h&&h.write(),!0}function v(t,r,o){c("publish: packet: %o",t);const s=o?o.protocolVersion:4,a=t||{},u=a.qos||0,l=a.retain?n.RETAIN_MASK:0,h=a.topic,f=a.payload||i,p=a.messageId,y=a.properties;let b=0;if("string"===typeof h)b+=e.byteLength(h)+2;else{if(!e.isBuffer(h))return r.emit("error",new Error("Invalid topic")),!1;b+=h.length+2}if(e.isBuffer(f)?b+=f.length:b+=e.byteLength(f),u&&"number"!==typeof p)return r.emit("error",new Error("Invalid messageId")),!1;u&&(b+=2);let g=null;if(5===s){if(g=L(r,y),!g)return!1;b+=g.length}return r.write(n.PUBLISH_HEADER[u][a.dup?1:0][l?1:0]),x(r,b),d(r,q(h)),r.write(h),u>0&&d(r,p),null!=g&&g.write(),c("publish: payload: %o",f),r.write(f)}function _(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.cmd||"puback",c=s.messageId,u=s.dup&&"pubrel"===a?n.DUP_MASK:0;let l=0;const h=s.reasonCode,f=s.properties;let p=5===o?3:2;if("pubrel"===a&&(l=1),"number"!==typeof c)return r.emit("error",new Error("Invalid messageId")),!1;let y=null;if(5===o&&"object"===typeof f){if(y=D(r,f,i,p),!y)return!1;p+=y.length}return r.write(n.ACKS[a][l][u][0]),x(r,p),d(r,c),5===o&&r.write(e.from([h])),null!==y&&y.write(),!0}function S(t,r,i){c("subscribe: packet: ");const o=i?i.protocolVersion:4,s=t||{},a=s.dup?n.DUP_MASK:0,u=s.messageId,l=s.subscriptions,h=s.properties;let f=0;if("number"!==typeof u)return r.emit("error",new Error("Invalid messageId")),!1;f+=2;let p=null;if(5===o){if(p=L(r,h),!p)return!1;f+=p.length}if("object"!==typeof l||!l.length)return r.emit("error",new Error("Invalid subscriptions")),!1;for(let n=0;n<l.length;n+=1){const t=l[n].topic,i=l[n].qos;if("string"!==typeof t)return r.emit("error",new Error("Invalid subscriptions - invalid topic")),!1;if("number"!==typeof i)return r.emit("error",new Error("Invalid subscriptions - invalid qos")),!1;if(5===o){const t=l[n].nl||!1;if("boolean"!==typeof t)return r.emit("error",new Error("Invalid subscriptions - invalid No Local")),!1;const e=l[n].rap||!1;if("boolean"!==typeof e)return r.emit("error",new Error("Invalid subscriptions - invalid Retain as Published")),!1;const i=l[n].rh||0;if("number"!==typeof i||i>2)return r.emit("error",new Error("Invalid subscriptions - invalid Retain Handling")),!1}f+=e.byteLength(t)+2+1}c("subscribe: writing to stream: %o",n.SUBSCRIBE_HEADER),r.write(n.SUBSCRIBE_HEADER[1][a?1:0][0]),x(r,f),d(r,u),null!==p&&p.write();let y=!0;for(const c of l){const t=c.topic,i=c.qos,s=+c.nl,a=+c.rap,u=c.rh;let l;I(r,t),l=n.SUBSCRIBE_OPTIONS_QOS[i],5===o&&(l|=s?n.SUBSCRIBE_OPTIONS_NL:0,l|=a?n.SUBSCRIBE_OPTIONS_RAP:0,l|=u?n.SUBSCRIBE_OPTIONS_RH[u]:0),y=r.write(e.from([l]))}return y}function E(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.messageId,c=s.granted,u=s.properties;let l=0;if("number"!==typeof a)return r.emit("error",new Error("Invalid messageId")),!1;if(l+=2,"object"!==typeof c||!c.length)return r.emit("error",new Error("Invalid qos vector")),!1;for(let e=0;e<c.length;e+=1){if("number"!==typeof c[e])return r.emit("error",new Error("Invalid qos vector")),!1;l+=1}let h=null;if(5===o){if(h=D(r,u,i,l),!h)return!1;l+=h.length}return r.write(n.SUBACK_HEADER),x(r,l),d(r,a),null!==h&&h.write(),r.write(e.from(c))}function k(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.messageId,c=s.dup?n.DUP_MASK:0,u=s.unsubscriptions,l=s.properties;let h=0;if("number"!==typeof a)return r.emit("error",new Error("Invalid messageId")),!1;if(h+=2,"object"!==typeof u||!u.length)return r.emit("error",new Error("Invalid unsubscriptions")),!1;for(let n=0;n<u.length;n+=1){if("string"!==typeof u[n])return r.emit("error",new Error("Invalid unsubscriptions")),!1;h+=e.byteLength(u[n])+2}let f=null;if(5===o){if(f=L(r,l),!f)return!1;h+=f.length}r.write(n.UNSUBSCRIBE_HEADER[1][c?1:0][0]),x(r,h),d(r,a),null!==f&&f.write();let p=!0;for(let e=0;e<u.length;e++)p=I(r,u[e]);return p}function A(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.messageId,c=s.dup?n.DUP_MASK:0,u=s.granted,l=s.properties,h=s.cmd,f=0;let p=2;if("number"!==typeof a)return r.emit("error",new Error("Invalid messageId")),!1;if(5===o){if("object"!==typeof u||!u.length)return r.emit("error",new Error("Invalid qos vector")),!1;for(let t=0;t<u.length;t+=1){if("number"!==typeof u[t])return r.emit("error",new Error("Invalid qos vector")),!1;p+=1}}let y=null;if(5===o){if(y=D(r,l,i,p),!y)return!1;p+=y.length}return r.write(n.ACKS[h][f][c][0]),x(r,p),d(r,a),null!==y&&y.write(),5===o&&r.write(e.from(u)),!0}function O(t,e,r){return e.write(n.EMPTY[t.cmd])}function P(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.reasonCode,c=s.properties;let u=5===o?1:0,l=null;if(5===o){if(l=D(r,c,i,u),!l)return!1;u+=l.length}return r.write(e.from([n.codes.disconnect<<4])),x(r,u),5===o&&r.write(e.from([a])),null!==l&&l.write(),!0}function R(t,r,i){const o=i?i.protocolVersion:4,s=t||{},a=s.reasonCode,c=s.properties;let u=5===o?1:0;5!==o&&r.emit("error",new Error("Invalid mqtt version for auth packet"));const l=D(r,c,i,u);return!!l&&(u+=l.length,r.write(e.from([n.codes.auth<<4])),x(r,u),r.write(e.from([a])),null!==l&&l.write(),!0)}Object.defineProperty(b,"cacheNumbers",{get(){return d===j},set(t){t?(u&&0!==Object.keys(u).length||(y=!0),d=j):(y=!1,d=C)}});const M={};function x(t,e){if(e>n.VARBYTEINT_MAX)return t.emit("error",new Error("Invalid variable byte integer: "+e)),!1;let r=M[e];return r||(r=f(e),e<16384&&(M[e]=r)),c("writeVarByteInt: writing to stream: %o",r),t.write(r)}function I(t,r){const n=e.byteLength(r);return d(t,n),c("writeString: %s",r),t.write(r,"utf8")}function T(t,e,r){I(t,e),I(t,r)}function j(t,e){return c("writeNumberCached: number: %d",e),c("writeNumberCached: %o",u[e]),t.write(u[e])}function C(t,e){const r=l(e);return c("writeNumberGenerated: %o",r),t.write(r)}function N(t,e){const r=p(e);return c("write4ByteNumber: %o",r),t.write(r)}function B(t,e){"string"===typeof e?I(t,e):e?(d(t,e.length),t.write(e)):d(t,0)}function L(t,r){if("object"!==typeof r||null!=r.length)return{length:1,write(){F(t,{},0)}};let i=0;function o(r,i){const o=n.propertiesTypes[r];let s=0;switch(o){case"byte":if("boolean"!==typeof i)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=2;break;case"int8":if("number"!==typeof i||i<0||i>255)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=2;break;case"binary":if(i&&null===i)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=1+e.byteLength(i)+2;break;case"int16":if("number"!==typeof i||i<0||i>65535)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=3;break;case"int32":if("number"!==typeof i||i<0||i>4294967295)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=5;break;case"var":if("number"!==typeof i||i<0||i>268435455)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=1+e.byteLength(f(i));break;case"string":if("string"!==typeof i)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=3+e.byteLength(i.toString());break;case"pair":if("object"!==typeof i)return t.emit("error",new Error(`Invalid ${r}: ${i}`)),!1;s+=Object.getOwnPropertyNames(i).reduce((t,r)=>{const n=i[r];return Array.isArray(n)?t+=n.reduce((t,n)=>(t+=3+e.byteLength(r.toString())+2+e.byteLength(n.toString()),t),0):t+=3+e.byteLength(r.toString())+2+e.byteLength(i[r].toString()),t},0);break;default:return t.emit("error",new Error(`Invalid property ${r}: ${i}`)),!1}return s}if(r)for(const e in r){let t=0,n=0;const s=r[e];if(Array.isArray(s))for(let r=0;r<s.length;r++){if(n=o(e,s[r]),!n)return!1;t+=n}else{if(n=o(e,s),!n)return!1;t=n}if(!t)return!1;i+=t}const s=e.byteLength(f(i));return{length:s+i,write(){F(t,r,i)}}}function D(t,e,r,n){const i=["reasonString","userProperties"],o=r&&r.properties&&r.properties.maximumPacketSize?r.properties.maximumPacketSize:0;let s=L(t,e);if(o)while(n+s.length>o){const r=i.shift();if(!r||!e[r])return!1;delete e[r],s=L(t,e)}return s}function U(t,r,i){const o=n.propertiesTypes[r];switch(o){case"byte":t.write(e.from([n.properties[r]])),t.write(e.from([+i]));break;case"int8":t.write(e.from([n.properties[r]])),t.write(e.from([i]));break;case"binary":t.write(e.from([n.properties[r]])),B(t,i);break;case"int16":t.write(e.from([n.properties[r]])),d(t,i);break;case"int32":t.write(e.from([n.properties[r]])),N(t,i);break;case"var":t.write(e.from([n.properties[r]])),x(t,i);break;case"string":t.write(e.from([n.properties[r]])),I(t,i);break;case"pair":Object.getOwnPropertyNames(i).forEach(o=>{const s=i[o];Array.isArray(s)?s.forEach(i=>{t.write(e.from([n.properties[r]])),T(t,o.toString(),i.toString())}):(t.write(e.from([n.properties[r]])),T(t,o.toString(),s.toString()))});break;default:return t.emit("error",new Error(`Invalid property ${r} value: ${i}`)),!1}}function F(t,e,r){x(t,r);for(const n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&null!==e[n]){const r=e[n];if(Array.isArray(r))for(let e=0;e<r.length;e++)U(t,n,r[e]);else U(t,n,r)}}function q(t){return t?t instanceof e?t.length:e.byteLength(t):0}function W(t){return"string"===typeof t||t instanceof e}t.exports=b}).call(this,r("1c35").Buffer)},"71c9":function(t,e,r){"use strict";var n=Object.defineProperty||!1;if(n)try{n({},"a",{value:1})}catch(i){n=!1}t.exports=n},"782c":function(t,e,r){"use strict";(function(e){var n;function i(t,e,r){return e=o(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function o(t){var e=s(t,"string");return"symbol"===typeof e?e:String(e)}function s(t,e){if("object"!==typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var a=r("bf09"),c=Symbol("lastResolve"),u=Symbol("lastReject"),l=Symbol("error"),h=Symbol("ended"),f=Symbol("lastPromise"),p=Symbol("handlePromise"),d=Symbol("stream");function y(t,e){return{value:t,done:e}}function b(t){var e=t[c];if(null!==e){var r=t[d].read();null!==r&&(t[f]=null,t[c]=null,t[u]=null,e(y(r,!1)))}}function g(t){e.nextTick(b,t)}function m(t,e){return function(r,n){t.then((function(){e[h]?r(y(void 0,!0)):e[p](r,n)}),n)}}var w=Object.getPrototypeOf((function(){})),v=Object.setPrototypeOf((n={get stream(){return this[d]},next:function(){var t=this,r=this[l];if(null!==r)return Promise.reject(r);if(this[h])return Promise.resolve(y(void 0,!0));if(this[d].destroyed)return new Promise((function(r,n){e.nextTick((function(){t[l]?n(t[l]):r(y(void 0,!0))}))}));var n,i=this[f];if(i)n=new Promise(m(i,this));else{var o=this[d].read();if(null!==o)return Promise.resolve(y(o,!1));n=new Promise(this[p])}return this[f]=n,n}},i(n,Symbol.asyncIterator,(function(){return this})),i(n,"return",(function(){var t=this;return new Promise((function(e,r){t[d].destroy(null,(function(t){t?r(t):e(y(void 0,!0))}))}))})),n),w),_=function(t){var e,r=Object.create(v,(e={},i(e,d,{value:t,writable:!0}),i(e,c,{value:null,writable:!0}),i(e,u,{value:null,writable:!0}),i(e,l,{value:null,writable:!0}),i(e,h,{value:t._readableState.endEmitted,writable:!0}),i(e,p,{value:function(t,e){var n=r[d].read();n?(r[f]=null,r[c]=null,r[u]=null,t(y(n,!1))):(r[c]=t,r[u]=e)},writable:!0}),e));return r[f]=null,a(t,(function(t){if(t&&"ERR_STREAM_PREMATURE_CLOSE"!==t.code){var e=r[u];return null!==e&&(r[f]=null,r[c]=null,r[u]=null,e(t)),void(r[l]=t)}var n=r[c];null!==n&&(r[f]=null,r[c]=null,r[u]=null,n(y(void 0,!0))),r[h]=!0})),t.on("readable",g.bind(null,r)),r};t.exports=_}).call(this,r("4362"))},"7f0f":function(t,e,r){(function(e){const n=r("7135"),i=r("faa1");function o(t,e){const r=new s;return n(t,r,e),r.concat()}class s extends i{constructor(){super(),this._array=new Array(20),this._i=0}write(t){return this._array[this._i++]=t,!0}concat(){let t=0;const r=new Array(this._array.length),n=this._array;let i,o=0;for(i=0;i<n.length&&void 0!==n[i];i++)"string"!==typeof n[i]?r[i]=n[i].length:r[i]=e.byteLength(n[i]),t+=r[i];const s=e.allocUnsafe(t);for(i=0;i<n.length&&void 0!==n[i];i++)"string"!==typeof n[i]?(n[i].copy(s,o),o+=r[i]):(s.write(n[i],o),o+=r[i]);return s}}t.exports=o}).call(this,r("1c35").Buffer)},"7f73":function(t,e,r){"use strict";t.exports=function(){throw new Error("ws does not work in the browser. Browser clients must use the native WebSocket object")}},8311:function(t,e,r){"use strict";const{Buffer:n}=r("1c35"),i=r("035d").Transform,o=r("56ac");let s,a,c;function u(){const t=new i;return t._write=function(t,e,r){s.send({data:t.buffer,success:function(){r()},fail:function(t){r(new Error(t))}})},t._flush=function(t){s.close({success:function(){t()}})},t}function l(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function h(t,e){const r="wxs"===t.protocol?"wss":"ws";let n=r+"://"+t.hostname+t.path;return t.port&&80!==t.port&&443!==t.port&&(n=r+"://"+t.hostname+":"+t.port+t.path),"function"===typeof t.transformWsUrl&&(n=t.transformWsUrl(n,t,e)),n}function f(){s.onOpen((function(){c.setReadable(a),c.setWritable(a),c.emit("connect")})),s.onMessage((function(t){let e=t.data;e=e instanceof ArrayBuffer?n.from(e):n.from(e,"utf8"),a.push(e)})),s.onClose((function(){c.end(),c.destroy()})),s.onError((function(t){c.destroy(new Error(t.errMsg))}))}function p(t,e){if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");const r="MQIsdp"===e.protocolId&&3===e.protocolVersion?"mqttv3.1":"mqtt";l(e);const n=h(e,t);s=wx.connectSocket({url:n,protocols:[r]}),a=u(),c=o.obj(),c._destroy=function(t,e){s.close({success:function(){e&&e(t)}})};const i=c.destroy;return c.destroy=function(){c.destroy=i;const t=this;setTimeout((function(){s.close({fail:function(){t._destroy(new Error)}})}),0)}.bind(c),f(),c}t.exports=p},"833a":function(t,e,r){"use strict";var n=r("f2e1"),i=r("17bc"),o=r("c3e0");t.exports=n?function(t){return n(t)}:i?function(t){if(!t||"object"!==typeof t&&"function"!==typeof t)throw new TypeError("getProto: not an object");return i(t)}:o?function(t){return o(t)}:null},"85f8":function(t,e,r){"use strict";(function(e,n){var i;t.exports=M,M.ReadableState=R;r("faa1").EventEmitter;var o=function(t,e){return t.listeners(e).length},s=r("9ede"),a=r("1c35").Buffer,c=("undefined"!==typeof e?e:"undefined"!==typeof window?window:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function u(t){return a.from(t)}function l(t){return a.isBuffer(t)||t instanceof c}var h,f=r(13);h=f&&f.debuglog?f.debuglog("stream"):function(){};var p,d,y,b=r("f688"),g=r("edb3"),m=r("31b5"),w=m.getHighWaterMark,v=r("fbd7").codes,_=v.ERR_INVALID_ARG_TYPE,S=v.ERR_STREAM_PUSH_AFTER_EOF,E=v.ERR_METHOD_NOT_IMPLEMENTED,k=v.ERR_STREAM_UNSHIFT_AFTER_END_EVENT;r("3fb5")(M,s);var A=g.errorOrDestroy,O=["error","close","destroy","pause","resume"];function P(t,e,r){if("function"===typeof t.prependListener)return t.prependListener(e,r);t._events&&t._events[e]?Array.isArray(t._events[e])?t._events[e].unshift(r):t._events[e]=[r,t._events[e]]:t.on(e,r)}function R(t,e,n){i=i||r("be3f"),t=t||{},"boolean"!==typeof n&&(n=e instanceof i),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.readableObjectMode),this.highWaterMark=w(this,t,"readableHighWaterMark",n),this.buffer=new b,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.paused=!0,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.destroyed=!1,this.defaultEncoding=t.defaultEncoding||"utf8",this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,t.encoding&&(p||(p=r("7d72").StringDecoder),this.decoder=new p(t.encoding),this.encoding=t.encoding)}function M(t){if(i=i||r("be3f"),!(this instanceof M))return new M(t);var e=this instanceof i;this._readableState=new R(t,this,e),this.readable=!0,t&&("function"===typeof t.read&&(this._read=t.read),"function"===typeof t.destroy&&(this._destroy=t.destroy)),s.call(this)}function x(t,e,r,n,i){h("readableAddChunk",e);var o,s=t._readableState;if(null===e)s.reading=!1,B(t,s);else if(i||(o=T(s,e)),o)A(t,o);else if(s.objectMode||e&&e.length>0)if("string"===typeof e||s.objectMode||Object.getPrototypeOf(e)===a.prototype||(e=u(e)),n)s.endEmitted?A(t,new k):I(t,s,e,!0);else if(s.ended)A(t,new S);else{if(s.destroyed)return!1;s.reading=!1,s.decoder&&!r?(e=s.decoder.write(e),s.objectMode||0!==e.length?I(t,s,e,!1):U(t,s)):I(t,s,e,!1)}else n||(s.reading=!1,U(t,s));return!s.ended&&(s.length<s.highWaterMark||0===s.length)}function I(t,e,r,n){e.flowing&&0===e.length&&!e.sync?(e.awaitDrain=0,t.emit("data",r)):(e.length+=e.objectMode?1:r.length,n?e.buffer.unshift(r):e.buffer.push(r),e.needReadable&&L(t)),U(t,e)}function T(t,e){var r;return l(e)||"string"===typeof e||void 0===e||t.objectMode||(r=new _("chunk",["string","Buffer","Uint8Array"],e)),r}Object.defineProperty(M.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&this._readableState.destroyed},set:function(t){this._readableState&&(this._readableState.destroyed=t)}}),M.prototype.destroy=g.destroy,M.prototype._undestroy=g.undestroy,M.prototype._destroy=function(t,e){e(t)},M.prototype.push=function(t,e){var r,n=this._readableState;return n.objectMode?r=!0:"string"===typeof t&&(e=e||n.defaultEncoding,e!==n.encoding&&(t=a.from(t,e),e=""),r=!0),x(this,t,e,!1,r)},M.prototype.unshift=function(t){return x(this,t,null,!0,!1)},M.prototype.isPaused=function(){return!1===this._readableState.flowing},M.prototype.setEncoding=function(t){p||(p=r("7d72").StringDecoder);var e=new p(t);this._readableState.decoder=e,this._readableState.encoding=this._readableState.decoder.encoding;var n=this._readableState.buffer.head,i="";while(null!==n)i+=e.write(n.data),n=n.next;return this._readableState.buffer.clear(),""!==i&&this._readableState.buffer.push(i),this._readableState.length=i.length,this};var j=1073741824;function C(t){return t>=j?t=j:(t--,t|=t>>>1,t|=t>>>2,t|=t>>>4,t|=t>>>8,t|=t>>>16,t++),t}function N(t,e){return t<=0||0===e.length&&e.ended?0:e.objectMode?1:t!==t?e.flowing&&e.length?e.buffer.head.data.length:e.length:(t>e.highWaterMark&&(e.highWaterMark=C(t)),t<=e.length?t:e.ended?e.length:(e.needReadable=!0,0))}function B(t,e){if(h("onEofChunk"),!e.ended){if(e.decoder){var r=e.decoder.end();r&&r.length&&(e.buffer.push(r),e.length+=e.objectMode?1:r.length)}e.ended=!0,e.sync?L(t):(e.needReadable=!1,e.emittedReadable||(e.emittedReadable=!0,D(t)))}}function L(t){var e=t._readableState;h("emitReadable",e.needReadable,e.emittedReadable),e.needReadable=!1,e.emittedReadable||(h("emitReadable",e.flowing),e.emittedReadable=!0,n.nextTick(D,t))}function D(t){var e=t._readableState;h("emitReadable_",e.destroyed,e.length,e.ended),e.destroyed||!e.length&&!e.ended||(t.emit("readable"),e.emittedReadable=!1),e.needReadable=!e.flowing&&!e.ended&&e.length<=e.highWaterMark,G(t)}function U(t,e){e.readingMore||(e.readingMore=!0,n.nextTick(F,t,e))}function F(t,e){while(!e.reading&&!e.ended&&(e.length<e.highWaterMark||e.flowing&&0===e.length)){var r=e.length;if(h("maybeReadMore read 0"),t.read(0),r===e.length)break}e.readingMore=!1}function q(t){return function(){var e=t._readableState;h("pipeOnDrain",e.awaitDrain),e.awaitDrain&&e.awaitDrain--,0===e.awaitDrain&&o(t,"data")&&(e.flowing=!0,G(t))}}function W(t){var e=t._readableState;e.readableListening=t.listenerCount("readable")>0,e.resumeScheduled&&!e.paused?e.flowing=!0:t.listenerCount("data")>0&&t.resume()}function K(t){h("readable nexttick read 0"),t.read(0)}function H(t,e){e.resumeScheduled||(e.resumeScheduled=!0,n.nextTick(V,t,e))}function V(t,e){h("resume",e.reading),e.reading||t.read(0),e.resumeScheduled=!1,t.emit("resume"),G(t),e.flowing&&!e.reading&&t.read(0)}function G(t){var e=t._readableState;h("flow",e.flowing);while(e.flowing&&null!==t.read());}function Q(t,e){return 0===e.length?null:(e.objectMode?r=e.buffer.shift():!t||t>=e.length?(r=e.decoder?e.buffer.join(""):1===e.buffer.length?e.buffer.first():e.buffer.concat(e.length),e.buffer.clear()):r=e.buffer.consume(t,e.decoder),r);var r}function $(t){var e=t._readableState;h("endReadable",e.endEmitted),e.endEmitted||(e.ended=!0,n.nextTick(z,e,t))}function z(t,e){if(h("endReadableNT",t.endEmitted,t.length),!t.endEmitted&&0===t.length&&(t.endEmitted=!0,e.readable=!1,e.emit("end"),t.autoDestroy)){var r=e._writableState;(!r||r.autoDestroy&&r.finished)&&e.destroy()}}function J(t,e){for(var r=0,n=t.length;r<n;r++)if(t[r]===e)return r;return-1}M.prototype.read=function(t){h("read",t),t=parseInt(t,10);var e=this._readableState,r=t;if(0!==t&&(e.emittedReadable=!1),0===t&&e.needReadable&&((0!==e.highWaterMark?e.length>=e.highWaterMark:e.length>0)||e.ended))return h("read: emitReadable",e.length,e.ended),0===e.length&&e.ended?$(this):L(this),null;if(t=N(t,e),0===t&&e.ended)return 0===e.length&&$(this),null;var n,i=e.needReadable;return h("need readable",i),(0===e.length||e.length-t<e.highWaterMark)&&(i=!0,h("length less than watermark",i)),e.ended||e.reading?(i=!1,h("reading or ended",i)):i&&(h("do read"),e.reading=!0,e.sync=!0,0===e.length&&(e.needReadable=!0),this._read(e.highWaterMark),e.sync=!1,e.reading||(t=N(r,e))),n=t>0?Q(t,e):null,null===n?(e.needReadable=e.length<=e.highWaterMark,t=0):(e.length-=t,e.awaitDrain=0),0===e.length&&(e.ended||(e.needReadable=!0),r!==t&&e.ended&&$(this)),null!==n&&this.emit("data",n),n},M.prototype._read=function(t){A(this,new E("_read()"))},M.prototype.pipe=function(t,e){var r=this,i=this._readableState;switch(i.pipesCount){case 0:i.pipes=t;break;case 1:i.pipes=[i.pipes,t];break;default:i.pipes.push(t);break}i.pipesCount+=1,h("pipe count=%d opts=%j",i.pipesCount,e);var s=(!e||!1!==e.end)&&t!==n.stdout&&t!==n.stderr,a=s?u:m;function c(t,e){h("onunpipe"),t===r&&e&&!1===e.hasUnpiped&&(e.hasUnpiped=!0,p())}function u(){h("onend"),t.end()}i.endEmitted?n.nextTick(a):r.once("end",a),t.on("unpipe",c);var l=q(r);t.on("drain",l);var f=!1;function p(){h("cleanup"),t.removeListener("close",b),t.removeListener("finish",g),t.removeListener("drain",l),t.removeListener("error",y),t.removeListener("unpipe",c),r.removeListener("end",u),r.removeListener("end",m),r.removeListener("data",d),f=!0,!i.awaitDrain||t._writableState&&!t._writableState.needDrain||l()}function d(e){h("ondata");var n=t.write(e);h("dest.write",n),!1===n&&((1===i.pipesCount&&i.pipes===t||i.pipesCount>1&&-1!==J(i.pipes,t))&&!f&&(h("false write response, pause",i.awaitDrain),i.awaitDrain++),r.pause())}function y(e){h("onerror",e),m(),t.removeListener("error",y),0===o(t,"error")&&A(t,e)}function b(){t.removeListener("finish",g),m()}function g(){h("onfinish"),t.removeListener("close",b),m()}function m(){h("unpipe"),r.unpipe(t)}return r.on("data",d),P(t,"error",y),t.once("close",b),t.once("finish",g),t.emit("pipe",r),i.flowing||(h("pipe resume"),r.resume()),t},M.prototype.unpipe=function(t){var e=this._readableState,r={hasUnpiped:!1};if(0===e.pipesCount)return this;if(1===e.pipesCount)return t&&t!==e.pipes||(t||(t=e.pipes),e.pipes=null,e.pipesCount=0,e.flowing=!1,t&&t.emit("unpipe",this,r)),this;if(!t){var n=e.pipes,i=e.pipesCount;e.pipes=null,e.pipesCount=0,e.flowing=!1;for(var o=0;o<i;o++)n[o].emit("unpipe",this,{hasUnpiped:!1});return this}var s=J(e.pipes,t);return-1===s||(e.pipes.splice(s,1),e.pipesCount-=1,1===e.pipesCount&&(e.pipes=e.pipes[0]),t.emit("unpipe",this,r)),this},M.prototype.on=function(t,e){var r=s.prototype.on.call(this,t,e),i=this._readableState;return"data"===t?(i.readableListening=this.listenerCount("readable")>0,!1!==i.flowing&&this.resume()):"readable"===t&&(i.endEmitted||i.readableListening||(i.readableListening=i.needReadable=!0,i.flowing=!1,i.emittedReadable=!1,h("on readable",i.length,i.reading),i.length?L(this):i.reading||n.nextTick(K,this))),r},M.prototype.addListener=M.prototype.on,M.prototype.removeListener=function(t,e){var r=s.prototype.removeListener.call(this,t,e);return"readable"===t&&n.nextTick(W,this),r},M.prototype.removeAllListeners=function(t){var e=s.prototype.removeAllListeners.apply(this,arguments);return"readable"!==t&&void 0!==t||n.nextTick(W,this),e},M.prototype.resume=function(){var t=this._readableState;return t.flowing||(h("resume"),t.flowing=!t.readableListening,H(this,t)),t.paused=!1,this},M.prototype.pause=function(){return h("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(h("pause"),this._readableState.flowing=!1,this.emit("pause")),this._readableState.paused=!0,this},M.prototype.wrap=function(t){var e=this,r=this._readableState,n=!1;for(var i in t.on("end",(function(){if(h("wrapped end"),r.decoder&&!r.ended){var t=r.decoder.end();t&&t.length&&e.push(t)}e.push(null)})),t.on("data",(function(i){if(h("wrapped data"),r.decoder&&(i=r.decoder.write(i)),(!r.objectMode||null!==i&&void 0!==i)&&(r.objectMode||i&&i.length)){var o=e.push(i);o||(n=!0,t.pause())}})),t)void 0===this[i]&&"function"===typeof t[i]&&(this[i]=function(e){return function(){return t[e].apply(t,arguments)}}(i));for(var o=0;o<O.length;o++)t.on(O[o],this.emit.bind(this,O[o]));return this._read=function(e){h("wrapped _read",e),n&&(n=!1,t.resume())},this},"function"===typeof Symbol&&(M.prototype[Symbol.asyncIterator]=function(){return void 0===d&&(d=r("34e3")),d(this)}),Object.defineProperty(M.prototype,"readableHighWaterMark",{enumerable:!1,get:function(){return this._readableState.highWaterMark}}),Object.defineProperty(M.prototype,"readableBuffer",{enumerable:!1,get:function(){return this._readableState&&this._readableState.buffer}}),Object.defineProperty(M.prototype,"readableFlowing",{enumerable:!1,get:function(){return this._readableState.flowing},set:function(t){this._readableState&&(this._readableState.flowing=t)}}),M._fromList=Q,Object.defineProperty(M.prototype,"readableLength",{enumerable:!1,get:function(){return this._readableState.length}}),"function"===typeof Symbol&&(M.from=function(t,e){return void 0===y&&(y=r("2527")),y(M,t,e)})}).call(this,r("c8ba"),r("4362"))},"86c6":function(t,e,r){"use strict";var n=r("9bfc").codes.ERR_INVALID_OPT_VALUE;function i(t,e,r){return null!=t.highWaterMark?t.highWaterMark:e?t[r]:null}function o(t,e,r,o){var s=i(e,o,r);if(null!=s){if(!isFinite(s)||Math.floor(s)!==s||s<0){var a=o?r:"highWaterMark";throw new n(a,s)}return Math.floor(s)}return t.objectMode?16:16384}t.exports={getHighWaterMark:o}},"8c43":function(t,e,r){"use strict";t.exports=Number.isNaN||function(t){return t!==t}},"8ca0":function(t,e,r){"use strict";var n=r("8c43");t.exports=function(t){return n(t)||0===t?t:t<0?-1:1}},"926d":function(t,e,r){"use strict";t.exports=Function.prototype.call},9671:function(t,e,r){"use strict";var n=Function.prototype.call,i=Object.prototype.hasOwnProperty,o=r("0f7c");t.exports=o.call(n,i)},"9bfc":function(t,e,r){"use strict";function n(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var i={};function o(t,e,r){function o(t,r,n){return"string"===typeof e?e:e(t,r,n)}r||(r=Error);var s=function(t){function e(e,r,n){return t.call(this,o(e,r,n))||this}return n(e,t),e}(r);s.prototype.name=r.name,s.prototype.code=t,i[t]=s}function s(t,e){if(Array.isArray(t)){var r=t.length;return t=t.map((function(t){return String(t)})),r>2?"one of ".concat(e," ").concat(t.slice(0,r-1).join(", "),", or ")+t[r-1]:2===r?"one of ".concat(e," ").concat(t[0]," or ").concat(t[1]):"of ".concat(e," ").concat(t[0])}return"of ".concat(e," ").concat(String(t))}function a(t,e,r){return t.substr(!r||r<0?0:+r,e.length)===e}function c(t,e,r){return(void 0===r||r>t.length)&&(r=t.length),t.substring(r-e.length,r)===e}function u(t,e,r){return"number"!==typeof r&&(r=0),!(r+e.length>t.length)&&-1!==t.indexOf(e,r)}o("ERR_INVALID_OPT_VALUE",(function(t,e){return'The value "'+e+'" is invalid for option "'+t+'"'}),TypeError),o("ERR_INVALID_ARG_TYPE",(function(t,e,r){var n,i;if("string"===typeof e&&a(e,"not ")?(n="must not be",e=e.replace(/^not /,"")):n="must be",c(t," argument"))i="The ".concat(t," ").concat(n," ").concat(s(e,"type"));else{var o=u(t,".")?"property":"argument";i='The "'.concat(t,'" ').concat(o," ").concat(n," ").concat(s(e,"type"))}return i+=". Received type ".concat(typeof r),i}),TypeError),o("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),o("ERR_METHOD_NOT_IMPLEMENTED",(function(t){return"The "+t+" method is not implemented"})),o("ERR_STREAM_PREMATURE_CLOSE","Premature close"),o("ERR_STREAM_DESTROYED",(function(t){return"Cannot call "+t+" after a stream was destroyed"})),o("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),o("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),o("ERR_STREAM_WRITE_AFTER_END","write after end"),o("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),o("ERR_UNKNOWN_ENCODING",(function(t){return"Unknown encoding: "+t}),TypeError),o("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),t.exports.codes=i},"9d37":function(t,e,r){"use strict";t.exports=i;var n=r("fe34");function i(t){if(!(this instanceof i))return new i(t);n.call(this,t)}r("3fb5")(i,n),i.prototype._transform=function(t,e,r){r(null,t)}},"9ede":function(t,e,r){t.exports=r("faa1").EventEmitter},a284:function(t,e,r){"use strict";t.exports=Object},a29f:function(t,e,r){"use strict";var n=r("bbc7"),i=Object.prototype.hasOwnProperty,o=Array.isArray,s=function(){for(var t=[],e=0;e<256;++e)t.push("%"+((e<16?"0":"")+e.toString(16)).toUpperCase());return t}(),a=function(t){while(t.length>1){var e=t.pop(),r=e.obj[e.prop];if(o(r)){for(var n=[],i=0;i<r.length;++i)"undefined"!==typeof r[i]&&n.push(r[i]);e.obj[e.prop]=n}}},c=function(t,e){for(var r=e&&e.plainObjects?{__proto__:null}:{},n=0;n<t.length;++n)"undefined"!==typeof t[n]&&(r[n]=t[n]);return r},u=function t(e,r,n){if(!r)return e;if("object"!==typeof r&&"function"!==typeof r){if(o(e))e.push(r);else{if(!e||"object"!==typeof e)return[e,r];(n&&(n.plainObjects||n.allowPrototypes)||!i.call(Object.prototype,r))&&(e[r]=!0)}return e}if(!e||"object"!==typeof e)return[e].concat(r);var s=e;return o(e)&&!o(r)&&(s=c(e,n)),o(e)&&o(r)?(r.forEach((function(r,o){if(i.call(e,o)){var s=e[o];s&&"object"===typeof s&&r&&"object"===typeof r?e[o]=t(s,r,n):e.push(r)}else e[o]=r})),e):Object.keys(r).reduce((function(e,o){var s=r[o];return i.call(e,o)?e[o]=t(e[o],s,n):e[o]=s,e}),s)},l=function(t,e){return Object.keys(e).reduce((function(t,r){return t[r]=e[r],t}),t)},h=function(t,e,r){var n=t.replace(/\+/g," ");if("iso-8859-1"===r)return n.replace(/%[0-9a-f]{2}/gi,unescape);try{return decodeURIComponent(n)}catch(i){return n}},f=1024,p=function(t,e,r,i,o){if(0===t.length)return t;var a=t;if("symbol"===typeof t?a=Symbol.prototype.toString.call(t):"string"!==typeof t&&(a=String(t)),"iso-8859-1"===r)return escape(a).replace(/%u[0-9a-f]{4}/gi,(function(t){return"%26%23"+parseInt(t.slice(2),16)+"%3B"}));for(var c="",u=0;u<a.length;u+=f){for(var l=a.length>=f?a.slice(u,u+f):a,h=[],p=0;p<l.length;++p){var d=l.charCodeAt(p);45===d||46===d||95===d||126===d||d>=48&&d<=57||d>=65&&d<=90||d>=97&&d<=122||o===n.RFC1738&&(40===d||41===d)?h[h.length]=l.charAt(p):d<128?h[h.length]=s[d]:d<2048?h[h.length]=s[192|d>>6]+s[128|63&d]:d<55296||d>=57344?h[h.length]=s[224|d>>12]+s[128|d>>6&63]+s[128|63&d]:(p+=1,d=65536+((1023&d)<<10|1023&l.charCodeAt(p)),h[h.length]=s[240|d>>18]+s[128|d>>12&63]+s[128|d>>6&63]+s[128|63&d])}c+=h.join("")}return c},d=function(t){for(var e=[{obj:{o:t},prop:"o"}],r=[],n=0;n<e.length;++n)for(var i=e[n],o=i.obj[i.prop],s=Object.keys(o),c=0;c<s.length;++c){var u=s[c],l=o[u];"object"===typeof l&&null!==l&&-1===r.indexOf(l)&&(e.push({obj:o,prop:u}),r.push(l))}return a(e),t},y=function(t){return"[object RegExp]"===Object.prototype.toString.call(t)},b=function(t){return!(!t||"object"!==typeof t)&&!!(t.constructor&&t.constructor.isBuffer&&t.constructor.isBuffer(t))},g=function(t,e){return[].concat(t,e)},m=function(t,e){if(o(t)){for(var r=[],n=0;n<t.length;n+=1)r.push(e(t[n]));return r}return e(t)};t.exports={arrayToObject:c,assign:l,combine:g,compact:d,decode:h,encode:p,isBuffer:b,isRegExp:y,maybeMap:m,merge:u}},a43f:function(t,e,r){"use strict";const n=r("63f0"),i=r("6155").NumberAllocator;function o(t){if(!(this instanceof o))return new o(t);t>0&&(this.aliasToTopic=new n({max:t}),this.topicToAlias={},this.numberAllocator=new i(1,t),this.max=t,this.length=0)}o.prototype.put=function(t,e){if(0===e||e>this.max)return!1;const r=this.aliasToTopic.get(e);return r&&delete this.topicToAlias[r],this.aliasToTopic.set(e,t),this.topicToAlias[t]=e,this.numberAllocator.use(e),this.length=this.aliasToTopic.length,!0},o.prototype.getTopicByAlias=function(t){return this.aliasToTopic.get(t)},o.prototype.getAliasByTopic=function(t){const e=this.topicToAlias[t];return"undefined"!==typeof e&&this.aliasToTopic.get(e),e},o.prototype.clear=function(){this.aliasToTopic.reset(),this.topicToAlias={},this.numberAllocator.clear(),this.length=0},o.prototype.getLruAlias=function(){const t=this.numberAllocator.firstVacant();return t||this.aliasToTopic.keys()[this.aliasToTopic.length-1]},t.exports=o},a493:function(t,e,r){"use strict";(function(e){var n=Object.keys||function(t){var e=[];for(var r in t)e.push(r);return e};t.exports=u;var i=r("0e8b"),o=r("f6ba");r("3fb5")(u,i);for(var s=n(o.prototype),a=0;a<s.length;a++){var c=s[a];u.prototype[c]||(u.prototype[c]=o.prototype[c])}function u(t){if(!(this instanceof u))return new u(t);i.call(this,t),o.call(this,t),this.allowHalfOpen=!0,t&&(!1===t.readable&&(this.readable=!1),!1===t.writable&&(this.writable=!1),!1===t.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",l)))}function l(){this._writableState.ended||e.nextTick(h,this)}function h(t){t.end()}Object.defineProperty(u.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(u.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(u.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(u.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}})}).call(this,r("4362"))},a50f:function(t,e){t.exports=function(){throw new Error("Readable.from is not available in the browser")}},a645:function(t,e,r){"use strict";t.exports=Error},a7c9:function(t,e){class r{constructor(){this.cmd=null,this.retain=!1,this.qos=0,this.dup=!1,this.length=-1,this.topic=null,this.payload=null}}t.exports=r},ab2b:function(t,e,r){"use strict";t.exports=Math.min},ab52:function(t,e,r){(function(e){var n=r("566b"),i=function(){},o=function(t){return t.setHeader&&"function"===typeof t.abort},s=function(t){return t.stdio&&Array.isArray(t.stdio)&&3===t.stdio.length},a=function(t,r,c){if("function"===typeof r)return a(t,null,r);r||(r={}),c=n(c||i);var u=t._writableState,l=t._readableState,h=r.readable||!1!==r.readable&&t.readable,f=r.writable||!1!==r.writable&&t.writable,p=!1,d=function(){t.writable||y()},y=function(){f=!1,h||c.call(t)},b=function(){h=!1,f||c.call(t)},g=function(e){c.call(t,e?new Error("exited with error code: "+e):null)},m=function(e){c.call(t,e)},w=function(){e.nextTick(v)},v=function(){if(!p)return(!h||l&&l.ended&&!l.destroyed)&&(!f||u&&u.ended&&!u.destroyed)?void 0:c.call(t,new Error("premature close"))},_=function(){t.req.on("finish",y)};return o(t)?(t.on("complete",y),t.on("abort",w),t.req?_():t.on("request",_)):f&&!u&&(t.on("end",d),t.on("close",d)),s(t)&&t.on("exit",g),t.on("end",b),t.on("finish",y),!1!==r.error&&t.on("error",m),t.on("close",w),function(){p=!0,t.removeListener("complete",y),t.removeListener("abort",w),t.removeListener("request",_),t.req&&t.req.removeListener("finish",y),t.removeListener("end",d),t.removeListener("close",d),t.removeListener("finish",y),t.removeListener("exit",g),t.removeListener("end",b),t.removeListener("error",m),t.removeListener("close",w)}};t.exports=a}).call(this,r("4362"))},ae84c:function(t,e,r){"use strict";function n(t){const e=t.split("/");for(let r=0;r<e.length;r++)if("+"!==e[r]){if("#"===e[r])return r===e.length-1;if(-1!==e[r].indexOf("+")||-1!==e[r].indexOf("#"))return!1}return!0}function i(t){if(0===t.length)return"empty_topic_list";for(let e=0;e<t.length;e++)if(!n(t[e]))return t[e];return null}t.exports={validateTopics:i}},aff9:function(t,e,r){"use strict";t.exports=function(t){t.prototype[Symbol.iterator]=function*(){for(let t=this.head;t;t=t.next)yield t.value}}},b289:function(t,e,r){(function(e){const r=t.exports;r.types={0:"reserved",1:"connect",2:"connack",3:"publish",4:"puback",5:"pubrec",6:"pubrel",7:"pubcomp",8:"subscribe",9:"suback",10:"unsubscribe",11:"unsuback",12:"pingreq",13:"pingresp",14:"disconnect",15:"auth"},r.codes={};for(const t in r.types){const e=r.types[t];r.codes[e]=t}r.CMD_SHIFT=4,r.CMD_MASK=240,r.DUP_MASK=8,r.QOS_MASK=3,r.QOS_SHIFT=1,r.RETAIN_MASK=1,r.VARBYTEINT_MASK=127,r.VARBYTEINT_FIN_MASK=128,r.VARBYTEINT_MAX=268435455,r.SESSIONPRESENT_MASK=1,r.SESSIONPRESENT_HEADER=e.from([r.SESSIONPRESENT_MASK]),r.CONNACK_HEADER=e.from([r.codes.connack<<r.CMD_SHIFT]),r.USERNAME_MASK=128,r.PASSWORD_MASK=64,r.WILL_RETAIN_MASK=32,r.WILL_QOS_MASK=24,r.WILL_QOS_SHIFT=3,r.WILL_FLAG_MASK=4,r.CLEAN_SESSION_MASK=2,r.CONNECT_HEADER=e.from([r.codes.connect<<r.CMD_SHIFT]),r.properties={sessionExpiryInterval:17,willDelayInterval:24,receiveMaximum:33,maximumPacketSize:39,topicAliasMaximum:34,requestResponseInformation:25,requestProblemInformation:23,userProperties:38,authenticationMethod:21,authenticationData:22,payloadFormatIndicator:1,messageExpiryInterval:2,contentType:3,responseTopic:8,correlationData:9,maximumQoS:36,retainAvailable:37,assignedClientIdentifier:18,reasonString:31,wildcardSubscriptionAvailable:40,subscriptionIdentifiersAvailable:41,sharedSubscriptionAvailable:42,serverKeepAlive:19,responseInformation:26,serverReference:28,topicAlias:35,subscriptionIdentifier:11},r.propertiesCodes={};for(const t in r.properties){const e=r.properties[t];r.propertiesCodes[e]=t}function n(t){return[0,1,2].map(n=>[0,1].map(i=>[0,1].map(o=>{const s=e.alloc(1);return s.writeUInt8(r.codes[t]<<r.CMD_SHIFT|(i?r.DUP_MASK:0)|n<<r.QOS_SHIFT|o,0,!0),s})))}r.propertiesTypes={sessionExpiryInterval:"int32",willDelayInterval:"int32",receiveMaximum:"int16",maximumPacketSize:"int32",topicAliasMaximum:"int16",requestResponseInformation:"byte",requestProblemInformation:"byte",userProperties:"pair",authenticationMethod:"string",authenticationData:"binary",payloadFormatIndicator:"byte",messageExpiryInterval:"int32",contentType:"string",responseTopic:"string",correlationData:"binary",maximumQoS:"int8",retainAvailable:"byte",assignedClientIdentifier:"string",reasonString:"string",wildcardSubscriptionAvailable:"byte",subscriptionIdentifiersAvailable:"byte",sharedSubscriptionAvailable:"byte",serverKeepAlive:"int16",responseInformation:"string",serverReference:"string",topicAlias:"int16",subscriptionIdentifier:"var"},r.PUBLISH_HEADER=n("publish"),r.SUBSCRIBE_HEADER=n("subscribe"),r.SUBSCRIBE_OPTIONS_QOS_MASK=3,r.SUBSCRIBE_OPTIONS_NL_MASK=1,r.SUBSCRIBE_OPTIONS_NL_SHIFT=2,r.SUBSCRIBE_OPTIONS_RAP_MASK=1,r.SUBSCRIBE_OPTIONS_RAP_SHIFT=3,r.SUBSCRIBE_OPTIONS_RH_MASK=3,r.SUBSCRIBE_OPTIONS_RH_SHIFT=4,r.SUBSCRIBE_OPTIONS_RH=[0,16,32],r.SUBSCRIBE_OPTIONS_NL=4,r.SUBSCRIBE_OPTIONS_RAP=8,r.SUBSCRIBE_OPTIONS_QOS=[0,1,2],r.UNSUBSCRIBE_HEADER=n("unsubscribe"),r.ACKS={unsuback:n("unsuback"),puback:n("puback"),pubcomp:n("pubcomp"),pubrel:n("pubrel"),pubrec:n("pubrec")},r.SUBACK_HEADER=e.from([r.codes.suback<<r.CMD_SHIFT]),r.VERSION3=e.from([3]),r.VERSION4=e.from([4]),r.VERSION5=e.from([5]),r.VERSION131=e.from([131]),r.VERSION132=e.from([132]),r.QOS=[0,1,2].map(t=>e.from([t])),r.EMPTY={pingreq:e.from([r.codes.pingreq<<4,0]),pingresp:e.from([r.codes.pingresp<<4,0]),disconnect:e.from([r.codes.disconnect<<4,0])}}).call(this,r("1c35").Buffer)},b98b:function(t,e,r){t.exports=r("faa1").EventEmitter},bbc7:function(t,e,r){"use strict";var n=String.prototype.replace,i=/%20/g,o={RFC1738:"RFC1738",RFC3986:"RFC3986"};t.exports={default:o.RFC3986,formatters:{RFC1738:function(t){return n.call(t,i,"+")},RFC3986:function(t){return String(t)}},RFC1738:o.RFC1738,RFC3986:o.RFC3986}},be3f:function(t,e,r){"use strict";(function(e){var n=Object.keys||function(t){var e=[];for(var r in t)e.push(r);return e};t.exports=u;var i=r("85f8"),o=r("13a8");r("3fb5")(u,i);for(var s=n(o.prototype),a=0;a<s.length;a++){var c=s[a];u.prototype[c]||(u.prototype[c]=o.prototype[c])}function u(t){if(!(this instanceof u))return new u(t);i.call(this,t),o.call(this,t),this.allowHalfOpen=!0,t&&(!1===t.readable&&(this.readable=!1),!1===t.writable&&(this.writable=!1),!1===t.allowHalfOpen&&(this.allowHalfOpen=!1,this.once("end",l)))}function l(){this._writableState.ended||e.nextTick(h,this)}function h(t){t.end()}Object.defineProperty(u.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),Object.defineProperty(u.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(u.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(u.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed&&this._writableState.destroyed)},set:function(t){void 0!==this._readableState&&void 0!==this._writableState&&(this._readableState.destroyed=t,this._writableState.destroyed=t)}})}).call(this,r("4362"))},bf09:function(t,e,r){"use strict";var n=r("9bfc").codes.ERR_STREAM_PREMATURE_CLOSE;function i(t){var e=!1;return function(){if(!e){e=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];t.apply(this,n)}}}function o(){}function s(t){return t.setHeader&&"function"===typeof t.abort}function a(t,e,r){if("function"===typeof e)return a(t,null,e);e||(e={}),r=i(r||o);var c=e.readable||!1!==e.readable&&t.readable,u=e.writable||!1!==e.writable&&t.writable,l=function(){t.writable||f()},h=t._writableState&&t._writableState.finished,f=function(){u=!1,h=!0,c||r.call(t)},p=t._readableState&&t._readableState.endEmitted,d=function(){c=!1,p=!0,u||r.call(t)},y=function(e){r.call(t,e)},b=function(){var e;return c&&!p?(t._readableState&&t._readableState.ended||(e=new n),r.call(t,e)):u&&!h?(t._writableState&&t._writableState.ended||(e=new n),r.call(t,e)):void 0},g=function(){t.req.on("finish",f)};return s(t)?(t.on("complete",f),t.on("abort",b),t.req?g():t.on("request",g)):u&&!t._writableState&&(t.on("end",l),t.on("close",l)),t.on("end",d),t.on("finish",f),!1!==e.error&&t.on("error",y),t.on("close",b),function(){t.removeListener("complete",f),t.removeListener("abort",b),t.removeListener("request",g),t.req&&t.req.removeListener("finish",f),t.removeListener("end",l),t.removeListener("close",l),t.removeListener("finish",f),t.removeListener("end",d),t.removeListener("error",y),t.removeListener("close",b)}}t.exports=a},c3ae5:function(t,e,r){"use strict";t.exports=Math.round},c3e0:function(t,e,r){"use strict";var n,i=r("f9ae"),o=r("2aa9");try{n=[].__proto__===Array.prototype}catch(u){if(!u||"object"!==typeof u||!("code"in u)||"ERR_PROTO_ACCESS"!==u.code)throw u}var s=!!n&&o&&o(Object.prototype,"__proto__"),a=Object,c=a.getPrototypeOf;t.exports=s&&"function"===typeof s.get?i([s.get]):"function"===typeof c&&function(t){return c(null==t?t:a(t))}},c4c7:function(t,e,r){"use strict";const{Buffer:n}=r("1c35"),i=r("035d").Transform,o=r("56ac");let s,a,c,u=!1;function l(){const t=new i;return t._write=function(t,e,r){s.sendSocketMessage({data:t.buffer,success:function(){r()},fail:function(){r(new Error)}})},t._flush=function(t){s.closeSocket({success:function(){t()}})},t}function h(t){t.hostname||(t.hostname="localhost"),t.path||(t.path="/"),t.wsOptions||(t.wsOptions={})}function f(t,e){const r="alis"===t.protocol?"wss":"ws";let n=r+"://"+t.hostname+t.path;return t.port&&80!==t.port&&443!==t.port&&(n=r+"://"+t.hostname+":"+t.port+t.path),"function"===typeof t.transformWsUrl&&(n=t.transformWsUrl(n,t,e)),n}function p(){u||(u=!0,s.onSocketOpen((function(){c.setReadable(a),c.setWritable(a),c.emit("connect")})),s.onSocketMessage((function(t){if("string"===typeof t.data){const e=n.from(t.data,"base64");a.push(e)}else{const e=new FileReader;e.addEventListener("load",(function(){let t=e.result;t=t instanceof ArrayBuffer?n.from(t):n.from(t,"utf8"),a.push(t)})),e.readAsArrayBuffer(t.data)}})),s.onSocketClose((function(){c.end(),c.destroy()})),s.onSocketError((function(t){c.destroy(t)})))}function d(t,e){if(e.hostname=e.hostname||e.host,!e.hostname)throw new Error("Could not determine host. Specify host manually.");const r="MQIsdp"===e.protocolId&&3===e.protocolVersion?"mqttv3.1":"mqtt";h(e);const n=f(e,t);return s=e.my,s.connectSocket({url:n,protocols:r}),a=l(),c=o.obj(),p(),c}t.exports=d},ccf1:function(t,e,r){"use strict";var n=r("2714"),i=r("0d25"),o=function(t,e,r){for(var n,i=t;null!=(n=i.next);i=n)if(n.key===e)return i.next=n.next,r||(n.next=t.next,t.next=n),n},s=function(t,e){if(t){var r=o(t,e);return r&&r.value}},a=function(t,e,r){var n=o(t,e);n?n.value=r:t.next={key:e,next:t.next,value:r}},c=function(t,e){return!!t&&!!o(t,e)},u=function(t,e){if(t)return o(t,e,!0)};t.exports=function(){var t,e={assert:function(t){if(!e.has(t))throw new i("Side channel does not contain "+n(t))},delete:function(e){var r=t&&t.next,n=u(t,e);return n&&r&&r===n&&(t=void 0),!!n},get:function(e){return s(t,e)},has:function(e){return c(t,e)},set:function(e,r){t||(t={next:void 0}),a(t,e,r)}};return e}},d633:function(t,e){function r(t,e){if(t&&e)return r(t)(e);if("function"!==typeof t)throw new TypeError("need wrapper function");return Object.keys(t).forEach((function(e){n[e]=t[e]})),n;function n(){for(var e=new Array(arguments.length),r=0;r<e.length;r++)e[r]=arguments[r];var n=t.apply(this,e),i=e[e.length-1];return"function"===typeof n&&n!==i&&Object.keys(i).forEach((function(t){n[t]=i[t]})),n}}t.exports=r},d9e1:function(t,e,r){"use strict";var n=r("fbd7").codes.ERR_STREAM_PREMATURE_CLOSE;function i(t){var e=!1;return function(){if(!e){e=!0;for(var r=arguments.length,n=new Array(r),i=0;i<r;i++)n[i]=arguments[i];t.apply(this,n)}}}function o(){}function s(t){return t.setHeader&&"function"===typeof t.abort}function a(t,e,r){if("function"===typeof e)return a(t,null,e);e||(e={}),r=i(r||o);var c=e.readable||!1!==e.readable&&t.readable,u=e.writable||!1!==e.writable&&t.writable,l=function(){t.writable||f()},h=t._writableState&&t._writableState.finished,f=function(){u=!1,h=!0,c||r.call(t)},p=t._readableState&&t._readableState.endEmitted,d=function(){c=!1,p=!0,u||r.call(t)},y=function(e){r.call(t,e)},b=function(){var e;return c&&!p?(t._readableState&&t._readableState.ended||(e=new n),r.call(t,e)):u&&!h?(t._writableState&&t._writableState.ended||(e=new n),r.call(t,e)):void 0},g=function(){t.req.on("finish",f)};return s(t)?(t.on("complete",f),t.on("abort",b),t.req?g():t.on("request",g)):u&&!t._writableState&&(t.on("end",l),t.on("close",l)),t.on("end",d),t.on("finish",f),!1!==e.error&&t.on("error",y),t.on("close",b),function(){t.removeListener("complete",f),t.removeListener("abort",b),t.removeListener("request",g),t.req&&t.req.removeListener("finish",f),t.removeListener("end",l),t.removeListener("close",l),t.removeListener("finish",f),t.removeListener("end",d),t.removeListener("error",y),t.removeListener("close",b)}}t.exports=a},dc90:function(t,e,r){function n(t){function e(t){let e=0;for(let r=0;r<t.length;r++)e=(e<<5)-e+t.charCodeAt(r),e|=0;return n.colors[Math.abs(e)%n.colors.length]}function n(t){let e,r,o,s=null;function a(...t){if(!a.enabled)return;const r=a,i=Number(new Date),o=i-(e||i);r.diff=o,r.prev=e,r.curr=i,e=i,t[0]=n.coerce(t[0]),"string"!==typeof t[0]&&t.unshift("%O");let s=0;t[0]=t[0].replace(/%([a-zA-Z%])/g,(e,i)=>{if("%%"===e)return"%";s++;const o=n.formatters[i];if("function"===typeof o){const n=t[s];e=o.call(r,n),t.splice(s,1),s--}return e}),n.formatArgs.call(r,t);const c=r.log||n.log;c.apply(r,t)}return a.namespace=t,a.useColors=n.useColors(),a.color=n.selectColor(t),a.extend=i,a.destroy=n.destroy,Object.defineProperty(a,"enabled",{enumerable:!0,configurable:!1,get:()=>null!==s?s:(r!==n.namespaces&&(r=n.namespaces,o=n.enabled(t)),o),set:t=>{s=t}}),"function"===typeof n.init&&n.init(a),a}function i(t,e){const r=n(this.namespace+("undefined"===typeof e?":":e)+t);return r.log=this.log,r}function o(t){n.save(t),n.namespaces=t,n.names=[],n.skips=[];const e=("string"===typeof t?t:"").trim().replace(" ",",").split(",").filter(Boolean);for(const r of e)"-"===r[0]?n.skips.push(r.slice(1)):n.names.push(r)}function s(t,e){let r=0,n=0,i=-1,o=0;while(r<t.length)if(n<e.length&&(e[n]===t[r]||"*"===e[n]))"*"===e[n]?(i=n,o=r,n++):(r++,n++);else{if(-1===i)return!1;n=i+1,o++,r=o}while(n<e.length&&"*"===e[n])n++;return n===e.length}function a(){const t=[...n.names,...n.skips.map(t=>"-"+t)].join(",");return n.enable(""),t}function c(t){for(const e of n.skips)if(s(t,e))return!1;for(const e of n.names)if(s(t,e))return!0;return!1}function u(t){return t instanceof Error?t.stack||t.message:t}function l(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return n.debug=n,n.default=n,n.coerce=u,n.disable=a,n.enable=o,n.enabled=c,n.humanize=r("1468"),n.destroy=l,Object.keys(t).forEach(e=>{n[e]=t[e]}),n.names=[],n.skips=[],n.formatters={},n.selectColor=e,n.enable(n.load()),n}t.exports=n},dc99:function(t,e,r){"use strict";t.exports=RangeError},df86:function(t,e,r){"use strict";(function(e,n){const i=r("faa1").EventEmitter,o=r("ea08"),s=r("e7d0"),a=r("a43f"),c=r("3409"),u=r("1e4d"),l=r("035d").Writable,h=r("3fb5"),f=r("29a2"),p=r("2a28"),d=r("ae84c"),y=r("53a8"),b=r("34eb")("mqttjs:client"),g=e?e.nextTick:function(t){setTimeout(t,0)},m=n.setImmediate||function(t){g(t)},w={keepalive:60,reschedulePings:!0,protocolId:"MQTT",protocolVersion:4,reconnectPeriod:1e3,connectTimeout:3e4,clean:!0,resubscribe:!0},v=["ECONNREFUSED","EADDRINUSE","ECONNRESET","ENOTFOUND"],_={0:"",1:"Unacceptable protocol version",2:"Identifier rejected",3:"Server unavailable",4:"Bad username or password",5:"Not authorized",16:"No matching subscribers",17:"No subscription existed",128:"Unspecified error",129:"Malformed Packet",130:"Protocol Error",131:"Implementation specific error",132:"Unsupported Protocol Version",133:"Client Identifier not valid",134:"Bad User Name or Password",135:"Not authorized",136:"Server unavailable",137:"Server busy",138:"Banned",139:"Server shutting down",140:"Bad authentication method",141:"Keep Alive timeout",142:"Session taken over",143:"Topic Filter invalid",144:"Topic Name invalid",145:"Packet identifier in use",146:"Packet Identifier not found",147:"Receive Maximum exceeded",148:"Topic Alias invalid",149:"Packet too large",150:"Message rate too high",151:"Quota exceeded",152:"Administrative action",153:"Payload format invalid",154:"Retain not supported",155:"QoS not supported",156:"Use another server",157:"Server moved",158:"Shared Subscriptions not supported",159:"Connection rate exceeded",160:"Maximum connect time",161:"Subscription Identifiers not supported",162:"Wildcard Subscriptions not supported"};function S(){return"mqttjs_"+Math.random().toString(16).substr(2,8)}function E(t,e){if(5===t.options.protocolVersion&&"publish"===e.cmd){let r;e.properties&&(r=e.properties.topicAlias);const n=e.topic.toString();if(t.topicAliasSend)if(r){if(0!==n.length&&(b("applyTopicAlias :: register topic: %s - alias: %d",n,r),!t.topicAliasSend.put(n,r)))return b("applyTopicAlias :: error out of range. topic: %s - alias: %d",n,r),new Error("Sending Topic Alias out of range")}else 0!==n.length&&(t.options.autoAssignTopicAlias?(r=t.topicAliasSend.getAliasByTopic(n),r?(e.topic="",e.properties={...e.properties,topicAlias:r},b("applyTopicAlias :: auto assign(use) topic: %s - alias: %d",n,r)):(r=t.topicAliasSend.getLruAlias(),t.topicAliasSend.put(n,r),e.properties={...e.properties,topicAlias:r},b("applyTopicAlias :: auto assign topic: %s - alias: %d",n,r))):t.options.autoUseTopicAlias&&(r=t.topicAliasSend.getAliasByTopic(n),r&&(e.topic="",e.properties={...e.properties,topicAlias:r},b("applyTopicAlias :: auto use topic: %s - alias: %d",n,r))));else if(r)return b("applyTopicAlias :: error out of range. topic: %s - alias: %d",n,r),new Error("Sending Topic Alias out of range")}}function k(t,e){let r;e.properties&&(r=e.properties.topicAlias);let n=e.topic.toString();if(0===n.length){if("undefined"===typeof r)return new Error("Unregistered Topic Alias");if(n=t.topicAliasSend.getTopicByAlias(r),"undefined"===typeof n)return new Error("Unregistered Topic Alias");e.topic=n}r&&delete e.properties.topicAlias}function A(t,e,r){b("sendPacket :: packet: %O",e),b("sendPacket :: emitting `packetsend`"),t.emit("packetsend",e),b("sendPacket :: writing to stream");const n=c.writeToStream(e,t.stream,t.options);b("sendPacket :: writeToStream result %s",n),!n&&r&&r!==M?(b("sendPacket :: handle events on `drain` once through callback."),t.stream.once("drain",r)):r&&(b("sendPacket :: invoking cb"),r())}function O(t){t&&(b("flush: queue exists? %b",!!t),Object.keys(t).forEach((function(e){"function"===typeof t[e].cb&&(t[e].cb(new Error("Connection closed")),delete t[e])})))}function P(t){t&&(b("flushVolatile :: deleting volatile messages from the queue and setting their callbacks as error function"),Object.keys(t).forEach((function(e){t[e].volatile&&"function"===typeof t[e].cb&&(t[e].cb(new Error("Connection closed")),delete t[e])})))}function R(t,e,r,n){b("storeAndSend :: store packet with cmd %s to outgoingStore",e.cmd);let i,o=e;if("publish"===o.cmd&&(o=p(e),i=k(t,o),i))return r&&r(i);t.outgoingStore.put(o,(function(i){if(i)return r&&r(i);n(),A(t,e,r)}))}function M(t){b("nop ::",t)}function x(t,e){let r;const n=this;if(!(this instanceof x))return new x(t,e);for(r in this.options=e||{},w)"undefined"===typeof this.options[r]?this.options[r]=w[r]:this.options[r]=e[r];b("MqttClient :: options.protocol",e.protocol),b("MqttClient :: options.protocolVersion",e.protocolVersion),b("MqttClient :: options.username",e.username),b("MqttClient :: options.keepalive",e.keepalive),b("MqttClient :: options.reconnectPeriod",e.reconnectPeriod),b("MqttClient :: options.rejectUnauthorized",e.rejectUnauthorized),b("MqttClient :: options.topicAliasMaximum",e.topicAliasMaximum),this.options.clientId="string"===typeof e.clientId?e.clientId:S(),b("MqttClient :: clientId",this.options.clientId),this.options.customHandleAcks=5===e.protocolVersion&&e.customHandleAcks?e.customHandleAcks:function(){arguments[3](0)},this.streamBuilder=t,this.messageIdProvider="undefined"===typeof this.options.messageIdProvider?new u:this.options.messageIdProvider,this.outgoingStore=e.outgoingStore||new o,this.incomingStore=e.incomingStore||new o,this.queueQoSZero=void 0===e.queueQoSZero||e.queueQoSZero,this._resubscribeTopics={},this.messageIdToTopic={},this.pingTimer=null,this.connected=!1,this.disconnecting=!1,this.queue=[],this.connackTimer=null,this.reconnectTimer=null,this._storeProcessing=!1,this._packetIdsDuringStoreProcessing={},this._storeProcessingQueue=[],this.outgoing={},this._firstConnection=!0,e.topicAliasMaximum>0&&(e.topicAliasMaximum>65535?b("MqttClient :: options.topicAliasMaximum is out of range"):this.topicAliasRecv=new s(e.topicAliasMaximum)),this.on("connect",(function(){const t=this.queue;function e(){const r=t.shift();b("deliver :: entry %o",r);let i=null;if(!r)return void n._resubscribe();i=r.packet,b("deliver :: call _sendPacket for %o",i);let o=!0;i.messageId&&0!==i.messageId&&(n.messageIdProvider.register(i.messageId)||(o=!1)),o?n._sendPacket(i,(function(t){r.cb&&r.cb(t),e()})):(b("messageId: %d has already used. The message is skipped and removed.",i.messageId),e())}b("connect :: sending queued packets"),e()})),this.on("close",(function(){b("close :: connected set to `false`"),this.connected=!1,b("close :: clearing connackTimer"),clearTimeout(this.connackTimer),b("close :: clearing ping timer"),null!==n.pingTimer&&(n.pingTimer.clear(),n.pingTimer=null),this.topicAliasRecv&&this.topicAliasRecv.clear(),b("close :: calling _setupReconnect"),this._setupReconnect()})),i.call(this),b("MqttClient :: setting up stream"),this._setupStream()}h(x,i),x.prototype._setupStream=function(){const t=this,e=new l,r=c.parser(this.options);let n=null;const i=[];function o(){if(i.length)g(s);else{const t=n;n=null,t()}}function s(){b("work :: getting next packet in queue");const e=i.shift();if(e)b("work :: packet pulled from queue"),t._handlePacket(e,o);else{b("work :: no packets in queue");const t=n;n=null,b("work :: done flag is %s",!!t),t&&t()}}function a(e){b("streamErrorHandler :: error",e.message),v.includes(e.code)?(b("streamErrorHandler :: emitting error"),t.emit("error",e)):M(e)}b("_setupStream :: calling method to clear reconnect"),this._clearReconnect(),b("_setupStream :: using streamBuilder provided to client to create stream"),this.stream=this.streamBuilder(this),r.on("packet",(function(t){b("parser :: on packet push to packets array."),i.push(t)})),e._write=function(t,e,i){n=i,b("writable stream :: parsing buffer"),r.parse(t),s()},b("_setupStream :: pipe stream to writable stream"),this.stream.pipe(e),this.stream.on("error",a),this.stream.on("close",(function(){b("(%s)stream :: on close",t.options.clientId),P(t.outgoing),b("stream: emit close to MqttClient"),t.emit("close")})),b("_setupStream: sending packet `connect`");const u=Object.create(this.options);if(u.cmd="connect",this.topicAliasRecv&&(u.properties||(u.properties={}),this.topicAliasRecv&&(u.properties.topicAliasMaximum=this.topicAliasRecv.max)),A(this,u),r.on("error",this.emit.bind(this,"error")),this.options.properties){if(!this.options.properties.authenticationMethod&&this.options.properties.authenticationData)return t.end(()=>this.emit("error",new Error("Packet has no Authentication Method"))),this;if(this.options.properties.authenticationMethod&&this.options.authPacket&&"object"===typeof this.options.authPacket){const t=y({cmd:"auth",reasonCode:0},this.options.authPacket);A(this,t)}}this.stream.setMaxListeners(1e3),clearTimeout(this.connackTimer),this.connackTimer=setTimeout((function(){b("!!connectTimeout hit!! Calling _cleanUp with force `true`"),t._cleanUp(!0)}),this.options.connectTimeout)},x.prototype._handlePacket=function(t,e){const r=this.options;if(5===r.protocolVersion&&r.properties&&r.properties.maximumPacketSize&&r.properties.maximumPacketSize<t.length)return this.emit("error",new Error("exceeding packets size "+t.cmd)),this.end({reasonCode:149,properties:{reasonString:"Maximum packet size was exceeded"}}),this;switch(b("_handlePacket :: emitting packetreceive"),this.emit("packetreceive",t),t.cmd){case"publish":this._handlePublish(t,e);break;case"puback":case"pubrec":case"pubcomp":case"suback":case"unsuback":this._handleAck(t),e();break;case"pubrel":this._handlePubrel(t,e);break;case"connack":this._handleConnack(t),e();break;case"auth":this._handleAuth(t),e();break;case"pingresp":this._handlePingresp(t),e();break;case"disconnect":this._handleDisconnect(t),e();break;default:break}},x.prototype._checkDisconnecting=function(t){return this.disconnecting&&(t&&t!==M?t(new Error("client disconnecting")):this.emit("error",new Error("client disconnecting"))),this.disconnecting},x.prototype.publish=function(t,e,r,n){b("publish :: message `%s` to topic `%s`",e,t);const i=this.options;"function"===typeof r&&(n=r,r=null);const o={qos:0,retain:!1,dup:!1};if(r=y(o,r),this._checkDisconnecting(n))return this;const s=this,a=function(){let o=0;if((1===r.qos||2===r.qos)&&(o=s._nextId(),null===o))return b("No messageId left"),!1;const a={cmd:"publish",topic:t,payload:e,qos:r.qos,retain:r.retain,messageId:o,dup:r.dup};switch(5===i.protocolVersion&&(a.properties=r.properties),b("publish :: qos",r.qos),r.qos){case 1:case 2:s.outgoing[a.messageId]={volatile:!1,cb:n||M},b("MqttClient:publish: packet cmd: %s",a.cmd),s._sendPacket(a,void 0,r.cbStorePut);break;default:b("MqttClient:publish: packet cmd: %s",a.cmd),s._sendPacket(a,n,r.cbStorePut);break}return!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!a())&&this._storeProcessingQueue.push({invoke:a,cbStorePut:r.cbStorePut,callback:n}),this},x.prototype.subscribe=function(){const t=this,e=new Array(arguments.length);for(let h=0;h<arguments.length;h++)e[h]=arguments[h];const r=[];let n=e.shift();const i=n.resubscribe;let o=e.pop()||M,s=e.pop();const a=this.options.protocolVersion;delete n.resubscribe,"string"===typeof n&&(n=[n]),"function"!==typeof o&&(s=o,o=M);const c=d.validateTopics(n);if(null!==c)return m(o,new Error("Invalid topic "+c)),this;if(this._checkDisconnecting(o))return b("subscribe: discconecting true"),this;const u={qos:0};if(5===a&&(u.nl=!1,u.rap=!1,u.rh=0),s=y(u,s),Array.isArray(n)?n.forEach((function(e){if(b("subscribe: array topic %s",e),!Object.prototype.hasOwnProperty.call(t._resubscribeTopics,e)||t._resubscribeTopics[e].qos<s.qos||i){const t={topic:e,qos:s.qos};5===a&&(t.nl=s.nl,t.rap=s.rap,t.rh=s.rh,t.properties=s.properties),b("subscribe: pushing topic `%s` and qos `%s` to subs list",t.topic,t.qos),r.push(t)}})):Object.keys(n).forEach((function(e){if(b("subscribe: object topic %s",e),!Object.prototype.hasOwnProperty.call(t._resubscribeTopics,e)||t._resubscribeTopics[e].qos<n[e].qos||i){const t={topic:e,qos:n[e].qos};5===a&&(t.nl=n[e].nl,t.rap=n[e].rap,t.rh=n[e].rh,t.properties=s.properties),b("subscribe: pushing `%s` to subs list",t),r.push(t)}})),!r.length)return o(null,[]),this;const l=function(){const e=t._nextId();if(null===e)return b("No messageId left"),!1;const n={cmd:"subscribe",subscriptions:r,qos:1,retain:!1,dup:!1,messageId:e};if(s.properties&&(n.properties=s.properties),t.options.resubscribe){b("subscribe :: resubscribe true");const e=[];r.forEach((function(r){if(t.options.reconnectPeriod>0){const n={qos:r.qos};5===a&&(n.nl=r.nl||!1,n.rap=r.rap||!1,n.rh=r.rh||0,n.properties=r.properties),t._resubscribeTopics[r.topic]=n,e.push(r.topic)}})),t.messageIdToTopic[n.messageId]=e}return t.outgoing[n.messageId]={volatile:!0,cb:function(t,e){if(!t){const t=e.granted;for(let e=0;e<t.length;e+=1)r[e].qos=t[e]}o(t,r)}},b("subscribe :: call _sendPacket"),t._sendPacket(n),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!l())&&this._storeProcessingQueue.push({invoke:l,callback:o}),this},x.prototype.unsubscribe=function(){const t=this,e=new Array(arguments.length);for(let a=0;a<arguments.length;a++)e[a]=arguments[a];let r=e.shift(),n=e.pop()||M,i=e.pop();"string"===typeof r&&(r=[r]),"function"!==typeof n&&(i=n,n=M);const o=d.validateTopics(r);if(null!==o)return m(n,new Error("Invalid topic "+o)),this;if(t._checkDisconnecting(n))return this;const s=function(){const e=t._nextId();if(null===e)return b("No messageId left"),!1;const o={cmd:"unsubscribe",qos:1,messageId:e};return"string"===typeof r?o.unsubscriptions=[r]:Array.isArray(r)&&(o.unsubscriptions=r),t.options.resubscribe&&o.unsubscriptions.forEach((function(e){delete t._resubscribeTopics[e]})),"object"===typeof i&&i.properties&&(o.properties=i.properties),t.outgoing[o.messageId]={volatile:!0,cb:n},b("unsubscribe: call _sendPacket"),t._sendPacket(o),!0};return(this._storeProcessing||this._storeProcessingQueue.length>0||!s())&&this._storeProcessingQueue.push({invoke:s,callback:n}),this},x.prototype.end=function(t,e,r){const n=this;function i(){b("end :: closeStores: closing incoming and outgoing stores"),n.disconnected=!0,n.incomingStore.close((function(t){n.outgoingStore.close((function(e){if(b("end :: closeStores: emitting end"),n.emit("end"),r){const n=t||e;b("end :: closeStores: invoking callback with args"),r(n)}}))})),n._deferredReconnect&&n._deferredReconnect()}function o(){b("end :: (%s) :: finish :: calling _cleanUp with force %s",n.options.clientId,t),n._cleanUp(t,()=>{b("end :: finish :: calling process.nextTick on closeStores"),g(i.bind(n))},e)}return b("end :: (%s)",this.options.clientId),null!=t&&"boolean"===typeof t||(r=e||M,e=t,t=!1,"object"!==typeof e&&(r=e,e=null,"function"!==typeof r&&(r=M))),"object"!==typeof e&&(r=e,e=null),b("end :: cb? %s",!!r),r=r||M,this.disconnecting?(r(),this):(this._clearReconnect(),this.disconnecting=!0,!t&&Object.keys(this.outgoing).length>0?(b("end :: (%s) :: calling finish in 10ms once outgoing is empty",n.options.clientId),this.once("outgoingEmpty",setTimeout.bind(null,o,10))):(b("end :: (%s) :: immediately calling finish",n.options.clientId),o()),this)},x.prototype.removeOutgoingMessage=function(t){const e=this.outgoing[t]?this.outgoing[t].cb:null;return delete this.outgoing[t],this.outgoingStore.del({messageId:t},(function(){e(new Error("Message removed"))})),this},x.prototype.reconnect=function(t){b("client reconnect");const e=this,r=function(){t?(e.options.incomingStore=t.incomingStore,e.options.outgoingStore=t.outgoingStore):(e.options.incomingStore=null,e.options.outgoingStore=null),e.incomingStore=e.options.incomingStore||new o,e.outgoingStore=e.options.outgoingStore||new o,e.disconnecting=!1,e.disconnected=!1,e._deferredReconnect=null,e._reconnect()};return this.disconnecting&&!this.disconnected?this._deferredReconnect=r:r(),this},x.prototype._reconnect=function(){b("_reconnect: emitting reconnect to client"),this.emit("reconnect"),this.connected?(this.end(()=>{this._setupStream()}),b("client already connected. disconnecting first.")):(b("_reconnect: calling _setupStream"),this._setupStream())},x.prototype._setupReconnect=function(){const t=this;!t.disconnecting&&!t.reconnectTimer&&t.options.reconnectPeriod>0?(this.reconnecting||(b("_setupReconnect :: emit `offline` state"),this.emit("offline"),b("_setupReconnect :: set `reconnecting` to `true`"),this.reconnecting=!0),b("_setupReconnect :: setting reconnectTimer for %d ms",t.options.reconnectPeriod),t.reconnectTimer=setInterval((function(){b("reconnectTimer :: reconnect triggered!"),t._reconnect()}),t.options.reconnectPeriod)):b("_setupReconnect :: doing nothing...")},x.prototype._clearReconnect=function(){b("_clearReconnect : clearing reconnect timer"),this.reconnectTimer&&(clearInterval(this.reconnectTimer),this.reconnectTimer=null)},x.prototype._cleanUp=function(t,e){const r=arguments[2];if(e&&(b("_cleanUp :: done callback provided for on stream close"),this.stream.on("close",e)),b("_cleanUp :: forced? %s",t),t)0===this.options.reconnectPeriod&&this.options.clean&&O(this.outgoing),b("_cleanUp :: (%s) :: destroying stream",this.options.clientId),this.stream.destroy();else{const t=y({cmd:"disconnect"},r);b("_cleanUp :: (%s) :: call _sendPacket with disconnect packet",this.options.clientId),this._sendPacket(t,m.bind(null,this.stream.end.bind(this.stream)))}this.disconnecting||(b("_cleanUp :: client not disconnecting. Clearing and resetting reconnect."),this._clearReconnect(),this._setupReconnect()),null!==this.pingTimer&&(b("_cleanUp :: clearing pingTimer"),this.pingTimer.clear(),this.pingTimer=null),e&&!this.connected&&(b("_cleanUp :: (%s) :: removing stream `done` callback `close` listener",this.options.clientId),this.stream.removeListener("close",e),e())},x.prototype._sendPacket=function(t,e,r){b("_sendPacket :: (%s) ::  start",this.options.clientId),r=r||M,e=e||M;const n=E(this,t);if(n)e(n);else{if(!this.connected)return"auth"===t.cmd?(this._shiftPingInterval(),void A(this,t,e)):(b("_sendPacket :: client not connected. Storing packet offline."),void this._storePacket(t,e,r));switch(this._shiftPingInterval(),t.cmd){case"publish":break;case"pubrel":return void R(this,t,e,r);default:return void A(this,t,e)}switch(t.qos){case 2:case 1:R(this,t,e,r);break;case 0:default:A(this,t,e);break}b("_sendPacket :: (%s) ::  end",this.options.clientId)}},x.prototype._storePacket=function(t,e,r){b("_storePacket :: packet: %o",t),b("_storePacket :: cb? %s",!!e),r=r||M;let n=t;if("publish"===n.cmd){n=p(t);const r=k(this,n);if(r)return e&&e(r)}0===(n.qos||0)&&this.queueQoSZero||"publish"!==n.cmd?this.queue.push({packet:n,cb:e}):n.qos>0?(e=this.outgoing[n.messageId]?this.outgoing[n.messageId].cb:null,this.outgoingStore.put(n,(function(t){if(t)return e&&e(t);r()}))):e&&e(new Error("No connection to broker"))},x.prototype._setupPingTimer=function(){b("_setupPingTimer :: keepalive %d (seconds)",this.options.keepalive);const t=this;!this.pingTimer&&this.options.keepalive&&(this.pingResp=!0,this.pingTimer=f((function(){t._checkPing()}),1e3*this.options.keepalive))},x.prototype._shiftPingInterval=function(){this.pingTimer&&this.options.keepalive&&this.options.reschedulePings&&this.pingTimer.reschedule(1e3*this.options.keepalive)},x.prototype._checkPing=function(){b("_checkPing :: checking ping..."),this.pingResp?(b("_checkPing :: ping response received. Clearing flag and sending `pingreq`"),this.pingResp=!1,this._sendPacket({cmd:"pingreq"})):(b("_checkPing :: calling _cleanUp with force true"),this._cleanUp(!0))},x.prototype._handlePingresp=function(){this.pingResp=!0},x.prototype._handleConnack=function(t){b("_handleConnack");const e=this.options,r=e.protocolVersion,n=5===r?t.reasonCode:t.returnCode;if(clearTimeout(this.connackTimer),delete this.topicAliasSend,t.properties){if(t.properties.topicAliasMaximum){if(t.properties.topicAliasMaximum>65535)return void this.emit("error",new Error("topicAliasMaximum from broker is out of range"));t.properties.topicAliasMaximum>0&&(this.topicAliasSend=new a(t.properties.topicAliasMaximum))}t.properties.serverKeepAlive&&e.keepalive&&(e.keepalive=t.properties.serverKeepAlive,this._shiftPingInterval()),t.properties.maximumPacketSize&&(e.properties||(e.properties={}),e.properties.maximumPacketSize=t.properties.maximumPacketSize)}if(0===n)this.reconnecting=!1,this._onConnect(t);else if(n>0){const t=new Error("Connection refused: "+_[n]);t.code=n,this.emit("error",t)}},x.prototype._handleAuth=function(t){const e=this.options,r=e.protocolVersion,n=5===r?t.reasonCode:t.returnCode;if(5!==r){const t=new Error("Protocol error: Auth packets are only supported in MQTT 5. Your version:"+r);return t.code=n,void this.emit("error",t)}const i=this;this.handleAuth(t,(function(t,e){if(t)i.emit("error",t);else if(24===n)i.reconnecting=!1,i._sendPacket(e);else{const e=new Error("Connection refused: "+_[n]);t.code=n,i.emit("error",e)}}))},x.prototype.handleAuth=function(t,e){e()},x.prototype._handlePublish=function(t,e){b("_handlePublish: packet %o",t),e="undefined"!==typeof e?e:M;let r=t.topic.toString();const n=t.payload,i=t.qos,o=t.messageId,s=this,a=this.options,c=[0,16,128,131,135,144,145,151,153];if(5===this.options.protocolVersion){let e;if(t.properties&&(e=t.properties.topicAlias),"undefined"!==typeof e)if(0===r.length){if(!(e>0&&e<=65535))return b("_handlePublish :: topic alias out of range. alias: %d",e),void this.emit("error",new Error("Received Topic Alias is out of range"));{const t=this.topicAliasRecv.getTopicByAlias(e);if(!t)return b("_handlePublish :: unregistered topic alias. alias: %d",e),void this.emit("error",new Error("Received unregistered Topic Alias"));r=t,b("_handlePublish :: topic complemented by alias. topic: %s - alias: %d",r,e)}}else{if(!this.topicAliasRecv.put(r,e))return b("_handlePublish :: topic alias out of range. alias: %d",e),void this.emit("error",new Error("Received Topic Alias is out of range"));b("_handlePublish :: registered topic: %s - alias: %d",r,e)}}switch(b("_handlePublish: qos %d",i),i){case 2:a.customHandleAcks(r,n,t,(function(r,n){return r instanceof Error||(n=r,r=null),r?s.emit("error",r):-1===c.indexOf(n)?s.emit("error",new Error("Wrong reason code for pubrec")):void(n?s._sendPacket({cmd:"pubrec",messageId:o,reasonCode:n},e):s.incomingStore.put(t,(function(){s._sendPacket({cmd:"pubrec",messageId:o},e)})))}));break;case 1:a.customHandleAcks(r,n,t,(function(i,a){return i instanceof Error||(a=i,i=null),i?s.emit("error",i):-1===c.indexOf(a)?s.emit("error",new Error("Wrong reason code for puback")):(a||s.emit("message",r,n,t),void s.handleMessage(t,(function(t){if(t)return e&&e(t);s._sendPacket({cmd:"puback",messageId:o,reasonCode:a},e)})))}));break;case 0:this.emit("message",r,n,t),this.handleMessage(t,e);break;default:b("_handlePublish: unknown QoS. Doing nothing.");break}},x.prototype.handleMessage=function(t,e){e()},x.prototype._handleAck=function(t){const e=t.messageId,r=t.cmd;let n=null;const i=this.outgoing[e]?this.outgoing[e].cb:null,o=this;let s;if(i){switch(b("_handleAck :: packet type",r),r){case"pubcomp":case"puback":{const r=t.reasonCode;r&&r>0&&16!==r&&(s=new Error("Publish error: "+_[r]),s.code=r,i(s,t)),delete this.outgoing[e],this.outgoingStore.del(t,i),this.messageIdProvider.deallocate(e),this._invokeStoreProcessingQueue();break}case"pubrec":{n={cmd:"pubrel",qos:2,messageId:e};const r=t.reasonCode;r&&r>0&&16!==r?(s=new Error("Publish error: "+_[r]),s.code=r,i(s,t)):this._sendPacket(n);break}case"suback":delete this.outgoing[e],this.messageIdProvider.deallocate(e);for(let r=0;r<t.granted.length;r++)if(0!==(128&t.granted[r])){const t=this.messageIdToTopic[e];t&&t.forEach((function(t){delete o._resubscribeTopics[t]}))}this._invokeStoreProcessingQueue(),i(null,t);break;case"unsuback":delete this.outgoing[e],this.messageIdProvider.deallocate(e),this._invokeStoreProcessingQueue(),i(null);break;default:o.emit("error",new Error("unrecognized packet type"))}this.disconnecting&&0===Object.keys(this.outgoing).length&&this.emit("outgoingEmpty")}else b("_handleAck :: Server sent an ack in error. Ignoring.")},x.prototype._handlePubrel=function(t,e){b("handling pubrel packet"),e="undefined"!==typeof e?e:M;const r=t.messageId,n=this,i={cmd:"pubcomp",messageId:r};n.incomingStore.get(t,(function(t,r){t?n._sendPacket(i,e):(n.emit("message",r.topic,r.payload,r),n.handleMessage(r,(function(t){if(t)return e(t);n.incomingStore.del(r,M),n._sendPacket(i,e)})))}))},x.prototype._handleDisconnect=function(t){this.emit("disconnect",t)},x.prototype._nextId=function(){return this.messageIdProvider.allocate()},x.prototype.getLastMessageId=function(){return this.messageIdProvider.getLastAllocated()},x.prototype._resubscribe=function(){b("_resubscribe");const t=Object.keys(this._resubscribeTopics);if(!this._firstConnection&&(this.options.clean||5===this.options.protocolVersion&&!this.connackPacket.sessionPresent)&&t.length>0)if(this.options.resubscribe)if(5===this.options.protocolVersion){b("_resubscribe: protocolVersion 5");for(let e=0;e<t.length;e++){const r={};r[t[e]]=this._resubscribeTopics[t[e]],r.resubscribe=!0,this.subscribe(r,{properties:r[t[e]].properties})}}else this._resubscribeTopics.resubscribe=!0,this.subscribe(this._resubscribeTopics);else this._resubscribeTopics={};this._firstConnection=!1},x.prototype._onConnect=function(t){if(this.disconnected)return void this.emit("connect",t);const e=this;function r(){let n=e.outgoingStore.createStream();function i(){e._storeProcessing=!1,e._packetIdsDuringStoreProcessing={}}function o(){n.destroy(),n=null,e._flushStoreProcessingQueue(),i()}function s(){if(!n)return;e._storeProcessing=!0;const t=n.read(1);let r;t?e._packetIdsDuringStoreProcessing[t.messageId]?s():e.disconnecting||e.reconnectTimer?n.destroy&&n.destroy():(r=e.outgoing[t.messageId]?e.outgoing[t.messageId].cb:null,e.outgoing[t.messageId]={volatile:!1,cb:function(t,e){r&&r(t,e),s()}},e._packetIdsDuringStoreProcessing[t.messageId]=!0,e.messageIdProvider.register(t.messageId)?e._sendPacket(t):b("messageId: %d has already used.",t.messageId)):n.once("readable",s)}e.once("close",o),n.on("error",(function(t){i(),e._flushStoreProcessingQueue(),e.removeListener("close",o),e.emit("error",t)})),n.on("end",(function(){let n=!0;for(const t in e._packetIdsDuringStoreProcessing)if(!e._packetIdsDuringStoreProcessing[t]){n=!1;break}n?(i(),e.removeListener("close",o),e._invokeAllStoreProcessingQueue(),e.emit("connect",t)):r()})),s()}this.connackPacket=t,this.messageIdProvider.clear(),this._setupPingTimer(),this.connected=!0,r()},x.prototype._invokeStoreProcessingQueue=function(){if(this._storeProcessingQueue.length>0){const t=this._storeProcessingQueue[0];if(t&&t.invoke())return this._storeProcessingQueue.shift(),!0}return!1},x.prototype._invokeAllStoreProcessingQueue=function(){while(this._invokeStoreProcessingQueue());},x.prototype._flushStoreProcessingQueue=function(){for(const t of this._storeProcessingQueue)t.cbStorePut&&t.cbStorePut(new Error("Connection closed")),t.callback&&t.callback(new Error("Connection closed"));this._storeProcessingQueue.splice(0)},t.exports=x}).call(this,r("4362"),r("c8ba"))},e050:function(t,e,r){"use strict";t.exports=Math.max},e16f:function(t,e,r){"use strict";t.exports=Function.prototype.apply},e7d0:function(t,e,r){"use strict";function n(t){if(!(this instanceof n))return new n(t);this.aliasToTopic={},this.max=t}n.prototype.put=function(t,e){return!(0===e||e>this.max)&&(this.aliasToTopic[e]=t,this.length=Object.keys(this.aliasToTopic).length,!0)},n.prototype.getTopicByAlias=function(t){return this.aliasToTopic[t]},n.prototype.clear=function(){this.aliasToTopic={}},t.exports=n},e7fc:function(t,e,r){"use strict";(function(e){const n=r("df86"),i=r("ea08"),o=r("0b16"),s=r("53a8"),a=r("34eb")("mqttjs"),c={};function u(t){let e;t.auth&&(e=t.auth.match(/^(.+):(.+)$/),e?(t.username=e[1],t.password=e[2]):t.username=t.auth)}function l(t,e){if(a("connecting to an MQTT broker..."),"object"!==typeof t||e||(e=t,t=null),e=e||{},t){const r=o.parse(t,!0);if(null!=r.port&&(r.port=Number(r.port)),e=s(r,e),null===e.protocol)throw new Error("Missing protocol");e.protocol=e.protocol.replace(/:$/,"")}if(u(e),e.query&&"string"===typeof e.query.clientId&&(e.clientId=e.query.clientId),e.cert&&e.key){if(!e.protocol)throw new Error("Missing secure protocol key");if(-1===["mqtts","wss","wxs","alis"].indexOf(e.protocol))switch(e.protocol){case"mqtt":e.protocol="mqtts";break;case"ws":e.protocol="wss";break;case"wx":e.protocol="wxs";break;case"ali":e.protocol="alis";break;default:throw new Error('Unknown protocol for secure connection: "'+e.protocol+'"!')}}if(!c[e.protocol]){const t=-1!==["mqtts","wss"].indexOf(e.protocol);e.protocol=["mqtt","mqtts","ws","wss","wx","wxs","ali","alis"].filter((function(e,r){return(!t||r%2!==0)&&"function"===typeof c[e]}))[0]}if(!1===e.clean&&!e.clientId)throw new Error("Missing clientId for unclean clients");function r(t){return e.servers&&(t._reconnectCount&&t._reconnectCount!==e.servers.length||(t._reconnectCount=0),e.host=e.servers[t._reconnectCount].host,e.port=e.servers[t._reconnectCount].port,e.protocol=e.servers[t._reconnectCount].protocol?e.servers[t._reconnectCount].protocol:e.defaultProtocol,e.hostname=e.host,t._reconnectCount++),a("calling streambuilder for",e.protocol),c[e.protocol](t,e)}e.protocol&&(e.defaultProtocol=e.protocol);const i=new n(r,e);return i.on("error",(function(){})),i}"undefined"!==typeof e&&"browser"!==e.title||"function"!==typeof r?(c.mqtt=r("05ee"),c.tcp=r("05ee"),c.ssl=r("fe3c"),c.tls=r("fe3c"),c.mqtts=r("fe3c")):(c.wx=r("8311"),c.wxs=r("8311"),c.ali=r("c4c7"),c.alis=r("c4c7")),c.ws=r("fcb9"),c.wss=r("fcb9"),t.exports=l,t.exports.connect=l,t.exports.MqttClient=n,t.exports.Store=i}).call(this,r("4362"))},e937:function(t,e,r){"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function o(t,e,r){return e=u(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,u(n.key),n)}}function c(t,e,r){return e&&a(t.prototype,e),r&&a(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function u(t){var e=l(t,"string");return"symbol"===typeof e?e:String(e)}function l(t,e){if("object"!==typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var h=r("1c35"),f=h.Buffer,p=r(16),d=p.inspect,y=d&&d.custom||"inspect";function b(t,e,r){f.prototype.copy.call(t,e,r)}t.exports=function(){function t(){s(this,t),this.head=null,this.tail=null,this.length=0}return c(t,[{key:"push",value:function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length}},{key:"unshift",value:function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length}},{key:"shift",value:function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(t){if(0===this.length)return"";var e=this.head,r=""+e.data;while(e=e.next)r+=t+e.data;return r}},{key:"concat",value:function(t){if(0===this.length)return f.alloc(0);var e=f.allocUnsafe(t>>>0),r=this.head,n=0;while(r)b(r.data,e,n),n+=r.data.length,r=r.next;return e}},{key:"consume",value:function(t,e){var r;return t<this.head.data.length?(r=this.head.data.slice(0,t),this.head.data=this.head.data.slice(t)):r=t===this.head.data.length?this.shift():e?this._getString(t):this._getBuffer(t),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(t){var e=this.head,r=1,n=e.data;t-=n.length;while(e=e.next){var i=e.data,o=t>i.length?i.length:t;if(o===i.length?n+=i:n+=i.slice(0,t),t-=o,0===t){o===i.length?(++r,e.next?this.head=e.next:this.head=this.tail=null):(this.head=e,e.data=i.slice(o));break}++r}return this.length-=r,n}},{key:"_getBuffer",value:function(t){var e=f.allocUnsafe(t),r=this.head,n=1;r.data.copy(e),t-=r.data.length;while(r=r.next){var i=r.data,o=t>i.length?i.length:t;if(i.copy(e,e.length-t,0,o),t-=o,0===t){o===i.length?(++n,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=i.slice(o));break}++n}return this.length-=n,e}},{key:y,value:function(t,e){return d(this,i(i({},e),{},{depth:0,customInspect:!1}))}}]),t}()},ea08:function(t,e,r){"use strict";const n=r("53a8"),i=r("035d").Readable,o={objectMode:!0},s={clean:!0};function a(t){if(!(this instanceof a))return new a(t);this.options=t||{},this.options=n(s,t),this._inflights=new Map}a.prototype.put=function(t,e){return this._inflights.set(t.messageId,t),e&&e(),this},a.prototype.createStream=function(){const t=new i(o),e=[];let r=!1,n=0;return this._inflights.forEach((function(t,r){e.push(t)})),t._read=function(){!r&&n<e.length?this.push(e[n++]):this.push(null)},t.destroy=function(){if(r)return;const t=this;r=!0,setTimeout((function(){t.emit("close")}),0)},t},a.prototype.del=function(t,e){return t=this._inflights.get(t.messageId),t?(this._inflights.delete(t.messageId),e(null,t)):e&&e(new Error("missing packet")),this},a.prototype.get=function(t,e){return t=this._inflights.get(t.messageId),t?e(null,t):e&&e(new Error("missing packet")),this},a.prototype.close=function(t){this.options.clean&&(this._inflights=null),t&&t()},t.exports=a},edb3:function(t,e,r){"use strict";(function(e){function r(t,r){var o=this,a=this._readableState&&this._readableState.destroyed,c=this._writableState&&this._writableState.destroyed;return a||c?(r?r(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,e.nextTick(s,this,t)):e.nextTick(s,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!r&&t?o._writableState?o._writableState.errorEmitted?e.nextTick(i,o):(o._writableState.errorEmitted=!0,e.nextTick(n,o,t)):e.nextTick(n,o,t):r?(e.nextTick(i,o),r(t)):e.nextTick(i,o)})),this)}function n(t,e){s(t,e),i(t)}function i(t){t._writableState&&!t._writableState.emitClose||t._readableState&&!t._readableState.emitClose||t.emit("close")}function o(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function s(t,e){t.emit("error",e)}function a(t,e){var r=t._readableState,n=t._writableState;r&&r.autoDestroy||n&&n.autoDestroy?t.destroy(e):t.emit("error",e)}t.exports={destroy:r,undestroy:o,errorOrDestroy:a}}).call(this,r("4362"))},f0cb:function(t,e,r){(function(e){const r=65536,n={},i=e.isBuffer(e.from([1,2]).subarray(0,1));function o(t){const r=e.allocUnsafe(2);return r.writeUInt8(t>>8,0),r.writeUInt8(255&t,1),r}function s(){for(let t=0;t<r;t++)n[t]=o(t)}function a(t){const r=4;let n=0,o=0;const s=e.allocUnsafe(r);do{n=t%128|0,t=t/128|0,t>0&&(n|=128),s.writeUInt8(n,o++)}while(t>0&&o<r);return t>0&&(o=0),i?s.subarray(0,o):s.slice(0,o)}function c(t){const r=e.allocUnsafe(4);return r.writeUInt32BE(t,0),r}t.exports={cache:n,generateCache:s,generateNumber:o,genBufVariableByteInt:a,generate4ByteBuffer:c}}).call(this,r("1c35").Buffer)},f177:function(t,e,r){"use strict";var n=r("5402"),i=r("a29f"),o=r("bbc7"),s=Object.prototype.hasOwnProperty,a={brackets:function(t){return t+"[]"},comma:"comma",indices:function(t,e){return t+"["+e+"]"},repeat:function(t){return t}},c=Array.isArray,u=Array.prototype.push,l=function(t,e){u.apply(t,c(e)?e:[e])},h=Date.prototype.toISOString,f=o["default"],p={addQueryPrefix:!1,allowDots:!1,allowEmptyArrays:!1,arrayFormat:"indices",charset:"utf-8",charsetSentinel:!1,commaRoundTrip:!1,delimiter:"&",encode:!0,encodeDotInKeys:!1,encoder:i.encode,encodeValuesOnly:!1,filter:void 0,format:f,formatter:o.formatters[f],indices:!1,serializeDate:function(t){return h.call(t)},skipNulls:!1,strictNullHandling:!1},d=function(t){return"string"===typeof t||"number"===typeof t||"boolean"===typeof t||"symbol"===typeof t||"bigint"===typeof t},y={},b=function t(e,r,o,s,a,u,h,f,b,g,m,w,v,_,S,E,k,A){var O=e,P=A,R=0,M=!1;while(void 0!==(P=P.get(y))&&!M){var x=P.get(e);if(R+=1,"undefined"!==typeof x){if(x===R)throw new RangeError("Cyclic object value");M=!0}"undefined"===typeof P.get(y)&&(R=0)}if("function"===typeof g?O=g(r,O):O instanceof Date?O=v(O):"comma"===o&&c(O)&&(O=i.maybeMap(O,(function(t){return t instanceof Date?v(t):t}))),null===O){if(u)return b&&!E?b(r,p.encoder,k,"key",_):r;O=""}if(d(O)||i.isBuffer(O)){if(b){var I=E?r:b(r,p.encoder,k,"key",_);return[S(I)+"="+S(b(O,p.encoder,k,"value",_))]}return[S(r)+"="+S(String(O))]}var T,j=[];if("undefined"===typeof O)return j;if("comma"===o&&c(O))E&&b&&(O=i.maybeMap(O,b)),T=[{value:O.length>0?O.join(",")||null:void 0}];else if(c(g))T=g;else{var C=Object.keys(O);T=m?C.sort(m):C}var N=f?String(r).replace(/\./g,"%2E"):String(r),B=s&&c(O)&&1===O.length?N+"[]":N;if(a&&c(O)&&0===O.length)return B+"[]";for(var L=0;L<T.length;++L){var D=T[L],U="object"===typeof D&&D&&"undefined"!==typeof D.value?D.value:O[D];if(!h||null!==U){var F=w&&f?String(D).replace(/\./g,"%2E"):String(D),q=c(O)?"function"===typeof o?o(B,F):B:B+(w?"."+F:"["+F+"]");A.set(e,R);var W=n();W.set(y,A),l(j,t(U,q,o,s,a,u,h,f,"comma"===o&&E&&c(O)?null:b,g,m,w,v,_,S,E,k,W))}}return j},g=function(t){if(!t)return p;if("undefined"!==typeof t.allowEmptyArrays&&"boolean"!==typeof t.allowEmptyArrays)throw new TypeError("`allowEmptyArrays` option can only be `true` or `false`, when provided");if("undefined"!==typeof t.encodeDotInKeys&&"boolean"!==typeof t.encodeDotInKeys)throw new TypeError("`encodeDotInKeys` option can only be `true` or `false`, when provided");if(null!==t.encoder&&"undefined"!==typeof t.encoder&&"function"!==typeof t.encoder)throw new TypeError("Encoder has to be a function.");var e=t.charset||p.charset;if("undefined"!==typeof t.charset&&"utf-8"!==t.charset&&"iso-8859-1"!==t.charset)throw new TypeError("The charset option must be either utf-8, iso-8859-1, or undefined");var r=o["default"];if("undefined"!==typeof t.format){if(!s.call(o.formatters,t.format))throw new TypeError("Unknown format option provided.");r=t.format}var n,i=o.formatters[r],u=p.filter;if(("function"===typeof t.filter||c(t.filter))&&(u=t.filter),n=t.arrayFormat in a?t.arrayFormat:"indices"in t?t.indices?"indices":"repeat":p.arrayFormat,"commaRoundTrip"in t&&"boolean"!==typeof t.commaRoundTrip)throw new TypeError("`commaRoundTrip` must be a boolean, or absent");var l="undefined"===typeof t.allowDots?!0===t.encodeDotInKeys||p.allowDots:!!t.allowDots;return{addQueryPrefix:"boolean"===typeof t.addQueryPrefix?t.addQueryPrefix:p.addQueryPrefix,allowDots:l,allowEmptyArrays:"boolean"===typeof t.allowEmptyArrays?!!t.allowEmptyArrays:p.allowEmptyArrays,arrayFormat:n,charset:e,charsetSentinel:"boolean"===typeof t.charsetSentinel?t.charsetSentinel:p.charsetSentinel,commaRoundTrip:!!t.commaRoundTrip,delimiter:"undefined"===typeof t.delimiter?p.delimiter:t.delimiter,encode:"boolean"===typeof t.encode?t.encode:p.encode,encodeDotInKeys:"boolean"===typeof t.encodeDotInKeys?t.encodeDotInKeys:p.encodeDotInKeys,encoder:"function"===typeof t.encoder?t.encoder:p.encoder,encodeValuesOnly:"boolean"===typeof t.encodeValuesOnly?t.encodeValuesOnly:p.encodeValuesOnly,filter:u,format:r,formatter:i,serializeDate:"function"===typeof t.serializeDate?t.serializeDate:p.serializeDate,skipNulls:"boolean"===typeof t.skipNulls?t.skipNulls:p.skipNulls,sort:"function"===typeof t.sort?t.sort:null,strictNullHandling:"boolean"===typeof t.strictNullHandling?t.strictNullHandling:p.strictNullHandling}};t.exports=function(t,e){var r,i,o=t,s=g(e);"function"===typeof s.filter?(i=s.filter,o=i("",o)):c(s.filter)&&(i=s.filter,r=i);var u=[];if("object"!==typeof o||null===o)return"";var h=a[s.arrayFormat],f="comma"===h&&s.commaRoundTrip;r||(r=Object.keys(o)),s.sort&&r.sort(s.sort);for(var p=n(),d=0;d<r.length;++d){var y=r[d],m=o[y];s.skipNulls&&null===m||l(u,b(m,y,h,f,s.allowEmptyArrays,s.strictNullHandling,s.skipNulls,s.encodeDotInKeys,s.encode?s.encoder:null,s.filter,s.sort,s.allowDots,s.serializeDate,s.format,s.formatter,s.encodeValuesOnly,s.charset,p))}var w=u.join(s.delimiter),v=!0===s.addQueryPrefix?"?":"";return s.charsetSentinel&&("iso-8859-1"===s.charset?v+="utf8=%26%2310003%3B&":v+="utf8=%E2%9C%93&"),w.length>0?v+w:""}},f213:function(t,e,r){"use strict";var n=r("00ce"),i=r("3bbf"),o=r("2714"),s=r("0d25"),a=n("%Map%",!0),c=i("Map.prototype.get",!0),u=i("Map.prototype.set",!0),l=i("Map.prototype.has",!0),h=i("Map.prototype.delete",!0),f=i("Map.prototype.size",!0);t.exports=!!a&&function(){var t,e={assert:function(t){if(!e.has(t))throw new s("Side channel does not contain "+o(t))},delete:function(e){if(t){var r=h(t,e);return 0===f(t)&&(t=void 0),r}return!1},get:function(e){if(t)return c(t,e)},has:function(e){return!!t&&l(t,e)},set:function(e,r){t||(t=new a),u(t,e,r)}};return e}},f214:function(t,e,r){e=t.exports=r("0e8b"),e.Stream=e,e.Readable=e,e.Writable=r("f6ba"),e.Duplex=r("a493"),e.Transform=r("fe34"),e.PassThrough=r("9d37"),e.finished=r("bf09"),e.pipeline=r("386b")},f2e1:function(t,e,r){"use strict";t.exports="undefined"!==typeof Reflect&&Reflect.getPrototypeOf||null},f482:function(t,e,r){"use strict";(function(e){function r(t,r){var o=this,a=this._readableState&&this._readableState.destroyed,c=this._writableState&&this._writableState.destroyed;return a||c?(r?r(t):t&&(this._writableState?this._writableState.errorEmitted||(this._writableState.errorEmitted=!0,e.nextTick(s,this,t)):e.nextTick(s,this,t)),this):(this._readableState&&(this._readableState.destroyed=!0),this._writableState&&(this._writableState.destroyed=!0),this._destroy(t||null,(function(t){!r&&t?o._writableState?o._writableState.errorEmitted?e.nextTick(i,o):(o._writableState.errorEmitted=!0,e.nextTick(n,o,t)):e.nextTick(n,o,t):r?(e.nextTick(i,o),r(t)):e.nextTick(i,o)})),this)}function n(t,e){s(t,e),i(t)}function i(t){t._writableState&&!t._writableState.emitClose||t._readableState&&!t._readableState.emitClose||t.emit("close")}function o(){this._readableState&&(this._readableState.destroyed=!1,this._readableState.reading=!1,this._readableState.ended=!1,this._readableState.endEmitted=!1),this._writableState&&(this._writableState.destroyed=!1,this._writableState.ended=!1,this._writableState.ending=!1,this._writableState.finalCalled=!1,this._writableState.prefinished=!1,this._writableState.finished=!1,this._writableState.errorEmitted=!1)}function s(t,e){t.emit("error",e)}function a(t,e){var r=t._readableState,n=t._writableState;r&&r.autoDestroy||n&&n.autoDestroy?t.destroy(e):t.emit("error",e)}t.exports={destroy:r,undestroy:o,errorOrDestroy:a}}).call(this,r("4362"))},f688:function(t,e,r){"use strict";function n(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var n=Object.getOwnPropertySymbols(t);e&&(n=n.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,n)}return r}function i(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?n(Object(r),!0).forEach((function(e){o(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):n(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function o(t,e,r){return e=u(e),e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function s(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function a(t,e){for(var r=0;r<e.length;r++){var n=e[r];n.enumerable=n.enumerable||!1,n.configurable=!0,"value"in n&&(n.writable=!0),Object.defineProperty(t,u(n.key),n)}}function c(t,e,r){return e&&a(t.prototype,e),r&&a(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function u(t){var e=l(t,"string");return"symbol"===typeof e?e:String(e)}function l(t,e){if("object"!==typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var n=r.call(t,e||"default");if("object"!==typeof n)return n;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}var h=r("1c35"),f=h.Buffer,p=r(14),d=p.inspect,y=d&&d.custom||"inspect";function b(t,e,r){f.prototype.copy.call(t,e,r)}t.exports=function(){function t(){s(this,t),this.head=null,this.tail=null,this.length=0}return c(t,[{key:"push",value:function(t){var e={data:t,next:null};this.length>0?this.tail.next=e:this.head=e,this.tail=e,++this.length}},{key:"unshift",value:function(t){var e={data:t,next:this.head};0===this.length&&(this.tail=e),this.head=e,++this.length}},{key:"shift",value:function(){if(0!==this.length){var t=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,t}}},{key:"clear",value:function(){this.head=this.tail=null,this.length=0}},{key:"join",value:function(t){if(0===this.length)return"";var e=this.head,r=""+e.data;while(e=e.next)r+=t+e.data;return r}},{key:"concat",value:function(t){if(0===this.length)return f.alloc(0);var e=f.allocUnsafe(t>>>0),r=this.head,n=0;while(r)b(r.data,e,n),n+=r.data.length,r=r.next;return e}},{key:"consume",value:function(t,e){var r;return t<this.head.data.length?(r=this.head.data.slice(0,t),this.head.data=this.head.data.slice(t)):r=t===this.head.data.length?this.shift():e?this._getString(t):this._getBuffer(t),r}},{key:"first",value:function(){return this.head.data}},{key:"_getString",value:function(t){var e=this.head,r=1,n=e.data;t-=n.length;while(e=e.next){var i=e.data,o=t>i.length?i.length:t;if(o===i.length?n+=i:n+=i.slice(0,t),t-=o,0===t){o===i.length?(++r,e.next?this.head=e.next:this.head=this.tail=null):(this.head=e,e.data=i.slice(o));break}++r}return this.length-=r,n}},{key:"_getBuffer",value:function(t){var e=f.allocUnsafe(t),r=this.head,n=1;r.data.copy(e),t-=r.data.length;while(r=r.next){var i=r.data,o=t>i.length?i.length:t;if(i.copy(e,e.length-t,0,o),t-=o,0===t){o===i.length?(++n,r.next?this.head=r.next:this.head=this.tail=null):(this.head=r,r.data=i.slice(o));break}++n}return this.length-=n,e}},{key:y,value:function(t,e){return d(this,i(i({},e),{},{depth:0,customInspect:!1}))}}]),t}()},f6ba:function(t,e,r){"use strict";(function(e,n){function i(t){var e=this;this.next=null,this.entry=null,this.finish=function(){V(e,t)}}var o;t.exports=R,R.WritableState=P;var s={deprecate:r("b7d1")},a=r("b98b"),c=r("1c35").Buffer,u=("undefined"!==typeof e?e:"undefined"!==typeof window?window:"undefined"!==typeof self?self:{}).Uint8Array||function(){};function l(t){return c.from(t)}function h(t){return c.isBuffer(t)||t instanceof u}var f,p=r("f482"),d=r("86c6"),y=d.getHighWaterMark,b=r("9bfc").codes,g=b.ERR_INVALID_ARG_TYPE,m=b.ERR_METHOD_NOT_IMPLEMENTED,w=b.ERR_MULTIPLE_CALLBACK,v=b.ERR_STREAM_CANNOT_PIPE,_=b.ERR_STREAM_DESTROYED,S=b.ERR_STREAM_NULL_VALUES,E=b.ERR_STREAM_WRITE_AFTER_END,k=b.ERR_UNKNOWN_ENCODING,A=p.errorOrDestroy;function O(){}function P(t,e,n){o=o||r("a493"),t=t||{},"boolean"!==typeof n&&(n=e instanceof o),this.objectMode=!!t.objectMode,n&&(this.objectMode=this.objectMode||!!t.writableObjectMode),this.highWaterMark=y(this,t,"writableHighWaterMark",n),this.finalCalled=!1,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1,this.destroyed=!1;var s=!1===t.decodeStrings;this.decodeStrings=!s,this.defaultEncoding=t.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(t){B(e,t)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.emitClose=!1!==t.emitClose,this.autoDestroy=!!t.autoDestroy,this.bufferedRequestCount=0,this.corkedRequestsFree=new i(this)}function R(t){o=o||r("a493");var e=this instanceof o;if(!e&&!f.call(R,this))return new R(t);this._writableState=new P(t,this,e),this.writable=!0,t&&("function"===typeof t.write&&(this._write=t.write),"function"===typeof t.writev&&(this._writev=t.writev),"function"===typeof t.destroy&&(this._destroy=t.destroy),"function"===typeof t.final&&(this._final=t.final)),a.call(this)}function M(t,e){var r=new E;A(t,r),n.nextTick(e,r)}function x(t,e,r,i){var o;return null===r?o=new S:"string"===typeof r||e.objectMode||(o=new g("chunk",["string","Buffer"],r)),!o||(A(t,o),n.nextTick(i,o),!1)}function I(t,e,r){return t.objectMode||!1===t.decodeStrings||"string"!==typeof e||(e=c.from(e,r)),e}function T(t,e,r,n,i,o){if(!r){var s=I(e,n,i);n!==s&&(r=!0,i="buffer",n=s)}var a=e.objectMode?1:n.length;e.length+=a;var c=e.length<e.highWaterMark;if(c||(e.needDrain=!0),e.writing||e.corked){var u=e.lastBufferedRequest;e.lastBufferedRequest={chunk:n,encoding:i,isBuf:r,callback:o,next:null},u?u.next=e.lastBufferedRequest:e.bufferedRequest=e.lastBufferedRequest,e.bufferedRequestCount+=1}else j(t,e,!1,a,n,i,o);return c}function j(t,e,r,n,i,o,s){e.writelen=n,e.writecb=s,e.writing=!0,e.sync=!0,e.destroyed?e.onwrite(new _("write")):r?t._writev(i,e.onwrite):t._write(i,o,e.onwrite),e.sync=!1}function C(t,e,r,i,o){--e.pendingcb,r?(n.nextTick(o,i),n.nextTick(K,t,e),t._writableState.errorEmitted=!0,A(t,i)):(o(i),t._writableState.errorEmitted=!0,A(t,i),K(t,e))}function N(t){t.writing=!1,t.writecb=null,t.length-=t.writelen,t.writelen=0}function B(t,e){var r=t._writableState,i=r.sync,o=r.writecb;if("function"!==typeof o)throw new w;if(N(r),e)C(t,r,i,e,o);else{var s=F(r)||t.destroyed;s||r.corked||r.bufferProcessing||!r.bufferedRequest||U(t,r),i?n.nextTick(L,t,r,s,o):L(t,r,s,o)}}function L(t,e,r,n){r||D(t,e),e.pendingcb--,n(),K(t,e)}function D(t,e){0===e.length&&e.needDrain&&(e.needDrain=!1,t.emit("drain"))}function U(t,e){e.bufferProcessing=!0;var r=e.bufferedRequest;if(t._writev&&r&&r.next){var n=e.bufferedRequestCount,o=new Array(n),s=e.corkedRequestsFree;s.entry=r;var a=0,c=!0;while(r)o[a]=r,r.isBuf||(c=!1),r=r.next,a+=1;o.allBuffers=c,j(t,e,!0,e.length,o,"",s.finish),e.pendingcb++,e.lastBufferedRequest=null,s.next?(e.corkedRequestsFree=s.next,s.next=null):e.corkedRequestsFree=new i(e),e.bufferedRequestCount=0}else{while(r){var u=r.chunk,l=r.encoding,h=r.callback,f=e.objectMode?1:u.length;if(j(t,e,!1,f,u,l,h),r=r.next,e.bufferedRequestCount--,e.writing)break}null===r&&(e.lastBufferedRequest=null)}e.bufferedRequest=r,e.bufferProcessing=!1}function F(t){return t.ending&&0===t.length&&null===t.bufferedRequest&&!t.finished&&!t.writing}function q(t,e){t._final((function(r){e.pendingcb--,r&&A(t,r),e.prefinished=!0,t.emit("prefinish"),K(t,e)}))}function W(t,e){e.prefinished||e.finalCalled||("function"!==typeof t._final||e.destroyed?(e.prefinished=!0,t.emit("prefinish")):(e.pendingcb++,e.finalCalled=!0,n.nextTick(q,t,e)))}function K(t,e){var r=F(e);if(r&&(W(t,e),0===e.pendingcb&&(e.finished=!0,t.emit("finish"),e.autoDestroy))){var n=t._readableState;(!n||n.autoDestroy&&n.endEmitted)&&t.destroy()}return r}function H(t,e,r){e.ending=!0,K(t,e),r&&(e.finished?n.nextTick(r):t.once("finish",r)),e.ended=!0,t.writable=!1}function V(t,e,r){var n=t.entry;t.entry=null;while(n){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree.next=t}r("3fb5")(R,a),P.prototype.getBuffer=function(){var t=this.bufferedRequest,e=[];while(t)e.push(t),t=t.next;return e},function(){try{Object.defineProperty(P.prototype,"buffer",{get:s.deprecate((function(){return this.getBuffer()}),"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.","DEP0003")})}catch(t){}}(),"function"===typeof Symbol&&Symbol.hasInstance&&"function"===typeof Function.prototype[Symbol.hasInstance]?(f=Function.prototype[Symbol.hasInstance],Object.defineProperty(R,Symbol.hasInstance,{value:function(t){return!!f.call(this,t)||this===R&&(t&&t._writableState instanceof P)}})):f=function(t){return t instanceof this},R.prototype.pipe=function(){A(this,new v)},R.prototype.write=function(t,e,r){var n=this._writableState,i=!1,o=!n.objectMode&&h(t);return o&&!c.isBuffer(t)&&(t=l(t)),"function"===typeof e&&(r=e,e=null),o?e="buffer":e||(e=n.defaultEncoding),"function"!==typeof r&&(r=O),n.ending?M(this,r):(o||x(this,n,t,r))&&(n.pendingcb++,i=T(this,n,o,t,e,r)),i},R.prototype.cork=function(){this._writableState.corked++},R.prototype.uncork=function(){var t=this._writableState;t.corked&&(t.corked--,t.writing||t.corked||t.bufferProcessing||!t.bufferedRequest||U(this,t))},R.prototype.setDefaultEncoding=function(t){if("string"===typeof t&&(t=t.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((t+"").toLowerCase())>-1))throw new k(t);return this._writableState.defaultEncoding=t,this},Object.defineProperty(R.prototype,"writableBuffer",{enumerable:!1,get:function(){return this._writableState&&this._writableState.getBuffer()}}),Object.defineProperty(R.prototype,"writableHighWaterMark",{enumerable:!1,get:function(){return this._writableState.highWaterMark}}),R.prototype._write=function(t,e,r){r(new m("_write()"))},R.prototype._writev=null,R.prototype.end=function(t,e,r){var n=this._writableState;return"function"===typeof t?(r=t,t=null,e=null):"function"===typeof e&&(r=e,e=null),null!==t&&void 0!==t&&this.write(t,e),n.corked&&(n.corked=1,this.uncork()),n.ending||H(this,n,r),this},Object.defineProperty(R.prototype,"writableLength",{enumerable:!1,get:function(){return this._writableState.length}}),Object.defineProperty(R.prototype,"destroyed",{enumerable:!1,get:function(){return void 0!==this._writableState&&this._writableState.destroyed},set:function(t){this._writableState&&(this._writableState.destroyed=t)}}),R.prototype.destroy=p.destroy,R.prototype._undestroy=p.undestroy,R.prototype._destroy=function(t,e){e(t)}}).call(this,r("c8ba"),r("4362"))},f9ae:function(t,e,r){"use strict";var n=r("0f7c"),i=r("0d25"),o=r("926d"),s=r("3b6a");t.exports=function(t){if(t.length<1||"function"!==typeof t[0])throw new i("a function is required");return s(n,o,t)}},f9c1:function(t,e){function r(t){var e=t._readableState;return e?e.objectMode||"number"===typeof t._duplexState?t.read():t.read(n(e)):null}function n(t){if(t.buffer.length){var e=t.bufferIndex||0;if(t.buffer.head)return t.buffer.head.data.length;if(t.buffer.length-e>0&&t.buffer[e])return t.buffer[e].length}return t.length}t.exports=r},fbd7:function(t,e,r){"use strict";function n(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,t.__proto__=e}var i={};function o(t,e,r){function o(t,r,n){return"string"===typeof e?e:e(t,r,n)}r||(r=Error);var s=function(t){function e(e,r,n){return t.call(this,o(e,r,n))||this}return n(e,t),e}(r);s.prototype.name=r.name,s.prototype.code=t,i[t]=s}function s(t,e){if(Array.isArray(t)){var r=t.length;return t=t.map((function(t){return String(t)})),r>2?"one of ".concat(e," ").concat(t.slice(0,r-1).join(", "),", or ")+t[r-1]:2===r?"one of ".concat(e," ").concat(t[0]," or ").concat(t[1]):"of ".concat(e," ").concat(t[0])}return"of ".concat(e," ").concat(String(t))}function a(t,e,r){return t.substr(!r||r<0?0:+r,e.length)===e}function c(t,e,r){return(void 0===r||r>t.length)&&(r=t.length),t.substring(r-e.length,r)===e}function u(t,e,r){return"number"!==typeof r&&(r=0),!(r+e.length>t.length)&&-1!==t.indexOf(e,r)}o("ERR_INVALID_OPT_VALUE",(function(t,e){return'The value "'+e+'" is invalid for option "'+t+'"'}),TypeError),o("ERR_INVALID_ARG_TYPE",(function(t,e,r){var n,i;if("string"===typeof e&&a(e,"not ")?(n="must not be",e=e.replace(/^not /,"")):n="must be",c(t," argument"))i="The ".concat(t," ").concat(n," ").concat(s(e,"type"));else{var o=u(t,".")?"property":"argument";i='The "'.concat(t,'" ').concat(o," ").concat(n," ").concat(s(e,"type"))}return i+=". Received type ".concat(typeof r),i}),TypeError),o("ERR_STREAM_PUSH_AFTER_EOF","stream.push() after EOF"),o("ERR_METHOD_NOT_IMPLEMENTED",(function(t){return"The "+t+" method is not implemented"})),o("ERR_STREAM_PREMATURE_CLOSE","Premature close"),o("ERR_STREAM_DESTROYED",(function(t){return"Cannot call "+t+" after a stream was destroyed"})),o("ERR_MULTIPLE_CALLBACK","Callback called multiple times"),o("ERR_STREAM_CANNOT_PIPE","Cannot pipe, not readable"),o("ERR_STREAM_WRITE_AFTER_END","write after end"),o("ERR_STREAM_NULL_VALUES","May not write null values to stream",TypeError),o("ERR_UNKNOWN_ENCODING",(function(t){return"Unknown encoding: "+t}),TypeError),o("ERR_STREAM_UNSHIFT_AFTER_END_EVENT","stream.unshift() after end event"),t.exports.codes=i},fcb9:function(t,e,r){"use strict";(function(e){const{Buffer:n}=r("1c35"),i=r("7f73"),o=r("34eb")("mqttjs:ws"),s=r("56ac"),a=r("035d").Transform,c=["rejectUnauthorized","ca","cert","key","pfx","passphrase"],u="undefined"!==typeof e&&"browser"===e.title||"function"===typeof r;function l(t,e){let r=t.protocol+"://"+t.hostname+":"+t.port+t.path;return"function"===typeof t.transformWsUrl&&(r=t.transformWsUrl(r,t,e)),r}function h(t){const e=t;return t.hostname||(e.hostname="localhost"),t.port||("wss"===t.protocol?e.port=443:e.port=80),t.path||(e.path="/"),t.wsOptions||(e.wsOptions={}),u||"wss"!==t.protocol||c.forEach((function(r){Object.prototype.hasOwnProperty.call(t,r)&&!Object.prototype.hasOwnProperty.call(t.wsOptions,r)&&(e.wsOptions[r]=t[r])})),e}function f(t){const e=h(t);if(e.hostname||(e.hostname=e.host),!e.hostname){if("undefined"===typeof document)throw new Error("Could not determine host. Specify host manually.");const t=new URL(document.URL);e.hostname=t.hostname,e.port||(e.port=t.port)}return void 0===e.objectMode&&(e.objectMode=!(!0===e.binary||void 0===e.binary)),e}function p(t,e,r){o("createWebSocket"),o("protocol: "+r.protocolId+" "+r.protocolVersion);const n="MQIsdp"===r.protocolId&&3===r.protocolVersion?"mqttv3.1":"mqtt";o("creating new Websocket for url: "+e+" and protocol: "+n);const s=new i(e,[n],r.wsOptions);return s}function d(t,e){const r="MQIsdp"===e.protocolId&&3===e.protocolVersion?"mqttv3.1":"mqtt",n=l(e,t),i=new WebSocket(n,[r]);return i.binaryType="arraybuffer",i}function y(t,e){o("streamBuilder");const r=h(e),n=l(r,t),s=p(t,n,r),a=i.createWebSocketStream(s,r.wsOptions);return a.url=n,s.on("close",()=>{a.destroy()}),a}function b(t,e){let r;o("browserStreamBuilder");const i=f(e),c=i.browserBufferSize||524288,u=e.browserBufferTimeout||1e3,l=!e.objectMode,h=d(t,e),p=b(e,S,E);e.objectMode||(p._writev=_),p.on("close",()=>{h.close()});const y="undefined"!==typeof h.addEventListener;function b(t,e,r){const n=new a({objectModeMode:t.objectMode});return n._write=e,n._flush=r,n}function g(){r.setReadable(p),r.setWritable(p),r.emit("connect")}function m(){r.end(),r.destroy()}function w(t){r.destroy(t)}function v(t){let e=t.data;e=e instanceof ArrayBuffer?n.from(e):n.from(e,"utf8"),p.push(e)}function _(t,e){const r=new Array(t.length);for(let i=0;i<t.length;i++)"string"===typeof t[i].chunk?r[i]=n.from(t[i],"utf8"):r[i]=t[i].chunk;this._write(n.concat(r),"binary",e)}function S(t,e,r){h.bufferedAmount>c&&setTimeout(S,u,t,e,r),l&&"string"===typeof t&&(t=n.from(t,"utf8"));try{h.send(t)}catch(i){return r(i)}r()}function E(t){h.close(),t()}return h.readyState===h.OPEN?r=p:(r=r=s(void 0,void 0,e),e.objectMode||(r._writev=_),y?h.addEventListener("open",g):h.onopen=g),r.socket=h,y?(h.addEventListener("close",m),h.addEventListener("error",w),h.addEventListener("message",v)):(h.onclose=m,h.onerror=w,h.onmessage=v),r}t.exports=u?b:y}).call(this,r("4362"))},fe34:function(t,e,r){"use strict";t.exports=l;var n=r("9bfc").codes,i=n.ERR_METHOD_NOT_IMPLEMENTED,o=n.ERR_MULTIPLE_CALLBACK,s=n.ERR_TRANSFORM_ALREADY_TRANSFORMING,a=n.ERR_TRANSFORM_WITH_LENGTH_0,c=r("a493");function u(t,e){var r=this._transformState;r.transforming=!1;var n=r.writecb;if(null===n)return this.emit("error",new o);r.writechunk=null,r.writecb=null,null!=e&&this.push(e),n(t);var i=this._readableState;i.reading=!1,(i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}function l(t){if(!(this instanceof l))return new l(t);c.call(this,t),this._transformState={afterTransform:u.bind(this),needTransform:!1,transforming:!1,writecb:null,writechunk:null,writeencoding:null},this._readableState.needReadable=!0,this._readableState.sync=!1,t&&("function"===typeof t.transform&&(this._transform=t.transform),"function"===typeof t.flush&&(this._flush=t.flush)),this.on("prefinish",h)}function h(){var t=this;"function"!==typeof this._flush||this._readableState.destroyed?f(this,null,null):this._flush((function(e,r){f(t,e,r)}))}function f(t,e,r){if(e)return t.emit("error",e);if(null!=r&&t.push(r),t._writableState.length)throw new a;if(t._transformState.transforming)throw new s;return t.push(null)}r("3fb5")(l,c),l.prototype.push=function(t,e){return this._transformState.needTransform=!1,c.prototype.push.call(this,t,e)},l.prototype._transform=function(t,e,r){r(new i("_transform()"))},l.prototype._write=function(t,e,r){var n=this._transformState;if(n.writecb=r,n.writechunk=t,n.writeencoding=e,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},l.prototype._read=function(t){var e=this._transformState;null===e.writechunk||e.transforming?e.needTransform=!0:(e.transforming=!0,this._transform(e.writechunk,e.writeencoding,e.afterTransform))},l.prototype._destroy=function(t,e){c.prototype._destroy.call(this,t,(function(t){e(t)}))}},fe3c:function(t,e,r){"use strict";const n=r(18),i=r(2),o=r("34eb")("mqttjs:tls");function s(t,e){e.port=e.port||8883,e.host=e.hostname||e.host||"localhost",0===i.isIP(e.host)&&(e.servername=e.host),e.rejectUnauthorized=!1!==e.rejectUnauthorized,delete e.path,o("port %d host %s rejectUnauthorized %b",e.port,e.host,e.rejectUnauthorized);const r=n.connect(e);function s(n){e.rejectUnauthorized&&t.emit("error",n),r.end()}return r.on("secureConnect",(function(){e.rejectUnauthorized&&!r.authorized?r.emit("error",new Error("TLS not authorized")):r.removeListener("error",s)})),r.on("error",s),r}t.exports=s}}]);