(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-f5a1632c"],{"07cf":function(t,e,a){"use strict";a("21c1")},"129f":function(t,e){t.exports=Object.is||function(t,e){return t===e?0!==t||1/t===1/e:t!=t&&e!=e}},"1c2f":function(t,e,a){"use strict";var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{staticClass:"filter-container",staticStyle:{"margin-bottom":"5px"}},[a("el-input",{staticClass:"filter-item",staticStyle:{width:"200px"},attrs:{placeholder:"请输入查询",size:"small"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}}),a("el-button",{staticClass:"filter-item",attrs:{type:"primary",size:"small"},on:{click:function(e){return t.search(t.strfilter)}}},[t._v(" 查询 ")])],1),a("div",[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"selPwProcess",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:"400px","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"},"row-class-name":t.rowIndex},on:{"row-click":t.rowClick}},[1==t.multi?a("el-table-column",{attrs:{fixed:"left",type:"selection",width:"55"}}):a("el-table-column",{attrs:{label:"",width:"40",fixed:""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-radio",{attrs:{label:e.$index},nativeOn:{change:function(a){return t.getCurrentRow(e.row)}},model:{value:t.radio,callback:function(e){t.radio=e},expression:"radio"}},[t._v(" "+t._s(" "))])]}}])}),a("el-table-column",{attrs:{label:"用户名",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.username))])]}}])}),a("el-table-column",{attrs:{label:"姓名",align:"center","min-width":"100","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.realname))])]}}])}),a("el-table-column",{attrs:{label:"手机号",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.phone))])]}}])}),a("el-table-column",{attrs:{label:"邮箱",align:"center","min-width":"70","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.row.email))])]}}])})],1)],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)},o=[],r=a("ade3"),n=(a("99af"),a("e9c4"),a("d3b7"),a("25f0"),a("4d90"),a("b775")),s=a("333d"),l={components:{Pagination:s["a"]},filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0");return"".concat(a,"/").concat(i,"/").concat(o)}},props:["multi"],data:function(){return Object(r["a"])(Object(r["a"])({title:"用户",listLoading:!0,lst:[],searchstr:" ",strfilter:"",radio:"",selrows:"",total:0},"searchstr",""),"queryParams",{PageNum:1,PageSize:10,OrderType:1,SearchType:1})},watch:{},created:function(){this.searchstr="",this.bindData()},methods:{getCurrentRow:function(t){this.$forceUpdate(),this.selrows=t,this.$emit("singleSel",t)},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},bindData:function(){var t=this;this.listLoading=!0,n["a"].post("/PmsSaUser/getPageList",JSON.stringify(this.queryParams)).then((function(e){200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},search:function(t){""!=t?this.queryParams.SearchPojo={username:t,realname:t}:this.$delete(this.queryParams,"SearchPojo"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},rowIndex:function(t){var e=t.row,a=t.rowIndex;e.row_index=a},rowClick:function(t){this.radio=t.row_index,this.getCurrentRow(t)}}},c=l,d=(a("f38a"),a("2877")),m=Object(d["a"])(c,i,o,!1,null,"409c3b4c",null);e["a"]=m.exports},"21c1":function(t,e,a){},"2a15":function(t,e,a){},3611:function(t,e,a){"use strict";a("9c3b")},"3e20":function(t,e,a){},5658:function(t,e,a){"use strict";a("7973")},"6f64":function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[t.FormVisible?a("div",{ref:"formedit",staticClass:"formedit"},[a("formedit",t._g({ref:"formedit",attrs:{idx:t.idx}},{compForm:t.compForm,closeForm:t.closeForm,changeidx:t.changeidx,bindData:t.bindData}))],1):t._e(),0==t.FormVisible?a("div",{ref:"index",staticClass:"index"},[a("listheader",{attrs:{tableForm:t.tableForm,showTree:t.treeVisble},on:{btnadd:function(e){return t.showform(0)},btnshowGroup:function(e){t.treeVisble=!t.treeVisble},btnsearch:t.search,bindData:t.bindData,AdvancedSearch:t.AdvancedSearch,btnExport:t.btnExport,bindColumn:t.getcolumn}}),a("div",{staticClass:"page-container"},[a("el-row",[a("el-col",{directives:[{name:"show",rawName:"v-show",value:t.treeVisble,expression:"treeVisble"}],attrs:{span:4}},[a("div",{staticStyle:{"font-size":"14px",color:"#606266"}},[a("div",{staticClass:"groupTitle"},[a("span",[t._v("分组")]),a("i",{staticClass:"el-icon-s-tools",style:{color:t.treeEditable?"#1e80ff":""},on:{click:function(e){return t.treeEdit()}}})]),a("el-tree",{staticStyle:{"font-size":"14px",height:"80vh",overflow:"auto",width:"100%"},attrs:{data:t.groupData,"node-key":"id","default-expand-all":""},scopedSlots:t._u([{key:"default",fn:function(e){var i=e.node,o=e.data;return a("span",{staticClass:"custom-tree-node"},[a("span",{on:{click:function(){return t.handleNodeClick(o)}}},[t._v(t._s(i.label)+" "),o.prefix?a("a",{attrs:{href:"javascript:;"}},[t._v("["+t._s(o.prefix)+"]")]):t._e()]),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==o.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-edit"},on:{click:function(){return t.editTreeNode(o)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-circle-plus-outline"},on:{click:function(){return t.addTreeChild(o)}}})],1),a("span",{directives:[{name:"show",rawName:"v-show",value:t.treeEditable,expression:"treeEditable"}]},[a("el-button",{directives:[{name:"show",rawName:"v-show",value:"0"!==o.id,expression:"data.id !== '0'"}],staticStyle:{"padding-left":"5px"},attrs:{type:"text",size:"mini",icon:"el-icon-delete-solid"},on:{click:function(){return t.delTreeNode(o)}}})],1)])}}],null,!1,744933850)})],1)]),a("el-col",{attrs:{span:t.treeVisble?20:24}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.listLoading,expression:"listLoading"}],ref:"tableData",staticClass:"table-container",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"1px 0px 1px 0px"},height:t.tableMaxHeight},on:{"sort-change":t.changeSort}},[a("el-table-column",{attrs:{align:"center",label:"ID","min-width":"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.$index+1)+" ")]}}],null,!1,3056706777)}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["groupuid"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showform(i.row.id)}}},[t._v(t._s(i.row.groupuid?i.row.groupuid:"客户编码"))]):"createdate"==e.itemcode||"modifydate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):"operate"==e.itemcode?a("div",[a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.showDetail(i.row,i.$index)}}},[t._v("详情")])],1):a("span",[t._v(t._s(i.row[e.itemcode]))])]}}],null,!0)})]}))],2),a("div",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],staticClass:"flex j-s a-c"},[a("Pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total > 0"}],attrs:{total:t.total,page:t.queryParams.PageNum,limit:t.queryParams.PageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"PageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"PageSize",e)},pagination:t.GetList}})],1)],1)],1)],1)],1):t._e(),t.gropuFormVisible?a("el-dialog",{attrs:{title:"货品分类","append-to-body":!0,visible:t.gropuFormVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.gropuFormVisible=e}}},[a("group",t._g({ref:"group",attrs:{idx:t.idx,pid:t.pid},on:{closeDialog:function(e){t.gropuFormVisible=!1}}},{compForm:t.compForm,bindData:t.BindTreeData}))],1):t._e(),t.detailvisible?a("el-drawer",{attrs:{visible:t.detailvisible,"with-header":!1,size:"80%"},on:{"update:visible":function(e){t.detailvisible=e}}},[a("FormDetail",{ref:"FormDetail",attrs:{idx:t.idx},on:{closeDialog:function(e){t.detailvisible=!1},bindData:t.bindData}})],1):t._e()],1)},o=[],r=a("2909"),n=(a("99af"),a("d81d"),a("e9c4"),a("b64b"),a("d3b7"),a("ac1f"),a("841c"),a("0643"),a("a573"),a("b775")),s=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")]),a("el-dropdown",{staticStyle:{"margin-left":"10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id},nativeOn:{click:function(e){return t.deleteForm(t.idx)}}},[t._v("删 除")])],1)],1),a("el-dropdown",{staticStyle:{margin:"0 10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("过程"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"})],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.closeForm(e)}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("p",{staticClass:"formTitle"},[t._v(t._s(t.title))]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-divider",{attrs:{"content-position":"left"}},[t._v("分组编码")]),a("el-row",[a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("wggroupid")}}},[a("el-form-item",{attrs:{label:"分组名称",prop:"wggroupid"}},[a("el-cascader",{ref:"cascader",staticStyle:{width:"100%"},attrs:{options:t.groupnameOptions,props:t.defaultProps,"show-all-levels":!1,placeholder:"请选择分组名称",size:"small"},on:{change:t.handleChangeGroupid},model:{value:t.formdata.wggroupid,callback:function(e){t.$set(t.formdata,"wggroupid",e)},expression:"formdata.wggroupid"}})],1)],1)]),a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("groupuid")}}},[a("el-form-item",{attrs:{label:"客户编码",prop:"groupuid"}},[a("el-input",{attrs:{placeholder:"请输入客户编码",clearable:"",size:"small"},model:{value:t.formdata.groupuid,callback:function(e){t.$set(t.formdata,"groupuid","string"===typeof e?e.trim():e)},expression:"formdata.groupuid"}})],1)],1)])],1),a("el-divider",{attrs:{"content-position":"left"}},[t._v("客户信息")]),a("el-row",[a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"客户信息",prop:"groupname"}},[a("el-input",{attrs:{placeholder:"请输入客户信息",clearable:"",size:"small"},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname","string"===typeof e?e.trim():e)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("abbreviate")}}},[a("el-form-item",{attrs:{label:"简写",prop:"abbreviate"}},[a("el-input",{attrs:{placeholder:"请输入简写",clearable:"",size:"small"},model:{value:t.formdata.abbreviate,callback:function(e){t.$set(t.formdata,"abbreviate","string"===typeof e?e.trim():e)},expression:"formdata.abbreviate"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("linkman")}}},[a("el-form-item",{attrs:{label:"联系人",prop:"linkman"}},[a("el-input",{attrs:{placeholder:"请输入联系人",clearable:"",size:"small"},model:{value:t.formdata.linkman,callback:function(e){t.$set(t.formdata,"linkman","string"===typeof e?e.trim():e)},expression:"formdata.linkman"}})],1)],1)]),a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("telephone")}}},[a("el-form-item",{attrs:{label:"联系方式",prop:"telephone"}},[a("el-input",{attrs:{placeholder:"请输入联系方式",size:"small"},model:{value:t.formdata.telephone,callback:function(e){t.$set(t.formdata,"telephone","string"===typeof e?e.trim():e)},expression:"formdata.telephone"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:10}},[a("div",{on:{click:function(e){return t.cleValidate("groupclass")}}},[a("el-form-item",{attrs:{label:"组分类",prop:"groupclass"}},[a("el-input",{attrs:{placeholder:"请输入组分类",clearable:"",size:"small"},model:{value:t.formdata.groupclass,callback:function(e){t.$set(t.formdata,"groupclass","string"===typeof e?e.trim():e)},expression:"formdata.groupclass"}})],1)],1)]),a("el-col",{attrs:{span:10}},[a("div",[a("el-form-item",[a("el-checkbox",{attrs:{label:"有效","true-label":1,"false-label":0},model:{value:t.formdata.enabledmark,callback:function(e){t.$set(t.formdata,"enabledmark",e)},expression:"formdata.enabledmark"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:21}},[a("el-form-item",{attrs:{label:"地址",prop:"groupadd"}},[a("el-input",{attrs:{type:"textarea",rows:2,placeholder:"请输入地址",clearable:"",size:"small"},model:{value:t.formdata.groupadd,callback:function(e){t.$set(t.formdata,"groupadd","string"===typeof e?e.trim():e)},expression:"formdata.groupadd"}})],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("h3",{staticStyle:{width:"100%","text-align":"center"}},[t._v("用户信息")]),a("el-row",{staticStyle:{"margin-bottom":"10px"}},[a("el-button",{attrs:{type:"primary",size:"mini",disabled:!t.idx},nativeOn:{click:function(e){t.userVisible=!0,t.multi=1}}},[t._v("添加")]),a("el-button",{attrs:{type:"danger",size:"mini",disabled:!t.lst.length||0==t.multipleSelection.length},on:{click:function(e){return t.deleteById()}}},[t._v("删除")])],1),a("el-row",[a("el-table",{ref:"multipleTable",staticClass:"tb-edit tableBox",attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"",height:t.tableHeight,"highlight-current-row":"",size:"small","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"4px 0px 4px 0px"},"cell-style":{padding:"4px 0px"},"row-style":{height:"20px"}},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"40",align:"center",fixed:""}}),t._l(t.tableForm.item,(function(e){return[!e.displaymark?t._e():a("el-table-column",{key:e.id,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return[a("div",[a("span",[t._v(t._s(i.row[e.itemcode]))])])]}}],null,!0)})]}))],2)],1)],1)],1)],1),a("el-divider")],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"新建日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.createdate)))])])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.modifydate,expression:"formdata.modifydate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})]),t.userVisible?a("el-dialog",{attrs:{title:"用户信息","append-to-body":!0,visible:t.userVisible,width:"800px","close-on-press-escape":!1,"close-on-click-modal":!1},on:{"update:visible":function(e){t.userVisible=e}}},[a("selPwProcess",{ref:"selPwProcess",attrs:{multi:t.multi}}),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},nativeOn:{click:function(e){return t.selPwProcess()}}},[t._v("确 定")]),a("el-button",{on:{click:function(e){t.userVisible=!1}}},[t._v("取 消")])],1)],1):t._e()],1)},l=[],c=a("c7eb"),d=a("1da1"),m=(a("4de4"),a("caad"),a("2532"),a("3ca3"),a("2382"),a("4e3e"),a("159b"),a("ddb0"),a("2b3d"),a("bf19"),a("9861"),a("1c2f")),u={formcode:"S06M14B1List",item:[{itemcode:"groupuid",itemname:"客户编码",minwidth:"100",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center",datasheet:"groupuid"},{itemcode:"groupname",itemname:"客户名称",minwidth:"80",displaymark:1,overflow:1,datasheet:"groupname"},{itemcode:"abbreviate",itemname:"缩写",minwidth:"80",displaymark:1,overflow:1,datasheet:"abbreviate"},{itemcode:"lister",itemname:"制表",minwidth:"100",displaymark:1,overflow:1,datasheet:"lister"},{itemcode:"modifydate",itemname:"修改日期",minwidth:"100",displaymark:1,overflow:1,datasheet:"modifydate"},{itemcode:"operate",itemname:"操作",minwidth:"100",displaymark:1,overflow:1,fixed:2}]},p={formcode:"S06M14B1Item",item:[{itemcode:"username",itemname:"用户名",minwidth:"100",defwidth:"",displaymark:1,fixed:1,sortable:0,overflow:1,aligntype:"center"},{itemcode:"realname",itemname:"姓名",minwidth:"80",displaymark:1,overflow:1,fixed:1},{itemcode:"phone",itemname:"手机号",minwidth:"100",displaymark:1,overflow:1},{itemcode:"email",itemname:"邮箱",minwidth:"100",displaymark:1,overflow:1}]},f={formcode:"S06M28B1List",item:[{itemcode:"refno",itemname:"编码",minwidth:"80",defwidth:"",displaymark:1,fixed:0,sortable:1,overflow:1,aligntype:"center"},{itemcode:"billtitle",itemname:"会议标题",minwidth:"100",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billtitle"},{itemcode:"billtype",itemname:"类型",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billtype"},{itemcode:"billdate",itemname:"日期",minwidth:"80",displaymark:1,overflow:1,datasheet:"Sa_Feedback.billdate"},{itemcode:"projectname",itemname:"项目名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"customer",itemname:"客户名称",minwidth:"80",displaymark:1,overflow:1},{itemcode:"location",itemname:"地点",minwidth:"80",displaymark:1,overflow:1},{itemcode:"story",itemname:"反馈内容",minwidth:"120",displaymark:1,overflow:1},{itemcode:"opinion",itemname:"处理意见",minwidth:"120",displaymark:1,overflow:1},{itemcode:"finishmark",itemname:"完工",minwidth:"100",displaymark:1,overflow:1},{itemcode:"submitmark",itemname:"是否提报",minwidth:"100",defwidth:"",displaymark:1,overflow:1,aligntype:"center"}]};const h={add(t){return console.log("add",t),new Promise((e,a)=>{var i=JSON.stringify(t);n["a"].post("/S06M14B1/create",i).then(t=>{200==t.data.code?e(t.data):a(t.data.message)}).catch(t=>{a(t)})})},update(t){return new Promise((e,a)=>{var i=JSON.stringify(t);n["a"].post("/S06M14B1/update",i).then(t=>{200==t.data.code?e(t.data):a(t.data.message)}).catch(t=>{a(t)})})},delete(t){return new Promise((e,a)=>{n["a"].get("/S06M14B1/delete?key="+t).then(t=>{200==t.data.code?e(t.data):a(t.data.message)}).catch(t=>{a(t)})})}};var b=h,g={name:"Formedit",components:{selPwProcess:m["a"]},props:["idx"],data:function(){return{title:"客户信息",formdata:{enabledmark:1,groupname:"",abbreviate:"",groupuid:"",remark:"",wggroupid:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{groupuid:[{required:!0,trigger:"blur",message:"客户编码不能为空"}],groupname:[{required:!0,trigger:"blur",message:"客户名称不能为空"}]},formLabelWidth:"100px",tableHeight:"220px",option:"",multi:0,groupnameOptions:[],defaultProps:{children:"children",label:"groupname",value:"id"},ReportVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,lst:[],userVisible:!1,multipleSelection:[],tableForm:p}},computed:{formcontainHeight:function(){return window.innerHeight-50-23-50+"px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData(),this.BindDataByuidgroupname()},methods:{bindData:function(){var t=this;this.listLoading=!0,0!=this.idx&&n["a"].get("/S06M14B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.lst=t.formdata.userList):t.$message.warning(e.data.msg||"获取客户信息失败"),t.listLoading=!1})).catch((function(e){t.listLoading=!1,t.$message.error("请求错误")}))},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return!1;e.saveForm()}))},saveForm:function(){var t=this;0==this.idx?b.add(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("changeidx",e.data.id),t.$emit("bindData")})).catch((function(e){t.$message.warning("保存失败")})):b.update(this.formdata).then((function(e){t.$message.success("保存成功"),t.$emit("bindData"),t.bindData()})).catch((function(e){t.$message.warning("保存失败")}))},closeForm:function(){this.$emit("closeForm")},deleteForm:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),b.delete(t).then((function(){console.log("执行关闭保存"),e.$emit("compForm")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},handleChangeGroupid:function(t){console.log(t);var e=t[t.length-1];this.formdata.gengroupid=t[t.length-1],this.changeByGroup(e)},BindDataByuidgroupname:function(){var t=this;n["a"].get("/SaBillGroup/getListByModuleCode?Code=S06M14B1").then((function(e){200==e.data.code&&(t.groupnameOptions=t.changeFormat(e.data.data))})).catch((function(t){}))},changeByGroup:function(t){this.formdata.wggroupid=t},printButton:function(){var t=this;n["a"].get("/SaBillGroup/getListByModuleCode?code=S06M14B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0})),this.ReportVisible=!0},submitReport:function(){var t=this;""!=this.reportModel?n["a"].get("/S06M14B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},changeidx:function(t){this.idx=t},changeFormat:function(t){var e=[];if(!Array.isArray(t))return e;t.forEach((function(t){delete t.children}));var a={};return t.forEach((function(t){a[t.id]=t})),t.forEach((function(t){var i=a[t.parentid];i?(i.children||(i.children=[])).push(t):e.push(t)})),e},handleSelectionChange:function(t){this.multipleSelection=t},selPwProcess:function(){var t=this;return Object(d["a"])(Object(c["a"])().mark((function e(){var a,i,o,r,s;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:for(t.userVisible=!1,a=t.$refs.selPwProcess.$refs.selPwProcess.selection,i=[],i=a.filter((function(e){var a=t.lst.map((function(t){return t.id}));return!a.includes(e.id)})),t,o=[],r=0;r<i.length;r++)s=new Promise((function(e,a){var o=i[r],s={groupid:t.formdata.id,userid:o.id};n["a"].post("/S06M14B2/create",JSON.stringify(s)).then((function(t){200==t.data.code?e(t.data):a(t.data.msg||"新增错误")}))})),o.push(s);return e.next=9,Promise.all(o).then((function(e){t.$message.success("新增成功"),t.bindData()}));case 9:case"end":return e.stop()}}),e)})))()},deleteById:function(){var t=this;return Object(d["a"])(Object(c["a"])().mark((function e(){var a,i,o,r;return Object(c["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:if(0!=t.multipleSelection.length){e.next=3;break}return t.$message.warning("请选择用户"),e.abrupt("return");case 3:for(a=t.multipleSelection,t,i=[],o=0;o<a.length;o++)r=new Promise((function(t,e){n["a"].get("/S06M14B2/delete?key="+a[o].usergroupid).then((function(a){200==a.data.code?t(a.data):e(a.data.msg)}))})),i.push(r);return e.next=9,Promise.all(i).then((function(e){t.$message.success("删除成功"),t.bindData()}));case 9:t.$refs.multipleTable.clearSelection();case 10:case"end":return e.stop()}}),e)})))()}}},v=g,w=(a("3611"),a("2877")),x=Object(w["a"])(v,s,l,!1,null,"0fe8629b",null),y=x.exports,k=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"component-container",staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticClass:"form_button flex a-c j-end p-r",staticStyle:{top:"0px",width:"100%","z-index":"10"}},[a("div",{staticClass:"button-container p-a",staticStyle:{top:"0px",right:"30px"}},[a("div",{staticStyle:{display:"inline-block",margin:"0 10px"}},[a("el-button",{attrs:{disabled:!t.formdata.id,type:"primary",size:"small"},nativeOn:{click:function(e){return t.printButton()}}},[t._v("打 印")])],1),a("el-dropdown",{staticStyle:{"margin-right":"10px"},attrs:{trigger:"click",placement:"bottom"}},[a("el-button",{attrs:{size:"small"}},[t._v("操作"),a("i",{staticClass:"el-icon-caret-bottom el-icon--right"})]),a("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},[a("el-dropdown-item",{attrs:{icon:"el-icon-delete",disabled:!t.formdata.id},nativeOn:{click:function(e){return t.rowdel(t.idx)}}},[t._v("删 除")])],1)],1),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)]),a("div",{staticStyle:{padding:"15px"}},[a("div",{staticClass:"form-border form-container shandow form_info flex f-d-c",staticStyle:{width:"100%"},style:{height:t.formcontainHeight}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r xsContent"},[a("div",{staticStyle:{"margin-bottom":"4px","margin-top":"-14px","font-size":"18px",color:"rgb(107, 119, 140)"}},[t._v(" "+t._s(t.formdata.groupname||"客户")+" ")]),a("h3",{staticClass:"xsTitle"},[t._v(t._s(t.formdata.abbreviate))]),a("div",{staticClass:"xsInfo"},[a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("编码")]),a("div",[t._v(t._s(t.formdata.groupuid||"-"))])]),a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("类型")]),a("div",[t._v(t._s(t.formdata.grouptype||"-"))])]),a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("联系人")]),a("div",[t._v(t._s(t.formdata.linkman||"-"))])]),a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("电话")]),a("div",[t._v(t._s(t.formdata.telephone||"-"))])]),a("div",{staticClass:"xsInfo-item"},[a("span",[t._v("地址")]),a("div",[t._v(t._s(t.formdata.groupadd||"-"))])])]),a("div",{staticClass:"xsBody"},[a("div",{staticClass:"outer-div"},[a("header",{staticClass:"header"},[a("ul",{staticClass:"tab-tilte"},[a("li",{class:{active:0==t.activeTabs},on:{click:function(e){t.activeTabs=0}}},[t._v(" 反馈单 ")]),a("li",{class:{active:1==t.activeTabs},on:{click:function(e){t.activeTabs=1}}},[t._v(" 客户反馈 ")]),a("li",{class:{active:2==t.activeTabs},on:{click:function(e){t.activeTabs=2}}},[t._v(" 需求清单 ")]),a("li",{class:{active:3==t.activeTabs},on:{click:function(e){t.activeTabs=3}}},[t._v(" 派工单 ")]),a("li",{class:{active:4==t.activeTabs},on:{click:function(e){t.activeTabs=4}}},[t._v(" 附件 ")])])]),a("div",{staticClass:"tab-content"},[a("div",{directives:[{name:"show",rawName:"v-show",value:0==t.activeTabs,expression:"activeTabs == 0"}],staticStyle:{"min-height":"380px"}},[a("FormFeedback",{ref:"FormFeedback",attrs:{formdata:t.formdata,idx:t.idx}})],1),a("div",{directives:[{name:"show",rawName:"v-show",value:1==t.activeTabs,expression:"activeTabs == 1"}],staticStyle:{"min-height":"380px"}}),a("div",{directives:[{name:"show",rawName:"v-show",value:2==t.activeTabs,expression:"activeTabs == 2"}],staticStyle:{"min-height":"380px"}}),a("div",{directives:[{name:"show",rawName:"v-show",value:3==t.activeTabs,expression:"activeTabs == 3"}],staticStyle:{"min-height":"380px"}}),a("div",{directives:[{name:"show",rawName:"v-show",value:4==t.activeTabs,expression:"activeTabs == 4"}],staticStyle:{"min-height":"380px"}})])])])])])]),a("el-dialog",{attrs:{title:"打印模板","append-to-body":!0,width:"400px",visible:t.ReportVisible},on:{"update:visible":function(e){t.ReportVisible=e}}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择打印模板"},model:{value:t.reportModel,callback:function(e){t.reportModel=e},expression:"reportModel"}},t._l(t.ReportData,(function(t){return a("el-option",{key:t.id,attrs:{label:t.rptname,value:t.id}})})),1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button-group",{staticStyle:{float:"left"}},[a("el-button",{attrs:{type:"print"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="print",t.submitRemoteReport()}}},[t._v("云打印")]),a("el-button",{attrs:{type:"preview"==t.printType?"primary":"default",size:"small"},nativeOn:{click:function(e){t.printType="preview",t.submitRemoteReport()}}},[t._v("云预览")])],1),a("el-button",{attrs:{type:"primary",size:"small"},on:{click:t.submitReport}},[t._v("确 定")]),a("el-button",{attrs:{size:"small"},on:{click:function(e){t.ReportVisible=!1}}},[t._v("取 消")])],1)],1),a("el-dialog",{staticClass:"pdfDialog",attrs:{"append-to-body":!0,width:"80%",visible:t.isViewPdf20,top:"2vh","close-on-click-modal":!1},on:{"update:visible":function(e){t.isViewPdf20=e}}},[a("iframe",{staticStyle:{width:"100%",height:"90vh"},attrs:{src:t.pdfUrl,frameborder:"0"}})])],1)},S=[],_=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px"}},[a("el-table",{ref:"tableList",staticStyle:{overflow:"auto"},attrs:{data:t.lst,"element-loading-text":"Loading",border:"",fit:"","highlight-current-row":"","header-cell-style":{background:"#F3F4F7",color:"#555",padding:"3px 0px 3px 0px"},"cell-style":{padding:"4px 0px 4px 0px"}}},[a("el-table-column",{attrs:{align:"center",label:"ID",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("span",[t._v(t._s(e.$index+1))])]}}])}),t._l(t.tableForm.item,(function(e,i){return[!e.displaymark?t._e():a("el-table-column",{key:i,attrs:{prop:e.itemcode,columnKey:e.itemcode,label:e.itemname,align:e.aligntype?e.aligntype:"center","min-width":e.minwidth,"show-overflow-tooltip":!!e.overflow,sortable:!!e.sortable},scopedSlots:t._u([{key:"default",fn:function(i){return["refno"==e.itemcode?a("el-button",{attrs:{type:"text",size:"small"},on:{click:function(e){return t.openS06M28B1(i.row)}}},[t._v(t._s(i.row[e.itemcode]?i.row[e.itemcode]:"反馈单"))]):"billdate"==e.itemcode||"createdate"==e.itemcode?a("span",[t._v(t._s(t._f("dateFormat")(i.row[e.itemcode])))]):"finishmark"==e.itemcode?a("div",[a("el-switch",{attrs:{"active-value":1,"inactive-value":0,size:"small"},on:{change:function(e){return t.changeFinishmark(e,i.row)}},model:{value:i.row[e.itemcode],callback:function(a){t.$set(i.row,e.itemcode,a)},expression:"scope.row[i.itemcode]"}})],1):"submitmark"==e.itemcode?a("div",[1==i.row[e.itemcode]?a("span",[t._v("已提报")]):a("span",[t._v("未提报")])]):a("span",[t._v(t._s(i.row[e.itemcode]||"-"))])]}}],null,!0)})]}))],2)],1),t.addformvisible?a("el-dialog",{attrs:{"append-to-body":!0,visible:t.addformvisible,width:"90vw",top:"1vh","close-on-press-escape":!1,"close-on-click-modal":!1,"show-close":!1},on:{"update:visible":function(e){t.addformvisible=e}}},[a("FormEdit",{ref:"formedit",attrs:{idx:t.selRow.pid,isDialog:!0,initData:t.selRow},on:{bindData:t.bindData,changeidx:t.changeIdx,closeForm:function(e){t.addformvisible=!1}}})],1):t._e()],1)},$=[],F=a("3be7"),D={name:"formContract",props:["idx","formdata"],components:{FormEdit:F["a"]},data:function(){return{gengroupid:[],defaultProps:{children:"children",label:"label",value:"id"},lst:[],tableForm:f,addformvisible:!1,selRow:{}}},mounted:function(){},methods:{bindData:function(){var t=this,e={PageNum:1,PageSize:200,OrderType:1,SearchType:1,SearchPojo:{groupid:this.formdata.id}};this.$request.post("/S06M28B1/getPageList",JSON.stringify(e)).then((function(e){200==e.data.code?t.lst=e.data.data.list:t.$message.warning(e.data.msg||"获取反馈单信息失败")}))},openS06M28B1:function(t){this.selRow={},0==t?this.selRow.pid=0:this.selRow=Object.assign({},t),this.addformvisible=!0,this.$forceUpdate()},changeIdx:function(t){this.selRow.id=t},changeFinishmark:function(t,e){var a=this;this.$set(e,"finishmark",t),t?this.$confirm("是否确定完工?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(t){a.updateItemList(e)})).catch((function(){a.$set(e,"finishmark",0)})):this.updateItemList(e)},updateItemList:function(t){var e=this;this.$request.post("/S06M28B1/updateItem",JSON.stringify(t)).then((function(t){200==t.data.code?(e.$message.success(t.data.msg||"操作成功！"),e.bindData()):e.$message({message:"更新失败",type:"error"})}))}}},C=D,P=(a("5658"),Object(w["a"])(C,_,$,!1,null,"e9436b1e",null)),B=P.exports,V=(a("b893"),{name:"Formedit",components:{FormFeedback:B},props:["idx","groupData"],data:function(){return{InfoVisible:!1,companyURL:"",arrList:[],lst:[],leads:{personname:"",position:"",wechat:"",telephone:"",email:""},printType:"print",formdata:{enabledmark:1,groupname:"",abbreviate:"",groupuid:"",remark:"",wggroupid:"",lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname},formRules:{gengroupid:[{required:!0,trigger:"blur",message:"分组为必填项"}],custname:[{required:!0,trigger:"blur",message:"客户名称为必填项"}],custtype:[{required:!0,trigger:"blur",message:"客户类型为必填项"}],custclass:[{required:!0,trigger:"blur",message:"客户等级为必填项"}],linkman:[{required:!0,trigger:"blur",message:"联系人为必填项"}]},ReportVisible:!1,setVisible:!1,ReportData:[],reportModel:"",pdfUrl:"",isViewPdf20:!1,dialogIdx:0,multi:0,activeTabs:0,activeData:[],operateData:[]}},computed:{formcontainHeight:function(){return window.innerHeight-30+"px"}},watch:{idx:function(t,e){this.bindData()},activeTabs:function(t,e){0==t?this.$refs.FormFeedback.bindData():1==t?this.$refs.FormItem.bindData():2==t?this.$refs.FormAnnex.bindData(this.formdata.id):3==t||(4==t?this.$refs.FormTeam.bindData():5==t?this.$refs.FormQuotation.bindData():6==t?this.$refs.FormContract.bindData():7==t&&this.$refs.FormBusiness.bindData())}},created:function(){},mounted:function(){this.bindData()},methods:{bindData:function(){var t=this;0!==this.idx&&n["a"].get("/S06M14B1/getEntity?key=".concat(this.idx)).then((function(e){200==e.data.code?(t.formdata=e.data.data,t.$refs.FormFeedback.bindData()):t.$message.warning(e.data.msg||"获取客户信息失败")})).catch((function(e){t.$message.error(e||"请求失败")}))},rowdel:function(t){var e=this;this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),b.delete(t).then((function(t){200==t.code&&e.$message({showClose:!0,message:"删除成功",type:"success"}),e.$emit("formcomp")})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},printButton:function(){var t=this;n["a"].get("/SaBillGroup/getListByModuleCode?code=S06M14B1Edit").then((function(e){t.ReportData=e.data.data,0!=t.ReportData.length&&(t.reportModel=t.ReportData[0].id),t.ReportVisible=!0}))},submitReport:function(){var t=this;""!=this.reportModel?n["a"].get("/S06M14B1/printBill?key="+this.idx+"&ptid="+this.reportModel,{responseType:"blob"}).then((function(e){t.formdata.printcount+=1,t.ReportVisible=!1;var a=[];a.push(e.data);var i=window.URL.createObjectURL(new Blob(a,{type:"application/pdf"}));t.pdfUrl=i,t.isViewPdf20=!0})):this.$message.warning("打印模板不能为空!")},submitRemoteReport:function(){var t=this;if(""!=this.reportModel){var e="/S07M02B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel;"preview"==this.printType&&(e="/S07M02B1/printWebBill?key="+this.idx+"&ptid="+this.reportModel+"&cmd=1"),n["a"].get(e).then((function(e){200==e.data.code?null==e.data.data||""==e.data.data?t.$message.warning(e.data.msg||"打印失败"):"连接成功"!=t.$store.state.webSocketMsg.webSocketState?(t.$setWs.initWebSocket("ws://127.0.0.1:18800"),setTimeout((function(){t.remoteprinting(e.data.data)}),500)):setTimeout((function(){t.remoteprinting(e.data.data)}),100):t.$message.warning(e.data.msg||"打印失败")}))}else this.$message.warning("打印模板不能为空!")},remoteprinting:function(t){"连接成功"==this.$store.state.webSocketMsg.webSocketState?(this.$setWs.wsSend(t),this.ReportVisible=!1):this.$message.warning("连接打印服务器失败")},cleValidate:function(t,e){this.$refs[t].clearValidate(e)},changeidx:function(t){this.dialogIdx=t},fileUploadBtn:function(t){var e=this,a=Object.assign({},t);a.enabledmark=1,a.modulecode="S07M03B1",a.moduleid=this.formdata.id,this.$request.post("/SaAttachment/create",JSON.stringify(a)).then((function(t){200==t.data.code?(e.$message.success("附件保存成功"),e.$refs.FormAnnex.bindData(e.formdata.id)):e.$message.warning(t.data.msg||"附件保存失败")}))}}}),T=V,z=(a("07cf"),Object(w["a"])(T,k,S,!1,null,"3619f928",null)),O=z.exports,R=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("div",{class:t.iShow?"filter-container flex j-s ishowDetail":"filter-container flex j-s"},[a("div",[a("div",{staticClass:"p-r",staticStyle:{display:"inline-block",height:"28px","margin-right":"5px",cursor:"pointer",color:"#383838"},on:{click:t.btnshowGroup}},[a("i",{staticClass:"p-r",class:[t.showTree?"el-icon-s-fold":"el-icon-s-unfold"],staticStyle:{"font-size":"20px",top:"4px"}})]),a("el-input",{staticClass:"filter-item",staticStyle:{width:"280px"},attrs:{placeholder:"请输入查询","prefix-icon":"el-icon-search",size:"mini"},model:{value:t.strfilter,callback:function(e){t.strfilter=e},expression:"strfilter"}},[a("el-button",{staticClass:"filter-item",attrs:{slot:"append",size:"mini"},on:{click:t.btnsearch},slot:"append"},[t._v("搜索 ")])],1),a("el-button",{staticClass:"filter-item",staticStyle:{"margin-left":"10px"},attrs:{type:"primary",icon:"el-icon-edit",plain:"",size:"mini"},on:{click:t.btnadd}},[t._v(" 添加 ")])],1),a("div",{staticClass:"iShowBtn"},[a("el-button",{attrs:{size:"mini",icon:"el-icon-refresh-right",title:"刷新"},on:{click:t.bindData}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-download",title:"导出Excel"},on:{click:t.btnExport}}),a("el-button",{attrs:{size:"mini",icon:"el-icon-s-tools"},on:{click:function(e){return t.openDialog()}}})],1)]),t.setColumsVisible?a("el-dialog",{attrs:{width:"800px",title:"列设置","append-to-body":!0,visible:t.setColumsVisible,"close-on-click-modal":!1,"destroy-on-close":!0},on:{"update:visible":function(e){t.setColumsVisible=e}}},[a("SetColums",{ref:"setcolums",attrs:{code:t.tableForm.formcode,tableForm:t.tableForm,baseparam:"/SaDgFormat"},on:{bindData:function(e){return t.$emit("bindColumn")},closeDialog:function(e){t.setColumsVisible=!1}}})],1):t._e()],1)},N=[],M={name:"Listheader",components:{},props:["showTree","tableForm"],data:function(){return{strfilter:"",iShow:!1,formdata:{},code:"S06M14B1List",setColumsVisible:!1,searchVisible:!1}},methods:{openDialog:function(){this.setColumsVisible=!0},submitUpdate:function(){this.$refs.setcolums.submitUpdate()},AdvancedSearch:function(t){this.iShow=!1,this.$emit("AdvancedSearch",t),this.searchVisible=!1},openSearchForm:function(){var t=this;this.searchVisible=!0,setTimeout((function(){t.$refs.searchForm.getInit()}),100)},btnadd:function(){this.$emit("btnadd")},btnshowGroup:function(){this.$emit("btnshowGroup")},btnsearch:function(){this.$emit("btnsearch",this.strfilter)},btnImport:function(){this.$emit("btnImport")},modelExport:function(){this.$emit("modelExport")},bindData:function(){this.$emit("bindData")},allDelete:function(){this.$emit("allDelete")},btnExport:function(){this.$emit("btnExport")}}},L=M,q=(a("8d45"),Object(w["a"])(L,R,N,!1,null,"a429dbe2",null)),I=q.exports,E=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticStyle:{width:"100%","box-sizing":"border-box"}},[a("div",{staticStyle:{padding:"20px 20px 20px 20px"}},[a("div",{staticStyle:{width:"100%"}},[a("div",{ref:"form_main_info",staticClass:"form form-head p-r"},[a("el-form",{ref:"formdata",staticClass:"custInfo",attrs:{model:t.formdata,"label-width":t.formLabelWidth,rules:t.formRules}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupname")}}},[a("el-form-item",{attrs:{label:"名称",prop:"groupname"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入名称",size:"small"},on:{input:t.writeCode},model:{value:t.formdata.groupname,callback:function(e){t.$set(t.formdata,"groupname",e)},expression:"formdata.groupname"}})],1)],1)]),a("el-col",{attrs:{span:12}},[a("div",{on:{click:function(e){return t.cleValidate("groupcode")}}},[a("el-form-item",{attrs:{label:"编码",prop:"groupcode"}},[a("el-input",{staticStyle:{width:"100%","min-width":"140px"},attrs:{placeholder:"请输入编码",size:"small"},model:{value:t.formdata.groupcode,callback:function(e){t.$set(t.formdata,"groupcode",e)},expression:"formdata.groupcode"}})],1)],1)])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"排序"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{"controls-position":"right",min:0,size:"small"},model:{value:t.formdata.rownum,callback:function(e){t.$set(t.formdata,"rownum",e)},expression:"formdata.rownum"}})],1)],1)],1)],1)],1),a("el-form",{staticClass:"footFormContent",attrs:{"label-width":t.formLabelWidth}},[a("el-row",{staticStyle:{"margin-top":"15px","margin-right":"20px"}},[a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"摘  要","label-position":"right","label-width":"100px"}},[a("el-input",{attrs:{placeholder:"请输入摘要",clearable:"",size:"small"},model:{value:t.formdata.remark,callback:function(e){t.$set(t.formdata,"remark",e)},expression:"formdata.remark"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"制表"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.lister,expression:"formdata.lister"}],staticClass:"el-form-item__label"},[t._v(t._s(t.formdata.lister))])])],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"修改日期"}},[a("span",{directives:[{name:"show",rawName:"v-show",value:t.formdata.createdate,expression:"formdata.createdate"}],staticClass:"el-form-item__label"},[t._v(t._s(t._f("dateFormat")(t.formdata.modifydate)))])])],1)],1)],1)],1)]),a("div",{staticClass:"button-container flex j-end"},[a("el-button",{directives:[{name:"preventReClick",rawName:"v-preventReClick"}],attrs:{type:"primary",size:"small"},nativeOn:{click:function(e){return t.submitForm("formdata")}}},[t._v(" 保 存")]),a("el-button",{attrs:{size:"small"},nativeOn:{click:function(e){return t.$emit("closeDialog")}}},[t._v(" 关 闭")])],1)])},j=[],G=(a("25f0"),a("4d90"),a("b0b8")),J={name:"Formedit",filters:{dateFormat:function(t){var e=new Date(t),a=e.getFullYear(),i=(e.getMonth()+1).toString().padStart(2,"0"),o=e.getDate().toString().padStart(2,"0"),r=e.getHours().toString().padStart(2,"0"),n=e.getMinutes().toString().padStart(2,"0"),s=e.getSeconds().toString().padStart(2,"0");return"".concat(a,"-").concat(i,"-").concat(o," ").concat(r,":").concat(n,":").concat(s)}},props:["idx","pid"],data:function(){return{formdata:{lister:JSON.parse(window.localStorage.getItem("getInfo")).realname,createby:JSON.parse(window.localStorage.getItem("getInfo")).realname,parentid:"",modulecode:"S06M14B1",groupcode:"",groupname:"",rownum:0,remark:"",enabledmark:1},formRules:{modulecode:[{required:!0,trigger:"blur",message:"功能编码为必填项"}],groupcode:[{required:!0,trigger:"blur",message:"分组编码为必填项"}],groupname:[{required:!0,trigger:"blur",message:"分组名称为必填项"}]},formLabelWidth:"100px",dialogVisible:!1,delDialogVisible:!1,option:""}},computed:{formcontainHeight:function(){return"300px"}},watch:{idx:function(t,e){this.bindData()}},created:function(){this.bindData()},methods:{bindData:function(){var t=this;0!=this.idx?n["a"].get("/SaBillGroup/getEntity?key=".concat(this.idx)).then((function(e){console.log("==================",e),200==e.data.code&&(t.formdata=e.data.data)})):this.formdata.parentid=this.idx},submitForm:function(t){var e=this;this.$refs[t].validate((function(t){if(!t)return console.log("error submit!!"),!1;e.saveForm()}))},saveForm:function(){var t=this;this.pid&&(this.formdata.parentid=this.pid),0==this.idx?n["a"].post("/SaBillGroup/create",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")})):n["a"].post("/SaBillGroup/update",JSON.stringify(this.formdata)).then((function(e){t.$emit("bindData"),t.$emit("closeDialog")}))},closeForm:function(){this.$emit("closeDialog")},check:function(){console.log("check")},cleValidate:function(t){this.$refs.formdata.clearValidate(t)},writeCode:function(t){G.setOptions({checkPolyphone:!1,charCase:1}),this.formdata.groupcode=G.getFullChars(t)}}},U=J,H=(a("ce9e"),Object(w["a"])(U,E,j,!1,null,"3e87535c",null)),W=H.exports,A={name:"S06M14B1",components:{listheader:I,formedit:y,FormDetail:O,group:W},data:function(){return{lst:[],FormVisible:!1,listLoading:!1,idx:0,total:0,searchstr:"",queryParams:{PageNum:1,PageSize:20,OrderType:1,SearchType:1},tableForm:u,treeTitle:"货品分组",gropuFormVisible:!1,treeVisble:!0,groupData:[],treeEditable:!1,pid:0,selectList:[],selRow:{},detailvisible:!1}},computed:{tableMaxHeight:function(){return window.innerHeight-160+"px"}},watch:{},created:function(){this.BindTreeData()},mounted:function(){this.bindData(),this.getcolumn()},methods:{bindData:function(){var t=this;this.listLoading=!0,this.$refs.scene&&-1!=this.$refs.scene.radio&&0!=this.total?this.queryParams.scenedata=JSON.parse(this.$refs.scene.lst[this.$refs.scene.radio].scenedata):(1==this.queryParams.SearchType&&this.$delete(this.queryParams,"scenedata"),this.$refs.scene&&(this.$refs.scene.radio=-1)),n["a"].post("/S06M14B1/getPageList",JSON.stringify(this.queryParams)).then((function(e){console.log("S06M14B1",e.data),200==e.data.code&&(t.lst=e.data.data.list,t.total=e.data.data.total),t.listLoading=!1})).catch((function(e){t.listLoading=!1}))},getcolumn:function(){var t=this;this.$getColumn(this.tableForm.formcode,u).then((function(e){t.tableForm=Object.assign({},e.colList)}))},BindTreeData:function(){var t=this;n["a"].get("SaBillGroup/getListByModuleCode?Code=S06M14B1").then((function(e){if(200==e.data.code){var a=e.data.data.map((function(t){return{id:t.id,pid:t.parentid,label:t.groupname,prefix:t.prefix}})),i=[{id:"0",pid:-1,label:"客户分组",prefix:!1}],o=[].concat(Object(r["a"])(a),i);t.groupData=t.transData(o,"id","pid","children")}}))},btnExport:function(){this.$btnExport(this.lst,this.tableForm,"客户信息")},GetList:function(t){this.queryParams.PageNum=t.page,this.queryParams.PageSize=t.limit,this.bindData()},search:function(t){""!=t?this.queryParams.SearchPojo={wggroupid:t,groupuid:t,groupname:t,abbreviate:t}:this.$delete(this.queryParams,"SearchPojo"),this.$delete(this.queryParams,"OrderBy"),this.queryParams.SearchType=1,this.queryParams.PageNum=1,this.bindData()},AdvancedSearch:function(t){this.queryParams.scenedata=t,""==t[0].field?this.queryParams.SearchType=1:this.queryParams.SearchType=0,this.queryParams.PageNum=1,this.bindData()},changeSort:function(t){"descending"==t.order?this.queryParams.OrderType=1:this.queryParams.OrderType=0,this.queryParams.OrderBy=this.$tablesort(t,this.tableForm),this.bindData()},showform:function(t){this.idx=t,this.FormVisible=!0},showGroupform:function(t){this.idx=t,this.gropuFormVisible=!0},closeForm:function(){this.FormVisible=!1,this.gropuFormVisible=!1,this.bindData()},compForm:function(){this.BindTreeData(),this.bindData(),this.FormVisible=!1,this.gropuFormVisible=!1},showDetail:function(t,e){this.selRow=Object.assign({},t),this.idx=t.id,this.detailvisible=!0},transData:function(t,e,a,i){for(var o=[],r={},n=e,s=a,l=i,c=0,d=0,m=t.length;c<m;c++)r[t[c][n]]=t[c];for(;d<m;d++){var u=t[d],p=r[u[s]];p?(!p[l]&&(p[l]=[]),p[l].push(u)):o.push(u)}return o},handleNodeClick:function(t){if(console.log("树形分组",t),0==t.id){var e="";this.search(e)}else{var a=t.id;this.search(a)}},treeEdit:function(){this.treeEditable=!this.treeEditable},editTreeNode:function(t){this.pid=t.pid,this.showGroupform(t.id)},addTreeChild:function(t){this.pid=t.id,this.showGroupform(0)},delTreeNode:function(t){var e=this;t.children?this.$message({message:"警告,该节点下存在子节点不能删除",type:"warning"}):this.$confirm("此操作将永久删除该记录, 是否继续?","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){console.log("执行删除"),n["a"].get("/SaBillGroup/delete?key=".concat(t.id)).then((function(){console.log("执行关闭保存"),e.$message.success({message:"删除成功！"}),e.BindTreeData()})).catch((function(){e.$message({showClose:!0,message:"删除失败",type:"warning"})}))})).catch((function(){}))},changeidx:function(t){this.idx=t},submitExPort:function(){this.$refs.exportFile.importf()}}},K=A,Y=(a("776a"),Object(w["a"])(K,i,o,!1,null,null,null));e["default"]=Y.exports},"776a":function(t,e,a){"use strict";a("8560")},7973:function(t,e,a){},"841c":function(t,e,a){"use strict";var i=a("d784"),o=a("825a"),r=a("1d80"),n=a("129f"),s=a("14c3");i("search",1,(function(t,e,a){return[function(e){var a=r(this),i=void 0==e?void 0:e[t];return void 0!==i?i.call(e,a):new RegExp(e)[t](String(a))},function(t){var i=a(e,t,this);if(i.done)return i.value;var r=o(t),l=String(this),c=r.lastIndex;n(c,0)||(r.lastIndex=0);var d=s(r,l);return n(r.lastIndex,c)||(r.lastIndex=c),null===d?-1:d.index}]}))},8560:function(t,e,a){},"8d45":function(t,e,a){"use strict";a("3e20")},"9c3b":function(t,e,a){},c6c3:function(t,e,a){},ce9e:function(t,e,a){"use strict";a("2a15")},f38a:function(t,e,a){"use strict";a("c6c3")}}]);