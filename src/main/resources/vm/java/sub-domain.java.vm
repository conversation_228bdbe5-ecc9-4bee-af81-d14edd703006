package ${packageName}.domain;

    #foreach ($import in $subImportList)
    import ${import};
    #end
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * ${subTable.functionName}对象 ${subTableName}
 *
 * <AUTHOR>
 * @date ${datetime}
 */
public class ${subClassName} extends

BaseEntity {
    private static final long serialVersionUID = 1L;

    #foreach ($column in $subTable.columns)
        #if(!$table.isSuperColumn($column.javaField))
            /** $column.columnComment */
            #if($column.list)
                #set($parentheseIndex=$column.columnComment.indexOf("（"))
                #if($parentheseIndex != -1)
                    #set($comment=$column.columnComment.substring(0, $parentheseIndex))
                #else
                    #set($comment=$column.columnComment)
                #end
                #if($parentheseIndex != -1)
                @Excel(name = "${comment}", readConverterExp = "$column.readConverterExp()")
                #elseif($column.javaType == 'Date')
                @JsonFormat(pattern = "yyyy-MM-dd")
                @Excel(name = "${comment}", width = 30, dateFormat = "yyyy-MM-dd")
                #else
                @Excel(name = "${comment}")
                #end
            #end
        private $column.javaType $column.javaField;

        #end
    #end
    #foreach ($column in $subTable.columns)
        #if(!$table.isSuperColumn($column.javaField))
            #if($column.javaField.length() > 2 && $column.javaField.substring(1,2).matches("[A-Z]"))
                #set($AttrName=$column.javaField)
            #else
                #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
            #end
            public void set${AttrName}($column.javaType $column.javaField)
            {
                this.$column.javaField = $column.javaField;
            }

    public $column.javaType get${AttrName}()
            {
                return $column.javaField;
            }
        #end
    #end

    @Override
    public String toString () {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            #foreach ($column in $subTable.columns)
                #if($column.javaField.length() > 2 && $column.javaField.substring(1,2).matches("[A-Z]"))
                    #set($AttrName=$column.javaField)
                #else
                    #set($AttrName=$column.javaField.substring(0,1).toUpperCase() + ${column.javaField.substring(1)})
                #end
                    .append("${column.javaField}", get${AttrName}())
                    #end
                .toString();
    }
}
